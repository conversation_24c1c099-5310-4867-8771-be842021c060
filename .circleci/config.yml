version: 2.1
setup: true
orbs:
  docker: circleci/docker@2.4.0
  slack: circleci/slack@4.12.5
  gh: circleci/github-cli@2.3.0
  lp_deploy: luxurypresence/lp_deploy@1.1

parameters:
  pull_request:
    type: boolean
    description: 'CircleCI pull request trigger'
    default: false
  release:
    type: boolean
    description: 'CircleCI release trigger'
    default: false
  pr_target_branch:
    type: string
    description: 'Pull Request target branch'
    default: main
  # !! TODO Uncomment after e2e tests implemented
  # run_e2e_test:
  #   type: boolean
  #   description: 'Condition to run e2e tests on current branch'
  #   default: false
  # e2e_grep:
  #   type: string
  #   description: 'Grep to define which tags to run'
  #   default: ''
  # e2e_grep_invert:
  #   type: string
  #   description: 'Grep to define which tags to not run'
  #   default: ''
  # e2e_workers:
  #   type: integer
  #   description: 'Amount of workers to use during tests'
  #   default: 2
  # e2e_project:
  #   type: string
  #   description: 'Which Playwright project to run'
  #   default: '' # empty means all projects

###############################
# REFERENCES & DEFAULTS
###############################
references:
  containerConfig: &containerConfig
    docker:
      - image: cimg/node:22.15
      - image: cimg/postgres:16.4
        environment:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: 123123
          POSTGRES_DB: lp_test
    resource_class: medium+
    # Speeds up restore_cache as long as repo and modules fit in RAM
    working_directory: /mnt/ramdisk

###############################
# COMMANDS
###############################
commands:
  install_code:
    description: 'Checkout and install the code'
    steps:
      - checkout
      - restore_cache:
          keys:
            - v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          command: |
            corepack enable --install-directory ~/bin
            # pinning to 9.15.0 to avoid issues with latest
            # https://github.com/pnpm/pnpm/issues/9029#issuecomment-2630882497
            corepack prepare pnpm@9.15.0 --activate
            pnpm config set store-dir .pnpm-store
      # Authenticate with NPM for private packages
      - run: echo "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}" > .npmrc
      - run: pnpm install --frozen-lockfile
      - save_cache:
          name: Save pnpm Package Cache
          key: v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
          paths:
            - .pnpm-store
      - run:
          name: Install jq
          command: sudo apt-get update && sudo apt-get install -y jq
      - setup_remote_docker
  # !! TODO Uncomment after e2e tests implemented
  # install_code_e2e:
  #   description: 'Checkout and install the code for e2e tests'
  #   steps:
  #     - checkout
  #     - restore_cache:
  #         keys:
  #           - v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
  #     - run:
  #         name: Install pnpm package manager
  #         command: |
  #           corepack enable --install-directory /bin
  #           corepack prepare pnpm@latest-9 --activate
  #           pnpm config set store-dir .pnpm-store
  #     # Authenticate with NPM for private packages
  #     - run: echo "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}" > .npmrc
  #     - run: pnpm install --frozen-lockfile
  #     - save_cache:
  #         name: Save pnpm Package Cache
  #         key: v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
  #         paths:
  #           - .pnpm-store
  setup_client_marketing_service_e2e:
    description: 'Setup environment for client-marketing-service e2e tests'
    steps:
      - checkout
      - restore_cache:
          keys:
            - v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          command: |
            corepack enable --install-directory ~/bin
            corepack prepare pnpm@9.15.0 --activate
            pnpm config set store-dir .pnpm-store
      # Authenticate with NPM for private packages
      - run: echo "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}" > .npmrc
      - run: pnpm install --frozen-lockfile
      - save_cache:
          name: Save pnpm Package Cache
          key: v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
          paths:
            - .pnpm-store
      - run:
          name: Install Java and psql for flyway
          command: |
            sudo apt update && sudo apt -y install gnupg2
            curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
            echo "deb http://apt.postgresql.org/pub/repos/apt/ `lsb_release -cs`-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
            sudo apt update
            sudo apt -y install postgresql-client-16 default-jre
      - run:
          name: Get Flyway
          command: |
            wget -qO- https://download.red-gate.com/maven/release/com/redgate/flyway/flyway-commandline/10.18.0/flyway-commandline-10.18.0-linux-x64.tar.gz | tar -xvz && sudo ln -s `pwd`/flyway-10.18.0/flyway /usr/local/bin
      - run:
          name: Wait for database
          command: |
            # Wait for PostgreSQL to be ready
            until pg_isready -h localhost -p 5432 -U postgres; do
              echo "Waiting for PostgreSQL..."
              sleep 2
            done
      - run:
          name: Create database schema for e2e tests
          command: |
            # Create the test database schema
            psql "postgresql://postgres:123123@localhost:5432/lp_test" -c "CREATE SCHEMA IF NOT EXISTS crew_ai_seo_automation;"
      - run:
          name: Set environment variables for e2e tests
          command: |
            echo "export DATABASE_HOST=localhost" >> $BASH_ENV
            echo "export DATABASE_PORT=5432" >> $BASH_ENV
            echo "export DATABASE_USER=postgres" >> $BASH_ENV
            echo "export DATABASE_PASSWORD=123123" >> $BASH_ENV
            echo "export DATABASE_NAME=lp_test" >> $BASH_ENV
            echo "export DATABASE_SCHEMA=crew_ai_seo_automation" >> $BASH_ENV
            echo "export NODE_ENV=test" >> $BASH_ENV
            source $BASH_ENV
  get_package_version:
    description: 'Get the package version from changeset or package.json based on the branch'
    parameters:
      packageName:
        type: string
        description: 'Name of the package'
    steps:
      - run:
          name: Get package version for << parameters.packageName >>
          command: |
            if ! grep -q "<< parameters.packageName >>" changeset-status.json; then
              echo "<< parameters.packageName >> has no updates on main"
              circleci-agent step halt
            else
              VERSION=$(jq -r ".releases[] | select(.name == \"<< parameters.packageName >>\") | .newVersion" changeset-status.json)
            fi
            echo "export VERSION=$VERSION" >> $BASH_ENV
            source $BASH_ENV
            echo "Version of << parameters.packageName >>: $VERSION"
  # Expects turbo prune to be run before
  docker-build:
    description: Checks if the Docker image exists on Docker Hub and if not builds and pushes it
    parameters:
      image:
        description: Docker image name
        type: string
        default: ''
      packageName:
        description: Package name
        type: string
        default: ''
      tag:
        description: Docker image tag
        type: string
        default: ''
      buildArgs:
        description: Docker build args
        type: string
        default: ''
    steps:
      - run: cp out/pnpm-lock.yaml out/full/pnpm-lock.yaml
      - run: cp .npmrc out/full/.npmrc
      - docker/check
      - run:
          name: Check if Docker image exists
          command: |
            if docker manifest inspect << parameters.image >>:<< parameters.tag >>; then
              echo "Image << parameters.image >>:<< parameters.tag >> already exists on Docker Hub. Skipping build."
              circleci-agent step halt
            else
              echo "Image << parameters.image >>:<< parameters.tag >> does not exist. Proceeding with build."
            fi
      - docker/build:
          image: << parameters.image >>
          dockerfile: out/full/packages/<< parameters.packageName >>/Dockerfile
          docker-context: out/full
          tag: << parameters.tag >>
          extra_build_args: << parameters.buildArgs >>
      - docker/push:
          image: << parameters.image >>
          tag: << parameters.tag >>
  docker-build-python:
    description: Checks if the Python Docker image exists on Docker Hub and if not builds and pushes it
    parameters:
      image:
        description: Docker image name
        type: string
        default: ''
      packageName:
        description: Package name
        type: string
        default: ''
      tag:
        description: Docker image tag
        type: string
        default: ''
      buildArgs:
        description: Docker build args
        type: string
        default: ''
    steps:
      - run: |
          # Create directory structure
          mkdir -p out/full/packages/<< parameters.packageName >>

          # Copy package files
          cp packages/<< parameters.packageName >>/pyproject.toml out/full/packages/<< parameters.packageName >>/
          if [ -f "packages/<< parameters.packageName >>/uv.lock" ]; then
            echo "Found uv.lock, copying..."
            cp packages/<< parameters.packageName >>/uv.lock out/full/packages/<< parameters.packageName >>/
          else
            echo "ERROR: uv.lock not found in packages/<< parameters.packageName >>/"
            exit 1
          fi

          # Copy source code
          mkdir -p out/full/packages/<< parameters.packageName >>/src
          cp -r packages/<< parameters.packageName >>/src/* out/full/packages/<< parameters.packageName >>/src/

          echo "Build context contents:"
          ls -R out/full/
      - run: |
          if [ -f "out/full/packages/<< parameters.packageName >>/Dockerfile" ]; then
            mv out/full/packages/<< parameters.packageName >>/Dockerfile out/full/.
          elif [ -f "packages/<< parameters.packageName >>/Dockerfile" ]; then
            cp packages/<< parameters.packageName >>/Dockerfile out/full/.
          else
            exit 1
          fi
      - run: |
          mkdir -p out/full/packages/<< parameters.packageName >>/src
          cp -r packages/<< parameters.packageName >>/src/* out/full/packages/<< parameters.packageName >>/src/
      - docker/check
      - run:
          name: Check if Docker image exists
          command: |
            if docker manifest inspect << parameters.image >>:<< parameters.tag >>; then
              echo "Image << parameters.image >>:<< parameters.tag >> already exists on Docker Hub. Skipping build."
              circleci-agent step halt
            else
              echo "Image << parameters.image >>:<< parameters.tag >> does not exist. Proceeding with build."
            fi
      - docker/build:
          image: << parameters.image >>
          dockerfile: out/full/Dockerfile
          docker-context: out/full
          tag: << parameters.tag >>
          extra_build_args: << parameters.buildArgs >>
      - docker/push:
          image: << parameters.image >>
          tag: << parameters.tag >>
  docker-build-migration:
    description: Checks if the migration Docker image exists on Docker Hub and if not builds and pushes it
    parameters:
      image:
        description: Docker image name
        type: string
        default: ''
      packageName:
        description: Package name
        type: string
        default: ''
      tag:
        description: Docker image tag
        type: string
        default: ''
      buildArgs:
        description: Docker build args
        type: string
        default: ''
    steps:
      - docker/check
      - run:
          name: Check if Docker image exists
          command: |
            if docker manifest inspect << parameters.image >>:<< parameters.tag >>; then
              echo "Image << parameters.image >>:<< parameters.tag >> already exists on Docker Hub. Skipping build."
              exit 0
            else
              echo "Image << parameters.image >>:<< parameters.tag >> does not exist. Proceeding with build."
            fi
      - run: cp out/pnpm-lock.yaml out/full/pnpm-lock.yaml
      - run: cp .npmrc out/full/.npmrc
      - docker/build:
          image: << parameters.image >>
          dockerfile: out/full/packages/<< parameters.packageName >>/migration.Dockerfile
          docker-context: out/full
          tag: << parameters.tag >>
          extra_build_args: << parameters.buildArgs >>
      - docker/push:
          image: << parameters.image >>
          tag: << parameters.tag >>
  slack_notify_deployment:
    parameters:
      version:
        type: string
      packageName:
        type: string
      projectName:
        type: string
    steps:
      - run:
          name: Create Changelog Link
          command: echo 'export CHANGELOG_URL="https://github.com/luxurypresence/seo-automation/blob/main/packages/<< parameters.packageName >>/CHANGELOG.md"' >> $BASH_ENV
      - slack/notify:
          event: fail
          template: basic_fail_1

###############################
# JOB DEFINITIONS
###############################
jobs:
  sql-lint:
    <<: *containerConfig
    steps:
      - run: |
          if [ -z "$CIRCLE_PULL_REQUEST" ]; then
              circleci-agent step halt
          fi
      - install_code
      - run:
          name: Update Environment Variables
          command: |
            set +o pipefail
            set +e
            echo "export SQUAWK_GITHUB_PRIVATE_KEY=$(echo -e \"$SQUAWK_GITHUB_PRIVATE_KEY\")" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_REPO_OWNER=$(echo $CIRCLE_PULL_REQUEST | awk -F/ '{print $4}')" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_REPO_NAME=$(echo $CIRCLE_PULL_REQUEST | awk -F/ '{print $5}')" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_PR_NUMBER=$(echo $CIRCLE_PULL_REQUEST | awk -F/ '{print $7}')" >> "$BASH_ENV"
            current_branch=$(git rev-parse --abbrev-ref HEAD)
            added_files=$(git diff --name-only --diff-filter=A main...$current_branch)
            echo "Added files before grep: $added_files"
            added_files=$(echo "$added_files" | grep -E '\.sql$' | tr '\n' ' ')
            echo "Added sql files: $added_files"
            if [ -z "$added_files" ]; then
              echo "No added SQL files found"
              circleci-agent step halt
              exit 0
            fi
            echo "export SQL_FILES=\"$(echo "$added_files")\"" >> "$BASH_ENV"
            source "$BASH_ENV"
      - run:
          name: Install Squawk
          command: npm install --prefix=$HOME/.local -g squawk-cli
      - run:
          name: Lint SQL
          command: squawk --exclude=prefer-robust-stmts upload-to-github $SQL_FILES

  python-lint:
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - run:
          name: Install dependencies
          command: |
            python -m pip install --upgrade pip
            cd packages/ai_seo_automation
            pip install ruff
      - run:
          name: Run linting
          command: |
            cd packages/ai_seo_automation
            ruff check .

  python-format:
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - run:
          name: Install dependencies
          command: |
            python -m pip install --upgrade pip
            cd packages/ai_seo_automation
            pip install black
      - run:
          name: Run formatting
          command: |
            cd packages/ai_seo_automation
            black --check .

  python-test:
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - run:
          name: Install Python dependencies
          command: |
            python -m pip install --upgrade pip
            cd packages/ai_seo_automation
            pip install --editable .[all]
            pip install crewai[tools] litellm pydantic sqlalchemy alembic psycopg2-binary beautifulsoup4 requests pytest-mock
      - run:
          name: Run python tests for ai_seo_automation
          command: |
            cd packages/ai_seo_automation
            pytest --maxfail=1 --disable-warnings

  # Test job
  test:
    <<: *containerConfig
    steps:
      - install_code
      - run: pnpm lint
      - run:
          name: Java and psql for flyway
          command: |
            sudo apt update && sudo apt -y install gnupg2
            curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
            echo "deb http://apt.postgresql.org/pub/repos/apt/ `lsb_release -cs`-pgdg main" | sudo tee /etc/apt/sources.list.d/pgdg.list
            sudo apt update
            sudo apt -y install postgresql-client-16 default-jre
      - run:
          name: Get Flyway
          command: |
            wget -qO- https://download.red-gate.com/maven/release/com/redgate/flyway/flyway-commandline/10.18.0/flyway-commandline-10.18.0-linux-x64.tar.gz | tar -xvz && sudo ln -s `pwd`/flyway-10.18.0/flyway /usr/local/bin
      - run:
          name: Wait for db
          command: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Create schema
          command: |
            psql \
              "postgresql://postgres:123123@localhost:5432/lp_test" \
              -c "CREATE SCHEMA property"
      - run:
          name: Run test
          command: pnpm test

  # Client Marketing Service E2E Tests
  client-marketing-service-e2e:
    <<: *containerConfig
    steps:
      - setup_client_marketing_service_e2e
      - run:
          name: Build client-marketing-service
          command: |
            cd packages/client-marketing-service
            pnpm build
      - run:
          name: Run client-marketing-service e2e tests
          command: |
            cd packages/client-marketing-service
            pnpm test:e2e
      - store_test_results:
          path: packages/client-marketing-service/test-results
      - store_artifacts:
          path: packages/client-marketing-service/test-results
          destination: client-marketing-service-e2e-results

  require-changeset:
    <<: *containerConfig
    steps:
      - install_code
      - gh/setup
      - run:
          name: Determine PR conditions
          command: |
            if [[ -z "$CIRCLE_PULL_REQUEST" && <<pipeline.parameters.pull_request>> == false ]]; then
              echo "This job is neither triggered by a GitHub Action PR nor a CircleCI PR."
              circleci-agent step halt
              exit 0
            fi

            if [[ -z "$CIRCLE_PULL_REQUEST" ]]; then
              TARGET_BRANCH="<<pipeline.parameters.pr_target_branch>>"
            else
              TARGET_BRANCH=$(gh pr view $CIRCLE_PULL_REQUEST --json baseRefName --jq '.baseRefName')
            fi

            echo "Target branch is $TARGET_BRANCH"

            if [[ "$TARGET_BRANCH" == "main" ]]; then
              echo "Proceeding with changeset check."
            else
              echo "Target branch is not main, skipping changeset check."
              circleci-agent step halt
            fi
      - run:
          name: Check for changeset
          command: |
            if [ -z "$(find .changeset -type f -name "*.md" ! -name "README.md")" ]; then
              echo "Changeset is missing for PR to main"
              exit 1
            fi

  release-npm-packages:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access node_modules
      - attach_workspace:
          at: .
      - run:
          name: Check if packages were modified (changeset present)
          command: |
            pnpm changeset status --output=changeset-status.json
            RELEASES_LENGTH=$(jq '.releases | length' changeset-status.json)
            if [ "$RELEASES_LENGTH" -eq "0" ]; then
              echo "no packages modified, skipping publish."
              circleci-agent step halt
            fi
      - persist_to_workspace:
          root: ./
          paths:
            - changeset-status.json
      - run:
          name: Build packages
          command: pnpm build --filter=@luxury-presence/*
      - run: git config --global user.email "<EMAIL>"
      - run: git config --global user.name "${CIRCLE_PROJECT_USERNAME}"
      - run: mkdir -p ~/.ssh
      - run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - run: pnpm publish-packages
      - run: git push origin $CIRCLE_BRANCH --follow-tags
      - slack/notify:
          event: fail
          template: basic_fail_1

  release-ai-marketing-scheduler:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: ai-marketing-scheduler
      - run: pnpm --filter ai-marketing-scheduler build
      - run: pnpm turbo prune ai-marketing-scheduler --docker
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/ai-marketing-scheduler-installer
          packageName: ai-marketing-scheduler
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - slack_notify_deployment:
          version: $VERSION
          packageName: ai-marketing-scheduler
          projectName: AI Marketing Scheduler

  release-bloom-scheduler-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: bloom-scheduler-lambda
      - run: pnpm --filter bloom-scheduler-lambda build
      - run: pnpm turbo prune bloom-scheduler-lambda --docker
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/bloom-scheduler-installer
          packageName: bloom-scheduler-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - slack_notify_deployment:
          version: $VERSION
          packageName: bloom-scheduler-lambda
          projectName: Bloom Scheduler

  release-brand-profile-generator:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: brand-profile-generator
      - run: pnpm --filter brand-profile-generator build
      - run: pnpm turbo prune brand-profile-generator --docker
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/brand-profile-generator-installer
          packageName: brand-profile-generator
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - slack_notify_deployment:
          version: $VERSION
          packageName: brand-profile-generator
          projectName: Brand Profile Generator

  release-blog-topic-generator:
    <<: *containerConfig
    steps:
      - install_code
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: blog-topic-generator
      - run: pnpm --filter blog-topic-generator build
      - run: pnpm turbo prune blog-topic-generator --docker
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/blog-topic-generator-installer
          packageName: blog-topic-generator
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - slack_notify_deployment:
          version: $VERSION
          packageName: blog-topic-generator
          projectName: Blog Topic Generator

  release-seo-automation:
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - setup_remote_docker
      - run:
          name: Update Python Package Version
          command: |
            if [ -f ".changeset/version.json" ]; then
              NEW_VERSION=$(jq -r '.releases[] | select(.name=="ai_seo_automation_flow") | .version' .changeset/version.json)
              if [ ! -z "$NEW_VERSION" ]; then
                perl -i -pe "s/^version = \".*\"/version = \"$NEW_VERSION\"/" packages/ai_seo_automation/pyproject.toml
              fi
            fi
      - docker-build-python:
          image: luxurypresence/ai_seo_automation
          packageName: ai_seo_automation
          tag: v$VERSION
      - slack_notify_deployment:
          version: $VERSION
          packageName: ai_seo_automation
          projectName: SEO Automation

  release-client-marketing-service:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access package.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: client-marketing-service
      - run: pnpm build --filter client-marketing-service
      - run: pnpm turbo prune client-marketing-service --docker
      - docker-build-migration:
          image: luxurypresence/load-client-marketing-service-migration
          packageName: client-marketing-service
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - docker-build:
          image: luxurypresence/client-marketing-service
          packageName: client-marketing-service
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - slack_notify_deployment:
          version: $VERSION
          packageName: client-marketing-service
          projectName: SEO API

  # !! TODO Uncomment after e2e tests implemented
  # run-e2e-test:
  #   docker:
  #     - image: mcr.microsoft.com/playwright:v1.43.1-jammy
  #   working_directory: /mnt/ramdisk
  #   resource_class: luxurypresence/eks-staging
  #   parallelism: 5
  #   steps:
  #     - install_code_e2e
  #     - run:
  #         name: Create report directory
  #         command: mkdir -p all-blob-reports
  #     - run:
  #         name: 'Run e2e tests'
  #         command: |
  #           cd packages/platform-tests-e2e
  #           export SLACK_REPORT=false
  #           export LOGS_URL="${CIRCLE_BUILD_URL}"
  #           export PLAYWRIGHT_REPORT_URL="https://output.circle-artifacts.com/output/job/${CIRCLE_WORKFLOW_JOB_ID}/artifacts/0/playwright-report/index.html"
  #           WORKERS="<< pipeline.parameters.e2e_workers >>"
  #           E2E_GREP="<< pipeline.parameters.e2e_grep >>"
  #           E2E_GREP_INVERT="<< pipeline.parameters.e2e_grep_invert >>"
  #           E2E_PROJECT="<< pipeline.parameters.e2e_project >>"
  #           npx playwright install webkit
  #           npx playwright install chrome
  #           npx playwright test "${E2E_PROJECT:+--project=$E2E_PROJECT}" --workers="${WORKERS}" --grep="${E2E_GREP}" --grep-invert="${E2E_GREP_INVERT}" --shard=$(($CIRCLE_NODE_INDEX+1))/$CIRCLE_NODE_TOTAL || true
  #           cp blob-report/* /mnt/ramdisk/all-blob-reports
  #     - store_artifacts:
  #         path: packages/platform-tests-e2e/playwright-report
  #         destination: playwright-report
  #     - store_test_results:
  #         path: packages/platform-tests-e2e/playwright-report/results.xml
  #     - persist_to_workspace:
  #         root: .
  #         paths:
  #           - all-blob-reports

  # merge-e2e-reports:
  #   docker:
  #     - image: mcr.microsoft.com/playwright:v1.43.1-jammy
  #   working_directory: /mnt/ramdisk
  #   resource_class: luxurypresence/eks-staging
  #   steps:
  #     - install_code_e2e
  #     - attach_workspace:
  #         at: .
  #     - run:
  #         name: Report
  #         command: |
  #           cd packages/platform-tests-e2e
  #           export SLACK_REPORT="${SLACK_REPORT}"
  #           export LOGS_URL="${CIRCLE_BUILD_URL}"
  #           export PLAYWRIGHT_REPORT_URL="https://output.circle-artifacts.com/output/job/${CIRCLE_WORKFLOW_JOB_ID}/artifacts/0/playwright-report/index.html"
  #           npx playwright merge-reports --config playwright.config.js /mnt/ramdisk/all-blob-reports
  #     - store_artifacts:
  #         path: packages/platform-tests-e2e/playwright-report
  #         destination: playwright-report

###############################
# WORKFLOW DEFINITIONS
###############################
workflows:
  pull-request-audit:
    when: << pipeline.parameters.pull_request >>
    jobs:
      - test:
          context:
            - slack
            - npm-read
            - turbo-cache
      - client-marketing-service-e2e:
          context:
            - slack
            - npm-read
            - turbo-cache
      - sql-lint:
          context:
            - github-creds
            - npm-read
            - squawk
      - require-changeset:
          context:
            - npm-read
            - github-creds
          requires:
            - test
  test-and-release:
    when: << pipeline.parameters.release >>
    jobs:
      - test:
          context:
            - npm-read
            - slack
            - turbo-cache
      - client-marketing-service-e2e:
          context:
            - npm-read
            - slack
            - turbo-cache
      - sql-lint:
          context:
            - npm-read
            - squawk
      - release-npm-packages:
          requires:
            - test
          context:
            - npm-read
            - slack
            - turbo-cache
            - github-creds
          filters:
            branches:
              only:
                - main
      # !! TODO Uncomment after e2e tests implemented
      # - release-platform-tests-e2e:
      #     context:
      #       - npm-read
      #       - dockerhub
      #       - slack
      #       - turbo-cache
      #     requires:
      #       - release-npm-packages
      - release-bloom-scheduler-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
          requires:
            - release-npm-packages
      - release-client-marketing-service:
          context:
            - npm-read
            - dockerhub
            - slack
          requires:
            - release-npm-packages
          filters:
            branches:
              only: main
      - release-ai-marketing-scheduler:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
          requires:
            - release-npm-packages
          filters:
            branches:
              only: main
      - release-brand-profile-generator:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
          requires:
            - release-npm-packages
          filters:
            branches:
              only: main
      - release-blog-topic-generator:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
          requires:
            - release-npm-packages
          filters:
            branches:
              only: main
      - lp_deploy/continue:
          changeset_file: changeset-status.json
          requires:
            - release-bloom-scheduler-lambda
            - release-client-marketing-service
            - release-ai-marketing-scheduler
            - release-brand-profile-generator
            - release-blog-topic-generator
          filters:
            branches:
              only: main
  # !! TODO Uncomment after e2e tests implemented
  # branches:
  #   only: main # Keep this filter for security reasons.
  # manual-end-to-end:
  #   when: << pipeline.parameters.run_e2e_test >>
  #   jobs:
  #     - run-e2e-test:
  #         context:
  #           - hackweek
  #           - npm-read
  #           - turbo-cache
  #     - merge-e2e-reports:
  #         requires:
  #           - run-e2e-test
  #         context:
  #           - hackweek
  #           - npm-read
  #           - turbo-cache
  # !! TODO Uncomment after e2e tests implemented
  # nightly-end-to-end:
  #   unless:
  #     or:
  #       - equal: [true, << pipeline.parameters.pull_request >>]
  #       - equal: [true, << pipeline.parameters.release >>]
  #   triggers:
  #     - schedule:
  #         cron: '5 3 * * *'
  #         filters:
  #           branches:
  #             only: main
  #   jobs:
  #     - run-e2e-test:
  #         context:
  #           - hackweek
  #           - npm-read
  #           - turbo-cache
  #     - merge-e2e-reports:
  #         requires:
  #           - run-e2e-test
  #         context:
  #           - hackweek
  #           - npm-read
  #           - turbo-cache
