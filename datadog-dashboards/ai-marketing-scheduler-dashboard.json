{"title": "SEO Automation", "description": "Comprehensive monitoring dashboard for ai-marketing-scheduler and client-marketing-service packages including Lambda functions, Step Functions, GraphQL API", "widgets": [{"id": 0, "definition": {"type": "note", "content": "# SEO Automation Overview\n\nThis dashboard monitors SEO automation including:\n- STAR API editor \n- 10 Lambda functions for SEO workflow processing\n- 2 Step Functions for workflow orchestration\n- Client Marketing Service GraphQL API for SEO recommendations & content", "background_color": "blue", "font_size": "14", "text_align": "left", "vertical_align": "center", "show_tick": true, "tick_pos": "50%", "tick_edge": "bottom", "has_padding": true}, "layout": {"x": 0, "y": 0, "width": 12, "height": 3}}, {"id": 2837281754035785, "definition": {"title": "STAR API editor success rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"name": "query2", "data_source": "logs", "search": {"query": "service:website-service @response.statusCode:[200 TO 299] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": ["*"], "group_by": [], "compute": {"aggregation": "count"}, "storage": "hot"}, {"name": "query1", "data_source": "logs", "search": {"query": "service:website-service @response.statusCode:[400 TO 599] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": ["*"], "group_by": [], "compute": {"aggregation": "count"}, "storage": "hot"}], "conditional_formats": [{"comparator": ">", "value": 98, "palette": "white_on_green"}, {"comparator": ">", "value": 95, "palette": "black_on_light_green"}, {"comparator": ">", "value": 30, "palette": "black_on_light_red"}, {"comparator": "<", "value": 30, "palette": "white_on_red"}], "formulas": [{"number_format": {"unit": {"type": "custom_unit_label", "label": "%"}}, "formula": "query2 / (query2 + query1) * 100"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"yaxis": {"include_zero": false}, "type": "area"}}, "layout": {"x": 0, "y": 3, "width": 4, "height": 2}}, {"id": 5149918049989616, "definition": {"title": "STAR API editor success count", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"name": "query2", "data_source": "logs", "search": {"query": "service:website-service @response.statusCode:[200 TO 299] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": ["*"], "group_by": [], "compute": {"aggregation": "count"}, "storage": "hot"}], "conditional_formats": [{"comparator": ">", "value": 1, "palette": "green_on_white"}], "formulas": [{"formula": "query2"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"type": "area", "yaxis": {"include_zero": false}}}, "layout": {"x": 4, "y": 3, "width": 2, "height": 2}}, {"id": 3721129035956089, "definition": {"title": "STAR API editor error count", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"name": "query1", "data_source": "logs", "search": {"query": "service:website-service @response.statusCode:[400 TO 599] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": ["*"], "group_by": [], "compute": {"aggregation": "count"}, "storage": "hot"}], "conditional_formats": [{"comparator": ">", "value": 1, "palette": "red_on_white"}], "formulas": [{"formula": "query1"}]}], "autoscale": true, "precision": 2, "timeseries_background": {"yaxis": {"include_zero": false}, "type": "area"}}, "layout": {"x": 6, "y": 3, "width": 2, "height": 2}}, {"id": 1032528747495516, "definition": {"title": "STAR API editor successes and errors", "title_size": "16", "title_align": "left", "show_legend": true, "legend_layout": "auto", "legend_columns": ["avg", "min", "max", "value", "sum"], "type": "timeseries", "requests": [{"formulas": [{"style": {"palette": "green", "palette_index": 4}, "formula": "query1"}, {"style": {"palette": "warm", "palette_index": 6}, "formula": "query2"}], "queries": [{"data_source": "logs", "name": "query1", "search": {"query": "service:website-service @response.statusCode:[200 TO 299] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": [], "compute": {"aggregation": "count"}, "group_by": [], "storage": "hot"}, {"name": "query2", "data_source": "logs", "search": {"query": "service:website-service @response.statusCode:[400 TO 599] env:$env @method:PATCH @url:\"/api/v1/star/editor\""}, "indexes": ["*"], "group_by": [], "compute": {"aggregation": "count"}, "storage": "hot"}], "response_format": "timeseries", "style": {"palette": "dog_classic", "order_by": "values", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}]}, "layout": {"x": 0, "y": 5, "width": 12, "height": 4}}, {"id": 8024059519959001, "definition": {"title": "STAR API error log messages", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:website-service status:error *:star-api env:$env", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "env", "width": "auto"}, {"field": "@requestId", "width": "auto"}, {"field": "content", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 9, "width": 12, "height": 4}}, {"id": 5708593113549856, "definition": {"title": "STAR API editor request error logs", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:website-service @response.statusCode:[400 TO 599] env:$env @method:PATCH @url:\"/api/v1/star/editor\"", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "env", "width": "auto"}, {"field": "@body.url", "width": "auto"}, {"field": "@body.input.elementTag", "width": "auto"}, {"field": "@body.input.elementProperty", "width": "auto"}, {"field": "@response.headers.x-request-id", "width": "auto"}, {"field": "content", "width": "auto"}, {"field": "@duration", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 13, "width": 12, "height": 4}}, {"id": 3326763923639282, "definition": {"title": "STAR API editor request success logs", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:website-service @response.statusCode:[200 TO 299] env:$env @method:PATCH @url:\"/api/v1/star/editor\"", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "env", "width": "auto"}, {"field": "@body.url", "width": "auto"}, {"field": "@body.input.elementTag", "width": "auto"}, {"field": "@body.input.elementProperty", "width": "auto"}, {"field": "@response.headers.x-request-id", "width": "auto"}, {"field": "content", "width": "auto"}, {"field": "@duration", "width": "auto"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 17, "width": 12, "height": 4}}, {"id": 70, "definition": {"title": "SEO API errors", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:seo-automation-api-gateway env:$env (\"graphql\" OR \"recommendation\" OR \"seo\" OR \"scrape\" OR \"keyword\") status:error", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "host", "width": "auto"}, {"field": "service", "width": "auto"}, {"field": "resource_name", "width": "auto"}, {"field": "message", "width": "auto"}, {"field": "content", "width": "compact"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 21, "width": 12, "height": 3}}, {"id": 1, "definition": {"title": "Lambda Invocations by Function", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-*,env:$env} by {functionname}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 24, "width": 6, "height": 3}}, {"id": 2, "definition": {"title": "Lambda Errors by Function", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-*,env:$env} by {functionname}"}], "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 24, "width": 6, "height": 3}}, {"id": 3, "definition": {"title": "Lambda Duration (Average) by Function", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-*,env:$env} by {functionname}"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 27, "width": 6, "height": 3}}, {"id": 4, "definition": {"title": "<PERSON><PERSON> Duration (Max) by Function", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "max:aws.lambda.duration{functionname:ai-marketing-scheduler-*,env:$env} by {functionname}"}], "style": {"palette": "orange", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 27, "width": 6, "height": 3}}, {"id": 5, "definition": {"title": "Overall Error Rate (%)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-*,env:$env}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query2", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-*,env:$env}", "aggregator": "avg"}], "formulas": [{"formula": "query1 / query2 * 100"}]}], "autoscale": true, "custom_unit": "%", "text_align": "center", "precision": 2}, "layout": {"x": 0, "y": 30, "width": 3, "height": 2}}, {"id": 6, "definition": {"title": "Total Invocations (24h)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-*,env:$env}", "aggregator": "sum"}]}], "autoscale": true, "text_align": "center", "precision": 0}, "layout": {"x": 3, "y": 30, "width": 3, "height": 2}}, {"id": 7, "definition": {"title": "Average Duration (ms)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-*,env:$env}", "aggregator": "avg"}]}], "autoscale": true, "custom_unit": "ms", "text_align": "center", "precision": 0}, "layout": {"x": 6, "y": 30, "width": 3, "height": 2}}, {"id": 8, "definition": {"title": "Total Errors (24h)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-*,env:$env}", "aggregator": "sum"}]}], "autoscale": true, "text_align": "center", "precision": 0}, "layout": {"x": 9, "y": 30, "width": 3, "height": 2}}, {"id": 9, "definition": {"type": "note", "content": "## Step Functions Monitoring\n\nMonitoring AWS Step Functions execution metrics for workflow orchestration", "background_color": "gray", "font_size": "14", "text_align": "left", "vertical_align": "center", "show_tick": false, "has_padding": true}, "layout": {"x": 0, "y": 32, "width": 12, "height": 1}}, {"id": 10, "definition": {"title": "Step Functions - Executions Started", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.states.executions_started{statemachinename:*ai-marketing-scheduler*,env:$env} by {statemachinename}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 33, "width": 6, "height": 3}}, {"id": 11, "definition": {"title": "Step Functions - Success vs Failed Executions", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.states.executions_succeeded{statemachinename:*ai-marketing-scheduler*,env:$env} by {statemachinename}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.states.executions_failed{statemachinename:*ai-marketing-scheduler*,env:$env} by {statemachinename}"}], "style": {"palette": "red", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 33, "width": 6, "height": 3}}, {"id": 12, "definition": {"title": "Step Functions - Average Execution Time", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.states.execution_time{statemachinename:*ai-marketing-scheduler*,env:$env} by {statemachinename}"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 36, "width": 6, "height": 3}}, {"id": 13, "definition": {"title": "Step Functions Error Rate (%)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.states.executions_failed{statemachinename:*ai-marketing-scheduler*,env:$env}", "aggregator": "avg"}, {"data_source": "metrics", "name": "query2", "query": "sum:aws.states.executions_started{statemachinename:*ai-marketing-scheduler*,env:$env}", "aggregator": "avg"}], "formulas": [{"formula": "query1 / query2 * 100"}]}], "autoscale": true, "custom_unit": "%", "text_align": "center", "precision": 2}, "layout": {"x": 6, "y": 36, "width": 3, "height": 2}}, {"id": 14, "definition": {"title": "Total SF Executions (24h)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.states.executions_started{statemachinename:*ai-marketing-scheduler*,env:$env}", "aggregator": "sum"}]}], "autoscale": true, "text_align": "center", "precision": 0}, "layout": {"x": 9, "y": 36, "width": 3, "height": 2}}, {"id": 15, "definition": {"type": "note", "content": "## Individual Lambda Function Details\n\nDetailed monitoring for key Lambda functions in the ai-marketing-scheduler workflow", "background_color": "gray", "font_size": "14", "text_align": "left", "vertical_align": "center", "show_tick": false, "has_padding": true}, "layout": {"x": 0, "y": 39, "width": 12, "height": 1}}, {"id": 16, "definition": {"title": "Draft Action Consumer Daemon - Duration", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-draft-action-consumer-daemon,env:$env}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "max:aws.lambda.duration{functionname:ai-marketing-scheduler-draft-action-consumer-daemon,env:$env}"}], "style": {"palette": "warm", "line_type": "dashed", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 40, "width": 6, "height": 3}}, {"id": 17, "definition": {"title": "Draft Action Consumer Daemon - Invocations vs Errors", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-draft-action-consumer-daemon,env:$env}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-draft-action-consumer-daemon,env:$env}"}], "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 40, "width": 6, "height": 3}}, {"id": 18, "definition": {"title": "URL Processing Lambdas - Duration", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-url-selector,env:$env}"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "max:aws.lambda.duration{functionname:ai-marketing-scheduler-url-scraper,env:$env}"}], "style": {"palette": "orange", "line_type": "dashed", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 43, "width": 6, "height": 3}}, {"id": 19, "definition": {"title": "URL Processing Lambdas - Invocations vs Errors", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-url-selector,env:$env}"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-url-scraper,env:$env}"}], "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "bars"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 43, "width": 6, "height": 3}}, {"id": 20, "definition": {"title": "AI Processing Lambdas - Duration Comparison", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-keyword-extractor,env:$env}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-recommendation-generator,env:$env}"}], "style": {"palette": "blue", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 46, "width": 6, "height": 3}}, {"id": 21, "definition": {"title": "AI Processing Lambdas - Error Rates", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-keyword-extractor,env:$env}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-recommendation-generator,env:$env}"}], "style": {"palette": "blue", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 46, "width": 6, "height": 3}}, {"id": 33, "definition": {"title": "Recent Error <PERSON> - Lambda Functions", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "pod_name:ai-marketing-scheduler-lambda* env:$env status:error", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "host", "width": "auto"}, {"field": "service", "width": "auto"}, {"field": "functionname", "width": "auto"}, {"field": "message", "width": "auto"}, {"field": "content", "width": "compact"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 49, "width": 12, "height": 4}}, {"id": 64, "definition": {"type": "note", "content": "## SEO GraphQL API Monitoring\n\nMonitoring GraphQL API performance for SEO recommendations, scraped pages, keywords, and content management", "background_color": "blue", "font_size": "14", "text_align": "left", "vertical_align": "center", "show_tick": false, "has_padding": true}, "layout": {"x": 0, "y": 53, "width": 12, "height": 1}}, {"id": 65, "definition": {"title": "SEO GraphQL API Performance", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:trace.express.request.hits{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env} by {resource_name}.as_count()"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:trace.express.request.duration{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env} by {resource_name}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:trace.express.request.errors{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env} by {resource_name}.as_count()"}], "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 54, "width": 12, "height": 3}}, {"id": 67, "definition": {"title": "SEO API Requests (24h)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:trace.express.request.hits{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env}", "aggregator": "sum"}]}], "autoscale": true, "text_align": "center", "precision": 0}, "layout": {"x": 0, "y": 57, "width": 4, "height": 2}}, {"id": 68, "definition": {"title": "SEO API Response Time", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:trace.express.request.duration{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env}", "aggregator": "avg"}]}], "autoscale": true, "custom_unit": "ms", "text_align": "center", "precision": 0}, "layout": {"x": 4, "y": 57, "width": 4, "height": 2}}, {"id": 69, "definition": {"title": "SEO API Error Rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:trace.express.request.errors{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env}", "aggregator": "sum"}, {"data_source": "metrics", "name": "query2", "query": "sum:trace.express.request.hits{service:seo-automation-api-gateway,resource_name:*graphql*,env:$env}", "aggregator": "sum"}], "formulas": [{"formula": "query1 / query2 * 100"}]}], "autoscale": true, "custom_unit": "%", "text_align": "center", "precision": 2}, "layout": {"x": 8, "y": 57, "width": 4, "height": 2}}, {"id": 2976755068841354, "definition": {"title": "SEO Pipeline Health", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-store-draft,env:$env}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-surfacer-daemon,env:$env}"}], "style": {"palette": "blue", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-publisher-daemon,env:$env}"}], "style": {"palette": "purple", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "invocations", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 59, "width": 12, "height": 3}}, {"id": 5422430921784274, "definition": {"title": "SEO API Activity", "title_size": "16", "title_align": "left", "requests": [{"response_format": "event_list", "query": {"data_source": "logs_stream", "query_string": "service:seo-automation-api-gateway env:$env (\"graphql\" OR \"recommendation\" OR \"seo\" OR \"scrape\" OR \"keyword\")", "indexes": [], "storage": "hot", "sort": {"order": "desc", "column": "timestamp"}}, "columns": [{"field": "status_line", "width": "auto"}, {"field": "timestamp", "width": "auto"}, {"field": "host", "width": "auto"}, {"field": "service", "width": "auto"}, {"field": "resource_name", "width": "auto"}, {"field": "message", "width": "auto"}, {"field": "content", "width": "compact"}]}], "type": "list_stream"}, "layout": {"x": 0, "y": 62, "width": 12, "height": 4}}, {"id": 89, "definition": {"type": "note", "content": "## External API Performance\n\nMonitoring Firecrawl web scraping, DataForSEO rankings, and third-party service health", "background_color": "purple", "font_size": "14", "text_align": "left", "vertical_align": "center", "show_tick": false, "has_padding": true}, "layout": {"x": 0, "y": 66, "width": 12, "height": 1}}, {"id": 90, "definition": {"title": "URL Processing Performance", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.duration{functionname:ai-marketing-scheduler-url-scraper,env:$env}"}], "style": {"palette": "dog_classic", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-scraper,env:$env}"}], "style": {"palette": "warm", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 0, "y": 67, "width": 6, "height": 3}}, {"id": 91, "definition": {"title": "External API Response Times", "title_size": "16", "title_align": "left", "show_legend": false, "type": "timeseries", "requests": [{"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-keyword-extractor,env:$env}"}], "style": {"palette": "green", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}, {"response_format": "timeseries", "queries": [{"data_source": "metrics", "name": "query1", "query": "avg:aws.lambda.duration{functionname:ai-marketing-scheduler-recommendation-generator,env:$env}"}], "style": {"palette": "blue", "line_type": "solid", "line_width": "normal"}, "display_type": "line"}], "yaxis": {"label": "milliseconds", "scale": "linear", "include_zero": true, "min": "auto", "max": "auto"}}, "layout": {"x": 6, "y": 67, "width": 6, "height": 3}}, {"id": 92, "definition": {"title": "URL Processing Success Rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-url-selector,env:$env} + sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-url-scraper,env:$env}", "aggregator": "sum"}, {"data_source": "metrics", "name": "query2", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-url-selector,env:$env} + sum:aws.lambda.errors{functionname:ai-marketing-scheduler-url-scraper,env:$env}", "aggregator": "sum"}], "formulas": [{"formula": "(query1 - query2) / query1 * 100"}]}], "autoscale": true, "custom_unit": "%", "text_align": "center", "precision": 2}, "layout": {"x": 0, "y": 70, "width": 4, "height": 2}}, {"id": 93, "definition": {"title": "AI Processing Success Rate", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-recommendation-generator,env:$env}", "aggregator": "sum"}, {"data_source": "metrics", "name": "query2", "query": "sum:aws.lambda.errors{functionname:ai-marketing-scheduler-recommendation-generator,env:$env}", "aggregator": "sum"}], "formulas": [{"formula": "(query1 - query2) / query1 * 100"}]}], "autoscale": true, "custom_unit": "%", "text_align": "center", "precision": 2}, "layout": {"x": 4, "y": 70, "width": 4, "height": 2}}, {"id": 94, "definition": {"title": "URL Processing Calls (24h)", "title_size": "16", "title_align": "left", "type": "query_value", "requests": [{"response_format": "scalar", "queries": [{"data_source": "metrics", "name": "query1", "query": "sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-url-selector,env:$env} + sum:aws.lambda.invocations{functionname:ai-marketing-scheduler-url-scraper,env:$env}", "aggregator": "sum"}]}], "autoscale": true, "text_align": "center", "precision": 0}, "layout": {"x": 8, "y": 70, "width": 4, "height": 2}}], "template_variables": [{"name": "env", "available_values": ["production", "staging"], "default": "production"}, {"name": "functionname", "prefix": "functionname", "available_values": ["ai-marketing-scheduler-draft-action-consumer-daemon", "ai-marketing-scheduler-draft-action-scheduler", "ai-marketing-scheduler-url-selector", "ai-marketing-scheduler-url-scraper", "ai-marketing-scheduler-keyword-extractor", "ai-marketing-scheduler-recommendation-generator", "ai-marketing-scheduler-store-draft", "ai-marketing-scheduler-catch-failure", "ai-marketing-scheduler-entitlement-fetcher", "ai-marketing-scheduler-surfacer-daemon", "ai-marketing-scheduler-emailer-daemon", "ai-marketing-scheduler-get-surfaced-actions", "ai-marketing-scheduler-publisher", "ai-marketing-scheduler-rank-batch-splitter", "ai-marketing-scheduler-rank-queuer-daemon", "ai-marketing-scheduler-rank-webhook"], "default": "*"}, {"name": "service", "prefix": "service", "available_values": ["ai-marketing-scheduler", "seo-automation-api-gateway", "client-marketing-service"], "default": "*"}, {"name": "team", "prefix": "team", "available_values": [], "default": "*"}], "layout_type": "ordered", "notify_list": [], "reflow_type": "fixed", "tags": ["team:client-marketing"]}