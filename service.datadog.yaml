apiVersion: v3
kind: system
metadata:
  name: seo-automation
  displayName: SEO Automation
  owner: client-marketing
  contacts:
    - name: Support Slack
      type: slack
      contact: https://luxurypresence.slack.com/archives/PZTCXIM

  links:
    - type: repo
      name: seo-automation
      url: https://github.com/luxurypresence/seo-automation.git
      provider: github

spec:
  lifecycle: production
  tier: critical

integrations:
  pagerduty:
    serviceURL: https://luxurypresence.pagerduty.com/service-directory/P0R0PQE

---
apiVersion: v3
kind: service
metadata:
  name: client-marketing-service
  displayName: Client Marketing Service
  owner: client-marketing
  description: Client Marketing Service for routing and managing API requests
  links:
    - type: repo
      name: client-marketing-service
      url: https://github.com/luxurypresence/seo-automation/tree/master/packages/client-marketing-service
      provider: github
    - type: repo
      name: kubernetes apps
      url: https://github.com/luxurypresence/apps-k8s-config/tree/main/apps/client-marketing-service
      provider: github
    - type: seo-automation
      name: Kubernetes seo-automation
      url: https://app.datadoghq.com/dash/integration/30322/kubernetes-pods-overview?tpl_var_cluster[0]=production-eks1&tpl_var_deployment[0]=client-marketing-service-deployment&tpl_var_namespace[0]=apps&live=true
      provider: datadog
    - type: other
      name: Logs
      url: https://app.datadoghq.com/logs?query=service:client-marketing-service&live=true
      provider: datadog
    - type: other
      name: ArgoCD Production
      url: https://argocd.ops.luxurycoders.com/applications/client-marketing-service-production
      provider: argocd
    - type: other
      name: ArgoCD Staging
      url: https://argocd.staging.luxurycoders.com/applications/client-marketing-service
      provider: argocd
spec:
  lifecycle: production
  tier: critical
  type: web
  componentOf:
    - system:seo-automation
  languages:
    - javascript
    - nodejs

---
apiVersion: v3
kind: service
metadata:
  name: ai-marketing-scheduler
  displayName: AI Marketing Scheduler
  owner: client-marketing
  description: AI Marketing Scheduler for scheduling AI marketing tasks
  links:
    - type: repo
      name: ai-marketing-scheduler
      url: https://github.com/luxurypresence/seo-automation/tree/master/packages/ai-marketing-scheduler
      provider: github
    - type: repo
      name: kubernetes apps
      url: https://github.com/luxurypresence/apps-k8s-config/tree/main/apps/ai-marketing-scheduler-lambda
      provider: github
    - type: other
      name: Logs
      url: https://app.datadoghq.com/logs?query=pod_name:ai-marketing-scheduler-lambda*&live=true
      provider: datadog
    - type: other
      name: ArgoCD Production
      url: https://argocd.ops.luxurycoders.com/applications/ai-marketing-scheduler-lambda-production
      provider: argocd
    - type: other
      name: ArgoCD Staging
      url: https://argocd.staging.luxurycoders.com/applications/ai-marketing-scheduler-lambda
      provider: argocd
spec:
  lifecycle: production
  tier: critical
  type: lambda
  componentOf:
    - system:seo-automation
  languages:
    - javascript
    - nodejs

---
apiVersion: v3
kind: service
metadata:
  name: bloom-scheduler
  displayName: Bloom Scheduler
  owner: client-marketing
  description: Bloom Scheduler for scheduling Bloom tasks
  links:
    - type: repo
      name: bloom-scheduler
      url: https://github.com/luxurypresence/seo-automation/tree/master/packages/data-syndication-lambda
      provider: github
    - type: repo
      name: kubernetes apps
      url: https://github.com/luxurypresence/apps-k8s-config/tree/main/apps/bloom-scheduler
      provider: github
    - type: other
      name: Logs
      url: https://app.datadoghq.com/logs?query=pod_name:bloom-scheduler-*&live=true
      provider: datadog
    - type: other
      name: ArgoCD Production
      url: https://argocd.ops.luxurycoders.com/applications/bloom-scheduler-production
      provider: argocd
    - type: other
      name: ArgoCD Staging
      url: https://argocd.staging.luxurycoders.com/applications/bloom-scheduler
      provider: argocd
spec:
  lifecycle: production
  tier: critical
  type: lambda
  componentOf:
    - system:seo-automation
  languages:
    - javascript
    - nodejs