name: Notify CircleCI on PR change

on:
  pull_request:
    branches: ['main']
    types: [opened, synchronize]
    paths-ignore:
      - 'service.datadog.yaml'

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - name: Build CircleCI payload
        env:
          CIRCLECI_BRANCH_BASE: ${{ github.base_ref }}
        run: echo "CIRCLECI_PAYLOAD={\"pull_request\":true, \"pr_target_branch\":\"${CIRCLECI_BRANCH_BASE}\"}" >> $GITHUB_ENV
      - name: Trigger CircleCI
        env:
          CIRCLECI_BRANCH: ${{ github.head_ref }}
          CIRCLECI_PROJECT: ${{ github.repository }}
        uses: promiseofcake/circleci-trigger-action@v1
        with:
          user-token: ${{ secrets.CIRCLECI_TOKEN }}
          project-slug: ${{ env.CIRCLECI_PROJECT }}
          branch: ${{ env.CIRCLECI_BRANCH }}
          payload: ${{ env.CIRCLECI_PAYLOAD }}
