# Code generated by gitStream GitHub app - DO NOT EDIT

name: gitStream workflow automation
run-name: |
  /:\ gitStream: PR #${{ fromJSON(fromJSON(github.event.inputs.client_payload)).pullRequestNumber }} from ${{ github.event.inputs.full_repository }}

on:
  workflow_dispatch:
    inputs:
      client_payload:
        description: The Client payload
        required: true
      full_repository:
        description: the repository name include the owner in `owner/repo_name` format
        required: true
      head_ref:
        description: the head sha
        required: true
      base_ref:
        description: the base ref 
        required: true
      installation_id:
        description: the installation id
        required: false
      resolver_url:
        description: the resolver url to pass results to
        required: true
      resolver_token:
        description: Optional resolver token for resolver service
        required: false
        default: ''

jobs:
  gitStream:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    name: gitStream workflow automation
    steps:
      - name: Evaluate Rules
        uses: linear-b/gitstream-github-action@v1
        id: rules-engine
        with:
          full_repository: ${{ github.event.inputs.full_repository }}
          head_ref: ${{ github.event.inputs.head_ref }}
          base_ref: ${{ github.event.inputs.base_ref }}
          client_payload: ${{ github.event.inputs.client_payload }}
          installation_id: ${{ github.event.inputs.installation_id }}
          resolver_url: ${{ github.event.inputs.resolver_url }}
          resolver_token: ${{ github.event.inputs.resolver_token }}

