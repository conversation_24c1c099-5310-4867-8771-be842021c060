#!/bin/bash

# Script to ensure changesets exist for CI in stacked PRs
# Can be called manually or from git hooks

set -e

# Source common functions
SCRIPT_DIR="$(dirname "${BASH_SOURCE[0]}")"
source "$SCRIPT_DIR/changeset-common.sh"

# Colors for output (only use if terminal supports it)
if [ -t 1 ]; then
  RED='\033[0;31m'
  GREEN='\033[0;32m'
  YELLOW='\033[1;33m'
  BLUE='\033[0;34m'
  NC='\033[0m' # No Color
else
  RED=''
  GREEN=''
  YELLOW=''
  BLUE=''
  NC=''
fi

# Get current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Skip if on main/master
if [ "$CURRENT_BRANCH" = "main" ] || [ "$CURRENT_BRANCH" = "master" ]; then
  echo "On main branch, skipping changeset check"
  exit 0
fi

# Check if we're in a Graphite stack
if ! is_graphite_branch "$CURRENT_BRANCH"; then
  echo "Not a Graphite-tracked branch, skipping changeset check"
  exit 0
fi

# Check if this is the topmost branch in the stack
if is_top_branch "$CURRENT_BRANCH"; then
  IS_TOP_BRANCH=true
else
  IS_TOP_BRANCH=false
fi
# Count existing changesets
CHANGESET_DIR=$(get_changeset_dir)
if [ ! -d "$CHANGESET_DIR" ]; then
  echo -e "${RED}Error: .changeset directory not found${NC}"
  exit 1
fi

# Count actual changeset files (excluding README and config)
CHANGESET_COUNT=$(find "$CHANGESET_DIR" -name "*.md" -not -name "README.md" -not -name "config.json" 2>/dev/null | wc -l | tr -d '[:space:]')

# Check if any changeset exists in the commit history of this branch
DEFAULT_BRANCH="$(git remote show origin 2>/dev/null | sed -n '/HEAD branch/s/.*: //p')"
[ -z "$DEFAULT_BRANCH" ] && DEFAULT_BRANCH="main"
COMMITS_WITH_CHANGESET=$(git log "${DEFAULT_BRANCH}..HEAD" --pretty=format:"%H" --name-only | grep "\.changeset/.*\.md" | wc -l 2>/dev/null || echo "0")
# Clean up the value to ensure it's a valid integer
COMMITS_WITH_CHANGESET=$(echo "$COMMITS_WITH_CHANGESET" | tr -d '[:space:]' | grep -o '[0-9]*' | head -1)
[ -z "$COMMITS_WITH_CHANGESET" ] && COMMITS_WITH_CHANGESET=0

if [ "$CHANGESET_COUNT" -eq "0" ] && [ "$COMMITS_WITH_CHANGESET" -eq "0" ]; then

  if [ "$IS_TOP_BRANCH" = true ]; then
    # TOP BRANCH: Require real changeset with version bump
    echo -e "${RED}⚠️  No changeset found in TOP branch of stack${NC}"
    echo -e "${YELLOW}The topmost branch requires a real changeset with version bump and description.${NC}"
    echo -e "${BLUE}Run: pnpm changeset${NC}"
    echo ""
    echo "This will prompt you to:"
    echo "  1. Select packages to version"
    echo "  2. Choose version bump type (patch/minor/major)"
    echo "  3. Write a description of changes"

    # In interactive mode, offer to run changeset command
    if [ -t 0 ]; then
      read -p "Run 'pnpm changeset' now? (y/n) " -n 1 -r
      echo
      if [[ $REPLY =~ ^[Yy]$ ]]; then
        pnpm changeset

        # Check if changeset was created
        NEW_CHANGESET_COUNT=$(find "$CHANGESET_DIR" -name "*.md" -not -name "README.md" -not -name "config.json" 2>/dev/null | wc -l | tr -d ' ')
        if [ "$NEW_CHANGESET_COUNT" -gt "$CHANGESET_COUNT" ]; then
          echo -e "${GREEN}✅ Changeset created successfully${NC}"

          # Offer to commit
          read -p "Commit the changeset? (y/n) " -n 1 -r
          echo
          if [[ $REPLY =~ ^[Yy]$ ]]; then
            git add "$CHANGESET_DIR"/*.md
            git commit -m "chore: add changeset"
            echo -e "${GREEN}✅ Changeset committed${NC}"
          fi
        fi
      else
        echo -e "${RED}Push cancelled. Please create a changeset before pushing.${NC}"
        exit 1
      fi
    else
      echo -e "${RED}Non-interactive mode: Cannot proceed without changeset on top branch${NC}"
      exit 1
    fi

  else
    # NON-TOP BRANCH: Auto-create empty changeset
    echo -e "${YELLOW}⚠️  No changeset found in non-top branch${NC}"
    echo -e "${BLUE}Auto-creating empty changeset for CI (this is a stacked PR)${NC}"

    # Create empty changeset with branch name and timestamp
    TIMESTAMP=$(date +%s)
    SAFE_BRANCH_NAME=$(echo "$CURRENT_BRANCH" | sed 's/[^a-zA-Z0-9-]/-/g')
    CHANGESET_FILE="$CHANGESET_DIR/auto-${SAFE_BRANCH_NAME}-${TIMESTAMP}.md"

    cat > "$CHANGESET_FILE" << EOF
---
---

EOF

    echo -e "${GREEN}✅ Created empty changeset: $CHANGESET_FILE${NC}"

    # Auto-commit in non-interactive mode or ask in interactive
    if [ -t 0 ]; then
      read -p "Commit the empty changeset? (y/n) " -n 1 -r
      echo
      if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add "$CHANGESET_FILE"
        git commit --no-verify -m "chore: add empty changeset for CI"
        echo -e "${GREEN}✅ Empty changeset committed${NC}"
      fi
    else
      # Non-interactive: auto-commit for non-top branches
      git add "$CHANGESET_FILE"
      git commit --no-verify -m "chore: add empty changeset for CI"
      echo -e "${GREEN}✅ Empty changeset auto-committed${NC}"
    fi
  fi

else
  # At least one changeset exists (either in current dir or in commit history)
  if [ "$CHANGESET_COUNT" -gt "0" ]; then
    if [ "$IS_TOP_BRANCH" = true ]; then
      # Check if it's a real changeset (has package versions)
      HAS_VERSIONS=$(grep -l "^\"@" "$CHANGESET_DIR"/*.md 2>/dev/null | wc -l | tr -d '[:space:]')
      if [ "$HAS_VERSIONS" -eq "0" ]; then
        echo -e "${YELLOW}⚠️  Found empty changeset(s) but this is the TOP branch${NC}"
        echo -e "${YELLOW}Consider adding a real changeset with version bumps${NC}"
      else
        echo -e "${GREEN}✅ Changeset exists for TOP branch (found $CHANGESET_COUNT changeset files)${NC}"
      fi
    else
      echo -e "${GREEN}✅ Changeset exists for non-top branch (found $CHANGESET_COUNT changeset files)${NC}"
    fi
  else
    # CHANGESET_COUNT is 0 but COMMITS_WITH_CHANGESET > 0
    echo -e "${GREEN}✅ Changeset found in commit history${NC}"
  fi
fi