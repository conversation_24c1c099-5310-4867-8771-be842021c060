#!/bin/bash

# Common functions for changeset management in Graphite stacks

# Get the changeset directory (always at repo root)
get_changeset_dir() {
  local git_root
  git_root=$(git rev-parse --show-toplevel)
  echo "$git_root/.changeset"
}

# Check if branch is tracked by Graphite
is_graphite_branch() {
  local current_branch="$1"

  # First check: Does gt command exist and work?
  if ! command -v gt >/dev/null 2>&1; then
    return 1 # false - gt not available
  fi

  # Second check: Does gt recognize this branch as part of a stack?
  # Use gt branch info to check if branch is tracked
  if ! gt branch info 2>/dev/null | grep -q "tracked.*true"; then
    return 1 # false - not tracked by Graphite
  fi

  return 0 # true - is a Graphite branch
}

# Determine if current branch is the top branch in the stack
is_top_branch() {
  local current_branch="$1"
  local stack_output top_candidate bottom_candidate

  stack_output="$(gt log --stack 2>/dev/null || echo '')"
  top_candidate="$(printf '%s\n' "$stack_output" | sed -n '1p' | sed 's/^[*-> ]*//')"

  if [ "$top_candidate" = "$current_branch" ]; then
    return 0 # true - is top branch
  fi

  # Fallback in case ordering is reversed in your gt version
  bottom_candidate="$(printf '%s\n' "$stack_output" | tail -1 | sed 's/^[*-> ]*//')"
  if [ "$bottom_candidate" = "$current_branch" ]; then
    return 0 # true - is top branch
  fi

  return 1 # false - not top branch
}