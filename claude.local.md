
# Rules

## Workflow & Checkpoints

- Commit often so there are clear rollback points.
- At the start of every session, create a SESSION_NOTES.md file.
- Update SESSION_NOTES.md at least every ~20 messages or after significant changes.

## Session Notes (SESSION_NOTES.md)

Must include:

- Decisions made
- TODOs
- Files touched
- Next steps (1–3 items)

## Purpose:

- Provides breadcrumbs for later reference
- Captures context consumed per session

## GraphQL Schema

- Never edit schema.graphql directly.
- It is auto-generated from GraphQL entities, DTOs, and resolvers.
