#!/bin/bash

# Auto-add empty changeset after rebase in stacked PRs
# This handles the case where a bottom PR with changeset merges

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0" 2>/dev/null || realpath "$0" 2>/dev/null || echo "$0")")"
source "$SCRIPT_DIR/../scripts/changeset-common.sh"

# Check if we're in a Graphite stack (not on main/master)
current_branch=$(git rev-parse --abbrev-ref HEAD)
if [ "$current_branch" = "main" ] || [ "$current_branch" = "master" ]; then
  exit 0
fi

# Check if this is a Graphite-tracked branch
if ! is_graphite_branch "$current_branch"; then
  exit 0
fi

# Check if this is the topmost branch (non-mutating)
if is_top_branch "$current_branch"; then
  is_top_branch=true
else
  is_top_branch=false
fi
# Check if there are any changesets in the current branch
changeset_dir=$(get_changeset_dir)
changeset_count=$(find "$changeset_dir" -name "*.md" -not -name "README.md" -not -name "config.json" 2>/dev/null | wc -l)

# If no changesets exist and this is NOT the top branch, add an empty one
if [ "$changeset_count" -eq 0 ] && [ "$is_top_branch" = false ]; then
  echo "📦 No changeset found after rebase (non-top branch). Adding empty changeset for CI..."

  # Create empty changeset with timestamp to ensure uniqueness
  timestamp=$(date +%s)
  safe_branch=$(echo "$current_branch" | sed 's/[^a-zA-Z0-9-]/-/g')
  cat > "$changeset_dir/auto-rebase-${safe_branch}-${timestamp}.md" << EOF
---
---

EOF

  git add "$changeset_dir/auto-rebase-${safe_branch}-${timestamp}.md"
  git commit --no-verify -m "chore: auto-add empty changeset for CI after rebase"

  echo "✅ Empty changeset added automatically (non-top branch)"
elif [ "$changeset_count" -eq 0 ] && [ "$is_top_branch" = true ]; then
  echo "⚠️  No changeset found after rebase on TOP branch"
  echo "📝 Please create a real changeset with: pnpm changeset"
fi