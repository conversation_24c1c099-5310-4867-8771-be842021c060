FROM node:22-alpine

RUN corepack enable
# Update corepack to the latest version to prevent signature issue.
# https://github.com/pnpm/pnpm/issues/9029#issuecomment-2629866277
RUN npm i -g corepack@latest


ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

ARG NODE_ENV=development
ARG NPM_AUTH_TOKEN
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
ENV HUSKY=0

WORKDIR /usr/src/app

COPY .npmrc .npmrc

COPY . .

RUN pnpm install --global serverless@3
RUN pnpm install --frozen-lockfile

# Package all dependencies to be included in the artifact
RUN pnpm --filter blog-topic-generator deploy output --prod

# Switch to deploy directory
WORKDIR /usr/src/app/output

ENTRYPOINT pnpm run deploy
#IN case I need to debug Docker
#ENTRYPOINT tail -f /dev/null