# blog-topic-generator

## 0.8.9

### Patch Changes

- [#889](https://github.com/luxurypresence/seo-automation/pull/889) [`93ade07`](https://github.com/luxurypresence/seo-automation/commit/93ade07cceaaaa1fd5142a291a541233198d336c) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes ResultSelector error due to non consistent outputs

## 0.8.8

### Patch Changes

- [#885](https://github.com/luxurypresence/seo-automation/pull/885) [`9604276`](https://github.com/luxurypresence/seo-automation/commit/960427639a61803531e5c1c830da180698a8efad) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for passing execution variables of enricher to processor machine

## 0.8.7

### Patch Changes

- [#880](https://github.com/luxurypresence/seo-automation/pull/880) [`caaf5f8`](https://github.com/luxurypresence/seo-automation/commit/caaf5f84f7e757fcc918f40458505809927139c0) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for SUCCEDED_0.json derive

## 0.8.6

### Patch Changes

- [#878](https://github.com/luxurypresence/seo-automation/pull/878) [`4794111`](https://github.com/luxurypresence/seo-automation/commit/4794111be071939388c5ba98788843e8d6abde92) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes resultSelector errors

## 0.8.5

### Patch Changes

- [#877](https://github.com/luxurypresence/seo-automation/pull/877) [`8cbfdde`](https://github.com/luxurypresence/seo-automation/commit/8cbfdde0517ac2a8e22791f041b11e0e2d05d39c) Thanks [@diegosr90](https://github.com/diegosr90)! - Add missing required field to ResultWriter definition

## 0.8.4

### Patch Changes

- [#859](https://github.com/luxurypresence/seo-automation/pull/859) [`0db0d24`](https://github.com/luxurypresence/seo-automation/commit/0db0d2407bb844b55f38142bbb7d84e965c7dd27) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds consolidation state for data prep before Map

## 0.8.3

### Patch Changes

- [#856](https://github.com/luxurypresence/seo-automation/pull/856) [`e075540`](https://github.com/luxurypresence/seo-automation/commit/e075540a027797e010aca3823a8267930aa8b5eb) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for ResultWriter output

## 0.8.2

### Patch Changes

- [#853](https://github.com/luxurypresence/seo-automation/pull/853) [`ccb315a`](https://github.com/luxurypresence/seo-automation/commit/ccb315a6136797a1f1987c4c9f4f62011c6ecd17) Thanks [@diegosr90](https://github.com/diegosr90)! - Add missing config

## 0.8.1

### Patch Changes

- [#851](https://github.com/luxurypresence/seo-automation/pull/851) [`2e896e5`](https://github.com/luxurypresence/seo-automation/commit/2e896e51d814251d823ce9ee0ff4a5032e75f59f) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for itemREader conf

## 0.8.0

### Minor Changes

- [#850](https://github.com/luxurypresence/seo-automation/pull/850) [`2f6b7c6`](https://github.com/luxurypresence/seo-automation/commit/2f6b7c626311c0336f9dd7701003d045c045614e) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for stepFn invocation

## 0.7.2

### Patch Changes

- [#848](https://github.com/luxurypresence/seo-automation/pull/848) [`e860888`](https://github.com/luxurypresence/seo-automation/commit/e8608889b01d84fb67234f2221be16577c36abe9) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes machine defition order

## 0.7.1

### Patch Changes

- [#847](https://github.com/luxurypresence/seo-automation/pull/847) [`5d71656`](https://github.com/luxurypresence/seo-automation/commit/5d716560953e1b16062a6758569e71ba93e15f94) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix variable definition for PROCESSOR_STATE_MACHINE_ARN

## 0.7.0

### Minor Changes

- [#845](https://github.com/luxurypresence/seo-automation/pull/845) [`2b7c980`](https://github.com/luxurypresence/seo-automation/commit/2b7c980e0cab5d2ae46184b9f65851e0e992e3e9) Thanks [@diegosr90](https://github.com/diegosr90)! - Refactor BlogTopic into 2 step functions (enrich, generate)

## 0.6.1

### Patch Changes

- [#843](https://github.com/luxurypresence/seo-automation/pull/843) [`d1d632a`](https://github.com/luxurypresence/seo-automation/commit/d1d632acc281eccf10efb74371f4e35a37aaddfb) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes folder name for keyword map lookup

## 0.6.0

### Minor Changes

- [#839](https://github.com/luxurypresence/seo-automation/pull/839) [`25e194f`](https://github.com/luxurypresence/seo-automation/commit/25e194f8df4841a5451292e7ce6caebf862e2150) Thanks [@diegosr90](https://github.com/diegosr90)! - Refactors from neste array mapping to flat map instead

## 0.5.9

### Patch Changes

- [#834](https://github.com/luxurypresence/seo-automation/pull/834) [`f283667`](https://github.com/luxurypresence/seo-automation/commit/f283667d5d5c4b791a686e9cc372da30398124e8) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds check for items before second map

## 0.5.8

### Patch Changes

- [#822](https://github.com/luxurypresence/seo-automation/pull/822) [`2eebdd6`](https://github.com/luxurypresence/seo-automation/commit/2eebdd6bbab0d8966ab03586265ef49724b8ad67) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for execution variable propagation and polling logic

## 0.5.7

### Patch Changes

- [#821](https://github.com/luxurypresence/seo-automation/pull/821) [`0c5d0a8`](https://github.com/luxurypresence/seo-automation/commit/0c5d0a86cba038cc9afabb7706f0417eb0ec537c) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for runtime error regarding execution variables

## 0.5.6

### Patch Changes

- [#816](https://github.com/luxurypresence/seo-automation/pull/816) [`861c7e0`](https://github.com/luxurypresence/seo-automation/commit/861c7e0d9a89133baec4f7545e297702798ad38a) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix data handling in between steps

## 0.5.5

### Patch Changes

- [#812](https://github.com/luxurypresence/seo-automation/pull/812) [`b5d2c07`](https://github.com/luxurypresence/seo-automation/commit/b5d2c076d724bbc706d2f15763a56cd496b9f3c9) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix CheckEntitlementsCount condition on validEntitlements

## 0.5.4

### Patch Changes

- [#810](https://github.com/luxurypresence/seo-automation/pull/810) [`4c929cb`](https://github.com/luxurypresence/seo-automation/commit/4c929cb0bb79db6a0cb62a1499b8f4679f91eb92) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for log config

## 0.5.3

### Patch Changes

- [#804](https://github.com/luxurypresence/seo-automation/pull/804) [`3f8c45f`](https://github.com/luxurypresence/seo-automation/commit/3f8c45f4f486d54a2fde941b5e5b93dbe0721021) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for serverless schema

## 0.5.2

### Patch Changes

- [#799](https://github.com/luxurypresence/seo-automation/pull/799) [`282ae97`](https://github.com/luxurypresence/seo-automation/commit/282ae978d9dbad7fa4258909ff192dd1ce74e989) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for serverless build

## 0.5.1

### Patch Changes

- [#794](https://github.com/luxurypresence/seo-automation/pull/794) [`22ac514`](https://github.com/luxurypresence/seo-automation/commit/22ac514b365e32d415c6e6156843b38152743dcb) Thanks [@diegosr90](https://github.com/diegosr90)! - Add VPC config and default execution values

## 0.5.0

### Minor Changes

- [#788](https://github.com/luxurypresence/seo-automation/pull/788) [`bf1bb9e`](https://github.com/luxurypresence/seo-automation/commit/bf1bb9e4c0eefdd16cacc04f88390921bdb7d86c) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-903: Improves s3 folder structure, dryRun and companyIds

  - Implemented semantic S3 structure (`blog-topic-jobs/YYYY-MM-DD/{execution|direct}`) matching `brand-profile-generator`.
  - Consolidated S3 path logic into `buildExecutionFileKey`; always enabled (no env toggle).
  - Added `-dry-run` folder/file suffix automatically when `dryRun` is true.
  - Defaulted `dryRun` to true; Step Functions Choice stops after `flatten-per-neighborhood` in dry-run.
  - Added `companyIds` filter to `fetch-entitlements` without changing output shape.
  - Moved S3 persistence of AI output to `poll-blog-topics-status` (raw OpenAI response).
  - Set `poll-blog-topics-status` to always write non-dry-run keys.
  - Reused “direct” prefix logic consistent with `brand-profile-generator`.
  - Updated and expanded unit tests; all passing.
  - Added package `README.md` with execution parameters and S3 structure.

## 0.4.0

### Minor Changes

- [#785](https://github.com/luxurypresence/seo-automation/pull/785) [`6fe0a96`](https://github.com/luxurypresence/seo-automation/commit/6fe0a964a20d6febe5395274c1a35dfed4bf94cf) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-908: Adds type field on Blog Topic

  ## Summary

  Added a new `type` field to blog topics to distinguish between `article` and `listicle` content types.

  ## Changes Made

  ### Database

  - **Migration**: Added `type` column to `blog_topic` table with enum values `('article', 'listicle')`
  - **Default**: Existing rows default to `'article'` for backward compatibility

  ### API Layer

  - **Entity**: Added `type` field with enum validation
  - **DTOs**:
    - `CreateBlogTopicInput`: Required `type` field
    - `UpdateBlogTopicInput`: Optional `type` field
    - `BlogTopicFiltersInput`: Added `type` filter support

  ### Service Layer

  - **BlogTopicService**: Updated `createBulk` and filtering logic
  - **BlogTopicGenerator**: Updated types and lambda to pass `type` field

  ### Testing

  - Updated unit tests for service and resolver layers
  - Added test coverage for `type` field functionality

  ## Impact

  - Enables content type classification for better content management
  - Maintains backward compatibility with existing data
  - Provides filtering capabilities for content type queries

## 0.3.3

### Patch Changes

- [#784](https://github.com/luxurypresence/seo-automation/pull/784) [`307a148`](https://github.com/luxurypresence/seo-automation/commit/307a14860c9d457079f773d7574233939e124bde) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for nested Maps

## 0.3.2

### Patch Changes

- [#781](https://github.com/luxurypresence/seo-automation/pull/781) [`84ca18d`](https://github.com/luxurypresence/seo-automation/commit/84ca18d9e2d1dad560038fe204cefce340b4041b) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for S3 ASL issue

## 0.3.1

### Patch Changes

- [#780](https://github.com/luxurypresence/seo-automation/pull/780) [`c50bc09`](https://github.com/luxurypresence/seo-automation/commit/c50bc0986d58381104dc3e51f458bc9bb04db5c7) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for serverless build

## 0.3.0

### Minor Changes

- [#766](https://github.com/luxurypresence/seo-automation/pull/766) [`8286915`](https://github.com/luxurypresence/seo-automation/commit/828691511c50aba7ddbac56f8fe92e64a5aba74a) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-851: Integrates StepFn

  ### Added

  - **Blog Topic Generation Pipeline**: Complete Step Functions workflow for generating blog topics per neighborhood
  - **Background Processing**: Async job processing with polling mechanism using OpenAI Responses API
  - **Neighborhood Flattening**: Distribute Map implementation to process entitlements per neighborhood
  - **Database Integration**: Bulk blog topic creation with CosmoClient and GraphQL mutations
  - **Comprehensive Testing**: 138 test cases covering all handlers and error scenarios

  ### Features

  - `flatten-per-neighborhood`: Flattens enriched entitlements by neighborhood
  - `generate-blog-topics-background`: Initiates background topic generation jobs
  - `poll-blog-topics-status`: Polls job completion and parses results
  - `save-generated-topics`: Persists generated topics to database
  - Langfuse/LangChain integration with structured output support

  ### Technical

  - AWS Step Functions with DISTRIBUTED mode and EXPRESS execution
  - Batch processing (50 items per batch) for database operations
  - Proper error handling and retry policies throughout
  - Context logging and monitoring integration

## 0.2.4

### Patch Changes

- [#733](https://github.com/luxurypresence/seo-automation/pull/733) [`568c737`](https://github.com/luxurypresence/seo-automation/commit/568c7370222d6aa13e0e3db1bfa5664d91fd178f) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify Cosmo calls to split m2mSuperApiKey

## 0.2.3

### Patch Changes

- [#734](https://github.com/luxurypresence/seo-automation/pull/734) [`ba8a545`](https://github.com/luxurypresence/seo-automation/commit/ba8a54554e4e5b52d3cc3f6ad2a427735f9c9032) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix stepFN definition for blog-topic-generator

## 0.2.2

### Patch Changes

- [#732](https://github.com/luxurypresence/seo-automation/pull/732) [`a1f3975`](https://github.com/luxurypresence/seo-automation/commit/a1f3975beccaba65bb10997e3e27a8df52705185) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix for tag value in serverless

## 0.2.1

### Patch Changes

- [#730](https://github.com/luxurypresence/seo-automation/pull/730) [`4e4ebda`](https://github.com/luxurypresence/seo-automation/commit/4e4ebdaf099b16f77fa8db1b45647a54894fbdfb) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds DD_API_KEY env var

## 0.2.0

### Minor Changes

- [#726](https://github.com/luxurypresence/seo-automation/pull/726) [`2f32dd6`](https://github.com/luxurypresence/seo-automation/commit/2f32dd64911909d3785a5b79dbe4430d24cc2a3e) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds build config for blog-topic-generator

## 0.1.3

### Patch Changes

- [#696](https://github.com/luxurypresence/seo-automation/pull/696) [`67bc553`](https://github.com/luxurypresence/seo-automation/commit/67bc553c3ff645a6e3c50bedd38a6238fb51777b) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-846: Adds getKeywordMap lambda

  ### Added

  - **get-keyword-map Lambda**: New Lambda function that enriches `EnrichedEntitlement` objects with keyword maps fetched from S3. The function processes entitlements in parallel using `pLimit` for optimal performance, fetches JSON files from `lp-bloom-scheduler-execution-${environment}/keywordmap/` bucket, and gracefully handles missing files by logging and omitting them from results. Includes comprehensive error handling for S3 failures, empty files, and JSON parsing errors.

  ### Changed

  - **Step Functions Integration**: Updated `blogTopicGenerator` state machine to include `get-keyword-map` step after `GetBrandProfile` for enhanced entitlement processing pipeline.

  ### Technical Details

  - Thread-safe parallel processing with controlled concurrency (10 concurrent S3 calls)
  - S3 client instantiation per invocation with proper region configuration
  - Structured logging with detailed error context and processing metrics
  - Comprehensive unit test coverage including parallel processing scenarios

## 0.1.2

### Patch Changes

- [#694](https://github.com/luxurypresence/seo-automation/pull/694) [`7885b18`](https://github.com/luxurypresence/seo-automation/commit/7885b181ba2e3ce92fc4b9fb287336beb0a9926f) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-839: Adds getBrandProfile lambda

  ## Changelog

  ### Added

  - **New Lambda Function**: `get-brand-profile` - Fetches brand profiles for enriched entitlements using CosmoClient
  - **Enhanced Type System**: Extended `EnrichedEntitlement` interface with optional `brandProfile` and `keywordMap` fields
  - **Comprehensive Test Suite**: 10 unit tests covering success scenarios, error handling, null profiles, and concurrency
  - **Concurrency Control**: Parallel processing with `p-limit` to respect API rate limits
  - **Structured Logging**: Context-aware logging with component identification and detailed metrics

  ### Changed

  - **Type Architecture**: Simplified from inheritance chain to single interface with optional enrichment fields
  - **Error Handling**: Graceful degradation - filters out entitlements without brand profiles or failed requests
  - **Logging**: Enhanced with company names and profile IDs for better traceability

  ### Technical Details

  - **Input**: `EnrichedEntitlement[]`
  - **Output**: `EnrichedEntitlement[]` (only with valid brand profiles)
  - **Dependencies**: CosmoClient, p-limit, ContextLogger
  - **Performance**: Concurrent processing with configurable limits
  - **Observability**: Complete request/response tracking with filtering metrics

  _Resolves: Brand profile enrichment for blog topic generation pipeline_

## 0.1.1

### Patch Changes

- [#680](https://github.com/luxurypresence/seo-automation/pull/680) [`daabe30`](https://github.com/luxurypresence/seo-automation/commit/daabe30c6239a9bb23f3cb499bb6c1f235d28e8a) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-838: Adds EntitlementEnrichment lambda

  ### Changelog

  ### Added

  - **Enrich-Entitlements Lambda**: New AWS Lambda function that fetches neighborhoods via API Gateway and filters entitlements based on Google Place Data availability
  - **Circuit-breaking logic**: Automatically filters out entitlements unsuitable for topic generation (no valid neighborhoods)
  - **Data optimization**: Reduces GooglePlaceData payload size by selecting only essential fields (`formattedAddress`, `addressComponents`)
  - **Comprehensive error handling**: Individual entitlement failures don't affect processing of other entitlements
  - **Detailed logging**: Tracks input/output counts, filtering statistics, and processing metrics

  ### Enhanced

  - **ApiGatewayClient**: Added comprehensive test suite (14 tests) covering pagination, error handling, and GraphQL query validation
  - **Enrich-Entitlements Lambda**: Added robust test suite (11 tests) covering all scenarios including edge cases and error conditions

  ### Technical

  - **Type safety**: Full TypeScript implementation with proper interfaces (`EntitlementDTO`, `EnrichedEntitlement`, `Neighborhood`)
  - **Performance**: Parallel processing with `Promise.all()` and early filtering to optimize downstream processing
  - **Reliability**: Graceful handling of API failures with detailed error logging and context preservation

  **Total test coverage**: 50 tests across 5 test suites, all passing ✅

## 0.1.0

### Minor Changes

- [#669](https://github.com/luxurypresence/seo-automation/pull/669) [`03672e7`](https://github.com/luxurypresence/seo-automation/commit/03672e7171eb3968e100de3a74000d1aeb6b8a20) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-833: Adds EntitlementFetcher for Topic generator

  # Changelog

  ### Added

  - **Fetch Entitlements Lambda**: New serverless function to retrieve and validate entitlements by product
  - **Date-based validation**: Validates entitlements against `start_date` and `end_date` fields
  - **S3 integration**: Stores valid entitlements in environment-specific S3 buckets with timestamp-based keys
  - **Chunking support**: Processes entitlements in configurable chunks for memory efficiency
  - **Comprehensive logging**: Structured logging with context for observability and debugging

  ### Changed

  - **EntitlementValidationService**: Enhanced with comprehensive date validation logic
  - **EntitlementService**: Updated to use new validation methods in standard workflow
  - **Test coverage**: Added 11 comprehensive unit tests with mocking and error scenarios

  ### Technical Details

  - Uses `TenantClient` for data fetching and `EntitlementService` for business logic
  - Implements AWS Lambda best practices with proper error handling and callback validation
  - Supports environment-based configuration with fallback to default product ID
  - Returns structured response with success status, counts, and S3 location metadata

## 0.0.2

### Patch Changes

- [#661](https://github.com/luxurypresence/seo-automation/pull/661) [`3730af7`](https://github.com/luxurypresence/seo-automation/commit/3730af727ef4942ab58c61c1d430ecba15ff21fd) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-754: Adds blog-topic-generator package
