import { GraphQLClient } from 'graphql-request';
import { ContextLogger, getGWAuthToken } from 'src/utils';

import { Neighborhood } from './types/neighborhood';
import { ApiError } from '../errors/ApiError';

export class ApiGatewayClient {
  private readonly token: string;

  constructor(
    private readonly apiGatewaySuperUser: string,
    private readonly apiGatewayKey: string,
    private readonly graphqlClient: GraphQLClient,
    private readonly logger: ContextLogger,
  ) {
    this.token = getGWAuthToken(this.apiGatewaySuperUser, this.apiGatewayKey);
  }

  async getNeighborhoods(
    companyId: string,
    limit: number,
    neighborhoodIds: string[],
  ): Promise<Neighborhood[]> {
    try {
      this.logger.info('Fetching neighborhoods', {
        companyId,
        limit,
        neighborhoodIds,
      });
      let offset = 0;
      let hasMore = true;
      const neighborhoods: Neighborhood[] = [];
      const neighborhoodsQuery = `
        query neighborhoods($companyId: String, $limit: Int, $offset: Int, $neighborhoodIds: [ID!]) {
          neighborhoods(companyId: $companyId, limit: $limit, offset: $offset, neighborhoodIds: $neighborhoodIds) {
            id
            name
            companyId
            googlePlaceData
          }
        }
      `;

      while (hasMore) {
        const neighborhoodsData = await this.graphqlClient.request<{
          neighborhoods: Neighborhood[];
        }>(
          neighborhoodsQuery,
          { companyId, limit, offset, neighborhoodIds },
          { Authorization: this.token },
        );
        neighborhoods.push(...neighborhoodsData.neighborhoods);
        offset += limit;
        hasMore = neighborhoodsData.neighborhoods.length === limit;
      }
      return neighborhoods;
    } catch (error) {
      const status: number = error?.response?.status || 500;
      this.logger.error('Failed to fetch company neighborhoods', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      throw new ApiError(
        `Failed to fetch company neighborhoods status: ${status}`,
        status,
        {
          companyId,
        },
      );
    }
  }
}
