import { ApiError } from '../../errors/ApiError';
import { ContextLogger } from '../../utils/contextLogger';
import { TenantClient } from '../TenantClient';

describe('TenantClient', () => {
  let client: TenantClient;
  let mockLogger: { info: jest.Mock; error: jest.Mock };
  const originalFetch = global.fetch;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    client = new TenantClient(
      'baseUrl',
      1,
      mockLogger as unknown as ContextLogger,
      'test-product',
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
    global.fetch = originalFetch;
  });

  describe('getAllEntitlements', () => {
    it('fetches and aggregates multiple pages of entitlements', async () => {
      const page1 = {
        data: [{ companyId: 'test1', units: 1 }],
        links: { next: 'page2url' },
      };
      const page2 = {
        data: [{ companyId: 'test2', units: 2 }],
        links: { next: 'page3url' },
      };
      const page3 = {
        data: [{ companyId: 'test3', units: 3 }],
        links: { next: null },
      };

      global.fetch = jest
        .fn()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(page1),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(page2),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(page3),
        });

      const result = await client.getAllEntitlements();
      expect(result).toHaveLength(3);
      expect(result).toEqual([...page1.data, ...page2.data, ...page3.data]);
      expect(global.fetch).toHaveBeenCalledTimes(3);
    });

    it('throws TenantServiceError when fetch fails', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 500,
      });

      await expect(client.getAllEntitlements()).rejects.toBeInstanceOf(
        ApiError,
      );
    });
  });
});
