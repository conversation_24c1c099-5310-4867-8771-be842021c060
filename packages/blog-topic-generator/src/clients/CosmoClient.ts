import { GraphQLClient } from 'graphql-request';
import { ApiError } from 'src/errors/ApiError';

import { ContextLogger } from '../utils';
import { BlogTopicInput } from './types/blogTopic';
import { BrandProfile } from './types/brandProfile';

export class CosmoClient {
  private readonly graphqlClient: GraphQLClient;

  constructor(
    cosmoGqlUrl: string,
    m2mSuperApiKey: string,
    private readonly logger: ContextLogger,
  ) {
    // Log initialization details for debugging
    this.logger.info('Initializing CosmoClient', {
      cosmoGqlUrl,
      hasApiKey: !!m2mSuperApiKey,
      apiKeyLength: m2mSuperApiKey?.length || 0,
    });

    this.graphqlClient = new GraphQLClient(cosmoGqlUrl, {
      headers: {
        'x-lp-api-key': m2mSuperApiKey.split(',')[0],
      },
    });
  }

  async getBrandProfile(companyId: string): Promise<BrandProfile | null> {
    try {
      const query = `
        query GetBrandProfile($companyId: ID!) {
          brandProfile(companyId: $companyId) {
            id
            companyId
            createdAt
            updatedAt
            updatedBy
            aboutTheBrand
            strategicFocus
            valueProposition
            idealCustomerProfiles
            missionAndCoreValues
            brandPointOfView
            toneOfVoice
            ctaText
            authorPersona
          }
        }
      `;

      const data = await this.graphqlClient.request<{
        brandProfile: BrandProfile | null;
      }>(query, { companyId });

      // Handle null response from GraphQL (profile doesn't exist)
      if (data.brandProfile === null) {
        this.logger.info('Brand profile query returned null', { companyId });
        return null;
      }

      this.logger.info('Brand profile fetched successfully', {
        companyId,
        profileId: data.brandProfile.id,
      });

      return data.brandProfile;
    } catch (error) {
      this.logger.error('Error fetching brand profile', { error });
      throw error;
    }
  }

  async createBlogTopicsBulk(
    blogTopics: Array<BlogTopicInput>,
  ): Promise<{ createdBlogTopics: Array<{ id: string }> }> {
    try {
      if (!blogTopics || blogTopics.length === 0) {
        return { createdBlogTopics: [] };
      }

      // Server enforces @ArrayMaxSize(50). Chunk requests accordingly.
      const MAX_BATCH_SIZE = 50;
      const createdBlogTopics: Array<{ id: string }> = [];

      const mutation = `
        mutation CreateBlogTopicsBulk($input: CreateBlogTopicsBulkInput!) {
          createBlogTopicsBulk(input: $input) {
            createdBlogTopics { id }
          }
        }
      `;

      const chunk = <T>(arr: T[], size: number): T[][] => {
        const chunks: T[][] = [];
        for (let i = 0; i < arr.length; i += size) {
          chunks.push(arr.slice(i, i + size));
        }
        return chunks;
      };

      const batches = chunk(blogTopics, MAX_BATCH_SIZE);
      this.logger.info('Creating blog topics in batches', {
        total: blogTopics.length,
        batches: batches.length,
        maxBatchSize: MAX_BATCH_SIZE,
      });

      for (const batch of batches) {
        const variables = { input: { blogTopics: batch } };
        const data = await this.graphqlClient.request<{
          createBlogTopicsBulk: { createdBlogTopics: Array<{ id: string }> };
        }>(mutation, variables);
        createdBlogTopics.push(...data.createBlogTopicsBulk.createdBlogTopics);
      }

      return { createdBlogTopics };
    } catch (error) {
      const status: number = error?.response?.status || 500;
      const message = error instanceof Error ? error.message : String(error);
      const graphQLErrors =
        error?.response?.errors?.map((e: any) => e.message) ?? undefined;
      this.logger.error('Failed to create blog topics bulk', {
        error: message,
        graphQLErrors,
        topicsCount: blogTopics?.length ?? 0,
      });
      // Preserve context without dumping payload
      throw new ApiError(
        message || `Failed to create blog topics in bulk`,
        status === 200 && graphQLErrors ? 502 : status,
        { topicsCount: blogTopics?.length ?? 0 },
      );
    }
  }
}
