/* eslint-disable @typescript-eslint/no-require-imports */
import { GraphQLClient } from 'graphql-request';

import { ApiError } from '../../errors/ApiError';
import { ApiGatewayClient } from '../ApiGatewayClient';
import { Neighborhood } from '../types/neighborhood';

// Mock GraphQL client
jest.mock('graphql-request');
const mockGraphQLClient = GraphQLClient as jest.MockedClass<
  typeof GraphQLClient
>;

// Mock utils
jest.mock('src/utils', () => ({
  getGWAuthToken: jest.fn(() => 'mock-jwt-token'),
  ContextLogger: jest.fn(),
}));

describe('ApiGatewayClient', () => {
  let apiGatewayClient: ApiGatewayClient;
  let mockGraphQLRequest: jest.Mock;
  let mockLogger: any;
  let mockGetGWAuthToken: jest.Mock;

  const mockNeighborhoods: Neighborhood[] = [
    {
      id: 'neigh-1',
      name: 'Downtown',
      companyId: 'company-1',
      googlePlaceData: {
        formatted_address: '123 Main St, Downtown, CA 90210',
        address_components: [
          { long_name: '123', short_name: '123', types: ['street_number'] },
          { long_name: 'Main St', short_name: 'Main St', types: ['route'] },
        ],
        place_id: 'ChIJ123456789',
        geometry: { location: { lat: 34.0522, lng: -118.2437 } },
      },
    },
    {
      id: 'neigh-2',
      name: 'Uptown',
      companyId: 'company-1',
      googlePlaceData: {
        formatted_address: '456 Oak Ave, Uptown, CA 90211',
        address_components: [
          { long_name: '456', short_name: '456', types: ['street_number'] },
          { long_name: 'Oak Ave', short_name: 'Oak Ave', types: ['route'] },
        ],
        place_id: 'ChIJ987654321',
        geometry: { location: { lat: 34.0622, lng: -118.2537 } },
      },
    },
    {
      id: 'neigh-3',
      name: 'Suburb',
      companyId: 'company-1',
      googlePlaceData: null,
    },
  ];

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
    };

    mockGraphQLRequest = jest.fn();
    mockGraphQLClient.prototype.request = mockGraphQLRequest;

    // Get the mocked function
    const { getGWAuthToken } = require('src/utils');
    mockGetGWAuthToken = getGWAuthToken as jest.Mock;

    // Reset all mocks
    jest.clearAllMocks();

    apiGatewayClient = new ApiGatewayClient(
      'super-user-id',
      'test-api-key',
      new GraphQLClient('https://test-api.com'),
      mockLogger,
    );
  });

  describe('constructor', () => {
    it('should initialize with correct parameters', () => {
      expect(mockGetGWAuthToken).toHaveBeenCalledWith(
        'super-user-id',
        'test-api-key',
      );
      expect(apiGatewayClient).toBeInstanceOf(ApiGatewayClient);
    });
  });

  describe('getNeighborhoods', () => {
    it('should fetch neighborhoods successfully', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(0, 2),
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        100,
        [],
      );

      expect(result).toEqual(mockNeighborhoods.slice(0, 2));
      expect(mockGraphQLRequest).toHaveBeenCalledTimes(1);
      expect(mockGraphQLRequest).toHaveBeenCalledWith(
        expect.stringContaining('query neighborhoods'),
        {
          companyId: 'company-1',
          limit: 100,
          offset: 0,
          neighborhoodIds: [],
        },
        { Authorization: 'mock-jwt-token' },
      );
      expect(mockLogger.info).toHaveBeenCalledWith('Fetching neighborhoods', {
        companyId: 'company-1',
        limit: 100,
        neighborhoodIds: [],
      });
    });

    it('should handle pagination correctly', async () => {
      // First page with limit reached (indicating more data)
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(0, 2),
      });
      // Second page with remaining data
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(2, 3),
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        2,
        [],
      );

      expect(result).toEqual(mockNeighborhoods);
      expect(mockGraphQLRequest).toHaveBeenCalledTimes(2);

      // First call
      expect(mockGraphQLRequest).toHaveBeenNthCalledWith(
        1,
        expect.stringContaining('query neighborhoods'),
        {
          companyId: 'company-1',
          limit: 2,
          offset: 0,
          neighborhoodIds: [],
        },
        { Authorization: 'mock-jwt-token' },
      );

      // Second call
      expect(mockGraphQLRequest).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('query neighborhoods'),
        {
          companyId: 'company-1',
          limit: 2,
          offset: 2,
          neighborhoodIds: [],
        },
        { Authorization: 'mock-jwt-token' },
      );
    });

    it('should stop pagination when response is less than limit', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(0, 1), // Less than limit
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        100,
        [],
      );

      expect(result).toEqual(mockNeighborhoods.slice(0, 1));
      expect(mockGraphQLRequest).toHaveBeenCalledTimes(1);
    });

    it('should handle empty neighborhoods response', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: [],
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        100,
        [],
      );

      expect(result).toEqual([]);
      expect(mockGraphQLRequest).toHaveBeenCalledTimes(1);
    });

    it('should pass neighborhoodIds parameter correctly', async () => {
      const neighborhoodIds = ['neigh-1', 'neigh-2'];
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(0, 2),
      });

      await apiGatewayClient.getNeighborhoods(
        'company-1',
        100,
        neighborhoodIds,
      );

      expect(mockGraphQLRequest).toHaveBeenCalledWith(
        expect.stringContaining('query neighborhoods'),
        {
          companyId: 'company-1',
          limit: 100,
          offset: 0,
          neighborhoodIds: ['neigh-1', 'neigh-2'],
        },
        { Authorization: 'mock-jwt-token' },
      );
    });

    it('should handle GraphQL errors with response status', async () => {
      const graphQLError = new Error('GraphQL Error');
      (graphQLError as any).response = { status: 404 };
      mockGraphQLRequest.mockRejectedValueOnce(graphQLError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow(ApiError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow('Failed to fetch company neighborhoods status: 500');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch company neighborhoods',
        {
          error: 'GraphQL Error',
          companyId: 'company-1',
        },
      );
    });

    it('should handle GraphQL errors without response status', async () => {
      const graphQLError = new Error('Network Error');
      mockGraphQLRequest.mockRejectedValueOnce(graphQLError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow(ApiError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow('Failed to fetch company neighborhoods status: 500');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch company neighborhoods',
        {
          error: 'Network Error',
          companyId: 'company-1',
        },
      );
    });

    it('should handle non-Error objects', async () => {
      mockGraphQLRequest.mockRejectedValueOnce('String error');

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow(ApiError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow('Failed to fetch company neighborhoods status: 500');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch company neighborhoods',
        {
          error: 'String error',
          companyId: 'company-1',
        },
      );
    });

    it('should handle errors with undefined response', async () => {
      const graphQLError = new Error('GraphQL Error');
      (graphQLError as any).response = undefined;
      mockGraphQLRequest.mockRejectedValueOnce(graphQLError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow(ApiError);

      await expect(
        apiGatewayClient.getNeighborhoods('company-1', 100, []),
      ).rejects.toThrow('Failed to fetch company neighborhoods status: 500');
    });

    it('should include companyId in ApiError context', async () => {
      const graphQLError = new Error('GraphQL Error');
      (graphQLError as any).response = { status: 404 };
      mockGraphQLRequest.mockRejectedValueOnce(graphQLError);

      try {
        await apiGatewayClient.getNeighborhoods('company-1', 100, []);
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError);
        const apiError = error as ApiError;
        expect(apiError.message).toContain(
          'Failed to fetch company neighborhoods status: 404',
        );
        expect(apiError.statusCode).toBe(404);
        // Check if context exists and contains companyId
        if ('context' in apiError) {
          expect(apiError.context).toEqual({ companyId: 'company-1' });
        }
      }
    });

    it('should handle complex pagination scenario', async () => {
      // Create more mock data for complex pagination
      const extendedNeighborhoods = [
        ...mockNeighborhoods,
        {
          id: 'neigh-4',
          name: 'Westside',
          companyId: 'company-1',
          googlePlaceData: {
            formatted_address: '789 Pine St, Westside, CA 90212',
            address_components: [],
          },
        },
        {
          id: 'neigh-5',
          name: 'Eastside',
          companyId: 'company-1',
          googlePlaceData: {
            formatted_address: '321 Elm St, Eastside, CA 90213',
            address_components: [],
          },
        },
      ];

      // First page: exactly at limit (more data expected)
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: extendedNeighborhoods.slice(0, 2),
      });
      // Second page: exactly at limit (more data expected)
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: extendedNeighborhoods.slice(2, 4),
      });
      // Third page: less than limit (end of data)
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: extendedNeighborhoods.slice(4, 5),
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        2,
        [],
      );

      expect(result).toEqual(extendedNeighborhoods.slice(0, 5));
      expect(mockGraphQLRequest).toHaveBeenCalledTimes(3);

      // Verify offset progression
      expect(mockGraphQLRequest).toHaveBeenNthCalledWith(
        1,
        expect.any(String),
        expect.objectContaining({ offset: 0 }),
        expect.any(Object),
      );
      expect(mockGraphQLRequest).toHaveBeenNthCalledWith(
        2,
        expect.any(String),
        expect.objectContaining({ offset: 2 }),
        expect.any(Object),
      );
      expect(mockGraphQLRequest).toHaveBeenNthCalledWith(
        3,
        expect.any(String),
        expect.objectContaining({ offset: 4 }),
        expect.any(Object),
      );
    });

    it('should handle neighborhoods with null googlePlaceData', async () => {
      const neighborhoodsWithNullData = [
        {
          id: 'neigh-1',
          name: 'Downtown',
          companyId: 'company-1',
          googlePlaceData: null,
        },
        {
          id: 'neigh-2',
          name: 'Uptown',
          companyId: 'company-1',
          googlePlaceData: {
            formatted_address: '456 Oak Ave, Uptown, CA 90211',
            address_components: [],
          },
        },
      ];

      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: neighborhoodsWithNullData,
      });

      const result = await apiGatewayClient.getNeighborhoods(
        'company-1',
        100,
        [],
      );

      expect(result).toEqual(neighborhoodsWithNullData);
      expect(result[0].googlePlaceData).toBeNull();
      expect(result[1].googlePlaceData).toEqual({
        formatted_address: '456 Oak Ave, Uptown, CA 90211',
        address_components: [],
      });
    });
  });

  describe('GraphQL query structure', () => {
    it('should use correct GraphQL query structure', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods.slice(0, 1),
      });

      await apiGatewayClient.getNeighborhoods('company-1', 100, []);

      const queryCall = mockGraphQLRequest.mock.calls[0][0];
      expect(queryCall).toContain('query neighborhoods');
      expect(queryCall).toContain('$companyId: String');
      expect(queryCall).toContain('$limit: Int');
      expect(queryCall).toContain('$offset: Int');
      expect(queryCall).toContain('$neighborhoodIds: [ID!]');
      expect(queryCall).toContain('neighborhoods(companyId: $companyId');
      expect(queryCall).toContain('limit: $limit');
      expect(queryCall).toContain('offset: $offset');
      expect(queryCall).toContain('neighborhoodIds: $neighborhoodIds)');
      expect(queryCall).toContain('id');
      expect(queryCall).toContain('name');
      expect(queryCall).toContain('companyId');
      expect(queryCall).toContain('googlePlaceData');
    });
  });
});
