import { GetBrandProfileInput } from 'src/lambdas/get-brand-profile';

export const event: GetBrandProfileInput = {
  enrichedWithNeighborhoods: {
    displayId: 'c3048718-4ea5-4da4-8a78-4d18effa87a6',
    id: 'c3048718-4ea5-4da4-8a78-4d18effa87a6',
    companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
    productId: '5c9b746c-7327-42ce-b998-ce707e7cee44',
    startDate: '2025-06-25T00:00:00.000Z',
    endDate: null,
    units: 2,
    salesforceServiceId: null,
    createdAt: '2025-06-26T17:37:38.251Z',
    updatedAt: '2025-06-26T17:37:38.251Z',
    product: {
      id: '5c9b746c-7327-42ce-b998-ce707e7cee44',
      name: 'AI Blog Specialist',
    },
    company: {
      name: "<PERSON>'s Beach Homes",
      website: 'ryansbeach.homes',
    },
    entitled: true,
    neighborhoods: [
      {
        id: '2ef64521-902b-497f-b311-13eeb9e6ad63',
        name: 'Corona del Mar',
        companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
        googlePlaceData: {
          formattedAddress: 'Corona Del Mar, Newport Beach, CA, USA',
          addressComponents: [
            {
              types: ['neighborhood', 'political'],
              long_name: 'Corona Del Mar',
              short_name: 'Corona Del Mar',
            },
            {
              types: ['locality', 'political'],
              long_name: 'Newport Beach',
              short_name: 'Newport Beach',
            },
            {
              types: ['administrative_area_level_2', 'political'],
              long_name: 'Orange County',
              short_name: 'Orange County',
            },
            {
              types: ['administrative_area_level_1', 'political'],
              long_name: 'California',
              short_name: 'CA',
            },
            {
              types: ['country', 'political'],
              long_name: 'United States',
              short_name: 'US',
            },
          ],
        },
      },
    ],
  },
  executionName: 'test-execution-12345',
  executionStartTime: '2025-09-17T14:00:00.000Z',
  dryRun: true,
};
