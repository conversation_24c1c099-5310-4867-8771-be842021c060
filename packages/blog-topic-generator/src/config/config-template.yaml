base:
  ENVIRONMENT: ${<PERSON><PERSON><PERSON>RONMENT}
  REGION: us-east-1
  API_GATEWAY_SUPER_USER_COMPANY_ID: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_SUPER_USER_COMPANY_ID
  API_GATEWAY_KEY: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_KEY
  LAUNCHDARKLY_KEY: vault:secret/data/${VAULT_ENV}/standard#LAUNCHDARKLY_KEY
  AIR_OPS_API_URL: https://app.airops.com/public_api/airops_apps
  AIR_OPS_API_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#AIR_OPS_API_KEY
  TENANT_SERVICE_URL: https://tenant-service.${BASE_DOMAIN}
  CMS_SERVICE_URL: https://cms-service.${BASE_DOMAIN}
  API_GATEWAY_URL: https://gw.${BASE_DOMAIN}
  COSMO_GQL_URL: https://graphql.${BASE_DOMAIN}/graphql
  AI_BLOG_SPECIALIST_PRODUCT_ID: 5c9b746c-7327-42ce-b998-ce707e7cee44
  M2M_SUPER_API_KEY: vault:secret/data/${VAULT_ENV}/standard#DOMAIN_M2M_KEY
  ANTHROPIC_API_KEY: vault:secret/data/${VAULT_ENV}/standard#ANTHROPIC_API_KEY
  LANGFUSE_SECRET_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_SECRET_KEY
  LANGFUSE_PUBLIC_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_PUBLIC_KEY
  LANGFUSE_FLUSH_AT: 1
  LANGFUSE_BASE_URL: https://us.cloud.langfuse.com
  API_GATEWAY_CONCURRENCY_LIMIT: 10
  OPENAI_API_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#OPENAI_API_KEY
  OPENAI_MODEL_NAME: gpt-5
  OPENAI_TEMPERATURE: 0.2
  OPENAI_REASONING: medium
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
  SECURITY_GROUP_IDS: ${SECURITY_GROUP_IDS}
  SUBNET_IDS: ${SUBNET_IDS}
staging:
  LOG_LEVEL: INFO
  POWERTOOLS_DEV: true
  ENTITLEMENTS_LIMIT: 3
  LANGFUSE_ENVIRONMENT: staging
  OPENAI_REASONING: medium
  OPENAI_VERBOSITY: medium
production:
  LOG_LEVEL: ERROR
  POWERTOOLS_DEV: false
  ENTITLEMENTS_LIMIT: 100
  ENTITLEMENTS_CHUNK_SIZE: 100
  LANGFUSE_ENVIRONMENT: production
  OPENAI_REASONING: high
  OPENAI_VERBOSITY: medium