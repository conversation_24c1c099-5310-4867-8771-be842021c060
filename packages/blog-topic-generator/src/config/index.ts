export interface EnvironmentConfig {
  AI_BLOG_SPECIALIST_PRODUCT_ID: string;
  TENANT_SERVICE_URL: string;
  CMS_SERVICE_URL: string;
  API_GATEWAY_URL: string;
  AIR_OPS_API_URL: string;
  AIR_OPS_API_KEY: string;
  API_GATEWAY_SUPER_USER_COMPANY_ID: string;
  API_GATEWAY_KEY: string;
  ENTITLEMENTS_CHUNK_SIZE?: number;
  POWERTOOLS_DEV: boolean;
  LOG_LEVEL: string;
  REGION: string;
  ENTITLEMENTS_LIMIT: number | undefined;
  ENVIRONMENT: string;
  LAUNCHDARKLY_KEY: string;
  API_GATEWAY_CONCURRENCY_LIMIT: number;
  ANTHROPIC_API_KEY: string;
  COSMO_GQL_URL: string;
  M2M_SUPER_API_KEY: string;
  LANGFUSE_SECRET_KEY: string;
  LANGFUSE_PUBLIC_KEY: string;
  LANGFUSE_BASE_URL: string;
  LANGFUSE_ENVIRONMENT: string;
  LANGFUSE_FLUSH_AT: number;
  OPENAI_API_KEY: string;
  OPENAI_MODEL_NAME?: string;
  OPENAI_TEMPERATURE?: number;
  OPENAI_REASONING?: string;
  OPENAI_VERBOSITY?: string;
}

let cachedConfig: EnvironmentConfig | null = null;

function parseNumberEnv(
  name: string,
  defaultValue?: number,
): number | undefined {
  const raw = process.env[name];
  if (!raw) return defaultValue;
  const n = Number(raw);
  if (Number.isNaN(n)) {
    throw new Error(`Invalid numeric value for ${name}: "${raw}"`);
  }
  return n;
}

export function getConfig(): EnvironmentConfig {
  if (!cachedConfig) {
    cachedConfig = getEnvironmentConfig();
  }
  return cachedConfig;
}

function getEnvironmentConfig(): EnvironmentConfig {
  const requiredEnvVars = {
    aiBlogSpecialistProductId: process.env.AI_BLOG_SPECIALIST_PRODUCT_ID,
    tenantServiceUrl: process.env.TENANT_SERVICE_URL,
    cmsServiceUrl: process.env.CMS_SERVICE_URL,
    apiGatewayUrl: process.env.API_GATEWAY_URL,
    apiGatewayKey: process.env.API_GATEWAY_KEY,
    airOpsApiUrl: process.env.AIR_OPS_API_URL,
    airOpsApiKey: process.env.AIR_OPS_API_KEY,
    anthropicApiKey: process.env.ANTHROPIC_API_KEY,
    cosmoGqlUrl: process.env.COSMO_GQL_URL,
    m2mSuperApiKey: process.env.M2M_SUPER_API_KEY,
    langfuseSecretKey: process.env.LANGFUSE_SECRET_KEY,
    langfusePublicKey: process.env.LANGFUSE_PUBLIC_KEY,
    langfuseBaseUrl: process.env.LANGFUSE_BASE_URL,
    langfuseEnvironment: process.env.LANGFUSE_ENVIRONMENT || 'staging',
    langfuseFlushAt: parseNumberEnv('LANGFUSE_FLUSH_AT', 1),
    openaiApiKey: process.env.OPENAI_API_KEY,
  };

  // Validate required env vars
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    if (value === undefined || value === null || value === '') {
      throw new Error(`Missing required environment variable: ${key}`);
    }
  });

  return {
    // Required env vars
    AI_BLOG_SPECIALIST_PRODUCT_ID: requiredEnvVars.aiBlogSpecialistProductId!,
    TENANT_SERVICE_URL: requiredEnvVars.tenantServiceUrl!,
    CMS_SERVICE_URL: requiredEnvVars.cmsServiceUrl!,
    API_GATEWAY_URL: requiredEnvVars.apiGatewayUrl!,
    API_GATEWAY_KEY: requiredEnvVars.apiGatewayKey!,
    AIR_OPS_API_URL: requiredEnvVars.airOpsApiUrl!,
    AIR_OPS_API_KEY: requiredEnvVars.airOpsApiKey!,
    ANTHROPIC_API_KEY: requiredEnvVars.anthropicApiKey!,
    COSMO_GQL_URL: requiredEnvVars.cosmoGqlUrl!,
    M2M_SUPER_API_KEY: requiredEnvVars.m2mSuperApiKey!,
    LANGFUSE_SECRET_KEY: requiredEnvVars.langfuseSecretKey!,
    LANGFUSE_PUBLIC_KEY: requiredEnvVars.langfusePublicKey!,
    LANGFUSE_BASE_URL: requiredEnvVars.langfuseBaseUrl!,
    LANGFUSE_ENVIRONMENT: requiredEnvVars.langfuseEnvironment,
    LANGFUSE_FLUSH_AT: requiredEnvVars.langfuseFlushAt!,
    OPENAI_API_KEY: requiredEnvVars.openaiApiKey!,
    // Non-required env vars
    OPENAI_MODEL_NAME: process.env.OPENAI_MODEL_NAME,
    OPENAI_TEMPERATURE: parseNumberEnv('OPENAI_TEMPERATURE', 0),
    OPENAI_REASONING: process.env.OPENAI_REASONING || 'low',
    OPENAI_VERBOSITY: process.env.OPENAI_VERBOSITY || 'medium',
    API_GATEWAY_SUPER_USER_COMPANY_ID:
      process.env.API_GATEWAY_SUPER_USER_COMPANY_ID || '',
    ENTITLEMENTS_CHUNK_SIZE: parseNumberEnv('ENTITLEMENTS_CHUNK_SIZE', 10),
    POWERTOOLS_DEV: process.env.POWERTOOLS_DEV === 'true',
    LOG_LEVEL: process.env.LOG_LEVEL || 'ERROR',
    REGION: process.env.REGION || 'us-east-1',
    ENTITLEMENTS_LIMIT: parseNumberEnv('ENTITLEMENTS_LIMIT', undefined),
    ENVIRONMENT: process.env.ENVIRONMENT || 'development',
    LAUNCHDARKLY_KEY: process.env.LAUNCHDARKLY_KEY || '',
    API_GATEWAY_CONCURRENCY_LIMIT: parseNumberEnv(
      'API_GATEWAY_CONCURRENCY_LIMIT',
      10,
    )!,
  };
}
