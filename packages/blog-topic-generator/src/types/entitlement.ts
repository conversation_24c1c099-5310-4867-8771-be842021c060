import { BrandProfile } from 'src/clients/types/brandProfile';
import { Neighborhood } from 'src/clients/types/neighborhood';

// Entitlement object returned from the tenant-service
export interface EntitlementDTO {
  id: string;
  displayId: string;
  createdAt: string;
  updatedAt: string;
  productId: string;
  companyId: string;
  startDate: string | null;
  endDate: string | null;
  units: number;
  salesforceServiceId: string | null;
  product: {
    id: string;
    name: string;
  };
  company?: {
    website?: string;
    name?: string;
  };
  entitled: boolean;
}

export interface EnrichedEntitlement extends EntitlementDTO {
  neighborhoods: Neighborhood[];
  brandProfile?: BrandProfile | null;
  keywordMap?: Record<string, string> | null;
}

export interface FlattenedEntitlement {
  companyId: string;
  brandProfile: BrandProfile | null;
  keywordMap: Record<string, string> | null;
  neighborhood: Neighborhood;
}
