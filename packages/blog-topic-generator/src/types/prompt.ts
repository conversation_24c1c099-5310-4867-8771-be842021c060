export type PromptResult<T = string> = {
  content: T;
  prompt: string;
  prompt_id: string;
  prompt_name: string;
};

export type GeneratedTopic = {
  topic: string;
  blogTitle?: string;
  neighborhoodId?: string;
  companyId: string;
  rationale?: string;
  parentTopic?: string;
  airopsExecutionId?: string;
  type: BlogTopicType;
};

export enum BlogTopicType {
  ARTICLE = 'article',
  LISTICLE = 'listicle',
}

export type GeneratedTopicsByNeighborhood = {
  neighborhoodId: string;
  topics: GeneratedTopic[];
};

export enum BackgroundJobStatus {
  VALIDATING = 'validating',
  QUEUED = 'queued',
  IN_PROGRESS = 'in_progress',
  FINALIZING = 'finalizing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  EXPIRED = 'expired',
  CANCELLING = 'cancelling',
  CANCELLED = 'cancelled',
}
