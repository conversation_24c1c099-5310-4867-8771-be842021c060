import { TenantClient } from '../../clients/TenantClient';
import { createMockContextLogger } from '../../mocks/contextLogger';
import { EntitlementDTO } from '../../types/entitlement';
import { EntitlementService } from '../EntitlementService';
import { EntitlementValidationService } from '../EntitlementValidationService';

// Mock dependencies
jest.mock('../EntitlementValidationService');
jest.mock('../../clients/TenantClient');

describe('EntitlementService', () => {
  let entitlementService: EntitlementService;
  let mockTenantClient: jest.Mocked<TenantClient>;
  let mockValidationService: jest.Mocked<EntitlementValidationService>;
  let mockContextLogger: any;

  const mockEntitlements: EntitlementDTO[] = [
    {
      id: 'ent-1',
      displayId: 'ent-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-1',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-12-31T23:59:59Z',
      units: 10,
      salesforceServiceId: 'sf-1',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test1.com',
        name: 'Test Company 1',
      },
      entitled: true,
    },
    {
      id: 'ent-2',
      displayId: 'ent-2',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-2',
      startDate: '2024-01-01T00:00:00Z',
      endDate: null,
      units: 5,
      salesforceServiceId: 'sf-2',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test2.com',
        name: 'Test Company 2',
      },
      entitled: true,
    },
    {
      id: 'ent-3',
      displayId: 'ent-3',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-3',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-12-31T23:59:59Z',
      units: 3,
      salesforceServiceId: 'sf-3',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test3.com',
        name: 'Test Company 3',
      },
      entitled: true,
    },
  ];

  const mockValidatedEntitlements: EntitlementDTO[] = [
    mockEntitlements[0],
    mockEntitlements[2],
  ];

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock ContextLogger
    mockContextLogger = createMockContextLogger();

    // Mock TenantClient
    mockTenantClient = {
      getAllEntitlements: jest.fn(),
    } as any;

    // Mock EntitlementValidationService
    mockValidationService = {
      validateEntitled: jest.fn(),
      validateEntitlementsByFeatureFlags: jest.fn(),
    } as any;

    entitlementService = new EntitlementService(
      mockTenantClient,
      mockValidationService,
      mockContextLogger,
    );
  });

  describe('constructor', () => {
    it('should initialize with correct dependencies', () => {
      expect(entitlementService).toBeInstanceOf(EntitlementService);
    });
  });

  describe('getEntitlements', () => {
    it('should fetch entitlements from tenant client', async () => {
      mockTenantClient.getAllEntitlements.mockResolvedValue(mockEntitlements);

      const result = await entitlementService.getEntitlements();

      expect(mockTenantClient.getAllEntitlements).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockEntitlements);
    });

    it('should create operation logger and log fetched entitlements', async () => {
      mockTenantClient.getAllEntitlements.mockResolvedValue(mockEntitlements);

      await entitlementService.getEntitlements();

      expect(mockContextLogger.createChild).toHaveBeenCalledWith({
        operation: 'getEntitlements',
      });

      const operationLogger =
        mockContextLogger.createChild.mock.results[0].value;
      expect(operationLogger.info).toHaveBeenCalledWith(
        'Fetched entitlements',
        { entitlements: 3 },
      );
    });

    it('should handle empty entitlements', async () => {
      mockTenantClient.getAllEntitlements.mockResolvedValue([]);

      const result = await entitlementService.getEntitlements();

      expect(result).toEqual([]);
      const operationLogger =
        mockContextLogger.createChild.mock.results[0].value;
      expect(operationLogger.info).toHaveBeenCalledWith(
        'Fetched entitlements',
        { entitlements: 0 },
      );
    });

    it('should propagate tenant client errors', async () => {
      const error = new Error('Tenant service error');
      mockTenantClient.getAllEntitlements.mockRejectedValue(error);

      await expect(entitlementService.getEntitlements()).rejects.toThrow(
        'Tenant service error',
      );
    });
  });

  describe('validateStandard', () => {
    it('should validate entitlements using validation service', async () => {
      mockValidationService.validateEntitled.mockReturnValue(mockEntitlements);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockValidatedEntitlements,
      );

      const result =
        await entitlementService.validateStandard(mockEntitlements);

      expect(mockValidationService.validateEntitled).toHaveBeenCalledWith(
        mockEntitlements,
      );
      expect(
        mockValidationService.validateEntitlementsByFeatureFlags,
      ).toHaveBeenCalledWith(mockEntitlements);
      expect(result).toEqual(mockValidatedEntitlements);
    });

    it('should create operation logger and log validation results', async () => {
      mockValidationService.validateEntitled.mockReturnValue(mockEntitlements);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockValidatedEntitlements,
      );

      await entitlementService.validateStandard(mockEntitlements);

      expect(mockContextLogger.createChild).toHaveBeenCalledWith({
        operation: 'validateStandard',
      });

      const operationLogger =
        mockContextLogger.createChild.mock.results[0].value;
      expect(operationLogger.info).toHaveBeenCalledWith(
        'Validated entitlements',
        { entitlements: 2 },
      );
    });

    it('should handle empty entitlements', async () => {
      mockValidationService.validateEntitled.mockReturnValue([]);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        [],
      );

      const result = await entitlementService.validateStandard([]);

      expect(result).toEqual([]);
      const operationLogger =
        mockContextLogger.createChild.mock.results[0].value;
      expect(operationLogger.info).toHaveBeenCalledWith(
        'Validated entitlements',
        { entitlements: 0 },
      );
    });

    it('should propagate validation service errors', async () => {
      const error = new Error('Validation service error');
      mockValidationService.validateEntitled.mockReturnValue(mockEntitlements);
      mockValidationService.validateEntitlementsByFeatureFlags.mockRejectedValue(
        error,
      );

      await expect(
        entitlementService.validateStandard(mockEntitlements),
      ).rejects.toThrow('Validation service error');
    });

    it('should handle validation service throwing on validateEntitled', async () => {
      const error = new Error('Entitled validation error');
      mockValidationService.validateEntitled.mockImplementation(() => {
        throw error;
      });

      await expect(
        entitlementService.validateStandard(mockEntitlements),
      ).rejects.toThrow('Entitled validation error');
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete workflow: get -> validate -> chunk', async () => {
      // Mock the complete workflow
      mockTenantClient.getAllEntitlements.mockResolvedValue(mockEntitlements);
      mockValidationService.validateEntitled.mockReturnValue(mockEntitlements);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockValidatedEntitlements,
      );

      // Execute workflow
      const fetchedEntitlements = await entitlementService.getEntitlements();
      const validatedEntitlements =
        await entitlementService.validateStandard(fetchedEntitlements);

      // Verify results
      expect(fetchedEntitlements).toEqual(mockEntitlements);
      expect(validatedEntitlements).toEqual(mockValidatedEntitlements);
    });

    it('should handle workflow with no valid entitlements', async () => {
      mockTenantClient.getAllEntitlements.mockResolvedValue(mockEntitlements);
      mockValidationService.validateEntitled.mockReturnValue([]);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        [],
      );

      const fetchedEntitlements = await entitlementService.getEntitlements();
      const validatedEntitlements =
        await entitlementService.validateStandard(fetchedEntitlements);

      expect(fetchedEntitlements).toEqual(mockEntitlements);
      expect(validatedEntitlements).toEqual([]);
    });
  });
});
