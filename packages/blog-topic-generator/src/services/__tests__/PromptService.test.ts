import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';
import { ChatPromptClient, Langfuse } from 'langfuse';
import { CallbackHandler } from 'langfuse-langchain';
import { z } from 'zod';

import { createMockContextLogger } from '../../mocks/contextLogger';
import { PromptService } from '../PromptService';

// Mock dependencies
jest.mock('@langchain/core/prompts');
jest.mock('@langchain/openai');
jest.mock('langfuse');
jest.mock('langfuse-langchain');
jest.mock('@langfuse/openai', () => ({
  observeOpenAI: jest.fn().mockImplementation(client => client),
}));
jest.mock('../../config');

// Mock config
const mockConfig = {
  LANGFUSE_SECRET_KEY: 'test-secret-key',
  LANGFUSE_PUBLIC_KEY: 'test-public-key',
  LANGFUSE_BASE_URL: 'https://test.langfuse.com',
  LANGFUSE_ENVIRONMENT: 'test',
  LANGFUSE_FLUSH_AT: 10,
  OPENAI_MODEL_NAME: 'gpt-4',
  OPENAI_TEMPERATURE: 0.7,
  OPENAI_API_KEY: 'test-openai-key',
  OPENAI_REASONING: 'medium',
};

jest.mock('../../config', () => ({
  getConfig: jest.fn(() => mockConfig),
}));

// Mock ContextLogger
const mockContextLogger = createMockContextLogger();

describe('PromptService', () => {
  let promptService: PromptService;
  let mockLangfuse: jest.Mocked<Langfuse>;
  let mockCallbackHandler: jest.Mocked<CallbackHandler>;
  let mockChatOpenAI: jest.Mocked<ChatOpenAI>;
  let mockChatPromptTemplate: jest.Mocked<ChatPromptTemplate>;
  let mockPromptTemplate: jest.Mocked<PromptTemplate>;

  // Helper function to create mock chain
  const createMockChain = (invokeResult: any) =>
    ({
      lc_runnable: true,
      getName: jest.fn(),
      bind: jest.fn(),
      map: jest.fn(),
      withConfig: jest.fn(),
      withFallbacks: jest.fn(),
      withRetry: jest.fn(),
      withListeners: jest.fn(),
      withCallbacks: jest.fn(),
      stream: jest.fn(),
      streamEvents: jest.fn(),
      batch: jest.fn(),
      invoke: jest.fn().mockResolvedValue(invokeResult),
      transform: jest.fn(),
      pipe: jest.fn(),
    }) as any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Mock Langfuse
    mockLangfuse = {
      getPrompt: jest.fn(),
    } as any;

    // Mock CallbackHandler
    mockCallbackHandler = {} as any;

    // Mock ChatOpenAI
    mockChatOpenAI = {
      withStructuredOutput: jest.fn().mockReturnThis(),
    } as any;

    // Mock ChatPromptTemplate
    mockChatPromptTemplate = {
      pipe: jest.fn().mockReturnValue({
        lc_runnable: true,
        getName: jest.fn(),
        bind: jest.fn(),
        map: jest.fn(),
        withConfig: jest.fn(),
        withFallbacks: jest.fn(),
        withRetry: jest.fn(),
        withListeners: jest.fn(),
        withCallbacks: jest.fn(),
        stream: jest.fn(),
        streamEvents: jest.fn(),
        batch: jest.fn(),
        invoke: jest.fn(),
        transform: jest.fn(),
        pipe: jest.fn(),
      } as any),
    } as any;

    // Mock PromptTemplate
    mockPromptTemplate = {
      pipe: jest.fn().mockReturnValue({
        lc_runnable: true,
        getName: jest.fn(),
        bind: jest.fn(),
        map: jest.fn(),
        withConfig: jest.fn(),
        withFallbacks: jest.fn(),
        withRetry: jest.fn(),
        withListeners: jest.fn(),
        withCallbacks: jest.fn(),
        stream: jest.fn(),
        streamEvents: jest.fn(),
        batch: jest.fn(),
        invoke: jest.fn(),
        transform: jest.fn(),
        pipe: jest.fn(),
      } as any),
    } as any;

    // Mock constructors
    (Langfuse as jest.MockedClass<typeof Langfuse>).mockImplementation(
      () => mockLangfuse,
    );
    (
      CallbackHandler as jest.MockedClass<typeof CallbackHandler>
    ).mockImplementation(() => mockCallbackHandler);
    (ChatOpenAI as jest.MockedClass<typeof ChatOpenAI>).mockImplementation(
      () => mockChatOpenAI,
    );
    (ChatPromptTemplate.fromMessages as jest.Mock).mockReturnValue(
      mockChatPromptTemplate,
    );
    (PromptTemplate.fromTemplate as jest.Mock).mockReturnValue(
      mockPromptTemplate,
    );

    promptService = new PromptService(mockContextLogger);
  });

  describe('constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(Langfuse).toHaveBeenCalledWith({
        secretKey: mockConfig.LANGFUSE_SECRET_KEY,
        publicKey: mockConfig.LANGFUSE_PUBLIC_KEY,
        baseUrl: mockConfig.LANGFUSE_BASE_URL,
      });

      expect(CallbackHandler).toHaveBeenCalledWith({
        secretKey: mockConfig.LANGFUSE_SECRET_KEY,
        publicKey: mockConfig.LANGFUSE_PUBLIC_KEY,
        baseUrl: mockConfig.LANGFUSE_BASE_URL,
        environment: mockConfig.LANGFUSE_ENVIRONMENT,
        flushAt: mockConfig.LANGFUSE_FLUSH_AT,
      });

      expect(ChatOpenAI).toHaveBeenCalledWith({
        modelName: mockConfig.OPENAI_MODEL_NAME,
        temperature: mockConfig.OPENAI_TEMPERATURE,
        apiKey: mockConfig.OPENAI_API_KEY,
      });
    });
  });

  describe('executeChatPrompt', () => {
    const mockChatPrompt: ChatPromptClient = {
      type: 'chat',
      prompt: [
        {
          role: 'user',
          content: 'Hello {{name}}',
        },
        {
          role: 'assistant',
          content: 'Hi there!',
        },
      ],
    } as any;

    const mockInputs = { name: 'John' };
    const mockResult = { content: 'Hello John!' };

    beforeEach(() => {
      mockLangfuse.getPrompt.mockResolvedValue(mockChatPrompt);
    });

    it('should execute chat prompt successfully', async () => {
      const mockChain = createMockChain(mockResult);
      mockChatPromptTemplate.pipe.mockReturnValue(mockChain);

      const result = await promptService.executeChatPrompt(
        'test-prompt',
        mockInputs,
      );

      expect(mockLangfuse.getPrompt).toHaveBeenCalledWith('test-prompt');
      expect(ChatPromptTemplate.fromMessages).toHaveBeenCalled();
      expect(mockChatPromptTemplate.pipe).toHaveBeenCalledWith(mockChatOpenAI);
      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [mockCallbackHandler],
        runName: 'test-prompt',
      });

      expect(result).toEqual({
        content: 'Hello John!',
        prompt: JSON.stringify(mockChatPromptTemplate),
        prompt_id: undefined,
        prompt_name: undefined,
      });
    });

    it('should execute chat prompt with structured output', async () => {
      const mockSchema = z.object({
        message: z.string(),
      });
      const mockStructuredResult = { message: 'Hello John!' };
      const mockStructuredModel = {
        invoke: jest.fn().mockResolvedValue(mockStructuredResult),
      };
      const mockStructuredChain = createMockChain(mockStructuredResult);

      mockChatOpenAI.withStructuredOutput.mockReturnValue(
        mockStructuredModel as any,
      );
      mockChatPromptTemplate.pipe.mockReturnValue(mockStructuredChain);

      const result = await promptService.executeChatPrompt(
        'test-prompt',
        mockInputs,
        mockSchema,
      );

      expect(mockChatOpenAI.withStructuredOutput).toHaveBeenCalledWith(
        mockSchema,
      );
      expect(mockChatPromptTemplate.pipe).toHaveBeenCalledWith(
        mockStructuredModel,
      );
      expect(result.content).toEqual(mockStructuredResult);
    });

    it('should execute chat prompt with metadata', async () => {
      const mockChain = createMockChain(mockResult);
      mockChatPromptTemplate.pipe.mockReturnValue(mockChain);
      const metadata = { userId: '123' };

      await promptService.executeChatPrompt(
        'test-prompt',
        mockInputs,
        undefined,
        metadata,
      );

      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [mockCallbackHandler],
        runName: 'test-prompt',
        metadata,
      });
    });

    it('should handle placeholder messages in chat prompt', async () => {
      const mockChatPromptWithPlaceholders: ChatPromptClient = {
        type: 'chat',
        prompt: [
          {
            role: 'user',
            content: 'Hello {{name}}',
          },
          {
            type: 'placeholder',
            name: 'dynamic_content',
          },
          {
            role: 'assistant',
            content: 'Hi there!',
          },
        ],
      } as any;

      mockLangfuse.getPrompt.mockResolvedValue(mockChatPromptWithPlaceholders);
      const mockChain = createMockChain(mockResult);
      mockChatPromptTemplate.pipe.mockReturnValue(mockChain);

      await promptService.executeChatPrompt('test-prompt', mockInputs);

      // Should filter out placeholder messages and sanitize content
      expect(ChatPromptTemplate.fromMessages).toHaveBeenCalledWith([
        ['human', 'Hello {{{name}}}'],
        ['ai', 'Hi there!'],
      ]);
    });

    it('should throw error for text prompt when expecting chat prompt', async () => {
      const mockTextPrompt = {
        type: 'text',
        getLangchainPrompt: jest.fn(),
      } as any;

      mockLangfuse.getPrompt.mockResolvedValue(mockTextPrompt);

      await expect(
        promptService.executeChatPrompt('test-prompt', mockInputs),
      ).rejects.toThrow(
        'Expected chat prompt but got text prompt: test-prompt',
      );
    });

    it('should handle errors during execution', async () => {
      const error = new Error('Langfuse error');
      mockLangfuse.getPrompt.mockRejectedValue(error);

      await expect(
        promptService.executeChatPrompt('test-prompt', mockInputs),
      ).rejects.toThrow('Langfuse error');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to execute chat prompt: test-prompt',
        { error },
      );
    });
  });

  describe('executeTextPrompt', () => {
    const mockTextPrompt = {
      type: 'text',
      getLangchainPrompt: jest.fn().mockReturnValue('Hello {{name}}'),
    } as any;

    const mockInputs = { name: 'John' };
    const mockResult = { content: 'Hello John!' };

    beforeEach(() => {
      mockLangfuse.getPrompt.mockResolvedValue(mockTextPrompt);
    });

    it('should execute text prompt successfully', async () => {
      const mockChain = createMockChain(mockResult);
      mockPromptTemplate.pipe.mockReturnValue(mockChain);

      const result = await promptService.executeTextPrompt(
        'test-prompt',
        mockInputs,
      );

      expect(mockLangfuse.getPrompt).toHaveBeenCalledWith('test-prompt');
      expect(mockTextPrompt.getLangchainPrompt).toHaveBeenCalled();
      expect(PromptTemplate.fromTemplate).toHaveBeenCalledWith(
        'Hello {{name}}',
      );
      expect(mockPromptTemplate.pipe).toHaveBeenCalledWith(mockChatOpenAI);
      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [mockCallbackHandler],
        runName: 'test-prompt',
      });

      expect(result).toEqual({
        content: 'Hello John!',
        prompt: JSON.stringify(mockPromptTemplate),
        prompt_id: undefined,
        prompt_name: undefined,
      });
    });

    it('should execute text prompt with metadata', async () => {
      const mockChain = createMockChain(mockResult);
      mockPromptTemplate.pipe.mockReturnValue(mockChain);
      const metadata = { userId: '123' };

      await promptService.executeTextPrompt(
        'test-prompt',
        mockInputs,
        metadata,
      );

      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [mockCallbackHandler],
        runName: 'test-prompt',
        metadata,
      });
    });

    it('should throw error for chat prompt when expecting text prompt', async () => {
      const mockChatPrompt = {
        type: 'chat',
        prompt: [],
      } as any;

      mockLangfuse.getPrompt.mockResolvedValue(mockChatPrompt);

      await expect(
        promptService.executeTextPrompt('test-prompt', mockInputs),
      ).rejects.toThrow(
        'Expected text prompt but got chat prompt: test-prompt',
      );
    });

    it('should handle errors during execution', async () => {
      const error = new Error('Langfuse error');
      mockLangfuse.getPrompt.mockRejectedValue(error);

      await expect(
        promptService.executeTextPrompt('test-prompt', mockInputs),
      ).rejects.toThrow('Langfuse error');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to execute text prompt: test-prompt',
        { error },
      );
    });
  });

  describe('sanitizePromptContent', () => {
    it('should sanitize prompt content correctly', () => {
      // Access private method through any type
      const sanitizePromptContent = (
        promptService as any
      ).sanitizePromptContent.bind(promptService);

      // Test basic replacement - the method converts {{}} to {{{}}}
      expect(sanitizePromptContent('Hello {{name}}')).toBe('Hello {{{name}}}');

      // Test complex replacement
      expect(
        sanitizePromptContent('Hello {{user.name}} from {{user.city}}'),
      ).toBe('Hello {{{user.name}}} from {{{user.city}}}');

      // Test with mixed braces - single braces get converted to {{}}
      expect(sanitizePromptContent('Hello {name} and {{other}}')).toBe(
        'Hello {{name}} and {{{other}}}',
      );

      // Test with nested braces
      expect(sanitizePromptContent('Hello {{{name}}}')).toBe(
        'Hello {{{name}}}',
      );
    });
  });

  describe('prompt type validation', () => {
    it('should correctly identify text prompts', () => {
      const isTextPrompt = (promptService as any).isTextPrompt;

      expect(isTextPrompt({ type: 'text' })).toBe(true);
      expect(isTextPrompt({ type: 'chat' })).toBe(false);
      expect(isTextPrompt(null)).toBe(false);
      expect(isTextPrompt(undefined)).toBe(false);
    });

    it('should correctly identify chat prompts', () => {
      const isChatPrompt = (promptService as any).isChatPrompt;

      expect(isChatPrompt({ type: 'chat' })).toBe(true);
      expect(isChatPrompt({ type: 'text' })).toBe(false);
      expect(isChatPrompt(null)).toBe(false);
      expect(isChatPrompt(undefined)).toBe(false);
    });
  });

  describe('executeChatReasoningPromptBackground', () => {
    const mockInputs = { input1: 'value1', input2: 'value2' };
    const mockMetadata = { sessionId: 'test-session' };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should execute background reasoning prompt successfully', async () => {
      const mockPrompt = {
        type: 'chat',
        compile: jest.fn().mockReturnValue('compiled prompt'),
      } as any;
      const mockResponse = {
        id: 'response-123',
        status: 'in_progress',
      };

      mockLangfuse.getPrompt.mockResolvedValue(mockPrompt);
      (promptService as any).openai = {
        responses: {
          create: jest.fn().mockResolvedValue(mockResponse),
        },
      };

      const result = await promptService.executeChatReasoningPromptBackground(
        'test-prompt',
        mockInputs,
        mockMetadata,
      );

      expect(result).toEqual({
        responseId: 'response-123',
        status: 'in_progress',
      });

      expect(mockLangfuse.getPrompt).toHaveBeenCalledWith('test-prompt');
      expect(mockPrompt.compile).toHaveBeenCalledWith(mockInputs);
      expect(
        (promptService as any).openai.responses.create,
      ).toHaveBeenCalledWith(
        expect.objectContaining({
          model: mockConfig.OPENAI_MODEL_NAME,
          background: true,
          stream: false,
          input: 'compiled prompt',
          reasoning: {
            effort: mockConfig.OPENAI_REASONING,
            summary: 'auto',
          },
          tools: [
            {
              type: 'web_search',
              filters: null,
              search_context_size: 'medium',
            },
          ],
          include: ['web_search_call.action.sources'],
          max_tool_calls: 20,
          parallel_tool_calls: true,
          text: {
            format: {
              type: 'text',
            },
            verbosity: 'low',
          },
        }),
      );
    });

    it('should handle missing prompt', async () => {
      mockLangfuse.getPrompt.mockResolvedValue(null as any);

      await expect(
        promptService.executeChatReasoningPromptBackground(
          'missing-prompt',
          mockInputs,
        ),
      ).rejects.toThrow('Prompt not found: missing-prompt');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to execute background reasoning prompt: missing-prompt',
        { error: expect.any(Error) },
      );
    });

    it('should handle wrong prompt type', async () => {
      const mockPrompt = { type: 'text' } as any;
      mockLangfuse.getPrompt.mockResolvedValue(mockPrompt);

      await expect(
        promptService.executeChatReasoningPromptBackground(
          'text-prompt',
          mockInputs,
        ),
      ).rejects.toThrow(
        'Expected chat prompt but got text prompt: text-prompt',
      );

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to execute background reasoning prompt: text-prompt',
        { error: expect.any(Error) },
      );
    });

    it('should handle OpenAI API errors', async () => {
      const mockPrompt = {
        type: 'chat',
        compile: jest.fn().mockReturnValue('compiled prompt'),
      } as any;
      const error = new Error('OpenAI API error');

      mockLangfuse.getPrompt.mockResolvedValue(mockPrompt);
      (promptService as any).openai = {
        responses: {
          create: jest.fn().mockRejectedValue(error),
        },
      };

      await expect(
        promptService.executeChatReasoningPromptBackground(
          'test-prompt',
          mockInputs,
        ),
      ).rejects.toThrow('OpenAI API error');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to execute background reasoning prompt: test-prompt',
        { error },
      );
    });

    it('should work without metadata', async () => {
      const mockPrompt = {
        type: 'chat',
        compile: jest.fn().mockReturnValue('compiled prompt'),
      } as any;
      const mockResponse = {
        id: 'response-456',
        status: 'queued',
      };

      mockLangfuse.getPrompt.mockResolvedValue(mockPrompt);
      (promptService as any).openai = {
        responses: {
          create: jest.fn().mockResolvedValue(mockResponse),
        },
      };

      const result = await promptService.executeChatReasoningPromptBackground(
        'test-prompt',
        mockInputs,
      );

      expect(result).toEqual({
        responseId: 'response-456',
        status: 'queued',
      });
    });
  });

  describe('getBackgroundJobStatus', () => {
    const mockResponseId = 'response-123';

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should get completed job successfully', async () => {
      const mockResponse = {
        id: mockResponseId,
        status: 'completed',
        content: { result: 'test result' },
      };

      (promptService as any).openai = {
        responses: {
          retrieve: jest.fn().mockResolvedValue(mockResponse),
        },
      };

      const result = await promptService.getBackgroundJobStatus(mockResponseId);

      expect(result).toEqual({
        status: 'completed',
        content: mockResponse,
      });

      expect(
        (promptService as any).openai.responses.retrieve,
      ).toHaveBeenCalledWith(mockResponseId);
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'Getting background job status: response-123',
      );
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'Background job status retrieved',
        {
          responseId: mockResponseId,
          status: 'completed',
        },
      );
    });

    it('should get in-progress job successfully', async () => {
      const mockResponse = {
        id: mockResponseId,
        status: 'in_progress',
      };

      (promptService as any).openai = {
        responses: {
          retrieve: jest.fn().mockResolvedValue(mockResponse),
        },
      };

      const result = await promptService.getBackgroundJobStatus(mockResponseId);

      expect(result).toEqual({
        status: 'in_progress',
      });

      expect(
        (promptService as any).openai.responses.retrieve,
      ).toHaveBeenCalledWith(mockResponseId);
    });

    it('should get failed job successfully', async () => {
      const mockResponse = {
        id: mockResponseId,
        status: 'failed',
      };

      (promptService as any).openai = {
        responses: {
          retrieve: jest.fn().mockResolvedValue(mockResponse),
        },
      };

      const result = await promptService.getBackgroundJobStatus(mockResponseId);

      expect(result).toEqual({
        status: 'failed',
      });
    });

    it('should handle OpenAI API errors', async () => {
      const error = new Error('OpenAI API error');

      (promptService as any).openai = {
        responses: {
          retrieve: jest.fn().mockRejectedValue(error),
        },
      };

      await expect(
        promptService.getBackgroundJobStatus(mockResponseId),
      ).rejects.toThrow('OpenAI API error');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to get background job status',
        {
          error: 'OpenAI API error',
          stack: expect.stringContaining('Error: OpenAI API error'),
          responseId: mockResponseId,
        },
      );
    });

    it('should handle different job statuses', async () => {
      const statuses = ['queued', 'validating', 'finalizing', 'cancelled'];

      for (const status of statuses) {
        const mockResponse = {
          id: mockResponseId,
          status,
        };

        (promptService as any).openai = {
          responses: {
            retrieve: jest.fn().mockResolvedValue(mockResponse),
          },
        };

        const result =
          await promptService.getBackgroundJobStatus(mockResponseId);

        expect(result).toEqual({
          status,
        });
      }
    });
  });
});
