import { EntitlementValidationService } from './EntitlementValidationService';
import { TenantClient } from '../clients/TenantClient';
import { EntitlementDTO } from '../types/entitlement';
import { ContextLogger } from '../utils/contextLogger';

export class EntitlementService {
  constructor(
    private readonly tenantClient: TenantClient,
    private readonly validationService: EntitlementValidationService,
    private readonly logger: ContextLogger,
  ) {}

  async getEntitlements(): Promise<EntitlementDTO[]> {
    const operationLogger = this.logger.createChild({
      operation: 'getEntitlements',
    });
    const entitlements = await this.tenantClient.getAllEntitlements();
    operationLogger.info('Fetched entitlements', {
      entitlements: entitlements.length,
    });
    return entitlements;
  }

  async validateStandard(
    entitlements: EntitlementDTO[],
  ): Promise<EntitlementDTO[]> {
    // Use the available validation methods
    const operationLogger = this.logger.createChild({
      operation: 'validateStandard',
    });
    const dateValidated = this.validationService.validateEntitled(entitlements);
    const featureFlagFiltered =
      await this.validationService.validateEntitlementsByFeatureFlags(
        dateValidated,
      );
    operationLogger.info('Validated entitlements', {
      entitlements: featureFlagFiltered.length,
    });
    return featureFlagFiltered;
  }
}
