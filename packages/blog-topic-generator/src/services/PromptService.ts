import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';
import { observeOpenAI } from '@langfuse/openai';
import { ChatPromptClient, Langfuse, TextPromptClient } from 'langfuse';
import { CallbackHandler } from 'langfuse-langchain';
import OpenAI from 'openai';
import { BackgroundJobStatus, PromptResult } from 'src/types/prompt';
import { ContextLogger } from 'src/utils';
import { z } from 'zod';
// import { zodToJsonSchema } from 'zod-to-json-schema';

import { getConfig } from '../config';

export class PromptService {
  private readonly langfuse: Langfuse;
  private readonly langfuseLangchainHandler: CallbackHandler;
  private readonly model: ChatOpenAI;
  private readonly openai: OpenAI;

  constructor(
    private readonly logger: ContextLogger,
    private readonly config = getConfig(),
  ) {
    this.langfuse = new Langfuse({
      secretKey: this.config.LANGFUSE_SECRET_KEY,
      publicKey: this.config.LANGFUSE_PUBLIC_KEY,
      baseUrl: this.config.LANGFUSE_BASE_URL,
    });

    this.langfuseLangchainHandler = new CallbackHandler({
      secretKey: this.config.LANGFUSE_SECRET_KEY,
      publicKey: this.config.LANGFUSE_PUBLIC_KEY,
      baseUrl: this.config.LANGFUSE_BASE_URL,
      environment: this.config.LANGFUSE_ENVIRONMENT,
      flushAt: this.config.LANGFUSE_FLUSH_AT,
    });

    // Initialize the default model
    this.model = new ChatOpenAI({
      modelName: this.config.OPENAI_MODEL_NAME,
      temperature: this.config.OPENAI_TEMPERATURE,
      apiKey: this.config.OPENAI_API_KEY,
    });

    // Initialize OpenAI client for direct API calls
    this.openai = new OpenAI({
      apiKey: this.config.OPENAI_API_KEY,
    });
  }

  /**
   * Sanitize prompt content by replacing {{ with {
   */
  private sanitizePromptContent(content: string): string {
    this.logger.debug('Sanitizing prompt content');

    // Step 1: Replace single braces that are not followed by another brace with !{ or !}
    const step1Content = content.replace(/(?<!{){|}(?!})/g, match => {
      return match === '{' ? '!{' : '!}';
    });

    // Step 2: Convert {{var}} to {var}
    const step2Content = step1Content.replace(/{{(.*?)}}/g, '{$1}');

    // Step 3: Convert !{ and !} back to {{ and }}
    const finalContent = step2Content.replace(/!{/g, '{{').replace(/!}/g, '}}');

    return finalContent;
  }

  /**
   * Convert LangFuse chat prompt into LangChain ChatPromptTemplate
   */
  private convertLangfuseChatPromptToTemplate(
    messages: ChatPromptClient['prompt'],
  ): ChatPromptTemplate {
    this.logger.debug('Converting LangFuse prompt to LangChain template', {
      messages: JSON.stringify(messages),
    });
    return ChatPromptTemplate.fromMessages(
      messages
        .map(msg => {
          // Handle the new Langfuse message structure
          if (msg.type === 'placeholder') {
            // Skip placeholder messages as they don't have role/content
            return null;
          }

          const role =
            msg.role === 'user'
              ? 'human'
              : msg.role === 'assistant'
                ? 'ai'
                : 'system';
          const content = this.sanitizePromptContent(msg.content);
          return [role, content];
        })
        .filter(Boolean) as [string, string][], // Remove null entries and type assert
    );
  }

  private convertLangfuseTextPromptToTemplate(
    prompt: TextPromptClient,
  ): PromptTemplate {
    this.logger.debug('Converting LangFuse text prompt to LangChain template');
    return PromptTemplate.fromTemplate(prompt.getLangchainPrompt());
  }

  /**
   * Execute a chat prompt from LangFuse
   */
  public async executeChatPrompt<T = any>(
    promptName: string,
    inputs: Record<string, unknown>,

    structuredOutputSchema?: z.ZodType<T>,
    metadata?: Record<string, unknown>,
  ): Promise<PromptResult<T | string>> {
    this.logger.info(`Executing chat prompt: ${promptName}`);
    try {
      const prompt = await this.langfuse.getPrompt(promptName);
      if (!prompt) {
        throw new Error(`Prompt not found: ${promptName}`);
      }
      if (!this.isChatPrompt(prompt)) {
        throw new Error(
          `Expected chat prompt but got text prompt: ${promptName}`,
        );
      }
      const promptTemplate = this.convertLangfuseChatPromptToTemplate(
        prompt.prompt as unknown as ChatPromptClient['prompt'],
      );

      // Create a model with structured output if schema is provided
      let model: any;
      if (structuredOutputSchema) {
        const baseModel = new ChatOpenAI({
          modelName: this.config.OPENAI_MODEL_NAME,
          temperature: this.config.OPENAI_TEMPERATURE,
          apiKey: this.config.OPENAI_API_KEY,
        });
        // @ts-expect-error - Complex LangChain types cause "Type instantiation is excessively deep" error
        model = baseModel.withStructuredOutput(structuredOutputSchema);
      } else {
        model = this.model;
      }

      const chain = promptTemplate.pipe(model);

      const result = await chain.invoke(inputs, {
        callbacks: [this.langfuseLangchainHandler],
        runName: promptName,
        ...(metadata && { metadata }),
      });

      // Return a PromptResult with content as the structured output if schema provided
      return {
        content: structuredOutputSchema
          ? (result as T)
          : ((result as any).content as string),
        prompt: JSON.stringify(promptTemplate),
        prompt_id: (prompt as any).id,
        prompt_name: (prompt as any).name,
      };
    } catch (error) {
      this.logger.error(`Failed to execute chat prompt: ${promptName}`, {
        error,
      });
      throw error as Error;
    }
  }

  /**
   * Execute a text prompt from LangFuse
   */
  public async executeTextPrompt(
    promptName: string,
    inputs: Record<string, unknown>,
    metadata?: Record<string, unknown>,
  ): Promise<PromptResult> {
    this.logger.info(`Executing text prompt: ${promptName}`);
    try {
      const prompt = await this.langfuse.getPrompt(promptName);
      if (!prompt) {
        throw new Error(`Prompt not found: ${promptName}`);
      }
      if (!this.isTextPrompt(prompt)) {
        throw new Error(
          `Expected text prompt but got chat prompt: ${promptName}`,
        );
      }

      const promptTemplate = this.convertLangfuseTextPromptToTemplate(prompt);
      const chain = promptTemplate.pipe(this.model);

      const result = await chain.invoke(inputs, {
        callbacks: [this.langfuseLangchainHandler],
        runName: promptName,
        ...(metadata && { metadata }),
      });

      return {
        content: result.content as string,
        prompt: JSON.stringify(promptTemplate),
        prompt_id: (prompt as any).id,
        prompt_name: (prompt as any).name,
      };
    } catch (error) {
      this.logger.error(`Failed to execute text prompt: ${promptName}`, {
        error,
      });
      throw error;
    }
  }

  private isTextPrompt(prompt: TextPromptClient): boolean {
    return !!(prompt && prompt.type === 'text');
  }

  private isChatPrompt(prompt: TextPromptClient): boolean {
    return !!(prompt && prompt.type === 'chat');
  }

  /**
   * Execute a chat prompt with reasoning model capabilities using OpenAI SDK directly with background processing
   * Returns a response ID that can be polled later for status
   */
  public async executeChatReasoningPromptBackground(
    promptName: string,
    inputs: Record<string, string>,
    metadata?: Record<string, unknown>,
  ): Promise<{ responseId: string; status: BackgroundJobStatus }> {
    this.logger.info(
      `Executing reasoning prompt with background processing: ${promptName}`,
    );
    try {
      const prompt = await this.langfuse.getPrompt(promptName);
      if (!prompt) {
        throw new Error(`Prompt not found: ${promptName}`);
      }
      if (!this.isChatPrompt(prompt)) {
        throw new Error(
          `Expected chat prompt but got text prompt: ${promptName}`,
        );
      }

      // Convert Langfuse prompt to input format for responses API
      const compiledPrompt = prompt.compile(inputs);

      // Prepare OpenAI request options with background processing
      const requestOptions = {
        model: this.config.OPENAI_MODEL_NAME,
        background: true,
        stream: false,
        input: compiledPrompt,
        reasoning: {
          effort: this.config.OPENAI_REASONING,
          summary: 'auto',
        },
        tools: [
          {
            type: 'web_search',
            filters: null,
            search_context_size: 'medium',
          },
        ],
        include: ['web_search_call.action.sources'],
        max_tool_calls: 20,
        parallel_tool_calls: true,
        text: {
          format: {
            type: 'text',
          },
          verbosity: 'low',
        },
        // max_output_tokens: 4000,
      };

      // Create observed OpenAI client for Langfuse tracking
      const observedOpenAI = observeOpenAI(this.openai, {
        traceName: promptName,
        generationMetadata: metadata,
        sessionId: metadata?.sessionId as string,
      });

      // Make the API call with automatic Langfuse tracking
      // @ts-expect-error the library has a known type error
      const response = await observedOpenAI.responses.create(requestOptions);
      this.logger.info('Background Response:', { response });

      return {
        responseId: response.id,
        status: response.status as BackgroundJobStatus,
      };
    } catch (error) {
      this.logger.error(
        `Failed to execute background reasoning prompt: ${promptName}`,
        {
          error,
        },
      );
      throw error as Error;
    }
  }

  /**
   * Get the status of a background job
   */
  public async getBackgroundJobStatus(responseId: string): Promise<{
    status: BackgroundJobStatus;
    content?: any;
  }> {
    this.logger.info(`Getting background job status: ${responseId}`);
    try {
      const response = await this.openai.responses.retrieve(responseId);

      this.logger.info('Background job status retrieved', {
        responseId,
        status: response.status,
      });

      // Use the response status directly since enum values match OpenAI statuses
      if (response.status === BackgroundJobStatus.COMPLETED) {
        // For completed responses, the content structure may vary
        // We'll return the entire response for now and let the calling code handle it
        return {
          status: BackgroundJobStatus.COMPLETED,
          content: response,
        };
      }

      return {
        status: response.status as BackgroundJobStatus,
      };
    } catch (error) {
      this.logger.error('Failed to get background job status', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        responseId,
      });
      throw error;
    }
  }
}
