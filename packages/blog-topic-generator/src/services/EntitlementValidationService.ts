import { EntitlementDTO } from '../types/entitlement';
import { FeatureFlags } from '../types/featureFlags';
import { LaunchDarkly, ContextLogger } from '../utils';

class EntitlementValidationError extends Error {
  constructor(
    message: string,
    public readonly details: {
      entitlements?: EntitlementDTO[];
      error?: unknown;
    },
  ) {
    super(`EntitlementValidation: ${message}`);
    this.name = this.constructor.name;
  }
}

export class EntitlementValidationService {
  constructor(private readonly logger: ContextLogger) {}

  private async checkFeatureFlags(companyId: string): Promise<boolean> {
    return LaunchDarkly.checkVariation(
      FeatureFlags.ENABLE_SUPER_BLOOM,
      false,
      companyId,
    );
  }

  async validateEntitlementsByFeatureFlags(
    entitlements: EntitlementDTO[],
  ): Promise<EntitlementDTO[]> {
    try {
      const featureFlagEntitlements = await Promise.all(
        entitlements.map(async entitlement => {
          const enabled = await this.checkFeatureFlags(entitlement.companyId);
          return { entitlement, enabled };
        }),
      );

      return featureFlagEntitlements
        .filter(({ enabled, entitlement }) => {
          if (!enabled) {
            this.logger.info('Feature flag disabled for company', {
              companyId: entitlement.companyId,
            });
          }
          return enabled;
        })
        .map(({ entitlement }) => entitlement);
    } catch (error) {
      this.logger.error('Error validating entitlements by feature flags', {
        error,
      });
      throw new EntitlementValidationError(
        'Error validating entitlements by feature flags',
        {
          entitlements,
          error,
        },
      );
    }
  }

  validateEntitled(entitlements: EntitlementDTO[]): EntitlementDTO[] {
    const filteredEntitlements = entitlements.reduce(
      (
        acc: { valid: EntitlementDTO[]; invalid: EntitlementDTO[] },
        entitlement,
      ) => {
        if (entitlement.entitled) {
          acc.valid.push(entitlement);
        } else {
          acc.invalid.push(entitlement);
        }
        return acc;
      },
      { valid: [], invalid: [] },
    );
    if (filteredEntitlements.invalid.length > 0) {
      this.logger.info(
        'Invalid entitlements with expired dates or not active yet.',
        {
          invalidEntitlements: filteredEntitlements.invalid.length,
          invalidEntitlementCompanyIds: filteredEntitlements.invalid.map(
            e => e.companyId,
          ),
        },
      );
    }
    this.logger.info('Valid entitlements', {
      validEntitlements: filteredEntitlements.valid.length,
      validEntitlementCompanyIds: filteredEntitlements.valid.map(
        e => e.companyId,
      ),
    });
    return filteredEntitlements.valid;
  }
}
