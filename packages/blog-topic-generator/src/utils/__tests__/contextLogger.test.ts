import { Logger } from '@aws-lambda-powertools/logger';

import { ContextLogger } from '../contextLogger';

describe('ContextLogger', () => {
  let mockBaseLogger: jest.Mocked<Logger>;

  beforeEach(() => {
    mockBaseLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    } as unknown as jest.Mocked<Logger>;
  });

  describe('Context Management', () => {
    test('should initialize with context', () => {
      const initialContext = {
        actionId: 'action-123',
        companyId: 'company-456',
      };

      const logger = new ContextLogger(mockBaseLogger, initialContext);

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
      });
    });

    test('should add context values using createChild', () => {
      const logger = new ContextLogger(mockBaseLogger);

      const loggerWithContext = logger.createChild({
        actionId: 'action-123',
        companyId: 'company-456',
      });

      loggerWithContext.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
      });
    });

    test('should set multiple context values using createChild', () => {
      const logger = new ContextLogger(mockBaseLogger);

      const loggerWithContext = logger.createChild({
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
      });

      loggerWithContext.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
      });
    });

    test('should create child logger with selective context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      const loggerWithSelectiveContext = logger.createChild({
        companyId: 'company-789', // Override existing value
        operation: 'fetch', // Add new value
      });

      loggerWithSelectiveContext.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123', // Preserved from parent
        companyId: 'company-789', // Overridden
        operation: 'fetch', // Added
      });
    });

    test('should create child logger with empty context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      const loggerWithEmptyContext = logger.createChild({});

      loggerWithEmptyContext.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123', // Preserved from parent
        companyId: 'company-456', // Preserved from parent
      });
    });
  });

  describe('Child Loggers', () => {
    test('should create child logger with additional context', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        lambdaName: 'test-lambda',
        companyId: 'company-456',
      });

      const childLogger = parentLogger.createChild({
        operation: 'fetchWebsites',
        itemIndex: 0,
      });

      childLogger.info('Processing item');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Processing item', {
        lambdaName: 'test-lambda',
        companyId: 'company-456',
        operation: 'fetchWebsites',
        itemIndex: 0,
      });
    });

    test('should create component logger with architectural context', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        lambdaName: 'test-lambda',
        companyId: 'company-456',
      });

      const componentLogger = parentLogger.createComponentLogger({
        serviceName: 'EntitlementService',
        layer: 'business',
      });

      componentLogger.info('Service operation');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Service operation', {
        lambdaName: 'test-lambda',
        companyId: 'company-456',
        serviceName: 'EntitlementService',
        layer: 'business',
      });
    });
  });

  describe('Concurrent Operations Context Preservation', () => {
    test('should preserve context across concurrent operations', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        lambdaName: 'test-lambda',
        requestId: 'req-123',
      });

      const operationLogger = parentLogger.createChild({
        operation: 'fetchAllWebsites',
      });

      // Simulate concurrent operations
      const concurrentOperations = [
        operationLogger.createChild({ companyId: 'company-1', itemIndex: 0 }),
        operationLogger.createChild({ companyId: 'company-2', itemIndex: 1 }),
        operationLogger.createChild({ companyId: 'company-3', itemIndex: 2 }),
      ];

      // Execute concurrent operations
      concurrentOperations.map((logger, index) => {
        logger.info(`Processing company ${index + 1}`);
      });

      // Verify all logs include the full context chain
      expect(mockBaseLogger.info).toHaveBeenCalledWith('Processing company 1', {
        lambdaName: 'test-lambda',
        requestId: 'req-123',
        operation: 'fetchAllWebsites',
        companyId: 'company-1',
        itemIndex: 0,
      });

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Processing company 2', {
        lambdaName: 'test-lambda',
        requestId: 'req-123',
        operation: 'fetchAllWebsites',
        companyId: 'company-2',
        itemIndex: 1,
      });

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Processing company 3', {
        lambdaName: 'test-lambda',
        requestId: 'req-123',
        operation: 'fetchAllWebsites',
        companyId: 'company-3',
        itemIndex: 2,
      });
    });
  });

  describe('Proxy Methods', () => {
    test('should proxy info calls with context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        companyId: 'company-456',
      });

      logger.info('Test message', { additional: 'data' });

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        companyId: 'company-456',
        additional: 'data',
      });
    });

    test('should proxy error calls with context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        companyId: 'company-456',
      });

      logger.error('Error message', { errorCode: 'ERR_001' });

      expect(mockBaseLogger.error).toHaveBeenCalledWith('Error message', {
        companyId: 'company-456',
        errorCode: 'ERR_001',
      });
    });

    test('should proxy warn calls with context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        companyId: 'company-456',
      });

      logger.warn('Warning message', { warningType: 'deprecation' });

      expect(mockBaseLogger.warn).toHaveBeenCalledWith('Warning message', {
        companyId: 'company-456',
        warningType: 'deprecation',
      });
    });

    test('should proxy debug calls with context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        companyId: 'company-456',
      });

      logger.debug('Debug message', { debugLevel: 'verbose' });

      expect(mockBaseLogger.debug).toHaveBeenCalledWith('Debug message', {
        companyId: 'company-456',
        debugLevel: 'verbose',
      });
    });
  });
});
