export type ExecutionContextInfo = {
  executionName?: string;
  executionStartTime?: string; // ISO string
  dryRun?: boolean;
};

function getDateFromStart(start?: string): string {
  if (start && start.includes('T')) return start.split('T')[0];
  return new Date().toISOString().split('T')[0];
}

function shouldUseDirectFolder(info: ExecutionContextInfo): boolean {
  const hasName =
    typeof info.executionName === 'string' &&
    info.executionName.trim().length > 0;
  const hasStart =
    typeof info.executionStartTime === 'string' &&
    info.executionStartTime.includes('T');
  return !(hasName && hasStart);
}

export function buildExecutionFolder(info: ExecutionContextInfo): string {
  const dateOnly = getDateFromStart(info.executionStartTime);
  const drySuffix = info.dryRun ? '-dry-run' : '';
  if (shouldUseDirectFolder(info)) {
    const iso = info.executionStartTime
      ? info.executionStartTime.replace(/[:.]/g, '-')
      : new Date().toISOString().replace(/[:.]/g, '-');
    return `blog-topic-jobs/${dateOnly}/direct-${iso}${drySuffix}`;
  }
  return `blog-topic-jobs/${dateOnly}/${info.executionName}${drySuffix}`;
}

export function buildExecutionFileKey(
  fileName: string,
  info: ExecutionContextInfo,
): string {
  const folder = buildExecutionFolder(info);
  if (info.dryRun) {
    return `${folder}/${fileName}-dry-run.json`;
  }
  return `${folder}/${fileName}.json`;
}
