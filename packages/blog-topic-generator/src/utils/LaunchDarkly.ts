import {
  L<PERSON><PERSON>,
  LDContext,
  LDFlagValue,
  init,
} from 'launchdarkly-node-server-sdk';
import { getConfig } from 'src/config';

import { createLogger } from './logger';

export class LaunchDarkly {
  private static readonly config = getConfig();
  private static readonly KEY = this.config.LAUNCHDARKLY_KEY || '';
  private static readonly API_GATEWAY_SUPER_USER_COMPANY_ID =
    this.config.API_GATEWAY_SUPER_USER_COMPANY_ID || '';
  private static client: LDClient;
  private static logger = createLogger('LaunchDarkly');

  public static generateLDContext(companyId: string): LDContext {
    return {
      kind: 'company',
      name: 'company',
      key: companyId,
      companyId,
    };
  }

  /**
   * Check Variation and return the value
   * Does multi-key for company and user
   * @param {string} flagName Name related to the FF created via Launch Darkly
   * @param {boolean} defaultValue This will be automatically set to false when empty
   * @param {string} companyId Company ID
   */
  public static async checkVariation(
    flagName: string,
    defaultValue = false,
    companyId: string = this.API_GATEWAY_SUPER_USER_COMPANY_ID,
  ): Promise<LDFlagValue> {
    try {
      if (['development', 'local'].includes(LaunchDarkly.config.ENVIRONMENT)) {
        return true;
      }
      const context = LaunchDarkly.generateLDContext(companyId);

      const client = await LaunchDarkly.getClient();

      return client.variation(flagName, context, defaultValue);
    } catch (error) {
      this.logger.error('Error in LaunchDarkly checkVariation', error);
      return defaultValue;
    }
  }

  /**
   * LaunchDarkly Initialize Client
   * @return {Promise<LaunchDarkly.LDClient>} Client
   */
  public static getClient(): Promise<LDClient> {
    if (!this.client) {
      if (!this.KEY) {
        this.logger.error(
          'LAUNCHDARKLY_KEY is missing; cannot initialize LaunchDarkly client',
        );
        throw new Error('LAUNCHDARKLY_KEY is missing');
      }
      this.client = init(this.KEY);
    }
    return this.client.waitForInitialization();
  }

  public static closeConnection(): void {
    if (this.client) {
      this.client.close();
    }
  }
}
