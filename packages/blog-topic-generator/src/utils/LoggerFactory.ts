import { Logger } from '@aws-lambda-powertools/logger';
import { LogLevel } from '@aws-lambda-powertools/logger';
import { Context } from 'aws-lambda';
import { getConfig } from 'src/config';

export class LoggerFactory {
  static createLogger(context?: Context): Logger {
    const config = getConfig();
    const logger = new Logger({
      serviceName: 'bloom-scheduler',
      persistentLogAttributes: {
        awsRequestId: context?.awsRequestId,
        functionName: context?.functionName,
      },
      logLevel:
        LogLevel[config.LOG_LEVEL as keyof typeof LogLevel] || LogLevel.INFO,
    });
    return logger;
  }
}
