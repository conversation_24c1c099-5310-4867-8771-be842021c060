import { Handler, Context } from 'aws-lambda';
import { CosmoClient } from 'src/clients/CosmoClient';
import { BlogTopicInput } from 'src/clients/types/blogTopic';
import { getConfig } from 'src/config';
import { GeneratedTopic } from 'src/types/prompt';
import { createContextLogger, ContextLogger } from 'src/utils/contextLogger';

export interface SaveGeneratedTopicsInput {
  success: boolean;
  companyId: string;
  neighborhoodId: string;
  neighborhoodName: string;
  responseId?: string;
  status?: string;
  generatedTopics?: GeneratedTopic[];
  totalTopics?: number;
  error?: string;
}

export interface SaveGeneratedTopicsOutput {
  success: boolean;
  companyId: string;
  neighborhoodId: string;
  neighborhoodName: string;
  topicsSaved: number;
  error?: string;
}

export const handler: Handler<
  SaveGeneratedTopicsInput,
  SaveGeneratedTopicsOutput
> = async (input, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'save-generated-topics',
    context,
  );

  // Validate input
  if (!input) {
    logger.error('Invalid input: SaveGeneratedTopicsInput is required');
    throw new Error('Invalid input: SaveGeneratedTopicsInput is required');
  }

  try {
    logger.info('SaveGeneratedTopics Lambda invoked', {
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      success: input.success,
      totalTopics: input.totalTopics,
      responseId: input.responseId,
      status: input.status,
    });

    if (
      !input.success ||
      !input.generatedTopics ||
      input.generatedTopics.length === 0
    ) {
      logger.warn('No topics to save', {
        companyId: input.companyId,
        neighborhoodId: input.neighborhoodId,
        success: input.success,
        topicsCount: input.generatedTopics?.length || 0,
        responseId: input.responseId,
        status: input.status,
      });

      return {
        success: input.success,
        companyId: input.companyId,
        neighborhoodId: input.neighborhoodId,
        neighborhoodName: input.neighborhoodName,
        topicsSaved: 0,
      };
    }

    const cosmoClient = new CosmoClient(
      config.COSMO_GQL_URL,
      config.M2M_SUPER_API_KEY,
      logger.createComponentLogger({ clientName: 'CosmoClient', layer: 'api' }),
    );

    const blogTopics: BlogTopicInput[] = input.generatedTopics.map(topic => ({
      topic: topic.topic,
      blogTitle: topic.blogTitle,
      rationale: topic.rationale,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      parentTopic: topic.parentTopic,
      type: topic.type,
    }));

    const savedTopics = await cosmoClient.createBlogTopicsBulk(blogTopics);

    logger.info('Topics saved successfully', {
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      topicsSaved: savedTopics.createdBlogTopics.length,
    });

    return {
      success: true,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      neighborhoodName: input.neighborhoodName,
      topicsSaved: savedTopics.createdBlogTopics.length,
    };
  } catch (error) {
    logger.error('Error in SaveGeneratedTopics Lambda', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
    });

    // Preserve original error message for tests and debugging
    const message = error instanceof Error ? error.message : 'Unknown error';

    return {
      success: false,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      neighborhoodName: input.neighborhoodName,
      topicsSaved: 0,
      error: message,
    };
  }
};
