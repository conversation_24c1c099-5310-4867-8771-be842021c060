import { Handler, Context } from 'aws-lambda';
import { getConfig } from 'src/config';
import { PromptService } from 'src/services/PromptService';
import { FlattenedEntitlement } from 'src/types/entitlement';
import { BackgroundJobStatus } from 'src/types/prompt';
import { create<PERSON><PERSON><PERSON>tLogger, ContextLogger } from 'src/utils/contextLogger';

const getNeighborhoodInfo = (neighborhood: any): string => {
  const components = [];

  if (neighborhood.name) {
    components.push(neighborhood.name);
  }

  if (neighborhood.googlePlaceData?.addressComponents) {
    const addressComponents = neighborhood.googlePlaceData.addressComponents;

    // Find components by type and add their long/short names
    addressComponents.forEach((component: any) => {
      if (component.types.includes('neighborhood') && component.long_name) {
        components.push(component.long_name);
      }
      if (component.types.includes('locality') && component.long_name) {
        components.push(component.long_name);
      }
      if (
        component.types.includes('administrative_area_level_2') &&
        component.long_name
      ) {
        components.push(component.long_name);
      }
      if (
        component.types.includes('administrative_area_level_1') &&
        component.short_name
      ) {
        components.push(component.short_name);
      }
      if (component.types.includes('country') && component.short_name) {
        components.push(component.short_name);
      }
    });
  }

  return components.filter(Boolean).join(', ');
};

interface GenerateBlogTopicsBackgroundOutput {
  success: boolean;
  companyId: string;
  neighborhoodId: string;
  neighborhoodName: string;
  responseId: string | null;
  status?: BackgroundJobStatus;
  error?: string;
}

// Add this interface for the execution context
interface ExecutionInfo {
  executionName: string;
  executionStartTime: string;
  dryRun: boolean;
}

// This is the NEW top-level event type for your Lambda handler
export interface GenerateBackgroundEvent {
  itemData: FlattenedEntitlement;
  executionInfo: ExecutionInfo;
}

export const handler: Handler<
  GenerateBackgroundEvent,
  GenerateBlogTopicsBackgroundOutput
> = async (event, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'generate-blog-topics-background',
    context,
  );

  // Validate input
  if (!event.itemData) {
    logger.error('Invalid input: FlattenedEntitlement is required');
    throw new Error('Invalid input: FlattenedEntitlement is required');
  }

  try {
    logger.info('GenerateBlogTopicsBackground Lambda invoked', {
      companyId: event.itemData.companyId,
      neighborhoodId: event.itemData.neighborhood.id,
    });

    const promptService = new PromptService(
      logger.createComponentLogger({
        serviceName: 'PromptService',
        layer: 'ai',
      }),
      config,
    );

    // Generate a unique job ID for tracking
    const jobId = `blog-topics-${event.itemData.companyId}-${event.itemData.neighborhood.id}-${Date.now()}`;

    logger.info('Starting background blog topic generation', {
      jobId,
      companyId: event.itemData.companyId,
      neighborhoodId: event.itemData.neighborhood.id,
    });

    // Execute the prompt with background processing
    const result = await promptService.executeChatReasoningPromptBackground(
      'Blog Topics Engine (One Location)',
      {
        neighborhood_info: getNeighborhoodInfo(event.itemData.neighborhood),
        brand_profile: JSON.stringify(event.itemData.brandProfile),
        keyword_map: JSON.stringify(event.itemData.keywordMap),
      },
      {
        companyId: event.itemData.companyId,
        neighborhoodId: event.itemData.neighborhood.id,
        jobId,
        generationMetadata: {
          environment: config.ENVIRONMENT,
          feature: 'blog-topic-generator-background',
        },
      },
    );

    logger.info('Background job initiated', {
      jobId,
      responseId: result.responseId,
      status: result.status,
      companyId: event.itemData.companyId,
      neighborhoodId: event.itemData.neighborhood.id,
    });

    return {
      success: true,
      companyId: event.itemData.companyId,
      neighborhoodId: event.itemData.neighborhood.id,
      neighborhoodName: event.itemData.neighborhood.name,
      responseId: result.responseId,
      status: result.status,
    };
  } catch (error) {
    logger.error('Error in GenerateBlogTopicsBackground Lambda', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: event.itemData?.companyId,
      neighborhoodId: event.itemData?.neighborhood?.id,
    });

    return {
      success: false,
      companyId: event.itemData?.companyId || 'unknown',
      neighborhoodId: event.itemData?.neighborhood?.id || 'unknown',
      neighborhoodName: event.itemData?.neighborhood?.name || 'unknown',
      error: error instanceof Error ? error.message : 'Unknown error',
      responseId: null,
    };
  }
};
