import { Handler, Context } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';
import { ApiGatewayClient } from 'src/clients/ApiGatewayClient';
import { getConfig } from 'src/config';
import { EntitlementDTO, EnrichedEntitlement } from 'src/types/entitlement';
import { createContextLogger, ContextLogger } from 'src/utils/contextLogger';

const DEFAULT_NEIGHBORHOODS_LIMIT = 100;

export type EnrichEntitlementsInput = {
  entitlement: EntitlementDTO;
  executionName?: string;
  executionStartTime?: string;
  dryRun?: boolean;
};

export const handler: Handler<
  EnrichEntitlementsInput,
  EnrichedEntitlement | null
> = async (input, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'enrich-entitlements',
    context,
  );

  logger.info('EnrichEntitlements Lambda invoked', {
    input: typeof input,
    hasEntitlement:
      input && typeof input === 'object' && 'entitlement' in input,
    companyId: input?.entitlement?.companyId,
  });

  if (!input || typeof input !== 'object' || !('entitlement' in input)) {
    logger.error('Invalid input: expected an object with entitlement');
    throw new Error('Invalid input: expected an object with entitlement');
  }

  const { entitlement } = input;

  if (!entitlement || typeof entitlement !== 'object') {
    logger.error('Invalid input: expected a valid EntitlementDTO');
    throw new Error('Invalid input: expected a valid EntitlementDTO');
  }

  const graphqlClient = new GraphQLClient(`${config.API_GATEWAY_URL}/graphql`);
  const apiClient = new ApiGatewayClient(
    config.API_GATEWAY_SUPER_USER_COMPANY_ID,
    config.API_GATEWAY_KEY,
    graphqlClient,
    logger.createComponentLogger({
      clientName: 'ApiGatewayClient',
      layer: 'api',
    }),
  );

  const limit = DEFAULT_NEIGHBORHOODS_LIMIT;

  try {
    logger.debug('Fetching neighborhoods for entitlement', {
      companyId: entitlement.companyId,
      displayId: entitlement.displayId,
    });

    const neighborhoods = await apiClient.getNeighborhoods(
      entitlement.companyId,
      limit,
      [],
    );

    const withPlaceData = neighborhoods
      .filter(n => n.googlePlaceData && n.googlePlaceData['formatted_address'])
      // Format the googlePlaceData reduce it's size by omitting the unnecessary fields
      .map(n => ({
        ...n,
        googlePlaceData: {
          formattedAddress: n.googlePlaceData!['formatted_address'],
          addressComponents: n.googlePlaceData!['address_components'],
        },
      }));

    if (withPlaceData.length === 0) {
      logger.warn(
        'Entitlement not suitable for topic generation - no valid neighborhoods',
        {
          companyId: entitlement.companyId,
          displayId: entitlement.displayId,
          totalNeighborhoods: neighborhoods.length,
          validNeighborhoods: 0,
        },
      );
      return null; // Filter out unsuitable entitlements
    }

    const enriched: EnrichedEntitlement = {
      ...entitlement,
      neighborhoods: withPlaceData,
    };

    logger.info('Enriched entitlement with neighborhoods', {
      companyId: entitlement.companyId,
      displayId: entitlement.displayId,
      neighborhoodsCount: withPlaceData.length,
    });

    return enriched;
  } catch (error) {
    logger.error('Failed to enrich entitlement', {
      error: error instanceof Error ? error.message : String(error),
      companyId: entitlement.companyId,
      displayId: entitlement.displayId,
    });
    // Return null for failed entitlements
    logger.warn(
      'Entitlement not suitable for topic generation - failed to fetch neighborhoods',
      {
        companyId: entitlement.companyId,
        displayId: entitlement.displayId,
        error: error instanceof Error ? error.message : String(error),
      },
    );
    return null; // Filter out failed entitlements
  }
};
