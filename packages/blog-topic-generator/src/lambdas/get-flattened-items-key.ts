import { PassThrough } from 'stream';

import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { <PERSON><PERSON> } from 'aws-lambda';

import { getConfig } from '../config';
import { createContextLogger } from '../utils/contextLogger';

export interface GetFlattenedItemsKeyInput {
  bucketName: string;
  key: string;
}

export interface GetFlattenedItemsKeyOutput {
  bucketName: string | null;
  key: string | null;
  totalFlattenedItems: number;
  success: boolean;
  message?: string;
}

interface FlattenResult {
  bucketName: string;
  success: boolean;
  totalFlattenedItems: number;
  key: string;
}

interface SucceededResult {
  executionName: string;
  dryRun: boolean;
  executionStartTime: string;
  flatten: FlattenResult;
  [key: string]: any; // Allow for other properties
}

const config = getConfig();
const s3Client = new S3Client({ region: config.REGION });

export const handler: <PERSON><PERSON><
  GetFlattenedItemsKeyInput,
  GetFlattenedItemsKeyOutput
> = async (event, context) => {
  const logger = createContextLogger('get-flattened-items-key', context, {
    lambdaName: 'get-flattened-items-key',
  });

  try {
    // Validate that the provided key is a non-empty string and points to a manifest
    if (
      typeof event.key !== 'string' ||
      event.key.length === 0 ||
      !event.key.endsWith('/manifest.json')
    ) {
      logger.warn(
        'Invalid or unexpected event.key for SUCCEEDED_0 derivation',
        {
          bucketName: event.bucketName,
          key: event.key,
        },
      );

      return {
        success: true,
        message:
          'Invalid input key: expected a non-empty string ending with /manifest.json',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      };
    }

    const succeededKey = event.key.replace(
      '/manifest.json',
      '/SUCCEEDED_0.json',
    );

    logger.info('Fetching SUCCEEDED_0.json file', {
      bucketName: event.bucketName,
      key: succeededKey,
    });

    // Fetch the SUCCEEDED_0.json file from S3
    const getObjectCommand = new GetObjectCommand({
      Bucket: event.bucketName,
      Key: succeededKey,
    });

    const response = await s3Client.send(getObjectCommand);

    if (!response.Body) {
      logger.warn('SUCCEEDED_0.json file exists but has no content');
      return {
        success: true,
        message: 'No content in SUCCEEDED_0.json file',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      };
    }

    // Parse the JSON content
    const fileContent = await response.Body.transformToString();
    const succeededResults: SucceededResult[] = JSON.parse(fileContent);

    // Check if we have any results
    if (!succeededResults || succeededResults.length === 0) {
      logger.info('No results found in SUCCEEDED_0.json file');
      return {
        success: true,
        message: 'No results found in SUCCEEDED_0.json file',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      };
    }

    // Process each result to collect flattened files
    const flattenedFiles: Array<{
      bucketName: string;
      key: string;
      totalItems: number;
    }> = [];
    let totalFlattenedItemsAcrossAll = 0;

    for (const result of succeededResults) {
      if (result.flatten && result.flatten.totalFlattenedItems > 0) {
        totalFlattenedItemsAcrossAll += result.flatten.totalFlattenedItems;

        flattenedFiles.push({
          bucketName: result.flatten.bucketName,
          key: result.flatten.key,
          totalItems: result.flatten.totalFlattenedItems,
        });

        logger.info('Found flattened items', {
          executionName: result.executionName,
          totalFlattenedItems: result.flatten.totalFlattenedItems,
          bucketName: result.flatten.bucketName,
          key: result.flatten.key,
        });
      }
    }

    // Return results based on whether we found flattened items
    if (totalFlattenedItemsAcrossAll > 0 && flattenedFiles.length > 0) {
      logger.info('Merging flattened files', {
        fileCount: flattenedFiles.length,
        totalFlattenedItems: totalFlattenedItemsAcrossAll,
      });
      // Generate a consolidated file key in the execution folder (one level up from SUCCEEDED_0.json)
      // Extract the execution folder path from the input key
      // e.g., "blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/1b01ba81-337f-474f-8375-cb909d305204/SUCCEEDED_0.json"
      // becomes "blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/"
      const keyParts = succeededKey.split('/');
      const executionFolderPath = keyParts.slice(0, -2).join('/'); // Remove the UUID folder and filename
      const consolidatedKey = `${executionFolderPath}/consolidated-flattened-items.json`;

      logger.info('Generated consolidated file path', {
        originalKey: succeededKey,
        executionFolderPath,
        consolidatedKey,
        uuidFolder: keyParts[keyParts.length - 2], // Log the UUID folder for reference
      });

      // Define conservative in-memory thresholds; above these, stream upload
      const SMALL_ITEM_THRESHOLD = 150; // items
      const SMALL_ESTIMATED_BYTES = 2 * 1024 * 1024; // ~2MB

      // If small enough, keep existing in-memory behavior for simplicity
      if (totalFlattenedItemsAcrossAll <= SMALL_ITEM_THRESHOLD) {
        // Download and merge all flattened files in-memory
        const mergedFlattenedItems: any[] = [];

        for (const file of flattenedFiles) {
          logger.info('Downloading flattened file', {
            bucketName: file.bucketName,
            key: file.key,
          });

          const getFileCommand = new GetObjectCommand({
            Bucket: file.bucketName,
            Key: file.key,
          });

          const fileResponse = await s3Client.send(getFileCommand);

          if (fileResponse.Body) {
            const fileContent = await fileResponse.Body.transformToString();
            const flattenedItems = JSON.parse(fileContent);
            const itemsArray = Array.isArray(flattenedItems)
              ? flattenedItems
              : [flattenedItems];
            mergedFlattenedItems.push(...itemsArray);

            logger.info('Merged flattened file', {
              key: file.key,
              itemsCount: itemsArray.length,
            });
          }
        }

        logger.info('Uploading consolidated flattened items (in-memory)', {
          bucketName: event.bucketName,
          key: consolidatedKey,
          totalItems: mergedFlattenedItems.length,
        });

        const putCommand = new PutObjectCommand({
          Bucket: event.bucketName,
          Key: consolidatedKey,
          Body: JSON.stringify(mergedFlattenedItems),
          ContentType: 'application/json',
        });

        await s3Client.send(putCommand);
      } else {
        // Stream upload JSON array to S3 without materializing entire array in memory
        logger.info('Uploading consolidated flattened items (streaming)', {
          bucketName: event.bucketName,
          key: consolidatedKey,
          totalItems: totalFlattenedItemsAcrossAll,
        });

        const pass = new PassThrough();
        const put = new PutObjectCommand({
          Bucket: event.bucketName,
          Key: consolidatedKey,
          Body: pass,
          ContentType: 'application/json',
        });
        // Start upload and then stream content into the PassThrough
        const uploadPromise = s3Client.send(put);

        // Write a JSON array incrementally: [item1,item2,...]
        pass.write('[');
        let isFirst = true;
        let bytesWritten = 1; // for initial '['

        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { chain } = require('stream-chain');
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { parser } = require('stream-json');
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const { streamArray } = require('stream-json/streamers/StreamArray');

        for (const file of flattenedFiles) {
          const getFileCommand = new GetObjectCommand({
            Bucket: file.bucketName,
            Key: file.key,
          });
          const fileResponse = await s3Client.send(getFileCommand);
          if (!fileResponse.Body) continue;

          const jsonStream = chain([
            fileResponse.Body,
            parser(),
            streamArray(),
          ]);

          for await (const chunk of jsonStream) {
            const value = chunk?.value;
            const serialized = JSON.stringify(value);
            const piece = (isFirst ? '' : ',') + serialized;
            pass.write(piece);
            isFirst = false;
            bytesWritten += piece.length;

            if (
              bytesWritten > SMALL_ESTIMATED_BYTES &&
              totalFlattenedItemsAcrossAll > SMALL_ITEM_THRESHOLD
            ) {
              logger.info('Streaming large consolidated payload', {
                bytesWritten,
                totalItems: totalFlattenedItemsAcrossAll,
              });
            }
          }
        }

        pass.write(']');
        pass.end();
        await uploadPromise;
      }

      logger.info('Successfully created consolidated flattened items', {
        totalFlattenedItems: totalFlattenedItemsAcrossAll,
        bucketName: event.bucketName,
        key: consolidatedKey,
      });

      return {
        success: true,
        bucketName: event.bucketName,
        key: consolidatedKey,
        totalFlattenedItems: totalFlattenedItemsAcrossAll,
      };
    } else {
      logger.info('No flattened items found - gracefully exiting', {
        totalFlattenedItems: totalFlattenedItemsAcrossAll,
      });

      return {
        success: true,
        message: 'No flattened items found',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      };
    }
  } catch (error) {
    if (error instanceof Error && error.name === 'NoSuchKey') {
      logger.info('SUCCEEDED_0.json file not found - gracefully exiting', {
        error: error.message,
      });

      return {
        success: true,
        message: 'SUCCEEDED_0.json file not found',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      };
    }

    logger.error('Error fetching flattened items key', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    throw error;
  }
};
