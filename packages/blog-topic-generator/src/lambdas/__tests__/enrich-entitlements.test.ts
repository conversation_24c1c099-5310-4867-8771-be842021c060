import { Context } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';

import { Neighborhood } from '../../clients/types/neighborhood';
import { EntitlementDTO } from '../../types/entitlement';
import { handler } from '../enrich-entitlements';

// Mock GraphQL client
jest.mock('graphql-request');
const mockGraphQLClient = GraphQLClient as jest.MockedClass<
  typeof GraphQLClient
>;

// Mock config
jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    API_GATEWAY_URL: 'https://test-api-gateway.com',
    API_GATEWAY_SUPER_USER_COMPANY_ID: 'super-user-id',
    API_GATEWAY_KEY: 'test-api-key',
    LOG_LEVEL: 'INFO',
    REGION: 'us-east-1',
    ENVIRONMENT: 'test',
    API_GATEWAY_CONCURRENCY_LIMIT: 10,
  })),
}));

// Mock ContextLogger
const mockContextLogger = {
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  createChild: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
  createComponentLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
};

jest.mock('../../utils/contextLogger', () => ({
  createContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
  ContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
}));

describe('enrich-entitlements Lambda', () => {
  let mockContext: Context;
  let mockGraphQLRequest: jest.Mock;

  const mockEntitlement: EntitlementDTO = {
    id: 'ent-1',
    displayId: 'ent-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    productId: 'test-product-id',
    companyId: 'company-1',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    units: 10,
    salesforceServiceId: 'sf-1',
    product: {
      id: 'test-product-id',
      name: 'Test Product',
    },
    company: {
      website: 'https://test1.com',
      name: 'Test Company 1',
    },
    entitled: true,
  };

  const mockNeighborhoodsWithPlaceData: Neighborhood[] = [
    {
      id: 'neigh-1',
      name: 'Downtown',
      companyId: 'company-1',
      googlePlaceData: {
        formatted_address: '123 Main St, Downtown, CA 90210',
        address_components: [
          { long_name: '123', short_name: '123', types: ['street_number'] },
          { long_name: 'Main St', short_name: 'Main St', types: ['route'] },
        ],
        place_id: 'ChIJ123456789',
        geometry: { location: { lat: 34.0522, lng: -118.2437 } },
      },
    },
    {
      id: 'neigh-2',
      name: 'Uptown',
      companyId: 'company-1',
      googlePlaceData: {
        formatted_address: '456 Oak Ave, Uptown, CA 90211',
        address_components: [
          { long_name: '456', short_name: '456', types: ['street_number'] },
          { long_name: 'Oak Ave', short_name: 'Oak Ave', types: ['route'] },
        ],
        place_id: 'ChIJ987654321',
        geometry: { location: { lat: 34.0622, lng: -118.2537 } },
      },
    },
  ];

  const mockNeighborhoodsWithoutPlaceData: Neighborhood[] = [
    {
      id: 'neigh-3',
      name: 'Suburb',
      companyId: 'company-2',
      googlePlaceData: null,
    },
    {
      id: 'neigh-4',
      name: 'Rural',
      companyId: 'company-2',
      googlePlaceData: {},
    },
  ];

  beforeEach(() => {
    mockContext = {
      awsRequestId: 'test-request-id',
      functionName: 'test-function',
    } as Context;

    mockGraphQLRequest = jest.fn();
    mockGraphQLClient.prototype.request = mockGraphQLRequest;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('successful enrichment', () => {
    it('should enrich single entitlement with valid neighborhoods', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoodsWithPlaceData,
      });
      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toEqual({
        ...mockEntitlement,
        neighborhoods: [
          {
            id: 'neigh-1',
            name: 'Downtown',
            companyId: 'company-1',
            googlePlaceData: {
              formattedAddress: '123 Main St, Downtown, CA 90210',
              addressComponents: [
                {
                  long_name: '123',
                  short_name: '123',
                  types: ['street_number'],
                },
                {
                  long_name: 'Main St',
                  short_name: 'Main St',
                  types: ['route'],
                },
              ],
            },
          },
          {
            id: 'neigh-2',
            name: 'Uptown',
            companyId: 'company-1',
            googlePlaceData: {
              formattedAddress: '456 Oak Ave, Uptown, CA 90211',
              addressComponents: [
                {
                  long_name: '456',
                  short_name: '456',
                  types: ['street_number'],
                },
                {
                  long_name: 'Oak Ave',
                  short_name: 'Oak Ave',
                  types: ['route'],
                },
              ],
            },
          },
        ],
      });

      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'EnrichEntitlements Lambda invoked',
        {
          input: 'object',
          hasEntitlement: true,
          companyId: 'company-1',
        },
      );
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'Enriched entitlement with neighborhoods',
        {
          companyId: 'company-1',
          displayId: 'ent-1',
          neighborhoodsCount: 2,
        },
      );
    });
  });

  describe('filtering invalid neighborhoods', () => {
    it('should return null for entitlements with no valid neighborhoods', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoodsWithoutPlaceData,
      });
      const callback = jest.fn();
      const input = {
        entitlement: {
          ...mockEntitlement,
          companyId: 'company-2',
          displayId: 'ent-2',
        },
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - no valid neighborhoods',
        {
          companyId: 'company-2',
          displayId: 'ent-2',
          totalNeighborhoods: 2,
          validNeighborhoods: 0,
        },
      );
    });

    it('should return null for entitlements with empty neighborhoods array', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: [],
      });
      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - no valid neighborhoods',
        {
          companyId: 'company-1',
          displayId: 'ent-1',
          totalNeighborhoods: 0,
          validNeighborhoods: 0,
        },
      );
    });

    it('should filter mixed valid/invalid neighborhoods and return only valid ones', async () => {
      const mixedNeighborhoods = [
        ...mockNeighborhoodsWithPlaceData,
        ...mockNeighborhoodsWithoutPlaceData,
      ];
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mixedNeighborhoods,
      });
      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).not.toBeNull();
      expect(result!.neighborhoods).toHaveLength(2); // Only valid ones
      expect(result!.neighborhoods[0].id).toBe('neigh-1');
      expect(result!.neighborhoods[1].id).toBe('neigh-2');
    });
  });

  describe('error handling', () => {
    it('should handle GraphQL errors and return null', async () => {
      const graphQLError = new Error('GraphQL Error (Code: 404)');
      mockGraphQLRequest.mockRejectedValueOnce(graphQLError);
      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to enrich entitlement',
        {
          error: 'Failed to fetch company neighborhoods status: 500',
          companyId: 'company-1',
          displayId: 'ent-1',
        },
      );
      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - failed to fetch neighborhoods',
        {
          companyId: 'company-1',
          displayId: 'ent-1',
          error: 'Failed to fetch company neighborhoods status: 500',
        },
      );
    });

    it('should handle non-Error objects in catch block', async () => {
      mockGraphQLRequest.mockRejectedValueOnce('String error');

      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Failed to enrich entitlement',
        {
          error: 'Failed to fetch company neighborhoods status: 500',
          companyId: 'company-1',
          displayId: 'ent-1',
        },
      );
    });
  });

  describe('input validation', () => {
    it('should throw error for invalid input structure', async () => {
      const invalidInput = { invalid: 'input' } as any;
      const callback = jest.fn();
      await expect(
        handler(invalidInput, mockContext, callback),
      ).rejects.toThrow('Invalid input: expected an object with entitlement');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Invalid input: expected an object with entitlement',
      );
    });

    it('should throw error for missing entitlement field', async () => {
      const invalidInput = { executionName: 'test' } as any;
      const callback = jest.fn();
      await expect(
        handler(invalidInput, mockContext, callback),
      ).rejects.toThrow('Invalid input: expected an object with entitlement');

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Invalid input: expected an object with entitlement',
      );
    });

    it('should handle minimal valid input with only entitlement', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoodsWithPlaceData,
      });
      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).not.toBeNull();
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'EnrichEntitlements Lambda invoked',
        {
          input: 'object',
          hasEntitlement: true,
          companyId: 'company-1',
        },
      );
    });
  });

  describe('googlePlaceData formatting', () => {
    it('should format googlePlaceData to reduce size', async () => {
      const neighborhoodsWithFullData: Neighborhood[] = [
        {
          id: 'neigh-1',
          name: 'Downtown',
          companyId: 'company-1',
          googlePlaceData: {
            formatted_address: '123 Main St, Downtown, CA 90210',
            address_components: [
              { long_name: '123', short_name: '123', types: ['street_number'] },
            ],
            place_id: 'ChIJ123456789',
            geometry: { location: { lat: 34.0522, lng: -118.2437 } },
            types: ['establishment'],
            business_status: 'OPERATIONAL',
            // ... many other fields
          },
        },
      ];

      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: neighborhoodsWithFullData,
      });

      const callback = jest.fn();
      const input = {
        entitlement: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result!.neighborhoods[0].googlePlaceData).toEqual({
        formattedAddress: '123 Main St, Downtown, CA 90210',
        addressComponents: [
          { long_name: '123', short_name: '123', types: ['street_number'] },
        ],
      });
      // Verify other fields are not included
      expect(result!.neighborhoods[0].googlePlaceData).not.toHaveProperty(
        'place_id',
      );
      expect(result!.neighborhoods[0].googlePlaceData).not.toHaveProperty(
        'geometry',
      );
      expect(result!.neighborhoods[0].googlePlaceData).not.toHaveProperty(
        'types',
      );
    });
  });
});
