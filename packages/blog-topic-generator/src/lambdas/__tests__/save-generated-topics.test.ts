import { event as saveGeneratedTopicsEvent } from '../../testEvents/saveGeneratedTopicsEvent';
import { handler } from '../save-generated-topics';

// Mock config
jest.mock('src/config', () => ({
  getConfig: jest.fn(() => ({
    ENVIRONMENT: 'test',
    REGION: 'us-east-1',
    COSMO_GQL_URL: 'https://api.example.com/graphql',
    M2M_SUPER_API_KEY: 'test-api-key',
  })),
}));

// Mock logger
const mockLogger: any = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createChild: jest.fn((): any => mockLogger),
  createComponentLogger: jest.fn((): any => mockLogger),
};
jest.mock('src/utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

// Mock CosmoClient with shared mock to allow per-test behavior
const mockCreateBlogTopicsBulk = jest.fn();
jest.mock('src/clients/CosmoClient', () => ({
  CosmoClient: jest.fn().mockImplementation(() => ({
    createBlogTopicsBulk: (...args: any[]) => mockCreateBlogTopicsBulk(...args),
  })),
}));

describe('save-generated-topics', () => {
  const mockContext: any = {
    awsRequestId: 'test-request-id',
    functionName: 'save-generated-topics',
    functionVersion: '$LATEST',
    invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test',
    memoryLimitInMB: '128',
    logGroupName: '/aws/lambda/test',
    logStreamName: '2024/01/01/[$LATEST]abcdef123456',
    getRemainingTimeInMillis: jest.fn(() => 60000),
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Default successful behavior unless a test overrides it
    mockCreateBlogTopicsBulk.mockReset();
    mockCreateBlogTopicsBulk.mockResolvedValue({
      createdBlogTopics: Array(5).fill({ id: 'test-id' }),
    });
  });

  it('should save generated topics successfully', async () => {
    const result = (await handler(
      saveGeneratedTopicsEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.companyId).toBe('company-1');
    expect(result.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');
    expect(result.neighborhoodName).toBe('Pebble Beach');
    expect(result.topicsSaved).toBe(5);
    expect(result.error).toBeUndefined();
  });

  it('should handle input with no topics to save', async () => {
    const inputWithNoTopics = {
      success: true,
      companyId: 'company-1',
      neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
      neighborhoodName: 'Pebble Beach',
      generatedTopics: [],
      totalTopics: 0,
    };

    const result = (await handler(
      inputWithNoTopics,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.topicsSaved).toBe(0);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'No topics to save',
      expect.objectContaining({
        companyId: 'company-1',
        neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
        success: true,
        topicsCount: 0,
      }),
    );
  });

  it('should handle unsuccessful input', async () => {
    const unsuccessfulInput = {
      success: false,
      companyId: 'company-1',
      neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
      neighborhoodName: 'Pebble Beach',
      error: 'Generation failed',
    };

    const result = (await handler(
      unsuccessfulInput,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.topicsSaved).toBe(0);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'No topics to save',
      expect.objectContaining({
        success: false,
        topicsCount: 0,
      }),
    );
  });

  it('should handle invalid input', async () => {
    await expect(handler(null as any, mockContext, jest.fn())).rejects.toThrow(
      'Invalid input: SaveGeneratedTopicsInput is required',
    );
  });

  it('should handle errors during saving', async () => {
    // For this test, make the client throw
    mockCreateBlogTopicsBulk.mockRejectedValueOnce(
      new Error('Simulated error'),
    );

    const result = (await handler(
      saveGeneratedTopicsEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.error).toBe('Simulated error');
    expect(result.topicsSaved).toBe(0);
  });

  it('should log topics information', async () => {
    // Clear previous mock calls
    mockLogger.info.mockClear();

    await handler(saveGeneratedTopicsEvent, mockContext, jest.fn());

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Topics saved successfully',
      expect.objectContaining({
        companyId: 'company-1',
        neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
        topicsSaved: 5,
      }),
    );
  });

  it('should pass type field correctly to CosmoClient', async () => {
    // Reset mocks
    mockCreateBlogTopicsBulk.mockClear();

    // Mock successful response
    mockCreateBlogTopicsBulk.mockResolvedValue({
      createdBlogTopics: [
        { id: 'topic-1' },
        { id: 'topic-2' },
        { id: 'topic-3' },
        { id: 'topic-4' },
        { id: 'topic-5' },
      ],
    });

    const result = await handler(
      saveGeneratedTopicsEvent,
      mockContext,
      jest.fn(),
    );

    // Verify the handler passes type field correctly
    expect(mockCreateBlogTopicsBulk).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          type: 'article', // First topic should have type 'article'
        }),
        expect.objectContaining({
          type: 'listicle', // Second topic should have type 'listicle'
        }),
      ]),
    );

    // Verify success response
    expect(result!.success).toBe(true);
    expect(result!.topicsSaved).toBe(5);
  });

  it('should rewrite topic IDs to match event-level IDs when topics have mismatched IDs', async () => {
    // Reset mocks
    mockCreateBlogTopicsBulk.mockClear();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();

    // Create modified event with mismatched topic IDs
    const saveGeneratedTopicsEventModified = {
      ...saveGeneratedTopicsEvent,
      generatedTopics: saveGeneratedTopicsEvent.generatedTopics!.map(
        (topic, index) => ({
          ...topic,
          // Create mismatched IDs - different from event-level IDs
          companyId: `mismatched-company-${index}`,
          neighborhoodId: `mismatched-neighborhood-${index}`,
        }),
      ),
    };

    // Mock successful response
    mockCreateBlogTopicsBulk.mockResolvedValue({
      createdBlogTopics: [
        { id: 'topic-1' },
        { id: 'topic-2' },
        { id: 'topic-3' },
        { id: 'topic-4' },
        { id: 'topic-5' },
      ],
    });

    const result = await handler(
      saveGeneratedTopicsEventModified,
      mockContext,
      jest.fn(),
    );

    // Verify the handler rewrites topic IDs to match event-level IDs
    expect(mockCreateBlogTopicsBulk).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({
          companyId: 'company-1', // Event-level companyId, not topic-level
          neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63', // Event-level neighborhoodId, not topic-level
        }),
      ]),
    );

    // Verify all topics use event-level IDs
    const calledTopics = mockCreateBlogTopicsBulk.mock.calls[0][0];
    calledTopics.forEach((topic: any) => {
      expect(topic.companyId).toBe('company-1');
      expect(topic.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');
    });

    // Verify success response
    expect(result!.success).toBe(true);
    expect(result!.topicsSaved).toBe(5);
    expect(result!.companyId).toBe('company-1');
    expect(result!.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');

    // Verify logging shows event-level IDs
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Topics saved successfully',
      expect.objectContaining({
        companyId: 'company-1',
        neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
        topicsSaved: 5,
      }),
    );
  });
});
