import { handler } from '../initialize-execution-input';

describe('initialize-execution-input Lambda', () => {
  it('defaults when input is empty', async () => {
    const result = await handler({}, {} as any, () => {});
    expect(result).toEqual({ dryRun: true, companyIds: [] });
  });

  it('preserves provided dryRun=false and companyIds', async () => {
    const result = await handler(
      { dryRun: false, companyIds: ['a'] } as any,
      {} as any,
      () => {},
    );
    expect(result).toEqual({ dryRun: false, companyIds: ['a'] });
  });

  it('coerces non-array companyIds to []', async () => {
    const result = await handler(
      { companyIds: null as unknown as string[] } as any,
      {} as any,
      () => {},
    );
    expect(result).toEqual({ dryRun: true, companyIds: [] });
  });

  it('passes through additional fields', async () => {
    const result = await handler(
      { some: 'x', dryRun: false } as any,
      {} as any,
      () => {},
    );
    expect(result).toEqual({ dryRun: false, companyIds: [], some: 'x' });
  });
});
