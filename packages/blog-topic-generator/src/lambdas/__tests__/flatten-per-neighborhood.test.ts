import { event as flattenPerNeighborhoodEvent } from '../../testEvents/flattenPerNeighborhoodEvent';
import { handler } from '../flatten-per-neighborhood';

// Mock AWS SDK
const mockS3Send = jest.fn();
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: mockS3Send,
  })),
  PutObjectCommand: jest.fn(),
}));

// Mock config
jest.mock('src/config', () => ({
  getConfig: jest.fn(() => ({
    ENVIRONMENT: 'test',
    REGION: 'us-east-1',
  })),
}));

// Mock context logger
jest.mock('src/utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('flatten-per-neighborhood', () => {
  const mockContext = {
    functionName: 'test-function',
    functionVersion: '1',
    invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/test',
    logStreamName: '2024/01/01/[$LATEST]test',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: true,
    dryRun: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset S3 mock to default successful behavior
    mockS3Send.mockResolvedValue({});
  });

  it('should flatten entitlements per neighborhood successfully', async () => {
    const result = (await handler(
      flattenPerNeighborhoodEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.totalFlattenedItems).toBe(2); // 1 entitlement with 2 neighborhoods
    expect(result.bucketName).toBe('blog-topic-generator-test');
    expect(result.key).toBe(
      'blog-topic-jobs/2025-09-17/test-execution-12345-dry-run/flattened-entitlements-a7b4401f-a8be-440d-922f-7b133d4f2197-dry-run.json',
    );
    expect(result.timestamp).toBeDefined();
  });

  it('should handle entitlement with no neighborhoods', async () => {
    const result = (await handler(
      {
        entitlement: {
          ...flattenPerNeighborhoodEvent.entitlement,
          neighborhoods: [],
        },
        executionName: 'test-execution-12345',
        executionStartTime: '2025-09-17T14:00:00.000Z',
        dryRun: true,
      },
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.totalFlattenedItems).toBe(0);
    expect(result.bucketName).toBeNull();
    expect(result.key).toBeNull();
  });

  it('should handle null event gracefully', async () => {
    const result = (await handler(null, mockContext, jest.fn())) as any;

    expect(result.success).toBe(true);
    expect(result.totalFlattenedItems).toBe(0);
    expect(result.bucketName).toBeNull();
    expect(result.key).toBeNull();
  });

  it('should handle null entitlement gracefully', async () => {
    const result = (await handler(
      {
        entitlement: null as any,
        executionName: 'test-execution-12345',
        executionStartTime: '2025-09-17T14:00:00.000Z',
        dryRun: true,
      },
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.totalFlattenedItems).toBe(0);
    expect(result.bucketName).toBeNull();
    expect(result.key).toBeNull();
  });

  it('should handle invalid input (non-object) gracefully', async () => {
    const result = (await handler(
      'invalid-string' as any,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.totalFlattenedItems).toBe(0);
    expect(result.bucketName).toBeNull();
    expect(result.key).toBeNull();
  });

  it('should handle S3 upload errors', async () => {
    // Override the default mock behavior for this test
    mockS3Send.mockRejectedValue(new Error('S3 upload failed'));

    const result = (await handler(
      flattenPerNeighborhoodEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.error).toBe('S3 upload failed');
    expect(result.totalFlattenedItems).toBe(0);
    expect(result.bucketName).toBeNull();
    expect(result.key).toBeNull();
  });

  it('should create correct flattened structure', async () => {
    const { PutObjectCommand } = await import('@aws-sdk/client-s3');

    await handler(flattenPerNeighborhoodEvent, mockContext, jest.fn());

    expect(PutObjectCommand).toHaveBeenCalledWith(
      expect.objectContaining({
        Bucket: 'blog-topic-generator-test',
        Key: 'blog-topic-jobs/2025-09-17/test-execution-12345-dry-run/flattened-entitlements-a7b4401f-a8be-440d-922f-7b133d4f2197-dry-run.json',
        Body: expect.stringContaining('"neighborhood"'),
        ContentType: 'application/json',
      }),
    );

    // Verify the body contains flattened data
    const putObjectCall = (PutObjectCommand as unknown as jest.Mock).mock
      .calls[0][0];
    const flattenedData = JSON.parse(putObjectCall.Body);

    expect(flattenedData).toHaveLength(2);
    expect(flattenedData[0]).toHaveProperty('companyId');
    expect(flattenedData[0]).toHaveProperty('brandProfile');
    expect(flattenedData[0]).toHaveProperty('keywordMap');
    expect(flattenedData[0]).toHaveProperty('neighborhood');
    expect(flattenedData[0].neighborhood.id).toBe(
      '2ef64521-902b-497f-b311-13eeb9e6ad63',
    );
    expect(flattenedData[0].neighborhood.name).toBe('Pebble Beach');
  });

  it('should use execution metadata for S3 key when provided', async () => {
    const contextWithExecution = {
      ...mockContext,
      executionName: 'my-execution',
      executionStartTime: '2024-01-15T10:30:00Z',
      dryRun: false,
    };

    await handler(
      {
        ...flattenPerNeighborhoodEvent,
        executionName: 'my-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      },
      contextWithExecution,
      jest.fn(),
    );

    const { PutObjectCommand } = await import('@aws-sdk/client-s3');
    expect(PutObjectCommand).toHaveBeenCalledWith(
      expect.objectContaining({
        Bucket: 'blog-topic-generator-test',
        Key: 'blog-topic-jobs/2024-01-15/my-execution/flattened-entitlements-a7b4401f-a8be-440d-922f-7b133d4f2197.json',
      }),
    );
  });

  describe('execution metadata validation', () => {
    it('should handle missing executionName gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        executionName: undefined as any,
      };

      const result = (await handler(
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle empty executionName gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        executionName: '',
      };

      const result = (await handler(
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle missing executionStartTime gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        executionStartTime: undefined as any,
      };

      const result = (await handler(
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle empty executionStartTime gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        executionStartTime: '',
      };

      const result = (await handler(
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle missing dryRun gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        dryRun: undefined as any,
      };

      const result = (await handler(
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle undefined entitlement gracefully', async () => {
      const eventWithUndefinedEntitlement = {
        ...flattenPerNeighborhoodEvent,
        entitlement: undefined as any,
      };

      const result = (await handler(
        eventWithUndefinedEntitlement,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
      expect(result.bucketName).toBeNull();
      expect(result.key).toBeNull();
    });

    it('should handle invalid entitlement type gracefully', async () => {
      const invalidEvent = {
        ...flattenPerNeighborhoodEvent,
        entitlement: 'not-an-object',
      };

      const result = (await handler(
        // @ts-expect-error - invalid event
        invalidEvent,
        mockContext,
        jest.fn(),
      )) as any;

      expect(result.success).toBe(true);
      expect(result.totalFlattenedItems).toBe(0);
    });
  });
});
