import { createContextLogger } from '../../utils/contextLogger';
import { handler } from '../catch-failure';

// Mock the context logger
jest.mock('../../utils/contextLogger');
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

(createContextLogger as jest.Mock).mockReturnValue(mockLogger);

describe('catch-failure handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockContext = {
    functionName: 'catch-failure',
    functionVersion: '1',
    invokedFunctionArn:
      'arn:aws:lambda:us-east-1:123456789012:function:catch-failure',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/catch-failure',
    logStreamName: '2023/01/01/[$LATEST]test-stream',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: false,
  };

  describe('sanitization logic', () => {
    it('should log sanitized metadata instead of full event with entitlement', () => {
      const event = {
        error: {
          Error: 'TestError',
          Cause: '{"message": "Test cause"}',
        },
        entitlement: {
          id: 'ent-123',
          displayId: 'ENT-123',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          productId: 'prod-123',
          companyId: 'company-123',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          units: 100,
          salesforceServiceId: 'sf-123',
          product: {
            id: 'prod-123',
            name: 'Test Product',
          },
          company: {
            website: 'https://example.com',
            name: 'Test Company',
          },
          entitled: true,
        },
        executionName: 'exec-123',
        executionStartTime: '2023-01-01T00:00:00Z',
        dryRun: false,
      };

      handler(event, mockContext);

      // Verify the sanitized metadata is logged
      expect(mockLogger.info).toHaveBeenCalledWith('Starting catch failure', {
        sanitizedMetadata: {
          eventId: 'exec-123',
          eventType: 'step-function-failure',
          source: 'catch-failure-handler',
          timestamp: '2023-01-01T00:00:00Z',
          companyId: 'company-123',
          stepName: 'EnrichEntitlements',
          hasEntitlement: true,
          hasEnrichedWithNeighborhoods: false,
          hasEnrichedWithBrandProfile: false,
          hasExecutionMetadata: true,
          errorSummary: {
            errorType: 'TestError',
            hasCause: true,
          },
        },
      });

      // Verify the failure log contains sanitized data
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Blog topic generator execution failed',
        {
          eventId: 'exec-123',
          eventType: 'step-function-failure',
          source: 'catch-failure-handler',
          timestamp: '2023-01-01T00:00:00Z',
          companyId: 'company-123',
          stepName: 'EnrichEntitlements',
          hasEntitlement: true,
          hasEnrichedWithNeighborhoods: false,
          hasEnrichedWithBrandProfile: false,
          hasExecutionMetadata: true,
          errorSummary: {
            errorType: 'TestError',
            hasCause: true,
          },
          error: {
            type: 'TestError',
            hasCause: true,
          },
        },
      );
    });

    it('should handle event with enriched neighborhoods data', () => {
      const event = {
        error: {
          Error: 'NeighborhoodError',
          Cause: '{"message": "Neighborhood processing failed"}',
        },
        enrichedWithNeighborhoods: {
          id: 'ent-123',
          displayId: 'ENT-123',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          productId: 'prod-123',
          companyId: 'company-456',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          units: 100,
          salesforceServiceId: 'sf-123',
          product: {
            id: 'prod-123',
            name: 'Test Product',
          },
          company: {
            website: 'https://example.com',
            name: 'Test Company',
          },
          entitled: true,
          neighborhoods: [
            {
              id: 'neighborhood-1',
              name: 'Downtown',
              companyId: 'company-456',
              googlePlaceData: {
                place_id: 'place-123',
                types: ['neighborhood'],
              },
            },
          ],
          brandProfile: {
            id: 'brand-1',
            companyId: 'company-456',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
            aboutTheBrand: 'Sensitive brand information',
            strategicFocus: 'More sensitive data',
            valueProposition: 'Even more sensitive data',
            idealCustomerProfiles: 'PII data',
            missionAndCoreValues: 'Confidential information',
            brandPointOfView: 'Sensitive perspective',
            toneOfVoice: 'Confidential tone',
            ctaText: 'Sensitive CTA',
            authorPersona: 'Sensitive persona data',
          },
        },
      };

      handler(event, mockContext);

      // Verify that sensitive data is not logged
      const loggedData = mockLogger.info.mock.calls[0][1].sanitizedMetadata;
      expect(loggedData.stepName).toBe('GetBrandProfile');
      expect(loggedData.companyId).toBe('company-456');
      expect(loggedData.hasEnrichedWithNeighborhoods).toBe(true);
      expect(loggedData.hasEnrichedWithBrandProfile).toBe(false);

      // Verify no sensitive fields are present in the logged data
      expect(loggedData).not.toHaveProperty('neighborhoods');
      expect(loggedData).not.toHaveProperty('brandProfile');
      expect(loggedData).not.toHaveProperty('aboutTheBrand');
      expect(loggedData).not.toHaveProperty('googlePlaceData');
    });

    it('should handle event with enriched brand profile data', () => {
      const event = {
        error: {
          Error: 'BrandProfileError',
          Cause: '{"message": "Brand profile processing failed"}',
        },
        enrichedWithBrandProfile: {
          id: 'ent-123',
          displayId: 'ENT-123',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          productId: 'prod-123',
          companyId: 'company-789',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          units: 100,
          salesforceServiceId: 'sf-123',
          product: {
            id: 'prod-123',
            name: 'Test Product',
          },
          company: {
            website: 'https://example.com',
            name: 'Test Company',
          },
          entitled: true,
          neighborhoods: [],
          brandProfile: {
            id: 'brand-1',
            companyId: 'company-789',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
            aboutTheBrand: 'Very sensitive brand information',
          },
          keywordMap: {
            keyword1: 'mapped-value-1',
            keyword2: 'mapped-value-2',
            'sensitive-keyword': 'sensitive-mapped-value',
          },
        },
      };

      handler(event, mockContext);

      const loggedData = mockLogger.info.mock.calls[0][1].sanitizedMetadata;
      expect(loggedData.stepName).toBe('GetKeywordMap');
      expect(loggedData.companyId).toBe('company-789');
      expect(loggedData.hasEnrichedWithNeighborhoods).toBe(false);
      expect(loggedData.hasEnrichedWithBrandProfile).toBe(true);

      // Verify no sensitive fields are present
      expect(loggedData).not.toHaveProperty('brandProfile');
      expect(loggedData).not.toHaveProperty('keywordMap');
      expect(loggedData).not.toHaveProperty('aboutTheBrand');
    });

    it('should handle event with minimal data', () => {
      const event = {
        error: {
          Error: 'MinimalError',
          Cause: '{"message": "Minimal cause"}',
        },
      };

      handler(event, mockContext);

      const loggedData = mockLogger.info.mock.calls[0][1].sanitizedMetadata;
      expect(loggedData.companyId).toBe('unknown');
      expect(loggedData.stepName).toBe('Unknown');
      expect(loggedData.hasEntitlement).toBe(false);
      expect(loggedData.hasEnrichedWithNeighborhoods).toBe(false);
      expect(loggedData.hasEnrichedWithBrandProfile).toBe(false);
      expect(loggedData.hasExecutionMetadata).toBe(false);
      expect(loggedData.errorSummary.hasCause).toBe(true);
    });

    it('should handle errors in the handler gracefully', () => {
      const event = {
        error: {
          Error: 'TestError',
          Cause: '{"message": "Test cause"}',
        },
        entitlement: {
          id: 'ent-123',
          displayId: 'ENT-123',
          createdAt: '2023-01-01T00:00:00Z',
          updatedAt: '2023-01-01T00:00:00Z',
          productId: 'prod-123',
          companyId: 'company-123',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          units: 100,
          salesforceServiceId: 'sf-123',
          product: {
            id: 'prod-123',
            name: 'Test Product',
          },
          company: {
            website: 'https://example.com',
            name: 'Test Company',
          },
          entitled: true,
        },
      };

      // Mock the second logger call to throw an error (after the first one succeeds)
      mockLogger.info
        .mockImplementationOnce(() => {}) // First call succeeds
        .mockImplementationOnce(() => {
          throw new Error('Logger error');
        }); // Second call throws

      handler(event, mockContext);

      // Verify error handling
      expect(mockLogger.error).toHaveBeenCalledWith('Error in catch failure', {
        error: 'Logger error',
        errorType: 'Error',
      });
    });
  });
});
