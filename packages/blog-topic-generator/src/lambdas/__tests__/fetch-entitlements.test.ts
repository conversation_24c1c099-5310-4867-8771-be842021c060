/* eslint-disable @typescript-eslint/no-require-imports */
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Context } from 'aws-lambda';

import { EntitlementDTO } from '../../types/entitlement';
import { handler } from '../fetch-entitlements';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');
const mockS3Client = S3Client as jest.MockedClass<typeof S3Client>;
const mockPutObjectCommand = PutObjectCommand as jest.MockedClass<
  typeof PutObjectCommand
>;

// Mock dependencies
jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    AI_BLOG_SPECIALIST_PRODUCT_ID: 'test-product-id',
    TENANT_SERVICE_URL: 'https://test-tenant-service.com',
    ENTITLEMENTS_LIMIT: 100,
    ENTITLEMENTS_CHUNK_SIZE: 10,
    REGION: 'us-east-1',
    ENVIRONMENT: 'test',
  })),
}));

jest.mock('../../clients/TenantClient');
jest.mock('../../services/EntitlementService');
jest.mock('../../services/EntitlementValidationService');

// Mock ContextLogger
const mockContextLogger = {
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  createChild: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
  createComponentLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
};

jest.mock('../../utils/contextLogger', () => ({
  createContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
  ContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
}));

describe('fetch-entitlements Lambda', () => {
  let mockContext: Context;
  let mockTenantClient: any;
  let mockEntitlementService: any;
  let mockValidationService: any;
  let mockS3Send: jest.Mock;

  const mockEntitlements: EntitlementDTO[] = [
    {
      id: 'ent-1',
      displayId: 'ent-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-1',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-12-31T23:59:59Z',
      units: 10,
      salesforceServiceId: 'sf-1',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test1.com',
        name: 'Test Company 1',
      },
      entitled: true,
    },
    {
      id: 'ent-2',
      displayId: 'ent-2',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-2',
      startDate: '2024-01-01T00:00:00Z',
      endDate: null,
      units: 5,
      salesforceServiceId: 'sf-2',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test2.com',
        name: 'Test Company 2',
      },
      entitled: true,
    },
    {
      id: 'ent-3',
      displayId: 'ent-3',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-3',
      startDate: '2025-01-01T00:00:00Z', // Future start date
      endDate: '2025-12-31T23:59:59Z',
      units: 3,
      salesforceServiceId: 'sf-3',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test3.com',
        name: 'Test Company 3',
      },
      entitled: true,
    },
    {
      id: 'ent-4',
      displayId: 'ent-4',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      productId: 'test-product-id',
      companyId: 'company-4',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2023-12-31T23:59:59Z', // Past end date
      units: 7,
      salesforceServiceId: 'sf-4',
      product: {
        id: 'test-product-id',
        name: 'Test Product',
      },
      company: {
        website: 'https://test4.com',
        name: 'Test Company 4',
      },
      entitled: true,
    },
  ];

  // Valid entitlements that should pass date validation (company-1 and company-2)
  const validEntitlements = mockEntitlements.filter(
    e => e.companyId === 'company-1' || e.companyId === 'company-2',
  );

  beforeEach(() => {
    mockContext = {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:test-function',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: jest.fn(() => 30000),
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    };

    mockS3Send = jest.fn().mockResolvedValue({});
    mockS3Client.prototype.send = mockS3Send;
    mockPutObjectCommand.mockImplementation((params: any) => params);

    // Mock TenantClient
    mockTenantClient = {
      getAllEntitlements: jest.fn().mockResolvedValue(mockEntitlements),
    };

    // Mock EntitlementService
    mockEntitlementService = {
      getEntitlements: jest.fn().mockResolvedValue(mockEntitlements),
      validateStandard: jest.fn().mockImplementation(entitlements => {
        // Filter out entitlements with future start dates or past end dates
        return Promise.resolve(
          entitlements.filter((e: EntitlementDTO) => {
            const now = new Date('2024-01-15T10:30:00Z'); // Test execution time
            const startDate = e.startDate ? new Date(e.startDate) : null;
            const endDate = e.endDate ? new Date(e.endDate) : null;

            // Filter out future start dates
            if (startDate && startDate > now) return false;
            // Filter out past end dates
            if (endDate && endDate < now) return false;

            return true;
          }),
        );
      }),
      chunkEntitlements: jest
        .fn()
        .mockImplementation(entitlements => [entitlements]),
    };

    // Mock EntitlementValidationService
    mockValidationService = {
      validateEntitled: jest.fn().mockReturnValue(validEntitlements),
      validateEntitlementsByFeatureFlags: jest
        .fn()
        .mockResolvedValue(validEntitlements),
    };

    // Mock the constructors
    const { TenantClient } = require('../../clients/TenantClient');
    const { EntitlementService } = require('../../services/EntitlementService');
    const {
      EntitlementValidationService,
    } = require('../../services/EntitlementValidationService');

    TenantClient.mockImplementation(() => mockTenantClient);
    EntitlementService.mockImplementation(() => mockEntitlementService);
    EntitlementValidationService.mockImplementation(
      () => mockValidationService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handler', () => {
    it('should fetch entitlements and write valid ones to S3', async () => {
      const event = {
        productId: 'custom-product-id',
        dryRun: false,
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
      };
      const callback = jest.fn();

      const result = await handler(event, mockContext, callback);

      expect(result?.success).toBe(true);
      expect(result?.totalEntitlements).toBe(4);
      expect(result?.validEntitlements).toBe(1); // Only company-1 is valid (company-3 has future start date)
      expect(result?.bucketName).toBe('blog-topic-generator-test');
      expect(result?.key).toBe(
        'blog-topic-jobs/2024-01-15/test-execution/entitlements.json',
      );

      expect(mockEntitlementService.getEntitlements).toHaveBeenCalled();
      expect(mockEntitlementService.validateStandard).toHaveBeenCalledWith(
        [mockEntitlements[0], mockEntitlements[2]], // Only company-1 and company-3 (filtered by companyIds)
      );
      expect(mockS3Send).toHaveBeenCalledWith(
        expect.objectContaining({
          Bucket: 'blog-topic-generator-test',
          Key: expect.stringMatching(
            'blog-topic-jobs/2024-01-15/test-execution/entitlements.json',
          ),
          Body: JSON.stringify([mockEntitlements[0]]), // Only company-1 is valid, no chunking
        }),
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should use default productId when not provided', async () => {
      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      const result = await handler(event, mockContext, callback);

      expect(result?.success).toBe(true);
      expect(result?.bucketName).toBe('blog-topic-generator-test');
      expect(result?.key).toBe(
        'blog-topic-jobs/2024-01-15/test-execution/entitlements.json',
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle case when no valid entitlements are found', async () => {
      // Mock empty entitlements
      mockEntitlementService.getEntitlements.mockResolvedValue([]);
      mockEntitlementService.validateStandard.mockResolvedValue([]);

      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      const result = await handler(event, mockContext, callback);

      expect(result?.success).toBe(true);
      expect(result?.totalEntitlements).toBe(0);
      expect(result?.validEntitlements).toBe(0);
      expect(result?.bucketName).toBeNull();
      expect(result?.key).toBeNull();

      expect(mockS3Send).not.toHaveBeenCalled();
      expect(callback).not.toHaveBeenCalled();
    });

    it('should validate entitlements by start_date and end_date correctly', async () => {
      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      const result = await handler(event, mockContext, callback);

      // Should filter out entitlements with future start_date and past end_date
      expect(result?.validEntitlements).toBe(1); // Only company-1 is valid (company-3 has future start date)

      // Verify S3 content contains entitlements array (no chunking)
      const s3Call = mockS3Send.mock.calls[0][0];
      const bodyContent = JSON.parse(s3Call.Body);

      expect(bodyContent).toEqual([mockEntitlements[0]]); // Only company-1, no chunking
      expect(bodyContent).toHaveLength(1);
      expect(bodyContent.map((e: any) => e.companyId)).toEqual(['company-1']);

      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle S3 upload errors', async () => {
      mockS3Send.mockRejectedValue(new Error('S3 upload failed'));

      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      await expect(handler(event, mockContext, callback)).rejects.toThrow(
        'S3 upload failed',
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should handle tenant service errors', async () => {
      mockEntitlementService.getEntitlements.mockRejectedValue(
        new Error('Tenant service error'),
      );

      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      await expect(handler(event, mockContext, callback)).rejects.toThrow(
        'Tenant service error',
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should use correct bucket name based on environment', async () => {
      const event = {
        companyIds: ['company-1', 'company-3'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      await handler(event, mockContext, callback);

      const s3Call = mockS3Send.mock.calls[0][0];
      expect(s3Call.Bucket).toBe('blog-topic-generator-test');
      expect(callback).not.toHaveBeenCalled();
    });

    it('should generate semantic s3Key with execution metadata', async () => {
      const event = {
        executionName: 'my-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
        companyIds: ['company-1', 'company-3'],
      };
      const callback = jest.fn();

      await handler(event, mockContext, callback);

      const s3Call = mockS3Send.mock.calls[0][0];
      expect(s3Call.Key).toBe(
        'blog-topic-jobs/2024-01-15/my-execution/entitlements.json',
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should generate semantic s3Key with dry-run suffix', async () => {
      const event = {
        executionName: 'my-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: true,
        companyIds: ['company-1', 'company-3'],
      };
      const callback = jest.fn();

      await handler(event, mockContext, callback);

      const s3Call = mockS3Send.mock.calls[0][0];
      expect(s3Call.Key).toBe(
        'blog-topic-jobs/2024-01-15/my-execution-dry-run/entitlements-dry-run.json',
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should filter entitlements by companyIds when provided', async () => {
      const event = {
        companyIds: ['company-1', 'company-3'], // Only company-1 is valid (company-3 has future start date)
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      await handler(event, mockContext, callback);

      expect(mockEntitlementService.validateStandard).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({ companyId: 'company-1' }),
        ]),
      );
      expect(mockEntitlementService.validateStandard).toHaveBeenCalledWith(
        expect.not.arrayContaining([
          expect.objectContaining({ companyId: 'company-2' }),
          expect.objectContaining({ companyId: 'company-4' }),
        ]),
      );
      expect(callback).not.toHaveBeenCalled();
    });

    it('should process all entitlements when companyIds is empty', async () => {
      const event = {
        companyIds: [],
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
        dryRun: false,
      };
      const callback = jest.fn();

      await handler(event, mockContext, callback);

      expect(mockEntitlementService.validateStandard).toHaveBeenCalledWith(
        mockEntitlements,
      );
      expect(callback).not.toHaveBeenCalled();
    });
  });

  describe('date validation logic', () => {
    it('should filter out entitlements with future start_date', () => {
      const entitlementsWithFutureStart = mockEntitlements.filter(
        e => e.companyId === 'company-3',
      );
      expect(entitlementsWithFutureStart).toHaveLength(1);
      expect(entitlementsWithFutureStart[0].startDate).toBe(
        '2025-01-01T00:00:00Z',
      );
    });

    it('should filter out entitlements with past end_date', () => {
      const entitlementsWithPastEnd = mockEntitlements.filter(
        e => e.companyId === 'company-4',
      );
      expect(entitlementsWithPastEnd).toHaveLength(1);
      expect(entitlementsWithPastEnd[0].endDate).toBe('2023-12-31T23:59:59Z');
    });

    it('should keep entitlements with null end_date', () => {
      const entitlementsWithNullEnd = mockEntitlements.filter(
        e => e.companyId === 'company-2',
      );
      expect(entitlementsWithNullEnd).toHaveLength(1);
      expect(entitlementsWithNullEnd[0].endDate).toBeNull();
    });
  });
});
