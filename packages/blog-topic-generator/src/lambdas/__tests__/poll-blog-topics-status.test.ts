import { PutObjectCommand } from '@aws-sdk/client-s3';
import { BackgroundJobStatus } from 'src/types/prompt';

import { event as pollBlogTopicsStatusEvent } from '../../testEvents/pollBlogTopicsStatusEvent';
import { handler } from '../poll-blog-topics-status';

// Mock AWS SDK
const mockS3Send = jest.fn();
jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn().mockImplementation(() => ({
    send: mockS3Send,
  })),
  PutObjectCommand: jest.fn(),
}));

// Mock PromptService
const mockPromptService = {
  getBackgroundJobStatus: jest.fn(),
};
jest.mock('src/services/PromptService', () => ({
  PromptService: jest.fn().mockImplementation(() => mockPromptService),
}));

// Mock config
jest.mock('src/config', () => ({
  getConfig: jest.fn(() => ({
    ENVIRONMENT: 'test',
    REGION: 'us-east-1',
    OPENAI_REASONING: 'medium',
    OPENAI_VERBOSITY: 'medium',
  })),
}));

// Mock logger
const mockLogger: any = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createChild: jest.fn((): any => mockLogger),
  createComponentLogger: jest.fn((): any => mockLogger),
};
jest.mock('src/utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

describe('poll-blog-topics-status', () => {
  const mockContext: any = {
    awsRequestId: 'test-request-id',
    functionName: 'poll-blog-topics-status',
    functionVersion: '$LATEST',
    invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test',
    memoryLimitInMB: '128',
    logGroupName: '/aws/lambda/test',
    logStreamName: '2024/01/01/[$LATEST]abcdef123456',
    getRemainingTimeInMillis: jest.fn(() => 60000),
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: true,
    dryRun: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockS3Send.mockResolvedValue({});
    jest.mocked(PutObjectCommand).mockImplementation((params: any) => params);
  });

  it('should poll status successfully when job is completed', async () => {
    const mockCompletedResponse = {
      status: BackgroundJobStatus.COMPLETED,
      content: {
        topics: Array.from({ length: 30 }, (_, i) => ({
          target_location: 'Pebble Beach',
          parent_topic: `Parent Topic ${i + 1}`,
          keyword: `keyword-${i + 1}`,
          blog_title: `Blog Title ${i + 1}`,
          page_title: `Page Title ${i + 1}`,
          meta_description: `Meta Description ${i + 1}`,
          rationale: `Rationale ${i + 1}`,
        })),
      },
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockCompletedResponse,
    );

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.status).toBe(BackgroundJobStatus.COMPLETED);
    expect(result.responseId).toBe(
      'resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb',
    );
    expect(result.companyId).toBe('a7b4401f-a8be-440d-922f-7b133d4f2197');
    expect(result.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');
    expect(result.neighborhoodName).toBe('Pebble Beach');
    expect(result.generatedTopics).toHaveLength(30);
    expect(result.totalTopics).toBe(30);
    expect(mockPromptService.getBackgroundJobStatus).toHaveBeenCalledWith(
      'resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb',
    );
  });

  it('should handle pending status', async () => {
    const mockPendingResponse = {
      status: BackgroundJobStatus.IN_PROGRESS,
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockPendingResponse,
    );

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.status).toBe(BackgroundJobStatus.IN_PROGRESS);
    expect(result.responseId).toBe(
      'resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb',
    );
    expect(result.generatedTopics).toEqual([]);
    expect(result.totalTopics).toBe(0);
  });

  it('should handle failed status', async () => {
    const mockFailedResponse = {
      status: BackgroundJobStatus.FAILED,
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockFailedResponse,
    );

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.status).toBe(BackgroundJobStatus.FAILED);
    expect(result.responseId).toBe(
      'resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb',
    );
    expect(result.error).toBe('Background job failed');
  });

  it('should handle invalid input', async () => {
    const invalidInput = { companyId: 'company-1' }; // Missing responseId

    await expect(
      handler(invalidInput as any, mockContext, jest.fn()),
    ).rejects.toThrow('Invalid input: responseId is required');
  });

  it('should handle prompt service errors', async () => {
    mockPromptService.getBackgroundJobStatus.mockRejectedValue(
      new Error('Polling failed'),
    );

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.status).toBe(BackgroundJobStatus.FAILED);
    expect(result.error).toBe('Polling failed');
  });

  it('should handle empty topics response', async () => {
    const mockCompletedResponse = {
      status: BackgroundJobStatus.COMPLETED,
      content: {
        topics: [],
      },
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockCompletedResponse,
    );

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.status).toBe(BackgroundJobStatus.COMPLETED);
    expect(result.generatedTopics).toEqual([]);
    expect(result.totalTopics).toBe(0);
  });

  it('should write OpenAI response to S3 when job is completed', async () => {
    const mockCompletedResponse = {
      status: BackgroundJobStatus.COMPLETED,
      content: {
        topics: Array.from({ length: 5 }, (_, i) => ({
          target_location: 'Pebble Beach',
          parent_topic: `Parent Topic ${i + 1}`,
          keyword: `keyword-${i + 1}`,
          blog_title: `Blog Title ${i + 1}`,
          page_title: `Page Title ${i + 1}`,
          meta_description: `Meta Description ${i + 1}`,
          rationale: `Rationale ${i + 1}`,
        })),
      },
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockCompletedResponse,
    );

    await handler(pollBlogTopicsStatusEvent, mockContext, jest.fn());

    expect(mockS3Send).toHaveBeenCalledWith(
      expect.objectContaining({
        Bucket: 'blog-topic-generator-test',
        Key: 'blog-topic-jobs/2024-01-15/test-execution/openai-response-resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb.json',
        Body: JSON.stringify(mockCompletedResponse.content),
        ContentType: 'application/json',
        Metadata: expect.objectContaining({
          companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
          neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
          environment: 'test',
          responseId: 'resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb',
        }),
      }),
    );
  });

  it('should use execution metadata for S3 key when provided', async () => {
    const mockCompletedResponse = {
      status: BackgroundJobStatus.COMPLETED,
      content: {
        topics: [{ target_location: 'Pebble Beach', blog_title: 'Test' }],
      },
    };

    const contextWithExecution = {
      ...mockContext,
      executionName: 'my-execution',
      executionStartTime: '2024-01-15T10:30:00Z',
      dryRun: false,
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockCompletedResponse,
    );

    await handler(
      {
        ...pollBlogTopicsStatusEvent,
        executionName: 'my-execution',
        executionStartTime: '2024-01-15T10:30:00Z',
      },
      contextWithExecution,
      jest.fn(),
    );

    expect(mockS3Send).toHaveBeenCalledWith(
      expect.objectContaining({
        Key: 'blog-topic-jobs/2024-01-15/my-execution/openai-response-resp_68c1e13cdb18819ba71efc9fe561ce27047aec1b98f7b4fb.json',
      }),
    );
  });

  it('should continue processing when S3 write fails', async () => {
    const mockCompletedResponse = {
      status: BackgroundJobStatus.COMPLETED,
      content: {
        topics: [{ target_location: 'Pebble Beach', blog_title: 'Test' }],
      },
    };

    mockPromptService.getBackgroundJobStatus.mockResolvedValue(
      mockCompletedResponse,
    );
    mockS3Send.mockRejectedValue(new Error('S3 write failed'));

    const result = (await handler(
      pollBlogTopicsStatusEvent,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.status).toBe(BackgroundJobStatus.COMPLETED);
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'Failed to write OpenAI response to S3 (continuing)',
      expect.objectContaining({
        error: 'S3 write failed',
      }),
    );
  });
});
