import { Context } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';

import { BrandProfile } from '../../clients/types/brandProfile';
import { Neighborhood } from '../../clients/types/neighborhood';
import { EnrichedEntitlement } from '../../types/entitlement';
import { handler } from '../get-brand-profile';

// Mock GraphQL client
jest.mock('graphql-request');
const mockGraphQLClient = GraphQLClient as jest.MockedClass<
  typeof GraphQLClient
>;

// Mock config
jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    COSMO_GQL_URL: 'https://test-cosmo-gql.com',
    API_GATEWAY_KEY: 'test-api-key',
    LOG_LEVEL: 'INFO',
    REGION: 'us-east-1',
    ENVIRONMENT: 'test',
    API_GATEWAY_CONCURRENCY_LIMIT: 10,
    M2M_SUPER_API_KEY: 'test-m2m-api-key',
  })),
}));

// Mock ContextLogger
const mockContextLogger = {
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  createChild: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
  createComponentLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
};

jest.mock('../../utils/contextLogger', () => ({
  createContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
  ContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
}));

describe('get-brand-profile Lambda', () => {
  let mockContext: Context;
  let mockGraphQLRequest: jest.Mock;

  const mockNeighborhoods: Neighborhood[] = [
    {
      id: 'neigh-1',
      name: 'Downtown',
      companyId: 'company-1',
      googlePlaceData: {
        formatted_address: '123 Main St, Downtown, CA 90210',
        address_components: [
          { long_name: '123', short_name: '123', types: ['street_number'] },
          { long_name: 'Main St', short_name: 'Main St', types: ['route'] },
        ],
        place_id: 'ChIJ123456789',
        geometry: { location: { lat: 34.0522, lng: -118.2437 } },
      },
    },
  ];

  const mockBrandProfile: BrandProfile = {
    id: 'brand-profile-1',
    companyId: 'company-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    updatedBy: 'user-1',
    aboutTheBrand: 'A great company',
    strategicFocus: 'Innovation and growth',
    valueProposition: 'We deliver value',
    idealCustomerProfiles: 'Small to medium businesses',
    missionAndCoreValues: 'To help businesses succeed',
    brandPointOfView: 'Customer-centric approach',
    toneOfVoice: 'Professional yet friendly',
    ctaText: 'Get started today',
    authorPersona: 'Industry expert',
  };

  const mockEnrichedEntitlement: EnrichedEntitlement = {
    id: 'ent-1',
    displayId: 'ent-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    productId: 'test-product-id',
    companyId: 'company-1',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    units: 10,
    salesforceServiceId: 'sf-1',
    product: {
      id: 'test-product-id',
      name: 'Test Product',
    },
    company: {
      website: 'https://test1.com',
      name: 'Test Company 1',
    },
    entitled: true,
    neighborhoods: mockNeighborhoods,
  };

  const mockInput = {
    enrichedWithNeighborhoods: mockEnrichedEntitlement,
    executionName: 'test-execution',
    executionStartTime: '2024-01-01T00:00:00Z',
    dryRun: true,
  };

  beforeEach(() => {
    mockContext = {
      awsRequestId: 'test-request-id',
      functionName: 'test-function',
    } as Context;

    mockGraphQLRequest = jest.fn();
    mockGraphQLClient.prototype.request = mockGraphQLRequest;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('successful brand profile fetching', () => {
    it('should add brand profile to single entitlement', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        brandProfile: mockBrandProfile,
      });

      const callback = jest.fn();
      const input = {
        enrichedWithNeighborhoods: mockEnrichedEntitlement,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toEqual({
        ...mockEnrichedEntitlement,
        brandProfile: mockBrandProfile,
      });

      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'GetBrandProfile Lambda invoked',
        {
          input: 'object',
          companyId: 'company-1',
        },
      );
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'Added brand profile to entitlement',
        {
          companyId: 'company-1',
          name: 'Test Company 1',
          profileId: 'brand-profile-1',
        },
      );
    });
  });

  describe('handling null brand profiles', () => {
    it('should return null for entitlements with null brand profiles', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        brandProfile: null,
      });

      const callback = jest.fn();
      const result = await handler(mockInput, mockContext, callback);

      expect(result).toBeNull();

      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - no brand profile',
        {
          companyId: 'company-1',
          name: 'Test Company 1',
        },
      );
    });
  });

  describe('error handling', () => {
    it('should return null when brand profile fetch fails', async () => {
      const error = new Error('GraphQL error');
      mockGraphQLRequest.mockRejectedValueOnce(error);

      const callback = jest.fn();
      const result = await handler(mockInput, mockContext, callback);

      expect(result).toBeNull();

      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - failed to fetch brand profile',
        {
          companyId: 'company-1',
          name: 'Test Company 1',
          error: 'GraphQL error',
        },
      );
    });

    it('should handle non-Error objects', async () => {
      mockGraphQLRequest.mockRejectedValueOnce('String error');

      const callback = jest.fn();
      const result = await handler(mockInput, mockContext, callback);

      expect(result).toBeNull();

      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Entitlement not suitable for topic generation - failed to fetch brand profile',
        {
          error: 'String error',
          companyId: 'company-1',
          name: 'Test Company 1',
        },
      );
    });
  });

  describe('input validation', () => {
    it('should throw error for invalid input structure', async () => {
      const invalidInput = { invalid: 'input' } as any;
      const callback = jest.fn();
      await expect(
        handler(invalidInput, mockContext, callback),
      ).rejects.toThrow(
        'Invalid input: expected enrichedWithNeighborhoods entitlement object',
      );

      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Invalid input: expected enrichedWithNeighborhoods entitlement object',
        expect.objectContaining({
          enrichedWithNeighborhoodsType: 'undefined',
        }),
      );
    });

    it('should throw error for missing enrichedWithNeighborhoods field', async () => {
      const invalidInput = { executionName: 'test' } as any;
      const callback = jest.fn();
      await expect(
        handler(invalidInput, mockContext, callback),
      ).rejects.toThrow(
        'Invalid input: expected enrichedWithNeighborhoods entitlement object',
      );
    });
  });

  describe('logging and debugging', () => {
    it('should log debug information for entitlement', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        brandProfile: mockBrandProfile,
      });

      const callback = jest.fn();
      await handler(mockInput, mockContext, callback);

      expect(mockContextLogger.debug).toHaveBeenCalledWith(
        'Fetching brand profile for entitlement',
        {
          companyId: 'company-1',
          name: 'Test Company 1',
        },
      );
    });

    it('should handle entitlements without company names', async () => {
      const entitlementWithoutCompanyName = {
        ...mockEnrichedEntitlement,
        company: { website: 'https://test1.com' }, // No name
      };

      mockGraphQLRequest.mockResolvedValueOnce({
        brandProfile: mockBrandProfile,
      });

      const callback = jest.fn();
      const result = await handler(
        {
          ...mockInput,
          enrichedWithNeighborhoods: entitlementWithoutCompanyName,
        },
        mockContext,
        callback,
      );

      expect(result).not.toBeNull();
      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'Added brand profile to entitlement',
        {
          companyId: 'company-1',
          name: undefined,
          profileId: 'brand-profile-1',
        },
      );
    });
  });
});
