import { BackgroundJobStatus } from 'src/types/prompt';

import { event as generateBlogTopicsBackgroundEvent } from '../../testEvents/generateBlogTopicsBackgroundEvent';
import { handler } from '../generate-blog-topics-background';

// Mock PromptService
const mockPromptService = {
  executeChatReasoningPromptBackground: jest.fn(),
};
jest.mock('src/services/PromptService', () => ({
  PromptService: jest.fn().mockImplementation(() => mockPromptService),
}));

// Mock config
jest.mock('src/config', () => ({
  getConfig: jest.fn(() => ({
    ENVIRONMENT: 'test',
    REGION: 'us-east-1',
    OPENAI_REASONING: 'medium',
    OPENAI_VERBOSITY: 'medium',
  })),
}));

// Mock logger
const mockLogger: any = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  createChild: jest.fn((): any => mockLogger),
  createComponentLogger: jest.fn((): any => mockLogger),
};
jest.mock('src/utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

describe('generate-blog-topics-background', () => {
  const mockContext: any = {
    awsRequestId: 'test-request-id',
    functionName: 'generate-blog-topics-background',
    functionVersion: '$LATEST',
    invokedFunctionArn: 'arn:aws:lambda:us-east-1:123456789012:function:test',
    memoryLimitInMB: '128',
    logGroupName: '/aws/lambda/test',
    logStreamName: '2024/01/01/[$LATEST]abcdef123456',
    getRemainingTimeInMillis: jest.fn(() => 60000),
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mock to default successful behavior
    mockPromptService.executeChatReasoningPromptBackground.mockResolvedValue({
      responseId: 'response-12345',
      status: BackgroundJobStatus.QUEUED,
    });
  });

  it('should initiate background job successfully', async () => {
    const result = (await handler(
      generateBlogTopicsBackgroundEvent as any,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.responseId).toBe('response-12345');
    expect(result.status).toBe(BackgroundJobStatus.QUEUED);
    expect(result.companyId).toBe('a7b4401f-a8be-440d-922f-7b133d4f2197');
    expect(result.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');
    expect(result.neighborhoodName).toBe('Pebble Beach');
  });

  it('should handle prompt service errors', async () => {
    mockPromptService.executeChatReasoningPromptBackground.mockRejectedValue(
      new Error('Prompt service failed'),
    );

    const result = (await handler(
      generateBlogTopicsBackgroundEvent as any,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(false);
    expect(result.error).toBe('Prompt service failed');
    expect(result.companyId).toBe('a7b4401f-a8be-440d-922f-7b133d4f2197');
    expect(result.neighborhoodId).toBe('2ef64521-902b-497f-b311-13eeb9e6ad63');
    expect(result.neighborhoodName).toBe('Pebble Beach');
  });

  it('should handle different status responses', async () => {
    mockPromptService.executeChatReasoningPromptBackground.mockResolvedValue({
      responseId: 'response-67890',
      status: BackgroundJobStatus.IN_PROGRESS,
    });

    const result = (await handler(
      generateBlogTopicsBackgroundEvent as any,
      mockContext,
      jest.fn(),
    )) as any;

    expect(result.success).toBe(true);
    expect(result.responseId).toBe('response-67890');
    expect(result.status).toBe(BackgroundJobStatus.IN_PROGRESS);
  });

  it('should call prompt service with correct parameters', async () => {
    await handler(
      generateBlogTopicsBackgroundEvent as any,
      mockContext,
      jest.fn(),
    );

    expect(
      mockPromptService.executeChatReasoningPromptBackground,
    ).toHaveBeenCalledWith(
      'Blog Topics Engine (One Location)',
      {
        neighborhood_info:
          'Pebble Beach, Pebble Beach, Del Monte Forest, Monterey County, CA, US',
        brand_profile: expect.any(String),
        keyword_map: expect.any(String),
      },
      {
        companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
        neighborhoodId: '2ef64521-902b-497f-b311-13eeb9e6ad63',
        jobId: expect.stringMatching(
          /^blog-topics-a7b4401f-a8be-440d-922f-7b133d4f2197-2ef64521-902b-497f-b311-13eeb9e6ad63-/,
        ),
        generationMetadata: {
          environment: 'test',
          feature: 'blog-topic-generator-background',
        },
      },
    );
  });
});
