import { S3Client } from '@aws-sdk/client-s3';
import { Context } from 'aws-lambda';

import { EnrichedEntitlement } from '../../types/entitlement';
import { handler } from '../get-keyword-map';

// Mock S3 client
jest.mock('@aws-sdk/client-s3');
const mockS3Client = S3Client as jest.MockedClass<typeof S3Client>;
const mockGetObjectCommand = jest.fn();

// Mock config
jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    LOG_LEVEL: 'INFO',
    REGION: 'us-east-1',
    ENVIRONMENT: 'test',
  })),
}));

// Mock ContextLogger
const mockContextLogger = {
  info: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  createChild: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
  createComponentLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  }),
};

jest.mock('../../utils/contextLogger', () => ({
  createContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
  ContextLogger: jest.fn().mockImplementation(() => mockContextLogger),
}));

describe('get-keyword-map Lambda', () => {
  let mockContext: Context;
  let mockS3Send: jest.Mock;

  const mockEntitlement: EnrichedEntitlement = {
    id: 'ent-1',
    displayId: 'ent-1',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    productId: 'test-product-id',
    companyId: 'company-1',
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-12-31T23:59:59Z',
    units: 10,
    salesforceServiceId: 'sf-1',
    product: {
      id: 'test-product-id',
      name: 'Test Product',
    },
    company: {
      website: 'https://test1.com',
      name: 'Test Company 1',
    },
    entitled: true,
    neighborhoods: [
      {
        id: 'neigh-1',
        name: 'Downtown',
        companyId: 'company-1',
        googlePlaceData: {
          formattedAddress: '123 Main St, Downtown, CA 90210',
          addressComponents: [
            { long_name: '123', short_name: '123', types: ['street_number'] },
          ],
        },
      },
    ],
    brandProfile: {
      id: 'brand-1',
      companyId: 'company-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
      updatedBy: 'test',
      aboutTheBrand: 'Test brand',
      strategicFocus: 'Test focus',
      valueProposition: 'Test value',
      idealCustomerProfiles: 'Test customers',
      missionAndCoreValues: 'Test mission',
      brandPointOfView: 'Test POV',
      toneOfVoice: 'Test tone',
      ctaText: 'Test CTA',
      authorPersona: 'Test persona',
    },
  };

  const mockKeywordMap1 = {
    'real estate': 'property',
    home: 'house',
    buy: 'purchase',
  };

  beforeEach(() => {
    mockContext = {
      awsRequestId: 'test-request-id',
      functionName: 'test-function',
    } as Context;

    mockS3Send = jest.fn();

    // Mock the S3Client constructor to return an instance with a mocked send method
    mockS3Client.mockImplementation(
      () =>
        ({
          send: mockS3Send,
        }) as any,
    );

    // Mock GetObjectCommand to return the parameters
    mockGetObjectCommand.mockImplementation((params: any) => params);
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    (require('@aws-sdk/client-s3').GetObjectCommand as jest.Mock) =
      mockGetObjectCommand;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('successful keyword map enrichment', () => {
    it('should enrich single entitlement with keyword map from S3', async () => {
      mockS3Send.mockResolvedValueOnce({
        Body: {
          transformToString: jest
            .fn()
            .mockResolvedValue(JSON.stringify(mockKeywordMap1)),
        },
      });

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toEqual({
        ...mockEntitlement,
        keywordMap: mockKeywordMap1,
      });

      expect(mockContextLogger.info).toHaveBeenCalledWith(
        'GetKeywordMap Lambda invoked',
        {
          input: 'object',
          companyId: 'company-1',
        },
      );
    });

    it('should handle non-object input and throw error', async () => {
      const callback = () => {};

      await expect(handler(null as any, mockContext, callback)).rejects.toThrow(
        'Invalid input: expected an object',
      );
    });

    it('should handle string input and throw error', async () => {
      const callback = () => {};

      await expect(
        handler('invalid-input' as any, mockContext, callback),
      ).rejects.toThrow('Invalid input: expected an object');
    });

    it('should handle invalid enrichedWithBrandProfile field', async () => {
      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: 'not-an-object',
        executionName: 'test-execution',
        dryRun: true,
      };

      await expect(
        handler(input as any, mockContext, callback),
      ).rejects.toThrow(
        'Invalid input: expected enrichedWithBrandProfile entitlement object',
      );
    });
  });

  describe('handling missing keyword map files', () => {
    it('should return null when keyword map file is not found', async () => {
      const noSuchKeyError = new Error('The specified key does not exist.');
      noSuchKeyError.name = 'NoSuchKey';
      mockS3Send.mockRejectedValueOnce(noSuchKeyError);

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();

      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Keyword map file not found for company',
        {
          companyId: 'company-1',
          bucketName: 'lp-bloom-scheduler-execution-test',
          key: 'keyword_map/company-1.json',
        },
      );
    });

    it('should return null for empty keyword map files', async () => {
      mockS3Send.mockResolvedValueOnce({
        Body: {
          transformToString: jest.fn().mockResolvedValue(''),
        },
      });

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.warn).toHaveBeenCalledWith(
        'Empty keyword map file found',
        {
          companyId: 'company-1',
          bucketName: 'lp-bloom-scheduler-execution-test',
          key: 'keyword_map/company-1.json',
        },
      );
    });
  });

  describe('error handling', () => {
    it('should return null when S3 errors occur', async () => {
      const s3Error = new Error('S3 connection error');
      mockS3Send.mockRejectedValueOnce(s3Error);

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Error fetching keyword map from S3',
        expect.objectContaining({
          companyId: 'company-1',
          error: 'S3 connection error',
        }),
      );
    });

    it('should return null for JSON parsing errors', async () => {
      mockS3Send.mockResolvedValueOnce({
        Body: {
          transformToString: jest.fn().mockResolvedValue('invalid-json'),
        },
      });

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      const result = await handler(input, mockContext, callback);

      expect(result).toBeNull();
      expect(mockContextLogger.error).toHaveBeenCalledWith(
        'Error fetching keyword map from S3',
        expect.objectContaining({
          companyId: 'company-1',
          error: expect.stringContaining('Unexpected token'),
        }),
      );
    });
  });

  describe('S3 client configuration', () => {
    it('should use correct bucket name based on environment', async () => {
      mockS3Send.mockResolvedValueOnce({
        Body: {
          transformToString: jest
            .fn()
            .mockResolvedValue(JSON.stringify(mockKeywordMap1)),
        },
      });

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      await handler(input, mockContext, callback);

      // Verify S3 was called with correct parameters
      expect(mockS3Send).toHaveBeenCalledWith(
        expect.objectContaining({
          Bucket: 'lp-bloom-scheduler-execution-test',
          Key: 'keyword_map/company-1.json',
        }),
      );
    });

    it('should use correct S3 region from config', async () => {
      mockS3Send.mockResolvedValueOnce({
        Body: {
          transformToString: jest
            .fn()
            .mockResolvedValue(JSON.stringify(mockKeywordMap1)),
        },
      });

      const callback = () => {};
      const input = {
        enrichedWithBrandProfile: mockEntitlement,
        executionName: 'test-execution',
        dryRun: true,
      };
      await handler(input, mockContext, callback);

      // The S3Client constructor should be called with the region from config
      expect(mockS3Client).toHaveBeenCalledWith({ region: 'us-east-1' });
    });
  });
});
