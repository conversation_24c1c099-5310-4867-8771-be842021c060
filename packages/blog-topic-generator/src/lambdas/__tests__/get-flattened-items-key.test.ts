import { createContextLogger } from '../../utils/contextLogger';
import {
  GetFlattenedItemsKeyOutput,
  handler,
} from '../get-flattened-items-key';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3', () => {
  const mockS3Send = jest.fn();
  return {
    S3Client: jest.fn(() => ({
      send: mockS3Send,
    })),
    GetObjectCommand: jest.fn(),
    PutObjectCommand: jest.fn(),
    mockS3Send, // Export the mock for use in tests
  };
});

// Get the mocked send function
// eslint-disable-next-line @typescript-eslint/no-require-imports
const { mockS3Send } = require('@aws-sdk/client-s3');

// Mock the context logger
jest.mock('../../utils/contextLogger');
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};
(createContextLogger as jest.Mock).mockReturnValue(mockLogger);

// Mock config
jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    ENVIRONMENT: 'test',
    REGION: 'us-east-1',
  })),
}));

// Mocks for stream-json path (used in streaming branch)
jest.mock(
  'stream-chain',
  () => ({
    chain: (stages: any[]) => {
      const body = stages[0];
      function* generator() {
        const items: any[] = (body && body.__items) || [];
        for (const value of items) {
          yield { value };
        }
      }
      return generator();
    },
  }),
  { virtual: true },
);
jest.mock(
  'stream-json',
  () => ({
    parser: () => (input: any) => input,
  }),
  { virtual: true },
);
jest.mock(
  'stream-json/streamers/StreamArray',
  () => ({
    streamArray: () => (input: any) => input,
  }),
  { virtual: true },
);

describe('get-flattened-items-key handler', () => {
  const mockContext = {
    functionName: 'get-flattened-items-key',
    functionVersion: '1',
    invokedFunctionArn:
      'arn:aws:lambda:us-east-1:123456789012:function:get-flattened-items-key',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/get-flattened-items-key',
    logStreamName: '2023/01/01/[$LATEST]test-stream',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: false,
  };

  const mockInput = {
    bucketName: 'blog-topic-generator-staging',
    key: 'blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/1b01ba81-337f-474f-8375-cb909d305204/manifest.json',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockS3Send.mockReset();
  });

  describe('successful consolidation', () => {
    it('should consolidate flattened items from multiple files', async () => {
      const mockSucceededContent = [
        {
          executionName: '5d6c7b47-cff1-43a9-b2d1-0f28117503ed',
          dryRun: true,
          flatten: {
            bucketName: 'blog-topic-generator-staging',
            success: true,
            totalFlattenedItems: 3,
            key: 'blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/flattened-entitlements-company-A.json',
          },
        },
        {
          executionName: '5d6c7b47-cff1-43a9-b2d1-0f28117503ed',
          dryRun: true,
          flatten: {
            bucketName: 'blog-topic-generator-staging',
            success: true,
            totalFlattenedItems: 2,
            key: 'blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/flattened-entitlements-company-B.json',
          },
        },
      ];

      const mockFlattenedItemsA = [
        {
          companyId: 'company-A',
          neighborhoodId: 'n1',
          neighborhoodName: 'Downtown',
        },
        {
          companyId: 'company-A',
          neighborhoodId: 'n2',
          neighborhoodName: 'Uptown',
        },
        {
          companyId: 'company-A',
          neighborhoodId: 'n3',
          neighborhoodName: 'Midtown',
        },
      ];

      const mockFlattenedItemsB = [
        {
          companyId: 'company-B',
          neighborhoodId: 'n4',
          neighborhoodName: 'East Side',
        },
        {
          companyId: 'company-B',
          neighborhoodId: 'n5',
          neighborhoodName: 'West Side',
        },
      ];

      // Mock the S3 calls
      mockS3Send
        .mockResolvedValueOnce({
          // First call: SUCCEEDED_0.json
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockSucceededContent)),
          },
        })
        .mockResolvedValueOnce({
          // Second call: flattened-entitlements-company-A.json
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockFlattenedItemsA)),
          },
        })
        .mockResolvedValueOnce({
          // Third call: flattened-entitlements-company-B.json
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockFlattenedItemsB)),
          },
        })
        .mockResolvedValueOnce({
          // Fourth call: PutObject for consolidated file
        });

      const result = await handler(mockInput, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        bucketName: 'blog-topic-generator-staging',
        key: 'blog-topic-jobs/2025-09-21/5d6c7b47-cff1-43a9-b2d1-0f28117503ed-dry-run/consolidated-flattened-items.json',
        totalFlattenedItems: 5,
      });

      expect(mockS3Send).toHaveBeenCalledTimes(4); // 1 get + 2 gets for files + 1 put
    });
  });

  describe('streaming branch', () => {
    it('should stream consolidated output when total items exceed threshold', async () => {
      // Total = 180 (> 150 threshold)
      const mockSucceededContent = [
        {
          executionName: 'exec-1',
          dryRun: false,
          flatten: {
            bucketName: 'blog-topic-generator-staging',
            success: true,
            totalFlattenedItems: 100,
            key: 'blog-topic-jobs/2025-09-21/exe-uuid/flattened-A.json',
          },
        },
        {
          executionName: 'exec-2',
          dryRun: false,
          flatten: {
            bucketName: 'blog-topic-generator-staging',
            success: true,
            totalFlattenedItems: 80,
            key: 'blog-topic-jobs/2025-09-21/exe-uuid/flattened-B.json',
          },
        },
      ];

      const itemsA = Array.from({ length: 100 }, (_, i) => ({ id: `A${i}` }));
      const itemsB = Array.from({ length: 80 }, (_, i) => ({ id: `B${i}` }));

      // 1) SUCCEEDED_0.json get
      mockS3Send
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockSucceededContent)),
          },
        })
        // 2) flattened-A.json get (Body used by stream-json mocks)
        .mockResolvedValueOnce({
          Body: { __items: itemsA },
        })
        // 3) flattened-B.json get
        .mockResolvedValueOnce({
          Body: { __items: itemsB },
        })
        // 4) PutObject for consolidated file (Body is a stream; we don't inspect)
        .mockResolvedValueOnce({});

      const result = await handler(mockInput, mockContext, jest.fn());

      expect((result as GetFlattenedItemsKeyOutput).success).toBe(true);
      expect((result as GetFlattenedItemsKeyOutput).totalFlattenedItems).toBe(
        180,
      );
      expect((result as GetFlattenedItemsKeyOutput).bucketName).toBe(
        'blog-topic-generator-staging',
      );
      expect((result as GetFlattenedItemsKeyOutput).key).toMatch(
        /consolidated-flattened-items\.json$/,
      );
      expect(mockS3Send).toHaveBeenCalledTimes(4);
    });
  });

  describe('graceful exit scenarios', () => {
    it('should gracefully exit when totalFlattenedItems is 0', async () => {
      const mockSucceededContent = [
        {
          executionName: '5d6c7b47-cff1-43a9-b2d1-0f28117503ed',
          dryRun: true,
          flatten: {
            bucketName: 'blog-topic-generator-staging',
            success: true,
            totalFlattenedItems: 0,
            key: 'empty-file.json',
          },
        },
      ];

      mockS3Send.mockResolvedValue({
        Body: {
          transformToString: () =>
            Promise.resolve(JSON.stringify(mockSucceededContent)),
        },
      });

      const result = await handler(mockInput, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message: 'No flattened items found',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
    });

    it('should gracefully exit when SUCCEEDED_0.json file does not exist', async () => {
      const noSuchKeyError = new Error('The specified key does not exist.');
      noSuchKeyError.name = 'NoSuchKey';

      mockS3Send.mockRejectedValue(noSuchKeyError);

      const result = await handler(mockInput, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message: 'SUCCEEDED_0.json file not found',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
    });

    it('should gracefully exit when file has no content', async () => {
      mockS3Send.mockResolvedValue({
        Body: undefined,
      });

      const result = await handler(mockInput, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message: 'No content in SUCCEEDED_0.json file',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
    });
  });

  describe('input validation', () => {
    it('should safely exit when key is empty string', async () => {
      const input = {
        ...mockInput,
        key: '',
      } as any;

      const result = await handler(input, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message:
          'Invalid input key: expected a non-empty string ending with /manifest.json',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
      expect(mockS3Send).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid or unexpected event.key for SUCCEEDED_0 derivation',
        expect.objectContaining({ bucketName: mockInput.bucketName, key: '' }),
      );
    });

    it('should safely exit when key does not end with /manifest.json', async () => {
      const badKey = 'blog-topic-jobs/2025-09-21/some-exec/manifest.txt';
      const input = {
        ...mockInput,
        key: badKey,
      } as any;

      const result = await handler(input, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message:
          'Invalid input key: expected a non-empty string ending with /manifest.json',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
      expect(mockS3Send).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid or unexpected event.key for SUCCEEDED_0 derivation',
        expect.objectContaining({
          bucketName: mockInput.bucketName,
          key: badKey,
        }),
      );
    });

    it('should safely exit when key is not a string', async () => {
      const input = {
        ...mockInput,
        key: 12345 as any,
      } as any;

      const result = await handler(input, mockContext, jest.fn());

      expect(result).toEqual({
        success: true,
        message:
          'Invalid input key: expected a non-empty string ending with /manifest.json',
        totalFlattenedItems: 0,
        bucketName: null,
        key: null,
      });
      expect(mockS3Send).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Invalid or unexpected event.key for SUCCEEDED_0 derivation',
        expect.objectContaining({
          bucketName: mockInput.bucketName,
          key: 12345,
        }),
      );
    });
  });

  describe('error handling', () => {
    it('should throw error for non-NoSuchKey S3 errors', async () => {
      const s3Error = new Error('Access denied');
      s3Error.name = 'AccessDenied';

      mockS3Send.mockRejectedValue(s3Error);

      await expect(handler(mockInput, mockContext, jest.fn())).rejects.toThrow(
        'Access denied',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error fetching flattened items key',
        {
          error: 'Access denied',
          stack: expect.any(String),
        },
      );
    });
  });
});
