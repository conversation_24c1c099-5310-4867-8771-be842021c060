import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Handler, Context } from 'aws-lambda';
import { Neighborhood } from 'src/clients/types/neighborhood';
import { EnvironmentConfig, getConfig } from 'src/config';
import {
  EnrichedEntitlement,
  FlattenedEntitlement,
} from 'src/types/entitlement';
import { createContextLogger, ContextLogger } from 'src/utils/contextLogger';
import { buildExecutionFileKey } from 'src/utils/s3Path';

interface FlattenPerNeighborhoodOutput {
  success: boolean;
  totalFlattenedItems: number;
  bucketName: string | null;
  key: string | null;
  timestamp?: string;
  error?: string;
}

export type FlattenPerNeighborhoodEvent = {
  entitlement: EnrichedEntitlement;
  executionName: string;
  executionStartTime: string;
  dryRun: boolean;
};

/**
 * Validates that required execution metadata fields are present and valid.
 * Returns false for missing or invalid fields, true if valid.
 */
function validateExecutionMetadata(
  event: FlattenPerNeighborhoodEvent,
  logger: ContextLogger,
): boolean {
  if (!event) {
    logger.error('Event is null or undefined');
    return false;
  }

  if (!event.executionName || typeof event.executionName !== 'string') {
    logger.error('executionName is missing or invalid', {
      executionName: event.executionName,
      type: typeof event.executionName,
    });
    return false;
  }

  if (
    !event.executionStartTime ||
    typeof event.executionStartTime !== 'string'
  ) {
    logger.error('executionStartTime is missing or invalid', {
      executionStartTime: event.executionStartTime,
      type: typeof event.executionStartTime,
    });
    return false;
  }

  if (typeof event.dryRun !== 'boolean') {
    logger.error('dryRun is missing or invalid', {
      dryRun: event.dryRun,
      type: typeof event.dryRun,
    });
    return false;
  }

  // Note: null entitlement check is handled in the main handler before calling this validation
  if (event.entitlement && typeof event.entitlement !== 'object') {
    logger.error('entitlement is invalid type when present', {
      entitlementType: typeof event.entitlement,
    });
    return false;
  }

  return true;
}

/**
 * Builds S3 location for flattened entitlements file.
 * Assumes execution metadata has already been validated.
 *
 * @param event - Lambda event with validated execution metadata
 * @param config - Environment configuration with ENVIRONMENT field
 * @returns Object with bucketName and s3Key
 */
function buildS3Location(
  event: FlattenPerNeighborhoodEvent,
  config: EnvironmentConfig,
): { bucketName: string; s3Key: string } {
  // Build bucket name from environment
  const bucketName = `blog-topic-generator-${config.ENVIRONMENT}`;

  // Create unique filename using company ID to prevent collisions
  const filename = `flattened-entitlements-${event.entitlement.companyId}`;

  // Build S3 key with execution metadata
  const s3Key = buildExecutionFileKey(filename, {
    executionName: event.executionName,
    executionStartTime: event.executionStartTime,
    dryRun: event.dryRun,
  });

  return { bucketName, s3Key };
}

export const handler: Handler<
  FlattenPerNeighborhoodEvent | null,
  FlattenPerNeighborhoodOutput
> = async (event, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'flatten-per-neighborhood',
    context,
  );

  // Check for null event first (filtered out by upstream Lambda)
  if (event === null || event === undefined) {
    logger.info(
      'Event is null - filtered out by upstream Lambda, skipping processing',
    );
    return {
      success: true,
      totalFlattenedItems: 0,
      bucketName: null,
      key: null,
    };
  }

  // Validate execution metadata first (this will catch invalid event types)
  if (!validateExecutionMetadata(event, logger)) {
    logger.warn('Invalid execution metadata, skipping processing');
    return {
      success: true,
      totalFlattenedItems: 0,
      bucketName: null,
      key: null,
    };
  }

  // Check for null entitlement (filtered out by upstream Lambda)
  if (event.entitlement === null || event.entitlement === undefined) {
    logger.info(
      'Entitlement is null - filtered out by upstream Lambda, skipping processing',
    );
    return {
      success: true,
      totalFlattenedItems: 0,
      bucketName: null,
      key: null,
    };
  }

  const entitlement = event.entitlement;

  logger.info('FlattenPerNeighborhood Lambda invoked', {
    companyId: entitlement.companyId,
    neighborhoodsCount: entitlement.neighborhoods?.length || 0,
  });

  if (!entitlement.neighborhoods || entitlement.neighborhoods.length === 0) {
    logger.info('No neighborhoods to process for entitlement', {
      companyId: entitlement.companyId,
    });
    return {
      success: true,
      totalFlattenedItems: 0,
      bucketName: null,
      key: null,
    };
  }

  try {
    logger.info('Processing entitlement with neighborhoods', {
      companyId: entitlement.companyId,
      totalNeighborhoods: entitlement.neighborhoods.length,
    });

    // Flatten entitlement per neighborhood
    const flattenedEntitlements: FlattenedEntitlement[] = [];

    entitlement.neighborhoods.forEach((neighborhood: Neighborhood) => {
      flattenedEntitlements.push({
        companyId: entitlement.companyId,
        brandProfile: entitlement.brandProfile || null,
        keywordMap: entitlement.keywordMap || null,
        neighborhood,
      });
    });

    logger.info('Flattened entitlement per neighborhood', {
      totalFlattenedItems: flattenedEntitlements.length,
      companyId: entitlement.companyId,
    });

    // Build S3 location (semantic)
    const { bucketName, s3Key } = buildS3Location(event, config);

    // Save to S3
    logger.info('Saving flattened entitlements to S3', {
      bucketName,
      s3Key,
      itemCount: flattenedEntitlements.length,
    });

    const s3Client = new S3Client({ region: config.REGION });
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: s3Key,
      Body: JSON.stringify(flattenedEntitlements),
      ContentType: 'application/json',
    });

    await s3Client.send(command);

    logger.info('Successfully saved flattened entitlements to S3', {
      bucketName,
      s3Key,
      itemCount: flattenedEntitlements.length,
    });

    return {
      success: true,
      totalFlattenedItems: flattenedEntitlements.length,
      bucketName,
      key: s3Key,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error('Error in FlattenPerNeighborhood Lambda', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      companyId: entitlement.companyId,
    });

    return {
      success: false,
      totalFlattenedItems: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
      bucketName: null,
      key: null,
      timestamp: new Date().toISOString(),
    };
  }
};
