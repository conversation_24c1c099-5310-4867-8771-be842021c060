import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Handler, Context } from 'aws-lambda';
import { getConfig } from 'src/config';
import { PromptService } from 'src/services/PromptService';
import {
  GeneratedTopic,
  BackgroundJobStatus,
  BlogTopicType,
} from 'src/types/prompt';
import { createContextLogger, ContextLogger } from 'src/utils/contextLogger';
import { buildExecutionFileKey } from 'src/utils/s3Path';

type GeneratedTopicsPayload = {
  topics: Array<{
    target_location: string;
    parent_topic: string;
    keyword: string;
    blog_title: string;
    page_title: string;
    meta_description: string;
    rationale: string;
    type: string;
  }>;
};

interface PollBlogTopicsStatusInput {
  responseId: string;
  companyId: string;
  neighborhoodId: string;
  neighborhoodName: string;
  executionName: string;
  executionStartTime: string;
  dryRun: boolean;
}

interface PollBlogTopicsStatusOutput {
  success: boolean;
  companyId: string;
  neighborhoodId: string;
  neighborhoodName: string;
  responseId: string;
  status: BackgroundJobStatus;
  generatedTopics: GeneratedTopic[];
  totalTopics: number;
  error?: string;
}

export const handler: Handler<
  PollBlogTopicsStatusInput,
  PollBlogTopicsStatusOutput
> = async (input, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'poll-blog-topics-status',
    context,
  );

  // Validate input
  if (!input.responseId) {
    logger.error('Invalid input: responseId is required');
    throw new Error('Invalid input: responseId is required');
  }
  if (
    !input.executionName ||
    !input.executionStartTime ||
    typeof input.dryRun !== 'boolean'
  ) {
    logger.error(
      'Invalid input: executionName, executionStartTime, and dryRun are required',
    );
    throw new Error(
      'Invalid input: executionName, executionStartTime, and dryRun are required',
    );
  }

  try {
    logger.info('PollBlogTopicsStatus Lambda invoked', {
      responseId: input.responseId,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
    });

    const promptService = new PromptService(
      logger.createComponentLogger({
        serviceName: 'PromptService',
        layer: 'ai',
      }),
      config,
    );

    // Check the current status of the background job
    // Note: This lambda performs a single status check. The actual polling
    // loop (with retries and delays) is handled by the Step Functions state machine.
    const jobStatus = await promptService.getBackgroundJobStatus(
      input.responseId,
    );

    if (
      jobStatus.status === BackgroundJobStatus.COMPLETED &&
      jobStatus.content
    ) {
      // Best-effort: save raw OpenAI response for debugging/traceability
      try {
        const bucketName = `blog-topic-generator-${config.ENVIRONMENT}`;
        const executionName = input.executionName;
        const executionStartTime = input.executionStartTime;
        const key = buildExecutionFileKey(
          `openai-response-${input.responseId}`,
          {
            executionName,
            executionStartTime,
            // This step only runs when dryRun=false due to Step Functions Choice
            dryRun: false,
          },
        );
        const s3 = new S3Client({ region: config.REGION });
        await s3.send(
          new PutObjectCommand({
            Bucket: bucketName,
            Key: key,
            Body: JSON.stringify(jobStatus.content),
            ContentType: 'application/json',
            Metadata: {
              companyId: input.companyId,
              neighborhoodId: input.neighborhoodId,
              environment: config.ENVIRONMENT,
              responseId: input.responseId,
            },
          }),
        );
        logger.info('Saved raw OpenAI response to S3', {
          bucketName,
          key,
        });
      } catch (s3Error) {
        logger.warn('Failed to write OpenAI response to S3 (continuing)', {
          error: s3Error instanceof Error ? s3Error.message : String(s3Error),
        });
      }
      // Support tests providing content directly or via output_text
      const raw = jobStatus.content.output_text
        ? jobStatus.content.output_text
        : JSON.stringify(jobStatus.content);

      let payload: GeneratedTopicsPayload;
      try {
        payload = JSON.parse(raw) as GeneratedTopicsPayload;
      } catch (parseError) {
        logger.error('Failed to parse JSON content from background job', {
          responseId: input.responseId,
          companyId: input.companyId,
          neighborhoodId: input.neighborhoodId,
          rawContent: raw,
          parseError:
            parseError instanceof Error
              ? parseError.message
              : String(parseError),
        });

        // Return failure response for malformed JSON
        return {
          success: false,
          companyId: input.companyId,
          neighborhoodId: input.neighborhoodId,
          neighborhoodName: input.neighborhoodName,
          responseId: input.responseId,
          status: BackgroundJobStatus.FAILED,
          error: 'Failed to parse generated topics content',
          generatedTopics: [],
          totalTopics: 0,
        };
      }

      if (
        payload?.topics &&
        Array.isArray(payload.topics) &&
        payload.topics.length > 0
      ) {
        const blogTopics: GeneratedTopic[] = payload.topics.map(topic => ({
          topic: topic.blog_title,
          blogTitle: topic.blog_title,
          neighborhoodId: input.neighborhoodId,
          companyId: input.companyId,
          rationale: topic.rationale,
          parentTopic: topic.parent_topic,
          type: topic.type as BlogTopicType,
        }));

        logger.info('Background job completed successfully', {
          responseId: input.responseId,
          totalTopics: blogTopics.length,
          companyId: input.companyId,
          neighborhoodId: input.neighborhoodId,
        });

        return {
          success: true,
          companyId: input.companyId,
          neighborhoodId: input.neighborhoodId,
          neighborhoodName: input.neighborhoodName,
          responseId: input.responseId,
          status: BackgroundJobStatus.COMPLETED,
          generatedTopics: blogTopics,
          totalTopics: blogTopics.length,
        };
      }
    }

    // Job is still processing or failed
    return {
      success: jobStatus.status === BackgroundJobStatus.COMPLETED,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      neighborhoodName: input.neighborhoodName,
      responseId: input.responseId,
      status: jobStatus.status,
      error:
        jobStatus.status === BackgroundJobStatus.FAILED
          ? 'Background job failed'
          : undefined,
      generatedTopics: [],
      totalTopics: 0,
    };
  } catch (error) {
    logger.error('Error in PollBlogTopicsStatus Lambda', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      responseId: input.responseId,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
    });

    return {
      success: false,
      companyId: input.companyId,
      neighborhoodId: input.neighborhoodId,
      neighborhoodName: input.neighborhoodName,
      responseId: input.responseId,
      status: BackgroundJobStatus.FAILED,
      error: error instanceof Error ? error.message : 'Unknown error',
      generatedTopics: [],
      totalTopics: 0,
    };
  }
};
