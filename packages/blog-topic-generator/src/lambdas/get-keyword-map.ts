import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Context, Handler } from 'aws-lambda';
import { getConfig } from 'src/config';
import { EnrichedEntitlement } from 'src/types/entitlement';
import { ContextLogger, createContextLogger } from 'src/utils/contextLogger';

export type GetKeywordMapInput = {
  enrichedWithBrandProfile: EnrichedEntitlement;
  executionName?: string;
  executionStartTime?: string;
  dryRun?: boolean;
};

export const handler: Handler<
  GetKeywordMapInput,
  EnrichedEntitlement | null
> = async (input, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger('get-keyword-map', context);

  logger.info('GetKeywordMap Lambda invoked', {
    input: typeof input,
    companyId: input?.enrichedWithBrandProfile?.companyId,
  });

  if (!input || typeof input !== 'object') {
    logger.error('Invalid input: expected an object');
    throw new Error('Invalid input: expected an object');
  }

  if (
    !input.enrichedWithBrandProfile ||
    typeof input.enrichedWithBrandProfile !== 'object'
  ) {
    logger.error(
      'Invalid input: expected enrichedWithBrandProfile entitlement object',
      {
        enrichedWithBrandProfilesType: typeof input.enrichedWithBrandProfile,
      },
    );
    throw new Error(
      'Invalid input: expected enrichedWithBrandProfile entitlement object',
    );
  }

  const entitlement = input.enrichedWithBrandProfile;

  logger.info('Processing entitlement', {
    companyId: entitlement.companyId,
  });

  const s3Client = new S3Client({ region: config.REGION });
  // TODO: This is a temporary solution.
  const bucketName = `lp-bloom-scheduler-execution-${config.ENVIRONMENT}`;
  const keywordMapPrefix = 'keyword_map/';

  try {
    const key = `${keywordMapPrefix}${entitlement.companyId}.json`;

    logger.debug('Fetching keyword map from S3', {
      bucketName,
      key,
      companyId: entitlement.companyId,
    });

    const getObjectCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: key,
    });

    const response = await s3Client.send(getObjectCommand);
    const bodyContents = await response.Body?.transformToString();

    if (!bodyContents) {
      logger.warn('Empty keyword map file found', {
        companyId: entitlement.companyId,
        bucketName,
        key,
      });
      return null;
    }

    const keywordMap: Record<string, string> = JSON.parse(bodyContents);

    logger.debug('Successfully fetched keyword map', {
      companyId: entitlement.companyId,
      keywordMapKeys: Object.keys(keywordMap).length,
    });

    const enriched: EnrichedEntitlement = {
      ...entitlement,
      keywordMap,
    };

    logger.info('Keyword map enrichment completed', {
      companyId: entitlement.companyId,
      keywordMapKeys: Object.keys(keywordMap).length,
    });

    return enriched;
  } catch (error) {
    // Check for 404 Not Found error (file doesn't exist)
    if (error instanceof Error && error.name === 'NoSuchKey') {
      logger.warn('Keyword map file not found for company', {
        companyId: entitlement.companyId,
        bucketName,
        key: `${keywordMapPrefix}${entitlement.companyId}.json`,
      });
      return null;
    } else {
      logger.error('Error fetching keyword map from S3', {
        companyId: entitlement.companyId,
        bucketName,
        key: `${keywordMapPrefix}${entitlement.companyId}.json`,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      });
      return null;
    }
  }
};
