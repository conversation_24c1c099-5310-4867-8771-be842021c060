import { Context } from 'aws-lambda';
import { EnrichedEntitlement, EntitlementDTO } from 'src/types/entitlement';

import { createContextLogger } from '../utils/contextLogger';

interface SanitizedEventMetadata {
  eventId?: string;
  eventType: string;
  source: string;
  timestamp?: string;
  companyId: string;
  stepName: string;
  hasEntitlement: boolean;
  hasEnrichedWithNeighborhoods: boolean;
  hasEnrichedWithBrandProfile: boolean;
  hasExecutionMetadata: boolean;
  errorSummary: {
    errorType: string;
    hasCause: boolean;
  };
}

interface StepFunctionError {
  Error: string;
  Cause: string; // A stringified JSON object more likely.
}
// CatchFailure receives the original lambda input + error (via ResultPath: $.error)
interface CatchFailureEvent {
  error: StepFunctionError;
  // Only one of these will be present depending on which step failed
  entitlement?: EntitlementDTO;
  enrichedWithNeighborhoods?: EnrichedEntitlement;
  enrichedWithBrandProfile?: EnrichedEntitlement;
  // Execution metadata from the original input
  executionName?: string;
  executionStartTime?: string;
  dryRun?: boolean;
}

/**
 * Sanitizes the event data to extract only safe metadata for logging,
 * avoiding PII and large nested objects like brand profiles and neighborhoods
 */
function sanitizeEventMetadata(
  event: CatchFailureEvent,
): SanitizedEventMetadata {
  // Extract company ID safely
  const companyId =
    event.entitlement?.companyId ||
    event.enrichedWithNeighborhoods?.companyId ||
    event.enrichedWithBrandProfile?.companyId ||
    'unknown';

  // Determine step name from available data
  const stepName = event.entitlement
    ? 'EnrichEntitlements'
    : event.enrichedWithNeighborhoods
      ? 'GetBrandProfile'
      : event.enrichedWithBrandProfile
        ? 'GetKeywordMap'
        : 'Unknown';

  return {
    eventId: event.executionName,
    eventType: 'step-function-failure',
    source: 'catch-failure-handler',
    timestamp: event.executionStartTime,
    companyId,
    stepName,
    hasEntitlement: !!event.entitlement,
    hasEnrichedWithNeighborhoods: !!event.enrichedWithNeighborhoods,
    hasEnrichedWithBrandProfile: !!event.enrichedWithBrandProfile,
    hasExecutionMetadata: !!(
      event.executionName ||
      event.executionStartTime ||
      event.dryRun
    ),
    errorSummary: {
      errorType: event.error?.Error || 'unknown',
      hasCause: !!event.error?.Cause,
    },
  };
}

export const handler = (event: CatchFailureEvent, context: Context) => {
  const logger = createContextLogger('catch-failure', context, {
    lambdaName: 'catch-failure',
  });

  const sanitizedMetadata = sanitizeEventMetadata(event);
  logger.info('Starting catch failure', { sanitizedMetadata });

  try {
    // Process the failure - setting status to FAILED after retries exhausted
    logger.info('Blog topic generator execution failed', {
      ...sanitizedMetadata,
      error: {
        type: event.error?.Error,
        hasCause: !!event.error?.Cause,
      },
    });

    logger.info('Catch failure processed successfully');
  } catch (error) {
    logger.error('Error in catch failure', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorType: error instanceof Error ? error.constructor.name : 'Unknown',
    });
  }
};
