import { Handler, Context } from 'aws-lambda';
import { CosmoClient } from 'src/clients/CosmoClient';
import { getConfig } from 'src/config';
import { EnrichedEntitlement } from 'src/types/entitlement';
import { createContextLogger, ContextLogger } from 'src/utils/contextLogger';

export type GetBrandProfileInput = {
  enrichedWithNeighborhoods: EnrichedEntitlement;
  executionName?: string;
  executionStartTime?: string;
  dryRun?: boolean;
};

export const handler: Handler<
  GetBrandProfileInput,
  EnrichedEntitlement | null
> = async (input, context: Context) => {
  const config = getConfig();
  const logger: ContextLogger = createContextLogger(
    'get-brand-profile',
    context,
  );

  logger.info('GetBrandProfile Lambda invoked', {
    input: typeof input,
    companyId: input?.enrichedWithNeighborhoods?.companyId,
  });

  if (!input || typeof input !== 'object') {
    logger.error('Invalid input: expected an object');
    throw new Error('Invalid input: expected an object');
  }

  if (
    !input.enrichedWithNeighborhoods ||
    typeof input.enrichedWithNeighborhoods !== 'object'
  ) {
    logger.error(
      'Invalid input: expected enrichedWithNeighborhoods entitlement object',
      {
        enrichedWithNeighborhoodsType: typeof input.enrichedWithNeighborhoods,
      },
    );
    throw new Error(
      'Invalid input: expected enrichedWithNeighborhoods entitlement object',
    );
  }

  const entitlement = input.enrichedWithNeighborhoods;

  logger.info('Processing entitlement', {
    companyId: entitlement.companyId,
  });

  const cosmoClient = new CosmoClient(
    config.COSMO_GQL_URL,
    config.M2M_SUPER_API_KEY,
    logger.createComponentLogger({ clientName: 'CosmoClient', layer: 'api' }),
  );

  try {
    logger.debug('Fetching brand profile for entitlement', {
      companyId: entitlement.companyId,
      name: entitlement.company?.name,
    });

    const brandProfile = await cosmoClient.getBrandProfile(
      entitlement.companyId,
    );

    // If no brand profile exists, log and return null
    if (brandProfile === null) {
      logger.warn(
        'Entitlement not suitable for topic generation - no brand profile',
        {
          companyId: entitlement.companyId,
          name: entitlement.company?.name,
        },
      );
      return null; // Filter out entitlements without brand profiles
    }

    const enriched: EnrichedEntitlement = {
      ...entitlement,
      brandProfile,
    };

    logger.info('Added brand profile to entitlement', {
      companyId: entitlement.companyId,
      name: entitlement.company?.name,
      profileId: brandProfile.id,
    });

    return enriched;
  } catch (error) {
    // Return null for failed entitlements
    logger.warn(
      'Entitlement not suitable for topic generation - failed to fetch brand profile',
      {
        companyId: entitlement.companyId,
        name: entitlement.company?.name,
        error: error instanceof Error ? error.message : String(error),
      },
    );
    return null; // Filter out failed entitlements
  }
};
