import { Handler } from 'aws-lambda';

type ExecutionInput = {
  dryRun?: boolean;
  companyIds?: string[];
  [key: string]: any;
};
// eslint-disable-next-line @typescript-eslint/require-await
export const handler: Handler<ExecutionInput, ExecutionInput> = async event => {
  const defaults: ExecutionInput = { dryRun: true, companyIds: [] };

  const merged: ExecutionInput = {
    ...defaults,
    ...event,
  };

  if (!Array.isArray(merged.companyIds)) {
    merged.companyIds = [];
  }

  return merged;
};
