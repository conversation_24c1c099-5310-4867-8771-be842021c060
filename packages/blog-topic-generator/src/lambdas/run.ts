import { Context } from 'aws-lambda';

import { handler } from './get-flattened-items-key';
import { event } from '../testEvents/getFlattenedItemsKeyEvent';

async function run() {
  try {
    const result = await handler(event, {} as Context, () => {});
    console.log('Lambda Result:', JSON.stringify(result, null, 2));
  } catch (err) {
    console.error('Lambda Error:', err);
  }
  process.exit(0);
}

void run();
