import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { <PERSON><PERSON> } from 'aws-lambda';

import { TenantClient } from '../clients/TenantClient';
import { getConfig } from '../config';
import { EntitlementService } from '../services/EntitlementService';
import { EntitlementValidationService } from '../services/EntitlementValidationService';
import { createContextLogger } from '../utils/contextLogger';
import { buildExecutionFileKey } from '../utils/s3Path';

interface FetchEntitlementsOutput {
  success: boolean;
  totalEntitlements: number;
  validEntitlements: number;
  bucketName: string | null;
  key: string | null;
  error?: string;
  executionStartTime?: string;
  executionFolderName?: string;
}
const config = getConfig();
const BUCKET_NAME = `blog-topic-generator-${config.ENVIRONMENT}`;

type FetchEntitlementsInput = {
  companyIds: string[];
  dryRun: boolean;
  executionName: string;
  executionStartTime: string;
};

export const handler: Handler<
  FetchEntitlementsInput,
  FetchEntitlementsOutput
> = async (input, context) => {
  const logger = createContextLogger('fetch-entitlements', context);
  const dryRunSuffix = input.dryRun ? '-dry-run' : '';
  const executionStartTime = input.executionStartTime;
  const executionFolderName = input.executionName
    ? `${input.executionName}${dryRunSuffix}`
    : undefined; // For Step Functions ResultWriter
  try {
    logger.info('FetchEntitlements Lambda triggered', { input });

    if (!input || typeof input !== 'object') {
      throw new Error('Invalid input');
    }
    if (
      !input.executionName ||
      !input.executionStartTime ||
      typeof input.dryRun !== 'boolean' ||
      !Array.isArray(input.companyIds)
    ) {
      throw new Error(
        'Invalid input: executionName, executionStartTime, dryRun, and companyIds are required',
      );
    }

    const productId = config.AI_BLOG_SPECIALIST_PRODUCT_ID;
    if (!productId) {
      throw new Error('Product ID is required');
    }

    const s3Key = buildExecutionFileKey('entitlements', {
      executionName: input.executionName,
      executionStartTime: input.executionStartTime,
      dryRun: !!input.dryRun,
    });

    // Create tenant client
    const tenantClient = new TenantClient(
      config.TENANT_SERVICE_URL,
      config.ENTITLEMENTS_LIMIT,
      logger,
      productId,
    );

    // Create validation service
    const validationService = new EntitlementValidationService(logger);

    // Create entitlement service
    const entitlementService = new EntitlementService(
      tenantClient,
      validationService,
      logger,
    );

    // Fetch all entitlements
    logger.info('Fetching entitlements from tenant service', { productId });
    const allEntitlements = await entitlementService.getEntitlements();
    logger.info('Fetched entitlements', { count: allEntitlements.length });

    // Optional execution-level filter by companyIds
    const companyIds = input.companyIds || [];
    const entitlementsToProcess =
      Array.isArray(companyIds) && companyIds.length > 0
        ? allEntitlements.filter(e => companyIds.includes(e.companyId))
        : allEntitlements;

    if (entitlementsToProcess.length !== allEntitlements.length) {
      logger.info('Filtered entitlements by execution companyIds', {
        original: allEntitlements.length,
        filtered: entitlementsToProcess.length,
      });
    }

    // Validate entitlements using the service
    logger.info('Validating entitlements');
    const validEntitlements = await entitlementService.validateStandard(
      entitlementsToProcess,
    );
    logger.info('Validated entitlements', {
      total: allEntitlements.length,
      valid: validEntitlements.length,
    });

    if (validEntitlements.length === 0) {
      logger.info('No valid entitlements found');
      return {
        success: true,
        totalEntitlements: allEntitlements.length,
        validEntitlements: 0,
        bucketName: null,
        key: null,
        executionStartTime,
        executionFolderName,
      };
    }

    // Write valid entitlements to S3
    logger.info('Writing valid entitlements to S3', {
      bucketName: BUCKET_NAME,
      s3Key,
      count: validEntitlements.length,
    });

    const s3Client = new S3Client({ region: config.REGION });

    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: JSON.stringify(validEntitlements),
      ContentType: 'application/json',
    });

    try {
      const result = await s3Client.send(command);
      logger.info('Entitlements saved to S3', { result });
    } catch (s3Error) {
      const err =
        s3Error instanceof Error ? s3Error : new Error(String(s3Error));
      logger.error('Error uploading to S3', {
        error: err.message,
        bucket: BUCKET_NAME,
        key: s3Key,
        stack: err.stack,
      });
      throw err;
    }

    return {
      success: true,
      totalEntitlements: allEntitlements.length,
      validEntitlements: validEntitlements.length,
      bucketName: BUCKET_NAME,
      key: s3Key,
      executionStartTime,
      executionFolderName,
    };
  } catch (error) {
    logger.error('Error in FetchEntitlements:', error);
    throw error instanceof Error ? error : new Error('Unknown error');
  }
};
