/**
 * Shared mock for <PERSON>textLogger to avoid repetition across test files
 */
export const createMockContextLogger = () =>
  ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    logger: {} as any,
    context: {} as any,
    getBaseLogger: jest.fn(),
    createChild: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      logger: {} as any,
      context: {} as any,
      getBaseLogger: jest.fn(),
    }),
    createComponentLogger: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      logger: {} as any,
      context: {} as any,
      getBaseLogger: jest.fn(),
    }),
  }) as any;
