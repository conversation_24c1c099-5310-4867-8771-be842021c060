'use strict';

// Conditionally include VPC configuration if SECURITY_GROUP_IDS and SUBNET_IDS are provided
// This mirrors the pattern used by other packages (e.g., brand-profile-generator)

// eslint-disable-next-line @typescript-eslint/require-await
module.exports = async () => {
  const configVPC = {};

  if (process.env.SECURITY_GROUP_IDS && process.env.SUBNET_IDS) {
    configVPC.securityGroupIds = {
      'Fn::Split': [',', process.env.SECURITY_GROUP_IDS],
    };
    configVPC.subnetIds = {
      'Fn::Split': [',', process.env.SUBNET_IDS],
    };
  }

  return configVPC;
};
