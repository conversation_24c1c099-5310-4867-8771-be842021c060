{"compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "baseUrl": "./src", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2021", "ES2022"], "module": "commonjs", "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "./dist", "paths": {"src/*": ["./*"]}, "pretty": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2021", "resolveJsonModule": true, "typeRoots": ["./src/types", "./node_modules/@types", "../node_modules/@types"], "useUnknownInCatchVariables": false}, "exclude": ["node_modules/**/*", ".serverless/**/*", ".webpack/**/*", "*.config.js"], "include": ["src/**/*", "test/**/*", "babel.config.cjs", "serverless/**/*.js"], "ts-node": {"require": ["tsconfig-paths/register"]}}