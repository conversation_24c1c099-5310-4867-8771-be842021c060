# Blog Topic Generator

AWS Lambda-based service for generating blog topics using OpenAI and Step Functions orchestration.

## Overview

This service handles:

- Fetching and validating company entitlements
- Flattening entitlements per neighborhood
- Generating blog topics using OpenAI
- Saving generated topics to database and S3

## Architecture

### Lambda Functions

- **fetch-entitlements**: Retrieves and validates company entitlements, filters by companyIds
- **flatten-per-neighborhood**: Flattens entitlements data per neighborhood for processing
- **generate-blog-topics-background**: Initiates OpenAI background job for topic generation
- **poll-blog-topics-status**: Polls OpenAI job status and retrieves generated topics
- **save-generated-topics**: Saves generated topics to database via GraphQL

### Step Functions

State machine orchestration for blog topic generation workflow with dry-run support.

## Development

### Prerequisites

- Node.js 18+
- pnpm package manager

### Setup

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build
```

### Environment Variables

Copy `.env.example` to `.env` and configure required variables. See `config-template.yaml` for vault-managed secrets.

## Running the State Machine

### AWS Console Execution

1. **Navigate to Step Functions**
   - AWS Console → Step Functions → State machines
   - Select `blog-topic-generator-[stage]`

2. **Start Execution**
   - Click "Start execution"
   - Provide input JSON (see examples below)

### Input Parameters

**Minimal Input (processes all entitled companies):**
```json
{}
```

**Specific Companies:**
```json
{
  "companyIds": ["comp_123", "comp_456"]
}
```

**Dry Run (no OpenAI tokens consumed, no DB writes):**
```json
{
  "companyIds": ["comp_123"],
  "dryRun": true
}
```

**Production Run (generates topics and saves to database):**
```json
{
  "companyIds": ["comp_123"],
  "dryRun": false
}
```

**Full Configuration:**
```json
{
  "companyIds": ["comp_123", "comp_456"],
  "dryRun": false,
  "executionName": "my-blog-generation",
  "executionStartTime": "2024-01-15T10:30:00Z"
}
```

### Input Parameters Explained

- **companyIds** (array, optional): Specific company IDs to process. If empty/omitted, processes all entitled companies
- **dryRun** (boolean, default: true): If true, stops after flatten-per-neighborhood step, avoiding OpenAI token consumption and database writes
- **executionName** (string, optional): Custom execution name for S3 organization. If omitted, uses auto-generated name
- **executionStartTime** (string, optional): ISO timestamp for S3 date folder. If omitted, uses current time

### Output Locations

**CloudWatch Logs:**
- Lambda logs: `/aws/lambda/blog-topic-generator-[stage]-[function]`
- Step Function logs: AWS Console → Step Functions → Execution History

**S3 Files:**
- Bucket: `blog-topic-generator-[stage]`
- Structure:
  ```
  blog-topic-jobs/
  └── YYYY-MM-DD/                          # Date from execution start time
      └── [execution-name]/                # With -dry-run suffix if dryRun: true
          ├── entitlements.json             # Validated entitlements (or entitlements.json-dry-run)
          ├── flattened-entitlements.json   # Flattened per neighborhood (or flattened-entitlements.json-dry-run)
          └── openai-response.json          # Raw OpenAI response for debugging (or openai-response.json-dry-run)
  ```

**Database (when dryRun: false):**
- Generated topics saved to `blog_topic` table via GraphQL mutation
- Query via GraphQL: `blogTopics(companyId: "comp_123", neighborhoodId: "neigh_456")`

### Monitoring Execution

1. **Real-time Status**: Step Functions console shows current state and progress
2. **Dry Run Detection**: Choice state automatically routes to End when dryRun: true
3. **Error Details**: Failed states show error messages in execution event history
4. **S3 Debugging**: Raw OpenAI responses saved for troubleshooting

### Execution Flow

**Normal Execution:**
1. `fetch-entitlements` → Validates and filters entitlements
2. `flatten-per-neighborhood` → Prepares data for OpenAI
3. `generate-blog-topics-background` → Initiates OpenAI job
4. `poll-blog-topics-status` → Retrieves generated topics
5. `save-generated-topics` → Saves to database

**Dry Run Execution:**
1. `fetch-entitlements` → Validates and filters entitlements
2. `flatten-per-neighborhood` → Prepares data for OpenAI
3. **Choice State** → Routes to End (skips OpenAI and DB steps)

### S3 Semantic Structure

The service uses semantic S3 paths for better organization:

- **Date-based folders**: `YYYY-MM-DD` from execution start time
- **Execution-specific folders**: Named after execution or auto-generated
- **Dry-run suffixes**: Files and folders get `-dry-run` suffix when dryRun: true
- **Standard filenames**: `entitlements.json`, `flattened-entitlements.json`, `openai-response.json`

## Testing

```bash
# Run unit tests
pnpm test

# Lint code
pnpm lint
```

## Deployment

The service is deployed using Serverless Framework
