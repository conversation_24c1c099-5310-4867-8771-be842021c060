service: blog-topic-generator

useDotenv: true

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-step-functions
  - serverless-plugin-datadog
  - serverless-offline

custom:
  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    exclude: ['aws-sdk']
    target: 'node22'
    define: { 'require.resolve': undefined }
    platform: 'node'
    concurrency: 1
    zipConcurrency: 1
    tsconfig: 'tsconfig.json'
    format: 'cjs'
    mainFields: ['module', 'main']
  serverless-offline:
    httpPort: 3005
    lambdaPort: 3006
  datadog:
    site: datadoghq.com
    apiKey: ${env:DD_API_KEY}
    enableDDTracing: true
    enableDDLogs: true
    enableXrayTracing: false
    enableSourceCodeIntegration: true
    captureLambdaPayload: false
    propagateUpstreamTrace: true
    addLayers: true
    enableColdStartTracing: true
    enableDDMonitoring: true
    env: ${self:provider.stage}
    service: blog-topic-generator
    version: ${env:GIT_SHA, 'latest'}
    tags: 'team:client-marketing'

package:
  individually: true

provider:
  name: aws
  stage: ${env:ENVIRONMENT, 'development'}
  region: us-east-1
  runtime: nodejs22.x
  versionFunctions: false
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT, 'development'}
    SLACK_NOTIFICATIONS_ENABLED: ${env:SLACK_NOTIFICATIONS_ENABLED, 'false'}
    SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL, ''}
    API_GATEWAY_SUPER_USER_COMPANY_ID: ${env:API_GATEWAY_SUPER_USER_COMPANY_ID, ''}
    API_GATEWAY_KEY: ${env:API_GATEWAY_KEY, ''}
    LAUNCHDARKLY_KEY: ${env:LAUNCHDARKLY_KEY, ''}
    TENANT_SERVICE_URL: ${env:TENANT_SERVICE_URL, ''}
    CMS_SERVICE_URL: ${env:CMS_SERVICE_URL, ''}
    API_GATEWAY_URL: ${env:API_GATEWAY_URL, ''}
    COSMO_GQL_URL: ${env:COSMO_GQL_URL, ''}
    LANGFUSE_SECRET_KEY: ${env:LANGFUSE_SECRET_KEY, ''}
    LANGFUSE_PUBLIC_KEY: ${env:LANGFUSE_PUBLIC_KEY, ''}
    M2M_SUPER_API_KEY: ${env:M2M_SUPER_API_KEY, ''}
    OPENAI_API_KEY: ${env:OPENAI_API_KEY, ''}
  stackTags:
    env: ${env:ENVIRONMENT, 'development'}
    app: blog-topic-generator
    platform_version: v3
    team: client-marketing
  tags:
    env: ${env:ENVIRONMENT, 'development'}
    app: blog-topic-generator
    platform_version: v3
    team: client-marketing
    Project: seo-automation
    Service: blog-topic-generator
    dd_trace_enabled: true
    dd_service: blog-topic-generator
    dd_env: ${env:ENVIRONMENT, 'development'}
    dd_version: ${env:GIT_SHA, 'latest'}
  vpc: ${file(./serverless/conditionalVPC.js)}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:ListBucket
          Resource:
            - !GetAtt BlogTopicGeneratorBucket.Arn
            - !Sub '${BlogTopicGeneratorBucket.Arn}/*'
        # Allow listing scheduler execution bucket
        - Effect: Allow
          Action:
            - s3:ListBucket
          Resource:
            - arn:aws:s3:::lp-bloom-scheduler-execution-${self:provider.stage}
        # Allow reading keyword maps from scheduler execution bucket objects
        - Effect: Allow
          Action:
            - s3:GetObject
          Resource:
            - arn:aws:s3:::lp-bloom-scheduler-execution-${self:provider.stage}/*
        - Effect: Allow
          Action:
            - lambda:InvokeFunction
          Resource:
            - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-${self:provider.stage}-*
        - Effect: Allow
          Action:
            - states:StartExecution
            - states:DescribeExecution
            - states:StopExecution
            - states:SendTaskSuccess
            - states:SendTaskFailure
            - states:SendTaskHeartbeat
          Resource:
            - !Sub arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:*
            - !Sub arn:aws:states:${AWS::Region}:${AWS::AccountId}:execution:*:*
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource:
            - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:service}-${self:provider.stage}-*'
            - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/${self:service}-${self:provider.stage}-*:log-stream:*'

functions:
  InitializeExecutionInput:
    handler: src/lambdas/initialize-execution-input.handler
    timeout: 10
    memorySize: 128
    description: Initialize and default execution input (dryRun=true, companyIds=[])
  FetchEntitlements:
    handler: src/lambdas/fetch-entitlements.handler
    timeout: 300
    memorySize: 1024
    description: Fetch entitlements for blog topic generation

  EnrichEntitlements:
    handler: src/lambdas/enrich-entitlements.handler
    timeout: 300
    memorySize: 1024
    description: Enrich entitlements with additional data

  GetBrandProfile:
    handler: src/lambdas/get-brand-profile.handler
    timeout: 300
    memorySize: 1024
    description: Get brand profile for company

  GetKeywordMap:
    handler: src/lambdas/get-keyword-map.handler
    timeout: 300
    memorySize: 1024
    description: Get keyword mapping for blog topics

  FlattenPerNeighborhood:
    handler: src/lambdas/flatten-per-neighborhood.handler
    timeout: 300
    memorySize: 1024
    description: Flatten enriched entitlements per neighborhood

  GenerateBlogTopicsBackground:
    handler: src/lambdas/generate-blog-topics-background.handler
    timeout: 900
    memorySize: 2048
    description: Generate blog topics with background processing

  PollBlogTopicsStatus:
    handler: src/lambdas/poll-blog-topics-status.handler
    timeout: 300
    memorySize: 1024
    description: Poll the status of background blog topic generation jobs

  SaveGeneratedTopics:
    handler: src/lambdas/save-generated-topics.handler
    timeout: 300
    memorySize: 1024
    description: Save generated blog topics to storage

  CatchFailure:
    handler: src/lambdas/catch-failure.handler
    memorySize: 128
    timeout: 30
    description: Handle and log step function failures
  
  GetFlattenedItemsKey:
    handler: src/lambdas/get-flattened-items-key.handler
    memorySize: 2048
    timeout: 180
    description: Merges the flattened items from the enrichment stage into a single file, return consolidated flattened items key


stepFunctions:
  validate: true
  stateMachines:
    blogTopicGeneratorProcessor:
      name: blog-topic-generator-processor-${self:provider.stage}
      role: !GetAtt StepFunctionsExecutionRole.Arn
      definition:
        Comment: 'Part 2: Generates blog topics from the enriched S3 data.'
        StartAt: CheckDryRun
        States:
          CheckDryRun:
            Type: Choice
            Choices:
              - Variable: $.dryRun
                BooleanEquals: true
                Next: DryRunCompleted
            Default: GetFlattenedItemsKey

          DryRunCompleted:
            Type: Pass
            Result: 'Dry-run: skipping topic generation'
            End: true
          
          GetFlattenedItemsKey:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !GetAtt GetFlattenedItemsKey.Arn
              Payload.$: $
            ResultSelector:
              key.$: $.Payload.key
              bucketName.$: $.Payload.bucketName
              totalFlattenedItems.$: $.Payload.totalFlattenedItems
            ResultPath: $.flattenKeyResult
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 1
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals:
                  - States.ALL
                ResultPath: $.flattenKeyError
                # TODO: Handle failure
                Next: NoFlattenedItems
            Next: CheckFlattenedItemsCount

          CheckFlattenedItemsCount:
            Type: Choice
            Choices:
              - Variable: $.flattenKeyResult.totalFlattenedItems
                NumericEquals: 0
                Next: NoFlattenedItems
            Default: MapGenerateTopics

          NoFlattenedItems:
            Type: Pass
            Result: 'No flattened items found - skipping topic generation'
            End: true

          MapGenerateTopics:
            Type: Map
            Parameters:
              itemData.$: $$.Map.Item.Value
              executionInfo:
                executionName.$: $.originalExecutionName
                executionStartTime.$: $.originalExecutionStartTime
                dryRun.$: $.dryRun
            ItemProcessor:
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
              StartAt: GenerateBlogTopicsBackground
              States:
                GenerateBlogTopicsBackground:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt GenerateBlogTopicsBackground.Arn
                    Payload.$: $
                  ResultSelector:
                    responseId.$: $.Payload.responseId
                    companyId.$: $.Payload.companyId
                    neighborhoodId.$: $.Payload.neighborhoodId
                    neighborhoodName.$: $.Payload.neighborhoodName
                  ResultPath: $.background
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Next: InitializePollingState

                InitializePollingState:
                  Type: Pass
                  Parameters:
                    responseId.$: $.background.responseId
                    companyId.$: $.background.companyId
                    neighborhoodId.$: $.background.neighborhoodId
                    neighborhoodName.$: $.background.neighborhoodName
                    maxPollingAttempts: 20
                    pollingAttempts: 0
                    executionInfo.$: $.executionInfo
                  Next: PollBlogTopicsStatus

                PollBlogTopicsStatus:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt PollBlogTopicsStatus.Arn
                    Payload:
                      responseId.$: $.responseId
                      companyId.$: $.companyId
                      neighborhoodId.$: $.neighborhoodId
                      neighborhoodName.$: $.neighborhoodName
                      executionName.$: $.executionInfo.executionName
                      executionStartTime.$: $.executionInfo.executionStartTime
                      dryRun.$: $.executionInfo.dryRun
                  ResultSelector:
                    status.$: $.Payload.status
                    success.$: $.Payload.success
                    generatedTopics.$: $.Payload.generatedTopics
                    totalTopics.$: $.Payload.totalTopics
                  ResultPath: $.pollResult
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - States.ALL
                      Next: HandlePollingError
                      ResultPath: $.error
                  Next: IncrementPollingAttempts

                IncrementPollingAttempts:
                  Type: Pass
                  Parameters:
                    responseId.$: $.responseId
                    companyId.$: $.companyId
                    neighborhoodId.$: $.neighborhoodId
                    neighborhoodName.$: $.neighborhoodName
                    maxPollingAttempts.$: $.maxPollingAttempts
                    pollingAttempts.$: States.MathAdd($.pollingAttempts, 1)
                    lastPollResult.$: $.pollResult
                    executionInfo.$: $.executionInfo
                  Next: CheckCircuitBreaker

                CheckCircuitBreaker:
                  Type: Choice
                  Choices:
                    - Variable: $.pollingAttempts
                      NumericGreaterThanPath: $.maxPollingAttempts
                      Next: PollingTimeoutExceeded
                    - Variable: $.lastPollResult.status
                      StringEquals: completed
                      Next: SaveGeneratedTopics
                    - Variable: $.lastPollResult.status
                      StringEquals: failed
                      Next: BlogTopicsFailed
                  Default: CheckPollingStatus

                CheckPollingStatus:
                  Type: Choice
                  Choices:
                    - Variable: $.lastPollResult.status
                      StringEquals: completed
                      Next: SaveGeneratedTopics
                    - Variable: $.lastPollResult.status
                      StringEquals: failed
                      Next: BlogTopicsFailed
                  Default: WaitAndRetry

                WaitAndRetry:
                  Type: Wait
                  Seconds: 30
                  Next: PollBlogTopicsStatus

                PollingTimeoutExceeded:
                  Type: Pass
                  Result: 'Polling timeout exceeded - maximum attempts reached'
                  End: true

                SaveGeneratedTopics:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt SaveGeneratedTopics.Arn
                    Payload:
                      success.$: $.lastPollResult.success
                      companyId.$: $.companyId
                      neighborhoodId.$: $.neighborhoodId
                      neighborhoodName.$: $.neighborhoodName
                      generatedTopics.$: $.lastPollResult.generatedTopics
                      totalTopics.$: $.lastPollResult.totalTopics
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  End: true

                BlogTopicsFailed:
                  Type: Pass
                  Result: 'Blog topics generation failed'
                  End: true

                HandlePollingError:
                  Type: Pass
                  Result: 'Error occurred while polling blog topics status'
                  End: true
            Label: MapGenerateTopics
            MaxConcurrency: 10
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket.$: $.flattenKeyResult.bucketName
                Key.$: $.flattenKeyResult.key
            Catch:
              - ErrorEquals:
                  - States.Runtime
                  - States.ItemReaderFailed
                  - States.NoSuchKey
                Next: HandleMissingInputFile
            End: true

          HandleMissingInputFile:
            Type: Pass
            Result: 'Failed: The SUCCEEDED_0.json file from the Enricher stage was not found in S3. This likely means the enrichment process did not complete successfully or no valid entitlements were processed.'
            End: true

    blogTopicGeneratorEnricher:
      name: blog-topic-generator-enricher-${self:provider.stage}
      role: !GetAtt StepFunctionsExecutionRole.Arn
      definition:
        Comment: 'Part 1: Enriches and flattens entitlements in parallel.'
        StartAt: InitializeExecutionInput
        States:
          InitializeExecutionInput:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !GetAtt InitializeExecutionInput.Arn
              Payload.$: $
            OutputPath: $.Payload
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 1
                MaxAttempts: 3
                BackoffRate: 2
            Next: FetchEntitlements
          FetchEntitlements:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !GetAtt FetchEntitlements.Arn
              Payload:
                executionName.$: $$.Execution.Name
                executionStartTime.$: $$.Execution.StartTime
                dryRun.$: $.dryRun
                companyIds.$: $.companyIds
            ResultSelector:
              bucketName.$: $.Payload.bucketName
              key.$: $.Payload.key
              validEntitlements.$: $.Payload.validEntitlements
              executionStartTime.$: $.Payload.executionStartTime
              executionFolderName.$: $.Payload.executionFolderName
            ResultPath: $.fetchResult
            Retry:
              - ErrorEquals:
                  - Lambda.ServiceException
                  - Lambda.AWSLambdaException
                  - Lambda.SdkClientException
                  - Lambda.TooManyRequestsException
                IntervalSeconds: 1
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
            Next: CheckEntitlementsCount

          CheckEntitlementsCount:
            Type: Choice
            Choices:
              - Variable: $.fetchResult.bucketName
                IsPresent: false
                Next: NoEntitlementsFound
              - Variable: $.fetchResult.validEntitlements
                NumericEquals: 0
                Next: NoEntitlementsFound
            Default: MapEntitlements

          NoEntitlementsFound:
            Type: Pass
            Result: 'No valid entitlements found - skipping processing'
            End: true

          MapEntitlements:
            Type: Map
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket.$: $.fetchResult.bucketName
                Key.$: $.fetchResult.key
            MaxConcurrency: 15
            ResultWriter:
              Resource: 'arn:aws:states:::s3:putObject'
              Parameters:
                Bucket: !Ref BlogTopicGeneratorBucket
                Prefix.$: States.Format('blog-topic-jobs/{}/{}',
                  States.ArrayGetItem(States.StringSplit($.fetchResult.executionStartTime, 'T'), 0),
                  $.fetchResult.executionFolderName)
              WriterConfig:
                Transformation: FLATTEN
                OutputType: JSON
            ItemSelector:
              executionName.$: $$.Execution.Name
              executionStartTime.$: $$.Execution.StartTime
              dryRun.$: $.dryRun
              entitlement.$: $$.Map.Item.Value
            ItemProcessor:
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
              StartAt: EnrichEntitlements
              States:
                EnrichEntitlements:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt EnrichEntitlements.Arn
                    Payload.$: $
                  ResultSelector:
                    enrichedWithNeighborhoods.$: $.Payload
                    executionName.$: $$.Execution.Input.executionName
                    executionStartTime.$: $$.Execution.Input.executionStartTime
                    dryRun.$: $$.Execution.Input.dryRun
                  ResultPath: $
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.Timeout']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                  Next: GetBrandProfile

                GetBrandProfile:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt GetBrandProfile.Arn
                    Payload.$: $
                  ResultSelector:
                    enrichedWithBrandProfile.$: $.Payload
                    executionName.$: $$.Execution.Input.executionName
                    executionStartTime.$: $$.Execution.Input.executionStartTime
                    dryRun.$: $$.Execution.Input.dryRun
                  ResultPath: $
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.Timeout']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                  Next: GetKeywordMap

                GetKeywordMap:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt GetKeywordMap.Arn
                    Payload.$: $
                  ResultSelector:
                    enrichedWithKeywordMaps.$: $.Payload
                    executionName.$: $$.Execution.Input.executionName
                    executionStartTime.$: $$.Execution.Input.executionStartTime
                    dryRun.$: $$.Execution.Input.dryRun
                  ResultPath: $
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.Timeout']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                  Next: FlattenPerNeighborhood

                FlattenPerNeighborhood:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt FlattenPerNeighborhood.Arn
                    Payload:
                      entitlement.$: $.enrichedWithKeywordMaps
                      executionName.$: $.executionName
                      executionStartTime.$: $.executionStartTime
                      dryRun.$: $.dryRun
                  ResultSelector:
                    success.$: $.Payload.success
                    totalFlattenedItems.$: $.Payload.totalFlattenedItems
                    bucketName.$: $.Payload.bucketName
                    key.$: $.Payload.key
                  ResultPath: $.flatten
                  Retry:
                    - ErrorEquals:
                        - Lambda.ServiceException
                        - Lambda.AWSLambdaException
                        - Lambda.SdkClientException
                        - Lambda.TooManyRequestsException
                      IntervalSeconds: 1
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.Timeout']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CatchFailureIterator
                  Next: CheckFlattenedItems

                CheckFlattenedItems:
                  Type: Choice
                  Choices:
                    - Variable: $.flatten.totalFlattenedItems
                      NumericEquals: 0
                      Next: NoFlattenedItems
                    - Variable: $.dryRun
                      BooleanEquals: true
                      Next: DryRunCompleted
                  Default: ProcessingCompleted

                NoFlattenedItems:
                  Type: Pass
                  Result: null
                  End: true

                DryRunCompleted:
                  Type: Succeed
                  Comment: "Dry-run completed: skipping topic generation"

                ProcessingCompleted:
                  Type: Succeed
                  Comment: "Successfully processed entitlement with neighborhoods, brand profile, and keyword map"

                CatchFailureIterator:
                  Type: Task
                  Resource: arn:aws:states:::lambda:invoke
                  Parameters:
                    FunctionName: !GetAtt CatchFailure.Arn
                    Payload.$: $
                  OutputPath: $.Payload
                  End: true
            ResultPath: $.mapResult
            Next: StartProcessorStateMachine

          StartProcessorStateMachine:
            Type: Task
            Resource: arn:aws:states:::states:startExecution
            Parameters:
              StateMachineArn:
                Fn::Sub: arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:stepFunctions.stateMachines.blogTopicGeneratorProcessor.name}
              Input:
                bucketName.$: $.mapResult.ResultWriterDetails.Bucket
                key.$: $.mapResult.ResultWriterDetails.Key
                dryRun.$: $.dryRun
                originalExecutionName.$: $$.Execution.Name
                originalExecutionStartTime.$: $$.Execution.StartTime
                AWS_STEP_FUNCTIONS_STARTED_BY_EXECUTION_ID.$: $$.Execution.Id
            Retry:
              - ErrorEquals:
                  - States.ExecutionLimitExceeded
                  - States.ExecutionAlreadyExists
                  - States.InvalidArn
                  - States.InvalidDefinition
                  - States.InvalidExecutionInput
                  - States.InvalidName
                  - States.StateMachineDeleting
                  - States.StateMachineDoesNotExist
                IntervalSeconds: 1
                MaxAttempts: 3
                BackoffRate: 2
            End: true
          
          CatchFailure:
            Type: Task
            Resource: arn:aws:states:::lambda:invoke
            Parameters:
              FunctionName: !GetAtt CatchFailure.Arn
              Payload.$: $
            OutputPath: $.Payload
            End: true

resources:
  Resources:
    StepFunctionsExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StepFunctionsExecutionPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                  Resource:
                    - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-${self:provider.stage}-*
                - Effect: Allow
                  Action:
                    - s3:GetObject
                    - s3:PutObject
                    - s3:DeleteObject
                    - s3:ListBucket
                  Resource:
                    - !GetAtt BlogTopicGeneratorBucket.Arn
                    - !Sub '${BlogTopicGeneratorBucket.Arn}/*'
                # Allow listing scheduler execution bucket
                - Effect: Allow
                  Action:
                    - s3:ListBucket
                  Resource:
                    - arn:aws:s3:::lp-bloom-scheduler-execution-${self:provider.stage}
                # Allow reading objects from scheduler execution bucket
                - Effect: Allow
                  Action:
                    - s3:GetObject
                  Resource:
                    - arn:aws:s3:::lp-bloom-scheduler-execution-${self:provider.stage}/*
                - Effect: Allow
                  Action:
                    - states:StartExecution
                    - states:DescribeExecution
                    - states:StopExecution
                  Resource:
                    - !Sub arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:*
                - Effect: Allow
                  Action:
                    - xray:PutTraceSegments
                    - xray:PutTelemetryRecords
                  Resource: '*'

    BlogTopicGeneratorBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: blog-topic-generator-${self:provider.stage}
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        BucketEncryption:
          ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
        VersioningConfiguration:
          Status: Enabled
