# Fetch Entitlements Lambda

## Overview

The `fetch-entitlements` lamb<PERSON> is responsible for fetching entitlements by product from the tenant service, validating them against start_date and end_date fields, and writing valid entitlements to S3 for further processing.

## Features

- **Product-based Entitlement Fetching**: Fetches entitlements for a specific product ID
- **Date Validation**: Validates entitlements against start_date and end_date fields
- **Feature Flag Validation**: Uses existing validation services for feature flags
- **S3 Storage**: Writes valid entitlements to S3 with metadata
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Error Handling**: Robust error handling with proper error propagation

## Input Parameters

The lambda accepts the following input parameters:

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `productId` | string | No | `AI_BLOG_SPECIALIST_PRODUCT_ID` | Product ID to fetch entitlements for |
| `bucketName` | string | No | `blog-topic-generator-{ENVIRONMENT}` | S3 bucket name to write entitlements to |
| `s3Key` | string | No | Auto-generated | S3 key path for the entitlements file |

## Output

The lambda returns a JSON object with the following structure:

```json
{
  "success": true,
  "totalEntitlements": 100,
  "validEntitlements": 85,
  "s3Location": "s3://blog-topic-generator-dev/entitlements/2024-01-15/valid-entitlements-1705267200000.json"
}
```

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Whether the operation completed successfully |
| `totalEntitlements` | number | Total number of entitlements fetched |
| `validEntitlements` | number | Number of entitlements that passed validation |
| `s3Location` | string | S3 location where valid entitlements were written |
| `error` | string | Error message (only present if success is false) |

## Date Validation Logic

The lambda performs the following date validations:

1. **Start Date Validation**: Entitlements with a `startDate` in the future are filtered out
2. **End Date Validation**: Entitlements with an `endDate` in the past are filtered out
3. **Null End Date**: Entitlements with `null` endDate are considered valid (no expiration)

### Validation Rules

- `startDate <= current_date` (entitlement must have started)
- `endDate > current_date` OR `endDate IS NULL` (entitlement must not have expired)

## S3 Output Format

The lambda writes entitlements to S3 in the following JSON format:

```json
{
  "metadata": {
    "generatedAt": "2024-01-15T10:30:00.000Z",
    "productId": "ai-blog-specialist",
    "totalEntitlements": 100,
    "validEntitlements": 85,
    "environment": "development"
  },
  "entitlements": [
    {
      "displayId": "ent-1",
      "companyId": "company-1",
      "productId": "ai-blog-specialist",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-12-31T23:59:59Z",
      "units": 10,
      "product": {
        "id": "ai-blog-specialist",
        "name": "AI Blog Specialist"
      },
      "company": {
        "website": "https://example.com",
        "name": "Example Company"
      }
    }
  ]
}
```

## Usage Examples

### Basic Usage (Default Parameters)

```json
{}
```

This will use the default product ID from configuration and auto-generate S3 bucket and key.

### Custom Product ID

```json
{
  "productId": "custom-product-id"
}
```

### Full Custom Configuration

```json
{
  "productId": "ai-blog-specialist",
  "bucketName": "my-custom-bucket",
  "s3Key": "custom/path/entitlements.json"
}
```

## Configuration

The lambda uses the following environment variables:

| Variable | Description | Required |
|----------|-------------|----------|
| `AI_BLOG_SPECIALIST_PRODUCT_ID` | Default product ID for entitlements | Yes |
| `TENANT_SERVICE_URL` | URL of the tenant service | Yes |
| `ENTITLEMENTS_LIMIT` | Maximum number of entitlements to fetch | No |
| `ENTITLEMENTS_CHUNK_SIZE` | Size of entitlement chunks | No |
| `REGION` | AWS region | No |
| `ENVIRONMENT` | Environment name | No |

## Error Handling

The lambda handles the following error scenarios:

1. **Tenant Service Errors**: Network errors, API errors, or invalid responses
2. **S3 Upload Errors**: Permission errors, bucket not found, or upload failures
3. **Configuration Errors**: Missing required environment variables
4. **Validation Errors**: Errors during entitlement validation

All errors are logged with detailed context and re-thrown to trigger Lambda retry mechanisms.

## Monitoring

The lambda provides comprehensive logging for monitoring:

- **Start/End Logs**: Logs when the lambda starts and completes
- **Fetch Logs**: Logs the number of entitlements fetched
- **Validation Logs**: Logs validation results and counts
- **S3 Logs**: Logs S3 upload details and locations
- **Error Logs**: Detailed error logging with context

## Testing

The lambda includes comprehensive unit tests covering:

- Basic functionality with default parameters
- Custom parameter handling
- Date validation logic
- Error scenarios
- S3 upload functionality

Run tests with:

```bash
npm test
```

## Integration with State Machine

This lambda is the first step in the blog topic generator state machine:

1. **Fetch Entitlements** (this lambda)
2. **Enrich Entitlements**
3. **Map Processing** (distributed processing of each entitlement)

The S3 output from this lambda serves as input for the subsequent steps in the workflow.
