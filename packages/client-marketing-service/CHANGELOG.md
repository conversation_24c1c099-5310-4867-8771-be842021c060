# client-marketing-service

## 11.16.0

### Minor Changes

- [#888](https://github.com/luxurypresence/seo-automation/pull/888) [`f0c50b6`](https://github.com/luxurypresence/seo-automation/commit/f0c50b674bdb5c080f01d32edd5e6b4110b3a34f) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Enhance campaign orchestration with improved notifications and simplified DTOs

  - Fix campaign metadata field GraphQL type from String to JSONObject
  - Simplify CreateCampaignWithPaymentDto by removing unused fields
  - Fix Linear API authentication (remove Bearer prefix)
  - Integrate NotificationContext in campaign services for comprehensive notifications
  - Add debug logging for Linear API interactions
  - Update tests to support new notification infrastructure

## 11.15.1

### Patch Changes

- [#896](https://github.com/luxurypresence/seo-automation/pull/896) [`6cee21a`](https://github.com/luxurypresence/seo-automation/commit/6cee21ae306085333b8e9d7f350f77a56f5753c2) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix casing of enum to avoid schema and DB enumn mismatch

## 11.15.0

### Minor Changes

- [#875](https://github.com/luxurypresence/seo-automation/pull/875) [`7ff7cfb`](https://github.com/luxurypresence/seo-automation/commit/7ff7cfb8f0681129da9a8b7c8cdfaa8e4de2a3f7) Thanks [@elisabethgross](https://github.com/elisabethgross)! - SEO-938: Remove PromoOrchestratorService and modernize strategy pattern

  - **Removed Legacy Service**: Deleted PromoOrchestratorService class and its wrapper methods, eliminating an unnecessary abstraction layer
  - **Direct Service Injection**: Updated BaseOrchestrationStrategy to inject CampaignPaymentService directly instead of through wrapper
  - **Modernized Strategies**: Refactored FullOrchestrationStrategy to use BundleLifecycleService directly for bundle operations
  - **Improved Validation**: Implemented proper campaign validation logic within the base strategy class
  - **Enhanced Testing**: Added comprehensive test coverage for the refactored strategy pattern with proper dependency injection
  - **Code Organization**: Improved import ordering, formatting, and eliminated technical debt

  This refactoring completes the strategy pattern modernization, reducing complexity and improving maintainability by removing intermediary abstraction layers.

## 11.14.0

### Minor Changes

- [#865](https://github.com/luxurypresence/seo-automation/pull/865) [`3ffbcd5`](https://github.com/luxurypresence/seo-automation/commit/****************************************) Thanks [@elisabethgross](https://github.com/elisabethgross)! - SEO-937: Centralize retry and failure recovery logic

  This change introduces a centralized RetryService to consolidate retry logic across the client-marketing-service package. This is the foundation for Phase 4 of our reliability improvements.

  ## What's New

  ### Core RetryService

  - **Simple retry mechanism**: Implements a basic retry pattern that attempts operations up to a configurable number of times
  - **Configurable attempts**: Supports configuration through both service parameters and environment variables
  - **Contextual logging**: Provides clear logging with optional context for debugging failed operations
  - **Alpha implementation**: Intentionally simplified for initial rollout - just retry X times then fail

  ### Integration with Campaign Watchdog

  - **Replaced manual retry logic**: The campaign watchdog now uses the centralized RetryService instead of its own retry implementation
  - **Atomic operations preserved**: Maintains the existing atomic increment pattern to prevent race conditions
  - **Consistent error handling**: Failed campaigns are properly marked with ERROR status after all retries are exhausted

  ## Technical Details

  The RetryService is designed as a simple, generic service that can be injected into any NestJS component:

  - Accepts async functions of any type through generics
  - Returns the result on success or throws the last error after all attempts fail
  - Logs retry attempts with appropriate severity levels (warn for retries, error for final failure)
  - No exponential backoff or complex retry strategies in this alpha version

  ## Migration Path

  This change is backward compatible. The campaign watchdog maintains the same external behavior while using the new centralized retry logic internally. The retry configuration defaults to 3 attempts if not specified.

  ## Testing

  Comprehensive test coverage includes:

  - Basic retry functionality with configurable attempts
  - Error propagation after all retries are exhausted
  - Configuration precedence (parameter > environment > default)
  - Integration with campaign watchdog service

## 11.13.1

### Patch Changes

- [#863](https://github.com/luxurypresence/seo-automation/pull/863) [`1f67219`](https://github.com/luxurypresence/seo-automation/commit/1f672193b954709135cd153b4f5ea5e8d2beb1c9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - [SEO-936] Phase 3: Remove legacy orchestration code

  This change completes Phase 3 of the campaign operations consolidation effort by removing the unused legacy orchestration code and improving the service documentation.

  ## Changes included:

  ### Code Removal

  - Removed the deprecated `orchestratePromoWorkflow()` method that was replaced by the strategy pattern
  - Cleaned up unused imports (CampaignStatus enum)

  ### Test Improvements

  - Added comprehensive test coverage for all PromoOrchestratorService methods
  - Added tests documenting that methods like `validateCampaign` and `publishPromoBundle` are no-op stubs maintained for backward compatibility
  - Updated test to verify the legacy method has been successfully removed

  ### Documentation Updates

  - Enhanced JSDoc comments to clearly explain the service's role as a helper for strategy implementations
  - Documented which methods are actively used by strategies vs deprecated
  - Added migration guidance for the deprecated `generatePromoBundle` method
  - Clarified that the service now supports the strategy pattern rather than orchestrating workflows directly

  ## Impact

  - No breaking changes - all actively used helper methods remain intact
  - Improves code maintainability by removing 65+ lines of unused code
  - Provides clear documentation for future developers about the service's purpose
  - Completes Phase 3 of the 5-phase legacy code cleanup initiative (SEO-933)

  ## Next Steps

  - Phase 4 and 5 can proceed to remove additional legacy code as identified
  - Consider eventually removing the deprecated `generatePromoBundle` wrapper method in a future phase

## 11.13.0

### Minor Changes

- [#829](https://github.com/luxurypresence/seo-automation/pull/829) [`e7acef3`](https://github.com/luxurypresence/seo-automation/commit/e7acef30f94f800b15644b7302aee5c0553bee27) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add blogTopicSelector to fetch random fully processed blog topics
  enhanced to prioritize preferredNeighborhoods optional input

## 11.12.4

### Patch Changes

- [#844](https://github.com/luxurypresence/seo-automation/pull/844) [`d47110b`](https://github.com/luxurypresence/seo-automation/commit/d47110b860d0219be6d5ac36e8cd2e550fcd49ad) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates to graphite hooks for auto changeset creation

## 11.12.3

### Patch Changes

- [#820](https://github.com/luxurypresence/seo-automation/pull/820) [`f5273ae`](https://github.com/luxurypresence/seo-automation/commit/f5273ae90f19b17c87ae5a0cc4a0f8539ba9c197) Thanks [@elisabethgross](https://github.com/elisabethgross)! - SEO-935: Improve BundleLifecycleService test mocking patterns

  - Replace global AWS SDK mocks with targeted jest.spyOn approach
  - Improve test isolation by mocking private S3Client and SQSClient methods directly
  - Remove unnecessary global mock setup that could interfere with other tests
  - Maintain test coverage while following better mocking practices

## 11.12.2

### Patch Changes

- [#792](https://github.com/luxurypresence/seo-automation/pull/792) [`cfbb661`](https://github.com/luxurypresence/seo-automation/commit/cfbb661fb9aa5582ae6a58f4a012f23fa59334d1) Thanks [@jaycholland](https://github.com/jaycholland)! - remove the locking when finding draft pending actions. This is a bandaid fix to allow draft-action-consumer lambda to timeout and rerun and find more DRAFT_PENDING actions to process.

## 11.12.1

### Patch Changes

- [#817](https://github.com/luxurypresence/seo-automation/pull/817) [`652a1c7`](https://github.com/luxurypresence/seo-automation/commit/652a1c780a961efbb32014552e8cf77de3cf3de6) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates graphite docs

## 11.12.0

### Minor Changes

- [#808](https://github.com/luxurypresence/seo-automation/pull/808) [`3d89813`](https://github.com/luxurypresence/seo-automation/commit/3d89813575a44d1119f5a6e22d4f2f65daea0c6f) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat(payment): migrate campaign.service.ts to use CampaignPaymentService

  - Refactored campaign.service.ts to use the new CampaignPaymentService
  - Updated all payment-related logic to go through the unified service
  - Ensured backward compatibility with existing payment flows
  - Fixed test suite to properly mock CampaignPaymentService dependency

## 11.11.0

### Minor Changes

- [#814](https://github.com/luxurypresence/seo-automation/pull/814) [`f39829e`](https://github.com/luxurypresence/seo-automation/commit/f39829e99ecaf56b3b4f9df9f39e7670c7b6f5c5) Thanks [@jaycholland](https://github.com/jaycholland)! - added paginated query for surfaced and publishe groups

## 11.10.1

### Patch Changes

- [#813](https://github.com/luxurypresence/seo-automation/pull/813) [`ef76efb`](https://github.com/luxurypresence/seo-automation/commit/ef76efb92cbb5d54aa7a4b81ce06917f87ac42b3) Thanks [@jaycholland](https://github.com/jaycholland)! - added schema for Get surfaced and published groups

## 11.10.0

### Minor Changes

- [#805](https://github.com/luxurypresence/seo-automation/pull/805) [`a8607dd`](https://github.com/luxurypresence/seo-automation/commit/a8607dda3299444ff27daa64b1cefcd5d8929166) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat(payment): Unify payment operations with CampaignPaymentService

  - Create unified CampaignPaymentService to consolidate payment operations
  - Migrate campaign.service.ts to use the new payment service
  - Migrate promo-orchestrator.service.ts to use the new payment service
  - Standardize payment record structure with PaymentResult interface
  - Add stubs for retry/refund operations (alpha phase - test gateway only)
  - Reduce code duplication and improve maintainability

## 11.9.0

### Minor Changes

- [#811](https://github.com/luxurypresence/seo-automation/pull/811) [`89eb7a0`](https://github.com/luxurypresence/seo-automation/commit/89eb7a08346d3a0be375d0e112b11f2882cfa7ec) Thanks [@jaycholland](https://github.com/jaycholland)! - New method for efficient querying findSurfacedAndPublishedGroupsForRanking

## 11.8.2

### Patch Changes

- [#809](https://github.com/luxurypresence/seo-automation/pull/809) [`75c8b25`](https://github.com/luxurypresence/seo-automation/commit/75c8b2559c2cce80c16e102647e5863a21e085bb) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes migration 1757800000000-add-campaign-metadata-column

## 11.8.1

### Patch Changes

- [#796](https://github.com/luxurypresence/seo-automation/pull/796) [`47c5c1f`](https://github.com/luxurypresence/seo-automation/commit/47c5c1f391178d370f74f2a7990141a459124771) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add priority to Linear tickets based on campaign value

  High-value campaigns ($5000+) are marked as urgent priority in Linear, while others are set to normal priority. This ensures proper prioritization of campaign execution tasks.

## 11.8.0

### Minor Changes

- [#795](https://github.com/luxurypresence/seo-automation/pull/795) [`3bb6622`](https://github.com/luxurypresence/seo-automation/commit/****************************************) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Implement Campaign Orchestration Strategy Pattern (SEO-922)

  - Add flexible orchestration modes (FULL, PAYMENT_ONLY, MOCK) via Strategy Pattern
  - Connect PromoOrchestratorService to CampaignService with configurable workflows
  - Add environment-based configuration via CAMPAIGN_ORCHESTRATION_MODE
  - Add metadata column to campaign entity for orchestration audit trail
  - Add testCardNumber support for payment testing scenarios
  - Implement comprehensive error handling and atomic transaction management
  - Maintain backward compatibility with existing GraphQL API
  - Add BaseOrchestrationStrategy for code deduplication and maintainability

  **Breaking Changes**: None - fully backward compatible

  **Configuration Required**:

  - Set `CAMPAIGN_ORCHESTRATION_MODE` environment variable (defaults to 'full')
  - Run database migration to add metadata column

  **Testing**:

  - Use `testCardNumber` field in createCampaignWithPayment mutation for payment testing
  - Set orchestration mode to 'mock' for development (no external calls)
  - Set orchestration mode to 'payment-only' for testing payment flows only

## 11.7.0

### Minor Changes

- [#790](https://github.com/luxurypresence/seo-automation/pull/790) [`467c72b`](https://github.com/luxurypresence/seo-automation/commit/467c72bfc8d95bcd441ea43d7365f791515cf2ec) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Wire up FakeGateway to PromoOrchestratorService for payment gateway integration

  - Integrate FakeGateway payment processor with PromoOrchestratorService
  - Configure PaymentModule with CampaignModule integration
  - Add comprehensive tests for payment processing workflows
  - Support payment creation and checkout URL generation through PromoOrchestratorService

## 11.6.1

### Patch Changes

- [#787](https://github.com/luxurypresence/seo-automation/pull/787) [`5fcc0bd`](https://github.com/luxurypresence/seo-automation/commit/5fcc0bd18298b7e44ac4545097db1f8ff2df28f2) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Clean up PaymentService architecture by consolidating payment creation logic

  - Remove PaymentFactory class and integrate methods directly into PaymentService
  - Remove unused PaymentProcessorFactory references from tests and module
  - Simplify architecture while maintaining all existing functionality

## 11.6.0

### Minor Changes

- [#785](https://github.com/luxurypresence/seo-automation/pull/785) [`6fe0a96`](https://github.com/luxurypresence/seo-automation/commit/6fe0a964a20d6febe5395274c1a35dfed4bf94cf) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-908: Adds type field on Blog Topic

  ## Summary

  Added a new `type` field to blog topics to distinguish between `article` and `listicle` content types.

  ## Changes Made

  ### Database

  - **Migration**: Added `type` column to `blog_topic` table with enum values `('article', 'listicle')`
  - **Default**: Existing rows default to `'article'` for backward compatibility

  ### API Layer

  - **Entity**: Added `type` field with enum validation
  - **DTOs**:
    - `CreateBlogTopicInput`: Required `type` field
    - `UpdateBlogTopicInput`: Optional `type` field
    - `BlogTopicFiltersInput`: Added `type` filter support

  ### Service Layer

  - **BlogTopicService**: Updated `createBulk` and filtering logic
  - **BlogTopicGenerator**: Updated types and lambda to pass `type` field

  ### Testing

  - Updated unit tests for service and resolver layers
  - Added test coverage for `type` field functionality

  ## Impact

  - Enables content type classification for better content management
  - Maintains backward compatibility with existing data
  - Provides filtering capabilities for content type queries

## 11.5.4

### Patch Changes

- [#783](https://github.com/luxurypresence/seo-automation/pull/783) [`7e0a427`](https://github.com/luxurypresence/seo-automation/commit/7e0a4271cb9e25e7714408d311ada037a322668d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add ORM-level unique constraints to Payment entity

  - Added @Unique decorators to match database-level constraints:
    - Unique constraint on (provider, providerRef) for payment provider references
    - Unique constraint on (parentPaymentId, attemptNumber) for payment retry attempts
  - Added missing idx_payment_parent_attempt_latest index decorator
  - Ensures consistency between database migrations and TypeORM entity definitions

## 11.5.3

### Patch Changes

- [#782](https://github.com/luxurypresence/seo-automation/pull/782) [`2072a0e`](https://github.com/luxurypresence/seo-automation/commit/2072a0e704f76d3a3ecd9f7e91be5b12cb40a721) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix payment-campaign foreign key constraint to match ORM entity definition

  - Add migration to change payment.campaign_id foreign key from ON DELETE SET NULL to ON DELETE CASCADE
  - Ensures database constraint matches Payment entity's onDelete: 'CASCADE' definition
  - Fixes potential ORM caching issues and ensures proper cascade deletion of payments when campaigns are deleted

## 11.5.2

### Patch Changes

- [#779](https://github.com/luxurypresence/seo-automation/pull/779) [`ade60b1`](https://github.com/luxurypresence/seo-automation/commit/ade60b193f1044880a487912f1257c3bf31eb927) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Moves api-gateway to src/common/services

## 11.5.1

### Patch Changes

- [#778](https://github.com/luxurypresence/seo-automation/pull/778) [`a2bad73`](https://github.com/luxurypresence/seo-automation/commit/a2bad7386c8b7b9c79b615280c0658d02d9288dc) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removess all deprecated stuff

## 11.5.0

### Minor Changes

- [#777](https://github.com/luxurypresence/seo-automation/pull/777) [`a85a97d`](https://github.com/luxurypresence/seo-automation/commit/a85a97d3a05867da21f51fd0ab6bcd11fff3e051) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add brand profile history tracking using repository pattern

### Patch Changes

- [#776](https://github.com/luxurypresence/seo-automation/pull/776) [`0ee0abf`](https://github.com/luxurypresence/seo-automation/commit/0ee0abffb9e3c95d844a5a19cfc329faa4f30dd3) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add Serena AI configuration for enhanced development workflow

## 11.4.1

### Patch Changes

- [#773](https://github.com/luxurypresence/seo-automation/pull/773) [`e1c141f`](https://github.com/luxurypresence/seo-automation/commit/e1c141ff2be7ee1bda72cb0ea4daae9f85f84377) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix unsurfaceScheduledAction to reset publishing timestamps

  When unsurfacing a scheduled action, now properly sets publishedAt and scheduledToBePublishedAt to null in addition to surfacedAt. This ensures that unsurfaced actions don't retain stale publishing information.

  References SEO-905

## 11.4.0

### Minor Changes

- [#772](https://github.com/luxurypresence/seo-automation/pull/772) [`6c304f4`](https://github.com/luxurypresence/seo-automation/commit/6c304f44b998fd4e1d58aeefbfb2e6e6b419bd31) Thanks [@devinellis](https://github.com/devinellis)! - Add unsurfaceScheduledAction mutation to reverse mistakenly surfaced SEO content

## 11.3.0

### Minor Changes

- [#769](https://github.com/luxurypresence/seo-automation/pull/769) [`7c63cdf`](https://github.com/luxurypresence/seo-automation/commit/7c63cdf80b5027a9a30de65ab5943361f687c125) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add unsurfaceScheduledAction mutation to reverse mistakenly surfaced SEO content

## 11.2.2

### Patch Changes

- [#768](https://github.com/luxurypresence/seo-automation/pull/768) [`904502d`](https://github.com/luxurypresence/seo-automation/commit/904502d01ecff6343cbf78b7dfe378d24ec6d0ef) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix GraphQL federation errors by removing conflicting field definitions

  - Remove 'id' from @requires directive as it's already provided by this subgraph
  - Convert Media and Agent to minimal extended entities to avoid field sharing conflicts
  - Remove media and agents field resolvers from Property resolver to prevent conflicts with cms_service
  - Clean up related test cases and unused imports

## 11.2.1

### Patch Changes

- [#767](https://github.com/luxurypresence/seo-automation/pull/767) [`400af78`](https://github.com/luxurypresence/seo-automation/commit/400af7887e066aabd824ab3bab19d9078f0db20e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix GraphQL injection error on deploy by removing direct GRAPHQL_ENDPOINT injection and using ConfigService instead

## 11.2.0

### Minor Changes

- [#762](https://github.com/luxurypresence/seo-automation/pull/762) [`42093e3`](https://github.com/luxurypresence/seo-automation/commit/42093e32a3e33ee31a7d05e0c6326ca7d73e554d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add media collections and agent information to property GraphQL responses

  - Add Media and Agent GraphQL types with comprehensive field coverage
  - Integrate with CMS service to fetch property media and agent data
  - Add resolver fields for media and agents on Property type
  - Support forCMS parameter for dashboard views
  - Media returned in original order as provided by CMS
  - Agents returned in original order as provided by CMS
  - Include comprehensive test coverage for new functionality

## 11.1.1

### Patch Changes

- [#760](https://github.com/luxurypresence/seo-automation/pull/760) [`b3d828d`](https://github.com/luxurypresence/seo-automation/commit/b3d828de84706b0acd93278b3cfc91037dbaf37a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat: implement core property data queries and DTO mapping for CMS

  - Add CMS_PROPERTY_FIELDS GraphQL fragment with all required fields for dashboard views
  - Implement GET_PROPERTIES_FOR_CMS bulk query with filters, pagination, and forCMS flag
  - Implement GET_PROPERTY_FOR_CMS single property query
  - Support for both MLS and manually added LP properties via forCMS flag
  - Complete field mapping including id, description, beds, baths, firstMedia, listing agent, brokerage, date listed, open house, and neighborhood
  - Minimal selection sets for optimal performance
  - Comprehensive unit tests with mocked GraphQL responses already in place

## 11.1.0

### Minor Changes

- [#759](https://github.com/luxurypresence/seo-automation/pull/759) [`c74d483`](https://github.com/luxurypresence/seo-automation/commit/c74d483e168abcea990b5cb2db271c47c306db48) Thanks [@fbionsc](https://github.com/fbionsc)! - - Update compose to use LP's pg image
  - Add new brand_profile_history table

## 11.0.0

### Major Changes

- [#757](https://github.com/luxurypresence/seo-automation/pull/757) [`5d62f98`](https://github.com/luxurypresence/seo-automation/commit/5d62f98dad5951b8acde37242de6d20d4362ded7) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Refactor Campaign–Payment coupling in client-marketing-service:

  - Replace campaign_payment join entity with direct one‑to‑many (Campaign → Payment)
  - Introduce append‑only payment attempts (attemptNumber, parentPaymentId)
  - Consolidate migrations into a single initial migration
  - Update services, repository queries, tests, and GraphQL schema

  This is a breaking change for consumers relying on the campaign_payment join.

  ## Migration notes

  - Run DB migrations after merge from packages/client-marketing-service:
    - from repo root: `pnpm --filter client-marketing-service typeorm:run`
  - If any legacy `campaign_payment` rows exist, backfill `payment.campaign_id` prior to dropping old artifacts. Example (PostgreSQL):
    ```sql
    UPDATE payment p
    SET campaign_id = cp.campaign_id
    FROM campaign_payment cp
    WHERE cp.payment_id = p.id AND p.campaign_id IS NULL;
    ```
  - Enforce attempt monotonicity/uniqueness at the DB layer (added in this PR):
    - unique index on `(parent_payment_id, attempt_number)` with a partial filter
    - `CHECK (attempt_number >= 1)` constraint
  - Enforce idempotency for provider callbacks (added in this PR):
    - partial unique index on `(provider, provider_ref)` where `provider_ref IS NOT NULL`
  - Add composite index to accelerate campaign timelines (added in this PR):
    - index on `(campaign_id, attempt_number, created_at)`

  ## API and runtime changes

  - Payment creation via `PaymentResolver.createPayment` is disabled at runtime and deprecated; create payments through the campaign workflow instead.
  - GraphQL: `Campaign.payments` replaces the former `CampaignPayment` surface. Update queries and mutations accordingly.
  - `PaymentProvider`: `STRIPE` remains in the schema for backward compatibility and is marked deprecated; only `TEST` is active.

## 10.20.0

### Minor Changes

- [#739](https://github.com/luxurypresence/seo-automation/pull/739) [`dcd26eb`](https://github.com/luxurypresence/seo-automation/commit/dcd26ebd74b58b46a25ce1d9ed905bfdba90f528) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-883: implement GraphQL client infrastructure for CMS supergraph

## 10.19.1

### Patch Changes

- [#731](https://github.com/luxurypresence/seo-automation/pull/731) [`863e9e2`](https://github.com/luxurypresence/seo-automation/commit/863e9e25928ea5bfdc07f183f1804c9cccf87f64) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix property campaign eligibility feature by implementing comprehensive GraphQL federation and authorization policies. This includes adding PropertyPolicy for proper authorization, integrating Property entity with GraphQL federation using orphaned types configuration, and refactoring resolver logic to eliminate duplicate code while maintaining proper access control.

## 10.19.0

### Minor Changes

- [#727](https://github.com/luxurypresence/seo-automation/pull/727) [`0b631c8`](https://github.com/luxurypresence/seo-automation/commit/0b631c83419f25104412e822e01344142669ad44) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add GraphQL CRUD operations for campaign, payment, and campaign_payment entities with resolvers, services, and DTOs

## 10.18.0

### Minor Changes

- [#706](https://github.com/luxurypresence/seo-automation/pull/706) [`be7010a`](https://github.com/luxurypresence/seo-automation/commit/be7010ada2abeb42d18b1d40ded4057175ae232c) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-855: Add CampaignBundle TypeScript interface and DTOs

## 10.17.0

### Minor Changes

- [#704](https://github.com/luxurypresence/seo-automation/pull/704) [`9b90e6c`](https://github.com/luxurypresence/seo-automation/commit/9b90e6c6f4a7db884040941303052a2fdc8c8816) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add AssetAggregator service class for SEO-854

## 10.16.1

### Patch Changes

- [#701](https://github.com/luxurypresence/seo-automation/pull/701) [`24ae53c`](https://github.com/luxurypresence/seo-automation/commit/24ae53c3a40a2480875b168c2f28c4fac957a60b) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement SQS/SNS integration for campaign handoff system

## 10.16.0

### Minor Changes

- [#699](https://github.com/luxurypresence/seo-automation/pull/699) [`eb8abb4`](https://github.com/luxurypresence/seo-automation/commit/eb8abb4491051806e76e2f84797aaf7fa00dd199) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add PromoOrchestratorService stub implementation for SEO-853

## 10.15.0

### Minor Changes

- [#698](https://github.com/luxurypresence/seo-automation/pull/698) [`c98f42d`](https://github.com/luxurypresence/seo-automation/commit/c98f42d9339fcf84eec6382153c01494e07a32b6) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement S3 upload functionality for CampaignBundles

  - Add AWS SDK S3 dependencies and S3Client initialization
  - Update configuration to include PROMO_S3_BUCKET environment variable
  - Implement createStubBundle method for basic campaign bundle object
  - Replace uploadBundle placeholder with real S3 upload logic using PutObjectCommand
  - Add environment-specific behavior: skip actual uploads in development
  - Update campaign.bundleUri with S3 URI on successful upload
  - Add comprehensive error handling to preserve retry capability
  - Add comprehensive test coverage for all scenarios

## 10.14.1

### Patch Changes

- [#691](https://github.com/luxurypresence/seo-automation/pull/691) [`3be5273`](https://github.com/luxurypresence/seo-automation/commit/3be5273a0ed9aecd0476c27233a8c0bc90c02b6d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add campaign watchdog cron service for retry handling

  - Implement CampaignWatchdogService with scheduled retry mechanism
  - Add CampaignHandoffService for bundle upload and publish operations
  - Use atomic database operations to prevent race conditions
  - Add overlap prevention for concurrent cron executions
  - Include @nestjs/schedule for cron scheduling support

## 10.14.0

### Minor Changes

- [#692](https://github.com/luxurypresence/seo-automation/pull/692) [`48e90d2`](https://github.com/luxurypresence/seo-automation/commit/48e90d2627689b32d758a96b5a8c5f857bdedeef) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add PaymentAdapter interface with FakeGateway strategy for payment simulation using Stripe test cards

## 10.13.1

### Patch Changes

- [#689](https://github.com/luxurypresence/seo-automation/pull/689) [`e74d401`](https://github.com/luxurypresence/seo-automation/commit/e74d401d5cb5ac630d0c6ec093498fb52b591c2a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Revert broken Property resolver

## 10.13.0

### Minor Changes

- [#679](https://github.com/luxurypresence/seo-automation/pull/679) [`66319f8`](https://github.com/luxurypresence/seo-automation/commit/66319f83923f52e259b135608b85e6c44f1d201c) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-786: Add campaign eligibility to Property type

## 10.12.0

### Minor Changes

- [#650](https://github.com/luxurypresence/seo-automation/pull/650) [`e9d95a8`](https://github.com/luxurypresence/seo-automation/commit/e9d95a8a230cd518cbbb8fc655ff07e122474ea4) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add initial database schema for campaigns and payments with TypeORM entities and GraphQL integration

## 10.11.3

### Patch Changes

- [#654](https://github.com/luxurypresence/seo-automation/pull/654) [`22b60b8`](https://github.com/luxurypresence/seo-automation/commit/22b60b8f25b632e6045affa9949cfbff18ccbc32) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add BrandProfile seeder implementation for populating test data

## 10.11.2

### Patch Changes

- [#649](https://github.com/luxurypresence/seo-automation/pull/649) [`fcc85db`](https://github.com/luxurypresence/seo-automation/commit/fcc85dbcc33698a44ac1e973851c6e93dac2bbee) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-758: Adds Blog Topic mutations

  ### Changelog

  ### Added

  - **Blog Topic Bulk Creation**: New `createBlogTopicsBulk` mutation for high-performance bulk topic creation
  - **Blog Topic Update**: New `updateBlogTopic` mutation for topic locking and metadata updates
  - **Performance Optimizations**: Batch processing with configurable chunk sizes (default: 100 items)
  - **Transaction Support**: Database transactions ensure data consistency during bulk operations
  - **Error Handling**: Graceful fallback to individual processing on chunk failures

## 10.11.1

### Patch Changes

- [#647](https://github.com/luxurypresence/seo-automation/pull/647) [`fb7139a`](https://github.com/luxurypresence/seo-automation/commit/fb7139aced977740c48cd834133fe4dcaeb02f1c) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-753: Adds Blog Topic queries

  ### Changelog

  Adds comprehensive BlogTopic functionality to track and manage blog topic generation process. Includes:

  - **New GraphQL types**: `BlogTopic` and `PaginatedBlogTopic` with filtering and pagination
  - **Entity & Repository**: TypeORM entity with proper indexing and constraints
  - **Service Layer**: CRUD operations with nestjs-paginate integration
  - **Resolver**: GraphQL queries with authorization middleware
  - **Policy**: Super user access control for all operations
  - **Validation**: Comprehensive input validation using class-validator
  - **Testing**: Full test coverage for resolver and service layers

## 10.11.0

### Minor Changes

- [#644](https://github.com/luxurypresence/seo-automation/pull/644) [`d6faf91`](https://github.com/luxurypresence/seo-automation/commit/d6faf91fa3ec999e4644880256cc8564ff0993a6) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-752: Adds Blog Topic entity

  ## Changelog

  ### Added

  - New `BlogTopic` entity for Super Bloom's blog topic generation feature
  - Database migration to create `blog_topic` table with proper schema
  - CSV-based seeder for populating blog topics from SuperBloom export data

  ### Technical

  - Foreign key constraint references `identity.company(display_id)`
  - Proper indexing on `company_id` for performance optimization

  ### Files Modified

  - `src/blog-topic/entities/blog-topic.entity.ts`
  - `src/migration/1755805505992-create-blog-topic-table.ts`
  - `src/seeders/blog-topic.seeder.ts`
  - `src/seeder.ts`

## 10.10.0

### Minor Changes

- [#643](https://github.com/luxurypresence/seo-automation/pull/643) [`c2c9bd6`](https://github.com/luxurypresence/seo-automation/commit/c2c9bd622c99d0c529cd539fb12dc12da0325093) Thanks [@fbionsc](https://github.com/fbionsc)! - Store "update by"

## 10.9.1

### Patch Changes

- [#642](https://github.com/luxurypresence/seo-automation/pull/642) [`ce5dec1`](https://github.com/luxurypresence/seo-automation/commit/ce5dec1e0275f05b23070e25b7f2727248115fa9) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix column names

## 10.9.0

### Minor Changes

- [#640](https://github.com/luxurypresence/seo-automation/pull/640) [`2e71162`](https://github.com/luxurypresence/seo-automation/commit/2e71162af1c9a6309d09a70057ccb8c41e7ad6ab) Thanks [@fbionsc](https://github.com/fbionsc)! - Add mutation to upsert brand profiles

## 10.8.1

### Patch Changes

- [#637](https://github.com/luxurypresence/seo-automation/pull/637) [`9676442`](https://github.com/luxurypresence/seo-automation/commit/9676442161df8a37b6249a905c570fc9e69a849b) Thanks [@fbionsc](https://github.com/fbionsc)! - Remove duplicate index migration

## 10.8.0

### Minor Changes

- [#632](https://github.com/luxurypresence/seo-automation/pull/632) [`beb89c6`](https://github.com/luxurypresence/seo-automation/commit/beb89c6e8cb7aba6cd72a38c986e9739add755b1) Thanks [@fbionsc](https://github.com/fbionsc)! - Add brand profile query

## 10.7.1

### Patch Changes

- [#636](https://github.com/luxurypresence/seo-automation/pull/636) [`5f4cf99`](https://github.com/luxurypresence/seo-automation/commit/5f4cf9922f441be94f3e97b90ca141c3c0a8eb85) Thanks [@fbionsc](https://github.com/fbionsc)! - - Fix default uuid extension
  - Replace broken brand profile migrations

## 10.7.0

### Minor Changes

- [#631](https://github.com/luxurypresence/seo-automation/pull/631) [`7459f12`](https://github.com/luxurypresence/seo-automation/commit/7459f124e13de96a9f7e25ac65a9db6fe0bf4c57) Thanks [@fbionsc](https://github.com/fbionsc)! - Add brand profile table

## 10.6.0

### Minor Changes

- [#630](https://github.com/luxurypresence/seo-automation/pull/630) [`e7fbdfc`](https://github.com/luxurypresence/seo-automation/commit/e7fbdfca029470cb86d430cfc5681db4006fe2cc) Thanks [@fbionsc](https://github.com/fbionsc)! - Update email to include link to contacts page filtered by leads

## 10.5.0

### Minor Changes

- [#629](https://github.com/luxurypresence/seo-automation/pull/629) [`8f33e0c`](https://github.com/luxurypresence/seo-automation/commit/8f33e0cd6767ffdc4af5509a04dae43fc22b006e) Thanks [@fbionsc](https://github.com/fbionsc)! - Filter out "all 0" ad reports

## 10.4.1

### Patch Changes

- [#628](https://github.com/luxurypresence/seo-automation/pull/628) [`b32bee8`](https://github.com/luxurypresence/seo-automation/commit/b32bee884540b1e86dbdf7b82a4b9d667ecd8162) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix blog post url

## 10.4.0

### Minor Changes

- [#627](https://github.com/luxurypresence/seo-automation/pull/627) [`085720c`](https://github.com/luxurypresence/seo-automation/commit/085720c2da50d22c355eb1bf0314ec8a0381530f) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds contacts page URL to be used in the UI and email

## 10.3.0

### Minor Changes

- [#622](https://github.com/luxurypresence/seo-automation/pull/622) [`006582d`](https://github.com/luxurypresence/seo-automation/commit/006582df2ddc4088c3c04d35ca0f5d220a8c9ef1) Thanks [@fbionsc](https://github.com/fbionsc)! - Add load testing script

## 10.2.2

### Patch Changes

- [#624](https://github.com/luxurypresence/seo-automation/pull/624) [`e683b69`](https://github.com/luxurypresence/seo-automation/commit/e683b692854914c06c04dbce2599ac70f9f7dc5e) Thanks [@jaycholland](https://github.com/jaycholland)! - added fields to main heading input to match output from save seo draft

## 10.2.1

### Patch Changes

- [#621](https://github.com/luxurypresence/seo-automation/pull/621) [`0236abd`](https://github.com/luxurypresence/seo-automation/commit/0236abd47a66f84c4ae22531a3f13968ddf2dcdf) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-692: Adds E2E Testing - Marketing - readonly account to Ads override

## 10.2.0

### Minor Changes

- [#619](https://github.com/luxurypresence/seo-automation/pull/619) [`80b84d4`](https://github.com/luxurypresence/seo-automation/commit/80b84d4f493acf50e8fc7d9480c23ad0827023bb) Thanks [@fbionsc](https://github.com/fbionsc)! - Fetch paid ad leads

## 10.1.2

### Patch Changes

- [#613](https://github.com/luxurypresence/seo-automation/pull/613) [`48a5eeb`](https://github.com/luxurypresence/seo-automation/commit/48a5eebd8683b092a1ff7375cf59dde73ce45cf3) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates owner of DD services to client-marketing

## 10.1.1

### Patch Changes

- [#601](https://github.com/luxurypresence/seo-automation/pull/601) [`31aa970`](https://github.com/luxurypresence/seo-automation/commit/31aa9703ec45fe8397f99c6ff76a94eeb44a7ca4) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix email blog links

## 10.1.0

### Minor Changes

- [#585](https://github.com/luxurypresence/seo-automation/pull/585) [`6f628b7`](https://github.com/luxurypresence/seo-automation/commit/6f628b752146e22bef5ad6685ce52c3c0c71d080) Thanks [@fbionsc](https://github.com/fbionsc)! - - Add "keyword" field to feed query

  - Fix subject line date formatting
  - Add "reply-to" parameter when sending emails
  - Adds several copy updates to email template
  - Updates utm parameters

- [#585](https://github.com/luxurypresence/seo-automation/pull/585) [`6f628b7`](https://github.com/luxurypresence/seo-automation/commit/6f628b752146e22bef5ad6685ce52c3c0c71d080) Thanks [@fbionsc](https://github.com/fbionsc)! - Update template

## 10.0.0

### Major Changes

- [#586](https://github.com/luxurypresence/seo-automation/pull/586) [`29a6d7c`](https://github.com/luxurypresence/seo-automation/commit/29a6d7caebb9a066983523ba4a3477a6d04b469d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates upcomingActions to only return actions with SURFACING_PENDING status

## 9.3.0

### Minor Changes

- [#583](https://github.com/luxurypresence/seo-automation/pull/583) [`c1f7d8a`](https://github.com/luxurypresence/seo-automation/commit/c1f7d8a6dc43b33e3a7d591e12bb4b72ae224977) Thanks [@jaycholland](https://github.com/jaycholland)! - Snowflake query with retries and additional logging

## 9.2.1

### Patch Changes

- [#584](https://github.com/luxurypresence/seo-automation/pull/584) [`6059b4b`](https://github.com/luxurypresence/seo-automation/commit/6059b4b9d5397d68643d72a8c17993c1f3421d21) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix TypeError when mainHeading is undefined in storeSEODraft recommendations processing

## 9.2.0

### Minor Changes

- [#582](https://github.com/luxurypresence/seo-automation/pull/582) [`87d8d6f`](https://github.com/luxurypresence/seo-automation/commit/87d8d6fd7b30c3fa71e65c28648335eadb9846b8) Thanks [@fbionsc](https://github.com/fbionsc)! - - Add "keyword" field to feed query
  - Fix subject line date formatting
  - Add "reply-to" parameter when sending emails
  - Adds several copy updates to email template
  - Updates utm parameters

## 9.1.0

### Minor Changes

- [#580](https://github.com/luxurypresence/seo-automation/pull/580) [`49c3998`](https://github.com/luxurypresence/seo-automation/commit/49c3998007380e2d90176955882ac8c24b55f6fb) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Make mainHeading field optional in RecommendationsInput for scheduled action resolver

## 9.0.0

### Major Changes

- [#550](https://github.com/luxurypresence/seo-automation/pull/550) [`d0bc389`](https://github.com/luxurypresence/seo-automation/commit/d0bc389932d4fa199223555f184a1af36c9f5670) Thanks [@devinellis](https://github.com/devinellis)! - mjml template fix

## 8.42.0

### Minor Changes

- [#576](https://github.com/luxurypresence/seo-automation/pull/576) [`f88c167`](https://github.com/luxurypresence/seo-automation/commit/f88c16738b5b1733711e7cf6e33462932d2e4c07) Thanks [@jaycholland](https://github.com/jaycholland)! - added e2e tests

## 8.41.0

### Minor Changes

- [#574](https://github.com/luxurypresence/seo-automation/pull/574) [`d6cd5f0`](https://github.com/luxurypresence/seo-automation/commit/d6cd5f0b2217be1424096054d4e36d6d6868419b) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds update_recommendation_status_updated_at trigger on status update

## 8.40.1

### Patch Changes

- [#566](https://github.com/luxurypresence/seo-automation/pull/566) [`a7470e0`](https://github.com/luxurypresence/seo-automation/commit/a7470e050dd5ac07f96f33f63afdb37e2e2be524) Thanks [@fbionsc](https://github.com/fbionsc)! - Update optimizations query to resolve only surfaced groups

## 8.40.0

### Minor Changes

- [#560](https://github.com/luxurypresence/seo-automation/pull/560) [`a690113`](https://github.com/luxurypresence/seo-automation/commit/a69011392336300578e9f493c257f72fd91cc8fd) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds "liveUrl" field to published blog posts (PoW feed)

## 8.39.2

### Patch Changes

- [#544](https://github.com/luxurypresence/seo-automation/pull/544) [`f8e52ec`](https://github.com/luxurypresence/seo-automation/commit/f8e52ec99ee5e8d7bf6363b8037fc2463ef69d3f) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix findUpcoming method to include SURFACING_PENDING status to prevent companies from exceeding action limits

## 8.39.1

### Patch Changes

- [#538](https://github.com/luxurypresence/seo-automation/pull/538) [`662cff0`](https://github.com/luxurypresence/seo-automation/commit/662cff0b004d55fcc9b68b01741acee4f3ca3f7e) Thanks [@diegosr90](https://github.com/diegosr90)! - Updates homepage-feed

  - Adds url to page object for FeedRecommendation
  - Updates fragment on scheduler

## 8.39.0

### Minor Changes

- [#537](https://github.com/luxurypresence/seo-automation/pull/537) [`fe8f5f0`](https://github.com/luxurypresence/seo-automation/commit/fe8f5f0dc3e84b4f085e8a960913d6bf12f88158) Thanks [@fbionsc](https://github.com/fbionsc)! - Replace entitlement date check with computed value

## 8.38.6

### Patch Changes

- [#536](https://github.com/luxurypresence/seo-automation/pull/536) [`a615e5b`](https://github.com/luxurypresence/seo-automation/commit/a615e5b1d96bff74ccbc95b4844b0ace8408c031) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add sigterm-stop logic

## 8.38.5

### Patch Changes

- [#534](https://github.com/luxurypresence/seo-automation/pull/534) [`e2114f3`](https://github.com/luxurypresence/seo-automation/commit/e2114f3c71ea2e75f2a33a8036593c3faf9e4387) Thanks [@jaycholland](https://github.com/jaycholland)! - Rejection reason now writing to the correct column

## 8.38.4

### Patch Changes

- [#532](https://github.com/luxurypresence/seo-automation/pull/532) [`ec407fc`](https://github.com/luxurypresence/seo-automation/commit/ec407fcba1a038ae7dbecb3d1494f901183fb3d2) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds resolved field for Group on Recommendation

## 8.38.3

### Patch Changes

- [#531](https://github.com/luxurypresence/seo-automation/pull/531) [`78ad27f`](https://github.com/luxurypresence/seo-automation/commit/78ad27f5a772df9fd7404ba52ac74f80ec319469) Thanks [@devinellis](https://github.com/devinellis)! - Add fake row for today's ads

## 8.38.2

### Patch Changes

- [#530](https://github.com/luxurypresence/seo-automation/pull/530) [`23a778b`](https://github.com/luxurypresence/seo-automation/commit/23a778b07ba4d283bf6de56eb4f40f6f9f813155) Thanks [@devinellis](https://github.com/devinellis)! - Add fake row for today's ads

## 8.38.1

### Patch Changes

- [#528](https://github.com/luxurypresence/seo-automation/pull/528) [`92079c2`](https://github.com/luxurypresence/seo-automation/commit/92079c25c9cf63b7c3871060c78b65ac1cd9402f) Thanks [@jaycholland](https://github.com/jaycholland)! - Sendgrid templating issue with .length. used greaterThan instead

## 8.38.0

### Minor Changes

- [#525](https://github.com/luxurypresence/seo-automation/pull/525) [`447ec5a`](https://github.com/luxurypresence/seo-automation/commit/447ec5a0531dbe7df1e120d2694df915616fd46a) Thanks [@jaycholland](https://github.com/jaycholland)! - Added Rank to GroupMetadata, type Int, deprecated metadata.ranking

## 8.37.3

### Patch Changes

- [#494](https://github.com/luxurypresence/seo-automation/pull/494) [`9300cca`](https://github.com/luxurypresence/seo-automation/commit/9300cca6b7f440bb0934d966b21e6ae83d57f176) Thanks [@fbionsc](https://github.com/fbionsc)! - - Fixes grouping of ad reports;
  - Removes slop;

## 8.37.2

### Patch Changes

- [#522](https://github.com/luxurypresence/seo-automation/pull/522) [`33c6c5c`](https://github.com/luxurypresence/seo-automation/commit/33c6c5c5fcc55eea20fce4e4d5ddbf7f7750d470) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates the seeder to set all recommendation statuses in contentPayload to PENDING

## 8.37.1

### Patch Changes

- [#521](https://github.com/luxurypresence/seo-automation/pull/521) [`a573dd3`](https://github.com/luxurypresence/seo-automation/commit/a573dd3e1deb3a5a7389d166f4a618a7c2ec0d1f) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates seeder to use a weighted random selection for recommendation status to get more PENDING statuses

## 8.37.0

### Minor Changes

- [#518](https://github.com/luxurypresence/seo-automation/pull/518) [`8de4576`](https://github.com/luxurypresence/seo-automation/commit/8de4576cd0440212547db181545cb25c6057cce7) Thanks [@jaycholland](https://github.com/jaycholland)! - reverts PR #501 that broke the homepage feed due to metadata ranking field type change

## 8.36.1

### Patch Changes

- [#514](https://github.com/luxurypresence/seo-automation/pull/514) [`4ccea36`](https://github.com/luxurypresence/seo-automation/commit/4ccea36d1ad8d235852fb22414503a1593c8540c) Thanks [@frankieramirez](https://github.com/frankieramirez)! - @frankieramirez - Update the mjml template for the weekly seo email

## 8.36.0

### Minor Changes

- [#501](https://github.com/luxurypresence/seo-automation/pull/501) [`5de278b`](https://github.com/luxurypresence/seo-automation/commit/5de278bf8c6108b001c1e09a5fb91723d84864a4) Thanks [@jaycholland](https://github.com/jaycholland)! - use rank from Page Keyword, fallback to metadata ranking

## 8.35.3

### Patch Changes

- [#516](https://github.com/luxurypresence/seo-automation/pull/516) [`250083b`](https://github.com/luxurypresence/seo-automation/commit/250083b3a865ee632f89df432d4469404f4a859a) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes email payload sent to notification-service, subject is required for the API, correct use for unsubscribe group id

## 8.35.2

### Patch Changes

- [#511](https://github.com/luxurypresence/seo-automation/pull/511) [`c8ae671`](https://github.com/luxurypresence/seo-automation/commit/c8ae671bcb4b4f22e5d14e578ec94a146a4214a0) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates seeder to seed correct scheduledAction.contentPayload json

## 8.35.1

### Patch Changes

- [#502](https://github.com/luxurypresence/seo-automation/pull/502) [`a2e82ef`](https://github.com/luxurypresence/seo-automation/commit/a2e82efcb44c187f50f541f32d0c6b654f7f9416) Thanks [@fbionsc](https://github.com/fbionsc)! - - Fix page.name query
  - Fix page.type query

## 8.35.0

### Minor Changes

- [#500](https://github.com/luxurypresence/seo-automation/pull/500) [`9cb7a91`](https://github.com/luxurypresence/seo-automation/commit/9cb7a91d85e4f290b1ae673bbdb6edbf4a97de0c) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds filter by companyId or groupId to recommendations query, loads related groupScheduledActions with scheduledAction query

## 8.34.2

### Patch Changes

- [#497](https://github.com/luxurypresence/seo-automation/pull/497) [`0e9ee58`](https://github.com/luxurypresence/seo-automation/commit/0e9ee588044a69d55faa7ef2647df82a52e4a0cf) Thanks [@devinellis](https://github.com/devinellis)! - remove subject

## 8.34.1

### Patch Changes

- [#491](https://github.com/luxurypresence/seo-automation/pull/491) [`88169be`](https://github.com/luxurypresence/seo-automation/commit/88169be977273199e7914aa87e7c11d72d589574) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds publishedAt to update scheduled action mutation

## 8.34.0

### Minor Changes

- [#442](https://github.com/luxurypresence/seo-automation/pull/442) [`4636880`](https://github.com/luxurypresence/seo-automation/commit/4636880e953808b8059ae9cd74d7e9f4758c3021) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds mutation to apply recommendations

## 8.33.1

### Patch Changes

- [#495](https://github.com/luxurypresence/seo-automation/pull/495) [`f290ada`](https://github.com/luxurypresence/seo-automation/commit/f290adacd8542b21aa426fddf0d9a5782a37a22a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes seeder

## 8.33.0

### Minor Changes

- [#492](https://github.com/luxurypresence/seo-automation/pull/492) [`5690696`](https://github.com/luxurypresence/seo-automation/commit/569069628b11005d2973c42830e10ec53cfea1e5) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Allow -1 values for rank fields to track non-ranking pages

  - Updated `PageKeyword` entity to accept -1 values for `currentRank` and `originalRank` fields
  - Modified validation decorators from `@Min(0)` to `@Min(-1)` in entity and DTOs
  - Updated `CreatePageKeywordDto` and `UpdatePageKeywordRankInput` to support -1 values
  - Added comprehensive test coverage for -1 rank values in service and resolver tests
  - This change enables RankWebhook to properly track pages that are not yet ranking in search results

## 8.32.2

### Patch Changes

- [#489](https://github.com/luxurypresence/seo-automation/pull/489) [`bc45e8f`](https://github.com/luxurypresence/seo-automation/commit/bc45e8f49b2ce343617a7cf2fef3c91d2fce4727) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes bcc

## 8.32.1

### Patch Changes

- [#486](https://github.com/luxurypresence/seo-automation/pull/486) [`f768ca0`](https://github.com/luxurypresence/seo-automation/commit/f768ca0deee8d735d4ad09fda8eed76d570498eb) Thanks [@elisabethgross](https://github.com/elisabethgross)! - add scheduledAction relation to group query joins to fix date bug

## 8.32.0

### Minor Changes

- [#485](https://github.com/luxurypresence/seo-automation/pull/485) [`89186ec`](https://github.com/luxurypresence/seo-automation/commit/89186ec6e14748142011b2e199c25a9730dc1b96) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement BCC workaround for email service - make separate API call with BCC recipients as 'to' field

## 8.31.1

### Patch Changes

- [#478](https://github.com/luxurypresence/seo-automation/pull/478) [`4d91cb2`](https://github.com/luxurypresence/seo-automation/commit/4d91cb2703e42e0964b97fdf855e04d85b8827d5) Thanks [@devinellis](https://github.com/devinellis)! - remove reply-to

## 8.31.0

### Minor Changes

- [#480](https://github.com/luxurypresence/seo-automation/pull/480) [`38ee73d`](https://github.com/luxurypresence/seo-automation/commit/38ee73d38a4b2f3999b55f870eacc8cc28f16d7d) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds entitlement checks before fetching feed data

## 8.30.2

### Patch Changes

- [#477](https://github.com/luxurypresence/seo-automation/pull/477) [`a9011da`](https://github.com/luxurypresence/seo-automation/commit/a9011dafcffcea7890254b766c8d9d14627f7bfb) Thanks [@devinellis](https://github.com/devinellis)! - Fix ad groups

## 8.30.1

### Patch Changes

- [#474](https://github.com/luxurypresence/seo-automation/pull/474) [`d554200`](https://github.com/luxurypresence/seo-automation/commit/d5542005219f82b856352173191bbc115cd68631) Thanks [@devinellis](https://github.com/devinellis)! - fix week grouping

## 8.30.0

### Minor Changes

- [#473](https://github.com/luxurypresence/seo-automation/pull/473) [`9eb917f`](https://github.com/luxurypresence/seo-automation/commit/9eb917f7e75b1a29a0f0badb1c44237063417de6) Thanks [@fbionsc](https://github.com/fbionsc)! - - Resolves scrapedPage object;
  - Deprecates pageName field;
  - Deprecates url field;

## 8.29.2

### Patch Changes

- [#472](https://github.com/luxurypresence/seo-automation/pull/472) [`0b0c0eb`](https://github.com/luxurypresence/seo-automation/commit/0b0c0eb51b09df258af2ff76cdb9027d240db41e) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Update ad performance dates to show next Tuesday instead of Monday week end (SEO-502 follow-up)

## 8.29.1

### Patch Changes

- [#471](https://github.com/luxurypresence/seo-automation/pull/471) [`37d1cd7`](https://github.com/luxurypresence/seo-automation/commit/37d1cd76e2e3b835005f1628044e4de800544491) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix ad performance grouping logic for Tuesday-Monday weeks - corrected week boundary calculation and aggregation

## 8.29.0

### Minor Changes

- [#470](https://github.com/luxurypresence/seo-automation/pull/470) [`9a986e9`](https://github.com/luxurypresence/seo-automation/commit/9a986e9f93b038b176387f1953ed70f6a86b5ba1) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds page.type field

## 8.28.3

### Patch Changes

- [#469](https://github.com/luxurypresence/seo-automation/pull/469) [`fe4579a`](https://github.com/luxurypresence/seo-automation/commit/fe4579aaf3b5e340f430f9cd04d1fc0916d4d804) Thanks [@fbionsc](https://github.com/fbionsc)! - Fixes grouping of ad performance reports

## 8.28.2

### Patch Changes

- [#466](https://github.com/luxurypresence/seo-automation/pull/466) [`5175266`](https://github.com/luxurypresence/seo-automation/commit/517526664b2142c17fefbd9b5d66bfc8a4d917b1) Thanks [@devinellis](https://github.com/devinellis)! - make mediaObject fields optional

## 8.28.1

### Patch Changes

- [#463](https://github.com/luxurypresence/seo-automation/pull/463) [`fc5a3f4`](https://github.com/luxurypresence/seo-automation/commit/fc5a3f433544eae93f54a976e52d895b642bd51e) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add media object support to FeedRecommendationGroup with thumbnailUrl and smallUrl fields

## 8.28.0

### Minor Changes

- [#465](https://github.com/luxurypresence/seo-automation/pull/465) [`10a946e`](https://github.com/luxurypresence/seo-automation/commit/10a946e4240b0ae0a29c29c84892fea5b74fccd4) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add formatted string fields to FeedAdPerformance GraphQL type

## 8.27.0

### Minor Changes

- [#461](https://github.com/luxurypresence/seo-automation/pull/461) [`b87beec`](https://github.com/luxurypresence/seo-automation/commit/b87beecf4014d8cea4bd4709f72e28db3c0873cb) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-492: Add API Gateway service for media thumbnails in homepage feed

## 8.26.1

### Patch Changes

- [#460](https://github.com/luxurypresence/seo-automation/pull/460) [`fff92c3`](https://github.com/luxurypresence/seo-automation/commit/fff92c3354b2a4d8411e1076f0be96abb001b131) Thanks [@devinellis](https://github.com/devinellis)! - Rename lead dir

## 8.26.0

### Minor Changes

- [#458](https://github.com/luxurypresence/seo-automation/pull/458) [`45a2b72`](https://github.com/luxurypresence/seo-automation/commit/45a2b72882ab0707c7bafd28c86c58ba676ba9e7) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Change homepage feed to use Tuesday-based weeks and filter partial weeks

### Patch Changes

- [#459](https://github.com/luxurypresence/seo-automation/pull/459) [`51aa3f2`](https://github.com/luxurypresence/seo-automation/commit/51aa3f27a4d955c2ed65c65aca5297107ec4c995) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Rename LeadService to ApiGatewayService to better reflect its purpose as an API gateway service

## 8.25.0

### Minor Changes

- [#457](https://github.com/luxurypresence/seo-automation/pull/457) [`ba87bd3`](https://github.com/luxurypresence/seo-automation/commit/ba87bd3768c8e50a8789894bd749be139be092ed) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-489: Add rejectionReason field to SeoOptimization GraphQL type

## 8.24.2

### Patch Changes

- [#454](https://github.com/luxurypresence/seo-automation/pull/454) [`e209571`](https://github.com/luxurypresence/seo-automation/commit/e20957141e4fd0b58585b6816fd6b00be3a0c9ed) Thanks [@fbionsc](https://github.com/fbionsc)! - Removes "publishedAt" fallback

## 8.24.1

### Patch Changes

- [#451](https://github.com/luxurypresence/seo-automation/pull/451) [`7f88799`](https://github.com/luxurypresence/seo-automation/commit/7f887994a3a163b5a713db72d74d3fb5476ef2f6) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-480: Adds bcc support for emails

## 8.24.0

### Minor Changes

- [#453](https://github.com/luxurypresence/seo-automation/pull/453) [`6e28d1d`](https://github.com/luxurypresence/seo-automation/commit/6e28d1db67c6454f84d81dd04398095f27eac693) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add surfacedAt to updateScheduledAction mutation

## 8.23.1

### Patch Changes

- [#450](https://github.com/luxurypresence/seo-automation/pull/450) [`da8a34a`](https://github.com/luxurypresence/seo-automation/commit/da8a34a12d997d763cb1efe4b0fadc33763ec22e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates all hardcoded llm model names

## 8.23.0

### Minor Changes

- [#447](https://github.com/luxurypresence/seo-automation/pull/447) [`0251baa`](https://github.com/luxurypresence/seo-automation/commit/0251baa389ba6c5e7a50e1f9f98a94c0fa8c98a4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds query for all surfaced and published groups

## 8.22.0

### Minor Changes

- [#445](https://github.com/luxurypresence/seo-automation/pull/445) [`c396a7b`](https://github.com/luxurypresence/seo-automation/commit/c396a7bf1392ba7f21d21f5af37add6bddcde687) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add updatePageKeywordRank mutation with rank

## 8.21.0

### Minor Changes

- [#439](https://github.com/luxurypresence/seo-automation/pull/439) [`cdd4aab`](https://github.com/luxurypresence/seo-automation/commit/cdd4aab1756d4c75a7cb15cc7c21b3dda75b917b) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-446: Adds Emailer Daemon.

## 8.20.0

### Minor Changes

- [#444](https://github.com/luxurypresence/seo-automation/pull/444) [`7e7cf94`](https://github.com/luxurypresence/seo-automation/commit/7e7cf94b1f2c5f6e0a98e98dcd364426954063a0) Thanks [@fbionsc](https://github.com/fbionsc)! - - Removes deprecated fields;
  - Fixes "publishedAt" type;

## 8.19.0

### Minor Changes

- [#443](https://github.com/luxurypresence/seo-automation/pull/443) [`42509ca`](https://github.com/luxurypresence/seo-automation/commit/42509ca2fef875a41695edab8552034861eeb5a9) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds "currentValue" field to SeoOptimizations

## 8.18.2

### Patch Changes

- [#438](https://github.com/luxurypresence/seo-automation/pull/438) [`f8678ff`](https://github.com/luxurypresence/seo-automation/commit/f8678ff7c3b6efd24be6828cfacfdda1c9c74fdb) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix upcoming check failed

## 8.18.1

### Patch Changes

- [#437](https://github.com/luxurypresence/seo-automation/pull/437) [`fc351d5`](https://github.com/luxurypresence/seo-automation/commit/fc351d56abbd74226f18a771adda1c2a0a808fc4) Thanks [@fbionsc](https://github.com/fbionsc)! - Fixes env variable for Auth0

## 8.18.0

### Minor Changes

- [#436](https://github.com/luxurypresence/seo-automation/pull/436) [`ad820ea`](https://github.com/luxurypresence/seo-automation/commit/ad820ea06139a8a77e3d15a39e40203aeba07e2b) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds auth check to feed resolver

## 8.17.0

### Minor Changes

- [#432](https://github.com/luxurypresence/seo-automation/pull/432) [`f0e9fbf`](https://github.com/luxurypresence/seo-automation/commit/f0e9fbf65893f819665b708c9bdb07c56dbfac00) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds auth checks to SEO optimizations resolver

## 8.16.0

### Minor Changes

- [#430](https://github.com/luxurypresence/seo-automation/pull/430) [`d21edf1`](https://github.com/luxurypresence/seo-automation/commit/d21edf17c86875313ea371fe9caf35a27e97856e) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds url field

## 8.15.0

### Minor Changes

- [#427](https://github.com/luxurypresence/seo-automation/pull/427) [`faa61ae`](https://github.com/luxurypresence/seo-automation/commit/faa61ae33d08d7d4225aa262775d8d190328f727) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds rejection mutation;

## 8.14.0

### Minor Changes

- [#423](https://github.com/luxurypresence/seo-automation/pull/423) [`059e9ec`](https://github.com/luxurypresence/seo-automation/commit/059e9ecb2043fc85a7fcf516bd675c96cb6d6720) Thanks [@fbionsc](https://github.com/fbionsc)! - - Throw error if fetching leads fails;
  - Replace auth method with M2M;

## 8.13.3

### Patch Changes

- [#425](https://github.com/luxurypresence/seo-automation/pull/425) [`844f7fa`](https://github.com/luxurypresence/seo-automation/commit/844f7fa69327daa0a867af8dded1e5a38da29be2) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO recommendation group timestamp calculation for Recent/Upcoming tabs

  - Update mapRecommendationGroups to use publishedAt || scheduledToBePublishedAt || createdAt
  - Groups with future scheduledToBePublishedAt dates now appear in Upcoming tab
  - Groups with past publishedAt dates appear in Recent tab
  - Resolves SEO-468

## 8.13.2

### Patch Changes

- [#424](https://github.com/luxurypresence/seo-automation/pull/424) [`c18044b`](https://github.com/luxurypresence/seo-automation/commit/c18044b56bbe6bb52f544774ada2c98ceb565559) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates company to use federation references properly

## 8.13.1

### Patch Changes

- [#422](https://github.com/luxurypresence/seo-automation/pull/422) [`66d28ba`](https://github.com/luxurypresence/seo-automation/commit/66d28ba68e815f14b58d418a9ee0fe22f8b289d4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Improves seeders for testing in the staging environment

## 8.13.0

### Minor Changes

- [#421](https://github.com/luxurypresence/seo-automation/pull/421) [`ee811ee`](https://github.com/luxurypresence/seo-automation/commit/ee811eee109b28c25a2c25cd7272ad394249fa15) Thanks [@fbionsc](https://github.com/fbionsc)! - - Adds "type" field;
  - Adds "reasoning" field;
  - Adds "recommendationValue" field;
  - Adds "appliedAt" field;
  - Adds "scheduledToBeAppliedAt" field;
  - Adds deprecation warning to "ranking" field;

## 8.12.0

### Minor Changes

- [#415](https://github.com/luxurypresence/seo-automation/pull/415) [`8fadfb4`](https://github.com/luxurypresence/seo-automation/commit/8fadfb498375926be365b8d8147c75f9c4e6e091) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds "find group by id" query

## 8.11.1

### Patch Changes

- [#418](https://github.com/luxurypresence/seo-automation/pull/418) [`75dd076`](https://github.com/luxurypresence/seo-automation/commit/75dd07669c1203f0d606adb32ad5025e9914384e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify scheduledAction update to set lockedAt to null

## 8.11.0

### Minor Changes

- [#417](https://github.com/luxurypresence/seo-automation/pull/417) [`121a4b8`](https://github.com/luxurypresence/seo-automation/commit/121a4b8f64f249882803d8e6d208c86021d4136d) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Widen findUpcoming's definition

## 8.10.0

### Minor Changes

- [#412](https://github.com/luxurypresence/seo-automation/pull/412) [`b3a5474`](https://github.com/luxurypresence/seo-automation/commit/b3a54742b0d17b14f8e9d0d87e37984b5eec7cf7) Thanks [@fbionsc](https://github.com/fbionsc)! - - Adds rank field;
  - Removes ranking field;
  - Adds mediaId field;

## 8.9.0

### Minor Changes

- [#411](https://github.com/luxurypresence/seo-automation/pull/411) [`de8d78e`](https://github.com/luxurypresence/seo-automation/commit/de8d78e3624976582bbabf96886b4f5b49ccff8c) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify update scheduledAction to not check for every field

## 8.8.0

### Minor Changes

- [#410](https://github.com/luxurypresence/seo-automation/pull/410) [`40eddaf`](https://github.com/luxurypresence/seo-automation/commit/40eddaf883a59f1e01789ad66b6253bedadf9780) Thanks [@fbionsc](https://github.com/fbionsc)! - Add "SEO Optimizations" resolver

## 8.7.2

### Patch Changes

- [#403](https://github.com/luxurypresence/seo-automation/pull/403) [`afac76e`](https://github.com/luxurypresence/seo-automation/commit/afac76e1ad5e28f51c1e7763d715c54abdd396ad) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix MainHeadingInput

## 8.7.1

### Patch Changes

- [#402](https://github.com/luxurypresence/seo-automation/pull/402) [`ac5ab63`](https://github.com/luxurypresence/seo-automation/commit/ac5ab63c73d9819a6e12ab52bf2c39113c85f43e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - New seeders, updated triggers

## 8.7.0

### Minor Changes

- [#393](https://github.com/luxurypresence/seo-automation/pull/393) [`2fd5f50`](https://github.com/luxurypresence/seo-automation/commit/2fd5f500ecba453447e27424a6f06aacd8feee2c) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-365: Updates StoreSEODraft lambda

## 8.6.0

### Minor Changes

- [#401](https://github.com/luxurypresence/seo-automation/pull/401) [`457d733`](https://github.com/luxurypresence/seo-automation/commit/457d7332bc4bfcac75f3c9de5d40c405bc0654de) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Rename page_id fk columns to scraped_page_id

## 8.5.2

### Patch Changes

- [#399](https://github.com/luxurypresence/seo-automation/pull/399) [`490fb2b`](https://github.com/luxurypresence/seo-automation/commit/490fb2ba5b1ae72381c4322fecf6aa69b2afd777) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix updateScheduledActionDTO to be IsDate, not isDateString

## 8.5.1

### Patch Changes

- [#396](https://github.com/luxurypresence/seo-automation/pull/396) [`fa1c699`](https://github.com/luxurypresence/seo-automation/commit/fa1c6998ab24989c2940130ebcd8e5387a1aa440) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Regenerate schema for scrapedPage

## 8.5.0

### Minor Changes

- [#388](https://github.com/luxurypresence/seo-automation/pull/388) [`c6501d5`](https://github.com/luxurypresence/seo-automation/commit/c6501d5ea78680d814c845e547f25b8134d8eeea) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Enable GraphQL queries for groups with recommendations and related scraped pages

  - Added `scrapedPage` field resolver to recommendation entity and resolver
  - Updated group service to load nested relations: recommendations → scrape → scrapedPage
  - Fixed all related tests to accommodate new relation loading
  - Enables queries like:
    ```graphql
    query {
      groups {
        id
        title
        recommendations {
          id
          currentValue
          scrapedPage {
            id
            url
          }
        }
      }
    }
    ```

## 8.4.0

### Minor Changes

- [#347](https://github.com/luxurypresence/seo-automation/pull/347) [`00184d1`](https://github.com/luxurypresence/seo-automation/commit/00184d120974aa562921f40ebfc9eee347dd73b7) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-428: Adds Surfacer lambda

## 8.3.1

### Patch Changes

- [#392](https://github.com/luxurypresence/seo-automation/pull/392) [`fda711e`](https://github.com/luxurypresence/seo-automation/commit/fda711ec865997a9b06c36136e76ad72c3a4c3aa) Thanks [@fbionsc](https://github.com/fbionsc)! - Extracts dates from scheduled actions;

## 8.3.0

### Minor Changes

- [#390](https://github.com/luxurypresence/seo-automation/pull/390) [`401a454`](https://github.com/luxurypresence/seo-automation/commit/401a45496c8c9e727486270295e36d69a9b3e72c) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix issues with global validation messing up REST and have errors go to Graphql Errors

## 8.2.2

### Patch Changes

- [#389](https://github.com/luxurypresence/seo-automation/pull/389) [`37fd844`](https://github.com/luxurypresence/seo-automation/commit/37fd844c17da47e1a6b979c0eacd45367c4ef502) Thanks [@fbionsc](https://github.com/fbionsc)! - Add company id parameter to query

## 8.2.1

### Patch Changes

- [#387](https://github.com/luxurypresence/seo-automation/pull/387) [`b0ca2ad`](https://github.com/luxurypresence/seo-automation/commit/b0ca2ad92353d4553b93a0b9634f45acaa450d9a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes unnecessary @external directive in company entity

## 8.2.0

### Minor Changes

- [#384](https://github.com/luxurypresence/seo-automation/pull/384) [`a24fe0d`](https://github.com/luxurypresence/seo-automation/commit/a24fe0d09d50394d4b3bf536ddb600b0f79e98d6) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify create/update ScheduledAction and create/update/upsert Keyword to have flat inputs, not object ones.

## 8.1.0

### Minor Changes

- [#385](https://github.com/luxurypresence/seo-automation/pull/385) [`0e9d882`](https://github.com/luxurypresence/seo-automation/commit/0e9d88260c5d64cc237030d1dbfa5c579195ac06) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Refactor company data fetching to use tenant service via GraphQL federation

  This major update refactors the company service architecture to properly integrate with the tenant service for company data retrieval:

  **Architecture Changes:**

  - Replaced mock company service with external GraphQL client integration
  - Added M2M authentication service for secure tenant service communication
  - Implemented proper error handling and dependency injection patterns

  **New Features:**

  - GraphQL client infrastructure with `TenantCompanyClient`
  - Company data loader now fetches real data from tenant service via federation
  - Comprehensive error handling with `CompanyClientError`

  **Code Quality:**

  - Updated all tests to mock external dependencies properly
  - Removed company resolver (now handled by tenant service federation)
  - Page entity renamed to ScrapedPage for clarity and consistency

  **Dependencies:**

  - Added `graphql-request` for external GraphQL API communication
  - Updated GraphQL schema to reflect Page → ScrapedPage migration

  This change enables the client marketing service to properly fetch company data from the centralized tenant service while maintaining clean separation of concerns through GraphQL federation.

## 8.0.2

### Patch Changes

- [#386](https://github.com/luxurypresence/seo-automation/pull/386) [`4d14594`](https://github.com/luxurypresence/seo-automation/commit/4d1459458927db9ac9237e7ff4e4c59251c807a8) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix missing columns error from Group entity.

## 8.0.1

### Patch Changes

- [#382](https://github.com/luxurypresence/seo-automation/pull/382) [`8e388bc`](https://github.com/luxurypresence/seo-automation/commit/8e388bce3381ec019cc393b1a1f6af6b327eb73d) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix scheduled action locking logic - move transaction from findUpcoming to findDraftPending

## 8.0.0

### Major Changes

- [#355](https://github.com/luxurypresence/seo-automation/pull/355) [`d981027`](https://github.com/luxurypresence/seo-automation/commit/d981027eb0ac6034c0af59ad499b4d8eb77c6c46) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Remove unused entities from client-marketing-service

  BREAKING CHANGES:

  - Remove Company entity and related sub-entities (CompanySeoData, CompanyAgent, CompanyNeighborhood)
  - Remove SeoPage entity (functionality replaced by existing Page entity)
  - Remove Markdown entity
  - Remove ScrapedJson entity
  - Update Recommendation entity to use Page relation instead of SeoPage
  - Disable SeoPage-dependent functionality in RecommendationService and generators
  - Update GraphQL schema to remove deleted entity types and queries
  - Remove scraper functionality that depended on deleted entities
  - Update Slack notifications to use Page relation instead of SeoPage

  Migration notes:

  - Database migration included to drop deleted entity tables
  - Existing Page entity provides equivalent functionality to SeoPage
  - Recommendation functionality preserved for core CRUD operations
  - Generated recommendation functionality disabled (dependent on deleted entities)

## 7.35.0

### Minor Changes

- [#366](https://github.com/luxurypresence/seo-automation/pull/366) [`bd44315`](https://github.com/luxurypresence/seo-automation/commit/bd4431568e93c605e38adea201ec4d242bf7a0f7) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds timestamp filters to feed query

## 7.34.0

### Minor Changes

- [#356](https://github.com/luxurypresence/seo-automation/pull/356) [`e8025ae`](https://github.com/luxurypresence/seo-automation/commit/e8025ae93866b8691c954a67b9af971417bf412a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add PublisherDaemon lambda for automated content publishing

  This release introduces a new PublisherDaemon lambda that automatically processes surfaced actions ready for publishing. The daemon runs on weekdays and uses a fan-out pattern to invoke the publishing lambda for each action.

  ### Features Added

  - **PublisherDaemon Lambda**: New scheduled lambda that queries SURFACED actions with `scheduled_to_be_published_at` ≤ current date
  - **Fan-out Pattern**: Processes actions in configurable chunks with exponential backoff to handle rate limits
  - **Weekday Scheduling**: Configured to run every weekday at 9 AM UTC via cron expression
  - **GraphQL Support**: Added `surfacedActions` query and resolver to client-marketing-service
  - **Configuration Management**: Added validation for chunk size and publishing lambda ARN
  - **Comprehensive Testing**: Full test coverage for all new components and scenarios

  ### Technical Details

  - Added `getSurfacedActions` method to API client and service layers
  - Implemented chunked processing with configurable `actionChunkSize`
  - Added proper error handling and logging throughout the fan-out process
  - Extended configuration interfaces to support new lambda requirements
  - Updated serverless.yml with weekday cron schedule (0 9 ? _ MON-FRI _)

  ### Breaking Changes

  None. This is a purely additive feature that doesn't modify existing functionality.

## 7.33.1

### Patch Changes

- [#359](https://github.com/luxurypresence/seo-automation/pull/359) [`db976d8`](https://github.com/luxurypresence/seo-automation/commit/db976d83314a827a4ebecf6e3f0f730451e320d0) Thanks [@jaycholland](https://github.com/jaycholland)! - updated readme setup instructions for client-marketing-service

## 7.33.0

### Minor Changes

- [#360](https://github.com/luxurypresence/seo-automation/pull/360) [`4660172`](https://github.com/luxurypresence/seo-automation/commit/4660172572080c192ef5fc0418dba9aed34c592c) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds lead ids to query

## 7.32.0

### Minor Changes

- [#353](https://github.com/luxurypresence/seo-automation/pull/353) [`10dd018`](https://github.com/luxurypresence/seo-automation/commit/10dd0189fa9f1619b912505254e018af6971db21) Thanks [@fbionsc](https://github.com/fbionsc)! - Run feed requests in parallel

## 7.31.0

### Minor Changes

- [#351](https://github.com/luxurypresence/seo-automation/pull/351) [`ae33880`](https://github.com/luxurypresence/seo-automation/commit/ae33880e9d0199875200832720eb374906db5d9c) Thanks [@fbionsc](https://github.com/fbionsc)! - - Adds fallback when fetching leads fails;
  - Updates schema;

## 7.30.0

### Minor Changes

- [#350](https://github.com/luxurypresence/seo-automation/pull/350) [`20631c2`](https://github.com/luxurypresence/seo-automation/commit/20631c211955d62ca3dcadc3f227c259d55c26b3) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add GroupScheduledAction join table for many-to-many relationship between Group and ScheduledAction entities

## 7.29.0

### Minor Changes

- [#314](https://github.com/luxurypresence/seo-automation/pull/314) [`2ef6569`](https://github.com/luxurypresence/seo-automation/commit/2ef6569c617d014e2399f3857af9723e7de2ce13) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds Snowflake integration to fetch ad performance reports

## 7.28.0

### Minor Changes

- [#346](https://github.com/luxurypresence/seo-automation/pull/346) [`1006952`](https://github.com/luxurypresence/seo-automation/commit/1006952a293d019e4ed281a1db4d5de3e33dcd24) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Create scheduledAction gql query

## 7.27.2

### Patch Changes

- [#343](https://github.com/luxurypresence/seo-automation/pull/343) [`811eea5`](https://github.com/luxurypresence/seo-automation/commit/811eea5e8039e58dd1f768cccb42eef03b87feaf) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds websiteUrls to draft pending scheduled actions

## 7.27.1

### Patch Changes

- [#342](https://github.com/luxurypresence/seo-automation/pull/342) [`f02b8f9`](https://github.com/luxurypresence/seo-automation/commit/f02b8f93b9cf10fe88a97bee19a81d5e55c157f4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates ApiGatewayClient to SeoApiGatewayClient

## 7.27.0

### Minor Changes

- [#330](https://github.com/luxurypresence/seo-automation/pull/330) [`f3eaa85`](https://github.com/luxurypresence/seo-automation/commit/f3eaa856f374f1c4b16e39498b9ae71987680367) Thanks [@thenickot2](https://github.com/thenickot2)! - feat: add companies dataloader and local-rover for local development

## 7.26.1

### Patch Changes

- [#339](https://github.com/luxurypresence/seo-automation/pull/339) [`fe7cb21`](https://github.com/luxurypresence/seo-automation/commit/fe7cb21f83f47da32ef2c36d27ab031e90e9a8a0) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix schema for new scheduledAction fields

## 7.26.0

### Minor Changes

- [#337](https://github.com/luxurypresence/seo-automation/pull/337) [`8c78d7a`](https://github.com/luxurypresence/seo-automation/commit/8c78d7a5e3d241281c3940fc30cc478551917bfc) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add fields to scheduledAction; modify findUpcoming to "lock" records

## 7.25.0

### Minor Changes

- [#338](https://github.com/luxurypresence/seo-automation/pull/338) [`d991c5f`](https://github.com/luxurypresence/seo-automation/commit/d991c5f33cdf338c75dfeccc33fb777ef1296cc6) Thanks [@fbionsc](https://github.com/fbionsc)! - - Adds error logging
  - Fixes external URLs

## 7.24.0

### Minor Changes

- [#336](https://github.com/luxurypresence/seo-automation/pull/336) [`e595e7a`](https://github.com/luxurypresence/seo-automation/commit/e595e7a67df71d859de1fbc5759ca0c8f8ba918b) Thanks [@fbionsc](https://github.com/fbionsc)! - Add query to lead service

## 7.23.5

### Patch Changes

- [#333](https://github.com/luxurypresence/seo-automation/pull/333) [`3977bef`](https://github.com/luxurypresence/seo-automation/commit/3977befc464fab4008143f741c41ab3d79a9e25e) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-368: Adds StoreDraft lambda. ScheduledToBeSurfacedAt and ScheduledToBePublishedAt fields added to mutation

## 7.23.4

### Patch Changes

- [#332](https://github.com/luxurypresence/seo-automation/pull/332) [`9664532`](https://github.com/luxurypresence/seo-automation/commit/9664532d8e701dae502ec276c52aec63e6983df9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes deprecated packages

## 7.23.3

### Patch Changes

- [#331](https://github.com/luxurypresence/seo-automation/pull/331) [`365fe62`](https://github.com/luxurypresence/seo-automation/commit/365fe6275669bb25cacf354ffa7ed7d5dfc2e00e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add M2M_SUPER_API_KEYS to config-template

## 7.23.2

### Patch Changes

- [#329](https://github.com/luxurypresence/seo-automation/pull/329) [`eabe09a`](https://github.com/luxurypresence/seo-automation/commit/eabe09af46bfeac66dddb48ffccc027655b5aa21) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix Dockerfiles

## 7.23.1

### Patch Changes

- [#328](https://github.com/luxurypresence/seo-automation/pull/328) [`005abb8`](https://github.com/luxurypresence/seo-automation/commit/005abb885ebea55934d8184e9f74a25c09c99eed) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify CircleCI to build to client-marketing-service in DockerHub

## 7.23.0

### Minor Changes

- [#322](https://github.com/luxurypresence/seo-automation/pull/322) [`233d3eb`](https://github.com/luxurypresence/seo-automation/commit/233d3eb86043c67fa0036d1614190e340234f821) Thanks [@fbionsc](https://github.com/fbionsc)! - Sort feed items by timestamp

### Patch Changes

- [#326](https://github.com/luxurypresence/seo-automation/pull/326) [`98f03dc`](https://github.com/luxurypresence/seo-automation/commit/98f03dc26db71f264804a4c66b1fbc3ee246748b) Thanks [@fbionsc](https://github.com/fbionsc)! - Add back .env.local file

- [#324](https://github.com/luxurypresence/seo-automation/pull/324) [`99174e0`](https://github.com/luxurypresence/seo-automation/commit/99174e0a3b8445350f186fd77e60eda6331ff2e7) Thanks [@fbionsc](https://github.com/fbionsc)! - Filter out groups without any recommendations

## 7.22.0

### Minor Changes

- [#323](https://github.com/luxurypresence/seo-automation/pull/323) [`f316172`](https://github.com/luxurypresence/seo-automation/commit/f31617289973be9243098f9680d8a6a8c6809851) Thanks [@thenickot2](https://github.com/thenickot2)! - feat: rename seo-automation-api-gateway to client-marketing-service

## 7.21.0

### Minor Changes

- [#321](https://github.com/luxurypresence/seo-automation/pull/321) [`b1eb7f4`](https://github.com/luxurypresence/seo-automation/commit/b1eb7f43a29af26f7135cb1c5cb54e3bce238172) Thanks [@thenickot2](https://github.com/thenickot2)! - enh: remove companies query, allow company type to be extended

## 7.20.2

### Patch Changes

- [#320](https://github.com/luxurypresence/seo-automation/pull/320) [`37a451e`](https://github.com/luxurypresence/seo-automation/commit/37a451ef9ebf423686a97bcc8f70d98e83880f0a) Thanks [@diegosr90](https://github.com/diegosr90)! - Fix Keyword DTOs for gql

## 7.20.1

### Patch Changes

- [#316](https://github.com/luxurypresence/seo-automation/pull/316) [`88c0436`](https://github.com/luxurypresence/seo-automation/commit/88c04364a44b6d0d047c53b34b7f62abe0afd0f2) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds create, update and upsert mutations on keyword

## 7.20.0

### Minor Changes

- [#313](https://github.com/luxurypresence/seo-automation/pull/313) [`6c1342e`](https://github.com/luxurypresence/seo-automation/commit/6c1342ef821bf35cab999ac53d86f9f8dab683ae) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds CMS service integration to fetch blog posts

## 7.19.0

### Minor Changes

- [#302](https://github.com/luxurypresence/seo-automation/pull/302) [`5698413`](https://github.com/luxurypresence/seo-automation/commit/5698413868338a534890bd07ca548c2e879df5b4) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds weekly reports on ad performance to feed.

## 7.18.2

### Patch Changes

- [#310](https://github.com/luxurypresence/seo-automation/pull/310) [`a0333e2`](https://github.com/luxurypresence/seo-automation/commit/a0333e20a2c4c96a3b04c6f3f20bd3c6e5f352b1) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds missing ScrapeRepository to scrape module

## 7.18.1

### Patch Changes

- [#309](https://github.com/luxurypresence/seo-automation/pull/309) [`60ab05a`](https://github.com/luxurypresence/seo-automation/commit/60ab05adb5e1f7720c1c391719703928cdb2b921) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds tests

## 7.18.0

### Minor Changes

- [#298](https://github.com/luxurypresence/seo-automation/pull/298) [`31e4558`](https://github.com/luxurypresence/seo-automation/commit/31e45580024107ebfcb41fb3d9c4cf4e16191fea) Thanks [@fbionsc](https://github.com/fbionsc)! - Adds AILN data to feed

## 7.17.0

### Minor Changes

- [#307](https://github.com/luxurypresence/seo-automation/pull/307) [`02b137d`](https://github.com/luxurypresence/seo-automation/commit/02b137d15b78cdf0d12362996c08e0fad3de4ee4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates page-keyword service and resolvers

## 7.16.0

### Minor Changes

- [#305](https://github.com/luxurypresence/seo-automation/pull/305) [`83294ff`](https://github.com/luxurypresence/seo-automation/commit/83294ffdd097bed884a0259f6408dcb1adc46737) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates page service methods and resolvers

## 7.15.0

### Minor Changes

- [#304](https://github.com/luxurypresence/seo-automation/pull/304) [`0330911`](https://github.com/luxurypresence/seo-automation/commit/033091139786b11aa32706b2792d1aeba33858b1) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates keyword service methods and resolvers

## 7.14.0

### Minor Changes

- [#303](https://github.com/luxurypresence/seo-automation/pull/303) [`587da06`](https://github.com/luxurypresence/seo-automation/commit/587da06ddec1ddb11c833e89f05bb6f8396e931d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds / updates recommendation service methods and resolvers

## 7.13.3

### Patch Changes

- [#301](https://github.com/luxurypresence/seo-automation/pull/301) [`49608a6`](https://github.com/luxurypresence/seo-automation/commit/49608a6574aab15d58376b4bd040b98f4d08fdbb) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Avoid installing husky in Dockerfile

## 7.13.2

### Patch Changes

- [#288](https://github.com/luxurypresence/seo-automation/pull/288) [`b850fe1`](https://github.com/luxurypresence/seo-automation/commit/b850fe17882ce51226c680b2ae1b000ca5af1890) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Confirm that improved changeset with name and links works

## 7.13.1

### Patch Changes

- a8c7c0b: Force release

## 7.13.0

### Minor Changes

- 01eee8a: - adds SEO recommendations data to feed;
- e483704: - adds query to fetch upcoming scheduled actions;
  - adds mutation to create owed scheduled actions;
  - adds function to identify and create actions based on entitlement.

## 7.12.0

### Minor Changes

- 58ff734: - adds resolver for homepage feed data
  - adds mock blog post content to new feed

## 7.11.0

### Minor Changes

- 88e6c29: Adds scheduled-action mutations for create and update

## 7.10.0

### Minor Changes

- bc67820: - Adds m2m auth
  - Updates group policy to check for super user
  - Adds findAll groups method

## 7.9.0

### Minor Changes

- a4435bb: Adds seeders

## 7.8.0

### Minor Changes

- 7ee2c14: Remove company foreign key relationships from page, group, and scrape entities

## 7.7.0

### Minor Changes

- 4de63c1: Add media ID column to scrape table and entity

## 7.6.1

### Patch Changes

- 0c67049: Updates migration files after failed deploy

## 7.6.0

### Minor Changes

- 61a163f: Standardize enum values to uppercase format for gql

## 7.5.0

### Minor Changes

- 3a6fa93: Add GraphQL queries to fetch scheduled actions with DRAFT_PENDING status with pagination and sorting

## 7.4.0

### Minor Changes

- bb4f3a7: Adds GraphQL operations to all existing entities

## 7.3.0

### Minor Changes

- f9ed79c: Adds new DB tables keyword, page, page_keyword, page_keyword_history, scrape

## 7.2.4

### Patch Changes

- 84fe83f: Make sure this package can build after CircleCI changes

## 7.2.3

### Patch Changes

- 810e117: Fixes bloom-scheduler-lambda tests

## 7.2.2

### Patch Changes

- af08216: Fix uuid generation

## 7.2.1

### Patch Changes

- 640ec28: Temporarily remove group query

## 7.2.0

### Minor Changes

- 1b8d078: Adds "scheduled actions" migrations

## 7.1.6

### Patch Changes

- cee3967: Fix environment variable declaration
- cee3967: Temporarily log request headers

## 7.1.5

### Patch Changes

- 67f387c: Fix environment variable declaration

## 7.1.4

### Patch Changes

- 5428846: Fix exceptions filter

## 7.1.3

### Patch Changes

- 5928c6a: Fixes the auth0 token audience

## 7.1.2

### Patch Changes

- bf2d769: Adds job ID tracking and improves logging across recommendation surfaces and applies

## 7.1.1

### Patch Changes

- 61330ce: fix: test cors true with cosmo router

## 7.1.0

### Minor Changes

- 8c699bd: - Adds auth-middleware check to GraphQL resolvers;
  - Adds authorization check before resolving recommendation groups;

## 7.0.6

### Patch Changes

- e016180: Exposes seoPage websiteUrl

## 7.0.5

### Patch Changes

- da5aa84: Removes lazy load logic

## 7.0.4

### Patch Changes

- fafae18: Fixes stuff

## 7.0.3

### Patch Changes

- cfa8c49: Hotfix for finding groups to surface and apply, removes eager loaded relations that aren't needed

## 7.0.2

### Patch Changes

- 4776760: Fixes date casting for finding groups to surface / apply

## 7.0.1

### Patch Changes

- 0f72db6: Adds debug logs for recommendation update

## 7.0.0

### Major Changes

- 9143796: Returns single scrapedJson or single markdown when found by seoPageId because the relation was changed to one to one

## 6.1.5

### Patch Changes

- 0431a42: Refactor logging and improve prompt handling in website update flow

## 6.1.4

### Patch Changes

- e5a6887: Replace dryRun with skipStarWebsiteUpdate flag which can be in the request body or in the recommendation metadata

## 6.1.3

### Patch Changes

- b3c7a58: BullMQ Dashboard fix

## 6.1.2

### Patch Changes

- d5c5df2: Attempt to fix BullMQ dashboard

## 6.1.1

### Patch Changes

- d2784f7: Fix id types

## 6.1.0

### Minor Changes

- 7b1a47d: Add pagination to scraped-json resource

## 6.0.12

### Patch Changes

- b1747ab: Add static file serving for Bull Board UI and update Express adapter config

## 6.0.11

### Patch Changes

- b181554: Set base path for Bull Board queue UI to /queues using custom Express adapter

## 6.0.10

### Patch Changes

- 242baac: Update API prefix exclusion pattern for queues

## 6.0.9

### Patch Changes

- 4de9de2: Add missing decorator

## 6.0.8

### Patch Changes

- f050a8c: Fixes /queues/static route to use updated wildcard syntax

## 6.0.7

### Patch Changes

- 108897f: Pass parameters from controller to service

## 6.0.6

### Patch Changes

- b3221ea: Add queues/static/\*\* to API endpoint exclusions list

## 6.0.5

### Patch Changes

- 79a6889: Conditionally run queue in production

## 6.0.4

### Patch Changes

- 900fb71: Add missing group query params
- 900fb71: Temporarily return an empty list of groups

## 6.0.3

### Patch Changes

- efbbbb3: Changes log level to debug

## 6.0.2

### Patch Changes

- bc1fac7: Add missing group query params

## 6.0.1

### Patch Changes

- d2df29b: Fix the /queues route for bullMQ dashboard

## 6.0.0

### Major Changes

- 4af49ea: Adds auto apply for recommendations with BullMQ

## 5.0.0

### Major Changes

- f011ced: Reduce pagination limits and simplify relations in SEO page config

## 4.12.0

### Minor Changes

- 3bd2c0f: - adds "group" field resolvers;
  - adds "recommendation" field resolvers;
  - enables graphqil and cors;

## 4.11.1

### Patch Changes

- 95dd3d4: Remove postinstall script for playwright chromium installation

## 4.11.0

### Minor Changes

- d347622: Update recommendation group's appliedAt timestamp when saving recommendation

## 4.10.0

### Minor Changes

- 2456672: Adds GraphQL endpoint

## 4.9.2

### Patch Changes

- 5bf369d: Adds tests for low coverage areas

## 4.9.1

### Patch Changes

- c7c0637: Set scheduledToBePublishedAt to 7 days from now when surfacing notifications

## 4.9.0

### Minor Changes

- 2c1c355: - Adds migration to update rec status from "approved" to "applied"
  - Adds `dryRun` option to update recommendations to udpate entity without applying the update to STAR API

## 4.8.1

### Patch Changes

- 67508d5: Suppress slack notifications on staging

## 4.8.0

### Minor Changes

- 402e55a: Adds "rejection reason" to recommendations

## 4.7.0

### Minor Changes

- 5a6e973: Refactors the scraper to be more moduler

## 4.6.0

### Minor Changes

- 2701725: - Fix SeoPage seeder;
  - Add local database Docker setup;

## 4.5.0

### Minor Changes

- 74bf478: Adds create neighborhoods and agents endpoints

## 4.4.1

### Patch Changes

- 33a1a8a: Adds fk constraint between company and group

## 4.4.0

### Minor Changes

- e5d50fc: Adds slug column to company_agent and company_neighborhood tables

## 4.3.0

### Minor Changes

- b0579af: Updates email template

## 4.2.11

### Patch Changes

- 218bb57: Fixes update on rejection

## 4.2.10

### Patch Changes

- 33248e4: Removes IsDate validation

## 4.2.9

### Patch Changes

- e188449: fix: force pnpm install in migration.Dockerfile to rewrite symlinks
- e188449: fix: hardcode NODE_ENV in migration.dockerfile

## 4.2.8

### Patch Changes

- 8959300: fix: force pnpm install in migration.Dockerfile to rewrite symlinks

## 4.2.7

### Patch Changes

- 03cdc6b: fix: turbo 2.4.4 does not copy dist directory, revert to 2.3.3

## 4.2.6

### Patch Changes

- cf56a0b: fix: migration dockerfile needs dev deps

## 4.2.5

### Patch Changes

- a7c9fd6: fix: add husky installation temporarily to changesets

## 4.2.4

### Patch Changes

- 84dd97b: fix: copy pnpm-lock.yml in build job

## 4.2.3

### Patch Changes

- 2ed9af7: fix: add npm-read context to release workflow

## 4.2.2

### Patch Changes

- 5455986: fix: dockerfile location and context in build job

## 4.2.1

### Patch Changes

- 3a27f8d: fix: use pnpm consistently in dockerfiles

## 4.2.0

### Minor Changes

- 3e45a16: Deprecate "APPROVED" status

## 4.1.1

### Patch Changes

- 9cad2a8: - Update CODEOWNERS

## 4.1.0

### Minor Changes

- c8e4209: chore: add standard logging, dd-trace and connection draining

## 4.0.7

### Patch Changes

- f9a572a: fix: move dotenv to initialize first

## 4.0.6

### Patch Changes

- 026202e: More logs

## 4.0.5

### Patch Changes

- 9936235: Logs for env var debuggin

## 4.0.4

### Patch Changes

- 8d37b93: Hotfix for broken config for LANGFUSE env vars

## 4.0.3

### Patch Changes

- 93114fb: Adds LANGFUSE_SECRET_KEY to config template

## 4.0.2

### Patch Changes

- 5feb92b: Backfill company data

## 4.0.1

### Patch Changes

- 0f07d09: Send slack alert on email failure

## 4.0.0

### Major Changes

- 9a58ba4: This is a MAJOR REFACTOR of seo-automation-api-gateway.
  It includes:

  - DB changes
  - Adds scraping, generate markdown, and generate recommendations endpoints
  - Changes API responses to always be wrapped in a "data" property on the response object

## 3.1.0

### Minor Changes

- f841f3d: Updates replyTo and from fields for email

## 3.0.7

### Patch Changes

- b3251ac: Updated the MAIN_HEADING website service prompt to include the old value and use "formValue title" terminology instead of "main heading (h1)".

## 3.0.6

### Patch Changes

- 8e49e36: Don't return an error when STAR fails

## 3.0.5

### Patch Changes

- a5c858b: Updates types for notification and email services

## 3.0.4

### Patch Changes

- 287dd1b: Sort recommmendations
- 287dd1b: H1 edits, fix subject line, add mjml template

## 3.0.3

### Patch Changes

- d168d95: Slack notification

## 3.0.2

### Patch Changes

- 292df9e: H1 edits, fix subject line, add mjml template

## 3.0.1

### Patch Changes

- 9abe29a: Updates group metadata from rank -> ranking and primaryKeyword -> keyword

## 3.0.0

### Major Changes

- bec686e: Updates recommendation type to new enum

## 2.12.0

### Minor Changes

- 3080878: Uses the company email instead of all the admins to send the notifications

## 2.11.0

### Minor Changes

- bcea4a5: - Adds notification post to send emails by groupId
  - Adds UserService to integrate with tenant-service
  - Adds EmailService to integrate with notification-service

## 2.10.1

### Patch Changes

- 1835ee5: Allow for $lte filter

## 2.10.0

### Minor Changes

- 18acd88: Merges metadata on update instead of overwriting it for group and recommendation

## 2.9.4

### Patch Changes

- 2e4a753: Another attempt to fix env vars

## 2.9.3

### Patch Changes

- f098ab5: Updates dockerfile for .env

## 2.9.2

### Patch Changes

- 9d957c4: Loads dotenv in main

## 2.9.1

### Patch Changes

- b5a4412: Adds config module to app module

## 2.9.0

### Minor Changes

- da1846c: Adds group patch route

## 2.8.1

### Patch Changes

- e9562d2: Fixes recommendation type enum from H1 to Main Heading

## 2.8.0

### Minor Changes

- b789f69: Adds better error handling, adds WEBSITE_SERVICE_URL to config-template.yaml

## 2.7.0

### Minor Changes

- 148e9a4: Adds all-exceptions filter for better error messages

## 2.6.0

### Minor Changes

- 7130aa3: - Updates env var name from API_GATEWAY_URL to SEO_API_GATEWAY_URL
  - Adds website-service to call STAR api on recommendation status update

## 2.5.1

### Patch Changes

- 68e1c4b: Adds seoPage relation to recommendation swagger docs

## 2.5.0

### Minor Changes

- dd35c32: ai-seo-automation:

  - adds gateway client
  - creates group per each recommendation llm run
  - saves recommendations to DB with correct seo-page and group ID's

  seo-automation-api-gateway:

  - makes currentValue optional in case there is no current value

  browser:

  - adds gateway client
  - creates seo-page for each scraped page
  - adds relevant metadata like path and domain to json metadata for use elsewhere

## 2.4.0

### Minor Changes

- ff9ed72: Adds pagination to seo-page

## 2.3.1

### Patch Changes

- 440f17c: Fixes the default value for recommendation status from 'suggested' to 'pending'

## 2.3.0

### Minor Changes

- 5b0a8b4: - Adds date transformation for group publishedAt field
  - Adds scripts to insert real staging data for staging testing

## 2.2.0

### Minor Changes

- 67cceb9: Eager load recommendations[].seoPage for group

## 2.1.0

### Minor Changes

- 88cff80: - Adds upsert to seo-page create endpoint to avoid duplicate company_id + domain + path
  - Adds LangFuse support: pulling prompts and tracing

## 2.0.0

### Major Changes

- ee825fa: Adds pagination to group and recommendation, adds patch route for recommendation

## 1.0.0

### Major Changes

- 0c37cb5: New database tables seo_page, recommendation, group

## 0.11.8

### Patch Changes

- 91b07f7: try --include=dev

## 0.11.7

### Patch Changes

- 8c3d04f: Try using npm instead of pnpm

## 0.11.6

### Patch Changes

- f249c7c: Don't mv dockerfile

## 0.11.5

### Patch Changes

- 6bce2b4: Set docker context

## 0.11.4

### Patch Changes

- 1e01655: Temp revert config.yml

## 0.11.3

### Patch Changes

- 615aa67: Temporarily remove lint-and-format circle job

## 0.11.2

### Patch Changes

- 17a7cc8: Removes catch in data-source

## 0.11.1

### Patch Changes

- 367a60b: Trying to fix circleci

## 0.11.0

### Minor Changes

- d80a061: Adds title column to recommendation table
- d976957: - Updates swagger docs
  - Adds bulk create endpoints / service methods for Pages and Recommendations

### Patch Changes

- 17c839f: Removes CrewAI package
- 2e2a04f: Fixes circleci config to remove old python package steps
- bfa6959: Circle ci fix

## 0.10.5

### Patch Changes

- 0220005: Health controller

## 0.10.4

### Patch Changes

- e42b8fb: Fix missing prod depencency

## 0.10.3

### Patch Changes

- 2a6572c: fix nest run

## 0.10.2

### Patch Changes

- af99105: Adds base config vars to template

## 0.10.1

### Patch Changes

- 9b15537: Adds yaml config file

## 0.10.0

### Minor Changes

- e9113de: Install devDependencies during Migration

### Patch Changes

- e9113de: WIP fixing migration
- e9113de: Use PG_CONNECTION_STRING
- e9113de: WIP migration fix
- e9113de: Use standard env vars
- e9113de: Fix migration 3
- e9113de: Fix migration 4

## 0.9.0

### Minor Changes

- 63d211a: Install devDependencies during Migration

### Patch Changes

- 63d211a: WIP fixing migration
- 63d211a: Use PG_CONNECTION_STRING
- 63d211a: WIP migration fix
- 63d211a: Use standard env vars
- 63d211a: Fix migration 3
- 63d211a: Fix migration 4

## 0.8.0

### Minor Changes

- 0b6a3c2: Install devDependencies during Migration

### Patch Changes

- 0b6a3c2: WIP fixing migration
- 0b6a3c2: WIP migration fix
- 0b6a3c2: Use standard env vars
- 0b6a3c2: Fix migration 3
- 0b6a3c2: Fix migration 4

## 0.7.0

### Minor Changes

- 753d97e: Install devDependencies during Migration

### Patch Changes

- 753d97e: WIP fixing migration
- 753d97e: WIP migration fix
- 753d97e: Fix migration 3
- 753d97e: Fix migration 4

## 0.6.0

### Minor Changes

- 36d88ae: Install devDependencies during Migration

### Patch Changes

- 36d88ae: WIP fixing migration
- 36d88ae: WIP migration fix
- 36d88ae: Fix migration 3

## 0.5.0

### Minor Changes

- 276ac16: Install devDependencies during Migration

### Patch Changes

- 276ac16: WIP fixing migration
- 276ac16: WIP migration fix

## 0.4.0

### Minor Changes

- fc8e5df: Install devDependencies during Migration

### Patch Changes

- fc8e5df: WIP fixing migration

## 0.3.0

### Minor Changes

- a6a08c7: Install devDependencies during Migration

## 0.2.0

### Minor Changes

- 6f9d918: Added Docker images for CircleCI

## 0.1.1

### Patch Changes

- 94cd25a: Updates pnpm-workspace to not include packages recursively

## 0.1.0

### Minor Changes

- b9e181c: Adds seo-automation-api-gateway package
