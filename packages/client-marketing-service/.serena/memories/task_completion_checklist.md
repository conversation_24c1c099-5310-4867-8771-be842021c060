# Task Completion Checklist

## Before Marking a Task as Complete

### 1. Code Quality Checks
```bash
# Run linting and fix any issues
pnpm run lint
# If errors exist, run:
pnpm run lint:fix

# Check and fix formatting
pnpm run format
# If errors exist, run:
pnpm run format:fix
```

### 2. Build Verification
```bash
# Ensure the project builds successfully
pnpm run build
```

### 3. Test Execution
```bash
# Run all unit tests
pnpm run test

# For significant changes, also run e2e tests
pnpm run test:e2e
```

### 4. GraphQL Schema
- If GraphQL schema was modified:
  ```bash
  # Start dev server to regenerate schema
  pnpm run start:dev
  # Let it fully start, then stop it
  ```
- **IMPORTANT**: Never manually edit `schema.graphql`
- Test federation compatibility if schema changed (see local-rover README)

### 5. TypeScript Type Checking
- Ensure no TypeScript errors in the IDE
- Check that strict null checks pass
- Verify imports are properly typed

### 6. Database Migrations
- If database schema changed:
  ```bash
  # Generate migration
  pnpm run typeorm:generate
  
  # Run migration
  pnpm run typeorm:run
  ```

### 7. Documentation Updates
- Update README if new setup steps added
- Update CLAUDE.md if new patterns discovered
- Document any new environment variables in `.env.sample`

### 8. Git Hygiene
- Commit frequently with clear messages
- Keep commits focused and atomic
- Never commit secrets or sensitive data
- Ensure working tree is clean

### 9. Review Checklist
- [ ] All lint errors fixed
- [ ] All formatting issues resolved
- [ ] Build succeeds
- [ ] Tests pass
- [ ] GraphQL schema regenerated (if needed)
- [ ] No TypeScript errors
- [ ] Database migrations created (if needed)
- [ ] Documentation updated (if needed)
- [ ] Code follows existing patterns

## Common Issues to Watch For
- Circular dependencies in modules
- Missing dependency injections
- Unhandled promise rejections
- GraphQL resolver conflicts
- Database connection issues
- Missing environment variables