# Client Marketing Service - Project Overview

## Purpose
Client Marketing Service is a NestJS-based API service that provides resources for frontend and backend applications within the client marketing domain. The service primarily focuses on SEO data including keywords and recommendations. It is part of a larger monorepo managed with pnpm workspaces and Turbo.

## Key Responsibilities
- SEO data management (keywords, recommendations)
- Client marketing resource APIs
- GraphQL federation support as a subgraph
- Integration with various marketing-related services

## Architecture
- **Service Type**: Backend API service
- **Framework**: NestJS with GraphQL support
- **Database**: PostgreSQL with TypeORM
- **Authentication**: M2M authentication for GraphQL endpoints (x-lp-api-key header)
- **Deployment**: Docker containerized application

## API Endpoints
- REST endpoints (no auth currently)
- GraphQL endpoint at `/graphql` with GraphiQL interface
- Health check endpoint

## Key Integrations
- Apollo Federation for GraphQL
- AWS services (S3, SNS, SQS)
- Snowflake for data analytics
- Redis for caching with BullMQ for job queues
- Langchain/Anthropic for AI features
- Langfuse for observability

## Environment
- Node.js version specified in monorepo's .nvmrc
- Docker compose for local development
- Datadog tracing for monitoring