# Suggested Commands

## Development Commands
```bash
# Install dependencies
pnpm install

# Start development server with hot reload
pnpm run start:dev

# Start production server
pnpm run start:prod

# Start debug mode
pnpm run start:debug
```

## Database Commands
```bash
# Run database migrations
pnpm run typeorm:run

# Generate new migration
pnpm run typeorm:generate

# Create new migration file
pnpm run typeorm:create

# Revert last migration
pnpm run typeorm:revert

# Seed database
pnpm run seed

# Refresh and reseed database
pnpm run seed:refresh
```

## Testing Commands
```bash
# Run unit tests
pnpm run test

# Run tests in watch mode
pnpm run test:watch

# Run tests with coverage
pnpm run test:cov

# Run e2e tests (isolated database)
pnpm run test:e2e

# Run e2e tests (shared dev database)
pnpm run test:e2e:shared

# Run load tests
pnpm run load-test-feed
```

## Code Quality Commands
```bash
# Check linting
pnpm run lint

# Fix linting issues
pnpm run lint:fix

# Check formatting
pnpm run format

# Fix formatting issues
pnpm run format:fix

# Build the project
pnpm run build
```

## Docker Commands
```bash
# Start local database and services
docker compose up -d

# Stop services
docker compose down

# View logs
docker compose logs -f

# Access pgAdmin
# Navigate to http://localhost:5050
```

## Monorepo Commands
```bash
# Run command for this package from root
pnpm --filter client-marketing-service <command>

# Create changeset (run from package directory)
pnpm run changeset
```

## Git Commands (Darwin/macOS)
```bash
# Standard git commands work normally
git status
git add .
git commit -m "message"
git push

# File operations
ls -la  # List files
find . -name "*.ts"  # Find TypeScript files
grep -r "pattern" .  # Search in files
```

## Environment Setup
```bash
# Copy sample env file
cp .env.sample .env

# Edit environment variables
# Use values from .env.local for local development
```