# Code Style and Conventions

## TypeScript Configuration
- **Target**: ES2023
- **Module**: Node16
- **Strict null checks**: Enabled
- **Decorators**: Enabled (for NestJS)
- **Path aliases**: `src/*` maps to source directory
- **No implicit any**: Disabled (legacy code compatibility)

## Code Organization
- **Structure**: Feature-based modules (each feature in its own directory)
- **Naming**: Kebab-case for directories, PascalCase for classes
- **File naming**: 
  - Services: `*.service.ts`
  - Controllers: `*.controller.ts`
  - Entities: `*.entity.ts`
  - DTOs: `*.dto.ts`
  - GraphQL inputs: `*.input.ts`
  - Modules: `*.module.ts`

## Import Organization (enforced by ESLint)
1. Built-in modules
2. External modules
3. Internal modules
4. Parent/sibling imports
5. Index imports
6. Object imports
7. Type imports
- Alphabetically sorted within groups
- Newline between import groups

## NestJS Patterns
- **Dependency Injection**: Constructor-based injection
- **Decorators**: Heavy use of NestJS decorators
- **Repository Pattern**: TypeORM repositories injected via `@InjectRepository`
- **Module-based architecture**: Each feature is a module

## Testing Conventions
- Test files: `*.spec.ts`
- Located alongside source files
- Coverage requirements exclude:
  - Migration files
  - Seeders
  - Module files
  - Entity files
  - DTOs and inputs
  - Decorators
  - Test utilities

## GraphQL Conventions
- **Schema**: Auto-generated from code (never edit schema.graphql directly)
- **Federation**: Subgraph in federated architecture
- **Resolvers**: Code-first approach with decorators

## Comments
- Minimal comments (only when necessary for complex logic)
- No comment-based documentation unless requested

## Error Handling
- Use NestJS built-in exceptions
- Proper logging with NestJS logger

## Best Practices
- Keep methods focused and single-purpose
- Use async/await for asynchronous operations
- Proper TypeScript types (avoid 'any' when possible)
- Follow existing patterns in the codebase