# Project Structure

## Root Structure
```
client-marketing-service/
├── src/                     # Source code
├── test/                    # E2E tests
├── scripts/                 # Utility scripts
├── .serena/                 # Serena AI configuration
├── .claude/                 # Claude AI instructions
├── compose.yml              # Docker compose for local dev
├── compose.test.yml         # Docker compose for testing
├── Dockerfile               # Main application container
├── migration.Dockerfile     # Migration runner container
└── package.json            # Package configuration
```

## Source Code Organization (`src/`)
```
src/
├── main.ts                 # Application entry point
├── app.module.ts           # Root application module
├── data-source.ts          # TypeORM data source config
├── seeder.ts              # Database seeding entry
├── health.controller.ts    # Health check endpoint
│
├── common/                 # Shared utilities and services
│   ├── decorators/        # Custom decorators
│   ├── services/          # Shared services
│   ├── graphql/           # GraphQL utilities
│   └── types/             # Shared types
│
├── config/                # Configuration module
├── auth/                  # Authentication module
├── middleware/            # Express/NestJS middleware
├── migration/             # Database migrations
├── seeders/               # Database seeders
│
# Feature Modules (each follows similar structure):
├── recommendation/        # Recommendation feature
├── keyword/              # Keyword management
├── campaign/             # Campaign management
├── seo-optimizations/    # SEO optimization features
├── scraped-page/         # Web scraping results
├── blog-topic/           # Blog topic suggestions
├── brand-profile/        # Brand profile management
├── company/              # Company management
├── group/                # Group management
├── property/             # Property management
├── notification/         # Notification system
├── payment/              # Payment processing
├── homepage-feed/        # Homepage feed feature
├── api-gateway/          # API gateway integration
├── snowflake/            # Snowflake data warehouse
├── asset-aggregator/     # Asset aggregation
└── cms/                  # CMS integration
```

## Feature Module Structure
Each feature module typically contains:
```
feature-name/
├── feature-name.module.ts      # Module definition
├── feature-name.service.ts     # Business logic
├── feature-name.controller.ts  # REST endpoints
├── feature-name.resolver.ts    # GraphQL resolvers
├── feature-name.entity.ts      # Database entity
├── dto/                        # Data transfer objects
│   ├── create-*.dto.ts
│   └── update-*.dto.ts
├── inputs/                     # GraphQL inputs
│   ├── create-*.input.ts
│   └── update-*.input.ts
└── feature-name.spec.ts       # Unit tests
```

## Test Structure
```
test/
├── jest-e2e.json          # E2E test configuration
├── app.e2e-spec.ts        # E2E test files
└── fixtures/              # Test data fixtures
```

## Configuration Files
- `tsconfig.json`: TypeScript configuration
- `nest-cli.json`: NestJS CLI configuration
- `eslint.config.mjs`: ESLint rules
- `.env.sample`: Environment variable template
- `global-setup.js`: Jest global setup
- `schema.graphql`: Auto-generated GraphQL schema

## Key Patterns
- **Module-based**: Each feature is a self-contained module
- **Separation of concerns**: Controllers/Resolvers → Services → Repositories
- **DTO/Input validation**: Separate objects for data transfer
- **Entity-first**: Database entities define data structure
- **Code-first GraphQL**: Schema generated from decorators