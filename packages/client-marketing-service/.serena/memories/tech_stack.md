# Technology Stack

## Core Framework
- **NestJS v11**: Main application framework
- **TypeScript**: Primary language (ES2023 target)
- **Node.js**: Runtime (version from monorepo .nvmrc)

## Database & ORM
- **PostgreSQL**: Primary database
- **TypeORM v0.3.20**: Database ORM
- **Redis (ioredis)**: Caching and job queue backend

## GraphQL & API
- **Apollo Server v4**: GraphQL server
- **Apollo Federation**: Microservices architecture
- **GraphQL v16**: Query language
- **Express**: HTTP server platform

## Job Processing
- **BullMQ v5**: Job queue management
- **Bull Board**: Queue monitoring UI

## AI/ML Integration
- **Langchain**: AI application framework
- **Anthropic Claude**: AI model integration
- **OpenAI**: AI model integration
- **Langfuse**: LLM observability

## AWS Services
- **AWS SDK v3**: Cloud services
  - S3: Object storage
  - SNS: Notification service
  - SQS: Message queuing

## Testing
- **Jest v29**: Testing framework
- **Supertest**: HTTP testing
- **Playwright**: E2E testing

## Utilities
- **class-validator**: Validation
- **class-transformer**: Object transformation
- **DataLoader**: Batching and caching
- **UUID**: Unique identifier generation
- **Zod**: Schema validation

## Development Tools
- **SWC**: Fast TypeScript compiler
- **ESLint v9**: Linting
- **Prettier v3**: Code formatting
- **ts-node**: TypeScript execution
- **K6**: Load testing