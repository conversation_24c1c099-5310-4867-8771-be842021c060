# Client Marketing Service

GraphQL API for SEO resources using NestJS with GraphQL federation and TypeORM.

## Architecture

- Apollo Federation for microservices
- Repository pattern, thin resolvers
- PostgreSQL + TypeORM (crew_ai_seo_automation schema)

## Key Entities

1. Company (Federated), ScrapedPage, Keyword, PageKeyword, Scrape
2. Group, Recommendation, ScheduledAction

**Workflow**: DRAFT_PENDING → SURFACING_PENDING/HUMAN_QA_PENDING → SURFACED → PUBLISHED

**Patterns**: Soft deletes, timestamps, JSONB metadata, DB triggers

## Commands

```bash
pnpm start:dev                                     # Local dev
pnpm typeorm:run                                   # Run migrations  
pnpm typeorm:generate src/migration/MigrationName # Generate migration
docker-compose up -d postgres                     # Local DB
```

## Development Rules

- Test utilities in `src/test-utils/`
- Mock external services in tests
- Test schema changes against federation
- Use DataLoader for N+1 prevention
- Test migrations locally first
- Include rollback migrations

## Authentication

**Pattern**: UnifiedAuthContext with policy-based authorization

- `@AuthContext()` decorator injects auth context into resolvers
- Policy classes (e.g., `RecommendationPolicy`) define permissions
- Auth checks: `authContext.can('resource', 'action', companyId)`
- Supports super users and company-scoped access
- M2M authentication via API keys
- Auth0 JWT validation with configurable issuers

**Usage**: All resolver methods require `@AuthContext()` parameter and explicit permission checks

## Config

- API endpoints, DB connections, auth keys (vault), feature flags (LaunchDarkly)
