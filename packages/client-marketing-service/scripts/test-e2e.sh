#!/bin/bash

# Script to run e2e tests with isolated database (default)
set -e

# Function to cleanup on exit
cleanup() {
  echo "🧹 Cleaning up test database..."
  if [ "$CI" != "true" ]; then
    docker compose -f compose.test.yml down --remove-orphans
  fi
}

# Set trap to cleanup on script exit
trap cleanup EXIT

echo "🚀 Starting isolated test database..."

# Check if we're running in CI environment
if [ "$CI" = "true" ]; then
  echo "🔧 Running in CI environment - using secondary container..."
  
  # Wait for database to be ready (CircleCI provides PostgreSQL as secondary container)
  echo "⏳ Waiting for test database to be ready..."
  if command -v pg_isready >/dev/null 2>&1; then
    until pg_isready -h localhost -p 5432 -U postgres; do
      echo "Waiting for PostgreSQL..."
      sleep 2
    done
  else
    echo "⚠️  pg_isready not available, waiting 10 seconds for database..."
    sleep 10
  fi
  
  # Create schema if it doesn't exist
  echo "📋 Creating database schema..."
  if command -v psql >/dev/null 2>&1; then
    psql "postgresql://postgres:123123@localhost:5432/lp_test" -c "CREATE SCHEMA IF NOT EXISTS crew_ai_seo_automation;"
  else
    echo "⚠️  psql not available, skipping schema creation..."
  fi
  
  # Set CircleCI environment variables
  export DATABASE_HOST=localhost
  export DATABASE_PORT=5432
else
  echo "💻 Running in local environment - using Docker Compose..."
  
  # Stop any existing test database containers
  docker compose -f compose.test.yml down --remove-orphans

  # Start the test database
  docker compose -f compose.test.yml up -d db-test

  # Wait for database to be ready
  echo "⏳ Waiting for test database to be ready..."
  until docker compose -f compose.test.yml exec -T db-test pg_isready -U postgres; do
    echo "Waiting for PostgreSQL..."
    sleep 2
  done
  
  # Set local environment variables
  export DATABASE_HOST=localhost
  export DATABASE_PORT=5433
fi

echo "✅ Test database is ready!"

# Set test environment variables
export NODE_ENV=test
export DATABASE_USER=postgres
export DATABASE_PASSWORD=123123
export DATABASE_NAME=lp_test
export DATABASE_SCHEMA=crew_ai_seo_automation
export LOG_LEVEL=error

echo "🧪 Running e2e tests..."
jest --config ./test/jest-e2e.json

echo "✅ E2E tests completed!" 