services:
  db-test:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_DB: lp_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123123
    ports:
      - 5433:5432  # Different port to avoid conflicts with dev database
    configs:
      - source: init-db-test.sql
        target: /docker-entrypoint-initdb.d/init-db-test.sql
    volumes:
      - pgdata_test:/var/lib/postgresql/data

configs:
  init-db-test.sql:
    content: |
      CREATE SCHEMA IF NOT EXISTS crew_ai_seo_automation;

volumes:
  pgdata_test: 