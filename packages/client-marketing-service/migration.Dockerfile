FROM node:22-alpine

ARG NPM_AUTH_TOKEN
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN

# TODO: Not sure why this is needed, NODE_ENV is somehow getting set to prod, causing problems
#   With symlinks from the needed devDependencies (ts-node)
ENV NODE_ENV="development"

RUN mkdir -p /usr/src/app
WORKDIR /usr/src/app

COPY . .

WORKDIR /usr/src/app/packages/client-marketing-service

RUN corepack enable
# Update corepack to the latest version to prevent signature issue.
# https://github.com/pnpm/pnpm/issues/9029#issuecomment-2629866277
RUN npm i -g corepack@latest
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV HUSKY=0

RUN pnpm install --frozen-lockfile --force

CMD ["pnpm", "run", "typeorm:run"]