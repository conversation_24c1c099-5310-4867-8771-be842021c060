{"compilerOptions": {"module": "node16", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "allowJs": true, "resolveJsonModule": true, "esModuleInterop": true, "paths": {"src/*": ["src/*"]}}, "include": ["src/**/*", "test/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}