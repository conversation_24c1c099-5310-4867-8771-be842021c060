extend schema
  @link(url: "https://specs.apollo.dev/federation/v2.3", import: ["@composeDirective", "@extends", "@external", "@inaccessible", "@interfaceObject", "@key", "@override", "@provides", "@requires", "@shareable", "@tag"])

type Company
  @key(fields: "displayId")
  @extends
{
  displayId: ID!
}

type ScheduledAction {
  id: ID!
  companyId: ID!
  workflowType: WorkflowType!
  status: ScheduledActionStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
  scheduledToBeSurfacedAt: DateTime
  surfacedAt: DateTime
  scheduledToBePublishedAt: DateTime
  publishedAt: DateTime
  contentPayload: JSONObject!
  generationPayload: JSONObject!
  failureReason: JSONObject!
  lockedAt: DateTime
  executionName: String
  executionArn: String
  groupScheduledActions: [GroupScheduledAction!]
  company: Company
}

enum WorkflowType {
  SEO
  BLOG
}

enum ScheduledActionStatus {
  DRAFT_PENDING
  HUMAN_QA_PENDING
  SURFACING_PENDING
  SURFACED
  PUBLISHED
  FAILED
  INVALIDATED
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject

type PaginatedScheduledAction {
  data: [ScheduledAction!]!
  totalItems: Int!
  currentPage: Int!
  totalPages: Int!
  itemsPerPage: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

type GroupScheduledAction {
  id: ID!
  groupId: ID!
  scheduledActionId: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  group: Group!
  scheduledAction: ScheduledAction!

  """Company ID from the related group"""
  companyId: ID!
  company: Company
}

type PageKeywordHistory {
  id: ID!
  scrapedPageId: ID!
  keywordId: ID!
  changeNote: String
  updatedBy: ID
  assignedAt: DateTime
  removedAt: DateTime
  createdAt: DateTime!
  scrapedPage: ScrapedPage!
  keyword: Keyword!
}

type Scrape {
  id: ID!
  companyId: ID!
  scrapedPageId: ID!
  rawHtml: String!
  markdown: String!
  currentScrapedValues: JSONObject
  mediaId: ID
  scrapedAt: DateTime!
  createdAt: DateTime!
  updatedAt: DateTime!
  scrapedPage: ScrapedPage!
  recommendations: [Recommendation!]
  company: Company
}

type Recommendation {
  id: ID!
  scrapeId: ID
  groupId: ID!
  type: RecommendationType!
  currentValue: String
  recommendationValue: String!
  reasoning: String!
  status: RecommendationStatus!
  rejectionReason: String
  metadata: JSONObject!
  createdAt: DateTime!
  statusUpdatedAt: DateTime!
  updatedAt: DateTime!
  scrapedPage: ScrapedPage
  companyId: ID
  group: Group
}

enum RecommendationType {
  META_TITLE
  META_DESCRIPTION
  MAIN_HEADING
}

enum RecommendationStatus {
  PENDING
  APPLIED
  REJECTED
}

type ScrapedPage {
  id: ID!
  companyId: ID!
  url: String!
  pageName: String!
  pageType: ScrapedPageType!
  metadata: JSONObject!
  createdAt: DateTime!
  updatedAt: DateTime!
  deletedAt: DateTime
  recommendations: [Recommendation!]
  scrapes: [Scrape!]
  pageKeywordHistory: [PageKeywordHistory!]
  pageKeywords: [PageKeyword!]
  groups: [Group!]
  company: Company
}

enum ScrapedPageType {
  HOMEPAGE
  HOME_VALUATION
  MORTGAGE_CALCULATOR
  BUYERS_GUIDE
  SELLERS_GUIDE
  NEIGHBORHOOD_GUIDE
  AGENT_BIO
  BLOG
}

type PageKeyword {
  id: ID!
  scrapedPageId: ID!
  keywordId: ID!
  originalRank: Float
  currentRank: Float
  rankCheckedAt: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  keyword: Keyword!
  scrapedPage: ScrapedPage!

  """Company ID from the related page"""
  companyId: ID!
  company: Company
}

type Keyword {
  id: ID!
  keyword: String!
  metadata: JSONObject!
  createdAt: DateTime!
  updatedAt: DateTime!
  groups: [Group!]
  pageKeywords: [PageKeyword!]
  pageKeywordHistory: [PageKeywordHistory!]
}

type Rank {
  original: Int
  current: Int
}

type GroupMetadata {
  keyword: String!
  ranking: String @deprecated(reason: "Use \"rank\" field instead")
  rank: Rank
}

type Group {
  id: ID!
  companyId: ID!
  keywordId: ID
  scrapedPageId: ID!
  title: String!
  description: String!
  metadata: GroupMetadata!
  createdAt: DateTime!
  updatedAt: DateTime!
  deletedAt: DateTime
  recommendations: [Recommendation!]!
  keyword: Keyword
  scrapedPage: ScrapedPage
  groupScheduledActions: [GroupScheduledAction!]
  company: Company
}

type RankingGroupKeyword {
  keyword: String!
}

type RankingGroupScrapedPage {
  id: String!
  url: String!
}

type RankingGroup {
  keyword: RankingGroupKeyword!
  scrapedPage: RankingGroupScrapedPage!
}

type SurfacedAndPublishedGroupsForRankingResponse {
  data: [RankingGroup!]!
  hasMore: Boolean!
  totalCount: Int!
  currentPage: Int!
  pageSize: Int!
}

type StoreSEODraftResponse {
  savedKeyword: Keyword
  savedRecommendations: [Recommendation!]
  savedScrape: Scrape
  savedScrapedPage: ScrapedPage
  savedPageKeyword: PageKeyword
  savedGroup: Group
}

type BlogTopic {
  id: ID!
  companyId: ID!
  topic: String!
  blogPostJobId: ID
  airopsExecutionId: String
  cmsPostId: ID
  neighborhoodId: ID
  parentTopic: String
  blogTitle: String
  rationale: String
  createdAt: DateTime!
  updatedAt: DateTime!
  type: BlogTopicType!
}

enum BlogTopicType {
  listicle
  article
}

type CreateBlogTopicsBulkResponse {
  createdBlogTopics: [BlogTopic!]!
}

type PaginatedBlogTopic {
  data: [BlogTopic!]!
  totalItems: Int!
  currentPage: Int!
  totalPages: Int!
  itemsPerPage: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

type BrandProfile {
  id: ID!
  companyId: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  updatedBy: String
  aboutTheBrand: String
  strategicFocus: String
  valueProposition: String
  idealCustomerProfiles: String
  missionAndCoreValues: String
  brandPointOfView: String
  toneOfVoice: String
  ctaText: String
  authorPersona: String
}

type Payment {
  id: ID!
  provider: PaymentProvider!
  providerRef: String
  status: PaymentStatus!
  amountCents: Int!
  currency: String!
  createdAt: DateTime!
  attemptNumber: Int!
  parentPaymentId: ID
  campaignId: ID!
  parentPayment: Payment
  childPayments: [Payment!]!
  campaign: Campaign!
}

enum PaymentProvider {
  TEST
}

enum PaymentStatus {
  PENDING
  SUCCEEDED
  FAILED
  REFUNDED
}

type Campaign {
  id: ID!
  propertyId: ID!
  priceCents: Int!
  currency: String!
  status: CampaignStatus!
  bundleUri: String
  sqsMessageId: String
  retryCount: Int!
  createdBy: String!
  metadata: JSONObject
  createdAt: DateTime!
  updatedAt: DateTime!
  payments: [Payment!]
}

enum CampaignStatus {
  PENDING
  SUCCESS
  ERROR
  CANCELED
}

type MediaObject {
  id: ID!
  thumbnailUrl: String
  smallUrl: String
  mediumUrl: String
  largeUrl: String
}

type LeadDto {
  id: ID!
  firstName: String!
  lastName: String!
  handedOffAt: String!
}

type FeedGroupMetadata {
  ranking: String @deprecated(reason: "Use \"rank\" field instead")
  rank: Rank
}

type FeedPage {
  name: String!
  type: ScrapedPageType!
  url: String!
}

type FeedRecommendation {
  type: RecommendationType!
}

type PaidLead {
  id: String!
}

type FeedItem {
  """
  Timestamp of the feed item. If null or a date in the future, consider it to be an "upcoming" item.
  """
  timestamp: DateTime

  """The card type used to the determine the UI element to render."""
  itemType: ItemType!
  content: Content!
}

enum ItemType {
  BLOG_POST
  SEO_RECOMMENDATION
  LEAD_HANDOFF
  AD_PERFORMANCE
}

union Content = FeedBlogPost | FeedRecommendationGroup | FeedLeads | FeedAdPerformance

"""The AI generated blog post."""
type FeedBlogPost {
  postId: ID!
  title: String!
  postStatus: String!
  publishedAt: DateTime
  scheduledAt: DateTime

  """The id of the image to be used as a thumbnail."""
  mediaId: String

  """The media object containing thumbnail URLs."""
  media: MediaObject
  liveUrl: String
}

"""A group of recommended SEO optimizations."""
type FeedRecommendationGroup {
  id: ID!
  publishedAt: DateTime
  scheduledToBePublishedAt: DateTime!
  keyword: String!
  metadata: FeedGroupMetadata!
  recommendations: [FeedRecommendation!]!
  page: FeedPage!

  """The id of the image to be used as a thumbnail."""
  mediaId: String

  """The media object containing thumbnail URLs."""
  media: MediaObject
}

type FeedLeads {
  leads: [LeadDto!]!
}

type FeedAdPerformance {
  impressionsCount: Int!
  clicksCount: Int!
  leadsCount: Int!
  impressionsCountFormatted: String!
  clicksCountFormatted: String!
  leadsCountFormatted: String!
  paidLeads: [PaidLead!]!
  paidLeadsHref: String!
}

type HomepageFeed {
  items: [FeedItem!]!
}

type PropertyCampaign {
  eligible: Boolean!
}

type Property
  @key(fields: "id")
  @extends
{
  id: ID!
  status: String @external
  companyId: String! @external
  campaign: PropertyCampaign @requires(fields: "status companyId")
}

type SeoOptimization {
  id: ID!
  type: RecommendationType!
  status: RecommendationStatus!
  reasoning: String!
  currentValue: String
  recommendationValue: String!
  rejectionReason: String
  appliedAt: DateTime
  scheduledToBeAppliedAt: DateTime!
}

type SeoOptimizationsGroup {
  id: ID!
  companyId: ID!
  keyword: String!
  pageName: String! @deprecated(reason: "Use \"scrapedPage.pageName\" field instead")
  url: String! @deprecated(reason: "Use \"scrapedPage.url\" field instead")
  scrapedPage: ScrapedPage!
  mediaId: String
  rank: Rank
  ranking: String @deprecated(reason: "Use \"rank\" field instead")
  appliedAt: DateTime
  scheduledToBeAppliedAt: DateTime!
  optimizations: [SeoOptimization!]!
}

type Query {
  """Fetches all campaigns."""
  campaigns: [Campaign!]!

  """Fetches a campaign by ID."""
  campaign(id: ID!): Campaign!

  """Fetches all payments."""
  payments: [Payment!]!

  """Fetches a payment by ID."""
  payment(id: ID!): Payment!
  surfacedGroupsByCompany(companyId: ID!): [Group!]!
  groups(companyId: ID): [Group!]!

  """Get all groups with surfaced or published status (superuser only)"""
  surfacedAndPublishedGroups: [Group!]!

  """
  Get surfaced and published groups with minimal data for ranking operations (superuser only)
  """
  surfacedAndPublishedGroupsForRanking: [Group!]!

  """
  Get paginated surfaced and published groups for ranking operations (superuser only)
  """
  surfacedAndPublishedGroupsForRankingPaginated(page: Int! = 1, limit: Int! = 100): SurfacedAndPublishedGroupsForRankingResponse!

  """Get all recommendations"""
  recommendations(companyId: ID, groupId: ID): [Recommendation!]!

  """Get a recommendation by ID"""
  recommendation(id: ID!): Recommendation!

  """Get scheduled actions with DRAFT_PENDING status"""
  draftPendingActions: [ScheduledAction!]!

  """Get upcoming scheduled actions"""
  upcomingActions(companyId: ID!, workflowType: WorkflowType!): [ScheduledAction!]!

  """Get a scheduled action by ID"""
  scheduledAction(id: ID!): ScheduledAction

  """Get scheduled actions with SURFACED status ready for publishing"""
  surfacedActions(scheduledToBePublishedAt: DateTime!): [ScheduledAction!]!

  """Get paginated scheduled actions"""
  findScheduledActionsPaginated(page: Int! = 1, limit: Int! = 50, companyId: ID, workflowType: WorkflowType, status: ScheduledActionStatus): PaginatedScheduledAction!

  """Get all scraped pages"""
  scrapedPages(companyId: ID): [ScrapedPage!]!

  """Get a scraped page by ID"""
  scrapedPage(id: ID!): ScrapedPage!

  """Get all keywords"""
  keywords: [Keyword!]!

  """Get a keyword by ID"""
  keyword(id: ID!): Keyword!

  """Get all page keywords"""
  pageKeywords(companyId: ID): [PageKeyword!]!

  """Get a page-keyword by ID"""
  pageKeyword(id: ID!): PageKeyword!

  """Get all group scheduled actions"""
  groupScheduledActions(companyId: ID): [GroupScheduledAction!]!

  """Get a group scheduled action by ID"""
  groupScheduledAction(id: ID!): GroupScheduledAction!

  """Fetches the homepage feed."""
  homepageFeed(companyId: ID!, filters: FeedFilters): HomepageFeed!

  """Fetches SEO optimizations."""
  seoOptimizationGroups(companyId: ID!): [SeoOptimizationsGroup!]!

  """Fetches specific SEO optimizations group."""
  seoOptimizationGroup(groupId: ID!): SeoOptimizationsGroup!

  """Fetches the company's brand profile."""
  brandProfile(companyId: ID!): BrandProfile!

  """Get all blog topics with filters and pagination"""
  blogTopics(companyId: ID, filters: BlogTopicFiltersInput, pagination: BlogTopicPaginationInput): PaginatedBlogTopic!

  """Get a blog topic by ID"""
  blogTopic(id: ID!): BlogTopic!

  """
  Select a blog topic for the company (currently random, may become smarter in the future)
  """
  blogTopicSelector(companyId: ID!, preferredNeighborhoods: [ID!]): BlogTopic
}

input FeedFilters {
  timestampStart: String
  timestampEnd: String
}

input BlogTopicFiltersInput {
  topic: String
  blogPostJobId: String
  airopsExecutionId: String
  cmsPostId: String
  neighborhoodId: String
  parentTopic: String
  blogTitle: String
  rationale: String
  type: BlogTopicType
}

input BlogTopicPaginationInput {
  page: Int! = 1
  limit: Int! = 50
}

type Mutation {
  """Create a new campaign with payment (campaigns require payments)."""
  createCampaignWithPayment(input: CreateCampaignWithPaymentDto!): Campaign!

  """Update an existing campaign."""
  updateCampaign(id: ID!, input: UpdateCampaignDto!): Campaign!

  """Delete a campaign."""
  deleteCampaign(id: ID!): Boolean!

  """
  Create a new payment (disabled - payments must be created with campaigns).
  """
  createPayment: Payment! @deprecated(reason: "Disabled. Create payments via createCampaign(input.payment).")

  """Create a new recommendation"""
  createRecommendation(input: CreateRecommendationInput!): Recommendation!

  """
  Allows systems and super users to apply a recommendation. Regular users are not allowed to apply recommendations.
  """
  applyRecommendation(id: ID!): Recommendation!

  """Create owed actions"""
  createOwedActions(companyId: ID!, workflowType: WorkflowType!, generationPayload: JSONObject): ScheduledAction!

  """Create a new scheduled action"""
  createScheduledAction(companyId: String!, workflowType: WorkflowType!, status: ScheduledActionStatus, scheduledToBePublishedAt: DateTime, contentPayload: JSONObject, generationPayload: JSONObject): ScheduledAction!

  """Update an existing scheduled action"""
  updateScheduledAction(id: ID!, status: ScheduledActionStatus, scheduledToBePublishedAt: DateTime, contentPayload: JSONObject, generationPayload: JSONObject, failureReason: JSONObject, executionName: String, executionArn: String, scheduledToBeSurfacedAt: DateTime, surfacedAt: DateTime, publishedAt: DateTime): ScheduledAction!

  """
  Store a SEO draft related entities in the DB for a given scheduled action
  """
  storeSEODraft(scraperResult: ScrapedPageInput!, keywords: String!, recommendations: RecommendationsInput!, companyId: ID!, actionId: ID!): StoreSEODraftResponse!

  """Unsurface a scheduled action that was mistakenly surfaced"""
  unsurfaceScheduledAction(id: ID!, overrideDate: DateTime): ScheduledAction!

  """Create a new keyword"""
  createKeyword(keyword: String!, metadata: JSONObject): Keyword!

  """Update an existing keyword"""
  updateKeyword(id: ID!, keyword: String, metadata: JSONObject): Keyword!

  """Create or update a page keyword record"""
  upsertPageKeyword(scrapedPageId: String!, keyword: String!): Keyword!

  """Update the current rank of a page-keyword"""
  updatePageKeywordRank(input: UpdatePageKeywordRankInput!): PageKeyword!
  rejectSeoOptimization(id: ID!, reasoning: String!): ID!

  """Create or update a company's brand profile."""
  upsertBrandProfile(input: UpsertBrandProfileDto!): BrandProfile!

  """Create multiple blog topics in bulk using single INSERT"""
  createBlogTopicsBulk(input: CreateBlogTopicsBulkInput!): CreateBlogTopicsBulkResponse!

  """Update an existing blog topic (usage for locking with job id)"""
  updateBlogTopic(id: ID!, input: UpdateBlogTopicInput!): BlogTopic!
}

input CreateCampaignWithPaymentDto {
  propertyId: ID!
  priceCents: Int!
  currency: String! = "USD"

  """
  Optional test card number for payment testing (development/testing only). Must be 13-19 digits.
  """
  testCardNumber: String
}

input UpdateCampaignDto {
  priceCents: Int
  status: CampaignStatus
  bundleUri: String
  sqsMessageId: String
}

input CreateRecommendationInput {
  groupId: ID!
  scrapeId: ID!
  type: RecommendationType!
  currentValue: String
  recommendationValue: String!
  reasoning: String!
  status: RecommendationStatus
  metadata: JSONObject
}

input ScrapedPageInput {
  firecrawlId: String!
  url: String!
  metaTitle: String
  metaDescription: String
  mainHeading: MainHeadingInput!
  type: ScrapedPageType!
  markdown: String!
  mediaId: String!
  companyId: String!
}

input MainHeadingInput {
  tag: String!
  index: Float!
  content: String!
  page_id: String
  element_id: String
  section_id: String
  website_id: String
}

input RecommendationsInput {
  metaTitle: SeoRecommendationInput!
  metaDescription: SeoRecommendationInput!
  mainHeading: SeoRecommendationInput
}

input SeoRecommendationInput {
  currentValue: String!
  recommendationValue: String!
  reasoning: String!
}

input UpdatePageKeywordRankInput {
  """The ID of the page-keyword to update"""
  id: ID!

  """The new current rank for the page-keyword"""
  currentRank: Int
}

input UpsertBrandProfileDto {
  companyId: String!
  aboutTheBrand: String
  strategicFocus: String
  valueProposition: String
  idealCustomerProfiles: String
  missionAndCoreValues: String
  brandPointOfView: String
  toneOfVoice: String
  ctaText: String
  authorPersona: String
}

input CreateBlogTopicsBulkInput {
  blogTopics: [CreateBlogTopicInput!]!
}

input CreateBlogTopicInput {
  topic: String!
  blogTitle: String
  neighborhoodId: ID
  companyId: ID!
  rationale: String
  airopsExecutionId: String
  parentTopic: String
  type: BlogTopicType!
}

input UpdateBlogTopicInput {
  topic: String
  blogTitle: String
  blogPostJobId: ID
  airopsExecutionId: String
  cmsPostId: ID
  neighborhoodId: ID
  parentTopic: String
  rationale: String
  type: BlogTopicType
}