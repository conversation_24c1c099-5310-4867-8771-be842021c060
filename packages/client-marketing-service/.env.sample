WEBSITE_SERVICE_URL=http://website-service.luxurycoders.com
TENANT_SERVICE_URL=https://tenant-service.luxurycoders.com
NOTIFICATION_SERVICE_URL=https://notification-service.luxurycoders.com
LANGFUSE_SECRET_KEY=sk-lf-
LANGFUSE_PUBLIC_KEY=pk-lf-
LANGFUSE_ENVIRONMENT=dev
OPENAI_API_KEY=sk-proj-
# Snowflake setup for home feed
SNOWFLAKE_ACCOUNT=ep
SNOWFLAKE_USER=PED_
SNOWFLAKE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----"
# Set to true to enable BullMQ locally (requires Redis)
QUEUE=false
# comma separated list of m2m super api keys
M2M_SUPER_API_KEYS=localkey
# Don't include for local DB
DATABASE_NAME=lp_core_staging
DATABASE_HOST=main-cluster-2-staging.cluster-cqcfymrmfxl7.us-east-1.rds.amazonaws.com
DATABASE_PORT=5432
DATABASE_USER=v-oidc-git-postgres-
DATABASE_PASSWORD=
