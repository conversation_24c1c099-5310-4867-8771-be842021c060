services:
  db:
    image: luxurypresence/main-postgres-staging:latest
    restart: always
    platform: linux/amd64
    environment:
      POSTGRES_DB: ${DATABASE_NAME}
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    ports:
      - 5432:5432
    volumes:
      - pgdata:/var/lib/postgresql/data

  schema-creator:
    image: postgres
    depends_on:
      - db
    command: >
      bash -c "
        echo 'Waiting for PostgreSQL to be ready...'
        until pg_isready -h db -p 5432 -U ${DATABASE_USER}; do
          echo 'PostgreSQL is unavailable - sleeping'
          sleep 2
        done
        echo 'PostgreSQL is ready - creating schema'
        PGPASSWORD=${DATABASE_PASSWORD} psql -h db -p 5432 -U ${DATABASE_USER} -d ${DATABASE_NAME} -c 'CREATE SCHEMA IF NOT EXISTS crew_ai_seo_automation;'
        echo 'Schema created successfully'
      "

  pgAdmin:
    image: dpage/pgadmin4
    restart: always
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=postgres
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    entrypoint: /bin/sh -c "chmod 600 /pgpass; /entrypoint.sh;"
    user: root
    configs:
      - source: servers.json
        target: /pgadmin4/servers.json
      - source: pgpass
        target: /pgpass
    ports:
      - 5050:80

configs:
  init-db.sql:
    content: |
      CREATE SCHEMA crew_ai_seo_automation;
  pgpass:
    content: db:5432:*:${DATABASE_USER}:${DATABASE_PASSWORD}
  servers.json:
    content: |
      {"Servers": {"1": {
        "Group": "Servers",
        "Name": "Local Postgres",
        "Host": "db",
        "Port": 5432,
        "MaintenanceDB": "${DATABASE_NAME}",
        "Username": "${DATABASE_USER}",
        "PassFile": "/pgpass",
        "SSLMode": "prefer"
      }}}

volumes:
  pgdata:
