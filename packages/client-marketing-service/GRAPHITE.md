# Graphite Workflow Guide

## When to Use Graphite vs Standard Git

**Default Approach**: Use standard Git workflow for most development tasks.

**Use Graphite strategically when you have**:

- **3+ related components** needing separate review (API + UI + tests)
- **>500 lines of changes** that can be logically separated
- **Cross-service work** requiring coordinated changes
- **Complex features** where incremental review adds significant value

## Quick Decision Framework

```
Single component + <500 LOC → Standard Git
Multiple components → Consider Graphite
Cross-service changes → Graphite recommended
Team collaboration on large feature → Graphite recommended
```

### Key Graphite Terms

- **Stack**: A sequence of dependent PRs (PR1 ← PR2 ← PR3)
- **Trunk**: The main branch (usually `main`)
- **Individual Submission**: Submit current branch + downstack (`gt submit` - default behavior)
- **Batch Submission**: Submit entire stack at once (`gt submit --stack` - avoid for changeset compatibility)

## How `gt submit` Works With Changesets

**Important**: `gt submit` (default behavior) is compatible with your changeset automation because:

- **Submits current branch + downstack**: When you run `gt submit` on a branch, it submits that branch AND any ancestor branches that don't have PRs yet
- **Individual git pushes**: Each branch gets its own `git push` operation
- **Pre-push hook runs per branch**: Your changeset validation runs once per branch push
- **Proper changeset detection**: Script correctly identifies top vs non-top branches

**Example workflow**:

```bash
gt create pr1 -m "first change"
gt submit  # ← Pushes pr1 only, hook creates empty changeset

gt create pr2 -m "second change"
gt submit  # ← Pushes pr2 only (pr1 already has PR), hook creates empty changeset

gt create pr3 -m "final change"
pnpm changeset  # ← Create real changeset manually
gt submit  # ← Pushes pr3 only, hook validates real changeset
```

## ⚠️ CRITICAL: Understanding `gt submit` vs Rebasing

**This is confusing but extremely important:** `gt submit` does NOT handle rebasing or branch management.

### What `gt submit` Actually Does

- ✅ **Creates/updates PRs** on GitHub
- ✅ **Pushes branches** (current + downstack without PRs)
- ✅ **Validates stack structure** before pushing
- ❌ **Does NOT rebase** branches onto their parents
- ❌ **Does NOT update children** when parent changes

### The Two-Step Process You Need

#### Step 1: Branch Management (Git Operations)

```bash
# When making changes to a branch in your stack:
gt checkout pr1
# Make changes...
gt modify -a      # ← This handles rebasing children automatically
# OR manually:
gt restack        # ← This rebases current branch + children
```

#### Step 2: PR Management (GitHub Operations)

```bash
gt submit         # ← This just pushes and updates PRs
```

### Common Confusion Example

```bash
# You have: main ← pr1 ← pr2 ← pr3
gt checkout pr1
gt submit         # ✅ Creates PR for pr1

# Later, pr1 needs review changes
gt modify -a      # ✅ Updates pr1 + should rebase pr2, pr3
gt submit         # ✅ Pushes updated pr1 to GitHub

# But pr2 and pr3 PRs still show OLD diffs!
# You need to update their PRs too:
gt checkout pr2
gt submit         # ← Push rebased pr2 to update its PR
gt checkout pr3
gt submit         # ← Push rebased pr3 to update its PR
```

### Key Mental Model

Think of Graphite as having **two separate systems**:

1. **Branch Management**: `gt modify`, `gt restack`, `gt sync`
   - Handles Git operations (rebasing, merging, conflicts)
   - Keeps your local stack structure correct

2. **PR Management**: `gt submit`
   - Handles GitHub operations (creating/updating PRs)
   - Just pushes current branch state to remote

### Best Practice Workflow

```bash
# When updating any branch in a stack:
gt checkout branch-to-update
gt modify -a               # 1. Update branch + auto-rebase children
gt submit                  # 2. Update this branch's PR

# Then update affected children PRs:
gt checkout child-branch
gt submit                  # 3. Push rebased child to update its PR
# Repeat for all children...
```

### Why This Matters for Changesets

Your changeset hooks work because `gt submit` triggers individual `git push` operations per branch, but you must remember to `gt submit` each affected branch after rebasing to keep PRs in sync.

## Simplified Workflow

### 1. Setup and Planning

```bash
# Start from updated main
gt sync

# Plan your stack structure first - aim for 2-3 PRs maximum
# Example: SEO-123-pr1-api → SEO-123-pr2-ui → SEO-123-pr3-tests
```

### 2. Create Each PR Individually

```bash
# PR 1: Base implementation
gt create SEO-123-pr1-api -m "SEO-123: feat: add API endpoint"
# Make your changes...
gt submit  # Submit individual PR

# PR 2: UI implementation
gt create SEO-123-pr2-ui -m "SEO-123: feat: add UI component"
# Make your changes...
gt submit  # Submit individual PR

# PR 3: Tests (if needed)
gt create SEO-123-pr3-tests -m "SEO-123: test: add comprehensive tests"
# Make your changes...
pnpm changeset    # Create real changeset for final PR
gt submit  # Submit final PR
```

### 3. Essential Navigation

```bash
# View your stack structure
gt log

# Navigate between branches
gt up      # Go to child branch (upstack)
gt down    # Go to parent branch (downstack)

# Sync with latest main
gt sync
```

### Understanding Stack Diagrams

When you run `gt ls` or `gt log`, you'll see diagrams showing your stack structure. Here's how to read them:

#### Diagram Symbols

- **◯** = A branch/commit (not current)
- **◉** = Your current branch (where you are now)
- **│** = Vertical connection (parent-child relationship)
- **─** = Horizontal connection line
- **┘** = Branch point connection

#### Example: Single Stack

```
◉  test_2         # Current branch (top of stack)
│
◯  test_1         # Middle branch
│
◯  main          # Base branch
```

**Structure:** `main ← test_1 ← test_2` (you're on test_2)

#### Example: Multiple Independent Stacks

```
◯    stack_1_feature_b    # Stack 1: Second branch
│
◯    stack_1_feature_a    # Stack 1: First branch
│ ◉  stack_2_auth         # Stack 2: Current branch
◯─┘  main                 # Both stacks branch from main
```

**Structure:**

- Stack 1: `main ← stack_1_feature_a ← stack_1_feature_b`
- Stack 2: `main ← stack_2_auth` (current)

#### Example: Complex Stack with Multiple Levels

```
◯      test_3
│
◉      test_2            # Current branch (middle of stack)
│
◯      test_1
│   ◯  other_feature     # Different stack
◯───┘  main              # Both stacks from main
```

**Structure:**

- Main stack: `main ← test_1 ← test_2 ← test_3` (you're on test_2)
- Side stack: `main ← other_feature`

## Common Workflows

### 1. Basic Stacked Feature

**When to stack**: Adding a new API endpoint with UI and tests (3 components)

```bash
# Start from main
gt sync

# PR 1: API foundation
gt create SEO-934-pr1-api -m "SEO-934: feat: add user API endpoint"
# Implement API endpoint...
gt submit

# PR 2: UI component
gt create SEO-934-pr2-ui -m "SEO-934: feat: add user profile UI"
# Implement UI that uses the API...
gt submit

# PR 3: Tests
gt create SEO-934-pr3-tests -m "SEO-934: test: add integration tests"
# Add comprehensive tests...
pnpm changeset  # Only on final PR
gt submit
```

### 2. Handling Review Feedback

```bash
# Checkout the PR that needs changes
gt checkout SEO-934-pr1-api

# Make changes
# ... fix issues ...

# Step 1: Update branch + auto-rebase children
gt modify -a

# Step 2: Update this PR on GitHub
gt submit

# Step 3: Update affected children PRs (they were rebased locally but PRs need updating)
gt checkout SEO-934-pr2-ui
gt submit  # Push rebased pr2 to update its PR

gt checkout SEO-934-pr3-tests
gt submit  # Push rebased pr3 to update its PR
```

**Alternative: Use `gt sync` for broader updates**

```bash
gt modify -a       # Update current branch
gt sync            # Sync all branches with main + restack everything
# Then submit any affected PRs individually
```

### 3. Simple Conflict Resolution

```bash
# When syncing hits conflicts
gt sync
# Fix conflicts in your editor
git add .
git rebase --continue

# Verify stack is clean
gt log
```

### 4. Post-Merge Cleanup

After your PRs merge, clean up your local branches:

```bash
# Sync with main and clean up merged branches
gt sync

# Graphite will:
# 1. Pull latest changes from main
# 2. Detect which branches have been merged
# 3. Prompt: "Delete merged branches? (y/n)"
# 4. Remove merged branches from your local repo

# ⚠️  WARNING: Use with extreme caution
gt sync --force
# --force skips ALL confirmations and can:
# - Delete local branches without asking
# - Overwrite trunk history
# - Force destructive operations
# Only use if you're certain of the consequences
```

**Why this is important:**

- Keeps your local repo clean
- Prevents confusion from stale branches
- Automatically handles branch cleanup across all your stacks
- Safer than manual `git branch -d` commands

## Best Practices

### Keep Stacks Simple

- **Maximum 3 PRs per stack** - More becomes hard to manage
- **Each PR 100-400 lines** - Focused and reviewable
- **Clear dependencies** - Each PR should build on the previous logically

### Branch Naming Convention

**Always use the format**: `[TICKET-ID]-pr[N]-[purpose]`

```bash
# Correct examples:
SEO-123-pr1-api      # First PR: API implementation
SEO-123-pr2-ui       # Second PR: UI component
SEO-123-pr3-tests    # Third PR: Tests

# This makes dependencies clear at a glance
```

### Individual Submission Strategy

- **Submit each PR immediately** when ready with `gt submit`
- **Don't use `gt submit --stack`** (batch submit) - creates changeset issues
- **Only use real changeset** on the final PR in your stack

### Linear Ticket Integration

- **Always include ticket ID** in commit messages: `SEO-123: feat: description`
- **Use same ticket ID** for all PRs in a stack
- **PRs automatically link** to Linear tickets

### Sync Frequently

- Run `gt sync` daily to stay updated with main
- Fix conflicts early rather than letting them accumulate

## How Graphite Prevents Premature Merging

### Understanding Stack Merge Protection

When you have a stack of PRs, Graphite ensures they merge in the correct order (bottom to top) even when all CI checks pass on upper branches. Here's how:

#### The Stack Structure

```
Initial State (with your changeset strategy):
main ← PR #1 (empty changeset) ← PR #2 (real changeset with version bumps)
       ↑                            ↑
       targets main                 targets PR #1's branch
```

#### Why Upper PRs Don't Merge Prematurely

**1. Base Branch Targeting**

- PR #2 targets PR #1's branch, NOT main
- GitHub only allows merging into the target branch
- Even if CI passes, PR #2 can only merge into PR #1's branch

**2. Graphite's Merge Queue Protection**

- Graphite enforces strict merge order: bottom to top
- Even with auto-merge enabled, Graphite blocks upper PR merges until:
  - All downstack PRs have merged
  - The PR is automatically rebased onto main
  - Fresh CI runs complete against main

**3. Automatic Rebase Flow**

```
Step 1: PR #1 merges to main
main (now includes PR #1 code, empty changeset)

Step 2: Graphite automatically rebases PR #2
main ← PR #2 (rebased, now targets main directly)
       ↑
       CI runs again here

Step 3: PR #2 merges to main
main (all code + version bumps from PR #2's real changeset)
```

#### The Changeset Strategy in Action

Your automated changeset approach works perfectly with this:

- **Bottom/Intermediate PRs**: Have empty changesets (`---\n---`)
  - Satisfy CI requirements
  - Don't trigger version bumps when merged
  - Just deliver code changes to main

- **Top PR**: Has the real changeset
  - Contains version bumps and descriptions
  - Only merges after all code is in main
  - Triggers the actual release with all accumulated changes

#### Safety Mechanisms

1. **GitHub's Protection**: PRs can only merge into their target branch
2. **Graphite's Enforcement**: Merge queue ensures correct order
3. **CI Re-runs**: Each PR must pass CI against its new base after rebasing
4. **Manual Override Protection**: Even clicking "Merge" on GitHub respects the target branch

#### What Happens If Someone Tries to Merge Out of Order?

- **Via GitHub UI**: PR #2 would merge into PR #1's feature branch (not main)
- **Via Graphite CLI**: Command fails with error about merge order
- **Via Graphite Web UI**: Merge button disabled until ready

This design ensures that your entire stack's changes are:

- Reviewed incrementally (small PRs)
- Merged safely (correct order)
- Released together (single changeset at the top)

## Quick Troubleshooting

### Common Issues

**"Branch has diverged" after merge**

```bash
gt sync  # Graphite will rebase and fix divergence
```

**Need to update a middle PR in your stack**

```bash
gt checkout SEO-123-pr1-api  # Go to the PR that needs changes
# Make your changes...
gt modify -a                 # Step 1: Update branch + auto-rebase children
gt submit                    # Step 2: Update this PR on GitHub

# Step 3: Update affected children PRs
gt checkout SEO-123-pr2-ui
gt submit                    # Push rebased child to update its PR
# Repeat for other children...
```

**Stack got messy after conflicts**

```bash
gt sync           # Try automatic sync first
# If that fails:
gt restack        # Manual rebase of current stack
```

**Want to check stack structure**

```bash
gt log            # Visual tree view of your stacks
gt ls             # Simple list view
```

**Clean up after PRs merge**

```bash
gt sync           # Graphite will prompt to delete merged branches
```

## Advanced Operations (When Needed)

### Reorder PRs in Stack

```bash
gt reorder  # Opens editor to rearrange branches
```

### Insert PR in Middle of Stack

```bash
gt checkout <branch-to-insert-after>
gt create -i SEO-123-pr1.5-fix -m "SEO-123: fix: critical bug"
```

### Delete Branch from Stack

```bash
gt delete <branch-name>  # Automatically rebases children
```

### Undo Last Operation

```bash
gt undo  # Reverses the last Graphite command
```

### Open PRs in Browser

```bash
gt pr          # Open current PR
gt pr --stack  # Open entire stack view
```

## Getting Help

```bash
gt help                    # General help
gt create --help          # Command-specific help
```

## Automatic Changeset Management

### How Husky Hooks Enable the Simplified Workflow

The repository uses automated hooks to handle changesets seamlessly:

**Active Hooks:**

- `.husky/pre-push` → Triggers changeset validation before every push
- `.husky/pre-commit` → Runs lint/format checks
- `.husky/post-rebase` → Manages changesets after rebasing

**The ensure-changeset.sh Script:**

This script runs automatically and handles changeset creation:

```bash
# When you run: gt submit
# What happens automatically:

1. GT prepares to push the branch
2. Pre-push hook triggers: .husky/pre-push
3. ensure-changeset.sh runs and:
   - Detects if branch is top-of-stack
   - Creates empty changeset if non-top branch
   - Validates real changeset if top branch
4. Push completes successfully
5. GT creates/updates the PR
```

**Benefits:**

- ✅ **Automatic changeset creation** (you don't have to remember)
- ✅ **Proper CI compliance** (changesets in every branch)
- ✅ **Top branch validation** (prevents missing version bumps)
- ✅ **Works with both workflows** (GT stacking AND standard Git)

**In Practice:**

- **Standard Git**: Script creates changeset on `git push`
- **Graphite Stacking**: Script creates changeset on `gt submit`
- **Both workflows**: Benefit from automatic changeset management

### Changeset Types Created

**Non-top branches**: Empty changeset

```yaml
# .changeset/auto-[branch-name]-[timestamp].md
---
---
```

**Top branches**: Real changeset (you create manually)

```yaml
# .changeset/[feature-name].md
---
"client-marketing-service": minor
---

Description of changes for release notes
```

## Integration with This Project

**Remember the strategic approach:**

1. **Default to standard Git** for most tasks
2. **Use Graphite only when** complexity justifies the overhead
3. **Keep stacks small** (2-3 PRs maximum)
4. **Always include Linear ticket IDs** in commit messages
5. **Use individual submission** (`gt submit`) not batch submission (`gt submit --stack`)
6. **Trust the automation** - hooks handle changesets automatically

**When in doubt, ask**: "Does this really need stacking, or would a single PR be simpler?"
