import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';

export const AuthContext = createParamDecorator(
  (
    data: unknown,
    ctx: ExecutionContext,
  ): UnifiedAuthContext<AppPolicyRegistry> => {
    const request = GqlExecutionContext.create(ctx).getContext().req;
    if (!request.authContext) {
      throw new Error('AuthContext not available on request');
    }
    return request.authContext;
  },
);

export const DataLoaders = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const context = GqlExecutionContext.create(ctx).getContext();
    if (!context.dataloaders) {
      throw new Error('DataLoaders not available in GraphQL context');
    }
    return context.dataloaders;
  },
);
