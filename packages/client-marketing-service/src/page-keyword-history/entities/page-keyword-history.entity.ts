import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID, IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { Factory } from 'nestjs-seeder';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import {
  PrimaryGeneratedColumn,
  Column,
  Index,
  Entity,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

/**
 * PageKeywordHistory entity
 *
 * This entity stores the history of keyword assignments to pages.
 * It is automatically populated by database triggers on the page_keyword table:
 *
 * 1. trigger_track_keyword_insert: AFTER INSERT on page_keyword
 *    - Creates a new history record when a keyword is assigned to a page
 *    - Defined in migration: 1747673553390-page-keyword-history-triggers.ts
 *
 * 2. trigger_track_keyword_update: AFTER UPDATE on page_keyword
 *    - Creates a new history record when a keyword assignment is updated
 *    - Marks previous records as removed when a keyword is changed
 *    - Defined in migration: 1747673553390-page-keyword-history-triggers.ts
 */
@Entity('page_keyword_history')
@ObjectType()
export class PageKeywordHistory {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'scraped_page_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  scrapedPageId: string;

  @Column({ type: 'uuid', name: 'keyword_id' })
  @IsUUID()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  keywordId: string;

  /**
   * Stores the reason for the change to the page-keyword relationship
   * This field is automatically populated by database triggers that capture the
   * PostgreSQL session variable 'app.change_note' set in PageKeywordService.updatePageKeyword()
   */
  @Column({ type: 'text', name: 'change_note', default: null })
  @IsString()
  @IsOptional()
  @Field(() => String, { nullable: true })
  changeNote: string;

  /**
   * Stores the UUID of the user who made the change
   * This field is automatically populated by database triggers that capture the
   * PostgreSQL session variable 'app.updated_by' set in PageKeywordService.updatePageKeyword()
   */
  @Column({ type: 'uuid', name: 'updated_by', nullable: true })
  @IsUUID()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID, { nullable: true })
  updatedBy: string;

  /**
   * Records when the keyword was assigned to the page
   * This is automatically set by the database trigger when a new page-keyword relationship is created
   */
  @Column({
    type: 'timestamptz',
    name: 'assigned_at',
    nullable: true,
  })
  @Field(() => Date, { nullable: true })
  assignedAt: Date | null;

  /**
   * Records when the keyword was removed from the page
   * This is automatically set by the database trigger when a keyword is changed or removed
   * Used to mark previous history records as no longer active
   */
  @Column({
    type: 'timestamptz',
    name: 'removed_at',
    nullable: true,
  })
  @Field(() => Date, { nullable: true })
  removedAt: Date | null;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @ManyToOne(() => ScrapedPage, scrapedPage => scrapedPage.pageKeywordHistory)
  @JoinColumn({ name: 'scraped_page_id' })
  @Field(() => ScrapedPage)
  scrapedPage: ScrapedPage;

  @ManyToOne(() => Keyword, keyword => keyword.pageKeywordHistory)
  @JoinColumn({ name: 'keyword_id' })
  @Field(() => Keyword)
  keyword: Keyword;
}
