import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';

import { BlogPost } from './dto/blog-post.dto';

@Injectable()
export class CMSService {
  private readonly logger = new Logger(CMSService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async findAIGeneratedBlogPosts(companyId: string): Promise<BlogPost[]> {
    try {
      const {
        data: { data },
      } = await lastValueFrom(
        this.httpService.request({
          url: new URL(
            '/api/v1/posts',
            this.configService.get('cmsService.url'),
          ).toString(),
          params: {
            companyId,
            aiGenerated: 'true',
            includes: 'media',
            limit: 100,
          },
        }),
      );

      return data as BlogPost[];
    } catch (error) {
      this.logger.error('Error fetching AI-generated blog posts', error);

      throw error;
    }
  }
}
