import { Field, ID, ObjectType } from '@nestjs/graphql';

import { MediaDto } from '../../common/services/api-gateway/dto/media.dto';

// TODO: add hero image
@ObjectType({ description: 'The AI generated blog post.' })
export class FeedBlogPost {
  @Field(() => ID)
  postId: string;

  @Field(() => String)
  title: string;

  // TODO: add enum
  @Field(() => String)
  postStatus: 'DRAFT' | 'PUBLISHED' | 'SCHEDULED';

  @Field(() => Date, { nullable: true })
  publishedAt: Date | null;

  @Field(() => Date, { nullable: true })
  scheduledAt: Date | null;

  @Field(() => String, {
    nullable: true,
    description: 'The id of the image to be used as a thumbnail.',
  })
  mediaId: string | null;

  @Field(() => MediaObject, {
    nullable: true,
    description: 'The media object containing thumbnail URLs.',
  })
  media: MediaDto | null;

  @Field(() => String, { nullable: true })
  liveUrl: string | null;
}

@ObjectType()
export class MediaObject {
  @Field(() => ID)
  id: string;

  @Field(() => String, { nullable: true })
  thumbnailUrl: string | null;

  @Field(() => String, { nullable: true })
  smallUrl: string | null;

  @Field(() => String, { nullable: true })
  mediumUrl: string | null;

  @Field(() => String, { nullable: true })
  largeUrl: string | null;
}
