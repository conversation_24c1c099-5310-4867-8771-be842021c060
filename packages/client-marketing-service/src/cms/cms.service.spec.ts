import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';

import { CMSService } from './cms.service';

describe('CMSService', () => {
  let service: CMSService;

  const mockHttpService: HttpService = {
    request: jest.fn().mockReturnValue(of({ data: { data: [] } })),
  } as unknown as HttpService;

  const mockConfigService: ConfigService = {
    get: jest.fn().mockReturnValue('https://cms-service.luxurycoders.com'),
  } as unknown as ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CMSService,
        { provide: HttpService, useValue: mockHttpService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<CMSService>(CMSService);
  });

  it('should fetch data from CMS service', async () => {
    const companyId = 'test-company-id';

    await service.findAIGeneratedBlogPosts(companyId);

    expect(mockHttpService.request).toHaveBeenCalledWith(
      expect.objectContaining({
        url: 'https://cms-service.luxurycoders.com/api/v1/posts',
        params: {
          companyId,
          aiGenerated: 'true',
          includes: 'media',
          limit: 100,
        },
      }),
    );
  });
});
