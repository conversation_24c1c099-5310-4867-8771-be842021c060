import { FilterOperator, PaginateConfig } from 'nestjs-paginate';

import { ScheduledAction } from './entities/scheduled-action.entity';

export const paginateConfig: PaginateConfig<ScheduledAction> = {
  sortableColumns: [
    'companyId',
    'workflowType',
    'status',
    'createdAt',
    'updatedAt',
    'scheduledToBeSurfacedAt',
    'scheduledToBePublishedAt',
  ],
  defaultSortBy: [['companyId', 'ASC']],
  maxLimit: 100,
  defaultLimit: 50,
  filterableColumns: {
    id: [FilterOperator.EQ, FilterOperator.IN],
    companyId: [FilterOperator.EQ, FilterOperator.IN],
    workflowType: [FilterOperator.EQ, FilterOperator.IN],
    status: [FilterOperator.EQ, FilterOperator.IN],
    createdAt: [FilterOperator.EQ, FilterOperator.IN],
    updatedAt: [FilterOperator.EQ, FilterOperator.IN],
    scheduledToBeSurfacedAt: [
      FilterOperator.BTW,
      FilterOperator.LTE,
      FilterOperator.NULL,
      FilterOperator.EQ,
    ],
    scheduledToBePublishedAt: [
      FilterOperator.BTW,
      FilterOperator.LTE,
      FilterOperator.NULL,
      FilterOperator.EQ,
    ],
  },
};
