import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  createBasicResolverModule,
  createMockAuthContext,
  createMockCreateScheduledActionInput,
  createMockScheduledAction,
  createMockUpdateScheduledActionInput,
  createScheduledActionServiceMock,
  mockAuthorizationFailure,
  mockAuthorizationSuccess,
  testAuthorizationError,
  testServiceError,
} from 'src/test-utils';

import { CreateScheduledActionInput } from './dto/create-scheduled-action.dto';
import { UpdateScheduledActionInput } from './dto/update-scheduled-action.dto';
import { Status, WorkflowType } from './entities/scheduled-action.entity';
import { ScheduledActionResolver } from './scheduled-action.resolver';
import { ScheduledActionService } from './scheduled-action.service';

describe('ScheduledActionResolver', () => {
  let resolver: ScheduledActionResolver;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockScheduledActionService: ReturnType<
    typeof createScheduledActionServiceMock
  >;

  const mockScheduledAction = createMockScheduledAction();
  const mockCreateInput = createMockCreateScheduledActionInput();
  const mockUpdateInput = createMockUpdateScheduledActionInput({
    publishedAt: new Date(),
  });

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockScheduledActionService = createScheduledActionServiceMock();

    const module: TestingModule = await createBasicResolverModule(
      ScheduledActionResolver,
      ScheduledActionService,
      mockScheduledActionService,
    );

    resolver = module.get<ScheduledActionResolver>(ScheduledActionResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test resolver definition
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findDraftPending', () => {
    it('should return draft pending actions when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findDraftPending.mockResolvedValue([
        mockScheduledAction,
      ]);

      const result = await resolver.findDraftPending(mockAuthContext);

      expect(result).toEqual([mockScheduledAction]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findDraftPending).toHaveBeenCalled();
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(resolver.findDraftPending(mockAuthContext)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(
        mockScheduledActionService.findDraftPending,
      ).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any) => resolver.findDraftPending(authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (authCtx: any) => resolver.findDraftPending(authCtx),
        mockScheduledActionService,
        'findDraftPending',
        new Error('Service error'),
        mockAuthContext,
      )();
    });

    it('should return empty array when no actions exist', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findDraftPending.mockResolvedValue([]);

      const result = await resolver.findDraftPending(mockAuthContext);

      expect(result).toEqual([]);
      expect(mockScheduledActionService.findDraftPending).toHaveBeenCalled();
    });
  });

  describe('findUpcoming', () => {
    it('should return upcoming actions when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findUpcoming.mockResolvedValue([
        mockScheduledAction,
      ]);

      const result = await resolver.findUpcoming(
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
      );

      expect(result).toEqual([mockScheduledAction]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findUpcoming).toHaveBeenCalledWith(
        'company-id',
        WorkflowType.SEO,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.findUpcoming('company-id', WorkflowType.SEO, mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findUpcoming).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, companyId: string, workflowType: WorkflowType) =>
          resolver.findUpcoming(companyId, workflowType, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        'company-id',
        WorkflowType.SEO,
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (companyId: string, workflowType: WorkflowType, authCtx: any) =>
          resolver.findUpcoming(companyId, workflowType, authCtx),
        mockScheduledActionService,
        'findUpcoming',
        new Error('Service error'),
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
      )();
    });

    it('should handle different workflow types', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findUpcoming.mockResolvedValue([
        mockScheduledAction,
      ]);

      await resolver.findUpcoming(
        'company-id',
        WorkflowType.BLOG,
        mockAuthContext,
      );

      expect(mockScheduledActionService.findUpcoming).toHaveBeenCalledWith(
        'company-id',
        WorkflowType.BLOG,
      );
    });
  });

  describe('findById', () => {
    it('should return scheduled action when authorized and found', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findById.mockResolvedValue(
        mockScheduledAction,
      );

      const result = await resolver.findById('test-id', mockAuthContext);

      expect(result).toEqual(mockScheduledAction);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findById).toHaveBeenCalledWith(
        'test-id',
      );
    });

    it('should return null when action not found', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findById.mockRejectedValue(
        new NotFoundException('Action not found'),
      );

      const result = await resolver.findById('test-id', mockAuthContext);

      expect(result).toBeNull();
      expect(mockScheduledActionService.findById).toHaveBeenCalledWith(
        'test-id',
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.findById('test-id', mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findById).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, id: string) => resolver.findById(id, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        'test-id',
      )();
    });

    it('should rethrow non-NotFoundException errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const serviceError = new Error('Database error');
      mockScheduledActionService.findById.mockRejectedValue(serviceError);

      await expect(
        resolver.findById('test-id', mockAuthContext),
      ).rejects.toThrow('Database error');
      expect(mockScheduledActionService.findById).toHaveBeenCalledWith(
        'test-id',
      );
    });
  });

  describe('createOwedActions', () => {
    it('should create owed actions when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.createOwed.mockResolvedValue(
        mockScheduledAction,
      );

      const result = await resolver.createOwedActions(
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
      );

      expect(result).toEqual(mockScheduledAction);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'create',
        [],
      );
      expect(mockScheduledActionService.createOwed).toHaveBeenCalledWith(
        'company-id',
        WorkflowType.SEO,
        undefined,
      );
    });

    it('should create owed actions with generation payload', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const generationPayload = { model: 'gpt-4o', temperature: 0.8 };
      mockScheduledActionService.createOwed.mockResolvedValue(
        mockScheduledAction,
      );

      const result = await resolver.createOwedActions(
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
        generationPayload,
      );

      expect(result).toEqual(mockScheduledAction);
      expect(mockScheduledActionService.createOwed).toHaveBeenCalledWith(
        'company-id',
        WorkflowType.SEO,
        generationPayload,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.createOwedActions(
          'company-id',
          WorkflowType.SEO,
          mockAuthContext,
        ),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'create',
        [],
      );
      expect(mockScheduledActionService.createOwed).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, companyId: string, workflowType: WorkflowType) =>
          resolver.createOwedActions(companyId, workflowType, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        'company-id',
        WorkflowType.SEO,
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (companyId: string, workflowType: WorkflowType, authCtx: any) =>
          resolver.createOwedActions(companyId, workflowType, authCtx),
        mockScheduledActionService,
        'createOwed',
        new Error('Service error'),
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
      )();
    });
  });

  describe('create', () => {
    it('should create scheduled action when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.create.mockResolvedValue(mockScheduledAction);

      const result = await resolver.create(mockCreateInput, mockAuthContext);

      expect(result).toEqual(mockScheduledAction);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'create',
        [],
      );
      expect(mockScheduledActionService.create).toHaveBeenCalledWith(
        mockCreateInput,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.create(mockCreateInput, mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'create',
        [],
      );
      expect(mockScheduledActionService.create).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, input: CreateScheduledActionInput) =>
          resolver.create(input, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        mockCreateInput,
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (input: CreateScheduledActionInput, authCtx: any) =>
          resolver.create(input, authCtx),
        mockScheduledActionService,
        'create',
        new Error('Service error'),
        mockCreateInput,
        mockAuthContext,
      )();
    });

    it('should create actions with different workflow types', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const blogInput = { ...mockCreateInput, workflowType: WorkflowType.BLOG };
      mockScheduledActionService.create.mockResolvedValue(mockScheduledAction);

      await resolver.create(blogInput, mockAuthContext);

      expect(mockScheduledActionService.create).toHaveBeenCalledWith(blogInput);
    });
  });

  describe('update', () => {
    it('should update scheduled action when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const updatedAction = {
        ...mockScheduledAction,
        status: Status.HUMAN_QA_PENDING,
      };
      mockScheduledActionService.update.mockResolvedValue(updatedAction);

      const result = await resolver.update(
        'test-id',
        mockUpdateInput,
        mockAuthContext,
      );

      expect(result).toEqual(updatedAction);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'update',
        [],
      );
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'test-id',
        mockUpdateInput,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.update('test-id', mockUpdateInput, mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'update',
        [],
      );
      expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, id: string, input: UpdateScheduledActionInput) =>
          resolver.update(id, input, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        'test-id',
        mockUpdateInput,
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (id: string, input: UpdateScheduledActionInput, authCtx: any) =>
          resolver.update(id, input, authCtx),
        mockScheduledActionService,
        'update',
        new Error('Service error'),
        'test-id',
        mockUpdateInput,
        mockAuthContext,
      )();
    });

    it('should update different status values', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const statusUpdate = { status: Status.SURFACED };
      const updatedAction = { ...mockScheduledAction, status: Status.SURFACED };
      mockScheduledActionService.update.mockResolvedValue(updatedAction);

      const result = await resolver.update(
        'test-id',
        statusUpdate,
        mockAuthContext,
      );

      expect(result.status).toBe(Status.SURFACED);
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'test-id',
        statusUpdate,
      );
    });
  });

  describe('findSurfaced', () => {
    it('should return surfaced actions when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const surfacedActions = [
        { ...mockScheduledAction, status: Status.SURFACED },
      ];
      mockScheduledActionService.findSurfaced.mockResolvedValue(
        surfacedActions,
      );

      const testDate = new Date('2024-01-01T00:00:00.000Z');
      const result = await resolver.findSurfaced(testDate, mockAuthContext);

      expect(result).toEqual(surfacedActions);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findSurfaced).toHaveBeenCalledWith(
        testDate,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      const testDate = new Date('2024-01-01T00:00:00.000Z');
      await expect(
        resolver.findSurfaced(testDate, mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'read',
        [],
      );
      expect(mockScheduledActionService.findSurfaced).not.toHaveBeenCalled();
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, date: Date) => resolver.findSurfaced(date, authCtx),
        mockAuthContext,
        authError,
        mockAuthContext,
        new Date('2024-01-01T00:00:00.000Z'),
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (date: Date, authCtx: any) => resolver.findSurfaced(date, authCtx),
        mockScheduledActionService,
        'findSurfaced',
        new Error('Service error'),
        new Date('2024-01-01T00:00:00.000Z'),
        mockAuthContext,
      )();
    });

    it('should return empty array when no surfaced actions exist', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findSurfaced.mockResolvedValue([]);

      const testDate = new Date('2024-01-01T00:00:00.000Z');
      const result = await resolver.findSurfaced(testDate, mockAuthContext);

      expect(result).toEqual([]);
      expect(mockScheduledActionService.findSurfaced).toHaveBeenCalledWith(
        testDate,
      );
    });

    it('should handle different date formats', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findSurfaced.mockResolvedValue([
        mockScheduledAction,
      ]);

      const testDate = new Date('2024-12-31T23:59:59.999Z');
      await resolver.findSurfaced(testDate, mockAuthContext);

      expect(mockScheduledActionService.findSurfaced).toHaveBeenCalledWith(
        testDate,
      );
    });
  });

  describe('method signature coverage', () => {
    it('should handle explicit undefined generationPayload', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.createOwed.mockResolvedValue(
        mockScheduledAction,
      );

      await resolver.createOwedActions(
        'test-company',
        WorkflowType.BLOG,
        mockAuthContext,
        undefined,
      );

      expect(mockScheduledActionService.createOwed).toHaveBeenCalledWith(
        'test-company',
        WorkflowType.BLOG,
        undefined,
      );
    });

    it('should handle different ID formats in findById', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findById.mockResolvedValue(
        mockScheduledAction,
      );

      const testIds = ['uuid-format', '123', 'very-long-id-string'];

      for (const id of testIds) {
        await resolver.findById(id, mockAuthContext);
        expect(mockScheduledActionService.findById).toHaveBeenCalledWith(id);
      }
    });

    it('should handle edge case date formats in findSurfaced', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findSurfaced.mockResolvedValue([
        mockScheduledAction,
      ]);

      const edgeDates = [
        new Date('2024-01-01T00:00:00.000Z'),
        new Date('2024-12-31T23:59:59.999Z'),
        new Date('2024-06-15T12:30:45.123Z'),
      ];

      for (const date of edgeDates) {
        await resolver.findSurfaced(date, mockAuthContext);
        expect(mockScheduledActionService.findSurfaced).toHaveBeenCalledWith(
          date,
        );
      }
    });
  });

  describe('comprehensive coverage scenarios', () => {
    it('should handle multiple workflow types in findUpcoming', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScheduledActionService.findUpcoming.mockResolvedValue([
        mockScheduledAction,
      ]);

      // Test all workflow types
      for (const workflowType of Object.values(WorkflowType)) {
        await resolver.findUpcoming(
          'company-id',
          workflowType,
          mockAuthContext,
        );
        expect(mockScheduledActionService.findUpcoming).toHaveBeenCalledWith(
          'company-id',
          workflowType,
        );
      }
    });

    it('should handle complex generation payloads in createOwedActions', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const complexPayload = {
        model: 'gpt-4o',
        temperature: 0.8,
        maxTokens: 2000,
        systemPrompt: 'Generate SEO content',
        experimentalFeatures: { enableFactChecking: true },
      };
      mockScheduledActionService.createOwed.mockResolvedValue(
        mockScheduledAction,
      );

      await resolver.createOwedActions(
        'company-id',
        WorkflowType.SEO,
        mockAuthContext,
        complexPayload,
      );

      expect(mockScheduledActionService.createOwed).toHaveBeenCalledWith(
        'company-id',
        WorkflowType.SEO,
        complexPayload,
      );
    });

    it('should handle all status values in updates', async () => {
      mockAuthorizationSuccess(mockAuthContext);

      const statusValues = [
        Status.DRAFT_PENDING,
        Status.HUMAN_QA_PENDING,
        Status.SURFACING_PENDING,
        Status.SURFACED,
        Status.PUBLISHED,
        Status.FAILED,
      ];

      for (const status of statusValues) {
        const updatedAction = { ...mockScheduledAction, status };
        mockScheduledActionService.update.mockResolvedValue(updatedAction);

        const result = await resolver.update(
          'test-id',
          { status },
          mockAuthContext,
        );
        expect(result.status).toBe(status);
      }
    });

    it('should handle null values in findById NotFoundException path', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const notFoundError = new NotFoundException('Scheduled action not found');
      mockScheduledActionService.findById.mockRejectedValue(notFoundError);

      const result = await resolver.findById(
        'non-existent-id',
        mockAuthContext,
      );

      expect(result).toBeNull();
      expect(mockScheduledActionService.findById).toHaveBeenCalledWith(
        'non-existent-id',
      );
    });

    it('should verify proper error handling in findById for non-NotFoundException', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const databaseError = new Error('Database connection failed');
      mockScheduledActionService.findById.mockRejectedValue(databaseError);

      await expect(
        resolver.findById('test-id', mockAuthContext),
      ).rejects.toThrow('Database connection failed');
      expect(mockScheduledActionService.findById).toHaveBeenCalledWith(
        'test-id',
      );
    });
  });

  describe('unsurfaceScheduledAction', () => {
    const mockUnsurfaceInput = {
      id: 'test-id',
      overrideDate: undefined,
    };

    it('should unsurface scheduled action when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const unsurfacedAction = {
        ...mockScheduledAction,
        status: Status.SURFACING_PENDING,
      };
      mockScheduledActionService.unsurfaceScheduledAction.mockResolvedValue(
        unsurfacedAction,
      );

      const result = await resolver.unsurfaceScheduledAction(
        mockUnsurfaceInput,
        mockAuthContext,
      );

      expect(result).toEqual(unsurfacedAction);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'update',
        [],
      );
      expect(
        mockScheduledActionService.unsurfaceScheduledAction,
      ).toHaveBeenCalledWith('test-id', undefined);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.unsurfaceScheduledAction(mockUnsurfaceInput, mockAuthContext),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scheduledAction',
        'update',
        [],
      );
      expect(
        mockScheduledActionService.unsurfaceScheduledAction,
      ).not.toHaveBeenCalled();
    });
  });
});
