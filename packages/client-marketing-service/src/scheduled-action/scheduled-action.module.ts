import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ScheduledAction } from './entities/scheduled-action.entity';
import { ScheduledActionResolver } from './scheduled-action.resolver';
import { ScheduledActionService } from './scheduled-action.service';
import { WebsiteService } from '../common/services/website/website.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ScheduledAction]),
    HttpModule,
    ConfigModule,
  ],
  providers: [ScheduledActionService, ScheduledActionResolver, WebsiteService],
  exports: [ScheduledActionService],
})
export class ScheduledActionModule {}
