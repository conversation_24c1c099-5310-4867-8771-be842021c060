import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, PaginateQuery } from 'nestjs-paginate';
import { <PERSON>N<PERSON>, LessThanOrEqual, Repository } from 'typeorm';

import { CreateScheduledActionInput } from './dto/create-scheduled-action.dto';
import {
  SaveSEODraftArgs,
  StoreSEODraftResponse,
} from './dto/save-seo-entities.dto';
import { UpdateScheduledActionInput } from './dto/update-scheduled-action.dto';
import {
  PaginatedScheduledAction,
  ScheduledAction,
  Status,
  WorkflowType,
} from './entities/scheduled-action.entity';
import { paginateConfig } from './scheduled-action.paginate';
import { WebsiteService } from '../common/services/website/website.service';
import { RecommendationType } from '../common/types/scraped-element.type';
import { PaginateToGQL } from '../common/utils/pagination';
import { Group } from '../group/entities/group.entity';
import { GroupScheduledAction } from '../group-scheduled-action/entities/group-scheduled-action.entity';
import { Keyword } from '../keyword/entities/keyword.entity';
import { PageKeyword } from '../page-keyword/entities/page-keyword.entity';
import { Recommendation } from '../recommendation/entities/recommendation.entity';
import { Scrape } from '../scrape/entities/scrape.entity';
import { ScrapedPage } from '../scraped-page/entities/scraped-page.entity';

@Injectable()
export class ScheduledActionService {
  private readonly logger = new Logger(ScheduledActionService.name);

  constructor(
    @InjectRepository(ScheduledAction)
    private readonly scheduledActionRepository: Repository<ScheduledAction>,
    private readonly websiteService: WebsiteService,
  ) {}

  async findDraftPending(): Promise<ScheduledAction[]> {
    this.logger.log('Fetching DRAFT_PENDING scheduled actions');

    return this.scheduledActionRepository.manager.transaction(
      async transactionalEntityManager => {
        const actions = await transactionalEntityManager.find(ScheduledAction, {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC', // Process oldest first
          },
          lock: { mode: 'pessimistic_write' },
        });

        return actions;
      },
    );
  }

  async findUpcoming(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]> {
    this.logger.log('Fetching upcoming scheduled actions');

    return this.scheduledActionRepository.find({
      where: {
        companyId,
        workflowType,
        status: Status.SURFACING_PENDING,
      },
    });
  }

  async createOwed(
    companyId: string,
    workflowType: WorkflowType,
    generationPayload?: object,
  ): Promise<ScheduledAction> {
    this.logger.log('Creating upcoming scheduled action');

    // If the work identifier function runs again before the action gets a scheduled_to_be_surfaced_at date,
    // these new actions will not be picked up, so we'll create "duplicates".
    // We may need to rethink the filters used to decide wether we owe actions or not.
    const scheduledAction = this.scheduledActionRepository.create({
      companyId,
      workflowType,
      status: Status.DRAFT_PENDING,
      ...(generationPayload ? { generationPayload } : {}),
    });

    return await this.scheduledActionRepository.save(scheduledAction);
  }
  async findById(id: string): Promise<ScheduledAction> {
    this.logger.log(`Fetching scheduled action with ID: ${id}`);

    const scheduledAction = await this.scheduledActionRepository.findOne({
      where: { id },
      relations: ['groupScheduledActions'],
    });

    if (!scheduledAction) {
      this.logger.error(`Scheduled action with ID ${id} not found`);
      throw new NotFoundException(`Scheduled action with ID ${id} not found`);
    }

    return scheduledAction;
  }

  async create(
    createInput: CreateScheduledActionInput,
  ): Promise<ScheduledAction> {
    this.logger.log(
      `Creating new scheduled action for company ${createInput.companyId}`,
    );

    // Set default status if not provided
    const status = createInput.status || Status.DRAFT_PENDING;

    // Create the new scheduled action entity
    const scheduledAction = this.scheduledActionRepository.create({
      ...createInput,
      status,
      contentPayload: createInput.contentPayload || {},
      generationPayload: createInput.generationPayload || {},
      failureReason: {},
    });

    // Save to the database
    const savedAction =
      await this.scheduledActionRepository.save(scheduledAction);
    this.logger.log(`Created scheduled action with ID: ${savedAction.id}`);

    return savedAction;
  }

  async update(
    id: string,
    updateInput: UpdateScheduledActionInput,
  ): Promise<ScheduledAction> {
    this.logger.log(`Updating scheduled action with ID: ${id}`);

    // Find the existing scheduled action
    const existingAction = await this.scheduledActionRepository.findOne({
      where: { id },
    });

    if (!existingAction) {
      this.logger.error(`Scheduled action with ID ${id} not found`);
      throw new NotFoundException(`Scheduled action with ID ${id} not found`);
    }

    const updateData: Partial<ScheduledAction> = {
      ...updateInput,
    };

    // If the status is being updated, reset the lockedAt field
    if (updateInput.status && updateInput.status !== existingAction.status) {
      updateData.lockedAt = null;
    }

    // Update the entity
    Object.assign(existingAction, updateData);

    // Save to the database
    const updatedAction =
      await this.scheduledActionRepository.save(existingAction);
    this.logger.log(`Updated scheduled action with ID: ${updatedAction.id}`);

    // For some reason updatedAction isn't returning the full record which breaks the resolver
    // So re-selecting
    const newAction = await this.scheduledActionRepository.findOne({
      where: { id },
    });

    // Typescript complains if we don't check for null
    if (!newAction) {
      this.logger.error(`Scheduled action with ID ${id} not found`);
      throw new NotFoundException(`Scheduled action with ID ${id} not found`);
    }

    return newAction;
  }

  async findPaginated(
    query: PaginateQuery,
    companyId?: string,
    workflowType?: WorkflowType | null,
    status?: Status | null,
  ): Promise<PaginatedScheduledAction> {
    this.logger.log('Fetching paginated scheduled actions');

    // Build where clause for filtering
    const whereClause = this.buildWhereClause(companyId, workflowType, status);

    // Use nestjs-paginate to get paginated results
    const paginatedResult = await paginate<ScheduledAction>(
      query,
      this.scheduledActionRepository,
      {
        ...paginateConfig,
        where: whereClause,
      },
    );

    // Transform the result to match our GraphQL schema
    const paginatedScheduledAction: PaginatedScheduledAction =
      PaginateToGQL(paginatedResult);
    return paginatedScheduledAction;
  }

  private buildWhereClause(
    companyId?: string,
    workflowType?: WorkflowType | null,
    status?: Status | null,
  ) {
    const whereClause: any = {};

    if (!companyId && !workflowType && !status) {
      return null;
    }

    if (companyId) {
      whereClause.companyId = companyId;
    }
    if (workflowType) {
      whereClause.workflowType = workflowType;
    }
    if (status) {
      whereClause.status = status;
    }

    return whereClause;
  }

  async findSurfaced(
    scheduledToBePublishedAt: Date,
  ): Promise<ScheduledAction[]> {
    this.logger.log(
      'Fetching SURFACED scheduled actions ready for publishing',
      {
        scheduledToBePublishedAt: scheduledToBePublishedAt.toISOString(),
      },
    );

    // Parse the date string and create end of day boundary
    const targetDate = new Date(scheduledToBePublishedAt);
    targetDate.setHours(23, 59, 59, 999); // End of day

    return this.scheduledActionRepository.find({
      where: {
        status: Status.SURFACED,
        scheduledToBePublishedAt: LessThanOrEqual(targetDate),
      },
      order: {
        scheduledToBePublishedAt: 'ASC', // Process actions scheduled earliest first
      },
    });
  }

  async storeSEODraft(input: SaveSEODraftArgs): Promise<StoreSEODraftResponse> {
    this.logger.log('Storing SEO draft related entities');
    if (
      !input.companyId ||
      !input.scraperResult ||
      !input.scraperResult.url ||
      !input.scraperResult.type ||
      !input.keywords ||
      !input.recommendations ||
      !input.actionId
    ) {
      throw new BadRequestException('Missing required input fields');
    }
    let savedScrapedPage: ScrapedPage | null = null;
    let savedKeyword: Keyword | null = null;
    let savedScrape: Scrape | null = null;
    let savedPageKeyword: PageKeyword | null = null;
    const savedRecommendations: Recommendation[] = [];
    let savedGroup: Group | null = null;
    // Transaction for saving entities in a single transaction
    await this.scheduledActionRepository.manager.transaction(
      async transactionalEntityManager => {
        // Get page data from STAR API
        let starPage;
        try {
          starPage = await this.websiteService.getPage(input.scraperResult.url);
        } catch (error) {
          this.logger.error(
            'Failed to fetch page data from website service',
            error,
          );
          throw new BadRequestException('Unable to fetch page metadata');
        }
        // Save scraped page
        savedScrapedPage = await transactionalEntityManager.save(ScrapedPage, {
          companyId: input.companyId,
          url: input.scraperResult.url,
          pageName: starPage.name,
          pageType: input.scraperResult.type,
        });

        // Save scrape
        savedScrape = await transactionalEntityManager.save(Scrape, {
          companyId: input.companyId,
          scrapedPageId: savedScrapedPage.id,
          currentScrapedValues: {
            metaTitle: input.scraperResult.metaTitle,
            metaDescription: input.scraperResult.metaDescription,
            mainHeading: input.scraperResult.mainHeading?.content,
          },
          // TODO: Remove this once this column is removed.
          rawHtml: 'rawHtml to be deprecated',
          markdown: input.scraperResult.markdown,
          mediaId: input.scraperResult.mediaId,
        });

        // Save keywords
        await transactionalEntityManager.upsert(
          Keyword,
          { keyword: input.keywords },
          ['keyword'],
        );
        // Now fetch the entity
        savedKeyword = await transactionalEntityManager.findOne(Keyword, {
          where: { keyword: input.keywords },
        });

        // Save page-keyword
        if (savedScrapedPage && savedKeyword) {
          savedPageKeyword = await transactionalEntityManager.save(
            PageKeyword,
            {
              scrapedPageId: savedScrapedPage.id,
              keywordId: savedKeyword.id,
            },
          );

          // Save group
          savedGroup = await transactionalEntityManager.save(Group, {
            companyId: input.companyId,
            keywordId: savedKeyword.id,
            scrapedPageId: savedScrapedPage.id,
            // TODO: Remove this once title and description columns are removed.
            title: 'Title to be deprecated',
            description: 'Description to be deprecated',
          });
        }

        // Save recommendations
        for (const [recommendationKey, recommendationValue] of Object.entries(
          input.recommendations,
        )) {
          if (!recommendationValue) {
            continue;
          }

          const recommendationType =
            this.getRecommendationType(recommendationKey);
          const savedRecommendation = await transactionalEntityManager.save(
            Recommendation,
            {
              type: recommendationType,
              currentValue: recommendationValue.currentValue,
              recommendationValue: recommendationValue.recommendationValue,
              reasoning: recommendationValue.reasoning,
              scrapeId: savedScrape?.id,
              groupId: savedGroup?.id,
            },
          );
          savedRecommendations.push(savedRecommendation);
        }

        // Save group-scheduled-action
        if (savedGroup) {
          await transactionalEntityManager.save(GroupScheduledAction, {
            groupId: savedGroup.id,
            scheduledActionId: input.actionId,
          });
        }
      },
    );

    return {
      savedKeyword,
      savedRecommendations,
      savedScrape,
      savedScrapedPage,
      savedPageKeyword,
      savedGroup,
    };
  }

  private getRecommendationType(recommendationKey: string): RecommendationType {
    // transform key to upper underscore
    const upperUnderscoreKey = recommendationKey
      .replace(/([A-Z])/g, '_$1')
      .toUpperCase();
    const recommendationType =
      RecommendationType[upperUnderscoreKey as keyof typeof RecommendationType];
    if (!recommendationType) {
      this.logger.error(
        `Invalid recommendation type: ${recommendationKey} (${upperUnderscoreKey})`,
      );
      throw new BadRequestException(
        `Invalid recommendation type: ${recommendationKey}`,
      );
    }
    return recommendationType;
  }

  async unsurfaceScheduledAction(
    id: string,
    overrideDate?: Date,
  ): Promise<ScheduledAction> {
    this.logger.log(`Unsurfacing scheduled action with ID: ${id}`);

    return this.scheduledActionRepository.manager.transaction(
      async transactionalEntityManager => {
        const scheduledAction = await transactionalEntityManager.findOne(
          ScheduledAction,
          {
            where: { id },
            relations: ['groupScheduledActions', 'groupScheduledActions.group'],
          },
        );

        if (!scheduledAction) {
          throw new NotFoundException(
            `Scheduled action with ID ${id} not found`,
          );
        }

        if (
          scheduledAction.status === Status.DRAFT_PENDING ||
          scheduledAction.status === Status.SURFACING_PENDING
        ) {
          throw new BadRequestException(
            `Cannot unsurface scheduled action with status ${scheduledAction.status}. Only actions with status HUMAN_QA_PENDING, SURFACED, PUBLISHED, FAILED, or INVALIDATED can be unsurfaced.`,
          );
        }

        const groups =
          scheduledAction.groupScheduledActions?.map(gsa => gsa.group) || [];

        for (const group of groups) {
          if (group?.id) {
            const recommendations = await transactionalEntityManager.find(
              Recommendation,
              {
                where: {
                  groupId: group.id,
                },
              },
            );

            if (recommendations.length > 0) {
              await transactionalEntityManager.remove(recommendations);
              this.logger.log(
                `Deleted ${recommendations.length} recommendations for group ${group.id}`,
              );
            }

            await transactionalEntityManager.softDelete(Group, {
              id: group.id,
            });
            this.logger.log(`Soft deleted group ${group.id}`);
          }
        }

        let newScheduledDate: Date;

        if (overrideDate) {
          newScheduledDate = overrideDate;
        } else {
          const maxDateResult = await transactionalEntityManager
            .createQueryBuilder(ScheduledAction, 'sa')
            .select('MAX(sa.scheduledToBeSurfacedAt)', 'maxDate')
            .where('sa.companyId = :companyId', {
              companyId: scheduledAction.companyId,
            })
            .andWhere('sa.id != :currentId', { currentId: id })
            .getRawOne();

          const maxDate = maxDateResult?.maxDate;
          const baseDate = maxDate ? new Date(maxDate) : new Date();

          newScheduledDate = new Date(baseDate);
          newScheduledDate.setDate(newScheduledDate.getDate() + 7);
        }

        await transactionalEntityManager.update(ScheduledAction, id, {
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: newScheduledDate,
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        });

        const updatedAction = await transactionalEntityManager.findOne(
          ScheduledAction,
          {
            where: { id },
          },
        );

        if (!updatedAction) {
          throw new NotFoundException(
            `Scheduled action with ID ${id} not found after update`,
          );
        }

        this.logger.log(`Successfully unsurfaced scheduled action ${id}`);
        return updatedAction;
      },
    );
  }
}
