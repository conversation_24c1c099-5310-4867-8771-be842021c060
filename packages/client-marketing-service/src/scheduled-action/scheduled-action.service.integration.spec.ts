import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { WebsiteService } from 'src/common/services/website/website.service';
import { Group } from 'src/group/entities/group.entity';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { EntityManager } from 'typeorm';

import { ScheduledAction } from './entities/scheduled-action.entity';
import { ScheduledActionService } from './scheduled-action.service';

describe('ScheduledActionService - scrapedPageId Integration', () => {
  let service: ScheduledActionService;
  let mockEntityManager: Partial<EntityManager>;

  const mockScrapedPage = {
    id: 'scraped-page-123',
    url: 'https://example.com',
    pageName: 'Test Page',
  };

  const mockScrape = {
    id: 'scrape-123',
    scrapedPageId: 'scraped-page-123',
  };

  const mockKeyword = {
    id: 'keyword-123',
    keyword: 'test keyword',
  };

  const mockGroup = {
    id: 'group-123',
    keywordId: 'keyword-123',
    scrapedPageId: 'scraped-page-123',
  };

  beforeEach(async () => {
    mockEntityManager = {
      save: jest.fn().mockImplementation((entityClass, data) => {
        if (entityClass === ScrapedPage)
          return Promise.resolve(mockScrapedPage);
        if (entityClass === Scrape) return Promise.resolve(mockScrape);
        if (entityClass === Keyword) return Promise.resolve(mockKeyword);
        if (entityClass === Group)
          return Promise.resolve({ ...mockGroup, ...data });
        if (entityClass === PageKeyword)
          return Promise.resolve({ id: 'page-keyword-123' });
        if (entityClass === Recommendation)
          return Promise.resolve({ id: 'rec-123' });
        if (entityClass === GroupScheduledAction)
          return Promise.resolve({ id: 'gsa-123' });
        return Promise.resolve(data);
      }),
      upsert: jest.fn().mockResolvedValue({}),
      findOne: jest.fn().mockResolvedValue(mockKeyword),
    };

    const mockScheduledActionRepository = {
      manager: {
        transaction: jest.fn().mockImplementation(async cb => {
          return await cb(mockEntityManager);
        }),
      },
    };

    const mockWebsiteService = {
      getPage: jest.fn().mockResolvedValue({
        name: 'Test Page',
        // Add any other properties that might be needed
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledActionService,
        {
          provide: getRepositoryToken(ScheduledAction),
          useValue: mockScheduledActionRepository,
        },
        {
          provide: WebsiteService,
          useValue: mockWebsiteService,
        },
      ],
    }).compile();

    service = module.get<ScheduledActionService>(ScheduledActionService);
  });

  describe('storeSEODraft - scrapedPageId data integrity', () => {
    it('should set scrapedPageId when creating a group', async () => {
      const input = {
        scraperResult: {
          firecrawlId: 'firecrawl-123',
          url: 'https://example.com',
          metaTitle: 'Current Title',
          metaDescription: 'Current Description',
          mainHeading: {
            tag: 'h1',
            index: 0,
            content: 'Current Heading',
          },
          type: 'HOMEPAGE' as any,
          markdown: '# Test',
          mediaId: 'media-123',
          companyId: 'company-123',
        },
        keywords: 'test keyword',
        recommendations: {
          metaTitle: {
            currentValue: 'Current Title',
            recommendationValue: 'Recommended Title',
            reasoning: 'Better for SEO',
          },
          metaDescription: {
            currentValue: 'Current Description',
            recommendationValue: 'Recommended Description',
            reasoning: 'Better for SEO',
          },
          mainHeading: {
            currentValue: 'Current Heading',
            recommendationValue: 'Recommended Heading',
            reasoning: 'Better for SEO',
          },
        },
        companyId: 'company-123',
        actionId: 'action-123',
      };

      await service.storeSEODraft(input);

      // Verify that Group was saved with scrapedPageId
      expect(mockEntityManager.save).toHaveBeenCalledWith(
        Group,
        expect.objectContaining({
          companyId: 'company-123',
          keywordId: 'keyword-123',
          scrapedPageId: 'scraped-page-123',
        }),
      );
    });

    it('should ensure all recommendations point to the same scraped page', async () => {
      const input = {
        scraperResult: {
          firecrawlId: 'firecrawl-123',
          url: 'https://example.com',
          metaTitle: 'Current Title',
          metaDescription: 'Current Description',
          mainHeading: {
            tag: 'h1',
            index: 0,
            content: 'Current Heading',
          },
          type: 'HOMEPAGE' as any,
          markdown: '# Test',
          mediaId: 'media-123',
          companyId: 'company-123',
        },
        keywords: 'test keyword',
        recommendations: {
          metaTitle: {
            currentValue: 'Current Title',
            recommendationValue: 'Recommended Title',
            reasoning: 'Better for SEO',
          },
          metaDescription: {
            currentValue: 'Current Description',
            recommendationValue: 'Recommended Description',
            reasoning: 'Better for SEO',
          },
          mainHeading: {
            currentValue: 'Current Heading',
            recommendationValue: 'Recommended Heading',
            reasoning: 'Better for SEO',
          },
        },
        companyId: 'company-123',
        actionId: 'action-123',
      };

      await service.storeSEODraft(input);

      // Verify Scrape was created with correct scrapedPageId
      expect(mockEntityManager.save).toHaveBeenCalledWith(
        Scrape,
        expect.objectContaining({
          scrapedPageId: 'scraped-page-123',
        }),
      );

      // Verify all recommendations were created with the same scrapeId
      const recommendationCalls = (
        mockEntityManager.save as jest.Mock
      ).mock.calls.filter(call => call[0] === Recommendation);

      expect(recommendationCalls).toHaveLength(3);
      recommendationCalls.forEach(call => {
        expect(call[1]).toMatchObject({
          scrapeId: 'scrape-123',
          groupId: 'group-123',
        });
      });
    });

    it('should maintain referential integrity between entities', async () => {
      const input = {
        scraperResult: {
          firecrawlId: 'firecrawl-123',
          url: 'https://example.com',
          metaTitle: 'Current Title',
          metaDescription: 'Current Description',
          mainHeading: {
            tag: 'h1',
            index: 0,
            content: 'Current Heading',
          },
          type: 'HOMEPAGE' as any,
          markdown: '# Test',
          mediaId: 'media-123',
          companyId: 'company-123',
        },
        keywords: 'test keyword',
        recommendations: {
          metaTitle: {
            currentValue: 'Current Title',
            recommendationValue: 'Recommended Title',
            reasoning: 'Better for SEO',
          },
          metaDescription: {
            currentValue: 'Current Description',
            recommendationValue: 'Recommended Description',
            reasoning: 'Better for SEO',
          },
          mainHeading: {
            currentValue: 'Current Heading',
            recommendationValue: 'Recommended Heading',
            reasoning: 'Better for SEO',
          },
        },
        companyId: 'company-123',
        actionId: 'action-123',
      };

      const result = await service.storeSEODraft(input);

      // Verify the response structure maintains integrity
      expect(result.savedGroup).toBeDefined();
      expect(result.savedScrapedPage).toBeDefined();
      expect(result.savedScrape).toBeDefined();

      // The group should reference the same scraped page
      expect(result.savedGroup?.scrapedPageId).toBe(
        result.savedScrapedPage?.id,
      );
    });
  });
});
