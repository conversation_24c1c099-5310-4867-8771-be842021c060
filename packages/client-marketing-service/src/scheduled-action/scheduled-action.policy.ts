import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class ScheduledActionPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  /**
   * Checks if the user has permission to read draft pending actions
   * Only super users can access this functionality
   * @returns boolean
   */
  read = () => {
    return this.auth.isSuper();
  };

  /**
   * Checks if the user has permission to create scheduled actions
   * Only super users can access this functionality
   * @returns boolean
   */
  create = () => {
    return this.auth.isSuper();
  };

  /**
   * Checks if the user has permission to update scheduled actions
   * Only super users can access this functionality
   * @returns boolean
   */
  update = () => {
    return this.auth.isSuper();
  };
}
