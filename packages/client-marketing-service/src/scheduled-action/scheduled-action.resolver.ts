import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import {
  Args,
  ID,
  Mutation,
  Query,
  Resolver,
  Int,
  ResolveField,
  Parent,
} from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { PaginateQuery } from 'nestjs-paginate';
import { AppPolicyRegistry } from 'src/auth.module';
import { Company } from 'src/company/entities/company.entity';
import { AuthContext } from 'src/graphql.decorator';

import { CreateScheduledActionInput } from './dto/create-scheduled-action.dto';
import {
  SaveSEODraftArgs,
  StoreSEODraftResponse,
} from './dto/save-seo-entities.dto';
import { UnsurfaceScheduledActionInput } from './dto/unsurface-scheduled-action.dto';
import { UpdateScheduledActionInput } from './dto/update-scheduled-action.dto';
import {
  ScheduledAction,
  Status,
  PaginatedScheduledAction,
  WorkflowType,
} from './entities/scheduled-action.entity';
import { ScheduledActionService } from './scheduled-action.service';

// Currently, only super users can access scheduled actions
@Resolver(() => ScheduledAction)
export class ScheduledActionResolver {
  constructor(
    private readonly scheduledActionService: ScheduledActionService,
  ) {}

  @Query(() => [ScheduledAction], {
    name: 'draftPendingActions',
    description: 'Get scheduled actions with DRAFT_PENDING status',
  })
  async findDraftPending(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction[]> {
    const canRead = await authContext.can('scheduledAction', 'read', []);

    if (!canRead) {
      throw new ForbiddenException(
        'Only super users can access draft pending actions',
      );
    }
    return this.scheduledActionService.findDraftPending();
  }

  @Query(() => [ScheduledAction], {
    name: 'upcomingActions',
    description: 'Get upcoming scheduled actions',
  })
  async findUpcoming(
    @Args('companyId', { type: () => ID }) companyId: string,
    @Args('workflowType', { type: () => WorkflowType })
    workflowType: WorkflowType,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction[]> {
    const canRead = await authContext.can('scheduledAction', 'read', []);

    if (!canRead) {
      throw new ForbiddenException(
        'Only super users can access upcoming actions',
      );
    }
    return this.scheduledActionService.findUpcoming(companyId, workflowType);
  }

  @Query(() => ScheduledAction, {
    name: 'scheduledAction',
    description: 'Get a scheduled action by ID',
    nullable: true,
  })
  async findById(
    @Args('id', { type: () => ID }) id: string,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction | null> {
    const canRead = await authContext.can('scheduledAction', 'read', []);

    if (!canRead) {
      throw new ForbiddenException(
        'Only super users can access scheduled actions',
      );
    }
    try {
      return await this.scheduledActionService.findById(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        return null;
      }
      throw error;
    }
  }

  @Mutation(() => ScheduledAction, { description: 'Create owed actions' })
  async createOwedActions(
    @Args('companyId', { type: () => ID }) companyId: string,
    @Args('workflowType', { type: () => WorkflowType })
    workflowType: WorkflowType,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('generationPayload', {
      type: () => GraphQLJSONObject,
      nullable: true,
    })
    generationPayload?: object,
  ): Promise<ScheduledAction> {
    const canCreate = await authContext.can('scheduledAction', 'create', []);

    if (!canCreate) {
      throw new ForbiddenException('Only super users can create owed actions');
    }
    return this.scheduledActionService.createOwed(
      companyId,
      workflowType,
      generationPayload,
    );
  }
  @Mutation(() => ScheduledAction, {
    name: 'createScheduledAction',
    description: 'Create a new scheduled action',
  })
  async create(
    @Args() createInput: CreateScheduledActionInput,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction> {
    const canCreate = await authContext.can('scheduledAction', 'create', []);

    if (!canCreate) {
      throw new ForbiddenException(
        'Only super users can create scheduled actions',
      );
    }
    return this.scheduledActionService.create(createInput);
  }

  @Mutation(() => ScheduledAction, {
    name: 'updateScheduledAction',
    description: 'Update an existing scheduled action',
  })
  async update(
    @Args('id', { type: () => ID }) id: string,
    @Args() updateInput: UpdateScheduledActionInput,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction> {
    const canUpdate = await authContext.can('scheduledAction', 'update', []);

    if (!canUpdate) {
      throw new ForbiddenException(
        'Only super users can update scheduled actions',
      );
    }
    return this.scheduledActionService.update(id, updateInput);
  }

  @Query(() => [ScheduledAction], {
    name: 'surfacedActions',
    description:
      'Get scheduled actions with SURFACED status ready for publishing',
  })
  async findSurfaced(
    @Args('scheduledToBePublishedAt', { type: () => Date })
    scheduledToBePublishedAt: Date,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction[]> {
    const canRead = await authContext.can('scheduledAction', 'read', []);

    if (!canRead) {
      throw new ForbiddenException(
        'Only super users can access scheduled actions',
      );
    }
    return this.scheduledActionService.findSurfaced(scheduledToBePublishedAt);
  }

  @Query(() => PaginatedScheduledAction, {
    name: 'findScheduledActionsPaginated',
    description: 'Get paginated scheduled actions',
  })
  async findPaginated(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('page', { type: () => Int, defaultValue: 1 }) page: number,
    @Args('limit', { type: () => Int, defaultValue: 50 }) limit: number,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
    @Args('workflowType', { type: () => WorkflowType, nullable: true })
    workflowType?: WorkflowType | null,
    @Args('status', { type: () => Status, nullable: true })
    status?: Status | null,
  ): Promise<PaginatedScheduledAction> {
    const canRead = await authContext.can('scheduledAction', 'read', []);

    if (!canRead) {
      throw new ForbiddenException(
        'Only super users can access scheduled actions',
      );
    }

    const paginateQuery: PaginateQuery = {
      path: '',
      page,
      limit,
    };

    return await this.scheduledActionService.findPaginated(
      paginateQuery,
      companyId,
      workflowType,
      status,
    );
  }

  @Mutation(() => StoreSEODraftResponse, {
    name: 'storeSEODraft',
    description:
      'Store a SEO draft related entities in the DB for a given scheduled action',
  })
  async storeSEODraft(
    @Args() input: SaveSEODraftArgs,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<StoreSEODraftResponse> {
    const canCreate = await authContext.can('scheduledAction', 'create', []);

    if (!canCreate) {
      throw new ForbiddenException(
        'Only super users can create scheduled actions',
      );
    }
    return this.scheduledActionService.storeSEODraft(input);
  }

  @Mutation(() => ScheduledAction, {
    name: 'unsurfaceScheduledAction',
    description: 'Unsurface a scheduled action that was mistakenly surfaced',
  })
  async unsurfaceScheduledAction(
    @Args() input: UnsurfaceScheduledActionInput,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<ScheduledAction> {
    const canUpdate = await authContext.can('scheduledAction', 'update', []);

    if (!canUpdate) {
      throw new ForbiddenException(
        'Only super users can unsurface scheduled actions',
      );
    }

    return this.scheduledActionService.unsurfaceScheduledAction(
      input.id,
      input.overrideDate,
    );
  }

  @ResolveField(() => Company, { nullable: true })
  company(@Parent() scheduledAction: ScheduledAction): Company | null {
    if (!scheduledAction.companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: scheduledAction.companyId } as Company;
  }
}
