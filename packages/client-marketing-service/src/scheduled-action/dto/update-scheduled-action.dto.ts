import { Field, ArgsType, OmitType, PartialType } from '@nestjs/graphql';
import {
  IsObject,
  IsOptional,
  IsString,
  IsDate,
  ValidateIf,
} from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';

import { CreateScheduledActionInput } from './create-scheduled-action.dto';

@ArgsType()
export class UpdateScheduledActionInput extends PartialType(
  OmitType(CreateScheduledActionInput, ['companyId', 'workflowType'] as const),
) {
  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsObject()
  @IsOptional()
  failureReason?: object;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  @ValidateIf((_, v) => v !== null && v !== undefined)
  executionName?: string | null;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  @ValidateIf((_, v) => v !== null && v !== undefined)
  executionArn?: string | null;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  scheduledToBeSurfacedAt?: Date;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  scheduledToBePublishedAt?: Date;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  surfacedAt?: Date;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  publishedAt?: Date;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsObject()
  @IsOptional()
  generationPayload?: object;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsObject()
  @IsOptional()
  contentPayload?: object;
}
