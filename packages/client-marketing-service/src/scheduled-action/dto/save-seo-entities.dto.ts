import { ArgsType, Field, ID, InputType, ObjectType } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty } from 'class-validator';

import { Group } from '../../group/entities/group.entity';
import { Keyword } from '../../keyword/entities/keyword.entity';
import { PageKeyword } from '../../page-keyword/entities/page-keyword.entity';
import { Recommendation } from '../../recommendation/entities/recommendation.entity';
import { Scrape } from '../../scrape/entities/scrape.entity';
import {
  ScrapedPage,
  ScrapedPageType,
} from '../../scraped-page/entities/scraped-page.entity';

@InputType()
export class MainHeadingInput {
  @Field()
  tag: string;

  @Field()
  index: number;

  @Field()
  content: string;

  @Field({ nullable: true })
  page_id?: string;

  @Field({ nullable: true })
  element_id?: string;

  @Field({ nullable: true })
  section_id?: string;

  @Field({ nullable: true })
  website_id?: string;
}

@InputType()
export class SeoRecommendationInput {
  @Field()
  currentValue: string;

  @Field()
  recommendationValue: string;

  @Field()
  reasoning: string;
}

@InputType()
export class ScrapedPageInput {
  @Field()
  firecrawlId: string;

  @Field()
  url: string;

  @Field({ nullable: true })
  metaTitle?: string;

  @Field({ nullable: true })
  metaDescription?: string;

  @Field(() => MainHeadingInput)
  mainHeading: MainHeadingInput;

  @Field(() => ScrapedPageType)
  @IsEnum(ScrapedPageType)
  type: ScrapedPageType;

  @Field()
  markdown: string;

  @Field()
  mediaId: string;

  @Field()
  companyId: string;
}

@InputType()
export class RecommendationsInput {
  @Field()
  metaTitle: SeoRecommendationInput;

  @Field()
  metaDescription: SeoRecommendationInput;

  @Field({ nullable: true })
  mainHeading?: SeoRecommendationInput;
}

@ArgsType()
export class SaveSEODraftArgs {
  @Field(() => ScrapedPageInput, { nullable: false })
  scraperResult: ScrapedPageInput;

  @Field({ nullable: false })
  keywords: string;

  @Field(() => RecommendationsInput, { nullable: false })
  recommendations: RecommendationsInput;

  @Field(() => ID)
  @IsNotEmpty()
  companyId: string;

  @Field(() => ID)
  @IsNotEmpty()
  actionId: string;
}

@ObjectType()
export class StoreSEODraftResponse {
  @Field(() => Keyword, { nullable: true })
  savedKeyword: Keyword | null;

  @Field(() => [Recommendation], { nullable: true })
  savedRecommendations: Recommendation[] | null;

  @Field(() => Scrape, { nullable: true })
  savedScrape: Scrape | null;

  @Field(() => ScrapedPage, { nullable: true })
  savedScrapedPage: ScrapedPage | null;

  @Field(() => PageKeyword, { nullable: true })
  savedPageKeyword: PageKeyword | null;

  @Field(() => Group, { nullable: true })
  savedGroup: Group | null;
}
