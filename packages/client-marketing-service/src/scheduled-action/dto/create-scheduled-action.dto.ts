import { Field, ArgsType } from '@nestjs/graphql';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';

import { Status, WorkflowType } from '../entities/scheduled-action.entity';

@ArgsType()
export class CreateScheduledActionInput {
  @Field()
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @Field(() => WorkflowType)
  @IsEnum(WorkflowType)
  @IsNotEmpty()
  workflowType: WorkflowType;

  @Field(() => Status, { nullable: true })
  @IsEnum(Status)
  @IsOptional()
  status?: Status;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  scheduledToBePublishedAt?: Date;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsObject()
  @IsOptional()
  contentPayload?: object;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @IsObject()
  @IsOptional()
  generationPayload?: object;
}
