import { Logger, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { IsNull } from 'typeorm';

import { CreateScheduledActionInput } from './dto/create-scheduled-action.dto';
import { UpdateScheduledActionInput } from './dto/update-scheduled-action.dto';
import {
  ScheduledAction,
  Status,
  WorkflowType,
} from './entities/scheduled-action.entity';
import { ScheduledActionService } from './scheduled-action.service';
import { WebsiteService } from '../common/services/website/website.service';
import { Group } from '../group/entities/group.entity';
import { Recommendation } from '../recommendation/entities/recommendation.entity';

describe('ScheduledActionService', () => {
  let service: ScheduledActionService;

  // Base time for consistent testing
  const baseTime = new Date('2024-01-15T10:00:00Z');
  const oneDayLater = new Date('2024-01-16T10:00:00Z');

  const mockScheduledAction: ScheduledAction = {
    id: 'test-id',
    companyId: 'company-id',
    workflowType: WorkflowType.SEO,
    status: Status.DRAFT_PENDING,
    createdAt: baseTime,
    updatedAt: baseTime,
    surfacedAt: null,
    scheduledToBePublishedAt: oneDayLater,
    publishedAt: null,
    contentPayload: { type: 'test', data: 'content' },
    generationPayload: { model: 'test-model', temperature: 0.7 },
    failureReason: {},
    scheduledToBeSurfacedAt: null,
    lockedAt: null,
    executionName: null,
    executionArn: null,
  };

  const mockCreateInput: CreateScheduledActionInput = {
    companyId: 'company-id',
    workflowType: WorkflowType.SEO,
    scheduledToBePublishedAt: oneDayLater,
    contentPayload: { type: 'test', data: 'content' },
    generationPayload: { model: 'test-model', temperature: 0.7 },
  };

  const mockUpdateInput: UpdateScheduledActionInput = {
    status: Status.HUMAN_QA_PENDING,
    contentPayload: { type: 'updated', data: 'updated content' },
    publishedAt: baseTime,
  };

  const updatedScheduledAction = {
    ...mockScheduledAction,
    status: Status.HUMAN_QA_PENDING,
    contentPayload: { type: 'updated', data: 'updated content' },
    publishedAt: baseTime,
  };

  const mockWebsiteService = {
    updateWebsite: jest.fn().mockResolvedValue({}),
    getPage: jest.fn().mockResolvedValue({}),
  };

  const managerScheduledActionRepository = {
    find: jest.fn().mockResolvedValue([mockScheduledAction]),
    findOne: jest.fn().mockResolvedValue(mockScheduledAction),
    create: jest.fn().mockReturnValue(mockScheduledAction),
    save: jest.fn().mockResolvedValue(mockScheduledAction),
    update: jest.fn().mockResolvedValue({ affected: 1 }),
    upsert: jest.fn().mockResolvedValue({ affected: 1 }),
    remove: jest.fn().mockResolvedValue([]),
    softDelete: jest.fn().mockResolvedValue({ affected: 1 }),
    createQueryBuilder: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getRawOne: jest.fn().mockResolvedValue({ maxDate: null }),
    }),
  };

  const mockScheduledActionRepository = {
    find: jest.fn().mockResolvedValue([mockScheduledAction]),
    findOne: jest.fn().mockResolvedValue(mockScheduledAction),
    create: jest.fn().mockReturnValue(mockScheduledAction),
    save: jest.fn().mockResolvedValue(mockScheduledAction),
    update: jest.fn().mockResolvedValue({ affected: 1 }),
    manager: {
      transaction: jest.fn().mockImplementation(async callback => {
        return await callback(managerScheduledActionRepository);
      }),
    },
    createQueryBuilder: jest.fn().mockReturnValue({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
    }),
  };
  beforeEach(async () => {
    jest.clearAllMocks();

    // Set up fake timers with base time
    jest.useFakeTimers();
    jest.setSystemTime(baseTime);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScheduledActionService,
        {
          provide: getRepositoryToken(ScheduledAction),
          useValue: mockScheduledActionRepository,
        },
        {
          provide: WebsiteService,
          useValue: mockWebsiteService,
        },
      ],
    }).compile();

    service = module.get<ScheduledActionService>(ScheduledActionService);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findDraftPending', () => {
    it('should return an array of DRAFT_PENDING scheduled actions', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const result = await service.findDraftPending();

      expect(result).toEqual([mockScheduledAction]);
      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Fetching DRAFT_PENDING scheduled actions',
      );
    });

    it('should return actions ordered by creation date (oldest first)', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const olderAction = {
        ...mockScheduledAction,
        id: 'older-id',
        createdAt: new Date('2024-01-01T10:00:00Z'),
      };
      const newerAction = {
        ...mockScheduledAction,
        id: 'newer-id',
        createdAt: new Date('2024-01-02T10:00:00Z'),
      };

      managerScheduledActionRepository.find.mockResolvedValueOnce([
        olderAction,
        newerAction,
      ]);

      const result = await service.findDraftPending();

      expect(result).toEqual([olderAction, newerAction]);
      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );
    });

    it('should return multiple DRAFT_PENDING actions from different companies', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const company1Action = {
        ...mockScheduledAction,
        id: 'company1-action',
        companyId: 'company-1',
      };
      const company2Action = {
        ...mockScheduledAction,
        id: 'company2-action',
        companyId: 'company-2',
      };

      managerScheduledActionRepository.find.mockResolvedValueOnce([
        company1Action,
        company2Action,
      ]);

      const result = await service.findDraftPending();

      expect(result).toEqual([company1Action, company2Action]);
      expect(result).toHaveLength(2);
    });

    it('should return multiple DRAFT_PENDING actions from different workflow types', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const seoAction = {
        ...mockScheduledAction,
        id: 'seo-action',
        workflowType: WorkflowType.SEO,
      };
      const blogAction = {
        ...mockScheduledAction,
        id: 'blog-action',
        workflowType: WorkflowType.BLOG,
      };

      managerScheduledActionRepository.find.mockResolvedValueOnce([
        seoAction,
        blogAction,
      ]);

      const result = await service.findDraftPending();

      expect(result).toEqual([seoAction, blogAction]);
      expect(result).toHaveLength(2);
    });

    it('should only return DRAFT_PENDING status actions', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      await service.findDraftPending();

      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );

      // Verify it doesn't include other statuses
      expect(managerScheduledActionRepository.find).not.toHaveBeenCalledWith(
        ScheduledAction,
        expect.objectContaining({
          where: expect.objectContaining({
            status: Status.HUMAN_QA_PENDING,
          }),
        }),
      );
    });

    it('should handle large numbers of DRAFT_PENDING actions', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Create 100 mock actions
      const manyActions = Array.from({ length: 100 }, (_, index) => ({
        ...mockScheduledAction,
        id: `action-${index}`,
        createdAt: new Date(baseTime.getTime() + index * 1000), // Spread over time
      }));

      managerScheduledActionRepository.find.mockResolvedValueOnce(manyActions);

      const result = await service.findDraftPending();

      expect(result).toHaveLength(100);
      expect(result).toEqual(manyActions);
    });

    it('should return an empty array when no DRAFT_PENDING actions exist', async () => {
      managerScheduledActionRepository.find.mockResolvedValueOnce([]);

      const result = await service.findDraftPending();

      expect(result).toEqual([]);
      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );
    });

    it('should handle repository timeout errors', async () => {
      const timeoutError = new Error('Database timeout');
      timeoutError.name = 'TimeoutError';
      managerScheduledActionRepository.find.mockRejectedValueOnce(timeoutError);

      await expect(service.findDraftPending()).rejects.toThrow(
        'Database timeout',
      );
    });

    it('should handle repository connection errors', async () => {
      const connectionError = new Error('Connection lost');
      connectionError.name = 'ConnectionError';
      managerScheduledActionRepository.find.mockRejectedValueOnce(
        connectionError,
      );

      await expect(service.findDraftPending()).rejects.toThrow(
        'Connection lost',
      );
    });

    it('should handle errors from the repository', async () => {
      const error = new Error('Database error');
      managerScheduledActionRepository.find.mockRejectedValueOnce(error);

      await expect(service.findDraftPending()).rejects.toThrow(error);
    });

    it('should find and return actions without locking', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      await service.findDraftPending();

      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );
      // Should not call update (no locking)
      expect(managerScheduledActionRepository.update).not.toHaveBeenCalled();
      // Should not call find again (no requerying)
      expect(managerScheduledActionRepository.find).toHaveBeenCalledTimes(1);
    });

    it('should filter by correct lockedAt values', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      await service.findDraftPending();

      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: {
            status: Status.DRAFT_PENDING,
            lockedAt: IsNull(),
          },
          order: {
            createdAt: 'ASC',
          },
          lock: { mode: 'pessimistic_write' },
        },
      );
    });

    it('should handle empty results', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      managerScheduledActionRepository.find.mockResolvedValueOnce([]);

      const result = await service.findDraftPending();

      expect(result).toEqual([]);
      expect(managerScheduledActionRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('findById', () => {
    it('should return a scheduled action by ID', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const result = await service.findById('test-id');

      expect(result).toEqual(mockScheduledAction);
      expect(mockScheduledActionRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        relations: ['groupScheduledActions'],
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Fetching scheduled action with ID: test-id',
      );
    });

    it('should throw NotFoundException when scheduled action is not found', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      jest.spyOn(Logger.prototype, 'error').mockImplementation();
      mockScheduledActionRepository.findOne.mockResolvedValueOnce(null);

      await expect(service.findById('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
      expect(Logger.prototype.error).toHaveBeenCalledWith(
        'Scheduled action with ID non-existent-id not found',
      );
    });

    it('should handle errors from the repository', async () => {
      const error = new Error('Database error');
      mockScheduledActionRepository.findOne.mockRejectedValueOnce(error);

      await expect(service.findById('test-id')).rejects.toThrow(error);
    });
  });

  describe('create', () => {
    it('should create a new scheduled action', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const result = await service.create(mockCreateInput);

      expect(result).toEqual(mockScheduledAction);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        ...mockCreateInput,
        status: Status.DRAFT_PENDING,
        contentPayload: mockCreateInput.contentPayload,
        generationPayload: mockCreateInput.generationPayload,
        failureReason: {},
      });
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        mockScheduledAction,
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        `Creating new scheduled action for company ${mockCreateInput.companyId}`,
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        `Created scheduled action with ID: ${mockScheduledAction.id}`,
      );
    });

    it('should use default values when optional fields are not provided', async () => {
      // Create a minimal input with only required fields
      const minimalInput: CreateScheduledActionInput = {
        companyId: 'company-id',
        workflowType: WorkflowType.SEO,
      };

      await service.create(minimalInput);

      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        ...minimalInput,
        status: Status.DRAFT_PENDING,
        contentPayload: {},
        generationPayload: {},
        failureReason: {},
      });
    });

    it('should handle errors from the repository', async () => {
      const error = new Error('Database error');
      mockScheduledActionRepository.save.mockRejectedValueOnce(error);

      await expect(service.create(mockCreateInput)).rejects.toThrow(error);
    });
  });

  describe('findUpcoming', () => {
    it('should return upcoming scheduled actions for a company and workflow', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';
      const workflowType = WorkflowType.SEO;

      const result = await service.findUpcoming(companyId, workflowType);

      expect(result).toEqual([mockScheduledAction]);
      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          workflowType,
          status: Status.SURFACING_PENDING,
        },
      });
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Fetching upcoming scheduled actions',
      );
    });

    it('should only filter by SURFACING_PENDING status', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';
      const workflowType = WorkflowType.SEO;

      await service.findUpcoming(companyId, workflowType);

      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          workflowType,
          status: Status.SURFACING_PENDING,
        },
      });
    });

    it('should filter by correct status values', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';
      const workflowType = WorkflowType.BLOG;

      await service.findUpcoming(companyId, workflowType);

      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          workflowType,
          status: Status.SURFACING_PENDING,
        },
      });
    });

    it('should handle different workflow types', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';

      // Test SEO workflow
      await service.findUpcoming(companyId, WorkflowType.SEO);
      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
        },
      });

      // Test BLOG workflow
      await service.findUpcoming(companyId, WorkflowType.BLOG);
      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          workflowType: WorkflowType.BLOG,
          status: Status.SURFACING_PENDING,
        },
      });
    });

    it('should handle empty results', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      mockScheduledActionRepository.find.mockResolvedValueOnce([]);

      const result = await service.findUpcoming('company-id', WorkflowType.SEO);

      expect(result).toEqual([]);
    });

    it('should handle different company IDs', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const differentCompanyId = 'different-company-id';

      await service.findUpcoming(differentCompanyId, WorkflowType.SEO);

      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId: differentCompanyId,
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
        },
      });
    });

    it('should handle boundary date conditions', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Test edge case where current time is end of month
      const mockEndOfMonth = new Date('2024-01-31T23:59:59Z');

      jest.setSystemTime(mockEndOfMonth);

      await service.findUpcoming('company-id', WorkflowType.SEO);

      expect(mockScheduledActionRepository.find).toHaveBeenCalledWith({
        where: {
          companyId: 'company-id',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
        },
      });
    });

    it('should handle errors from the repository', async () => {
      const error = new Error('Database error');
      mockScheduledActionRepository.find.mockRejectedValueOnce(error);

      await expect(
        service.findUpcoming('company-id', WorkflowType.SEO),
      ).rejects.toThrow(error);
    });
  });

  describe('createOwed', () => {
    it('should create a new owed scheduled action', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';
      const workflowType = WorkflowType.SEO;

      const result = await service.createOwed(companyId, workflowType);

      expect(result).toEqual(mockScheduledAction);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId,
        workflowType,
        status: Status.DRAFT_PENDING,
      });
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        mockScheduledAction,
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Creating upcoming scheduled action',
      );
    });

    it('should create owed action with DRAFT_PENDING status by default', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';
      const workflowType = WorkflowType.BLOG;

      await service.createOwed(companyId, workflowType);

      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId,
        workflowType,
        status: Status.DRAFT_PENDING,
      });
    });

    it('should handle different workflow types for owed actions', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const companyId = 'company-id';

      // Test SEO workflow
      await service.createOwed(companyId, WorkflowType.SEO);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId,
        workflowType: WorkflowType.SEO,
        status: Status.DRAFT_PENDING,
      });

      // Test BLOG workflow
      await service.createOwed(companyId, WorkflowType.BLOG);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId,
        workflowType: WorkflowType.BLOG,
        status: Status.DRAFT_PENDING,
      });
    });

    it('should handle different company IDs', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const workflowType = WorkflowType.SEO;

      // Test different company IDs
      const companyId1 = 'company-id-1';
      const companyId2 = 'company-id-2';

      await service.createOwed(companyId1, workflowType);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId: companyId1,
        workflowType,
        status: Status.DRAFT_PENDING,
      });

      await service.createOwed(companyId2, workflowType);
      expect(mockScheduledActionRepository.create).toHaveBeenCalledWith({
        companyId: companyId2,
        workflowType,
        status: Status.DRAFT_PENDING,
      });
    });

    it('should log the correct message', async () => {
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      await service.createOwed('company-id', WorkflowType.SEO);

      expect(logSpy).toHaveBeenCalledWith('Creating upcoming scheduled action');
    });

    it('should handle repository create errors', async () => {
      const error = new Error('Create failed');
      mockScheduledActionRepository.create.mockImplementationOnce(() => {
        throw error;
      });

      await expect(
        service.createOwed('company-id', WorkflowType.SEO),
      ).rejects.toThrow(error);
    });

    it('should handle repository save errors', async () => {
      const error = new Error('Database error');
      mockScheduledActionRepository.save.mockRejectedValueOnce(error);

      await expect(
        service.createOwed('company-id', WorkflowType.SEO),
      ).rejects.toThrow(error);
    });

    it('should properly sequence repository operations', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      await service.createOwed('company-id', WorkflowType.SEO);

      // Ensure create is called before save
      expect(mockScheduledActionRepository.create).toHaveBeenCalled();
      expect(mockScheduledActionRepository.save).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    beforeEach(() => {
      // Mock findOne to return the existing action for update tests
      mockScheduledActionRepository.findOne.mockResolvedValue(
        mockScheduledAction,
      );
      // Mock save to return the updated action
      mockScheduledActionRepository.save.mockResolvedValue(
        updatedScheduledAction,
      );
    });

    it('should update a scheduled action', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Mock the save method to return the updated action
      mockScheduledActionRepository.save.mockResolvedValueOnce(
        updatedScheduledAction,
      );

      const result = await service.update('test-id', mockUpdateInput);

      expect(result).toEqual(updatedScheduledAction);
      expect(mockScheduledActionRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
      expect(mockScheduledActionRepository.save).toHaveBeenCalled();
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        `Updating scheduled action with ID: test-id`,
      );
    });

    it('should only update fields that are provided in the input', async () => {
      // Create an input with only status update
      const statusOnlyInput: UpdateScheduledActionInput = {
        status: Status.HUMAN_QA_PENDING,
      };

      await service.update('test-id', statusOnlyInput);

      // The existing action should be updated with only the status field
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: Status.HUMAN_QA_PENDING,
        }),
      );
    });

    it('should update all provided fields correctly', async () => {
      const fullUpdateInput: UpdateScheduledActionInput = {
        status: Status.HUMAN_QA_PENDING,
        scheduledToBePublishedAt: new Date('2024-01-17T10:00:00Z'), // day after tomorrow
        contentPayload: { updated: 'content' },
        generationPayload: { updated: 'generation' },
        failureReason: { error: 'test error' },
      };

      await service.update('test-id', fullUpdateInput);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: Status.HUMAN_QA_PENDING,
          scheduledToBePublishedAt: fullUpdateInput.scheduledToBePublishedAt,
          contentPayload: { updated: 'content' },
          generationPayload: { updated: 'generation' },
          failureReason: { error: 'test error' },
        }),
      );
    });

    it('should throw NotFoundException when scheduled action is not found', async () => {
      // Spy on the logger
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      jest.spyOn(Logger.prototype, 'error').mockImplementation();
      mockScheduledActionRepository.findOne.mockResolvedValueOnce(null);

      await expect(
        service.update('non-existent-id', {
          status: Status.HUMAN_QA_PENDING,
        }),
      ).rejects.toThrow(NotFoundException);
      expect(Logger.prototype.error).toHaveBeenCalledWith(
        'Scheduled action with ID non-existent-id not found',
      );
    });

    it('should handle errors from the repository', async () => {
      const error = new Error('Database error');
      mockScheduledActionRepository.findOne.mockRejectedValueOnce(error);

      await expect(service.update('test-id', mockUpdateInput)).rejects.toThrow(
        error,
      );
    });

    it('should handle status workflow transitions', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Test progression through workflow states
      const workflowStates = [
        Status.DRAFT_PENDING,
        Status.HUMAN_QA_PENDING,
        Status.SURFACING_PENDING,
        Status.SURFACED,
        Status.PUBLISHED,
      ];

      for (const status of workflowStates) {
        const statusUpdate: UpdateScheduledActionInput = { status };
        await service.update('test-id', statusUpdate);

        const expectedLockedAt =
          status === mockScheduledAction.status ? null : undefined;

        expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
          expect.objectContaining({ status, lockedAt: expectedLockedAt }),
        );
      }
    });

    it('should handle failure and invalidated status updates', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Test FAILED status
      const fixedTimestamp = new Date('2024-08-12T09:00:00Z');
      const failedUpdate: UpdateScheduledActionInput = {
        status: Status.FAILED,
        failureReason: {
          error: 'Processing failed',
          timestamp: fixedTimestamp,
        },
      };

      await service.update('test-id', failedUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: Status.FAILED,
          failureReason: {
            error: 'Processing failed',
            timestamp: fixedTimestamp,
          },
          lockedAt: null,
        }),
      );

      // Test INVALIDATED status
      const invalidatedUpdate: UpdateScheduledActionInput = {
        status: Status.INVALIDATED,
      };

      await service.update('test-id', invalidatedUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: Status.INVALIDATED,
          lockedAt: null,
        }),
      );
    });

    it('should handle complex payload updates', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const complexUpdate: UpdateScheduledActionInput = {
        contentPayload: {
          seoData: {
            title: 'Updated SEO Title',
            description: 'Updated meta description',
            keywords: ['seo', 'optimization', 'content'],
          },
          blogData: {
            title: 'Blog Post Title',
            content: 'Lorem ipsum dolor sit amet...',
            tags: ['blog', 'content', 'marketing'],
          },
        },
        generationPayload: {
          model: 'gpt-4o',
          temperature: 0.8,
          maxTokens: 2000,
          prompt: 'Generate engaging content about...',
          experimentalFeatures: {
            useAdvancedPrompting: true,
            enableFactChecking: false,
          },
        },
      };

      await service.update('test-id', complexUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          contentPayload: complexUpdate.contentPayload,
          generationPayload: complexUpdate.generationPayload,
        }),
      );
    });

    it('should handle date field updates with validation', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const futureDate = new Date('2024-01-22T10:00:00Z'); // 7 days from now
      const dateUpdate: UpdateScheduledActionInput = {
        scheduledToBePublishedAt: futureDate,
      };

      await service.update('test-id', dateUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          scheduledToBePublishedAt: futureDate,
        }),
      );
    });

    it('should preserve existing fields when only updating specific fields', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Update only the status
      const statusOnlyUpdate: UpdateScheduledActionInput = {
        status: Status.SURFACED,
      };

      await service.update('test-id', statusOnlyUpdate);

      // Verify that the save was called with the original action plus the status update
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: mockScheduledAction.id,
          companyId: mockScheduledAction.companyId,
          workflowType: mockScheduledAction.workflowType,
          status: Status.SURFACED, // Updated field
          lockedAt: null, // Updated field
          contentPayload: mockScheduledAction.contentPayload, // Preserved
          generationPayload: mockScheduledAction.generationPayload, // Preserved
        }),
      );
    });

    it('should handle the execution name and arn', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Update only the execution name and arn
      const executionUpdate: UpdateScheduledActionInput = {
        executionName: 'test-execution-name',
        executionArn: 'test-execution-arn',
      };

      await service.update('test-id', executionUpdate);

      // Verify that the save was called with the original action plus the status update
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          executionName: 'test-execution-name',
          executionArn: 'test-execution-arn',
        }),
      );
    });

    it('should handle null values for execution name and arn', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Update only the execution name and arn
      const executionUpdate: UpdateScheduledActionInput = {
        executionName: null,
        executionArn: null,
      };

      await service.update('test-id', executionUpdate);

      // Verify that the save was called with the original action plus the status update
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          executionName: null,
          executionArn: null,
        }),
      );
    });

    it('should handle concurrent update scenarios', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Simulate multiple updates to the same action
      const update1: UpdateScheduledActionInput = {
        status: Status.HUMAN_QA_PENDING,
      };
      const update2: UpdateScheduledActionInput = {
        status: Status.SURFACING_PENDING,
      };

      // Both updates should succeed (in a real scenario, this would test race conditions)
      await Promise.all([
        service.update('test-id', update1),
        service.update('test-id', update2),
      ]);

      // findOne is called once to confirm we have the record and once to get the updated record
      expect(mockScheduledActionRepository.findOne).toHaveBeenCalledTimes(4);
      expect(mockScheduledActionRepository.save).toHaveBeenCalledTimes(2);
    });

    it('should handle empty update objects', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const emptyUpdate: UpdateScheduledActionInput = {};

      await service.update('test-id', emptyUpdate);

      // Should still call save with the original action
      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: mockScheduledAction.id,
        }),
      );
    });
  });

  // Additional test suite for workflow validation scenarios
  describe('workflow state validation', () => {
    beforeEach(() => {
      mockScheduledActionRepository.findOne.mockResolvedValue(
        mockScheduledAction,
      );
      mockScheduledActionRepository.save.mockResolvedValue(mockScheduledAction);
    });

    it('should handle timestamp consistency validation', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Create an action that has been surfaced but not published
      const surfacedAction = {
        ...mockScheduledAction,
        status: Status.SURFACED,
        surfacedAt: baseTime,
        publishedAt: null,
      };

      mockScheduledActionRepository.findOne.mockResolvedValueOnce(
        surfacedAction,
      );

      // Update to published status
      const publishUpdate: UpdateScheduledActionInput = {
        status: Status.PUBLISHED,
      };

      await service.update('test-id', publishUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: Status.PUBLISHED,
        }),
      );
    });

    it('should handle workflow type consistency', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Test that updates maintain workflow type
      const contentUpdate: UpdateScheduledActionInput = {
        contentPayload: { workflowSpecificData: 'SEO content' },
      };

      await service.update('test-id', contentUpdate);

      expect(mockScheduledActionRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          workflowType: WorkflowType.SEO, // Should remain unchanged
          contentPayload: contentUpdate.contentPayload,
        }),
      );
    });
  });

  describe('storeSEODraft input validation', () => {
    it('should throw BadRequestException if any required field is missing', async () => {
      const validInput = {
        companyId: 'company-id',
        actionId: 'action-id',
        keywords: 'seo',
        recommendations: {
          metaTitle: {
            currentValue: 'a',
            recommendationValue: 'b',
            reasoning: 'r',
          },
          metaDescription: {
            currentValue: 'a',
            recommendationValue: 'b',
            reasoning: 'r',
          },
          mainHeading: {
            currentValue: 'a',
            recommendationValue: 'b',
            reasoning: 'r',
          },
        },
        scraperResult: {
          firecrawlId: 'fcid',
          url: 'https://example.com',
          mainHeading: { selector: 'h1', content: 'Title' },
          type: 'HOMEPAGE',
          markdown: '# Title',
          mediaId: 'mid',
          companyId: 'company-id',
        },
      };
      const requiredFields = [
        'companyId',
        'actionId',
        'keywords',
        'recommendations',
        'scraperResult',
      ];
      for (const field of requiredFields) {
        const input = { ...validInput };
        delete input[field];
        await expect(service.storeSEODraft(input as any)).rejects.toThrow(
          'Missing required input fields',
        );
      }
      // Test nested required fields in scraperResult
      const nestedFields = ['url', 'type'];
      for (const field of nestedFields) {
        const input = {
          ...validInput,
          scraperResult: { ...validInput.scraperResult },
        };
        delete input.scraperResult[field];
        await expect(service.storeSEODraft(input as any)).rejects.toThrow(
          'Missing required input fields',
        );
      }
    });

    it('should handle undefined mainHeading in recommendations', async () => {
      const inputWithoutMainHeading = {
        companyId: 'company-id',
        actionId: 'action-id',
        keywords: 'seo',
        recommendations: {
          metaTitle: {
            currentValue: 'Current Title',
            recommendationValue: 'Recommended Title',
            reasoning: 'Title reasoning',
          },
          metaDescription: {
            currentValue: 'Current Description',
            recommendationValue: 'Recommended Description',
            reasoning: 'Description reasoning',
          },
          mainHeading: undefined,
        },
        scraperResult: {
          firecrawlId: 'fcid',
          url: 'https://example.com',
          mainHeading: { selector: 'h1', content: 'Title' },
          type: 'HOMEPAGE',
          markdown: '# Title',
          mediaId: 'mid',
          companyId: 'company-id',
        },
      };

      await expect(
        service.storeSEODraft(inputWithoutMainHeading as any),
      ).resolves.toBeDefined();
    });
  });

  describe('unsurfaceScheduledAction', () => {
    const mockGroup = {
      id: 'group-1',
      companyId: 'company-1',
      title: 'Test Group',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockRecommendation = {
      id: 'rec-1',
      groupId: 'group-1',
      type: 'META_TITLE',
      currentValue: 'Old Title',
      recommendationValue: 'New Title',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockGroupScheduledAction = {
      id: 'gsa-1',
      groupId: 'group-1',
      scheduledActionId: 'test-id',
      group: mockGroup,
    };

    const mockSurfacedAction = {
      ...mockScheduledAction,
      status: Status.SURFACED,
      surfacedAt: new Date(),
      groupScheduledActions: [mockGroupScheduledAction],
    };

    beforeEach(() => {
      managerScheduledActionRepository.findOne
        .mockResolvedValueOnce(mockSurfacedAction)
        .mockResolvedValue({
          ...mockSurfacedAction,
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: new Date('2024-01-27T10:00:00Z'),
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        });
      managerScheduledActionRepository.find.mockResolvedValue([
        mockRecommendation,
      ]);
      managerScheduledActionRepository.remove.mockResolvedValue([
        mockRecommendation,
      ]);
      managerScheduledActionRepository.softDelete.mockResolvedValue({
        affected: 1,
      });
      managerScheduledActionRepository.update.mockResolvedValue({
        affected: 1,
      });
      managerScheduledActionRepository.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          maxDate: new Date('2024-01-20T10:00:00Z'),
        }),
      });
    });

    it('should unsurface a scheduled action successfully', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();

      const result = await service.unsurfaceScheduledAction('test-id');

      expect(result).toEqual({
        ...mockSurfacedAction,
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: new Date('2024-01-27T10:00:00Z'),
        surfacedAt: null,
        publishedAt: null,
        scheduledToBePublishedAt: null,
      });
      expect(managerScheduledActionRepository.findOne).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: { id: 'test-id' },
          relations: ['groupScheduledActions', 'groupScheduledActions.group'],
        },
      );
      expect(managerScheduledActionRepository.find).toHaveBeenCalledWith(
        Recommendation,
        {
          where: { groupId: 'group-1' },
        },
      );
      expect(managerScheduledActionRepository.remove).toHaveBeenCalledWith([
        mockRecommendation,
      ]);
      expect(managerScheduledActionRepository.softDelete).toHaveBeenCalledWith(
        Group,
        { id: 'group-1' },
      );
      expect(managerScheduledActionRepository.update).toHaveBeenCalledWith(
        ScheduledAction,
        'test-id',
        {
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: expect.any(Date),
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        },
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Unsurfacing scheduled action with ID: test-id',
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Successfully unsurfaced scheduled action test-id',
      );
    });

    it('should use override date when provided', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const overrideDate = new Date('2024-02-01T10:00:00Z');

      await service.unsurfaceScheduledAction('test-id', overrideDate);

      expect(managerScheduledActionRepository.update).toHaveBeenCalledWith(
        ScheduledAction,
        'test-id',
        {
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: overrideDate,
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        },
      );
    });

    it('should calculate new date as +7 days from max company date', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const maxDate = new Date('2024-01-20T10:00:00Z');
      const expectedDate = new Date('2024-01-27T10:00:00Z');

      managerScheduledActionRepository.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxDate }),
      });

      await service.unsurfaceScheduledAction('test-id');

      expect(managerScheduledActionRepository.update).toHaveBeenCalledWith(
        ScheduledAction,
        'test-id',
        {
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: expectedDate,
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        },
      );
    });

    it('should use current date +7 days when no max date found', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const currentDate = new Date('2024-01-15T10:00:00Z');
      const expectedDate = new Date('2024-01-22T10:00:00Z');
      jest.setSystemTime(currentDate);

      managerScheduledActionRepository.createQueryBuilder.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxDate: null }),
      });

      await service.unsurfaceScheduledAction('test-id');

      expect(managerScheduledActionRepository.update).toHaveBeenCalledWith(
        ScheduledAction,
        'test-id',
        {
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: expectedDate,
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        },
      );
    });

    it('should throw NotFoundException when scheduled action not found', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      managerScheduledActionRepository.findOne.mockResolvedValueOnce(null);

      await expect(
        service.unsurfaceScheduledAction('non-existent-id'),
      ).rejects.toThrow(NotFoundException);
      expect(managerScheduledActionRepository.findOne).toHaveBeenCalledWith(
        ScheduledAction,
        {
          where: { id: 'non-existent-id' },
          relations: ['groupScheduledActions', 'groupScheduledActions.group'],
        },
      );
    });

    it('should throw BadRequestException when action has DRAFT_PENDING status', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const draftPendingAction = {
        ...mockScheduledAction,
        status: Status.DRAFT_PENDING,
      };
      // Reset the mock from beforeEach and set a new one
      managerScheduledActionRepository.findOne.mockReset();
      managerScheduledActionRepository.findOne.mockResolvedValueOnce(
        draftPendingAction,
      );

      await expect(service.unsurfaceScheduledAction('test-id')).rejects.toThrow(
        'Cannot unsurface scheduled action with status DRAFT_PENDING. Only actions with status HUMAN_QA_PENDING, SURFACED, PUBLISHED, FAILED, or INVALIDATED can be unsurfaced.',
      );
    });

    it('should throw BadRequestException when action has SURFACING_PENDING status', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const surfacingPendingAction = {
        ...mockScheduledAction,
        status: Status.SURFACING_PENDING,
      };
      // Reset the mock from beforeEach and set a new one
      managerScheduledActionRepository.findOne.mockReset();
      managerScheduledActionRepository.findOne.mockResolvedValueOnce(
        surfacingPendingAction,
      );

      await expect(service.unsurfaceScheduledAction('test-id')).rejects.toThrow(
        'Cannot unsurface scheduled action with status SURFACING_PENDING. Only actions with status HUMAN_QA_PENDING, SURFACED, PUBLISHED, FAILED, or INVALIDATED can be unsurfaced.',
      );
    });

    it('should allow unsurfacing actions with valid statuses', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const validStatuses = [
        Status.HUMAN_QA_PENDING,
        Status.SURFACED,
        Status.PUBLISHED,
        Status.FAILED,
        Status.INVALIDATED,
      ];

      for (const status of validStatuses) {
        const actionWithStatus = {
          ...mockSurfacedAction,
          status,
        };
        managerScheduledActionRepository.findOne.mockResolvedValueOnce(
          actionWithStatus,
        );

        await service.unsurfaceScheduledAction('test-id');

        expect(managerScheduledActionRepository.update).toHaveBeenCalledWith(
          ScheduledAction,
          'test-id',
          expect.objectContaining({
            status: Status.SURFACING_PENDING,
            surfacedAt: null,
            publishedAt: null,
            scheduledToBePublishedAt: null,
          }),
        );
      }
    });

    it('should handle multiple groups and recommendations', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const mockGroup2 = { ...mockGroup, id: 'group-2' };
      const mockRecommendation2 = { ...mockRecommendation, id: 'rec-2' };
      const mockGroupScheduledAction2 = {
        ...mockGroupScheduledAction,
        id: 'gsa-2',
        groupId: 'group-2',
        group: mockGroup2,
      };

      const actionWithMultipleGroups = {
        ...mockSurfacedAction,
        groupScheduledActions: [
          mockGroupScheduledAction,
          mockGroupScheduledAction2,
        ],
      };

      // Reset mocks for this specific test
      managerScheduledActionRepository.findOne.mockReset();
      managerScheduledActionRepository.find.mockReset();
      managerScheduledActionRepository.remove.mockReset();

      managerScheduledActionRepository.findOne
        .mockResolvedValueOnce(actionWithMultipleGroups)
        .mockResolvedValueOnce({
          ...actionWithMultipleGroups,
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: new Date('2024-01-27T10:00:00Z'),
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        });
      managerScheduledActionRepository.find
        .mockResolvedValueOnce([mockRecommendation])
        .mockResolvedValueOnce([mockRecommendation2]);
      managerScheduledActionRepository.remove
        .mockResolvedValueOnce([mockRecommendation])
        .mockResolvedValueOnce([mockRecommendation2]);

      await service.unsurfaceScheduledAction('test-id');

      expect(managerScheduledActionRepository.find).toHaveBeenCalledTimes(2);
      expect(managerScheduledActionRepository.remove).toHaveBeenCalledTimes(2);
      expect(managerScheduledActionRepository.softDelete).toHaveBeenCalledTimes(
        2,
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Deleted 1 recommendations for group group-1',
      );
      expect(Logger.prototype.log).toHaveBeenCalledWith(
        'Soft deleted group group-1',
      );
    });

    it('should handle groups with no recommendations', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      // Reset the find mock and set it to return empty array
      managerScheduledActionRepository.find.mockReset();
      managerScheduledActionRepository.find.mockResolvedValueOnce([]);

      await service.unsurfaceScheduledAction('test-id');

      expect(managerScheduledActionRepository.remove).not.toHaveBeenCalled();
      expect(managerScheduledActionRepository.softDelete).toHaveBeenCalledWith(
        Group,
        { id: 'group-1' },
      );
    });

    it('should handle action with no groups', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      // Reset mocks and set up for action with no groups
      managerScheduledActionRepository.findOne.mockReset();
      managerScheduledActionRepository.find.mockReset();
      const actionWithNoGroups = {
        ...mockSurfacedAction,
        groupScheduledActions: [],
      };
      managerScheduledActionRepository.findOne
        .mockResolvedValueOnce(actionWithNoGroups)
        .mockResolvedValueOnce({
          ...actionWithNoGroups,
          status: Status.SURFACING_PENDING,
          scheduledToBeSurfacedAt: new Date('2024-01-27T10:00:00Z'),
          surfacedAt: null,
          publishedAt: null,
          scheduledToBePublishedAt: null,
        });

      await service.unsurfaceScheduledAction('test-id');

      expect(managerScheduledActionRepository.find).not.toHaveBeenCalled();
      expect(managerScheduledActionRepository.remove).not.toHaveBeenCalled();
      expect(
        managerScheduledActionRepository.softDelete,
      ).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when updated action not found', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      // Reset the mock from beforeEach and set new values
      managerScheduledActionRepository.findOne.mockReset();
      managerScheduledActionRepository.findOne
        .mockResolvedValueOnce(mockSurfacedAction)
        .mockResolvedValueOnce(null);

      await expect(service.unsurfaceScheduledAction('test-id')).rejects.toThrow(
        'Scheduled action with ID test-id not found after update',
      );
    });

    it('should handle transaction errors', async () => {
      jest.spyOn(Logger.prototype, 'log').mockImplementation();
      const error = new Error('Transaction failed');
      mockScheduledActionRepository.manager.transaction.mockRejectedValueOnce(
        error,
      );

      await expect(service.unsurfaceScheduledAction('test-id')).rejects.toThrow(
        error,
      );
    });
  });
});
