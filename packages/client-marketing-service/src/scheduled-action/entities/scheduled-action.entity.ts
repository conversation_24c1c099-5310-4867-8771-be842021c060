import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Factory } from 'nestjs-seeder';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Company } from 'src/company/entities/company.entity';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { PaginatedEntity } from '../../common/utils/pagination';

export enum WorkflowType {
  SEO = 'SEO',
  BLOG = 'BLOG',
}

export enum Status {
  DRAFT_PENDING = 'DRAFT_PENDING',
  HUMAN_QA_PENDING = 'HUMAN_QA_PENDING',
  SURFACING_PENDING = 'SURFACING_PENDING',
  SURFACED = 'SURFACED',
  PUBLISHED = 'PUBLISHED',
  FAILED = 'FAILED',
  INVALIDATED = 'INVALIDATED',
}

// Register enums with GraphQL
registerEnumType(WorkflowType, { name: 'WorkflowType' });
registerEnumType(Status, { name: 'ScheduledActionStatus' });

@Entity('scheduled_action')
@ObjectType()
export class ScheduledAction {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'company_id' })
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  companyId: string;

  @Column({ type: 'enum', enum: WorkflowType, name: 'workflow_type' })
  @Factory(faker => faker?.helpers.arrayElement(Object.values(WorkflowType)))
  @Field(() => WorkflowType)
  workflowType: WorkflowType;

  @Column({ type: 'enum', enum: Status, name: 'status' })
  @Factory(faker => faker?.helpers.arrayElement(Object.values(Status)))
  @Field(() => Status)
  status: Status;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @Column({
    name: 'scheduled_to_be_surfaced_at',
    type: 'timestamptz',
    nullable: true,
  })
  @Field(() => Date, { nullable: true })
  @Factory(faker => faker?.date.soon({ days: 2 }))
  scheduledToBeSurfacedAt: Date | null;

  @Column({ name: 'surfaced_at', type: 'timestamptz', nullable: true })
  @Factory(() => null) // Will be set conditionally in seeder
  @Field(() => Date, { nullable: true })
  surfacedAt: Date | null;

  @Column({
    name: 'scheduled_to_be_published_at',
    type: 'timestamptz',
    nullable: true,
  })
  @Factory(faker => faker?.date.soon({ days: 7 }))
  @Field(() => Date, { nullable: true })
  scheduledToBePublishedAt: Date | null;

  @Column({ name: 'published_at', type: 'timestamptz', nullable: true })
  @Factory(() => null) // Will be set conditionally in seeder
  @Field(() => Date, { nullable: true })
  publishedAt: Date | null;

  @Column({ type: 'jsonb', name: 'content_payload', default: '{}' })
  @Factory(SeederFactories.scheduledActionContentPayload)
  @Field(() => GraphQLJSONObject)
  @ApiProperty({
    description:
      'The content payload for the action, such as the content of the blog post, the recommendations, etc.',
    type: Object,
    required: false,
  })
  contentPayload: object;

  @Column({ type: 'jsonb', name: 'generation_payload', default: '{}' })
  @Factory(SeederFactories.scheduledActionGenerationPayload)
  @Field(() => GraphQLJSONObject)
  @ApiProperty({
    description:
      'Context info for the action such as the event passed to the lambda that created the action, metadata, etc.',
    type: Object,
    required: false,
  })
  generationPayload: object;

  @Column({ type: 'jsonb', name: 'failure_reason', default: '{}' })
  @Factory(SeederFactories.scheduledActionFailureReason)
  @Field(() => GraphQLJSONObject)
  failureReason: object;

  @Column({ name: 'locked_at', type: 'timestamptz', nullable: true })
  @Factory(() => null) // Will be set conditionally in seeder
  @Field(() => Date, { nullable: true })
  lockedAt: Date | null;

  @Column({ name: 'execution_name', type: 'text', nullable: true })
  @Factory(() => null) // Will be set conditionally in seeder
  @Field(() => String, { nullable: true })
  executionName: string | null;

  @Column({ name: 'execution_arn', type: 'text', nullable: true })
  @Factory(() => null) // Will be set conditionally in seeder
  @Field(() => String, { nullable: true })
  executionArn: string | null;

  @OneToMany(
    () => GroupScheduledAction,
    groupScheduledAction => groupScheduledAction.scheduledAction,
  )
  @Field(() => [GroupScheduledAction], { nullable: true })
  groupScheduledActions?: GroupScheduledAction[];

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}

@ObjectType()
export class PaginatedScheduledAction extends PaginatedEntity(
  ScheduledAction,
) {}
