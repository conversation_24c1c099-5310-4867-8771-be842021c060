import { check, sleep } from 'k6';
import http from 'k6/http';

export const options = {
  vus: 200, // Virtual Users
  duration: '30s',
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95th percentile of request duration should be less than 500ms
    http_req_failed: ['rate<0.01'], // Error rate should be less than 1%
  },
};

const companyId = 'mock company id'; // Replace with the company id you want to test
const authToken = 'mock auth token'; // Replace with your actual auth token

export default function () {
  const payload = JSON.stringify({
    operationName: 'HomepageFeed',
    query:
      'query HomepageFeed($companyId: ID!) {\n  homepageFeed(companyId: $companyId) {\n    items {\n      itemType\n      timestamp\n      content {\n        ... on FeedBlogPost {\n          postId\n          title\n          postStatus\n          publishedAt\n          scheduledAt\n          mediaId\n          liveUrl\n          media {\n            id\n            thumbnailUrl\n            smallUrl\n            mediumUrl\n            largeUrl\n            __typename\n          }\n          __typename\n        }\n        ... on FeedAdPerformance {\n          impressionsCount\n          clicksCount\n          leadsCount\n          clicksCountFormatted\n          leadsCountFormatted\n          impressionsCountFormatted\n          __typename\n        }\n        ... on FeedLeads {\n          leads {\n            id\n            firstName\n            lastName\n            handedOffAt\n            __typename\n          }\n          __typename\n        }\n        ... on FeedRecommendationGroup {\n          id\n          publishedAt\n          scheduledToBePublishedAt\n          mediaId\n          media {\n            id\n            thumbnailUrl\n            smallUrl\n            mediumUrl\n            largeUrl\n            __typename\n          }\n          page {\n            name\n            type\n            url\n            __typename\n          }\n          metadata {\n            rank {\n              current\n              original\n              __typename\n            }\n            __typename\n          }\n          recommendations {\n            type\n            __typename\n          }\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}\n',
    variables: {
      companyId,
    },
  });

  const params = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: authToken,
    },
  };

  const res = http.post(
    'https://graphql.luxurypresence.com/graphql',
    payload,
    params,
  );

  check(res, {
    'status is 200': r => {
      return r.status === 200;
    },
  });

  sleep(1); // Simulate user thinking time (1 second)
}
