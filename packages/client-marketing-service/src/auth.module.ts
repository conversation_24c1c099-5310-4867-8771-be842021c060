import {
  AuthContextMiddleware,
  PolicyFactory,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { M2MAuthService } from './auth/services/m2m-auth.service';
import { BlogTopicPolicy } from './blog-topic/blog-topic.policy';
import { BrandProfilePolicy } from './brand-profile/brand-profile.policy';
import { RecommendationGroup } from './group/group.policy';
import { HomepageFeedPolicy } from './homepage-feed/homepage-feed.policy';
import { KeywordPolicy } from './keyword/keyword.policy';
import { PageKeywordPolicy } from './page-keyword/page-keyword.policy';
import { PropertyPolicy } from './property/property.policy';
import { RecommendationPolicy } from './recommendation/recommendation.policy';
import { ScheduledActionPolicy } from './scheduled-action/scheduled-action.policy';
import { ScrapedPagePolicy } from './scraped-page/scraped-page.policy';
import { SeoOptimizationsPolicy } from './seo-optimizations/seo-optimizations.policy';

const policyRegistry = {
  recommendationGroup: RecommendationGroup,
  recommendation: RecommendationPolicy,
  scheduledAction: ScheduledActionPolicy,
  pageKeyword: PageKeywordPolicy,
  scrapedPage: ScrapedPagePolicy,
  keyword: KeywordPolicy,
  seoOptimizations: SeoOptimizationsPolicy,
  homepageFeed: HomepageFeedPolicy,
  brandProfile: BrandProfilePolicy,
  blogTopic: BlogTopicPolicy,
  property: PropertyPolicy,
};

export type AppPolicyRegistry = typeof policyRegistry;

@Module({
  providers: [
    {
      provide: UnifiedAuthContext,
      useFactory: () => {},
      inject: [PolicyFactory],
    },
    {
      provide: PolicyFactory,
      useFactory: moduleRef => {
        return new PolicyFactory(moduleRef);
      },
      inject: [ModuleRef],
    },
    {
      provide: 'POLICY_REGISTRY',
      useValue: policyRegistry,
    },
    {
      provide: 'AUTH_OPTIONS',
      useFactory: (configService: ConfigService) => {
        return {
          m2mSuperApiKeys: configService.get('m2mSuperApiKeys'),
          policyRegistry,
          auth0Config: {
            auth0Domain: configService.get('auth0Config.auth0Domain'),
            hostname: configService.get('auth0Config.hostname'),
            acceptedIssuers: configService
              .get('auth0Config.acceptedIssuers')
              .split(' '),
            namespace: configService.get('auth0Config.namespace'),
          },
          websiteServiceUrl: configService.get('websiteService.url'),
        };
      },
      inject: [ConfigService],
    },
    {
      provide: AuthContextMiddleware,
      useFactory: (options, policyFactory) => {
        return new AuthContextMiddleware<AppPolicyRegistry>({
          ...options,
          policyFactory,
        });
      },
      inject: ['AUTH_OPTIONS', PolicyFactory],
    },
    M2MAuthService,
    RecommendationGroup,
    RecommendationPolicy,
    ScheduledActionPolicy,
    PageKeywordPolicy,
    ScrapedPagePolicy,
    KeywordPolicy,
    SeoOptimizationsPolicy,
    HomepageFeedPolicy,
    BrandProfilePolicy,
    BlogTopicPolicy,
    PropertyPolicy,
  ],
  exports: [AuthContextMiddleware, M2MAuthService],
})
export class AuthModule {}
