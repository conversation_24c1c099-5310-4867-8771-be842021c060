import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

import { Email } from './notification.types';
import { TenantService } from '../common/services/tenant/tenant.service';
import {
  notifySendEmailFailure,
  sendSlackMessage,
} from '../common/utils/slack.util';
import { GroupService } from '../group/group.service';
import { EmailDto } from './dto/send-email.dto';
import { EmailService } from './services/email.service';
import { Group } from '../group/entities/group.entity';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(
    private readonly emailService: EmailService,
    private readonly tenantService: TenantService,
    private readonly groupService: GroupService,
  ) {}

  private constructEmailContent(group: Group, admin: any): Email {
    return {
      to: admin.email,
      subject: `SEO Recommendations for ${group.title}`,
      dynamicTemplateData: {
        user: {
          firstName: admin.firstName,
          lastName: admin.lastName,
        },
        seoGroup: plainToInstance(Group, group),
      },
    };
  }

  async sendGroupNotification(
    groupIds: string[],
    options: { dryRun?: boolean } = {},
    jobId?: string,
  ): Promise<
    | {
        successful: Array<Email>;
        failed: Array<Email>;
      }
    | {
        dryRun: true;
        emails: Array<Email>;
      }
  > {
    const dryRunEmails: Array<Email> = [];
    const failedEmails: Array<Email> = [];
    const successEmails: Array<Email> = [];

    for (const groupId of groupIds) {
      try {
        const group = await this.groupService.findOne(groupId);
        if (!group) {
          this.logger.log(`Group not found: ${groupId}`, {
            jobId: jobId || 'N/A',
            groupId,
          });
          continue;
        }

        // Get company admins
        const admins = await this.tenantService.find({
          displayId: group.companyId,
        });

        if (!admins.length) {
          this.logger.log(`No admins found for company: ${group.companyId}`, {
            jobId: jobId || 'N/A',
            companyId: group.companyId,
          });
          continue;
        }

        // Send notification to each admin
        const scheduledToBePublishedAt = new Date(
          // 7 days from now
          Date.now() + 7 * 24 * 60 * 60 * 1000,
        );
        for (const admin of admins) {
          const emailContent = this.constructEmailContent(group, admin);
          try {
            if (options.dryRun) {
              dryRunEmails.push({
                to: admin.email,
                subject: emailContent.subject,
                dynamicTemplateData: emailContent.dynamicTemplateData,
              });
            } else {
              await this.emailService.sendPostGeneratedEmail(
                emailContent,
                jobId,
              );
              // Update group surfacedAt timestamp
              await this.groupService.update(groupId, {
                surfacedAt: new Date(),
                scheduledToBePublishedAt,
              });

              this.logger.log(
                `Scheduled to apply recommendations on for group: ${group.id} to ${scheduledToBePublishedAt.toISOString()}`,
                { jobId: jobId || 'N/A' },
              );

              this.logger.log(
                `Sent notification to ${admin.email} for group: ${groupId}`,
              );
              successEmails.push(emailContent);
            }
          } catch (error) {
            await notifySendEmailFailure({
              email: emailContent,
              error,
              jobId,
            });
            this.logger.error(
              `Failed to send notification to ${admin.email} for group: ${groupId}`,
              error,
              { jobId: jobId || 'N/A' },
            );
            failedEmails.push(emailContent);
          }
        }

        if (!options.dryRun) {
          this.logger.log(`Completed notifications for group: ${groupId}`, {
            jobId: jobId || 'N/A',
          });
        }
      } catch (error) {
        await sendSlackMessage(
          `Something went wrong with sending a notification ${error instanceof Error ? error.message : String(error)}. Job ID: ${jobId || 'N/A'}`,
        );
        this.logger.error(
          `Failed to process notifications for group: ${groupId}`,
          error,
          { jobId: jobId || 'N/A' },
        );
      }
    }

    if (options.dryRun) {
      return { dryRun: true, emails: dryRunEmails };
    }
    return { successful: successEmails, failed: failedEmails };
  }

  async sendEmail(dto: EmailDto): Promise<void> {
    const {
      recipients,
      dynamicTemplateData,
      templateId,
      bcc,
      groupUnsubscribeId,
      subject,
    } = dto;
    this.logger.log('Email DTO', { dto });
    try {
      await this.emailService.sendPostGeneratedEmail({
        to: recipients,
        templateId: templateId,
        dynamicTemplateData,
        bcc,
        groupUnsubscribeId,
        subject,
      });
    } catch (error) {
      this.logger.error('Failed to send email', {
        message: error instanceof Error ? error.message : String(error),
        params: dto,
      });
      throw error;
    }
  }
}
