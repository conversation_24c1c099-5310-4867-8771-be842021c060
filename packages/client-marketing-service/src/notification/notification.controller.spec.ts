import { Test, TestingModule } from '@nestjs/testing';

import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';

describe('NotificationController', () => {
  let controller: NotificationController;
  let service: NotificationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationController],
      providers: [
        {
          provide: NotificationService,
          useValue: {
            sendGroupNotification: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<NotificationController>(NotificationController);
    service = module.get<NotificationService>(NotificationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendGroupNotification', () => {
    const mockGroupIds = ['group-1', 'group-2'];

    it('should call service.sendGroupNotification with correct group IDs', async () => {
      await controller.sendGroupNotification({
        groupIds: mockGroupIds,
        dryRun: false,
      });

      expect(service.sendGroupNotification).toHaveBeenCalledWith(mockGroupIds, {
        dryRun: false,
      });
    });

    it('should handle service errors', async () => {
      const error = new Error('Failed to send notifications');
      jest.spyOn(service, 'sendGroupNotification').mockRejectedValue(error);

      await expect(
        controller.sendGroupNotification({
          groupIds: mockGroupIds,
          dryRun: false,
        }),
      ).rejects.toThrow(error);
    });

    it('should return successful and failed emails on success', async () => {
      jest.spyOn(service, 'sendGroupNotification').mockResolvedValue({
        successful: [],
        failed: [],
      });

      const result = await controller.sendGroupNotification({
        groupIds: mockGroupIds,
        dryRun: false,
      });

      expect(result).toEqual({
        successful: [],
        failed: [],
      });
    });
  });
});
