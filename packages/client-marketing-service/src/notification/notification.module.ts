import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { NotificationController } from './notification.controller';
import { NotificationService } from './notification.service';
import { EmailService } from './services/email.service';
import { TenantModule } from '../common/services/tenant/tenant.module';
import { GroupModule } from '../group/group.module';

@Module({
  imports: [GroupModule, HttpModule, TenantModule],
  controllers: [NotificationController],
  providers: [NotificationService, EmailService],
  exports: [NotificationService, EmailService],
})
export class NotificationModule {}
