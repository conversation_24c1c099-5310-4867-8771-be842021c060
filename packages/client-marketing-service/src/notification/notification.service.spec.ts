import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { RecommendationType } from 'src/common/types/scraped-element.type';

import { NotificationService } from './notification.service';
import { EmailService } from './services/email.service';
import { TenantService } from '../common/services/tenant/tenant.service';
import { Group } from '../group/entities/group.entity';
import { GroupService } from '../group/group.service';
import { RecommendationStatus } from '../recommendation/entities/recommendation.entity';

describe('NotificationService', () => {
  let service: NotificationService;
  let emailService: EmailService;
  let tenantService: TenantService;
  let groupService: GroupService;
  let mockLogger: jest.SpyInstance;

  const mockGroup = {
    id: 'group-1',
    title: 'Test Group',
    companyId: 'company-1',
    description: 'Test Description',
    createdAt: new Date('2025-03-31'),
    updatedAt: new Date('2025-03-31'),
    surfacedAt: new Date('2025-03-31'),
    metadata: {
      keyword: 'test keyword',
      ranking: 5,
    },
    recommendations: [
      {
        id: 'rec-1',
        seoPageId: 'page-1',
        groupId: 'group-1',
        type: RecommendationType.META_TITLE,
        currentValue: 'Current Title',
        recommendationValue: 'Recommended Title',
        status: RecommendationStatus.PENDING,
        createdAt: new Date('2025-03-31'),
        updatedAt: new Date('2025-03-31'),
      },
    ],
  } as unknown as Group;

  const mockAdmins = [
    {
      userId: 'user-1',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'One',
      displayId: 'user-1',
    },
  ];

  beforeEach(async () => {
    mockLogger = jest.spyOn(Logger.prototype, 'log');
    jest.spyOn(Logger.prototype, 'error');
    jest.spyOn(Logger.prototype, 'log');

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: EmailService,
          useValue: {
            sendPostGeneratedEmail: jest.fn(),
          },
        },
        {
          provide: TenantService,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: GroupService,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    emailService = module.get<EmailService>(EmailService);
    tenantService = module.get<TenantService>(TenantService);
    groupService = module.get<GroupService>(GroupService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendGroupNotification', () => {
    const groupIds = ['group-1'];

    beforeEach(() => {
      jest.spyOn(groupService, 'findOne').mockResolvedValue(mockGroup);
      jest.spyOn(tenantService, 'find').mockResolvedValue(
        mockAdmins.map(admin => ({
          ...admin,
          companyId: mockGroup.companyId,
          displayId: mockGroup.companyId,
        })),
      );
      jest
        .spyOn(emailService, 'sendPostGeneratedEmail')
        .mockResolvedValue(undefined);
    });

    it('should send notifications to all admins for each group', async () => {
      await service.sendGroupNotification(groupIds);

      expect(groupService.findOne).toHaveBeenCalledWith(groupIds[0]);
      expect(tenantService.find).toHaveBeenCalledWith({
        displayId: mockGroup.companyId,
      });
      expect(emailService.sendPostGeneratedEmail).toHaveBeenCalledWith(
        {
          to: mockAdmins[0].email,
          subject: `SEO Recommendations for ${mockGroup.title}`,
          dynamicTemplateData: {
            user: {
              firstName: mockAdmins[0].firstName,
              lastName: mockAdmins[0].lastName,
            },
            seoGroup: expect.objectContaining({
              id: mockGroup.id,
              title: mockGroup.title,
            }),
          },
        },
        undefined,
      );
    });

    it('should log warning and continue if group is not found', async () => {
      jest
        .spyOn(groupService, 'findOne')
        .mockResolvedValue(null as unknown as Group);

      await service.sendGroupNotification(groupIds);

      expect(mockLogger).toHaveBeenCalledWith(
        `Group not found: ${groupIds[0]}`,
        expect.objectContaining({
          jobId: 'N/A',
          groupId: groupIds[0],
        }),
      );
      expect(tenantService.find).not.toHaveBeenCalled();
      expect(emailService.sendPostGeneratedEmail).not.toHaveBeenCalled();
    });

    it('should log warning and continue if no admins found', async () => {
      jest.spyOn(tenantService, 'find').mockResolvedValue([]);

      await service.sendGroupNotification(groupIds);

      expect(mockLogger).toHaveBeenCalledWith(
        `No admins found for company: ${mockGroup.companyId}`,
        expect.objectContaining({
          jobId: 'N/A',
          companyId: mockGroup.companyId,
        }),
      );
      expect(emailService.sendPostGeneratedEmail).not.toHaveBeenCalled();
    });

    it('should handle email service errors for individual admins', async () => {
      const error = new Error('Failed to send email');
      jest
        .spyOn(emailService, 'sendPostGeneratedEmail')
        .mockRejectedValue(error);

      const mockLogger = jest.spyOn(Logger.prototype, 'error');
      await service.sendGroupNotification(groupIds);

      expect(mockLogger).toHaveBeenCalledWith(
        `Failed to send notification to ${mockAdmins[0].email} for group: ${groupIds[0]}`,
        error,
        expect.objectContaining({
          jobId: 'N/A',
        }),
      );
    });

    it('should return email contents when dryRun is true', async () => {
      const result = await service.sendGroupNotification(groupIds, {
        dryRun: true,
      });

      expect(result).toEqual({
        dryRun: true,
        emails: [
          {
            to: mockAdmins[0].email,
            subject: `SEO Recommendations for ${mockGroup.title}`,
            dynamicTemplateData: {
              user: {
                firstName: mockAdmins[0].firstName,
                lastName: mockAdmins[0].lastName,
              },
              seoGroup: expect.objectContaining({
                id: mockGroup.id,
                title: mockGroup.title,
              }),
            },
          },
        ],
      });
      expect(emailService.sendPostGeneratedEmail).not.toHaveBeenCalled();
    });

    it('should not send emails when dryRun is true', async () => {
      await service.sendGroupNotification(groupIds, { dryRun: true });

      expect(groupService.findOne).toHaveBeenCalled();
      expect(tenantService.find).toHaveBeenCalled();
      expect(emailService.sendPostGeneratedEmail).not.toHaveBeenCalled();
    });

    it('should update group surfacedAt and scheduledToBePublishedAt timestamps after successful email notification', async () => {
      const now = new Date();
      jest.useFakeTimers().setSystemTime(now);

      await service.sendGroupNotification(groupIds);

      expect(groupService.update).toHaveBeenCalledWith(groupIds[0], {
        surfacedAt: now,
        scheduledToBePublishedAt: new Date(
          now.getTime() + 7 * 24 * 60 * 60 * 1000,
        ),
      });
      jest.useRealTimers();
    });

    it('should not update group surfacedAt or scheduledToBePublishedAt when dryRun is true', async () => {
      await service.sendGroupNotification(groupIds, { dryRun: true });

      expect(groupService.update).not.toHaveBeenCalled();
    });

    it('should not update group surfacedAt or scheduledToBePublishedAt when email sending fails', async () => {
      jest
        .spyOn(emailService, 'sendPostGeneratedEmail')
        .mockRejectedValue(new Error('Failed to send email'));

      await service.sendGroupNotification(groupIds);

      expect(groupService.update).not.toHaveBeenCalled();
    });

    it('should update surfacedAt and scheduledToBePublishedAt for each group after successful email notifications', async () => {
      const now = new Date();
      jest.useFakeTimers().setSystemTime(now);

      const multipleGroupIds = ['group-1', 'group-2'];
      await service.sendGroupNotification(multipleGroupIds);

      expect(groupService.update).toHaveBeenCalledTimes(2);
      expect(groupService.update).toHaveBeenNthCalledWith(
        1,
        multipleGroupIds[0],
        {
          surfacedAt: now,
          scheduledToBePublishedAt: new Date(
            now.getTime() + 7 * 24 * 60 * 60 * 1000,
          ),
        },
      );
      expect(groupService.update).toHaveBeenNthCalledWith(
        2,
        multipleGroupIds[1],
        {
          surfacedAt: now,
          scheduledToBePublishedAt: new Date(
            now.getTime() + 7 * 24 * 60 * 60 * 1000,
          ),
        },
      );

      jest.useRealTimers();
    });

    it('should process multiple groups', async () => {
      const multipleGroupIds = ['group-1', 'group-2'];
      await service.sendGroupNotification(multipleGroupIds);

      expect(groupService.findOne).toHaveBeenCalledTimes(2);
      expect(groupService.findOne).toHaveBeenNthCalledWith(
        1,
        multipleGroupIds[0],
      );
      expect(groupService.findOne).toHaveBeenNthCalledWith(
        2,
        multipleGroupIds[1],
      );
    });
  });

  describe('tenant service integration', () => {
    it('should properly combine company and user data', async () => {
      jest.spyOn(groupService, 'findOne').mockResolvedValue(mockGroup);

      jest.spyOn(tenantService, 'find').mockImplementation(() =>
        Promise.resolve([
          {
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            displayId: 'company-1',
            companyId: 'company-1',
          },
        ]),
      );

      await service.sendGroupNotification([mockGroup.id]);

      expect(emailService.sendPostGeneratedEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: `SEO Recommendations for ${mockGroup.title}`,
          dynamicTemplateData: {
            user: {
              firstName: 'John',
              lastName: 'Doe',
            },
            seoGroup: expect.objectContaining(mockGroup),
          },
        }),
        undefined,
      );
    });

    it('should handle missing user data gracefully', async () => {
      jest.spyOn(groupService, 'findOne').mockResolvedValue(mockGroup);

      jest.spyOn(tenantService, 'find').mockImplementation(() =>
        Promise.resolve([
          {
            email: '<EMAIL>',
            firstName: '',
            lastName: '',
            displayId: 'company-1',
            companyId: 'company-1',
          },
        ]),
      );

      await service.sendGroupNotification([mockGroup.id]);

      expect(emailService.sendPostGeneratedEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: `SEO Recommendations for ${mockGroup.title}`,
          dynamicTemplateData: {
            user: {
              firstName: '',
              lastName: '',
            },
            seoGroup: expect.objectContaining(mockGroup),
          },
        }),
        undefined,
      );
    });
  });
});
