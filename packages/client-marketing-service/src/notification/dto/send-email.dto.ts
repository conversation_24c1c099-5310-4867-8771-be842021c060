import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON>rray,
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class EmailDto {
  @ApiProperty({
    description: 'The company ID',
    type: String,
  })
  @IsString()
  companyId: string;

  @ApiProperty({
    description: 'Array of email addresses to send the email to',
    type: [String],
  })
  @IsArray()
  @IsEmail({}, { each: true })
  recipients: string[];

  @ApiProperty({
    description: 'The bcc email addresses for the email',
    type: [String],
  })
  @IsArray()
  @IsEmail({}, { each: true })
  @IsOptional()
  bcc: string[];

  @ApiProperty({
    description: 'The dynamic template data for the email',
    type: Object,
  })
  @IsObject()
  @IsOptional()
  dynamicTemplateData: Record<string, unknown>;

  @ApiProperty({
    description: 'The template ID to use for the email',
    type: String,
  })
  @IsString()
  @IsOptional()
  templateId: string;

  @ApiProperty({
    description: 'The group unsubscribe ID to use for the email',
    type: String,
  })
  @IsString()
  @IsOptional()
  groupUnsubscribeId: string;

  @ApiProperty({
    description: 'The subject of the email',
    type: String,
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  subject: string;
}
