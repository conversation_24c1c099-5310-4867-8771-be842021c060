import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsUUID, IsOptional, IsBoolean } from 'class-validator';

export class SendGroupNotificationDto {
  @ApiProperty({
    description: 'Array of group IDs to send notifications for',
    type: [String],
  })
  @IsArray()
  @IsUUID('all', { each: true })
  groupIds: string[];

  @ApiProperty({
    description:
      'If true, returns the email content that would be sent without actually sending it',
    type: Boolean,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  dryRun?: boolean;
}
