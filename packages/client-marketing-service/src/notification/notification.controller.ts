import { <PERSON>, Post, Body } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DataResponseDto } from 'src/common/interceptors/response-wrapper.interceptor';

import { EmailDto } from './dto/send-email.dto';
import { SendGroupNotificationDto } from './dto/send-group-notification.dto';
import { NotificationService } from './notification.service';
import { Email } from './notification.types';

@ApiExtraModels(DataResponseDto)
@ApiTags('notification')
@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post('group')
  @ApiOperation({
    summary: 'Send notifications for specified groups',
    description:
      'Send notifications for the specified groups. Use dryRun=true to preview the email content without sending.',
  })
  async sendGroupNotification(@Body() dto: SendGroupNotificationDto): Promise<
    | void
    | {
        dryRun: true;
        emails: Array<Email>;
      }
    | {
        successful: Array<Email>;
        failed: Array<Email>;
      }
  > {
    return this.notificationService.sendGroupNotification(dto.groupIds, {
      dryRun: dto.dryRun,
    });
  }

  @Post('email')
  @ApiOperation({
    summary: 'Send email',
    description: 'Send email via notification-service',
  })
  async sendEmail(@Body() dto: EmailDto): Promise<void> {
    return this.notificationService.sendEmail(dto);
  }
}
