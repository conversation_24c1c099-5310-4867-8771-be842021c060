import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';
import { RecommendationType } from 'src/common/types/scraped-element.type';

import { EmailService } from './email.service';
import { Group } from '../../group/entities/group.entity';
import { RecommendationStatus } from '../../recommendation/entities/recommendation.entity';

import type { AxiosResponse } from 'axios';

describe('EmailService', () => {
  let service: EmailService;
  let httpService: HttpService;
  let configService: ConfigService;

  const mockConfig = {
    'notificationService.url': 'http://notification-service',
    'notificationService.emailTemplateId': 'test-template-id',
  };

  const mockGroup = {
    id: 'group-1',
    title: 'Test Group',
    companyId: 'company-1',
    description: 'Test Description',
    createdAt: new Date(),
    updatedAt: new Date(),
    surfacedAt: new Date(),
    metadata: {
      keyword: 'test keyword',
      ranking: 5,
    },
    recommendations: [
      {
        id: 'rec-1',
        seoPageId: 'page-1',
        groupId: 'group-1',
        type: RecommendationType.META_TITLE,
        currentValue: 'Current Title',
        recommendationValue: 'Recommended Title',
        status: RecommendationStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ],
  } as unknown as Group;

  const mockEmail = {
    to: '<EMAIL>',
    subject: 'Test Email Subject',
    dynamicTemplateData: {
      user: {
        firstName: 'Test',
        lastName: 'User',
      },
      seoGroup: mockGroup,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: HttpService,
          useValue: {
            post: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => mockConfig[key]),
          },
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendPostGeneratedEmail', () => {
    it('should send email with correct template and data', async () => {
      const mockResponse = { success: true };
      jest.spyOn(httpService, 'post').mockReturnValue(
        of({
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {
            headers: {},
          },
        } as AxiosResponse),
      );

      const result = await service.sendPostGeneratedEmail(mockEmail);

      expect(httpService.post).toHaveBeenCalledWith(
        'http://notification-service/api/v1/emails',
        {
          templateId: 'test-template-id',
          from: 'Luxury Presence <<EMAIL>>',
          replyTo: '<EMAIL>',
          to: mockEmail.to,
          subject: mockEmail.subject,
          dynamicTemplateData: mockEmail.dynamicTemplateData,
        },
      );

      expect(result).toEqual(mockResponse);
    });

    it('should use default template ID if not configured', async () => {
      jest.spyOn(configService, 'get').mockReturnValue(undefined);
      jest.spyOn(httpService, 'post').mockReturnValue(
        of({
          data: {},
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {
            headers: {},
          },
        } as AxiosResponse),
      );

      await service.sendPostGeneratedEmail(mockEmail);

      expect(httpService.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          templateId:
            configService.get<string>('notificationService.emailTemplateId') ||
            'd-cc46493aba224cbcb2b41598c5654776',
          from: service.DEFAULT_FROM_EMAIL,
          to: mockEmail.to,
          subject: mockEmail.subject,
          dynamicTemplateData: expect.any(Object),
        }),
      );
    });

    it('should handle single BCC recipient as string', async () => {
      const mockResponse = { success: true };
      const emailWithBcc = {
        ...mockEmail,
        bcc: '<EMAIL>',
      };

      jest.spyOn(httpService, 'post').mockReturnValue(
        of({
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {
            headers: {},
          },
        } as AxiosResponse),
      );

      await service.sendPostGeneratedEmail(emailWithBcc);

      expect(httpService.post).toHaveBeenCalledTimes(1);
    });

    it('should not send BCC email when BCC is empty array', async () => {
      const mockResponse = { success: true };
      const emailWithEmptyBcc = {
        ...mockEmail,
        bcc: [],
      };

      jest.spyOn(httpService, 'post').mockReturnValue(
        of({
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {
            headers: {},
          },
        } as AxiosResponse),
      );

      await service.sendPostGeneratedEmail(emailWithEmptyBcc);

      expect(httpService.post).toHaveBeenCalledTimes(1);
    });

    it('should not send BCC email when BCC is empty string', async () => {
      const mockResponse = { success: true };
      const emailWithEmptyBcc = {
        ...mockEmail,
        bcc: '   ',
      };

      jest.spyOn(httpService, 'post').mockReturnValue(
        of({
          data: mockResponse,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {
            headers: {},
          },
        } as AxiosResponse),
      );

      await service.sendPostGeneratedEmail(emailWithEmptyBcc);

      expect(httpService.post).toHaveBeenCalledTimes(1);
    });

    it('should handle HTTP errors gracefully', async () => {
      const mockError = {
        message: 'Service unavailable',
        code: 'ECONNREFUSED',
        config: {
          method: 'POST',
          url: 'http://notification-service/api/v1/emails',
        },
      };

      jest
        .spyOn(httpService, 'post')
        .mockReturnValue(throwError(() => mockError));

      await expect(service.sendPostGeneratedEmail(mockEmail)).rejects.toThrow(
        new HttpException(
          {
            error: 'Notification Service Error',
            message:
              'Failed to communicate with notification service: Service unavailable',
            code: 'ECONNREFUSED',
            requestMethod: 'POST',
            requestUrl: '/api/v1/emails',
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        ),
      );
    });

    it('should handle HTTP errors without config', async () => {
      const mockError = {
        message: 'Service unavailable',
      };

      jest
        .spyOn(httpService, 'post')
        .mockReturnValue(throwError(() => mockError));

      await expect(service.sendPostGeneratedEmail(mockEmail)).rejects.toThrow(
        new HttpException(
          {
            error: 'Notification Service Error',
            message:
              'Failed to communicate with notification service: Service unavailable',
            code: undefined,
            requestMethod: undefined,
            requestUrl: undefined,
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        ),
      );
    });
  });
});
