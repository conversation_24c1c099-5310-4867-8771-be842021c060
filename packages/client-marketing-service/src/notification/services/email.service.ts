import { HttpService } from '@nestjs/axios';
import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import { Email, TemplateData } from '../notification.types';

const DEFAULT_TEMPLATE_ID = 'd-cc46493aba224cbcb2b41598c5654776';

export interface SendNotificationDto {
  templateId: string;
  from: string;
  replyTo?: string;
  to: string | string[];
  scheduledAt?: Date;
  dynamicTemplateData?: TemplateData | Record<string, unknown>;
  bcc?: string | string[];
  groupUnsubscribeId?: string;
  subject: string;
}

@Injectable()
export class EmailService {
  private static readonly DEFAULT_FROM_EMAIL =
    'Luxury Presence <<EMAIL>>';
  private static readonly DEFAULT_REPLY_TO = '<EMAIL>';

  public get DEFAULT_FROM_EMAIL(): string {
    return EmailService.DEFAULT_FROM_EMAIL;
  }

  public get DEFAULT_REPLY_TO(): string {
    return EmailService.DEFAULT_REPLY_TO;
  }

  private readonly baseUrl: string;
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = `${this.configService.get<string>('notificationService.url')}/api/v1`;
  }

  private async post(
    path: string,
    data: SendNotificationDto,
    jobId?: string,
  ): Promise<any> {
    this.logger.log('Sending notification', {
      baseUrl: this.baseUrl,
      path,
      data,
      jobId: jobId || 'N/A',
    });
    try {
      const response = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/${path}`, {
          ...data,
        }),
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        'Error sending email with notification service',
        error,
        {
          jobId: jobId || 'N/A',
          path,
          data,
        },
      );
      throw new HttpException(
        {
          error: 'Notification Service Error',
          message: `Failed to communicate with notification service: ${error.message}`,
          code: error.code,
          requestMethod: error.config?.method,
          requestUrl: error.config?.url
            ? new URL(error.config.url).pathname
            : undefined,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async sendPostGeneratedEmail(params: Email, jobId?: string): Promise<any> {
    const {
      to,
      templateId,
      dynamicTemplateData,
      bcc,
      groupUnsubscribeId,
      subject,
    } = params;
    this.logger.log('Sending post-generated email', {
      params,
      jobId: jobId || 'N/A',
      emailToAddress: to,
    });

    const emailPayload = {
      templateId:
        templateId ||
        this.configService.get<string>('notificationService.emailTemplateId') ||
        DEFAULT_TEMPLATE_ID,
      from: this.DEFAULT_FROM_EMAIL,
      replyTo: this.DEFAULT_REPLY_TO,
      to,
      dynamicTemplateData,
      bcc,
      groupUnsubscribeId,
      subject,
    };

    return await this.post('emails', emailPayload, jobId);
  }
}
