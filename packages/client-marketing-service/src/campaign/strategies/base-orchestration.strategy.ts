import { Logger } from '@nestjs/common';

import { CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationStrategy,
  CampaignOrchestrationContext,
  CampaignOrchestrationResult,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';
import { CampaignPaymentService } from '../payment/campaign-payment.service';

export abstract class BaseOrchestrationStrategy
  implements CampaignOrchestrationStrategy
{
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    protected readonly campaignPaymentService: CampaignPaymentService,
  ) {}

  abstract execute(
    context: CampaignOrchestrationContext,
  ): Promise<CampaignOrchestrationResult>;

  abstract getMode(): CampaignOrchestrationMode;

  /**
   * Shared validation logic
   */
  protected validateCampaign(
    context: CampaignOrchestrationContext,
  ): CampaignOrchestrationResult | null {
    const { campaign, metadata = {} } = context;
    const isValid =
      campaign.id && campaign.propertyId && campaign.priceCents > 0;

    if (!isValid) {
      return {
        campaign: {
          ...campaign,
          status: CampaignStatus.ERROR,
        },
        metadata: {
          ...metadata,
          orchestrationStep: 'validation',
        },
        orchestrationMode: this.getMode(),
        success: false,
        error: 'Campaign validation failed',
      };
    }

    return null; // Validation passed
  }

  /**
   * Shared payment processing logic
   * @returns Error result if payment fails, null if payment succeeds
   */
  protected async processPayment(
    context: CampaignOrchestrationContext,
  ): Promise<{
    result: CampaignOrchestrationResult | null;
    transactionId?: string;
  }> {
    const { campaign, metadata = {} } = context;

    const paymentResult =
      await this.campaignPaymentService.processCampaignPayment({
        campaign,
      });

    if (!paymentResult.success) {
      this.logger.error('Payment processing failed', {
        campaignId: campaign.id,
        error: paymentResult.error,
      });

      return {
        result: {
          campaign: {
            ...campaign,
            status: CampaignStatus.ERROR,
          },
          metadata: {
            ...metadata,
            paymentError: paymentResult.error,
            orchestrationStep: 'payment',
          },
          orchestrationMode: this.getMode(),
          success: false,
          error: `Payment processing failed: ${paymentResult.error}`,
        },
      };
    }

    return { result: null, transactionId: paymentResult.transactionId }; // Payment succeeded
  }

  /**
   * Shared error handling for unexpected errors
   */
  protected handleUnexpectedError(
    context: CampaignOrchestrationContext,
    error: Error,
  ): CampaignOrchestrationResult {
    const { campaign, metadata = {} } = context;

    this.logger.error(`${this.constructor.name} failed with unexpected error`, {
      campaignId: campaign.id,
      error: error.message,
      stack: error.stack,
    });

    return {
      campaign: {
        ...campaign,
        status: CampaignStatus.ERROR,
      },
      metadata: {
        ...metadata,
        unexpectedError: error.message,
        orchestrationStep: 'error',
      },
      orchestrationMode: this.getMode(),
      success: false,
      error: `Orchestration failed: ${error.message}`,
    };
  }
}
