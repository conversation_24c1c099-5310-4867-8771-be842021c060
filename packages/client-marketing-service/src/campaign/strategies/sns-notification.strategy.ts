import {
  SNSClient,
  PublishCommand,
  PublishCommandInput,
} from '@aws-sdk/client-sns';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { Campaign } from '../entities/campaign.entity';
import {
  NotificationStrategy,
  NotificationWorkItem,
  NotificationResult,
} from '../interfaces/notification-strategy.interface';

@Injectable()
export class SNSNotificationStrategy implements NotificationStrategy {
  private readonly logger = new Logger(SNSNotificationStrategy.name);
  private readonly snsClient: SNSClient;
  private readonly snsTopic: string;

  constructor(private readonly configService: ConfigService) {
    const awsRegion = this.configService.get<string>('aws.region', 'us-east-1');
    this.snsClient = new SNSClient({ region: awsRegion });
    this.snsTopic = this.configService.get<string>('campaign.snsTopicArn', '');
  }

  async notify(
    workItem: NotificationWorkItem,
    campaign: Campaign,
  ): Promise<NotificationResult> {
    try {
      if (!this.snsTopic) {
        this.logger.warn('SNS Topic ARN not configured, skipping notification');
        return {
          success: true,
          messageId: 'sns-skipped',
        };
      }

      const params: PublishCommandInput = {
        TopicArn: this.snsTopic,
        Message: JSON.stringify(workItem),
      };

      const command = new PublishCommand(params);
      const response = await this.snsClient.send(command);

      this.logger.log(
        `SNS notification sent successfully for campaign ${campaign.id}`,
      );

      return {
        success: true,
        messageId: response.MessageId || `sns-${Date.now()}`,
      };
    } catch (error) {
      this.logger.error(
        `Failed to send SNS notification for campaign ${campaign.id}:`,
        error,
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
