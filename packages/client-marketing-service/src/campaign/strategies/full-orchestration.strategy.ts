import { Injectable } from '@nestjs/common';

import { BaseOrchestrationStrategy } from './base-orchestration.strategy';
import { BundleLifecycleService } from '../bundle/bundle-lifecycle.service';
import { CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationContext,
  CampaignOrchestrationResult,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';
import { CampaignPaymentService } from '../payment/campaign-payment.service';

@Injectable()
export class FullOrchestrationStrategy extends BaseOrchestrationStrategy {
  constructor(
    campaignPaymentService: CampaignPaymentService,
    private readonly bundleLifecycleService: BundleLifecycleService,
  ) {
    super(campaignPaymentService);
  }

  async execute(
    context: CampaignOrchestrationContext,
  ): Promise<CampaignOrchestrationResult> {
    const { campaign, metadata = {} } = context;

    this.logger.log('Executing full orchestration for campaign', {
      campaignId: campaign.id,
      mode: this.getMode(),
    });

    try {
      // Step 1: Validate campaign using shared logic
      const validationError = this.validateCampaign(context);
      if (validationError) {
        return validationError;
      }

      // Step 2: Process payment using shared logic
      const paymentResult = await this.processPayment(context);
      if (paymentResult.result) {
        return paymentResult.result;
      }
      const transactionId = paymentResult.transactionId;

      // Step 3: Generate promo bundle
      const bundleResult =
        await this.bundleLifecycleService.generatePromoBundle(campaign);
      if (!bundleResult.success) {
        this.logger.error('Bundle generation failed', {
          campaignId: campaign.id,
          error: bundleResult.error,
        });

        return {
          campaign: {
            ...campaign,
            status: CampaignStatus.ERROR,
          },
          metadata: {
            ...metadata,
            bundleGenerationError: bundleResult.error,
            orchestrationStep: 'bundle_generation',
          },
          orchestrationMode: this.getMode(),
          success: false,
          error: bundleResult.error || 'Failed to generate bundle',
        };
      }

      const bundleId = bundleResult.data?.bundleId;

      // Step 3.5: Upload bundle to storage (produces a bundleUri)
      const uploadResult =
        await this.bundleLifecycleService.uploadBundle(campaign);
      if (!uploadResult.success) {
        this.logger.error('Bundle upload failed', {
          campaignId: campaign.id,
          error: uploadResult.error,
        });

        return {
          campaign: {
            ...campaign,
            status: CampaignStatus.ERROR,
          },
          metadata: {
            ...metadata,
            bundleId,
            uploadError: uploadResult.error,
            orchestrationStep: 'upload',
          },
          orchestrationMode: this.getMode(),
          success: false,
          error: uploadResult.error || 'Failed to upload bundle',
        };
      }

      const bundleUri = uploadResult.data?.bundleUri ?? campaign.bundleUri;
      this.logger.log('Generated and uploaded promo bundle', {
        campaignId: campaign.id,
        bundleId,
        bundleUri,
      });

      // Step 4: Publish promo bundle to S3 and send to SQS/SNS (Full orchestration specific)
      const publishResult =
        await this.bundleLifecycleService.publishBundle(campaign);
      if (!publishResult.success) {
        this.logger.error('Bundle publishing failed', {
          campaignId: campaign.id,
          error: publishResult.error,
        });

        return {
          campaign: {
            ...campaign,
            status: CampaignStatus.ERROR,
          },
          metadata: {
            ...metadata,
            bundleId,
            publishError: publishResult.error || 'Failed to publish bundle',
            orchestrationStep: 'publish',
          },
          orchestrationMode: this.getMode(),
          success: false,
          error: publishResult.error || 'Failed to publish bundle',
        };
      }

      // Success - return completed campaign
      this.logger.log('Full orchestration completed successfully', {
        campaignId: campaign.id,
        bundleId,
        bundleUri,
      });

      return {
        campaign: {
          ...campaign,
          status: CampaignStatus.SUCCESS,
          bundleUri,
        },
        metadata: {
          ...metadata,
          bundleId,
          transactionId,
          orchestrationStep: 'completed',
          published: true,
        },
        orchestrationMode: this.getMode(),
        success: true,
      };
    } catch (error) {
      return this.handleUnexpectedError(context, error);
    }
  }

  getMode(): CampaignOrchestrationMode {
    return CampaignOrchestrationMode.FULL;
  }
}
