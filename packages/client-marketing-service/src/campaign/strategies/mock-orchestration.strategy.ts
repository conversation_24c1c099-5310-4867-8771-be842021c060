import { Injectable, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

import { CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationStrategy,
  CampaignOrchestrationContext,
  CampaignOrchestrationResult,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';

@Injectable()
export class MockOrchestrationStrategy
  implements CampaignOrchestrationStrategy
{
  private readonly logger = new Logger(MockOrchestrationStrategy.name);

  async execute(
    context: CampaignOrchestrationContext,
  ): Promise<CampaignOrchestrationResult> {
    const { campaign, metadata = {} } = context;

    this.logger.log('Executing mock orchestration for campaign', {
      campaignId: campaign.id,
      mode: this.getMode(),
    });

    // Simulate a small delay to mimic async operations
    await new Promise(resolve => setTimeout(resolve, 100));

    // Mock successful execution with generated test data
    const mockTransactionId = `mock-txn-${uuidv4()}`;
    const mockBundleId = `mock-bundle-${campaign.id}`;

    this.logger.log('Mock orchestration completed successfully', {
      campaignId: campaign.id,
      mockTransactionId,
      mockBundleId,
      note: 'All operations mocked - no external calls made',
    });

    return {
      campaign: {
        ...campaign,
        status: CampaignStatus.SUCCESS,
        bundleUri: mockBundleId,
      },
      metadata: {
        ...metadata,
        transactionId: mockTransactionId,
        bundleId: mockBundleId,
        orchestrationStep: 'completed',
        published: false,
        mocked: true,
        note: 'All operations mocked - no external calls made',
      },
      orchestrationMode: this.getMode(),
      success: true,
    };
  }

  getMode(): CampaignOrchestrationMode {
    return CampaignOrchestrationMode.MOCK;
  }
}
