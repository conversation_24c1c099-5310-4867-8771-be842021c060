import { Test, TestingModule } from '@nestjs/testing';

import { PaymentOnlyStrategy } from './payment-only.strategy';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationContext,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';
import { CampaignPaymentService } from '../payment/campaign-payment.service';

describe('PaymentOnlyStrategy', () => {
  let strategy: PaymentOnlyStrategy;
  let campaignPaymentService: jest.Mocked<CampaignPaymentService>;

  const mockCampaign: Campaign = {
    id: 'campaign-123',
    propertyId: 'property-123',
    priceCents: 10000,
    currency: 'USD',
    status: CampaignStatus.PENDING,
    bundleUri: null,
    sqsMessageId: null,
    retryCount: 0,
    createdBy: 'user-123',
    metadata: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockCampaignPaymentService = {
      processCampaignPayment: jest.fn(),
      createPaymentRetry: jest.fn(),
      createPaymentRefund: jest.fn(),
      getPaymentHistory: jest.fn(),
      hasSuccessfulPayment: jest.fn(),
      getLatestPayment: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentOnlyStrategy,
        {
          provide: CampaignPaymentService,
          useValue: mockCampaignPaymentService,
        },
      ],
    }).compile();

    strategy = module.get<PaymentOnlyStrategy>(PaymentOnlyStrategy);
    campaignPaymentService = module.get(CampaignPaymentService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return PAYMENT_ONLY mode', () => {
    expect(strategy.getMode()).toBe(CampaignOrchestrationMode.PAYMENT_ONLY);
  });

  describe('execute', () => {
    const context: CampaignOrchestrationContext = {
      campaign: mockCampaign,
      testCardNumber: '****************',
      metadata: { someKey: 'someValue' },
    };

    it('should successfully execute payment-only workflow', async () => {
      // Arrange
      // Validation is now handled internally by the base strategy
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.campaign.bundleUri).toBeNull(); // No bundle in payment-only mode
      expect(result.orchestrationMode).toBe(
        CampaignOrchestrationMode.PAYMENT_ONLY,
      );
      expect(result.metadata).toMatchObject({
        someKey: 'someValue',
        transactionId: 'txn-123',
        orchestrationStep: 'completed',
        published: false,
        note: 'Bundle generation and publishing skipped in payment-only mode',
      });

      // Validation is now handled internally by the base strategy
      expect(
        campaignPaymentService.processCampaignPayment,
      ).toHaveBeenCalledWith({
        campaign: mockCampaign,
      });
      // Bundle publishing is not part of payment-only strategy
    });

    it('should handle validation failure', async () => {
      // Arrange - create invalid campaign
      const invalidContext = {
        ...context,
        campaign: { ...mockCampaign, id: '', priceCents: 0 },
      };

      // Act
      const result = await strategy.execute(invalidContext);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Campaign validation failed');
      expect(result.metadata.orchestrationStep).toBe('validation');

      expect(
        campaignPaymentService.processCampaignPayment,
      ).not.toHaveBeenCalled();
      // Bundle publishing is not part of payment-only strategy
    });

    it('should handle payment failure', async () => {
      // Arrange
      // Validation is now handled internally by the base strategy
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: false,
        error: 'Insufficient funds',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe(
        'Payment processing failed: Insufficient funds',
      );
      expect(result.metadata.orchestrationStep).toBe('payment');
      expect(result.metadata.paymentError).toBe('Insufficient funds');

      // Bundle publishing is not part of payment-only strategy
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      const unexpectedError = new Error('Database connection lost');
      // Mock payment service to throw an error to simulate unexpected failure
      campaignPaymentService.processCampaignPayment.mockImplementation(() => {
        throw unexpectedError;
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe(
        'Orchestration failed: Database connection lost',
      );
      expect(result.metadata.orchestrationStep).toBe('error');
      expect(result.metadata.unexpectedError).toBe('Database connection lost');
    });
  });
});
