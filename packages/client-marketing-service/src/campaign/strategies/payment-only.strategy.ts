import { Injectable } from '@nestjs/common';

import { BaseOrchestrationStrategy } from './base-orchestration.strategy';
import { CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationContext,
  CampaignOrchestrationResult,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';
import { CampaignPaymentService } from '../payment/campaign-payment.service';

@Injectable()
export class PaymentOnlyStrategy extends BaseOrchestrationStrategy {
  constructor(campaignPaymentService: CampaignPaymentService) {
    super(campaignPaymentService);
  }

  async execute(
    context: CampaignOrchestrationContext,
  ): Promise<CampaignOrchestrationResult> {
    const { campaign, metadata = {} } = context;

    this.logger.log('Executing payment-only orchestration for campaign', {
      campaignId: campaign.id,
      mode: this.getMode(),
    });

    try {
      // Step 1: Validate campaign using shared logic
      const validationError = this.validateCampaign(context);
      if (validationError) {
        return validationError;
      }

      // Step 2: Process payment using shared logic
      const paymentResult = await this.processPayment(context);
      if (paymentResult.result) {
        return paymentResult.result;
      }
      const transactionId = paymentResult.transactionId;

      // Payment successful - skip bundle generation and publishing (Payment-only specific)
      this.logger.log('Payment-only orchestration completed successfully', {
        campaignId: campaign.id,
        transactionId,
        note: 'Bundle generation and publishing skipped in payment-only mode',
      });

      return {
        campaign: {
          ...campaign,
          status: CampaignStatus.SUCCESS,
          bundleUri: null, // No bundle generated in payment-only mode
        },
        metadata: {
          ...metadata,
          transactionId,
          orchestrationStep: 'completed',
          published: false,
          note: 'Bundle generation and publishing skipped in payment-only mode',
        },
        orchestrationMode: this.getMode(),
        success: true,
      };
    } catch (error) {
      return this.handleUnexpectedError(context, error);
    }
  }

  getMode(): CampaignOrchestrationMode {
    return CampaignOrchestrationMode.PAYMENT_ONLY;
  }
}
