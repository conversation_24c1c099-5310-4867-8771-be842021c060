import { Test, TestingModule } from '@nestjs/testing';

import { NotificationContext } from './notification-context.service';
import { NotificationStrategyFactory } from './notification-strategy.factory';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import {
  NotificationStrategy,
  NotificationWorkItem,
  NotificationResult,
} from '../interfaces/notification-strategy.interface';

describe('NotificationContext', () => {
  let context: NotificationContext;
  let factory: jest.Mocked<NotificationStrategyFactory>;
  let mockStrategies: jest.Mocked<NotificationStrategy>[];

  beforeEach(async () => {
    mockStrategies = [
      {
        notify: jest.fn(),
      } as unknown as jest.Mocked<NotificationStrategy>,
      {
        notify: jest.fn(),
      } as unknown as jest.Mocked<NotificationStrategy>,
    ];

    factory = {
      getStrategies: jest.fn().mockReturnValue(mockStrategies),
    } as unknown as jest.Mocked<NotificationStrategyFactory>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationContext,
        {
          provide: NotificationStrategyFactory,
          useValue: factory,
        },
      ],
    }).compile();

    context = module.get<NotificationContext>(NotificationContext);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('notifyAll', () => {
    const workItem: NotificationWorkItem = {
      campaignId: 'campaign-123',
      propertyId: 'property-456',
      bundleUri: 's3://bucket/bundle-123.json',
      createdAt: new Date('2024-01-01'),
      currency: 'USD',
      priceCents: 100000,
    };

    const campaign: Campaign = {
      id: 'campaign-123',
      propertyId: 'property-456',
      priceCents: 100000,
      currency: 'USD',
      status: CampaignStatus.PENDING,
      bundleUri: 's3://bucket/bundle-123.json',
      sqsMessageId: null,
      retryCount: 0,
      createdBy: 'user-123',
      metadata: null,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      payments: [],
    };

    it('should notify all strategies successfully', async () => {
      const successResult: NotificationResult = {
        success: true,
        messageId: 'test-123',
      };

      mockStrategies[0].notify.mockResolvedValue(successResult);
      mockStrategies[1].notify.mockResolvedValue(successResult);

      const results = await context.notifyAll(workItem, campaign);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
      expect(mockStrategies[0].notify).toHaveBeenCalledWith(workItem, campaign);
      expect(mockStrategies[1].notify).toHaveBeenCalledWith(workItem, campaign);
    });

    it('should handle mixed success and failure results', async () => {
      const successResult: NotificationResult = {
        success: true,
        messageId: 'test-123',
      };

      const failureResult: NotificationResult = {
        success: false,
        error: 'Failed to send',
      };

      mockStrategies[0].notify.mockResolvedValue(successResult);
      mockStrategies[1].notify.mockResolvedValue(failureResult);

      const results = await context.notifyAll(workItem, campaign);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toBe('Failed to send');
    });

    it('should handle strategy exceptions gracefully', async () => {
      const successResult: NotificationResult = {
        success: true,
        messageId: 'test-123',
      };

      mockStrategies[0].notify.mockResolvedValue(successResult);
      mockStrategies[1].notify.mockRejectedValue(
        new Error('Strategy exception'),
      );

      const results = await context.notifyAll(workItem, campaign);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(results[1].error).toBe('Strategy exception');
    });

    it('should work with a single strategy', async () => {
      const singleStrategy = [
        {
          notify: jest
            .fn()
            .mockResolvedValue({ success: true, messageId: 'single-123' }),
        } as unknown as jest.Mocked<NotificationStrategy>,
      ];

      factory.getStrategies.mockReturnValue(singleStrategy);

      const singleContext = new NotificationContext(factory);
      const results = await singleContext.notifyAll(workItem, campaign);

      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
      expect(results[0].messageId).toBe('single-123');
    });

    it('should handle all strategies failing', async () => {
      const failureResult: NotificationResult = {
        success: false,
        error: 'Failed to send',
      };

      mockStrategies[0].notify.mockResolvedValue(failureResult);
      mockStrategies[1].notify.mockRejectedValue(new Error('Exception'));

      const results = await context.notifyAll(workItem, campaign);

      expect(results).toHaveLength(2);
      expect(results.every(r => !r.success)).toBe(true);
    });
  });
});
