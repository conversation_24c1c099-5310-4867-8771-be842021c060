import { Injectable, Logger } from '@nestjs/common';

import { NotificationStrategyFactory } from './notification-strategy.factory';
import { Campaign } from '../entities/campaign.entity';
import {
  NotificationStrategy,
  NotificationWorkItem,
  NotificationResult,
} from '../interfaces/notification-strategy.interface';

@Injectable()
export class NotificationContext {
  private readonly logger = new Logger(NotificationContext.name);
  private readonly strategies: NotificationStrategy[];

  constructor(private readonly strategyFactory: NotificationStrategyFactory) {
    this.strategies = this.strategyFactory.getStrategies();
  }

  async notifyAll(
    workItem: NotificationWorkItem,
    campaign: Campaign,
  ): Promise<NotificationResult[]> {
    const results = await Promise.allSettled(
      this.strategies.map(strategy => strategy.notify(workItem, campaign)),
    );

    const notificationResults: NotificationResult[] = [];

    results.forEach((result, index) => {
      const strategyName = this.strategies[index].constructor.name;

      if (result.status === 'fulfilled') {
        notificationResults.push(result.value);
        if (result.value.success) {
          this.logger.log(
            `${strategyName} notification sent successfully for campaign ${campaign.id}`,
          );
        } else {
          this.logger.warn(
            `${strategyName} notification failed for campaign ${campaign.id}: ${result.value.error}`,
          );
        }
      } else {
        this.logger.error(
          `${strategyName} notification threw error for campaign ${campaign.id}:`,
          result.reason,
        );
        notificationResults.push({
          success: false,
          error: result.reason?.message || 'Unknown error',
        });
      }
    });

    return notificationResults;
  }
}
