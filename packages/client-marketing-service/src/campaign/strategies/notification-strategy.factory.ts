import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { LinearNotificationStrategy } from './linear-notification.strategy';
import { SNSNotificationStrategy } from './sns-notification.strategy';
import { NotificationStrategy } from '../interfaces/notification-strategy.interface';

export enum NotificationStrategyType {
  SQS = 'sqs',
  LINEAR = 'linear',
  BOTH = 'both',
}

@Injectable()
export class NotificationStrategyFactory {
  private readonly logger = new Logger(NotificationStrategyFactory.name);
  private readonly strategyType: NotificationStrategyType;

  constructor(
    private readonly configService: ConfigService,
    private readonly snsStrategy: SNSNotificationStrategy,
    private readonly linearStrategy: LinearNotificationStrategy,
  ) {
    const strategyString = (
      this.configService.get<string>('campaign.notificationStrategy') ||
      'linear'
    ).toLowerCase();

    // Validate the notification strategy and default to LINEAR if invalid
    if (
      !Object.values(NotificationStrategyType).includes(
        strategyString as NotificationStrategyType,
      )
    ) {
      const validStrategies = Object.values(NotificationStrategyType).join(
        ', ',
      );
      this.logger.error(
        `Invalid notification strategy: '${strategyString}'. Valid options are: ${validStrategies}. Defaulting to LINEAR.`,
      );
      this.strategyType = NotificationStrategyType.LINEAR;
    } else {
      this.strategyType = strategyString as NotificationStrategyType;
    }

    this.logger.log(`Notification strategy configured: ${this.strategyType}`);
  }

  getStrategies(): NotificationStrategy[] {
    const strategies: NotificationStrategy[] = [];

    switch (this.strategyType) {
      case NotificationStrategyType.LINEAR:
        strategies.push(this.linearStrategy);
        break;
      case NotificationStrategyType.BOTH:
        strategies.push(this.snsStrategy);
        strategies.push(this.linearStrategy);
        break;
      case NotificationStrategyType.SQS:
      default:
        // SQS strategy uses SNS for notifications
        strategies.push(this.snsStrategy);
        break;
    }

    return strategies;
  }
}
