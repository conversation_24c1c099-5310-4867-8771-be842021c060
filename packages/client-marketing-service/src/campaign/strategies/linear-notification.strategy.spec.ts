import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';

import { LinearNotificationStrategy } from './linear-notification.strategy';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import { NotificationWorkItem } from '../interfaces/notification-strategy.interface';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('LinearNotificationStrategy', () => {
  let strategy: LinearNotificationStrategy;
  let configService: jest.Mocked<ConfigService>;
  let mockPost: jest.Mock;

  beforeEach(async () => {
    // Set up mock before module creation
    mockPost = jest.fn();
    mockedAxios.create.mockReturnValue({
      post: mockPost,
      interceptors: {
        request: {
          use: jest.fn(),
        },
        response: {
          use: jest.fn(),
        },
      },
    } as any);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LinearNotificationStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                'linear.apiKey': 'test-api-key',
                'linear.teamId': 'test-team-id',
                'linear.projectId': 'test-project-id',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<LinearNotificationStrategy>(
      LinearNotificationStrategy,
    );
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('notify', () => {
    const workItem: NotificationWorkItem = {
      campaignId: 'campaign-123',
      propertyId: 'property-456',
      bundleUri: 's3://bucket/bundle-123.json',
      createdAt: new Date('2024-01-01'),
      currency: 'USD',
      priceCents: 100000,
    };

    const campaign: Campaign = {
      id: 'campaign-123',
      propertyId: 'property-456',
      priceCents: 100000,
      currency: 'USD',
      status: CampaignStatus.PENDING,
      bundleUri: 's3://bucket/bundle-123.json',
      sqsMessageId: null,
      retryCount: 0,
      createdBy: 'user-123',
      metadata: null,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      payments: [],
    };

    it('should successfully create a Linear ticket', async () => {
      mockPost.mockResolvedValue({
        data: {
          data: {
            issueCreate: {
              success: true,
              issue: {
                id: 'issue-123',
                identifier: 'PAID-123',
                title: 'Campaign Ready for Execution: campaign-123',
                url: 'https://linear.app/team/issue/PAID-123',
              },
            },
          },
        },
      });

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('issue-123');
      expect(mockPost).toHaveBeenCalledWith('/graphql', expect.any(Object));
    });

    it('should handle Linear API errors', async () => {
      mockPost.mockResolvedValue({
        data: {
          errors: [{ message: 'Invalid API key' }],
        },
      });

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Linear API errors');
    });

    it('should handle network errors gracefully', async () => {
      mockPost.mockRejectedValue(new Error('Network error'));

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
    });

    it('should skip notification when Linear is not configured', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'linear.apiKey') return '';
          if (key === 'linear.teamId') return '';
          if (key === 'linear.projectId') return '';
          return defaultValue;
        },
      );

      const unconfiguredStrategy = new LinearNotificationStrategy(
        configService,
      );
      const result = await unconfiguredStrategy.notify(workItem, campaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('linear-skipped');
    });
  });
});
