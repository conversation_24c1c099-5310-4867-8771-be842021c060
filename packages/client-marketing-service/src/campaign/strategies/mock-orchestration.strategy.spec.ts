import { Test, TestingModule } from '@nestjs/testing';

import { MockOrchestrationStrategy } from './mock-orchestration.strategy';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationContext,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';

describe('MockOrchestrationStrategy', () => {
  let strategy: MockOrchestrationStrategy;

  const mockCampaign: Campaign = {
    id: 'campaign-123',
    propertyId: 'property-123',
    priceCents: 10000,
    currency: 'USD',
    status: CampaignStatus.PENDING,
    bundleUri: null,
    sqsMessageId: null,
    retryCount: 0,
    createdBy: 'user-123',
    metadata: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MockOrchestrationStrategy],
    }).compile();

    strategy = module.get<MockOrchestrationStrategy>(MockOrchestrationStrategy);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return MOCK mode', () => {
    expect(strategy.getMode()).toBe(CampaignOrchestrationMode.MOCK);
  });

  describe('execute', () => {
    const context: CampaignOrchestrationContext = {
      campaign: mockCampaign,
      metadata: { someKey: 'someValue' },
    };

    it('should successfully execute mock orchestration workflow', async () => {
      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.campaign.bundleUri).toMatch(/^mock-bundle-campaign-123$/);
      expect(result.orchestrationMode).toBe(CampaignOrchestrationMode.MOCK);
      expect(result.metadata).toMatchObject({
        someKey: 'someValue',
        bundleId: 'mock-bundle-campaign-123',
        orchestrationStep: 'completed',
        published: false,
        mocked: true,
        note: 'All operations mocked - no external calls made',
      });
      // Check that transactionId is generated
      expect(result.metadata.transactionId).toMatch(/^mock-txn-/);
    });

    it('should always return success for different campaigns', async () => {
      // Arrange
      const differentCampaign = {
        ...mockCampaign,
        id: 'campaign-456',
      };
      const differentContext = {
        ...context,
        campaign: differentCampaign,
      };

      // Act
      const result = await strategy.execute(differentContext);

      // Assert
      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.campaign.bundleUri).toBe('mock-bundle-campaign-456');
    });

    it('should include metadata from context in response', async () => {
      // Arrange
      const contextWithMetadata = {
        ...context,
        metadata: {
          key1: 'value1',
          key2: 'value2',
          nestedData: { nested: true },
        },
      };

      // Act
      const result = await strategy.execute(contextWithMetadata);

      // Assert
      expect(result.metadata).toMatchObject({
        key1: 'value1',
        key2: 'value2',
        nestedData: { nested: true },
        mocked: true,
        published: false,
      });
    });

    it('should complete quickly simulating async operation', async () => {
      // Act
      const startTime = Date.now();
      await strategy.execute(context);
      const endTime = Date.now();
      const elapsedTime = endTime - startTime;

      // Assert - should complete in roughly 100ms with tolerance for CI timing variations
      expect(elapsedTime).toBeGreaterThanOrEqual(95); // Allow 5ms tolerance for CI timing
      expect(elapsedTime).toBeLessThan(200); // Allow for some overhead
    });

    it('should not require testCardNumber in context', async () => {
      // Arrange
      const contextWithoutCard = {
        campaign: mockCampaign,
        metadata: {},
      };

      // Act
      const result = await strategy.execute(contextWithoutCard);

      // Assert
      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
    });

    it('should work with empty metadata', async () => {
      // Arrange
      const contextWithEmptyMetadata = {
        campaign: mockCampaign,
      };

      // Act
      const result = await strategy.execute(contextWithEmptyMetadata);

      // Assert
      expect(result.success).toBe(true);
      expect(result.metadata).toMatchObject({
        mocked: true,
        published: false,
        note: 'All operations mocked - no external calls made',
      });
    });
  });
});
