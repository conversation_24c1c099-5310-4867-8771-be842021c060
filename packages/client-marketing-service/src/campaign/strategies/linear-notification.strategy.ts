import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';

import { Campaign } from '../entities/campaign.entity';
import {
  NotificationStrategy,
  NotificationWorkItem,
  NotificationResult,
} from '../interfaces/notification-strategy.interface';

// Linear API type definitions
interface LinearTicketInput {
  title: string;
  description: string;
  teamId: string;
  projectId?: string;
  priority?: number;
}

interface LinearIssue {
  id: string;
  identifier: string;
  title: string;
  url: string;
}

interface LinearIssueCreateResult {
  success: boolean;
  issue?: LinearIssue;
}

interface LinearGraphQLResponse {
  data?: {
    issueCreate?: LinearIssueCreateResult;
  };
  errors?: Array<{
    message: string;
    extensions?: Record<string, unknown>;
  }>;
}

interface LinearTicketResult {
  success: boolean;
  issueId?: string;
  identifier?: string;
  url?: string;
}

@Injectable()
export class LinearNotificationStrategy implements NotificationStrategy {
  private readonly logger = new Logger(LinearNotificationStrategy.name);
  private readonly linearClient: AxiosInstance;
  private readonly teamId: string;
  private readonly projectId: string;
  private readonly enabled: boolean;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('linear.apiKey', '');
    this.teamId = this.configService.get<string>('linear.teamId', '');
    this.projectId = this.configService.get<string>('linear.projectId', '');
    this.enabled = !!apiKey && !!this.teamId;

    this.linearClient = axios.create({
      baseURL: 'https://api.linear.app',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000,
    });

    // Add request interceptor to attach Authorization header (Linear expects API key directly, not Bearer token)
    this.linearClient.interceptors.request.use(
      config => {
        const token = this.configService.get<string>('linear.apiKey', '');
        if (token) {
          config.headers.Authorization = token;
        }
        return config;
      },
      error => {
        return Promise.reject(error as Error);
      },
    );

    // Add response interceptor to redact Authorization header in logs
    this.linearClient.interceptors.response.use(
      response => {
        // Redact Authorization header if present in logged config
        if (response.config?.headers?.Authorization) {
          response.config.headers.Authorization = '[REDACTED]';
        }
        return response;
      },
      error => {
        // Redact Authorization header in error config as well
        if (error.config?.headers?.Authorization) {
          error.config.headers.Authorization = '[REDACTED]';
        }
        return Promise.reject(error as Error);
      },
    );
  }

  async notify(
    workItem: NotificationWorkItem,
    campaign: Campaign,
  ): Promise<NotificationResult> {
    try {
      if (!this.enabled) {
        this.logger.warn('Linear API not configured, skipping notification');
        return {
          success: true,
          messageId: 'linear-skipped',
        };
      }

      const ticketData = this.buildTicketData(workItem, campaign);
      const response = await this.createLinearTicket(ticketData);

      this.logger.log(
        `Linear ticket created successfully for campaign ${campaign.id}: ${response.issueId}`,
      );

      return {
        success: true,
        messageId: response.issueId,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create Linear ticket for campaign ${campaign.id}:`,
        error,
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private buildTicketData(
    workItem: NotificationWorkItem,
    campaign: Campaign,
  ): LinearTicketInput {
    const title = `Campaign Ready for Execution: ${campaign.id}`;

    const description = `# Campaign Ready for Execution: ${campaign.id}

## Campaign Details
- **Campaign ID**: ${campaign.id}
- **Property ID**: ${workItem.propertyId}
- **Budget**: $${(workItem.priceCents / 100).toFixed(2)} ${workItem.currency}
- **Created**: ${workItem.createdAt.toISOString()}

## Bundle Information
- **S3 Bundle URL**: ${workItem.bundleUri || 'Not available'}
- **Bundle Generated**: ${new Date().toISOString()}

## Action Required
Please review and execute this campaign according to the specifications in the bundle.

## Source
This ticket was automatically created by the Campaign Handoff Service.`;

    // Set priority based on campaign value
    // Priority levels: 1 = Urgent, 2 = High, 3 = Normal, 4 = Low
    const priority = workItem.priceCents >= 500000 ? 1 : 3; // $5000+ = Urgent, else Normal

    return {
      title,
      description,
      teamId: this.teamId,
      projectId: this.projectId || undefined,
      priority,
    };
  }

  private async createLinearTicket(
    ticketData: LinearTicketInput,
  ): Promise<LinearTicketResult> {
    const query = `
      mutation IssueCreate($input: IssueCreateInput!) {
        issueCreate(input: $input) {
          success
          issue {
            id
            identifier
            title
            url
          }
        }
      }
    `;

    const variables = {
      input: {
        title: ticketData.title,
        description: ticketData.description,
        teamId: ticketData.teamId,
        projectId: ticketData.projectId,
        priority: ticketData.priority,
      },
    };

    let response: any;
    try {
      response = await this.linearClient.post<LinearGraphQLResponse>(
        '/graphql',
        {
          query,
          variables,
        },
      );
    } catch (error) {
      this.logger.error('Linear API request failed:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        errors: error.response?.data?.errors,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers,
        },
      });
      throw error;
    }

    if (response.data.errors) {
      throw new Error(
        `Linear API errors: ${JSON.stringify(response.data.errors)}`,
      );
    }

    if (!response.data.data?.issueCreate?.success) {
      throw new Error('Failed to create Linear issue');
    }

    const issue = response.data.data.issueCreate.issue;
    if (!issue) {
      throw new Error('Linear issue created but no issue data returned');
    }

    return {
      success: true,
      issueId: issue.id,
      identifier: issue.identifier,
      url: issue.url,
    };
  }
}
