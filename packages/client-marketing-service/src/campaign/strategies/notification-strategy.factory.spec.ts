import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import { LinearNotificationStrategy } from './linear-notification.strategy';
import { NotificationStrategyFactory } from './notification-strategy.factory';
import { SNSNotificationStrategy } from './sns-notification.strategy';

jest.mock('@aws-sdk/client-sns');
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: {
        use: jest.fn(),
      },
      response: {
        use: jest.fn(),
      },
    },
    post: jest.fn(),
  })),
}));

describe('NotificationStrategyFactory', () => {
  let factory: NotificationStrategyFactory;
  let configService: jest.Mocked<ConfigService>;
  let snsStrategy: SNSNotificationStrategy;
  let linearStrategy: LinearNotificationStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationStrategyFactory,
        SNSNotificationStrategy,
        LinearNotificationStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config = {
                'campaign.notificationStrategy': 'sqs',
                'campaign.snsTopicArn':
                  'arn:aws:sns:us-east-1:123456789012:test-topic',
                'linear.apiKey': 'test-api-key',
                'linear.teamId': 'test-team-id',
                'linear.projectId': 'test-project-id',
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    factory = module.get<NotificationStrategyFactory>(
      NotificationStrategyFactory,
    );
    configService = module.get(ConfigService);
    snsStrategy = module.get<SNSNotificationStrategy>(SNSNotificationStrategy);
    linearStrategy = module.get<LinearNotificationStrategy>(
      LinearNotificationStrategy,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getStrategies', () => {
    it('should return SNS strategy when configured for SQS (SQS uses SNS for notifications)', () => {
      const strategies = factory.getStrategies();
      expect(strategies).toHaveLength(1);
      expect(strategies[0]).toBe(snsStrategy);
    });

    it('should return only Linear strategy when configured for Linear', () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'campaign.notificationStrategy') return 'linear';
          return defaultValue;
        },
      );

      const linearFactory = new NotificationStrategyFactory(
        configService,
        snsStrategy,
        linearStrategy,
      );

      const strategies = linearFactory.getStrategies();
      expect(strategies).toHaveLength(1);
      expect(strategies[0]).toBe(linearStrategy);
    });

    it('should return both strategies when configured for both', () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'campaign.notificationStrategy') return 'both';
          return defaultValue;
        },
      );

      const bothFactory = new NotificationStrategyFactory(
        configService,
        snsStrategy,
        linearStrategy,
      );

      const strategies = bothFactory.getStrategies();
      expect(strategies).toHaveLength(2);
      expect(strategies[0]).toBe(snsStrategy);
      expect(strategies[1]).toBe(linearStrategy);
    });

    it('should default to LINEAR strategy when configuration is invalid', () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'campaign.notificationStrategy') return 'invalid';
          return defaultValue;
        },
      );

      const invalidFactory = new NotificationStrategyFactory(
        configService,
        snsStrategy,
        linearStrategy,
      );

      const strategies = invalidFactory.getStrategies();
      expect(strategies).toHaveLength(1);
      expect(strategies[0]).toBe(linearStrategy); // LINEAR is the default strategy
    });

    it('should handle uppercase strategy configuration', () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'campaign.notificationStrategy') return 'LINEAR';
          return defaultValue;
        },
      );

      const upperFactory = new NotificationStrategyFactory(
        configService,
        snsStrategy,
        linearStrategy,
      );

      const strategies = upperFactory.getStrategies();
      expect(strategies).toHaveLength(1);
      expect(strategies[0]).toBe(linearStrategy);
    });
  });
});
