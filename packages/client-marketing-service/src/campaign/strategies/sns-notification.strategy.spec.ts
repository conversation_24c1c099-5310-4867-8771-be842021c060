import { SNSClient } from '@aws-sdk/client-sns';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import { SNSNotificationStrategy } from './sns-notification.strategy';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import { NotificationWorkItem } from '../interfaces/notification-strategy.interface';

jest.mock('@aws-sdk/client-sns');

describe('SNSNotificationStrategy', () => {
  let strategy: SNSNotificationStrategy;
  let configService: jest.Mocked<ConfigService>;
  let mockSNSSend: jest.Mock;

  beforeEach(async () => {
    mockSNSSend = jest.fn();

    const mockSNSClient = {
      send: mockSNSSend,
    };

    (SNSClient as jest.MockedClass<typeof SNSClient>).mockImplementation(
      () => mockSNSClient as any,
    );

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SNSNotificationStrategy,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              if (key === 'campaign.snsTopicArn')
                return 'arn:aws:sns:us-east-1:123456789012:test-topic';
              return defaultValue;
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<SNSNotificationStrategy>(SNSNotificationStrategy);
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('notify', () => {
    const workItem: NotificationWorkItem = {
      campaignId: 'campaign-123',
      propertyId: 'property-456',
      bundleUri: 's3://bucket/bundle-123.json',
      createdAt: new Date('2024-01-01'),
      currency: 'USD',
      priceCents: 100000,
    };

    const campaign: Campaign = {
      id: 'campaign-123',
      propertyId: 'property-456',
      priceCents: 100000,
      currency: 'USD',
      status: CampaignStatus.PENDING,
      bundleUri: 's3://bucket/bundle-123.json',
      sqsMessageId: null,
      retryCount: 0,
      createdBy: 'user-123',
      metadata: null,
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      payments: [],
    };

    it('should successfully send SNS notification', async () => {
      mockSNSSend.mockResolvedValue({
        MessageId: 'sns-message-123',
      });

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('sns-message-123');
      expect(mockSNSSend).toHaveBeenCalledTimes(1);
      expect(mockSNSSend).toHaveBeenCalledWith(expect.any(Object));
    });

    it('should handle SNS errors gracefully', async () => {
      mockSNSSend.mockRejectedValue(new Error('SNS service unavailable'));

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(false);
      expect(result.error).toBe('SNS service unavailable');
    });

    it('should skip notification when SNS topic is not configured', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'campaign.snsTopicArn') return '';
          return defaultValue;
        },
      );

      const unconfiguredStrategy = new SNSNotificationStrategy(configService);
      const result = await unconfiguredStrategy.notify(workItem, campaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('sns-skipped');
      expect(mockSNSSend).not.toHaveBeenCalled();
    });

    it('should use fallback message ID when SNS response lacks MessageId', async () => {
      mockSNSSend.mockResolvedValue({});

      const result = await strategy.notify(workItem, campaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toMatch(/^sns-\d+$/);
    });
  });
});
