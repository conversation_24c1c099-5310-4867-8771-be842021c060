import { Test, TestingModule } from '@nestjs/testing';

import { FullOrchestrationStrategy } from './full-orchestration.strategy';
import { BundleLifecycleService } from '../bundle/bundle-lifecycle.service';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import {
  CampaignOrchestrationContext,
  CampaignOrchestrationMode,
} from '../interfaces/campaign-orchestration.interface';
import { CampaignPaymentService } from '../payment/campaign-payment.service';

describe('FullOrchestrationStrategy', () => {
  let strategy: FullOrchestrationStrategy;
  let campaignPaymentService: jest.Mocked<CampaignPaymentService>;
  let bundleLifecycleService: jest.Mocked<BundleLifecycleService>;

  const mockCampaign: Campaign = {
    id: 'campaign-123',
    propertyId: 'property-123',
    priceCents: 10000,
    currency: 'USD',
    status: CampaignStatus.PENDING,
    bundleUri: null,
    sqsMessageId: null,
    retryCount: 0,
    createdBy: 'user-123',
    metadata: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockCampaignPaymentService = {
      processCampaignPayment: jest.fn(),
    };

    const mockBundleLifecycleService = {
      generatePromoBundle: jest.fn(),
      uploadBundle: jest.fn(),
      publishBundle: jest.fn(),
      executeFullLifecycle: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FullOrchestrationStrategy,
        {
          provide: CampaignPaymentService,
          useValue: mockCampaignPaymentService,
        },
        {
          provide: BundleLifecycleService,
          useValue: mockBundleLifecycleService,
        },
      ],
    }).compile();

    strategy = module.get<FullOrchestrationStrategy>(FullOrchestrationStrategy);
    campaignPaymentService = module.get(CampaignPaymentService);
    bundleLifecycleService = module.get(BundleLifecycleService);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  it('should return FULL mode', () => {
    expect(strategy.getMode()).toBe(CampaignOrchestrationMode.FULL);
  });

  describe('execute', () => {
    const context: CampaignOrchestrationContext = {
      campaign: mockCampaign,
      testCardNumber: '****************',
      metadata: { someKey: 'someValue' },
    };

    it('should successfully execute full orchestration workflow', async () => {
      // Arrange
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });
      bundleLifecycleService.generatePromoBundle.mockResolvedValue({
        success: true,
        data: { bundleId: 'bundle-123' },
      });
      bundleLifecycleService.uploadBundle.mockResolvedValue({
        success: true,
        data: { bundleUri: 's3://my-bucket/bundle-123.json' },
      });
      bundleLifecycleService.publishBundle.mockResolvedValue({
        success: true,
        messageId: 'msg-123',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.campaign.bundleUri).toBe('s3://my-bucket/bundle-123.json');
      expect(result.orchestrationMode).toBe(CampaignOrchestrationMode.FULL);
      expect(result.metadata).toMatchObject({
        someKey: 'someValue',
        bundleId: 'bundle-123',
        transactionId: 'txn-123',
        orchestrationStep: 'completed',
        published: true,
      });

      expect(
        campaignPaymentService.processCampaignPayment,
      ).toHaveBeenCalledWith({
        campaign: mockCampaign,
      });
      expect(bundleLifecycleService.generatePromoBundle).toHaveBeenCalledWith(
        mockCampaign,
      );
      expect(bundleLifecycleService.uploadBundle).toHaveBeenCalledWith(
        mockCampaign,
      );
      expect(bundleLifecycleService.publishBundle).toHaveBeenCalledWith(
        mockCampaign,
      );
    });

    it('should handle validation failure', async () => {
      // Arrange - create invalid campaign (no id)
      const invalidContext = {
        ...context,
        campaign: { ...mockCampaign, id: '', priceCents: 0 },
      };

      // Act
      const result = await strategy.execute(invalidContext);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Campaign validation failed');
      expect(result.metadata.orchestrationStep).toBe('validation');

      expect(
        campaignPaymentService.processCampaignPayment,
      ).not.toHaveBeenCalled();
      expect(bundleLifecycleService.generatePromoBundle).not.toHaveBeenCalled();
      expect(bundleLifecycleService.publishBundle).not.toHaveBeenCalled();
    });

    it('should handle payment failure', async () => {
      // Arrange
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: false,
        error: 'Card declined',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Payment processing failed: Card declined');
      expect(result.metadata.orchestrationStep).toBe('payment');
      expect(result.metadata.paymentError).toBe('Card declined');

      expect(bundleLifecycleService.generatePromoBundle).not.toHaveBeenCalled();
      expect(bundleLifecycleService.publishBundle).not.toHaveBeenCalled();
    });

    it('should handle bundle generation failure', async () => {
      // Arrange
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });
      bundleLifecycleService.generatePromoBundle.mockResolvedValue({
        success: false,
        error: 'Bundle generation failed',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Bundle generation failed');
      expect(result.metadata.orchestrationStep).toBe('bundle_generation');
      expect(result.metadata.bundleGenerationError).toBe(
        'Bundle generation failed',
      );

      expect(bundleLifecycleService.uploadBundle).not.toHaveBeenCalled();
      expect(bundleLifecycleService.publishBundle).not.toHaveBeenCalled();
    });

    it('should handle bundle upload failure', async () => {
      // Arrange
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });
      bundleLifecycleService.generatePromoBundle.mockResolvedValue({
        success: true,
        data: { bundleId: 'bundle-123' },
      });
      bundleLifecycleService.uploadBundle.mockResolvedValue({
        success: false,
        error: 'Failed to upload bundle',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Failed to upload bundle');
      expect(result.metadata.orchestrationStep).toBe('upload');
      expect(result.metadata.bundleId).toBe('bundle-123');
      expect(result.metadata.uploadError).toBe('Failed to upload bundle');

      expect(bundleLifecycleService.publishBundle).not.toHaveBeenCalled();
    });

    it('should handle bundle publishing failure', async () => {
      // Arrange
      campaignPaymentService.processCampaignPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });
      bundleLifecycleService.generatePromoBundle.mockResolvedValue({
        success: true,
        data: { bundleId: 'bundle-123' },
      });
      bundleLifecycleService.uploadBundle.mockResolvedValue({
        success: true,
        data: { bundleUri: 's3://my-bucket/bundle-123.json' },
      });
      bundleLifecycleService.publishBundle.mockResolvedValue({
        success: false,
        error: 'Failed to publish bundle',
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Failed to publish bundle');
      expect(result.metadata.orchestrationStep).toBe('publish');
      expect(result.metadata.bundleId).toBe('bundle-123');
      expect(result.metadata.publishError).toBe('Failed to publish bundle');
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      const unexpectedError = new Error('Unexpected error');
      // Mock payment service to throw an error to simulate unexpected failure
      campaignPaymentService.processCampaignPayment.mockImplementation(() => {
        throw unexpectedError;
      });

      // Act
      const result = await strategy.execute(context);

      // Assert
      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toBe('Orchestration failed: Unexpected error');
      expect(result.metadata.orchestrationStep).toBe('error');
      expect(result.metadata.unexpectedError).toBe('Unexpected error');
    });
  });
});
