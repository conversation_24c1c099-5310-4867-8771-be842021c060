import { Campaign } from '../entities/campaign.entity';

export interface NotificationWorkItem {
  campaignId: string;
  propertyId: string;
  bundleUri: string | null;
  createdAt: Date;
  currency: string;
  priceCents: number;
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface NotificationStrategy {
  notify(
    workItem: NotificationWorkItem,
    campaign: Campaign,
  ): Promise<NotificationResult>;
}
