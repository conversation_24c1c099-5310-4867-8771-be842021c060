import { Campaign } from '../entities/campaign.entity';

export interface CampaignOrchestrationContext {
  campaign: Campaign;
  testCardNumber?: string;
  metadata?: Record<string, any>;
}

export interface CampaignOrchestrationResult {
  campaign: Campaign;
  metadata: Record<string, any>;
  orchestrationMode: CampaignOrchestrationMode;
  success: boolean;
  error?: string;
}

export enum CampaignOrchestrationMode {
  FULL = 'full',
  PAYMENT_ONLY = 'payment-only',
  MOCK = 'mock',
}

export interface CampaignOrchestrationStrategy {
  execute(
    context: CampaignOrchestrationContext,
  ): Promise<CampaignOrchestrationResult>;
  getMode(): CampaignOrchestrationMode;
}
