import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { BundleLifecycleService } from './bundle-lifecycle.service';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import { NotificationContext } from '../strategies/notification-context.service';

describe('BundleLifecycleService', () => {
  let service: BundleLifecycleService;
  let campaignRepository: jest.Mocked<Repository<Campaign>>;
  let configService: jest.Mocked<ConfigService>;

  const mockCampaign: Campaign = {
    id: 'test-campaign-id',
    propertyId: 'test-property-id',
    priceCents: 10000,
    currency: 'USD',
    status: CampaignStatus.PENDING,
    bundleUri: null,
    sqsMessageId: null,
    retryCount: 0,
    createdBy: 'test-user',
    metadata: { testKey: 'testValue' },
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const mockManager = {
      update: jest.fn(),
      transaction: jest.fn(callback => callback(mockManager)),
    };

    const mockRepository = {
      manager: mockManager,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BundleLifecycleService,
        {
          provide: getRepositoryToken(Campaign),
          useValue: mockRepository,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: any) => {
              const config: Record<string, any> = {
                'campaign.promoBucket': 'test-bucket',
                'campaign.sqsUrl': 'https://sqs.test.com/queue',
                'campaign.notificationStrategy': 'sqs',
                'aws.region': 'us-east-1',
                nodeEnv: 'test',
              };
              return config[key] ?? defaultValue;
            }),
          },
        },
        {
          provide: NotificationContext,
          useValue: {
            notifyAll: jest.fn().mockResolvedValue([]),
          },
        },
      ],
    }).compile();

    service = module.get<BundleLifecycleService>(BundleLifecycleService);
    campaignRepository = module.get(getRepositoryToken(Campaign));
    configService = module.get(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('generatePromoBundle', () => {
    it('should generate a bundle successfully', async () => {
      const result = await service.generatePromoBundle(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.message).toContain('Bundle generated successfully');
      expect(result.data).toEqual({
        bundleId: `bundle-${mockCampaign.id}`,
        content: {
          campaignId: mockCampaign.id,
          timestamp: expect.any(String),
          propertyId: mockCampaign.propertyId,
        },
      });
    });

    it('should include metadata when requested', async () => {
      const result = await service.generatePromoBundle(mockCampaign, {
        includeMetadata: true,
      });

      expect(result.success).toBe(true);
      expect(result.data.content.metadata).toEqual(mockCampaign.metadata);
    });

    it('should validate bundle content when requested', async () => {
      const invalidCampaign = { ...mockCampaign, propertyId: null };
      const result = await service.generatePromoBundle(invalidCampaign as any, {
        validate: true,
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid bundle content');
    });
  });

  describe('uploadBundle', () => {
    it('should upload bundle to S3 in production', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'nodeEnv') return 'production';
          if (key === 'campaign.promoBucket') return 'test-bucket';
          return defaultValue;
        },
      );

      // Mock the private S3Client send method through service internals
      const s3SendSpy = jest
        .spyOn(service['s3Client'] as any, 'send')
        .mockResolvedValue({} as any);

      const result = await service.uploadBundle(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.message).toContain('Bundle uploaded successfully');
      expect(s3SendSpy).toHaveBeenCalled();
      expect(campaignRepository.manager.update).toHaveBeenCalledWith(
        Campaign,
        { id: mockCampaign.id },
        { bundleUri: expect.stringContaining('s3://test-bucket/') },
      );
    });

    it('should skip S3 upload in development', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'nodeEnv') return 'development';
          if (key === 'campaign.promoBucket') return 'test-bucket';
          return defaultValue;
        },
      );

      const s3SendSpy = jest.spyOn(service['s3Client'] as any, 'send');

      const result = await service.uploadBundle(mockCampaign);

      expect(result.success).toBe(true);
      expect(s3SendSpy).not.toHaveBeenCalled();
      expect(result.data.bundleUri).toContain('s3://test-bucket/');
    });

    it('should handle upload errors', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'nodeEnv') return 'production';
          if (key === 'campaign.promoBucket') return 'test-bucket';
          return defaultValue;
        },
      );

      jest
        .spyOn(service['s3Client'] as any, 'send')
        .mockRejectedValue(new Error('S3 error'));

      const result = await service.uploadBundle(mockCampaign);

      expect(result.success).toBe(false);
      expect(result.error).toContain('S3 error');
    });
  });

  describe('publishBundle', () => {
    it('should skip publishing if campaign is already successful', async () => {
      const successCampaign = {
        ...mockCampaign,
        status: CampaignStatus.SUCCESS,
      };
      const sqsSendSpy = jest.spyOn(service['sqsClient'] as any, 'send');

      const result = await service.publishBundle(successCampaign);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Campaign already published');
      expect(sqsSendSpy).not.toHaveBeenCalled();
    });

    it('should publish to SQS when strategy is sqs', async () => {
      const sqsSendSpy = jest
        .spyOn(service['sqsClient'] as any, 'send')
        .mockResolvedValue({ MessageId: 'msg-123' } as any);

      const result = await service.publishBundle(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.messageId).toBe('msg-123');
      expect(sqsSendSpy).toHaveBeenCalled();
      expect(campaignRepository.manager.update).toHaveBeenCalledWith(
        Campaign,
        { id: mockCampaign.id },
        {
          status: CampaignStatus.SUCCESS,
          sqsMessageId: 'msg-123',
        },
      );
    });

    it('should skip SQS when strategy is linear', async () => {
      const sqsSendSpy = jest.spyOn(service['sqsClient'] as any, 'send');

      const result = await service.publishBundle(mockCampaign, {
        notificationStrategy: 'linear',
      });

      expect(result.success).toBe(true);
      expect(sqsSendSpy).not.toHaveBeenCalled();
      expect(result.messageId).toBeUndefined();
    });

    it('should handle SQS errors', async () => {
      // Ensure the campaign has a bundleUri to pass validation and reset status
      const campaignWithBundle = {
        ...mockCampaign,
        bundleUri: 's3://bucket/key',
        status: CampaignStatus.PENDING, // Explicitly reset status
      };

      jest
        .spyOn(service['sqsClient'] as any, 'send')
        .mockRejectedValue(new Error('SQS error'));

      // Explicitly use sqs strategy to ensure SQS is called
      const result = await service.publishBundle(campaignWithBundle, {
        notificationStrategy: 'sqs',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('SQS error');
    });
  });

  describe('executeFullLifecycle', () => {
    it('should execute all steps successfully', async () => {
      jest
        .spyOn(service['s3Client'] as any, 'send')
        .mockResolvedValue({} as any);
      jest
        .spyOn(service['sqsClient'] as any, 'send')
        .mockResolvedValue({ MessageId: 'msg-123' } as any);

      const result = await service.executeFullLifecycle(mockCampaign);

      expect(result.generation.success).toBe(true);
      expect(result.upload.success).toBe(true);
      expect(result.publish.success).toBe(true);
    });

    it('should stop on generation failure', async () => {
      const invalidCampaign = { ...mockCampaign, id: null };

      const result = await service.executeFullLifecycle(
        invalidCampaign as any,
        {
          generation: { validate: true },
        },
      );

      expect(result.generation.success).toBe(false);
      expect(result.upload.error).toBe('Not executed');
      expect(result.publish.error).toBe('Not executed');
    });

    it('should stop on upload failure', async () => {
      configService.get.mockImplementation(
        (key: string, defaultValue?: any) => {
          if (key === 'nodeEnv') return 'production';
          if (key === 'campaign.promoBucket') return 'test-bucket';
          return defaultValue;
        },
      );

      jest
        .spyOn(service['s3Client'] as any, 'send')
        .mockRejectedValue(new Error('S3 error'));

      const result = await service.executeFullLifecycle(mockCampaign);

      expect(result.generation.success).toBe(true);
      expect(result.upload.success).toBe(false);
      expect(result.publish.error).toBe('Not executed');
    });
  });
});
