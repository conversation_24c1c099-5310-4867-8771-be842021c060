import { SendMessageCommandOutput } from '@aws-sdk/client-sqs';

/**
 * Common result interface for bundle operations
 */
export interface BundleOperationResult {
  success: boolean;
  message?: string;
  error?: string;
  data?: any;
}

/**
 * Options for bundle generation
 */
export interface BundleGenerationOptions {
  includeMetadata?: boolean;
  format?: 'json' | 'xml';
  validate?: boolean;
}

/**
 * Options for bundle upload to S3
 */
export interface BundleUploadOptions {
  skipInDevelopment?: boolean;
  contentType?: string;
  tags?: Record<string, string>;
}

/**
 * Options for bundle publishing
 */
export interface BundlePublishOptions {
  notificationStrategy?: 'sqs' | 'linear' | 'both' | 'none';
  skipNotifications?: boolean;
  messageGroupId?: string;
  messageDeduplicationId?: string;
}

/**
 * Bundle content structure
 */
export interface BundleContent {
  campaignId: string;
  timestamp: string;
  propertyId: string;
  metadata?: Record<string, any>;
}

/**
 * Result of publishing to SQS
 */
export interface PublishResult extends BundleOperationResult {
  sqsResult?: SendMessageCommandOutput;
  messageId?: string;
}

/**
 * Interface for the complete bundle lifecycle
 */
export interface IBundleLifecycleService {
  /**
   * Generates a promo bundle for the campaign
   */
  generatePromoBundle(
    campaign: any,
    options?: BundleGenerationOptions,
  ): Promise<BundleOperationResult>;

  /**
   * Uploads the bundle to S3 storage
   */
  uploadBundle(
    campaign: any,
    options?: BundleUploadOptions,
  ): Promise<BundleOperationResult>;

  /**
   * Publishes the bundle to downstream services
   */
  publishBundle(
    campaign: any,
    options?: BundlePublishOptions,
  ): Promise<BundleOperationResult>;

  /**
   * Executes the complete bundle lifecycle
   */
  executeFullLifecycle(
    campaign: any,
    options?: {
      generation?: BundleGenerationOptions;
      upload?: BundleUploadOptions;
      publish?: BundlePublishOptions;
    },
  ): Promise<{
    generation: BundleOperationResult;
    upload: BundleOperationResult;
    publish: BundleOperationResult;
  }>;
}
