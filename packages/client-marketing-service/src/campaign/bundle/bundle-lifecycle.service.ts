import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import {
  SendMessageCommand,
  SendMessageCommandInput,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CampaignStatus } from '../entities/campaign.entity';
import { Campaign } from '../entities/campaign.entity';
import { NotificationWorkItem } from '../interfaces/notification-strategy.interface';
import { NotificationContext } from '../strategies/notification-context.service';
import { NotificationStrategyType } from '../strategies/notification-strategy.factory';
import {
  BundleContent,
  BundleGenerationOptions,
  BundleOperationResult,
  BundlePublishOptions,
  BundleUploadOptions,
  PublishResult,
} from './interfaces/bundle-lifecycle.interface';

/**
 * Unified service for managing bundle lifecycle operations (generate, upload, publish)
 * Consolidates bundle logic previously scattered across multiple services
 */
@Injectable()
export class BundleLifecycleService {
  private readonly logger = new Logger(BundleLifecycleService.name);
  private readonly s3Client: S3Client;
  private readonly sqsClient: SQSClient;
  private readonly bucketName: string;
  private readonly sqsUrl: string;
  private readonly notificationStrategy: NotificationStrategyType;
  private readonly awsRegion: string;

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    private readonly configService: ConfigService,
    private readonly notificationContext: NotificationContext,
  ) {
    this.awsRegion = this.configService.get<string>('aws.region', 'us-east-1');
    this.s3Client = new S3Client({ region: this.awsRegion });
    this.sqsClient = new SQSClient({ region: this.awsRegion });
    this.bucketName =
      this.configService.get<string>('campaign.promoBucket') ||
      'luxury-presence-promo-dev';
    this.sqsUrl = this.configService.get<string>('campaign.sqsUrl', '');

    const strategyString = (
      this.configService.get<string>('campaign.notificationStrategy') ||
      'linear'
    ).toLowerCase();

    // Validate and set notification strategy, default to LINEAR if invalid
    if (
      !Object.values(NotificationStrategyType).includes(
        strategyString as NotificationStrategyType,
      )
    ) {
      const validStrategies = Object.values(NotificationStrategyType).join(
        ', ',
      );
      this.logger.warn(
        `Invalid notification strategy: '${strategyString}'. Valid options are: ${validStrategies}. Defaulting to LINEAR.`,
      );
      this.notificationStrategy = NotificationStrategyType.LINEAR;
    } else {
      this.notificationStrategy = strategyString as NotificationStrategyType;
    }
    this.logger.log(
      `Configured with AWS region: ${this.awsRegion}, notification strategy: ${this.notificationStrategy}`,
    );
  }

  /**
   * Creates the bundle content structure
   */
  private createBundleContent(
    campaign: Campaign,
    options?: BundleGenerationOptions,
  ): BundleContent {
    const content: BundleContent = {
      campaignId: campaign.id,
      timestamp: new Date().toISOString(),
      propertyId: campaign.propertyId,
    };

    if (options?.includeMetadata && campaign.metadata) {
      content.metadata = campaign.metadata;
    }

    return content;
  }

  /**
   * Generates a promo bundle for the campaign
   */
  generatePromoBundle(
    campaign: Campaign,
    options?: BundleGenerationOptions,
  ): Promise<BundleOperationResult> {
    try {
      const bundleContent = this.createBundleContent(campaign, options);

      if (options?.validate) {
        // Basic validation
        if (!bundleContent.campaignId || !bundleContent.propertyId) {
          throw new Error('Invalid bundle content: missing required fields');
        }
      }

      const bundleId = `bundle-${campaign.id}`;
      this.logger.log(
        `Generated promo bundle for campaign ${campaign.id}: ${bundleId}`,
      );

      return Promise.resolve({
        success: true,
        message: `Bundle generated successfully: ${bundleId}`,
        data: {
          bundleId,
          content: bundleContent,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to generate bundle for campaign ${campaign.id}:`,
        error,
      );
      return Promise.resolve({
        success: false,
        error: error.message || 'Failed to generate bundle',
      });
    }
  }

  /**
   * Uploads the bundle to S3 storage
   */
  async uploadBundle(
    campaign: Campaign,
    options?: BundleUploadOptions,
  ): Promise<BundleOperationResult> {
    try {
      const bundleContent = this.createBundleContent(campaign);
      const key = `bundle-${campaign.id}.json`;
      const bundleJson = JSON.stringify(bundleContent, null, 2);

      const nodeEnv = this.configService.get<string>('nodeEnv', 'development');
      const isDevelopment = nodeEnv === 'development' || nodeEnv === 'local';

      let bundleUri: string;

      if (isDevelopment && options?.skipInDevelopment !== false) {
        // Skip actual upload in development
        bundleUri = `s3://${this.bucketName}/${key}`;
        this.logger.log(
          `Development mode: Skipping actual S3 upload for campaign ${campaign.id}, using dummy URI: ${bundleUri}`,
        );
      } else {
        // Perform actual S3 upload
        const putCommand = new PutObjectCommand({
          Bucket: this.bucketName,
          Key: key,
          Body: bundleJson,
          ContentType: options?.contentType || 'application/json',
          ...(options?.tags && {
            Tagging: Object.entries(options.tags)
              .map(([k, v]) => `${k}=${v}`)
              .join('&'),
          }),
        });

        await this.s3Client.send(putCommand);
        bundleUri = `s3://${this.bucketName}/${key}`;
        this.logger.log(
          `Successfully uploaded bundle for campaign ${campaign.id} to S3: ${bundleUri}`,
        );
      }

      // Update campaign with bundle URI
      await this.campaignRepository.manager.transaction(async manager => {
        await manager.update(Campaign, { id: campaign.id }, { bundleUri });
        campaign.bundleUri = bundleUri;
      });

      return {
        success: true,
        message: `Bundle uploaded successfully to ${bundleUri}`,
        data: {
          bundleUri,
          key,
        },
      };
    } catch (error) {
      this.logger.error(
        `Failed to upload bundle for campaign ${campaign.id}:`,
        error,
      );
      return {
        success: false,
        error: error.message || 'Failed to upload bundle',
      };
    }
  }

  /**
   * Publishes the bundle to downstream services (SQS/notifications)
   */
  async publishBundle(
    campaign: Campaign,
    options?: BundlePublishOptions,
  ): Promise<PublishResult> {
    try {
      if (campaign.status === CampaignStatus.SUCCESS) {
        return {
          success: true,
          message: 'Campaign already published',
        };
      }

      // Use the provided strategy or default to the configured strategy
      const optionsStrategy = options?.notificationStrategy;
      const configuredStrategy = this.notificationStrategy;

      // Note: 'sqs' string in BundlePublishOptions maps to SQS enum value
      // SQS strategy publishes to SQS queue and may trigger SNS notifications
      const shouldPublishToSqs =
        optionsStrategy === 'sqs' ||
        optionsStrategy === 'both' ||
        (!optionsStrategy &&
          (configuredStrategy === NotificationStrategyType.SQS ||
            configuredStrategy === NotificationStrategyType.BOTH));

      let messageId: string | undefined;

      if (shouldPublishToSqs && !options?.skipNotifications) {
        if (!this.sqsUrl) {
          this.logger.error(
            `SQS URL not configured but notification strategy requires SQS. ` +
              `Please set PROMO_SQS_URL environment variable or change CAMPAIGN_NOTIFICATION_STRATEGY to 'linear'.`,
          );
          throw new Error(
            'SQS URL not configured. Set PROMO_SQS_URL environment variable or change notification strategy to "linear".',
          );
        }

        const workItem = {
          campaignId: campaign.id,
          propertyId: campaign.propertyId,
          bundleUri: campaign.bundleUri,
          createdAt: campaign.createdAt,
          currency: campaign.currency,
          priceCents: campaign.priceCents,
        };

        const params: SendMessageCommandInput = {
          QueueUrl: this.sqsUrl,
          MessageBody: JSON.stringify(workItem),
          MessageGroupId: options?.messageGroupId || campaign.id,
          MessageDeduplicationId:
            options?.messageDeduplicationId || campaign.id,
        };

        const command = new SendMessageCommand(params);
        const sqsResult = await this.sqsClient.send(command);
        messageId = sqsResult.MessageId;

        this.logger.log(
          `Published campaign ${campaign.id} to SQS with message ID: ${messageId}`,
        );
      }

      // Handle Linear notifications (for Linear-only and BOTH strategies)
      if (
        optionsStrategy === 'linear' ||
        optionsStrategy === 'both' ||
        (!optionsStrategy &&
          (configuredStrategy === NotificationStrategyType.LINEAR ||
            configuredStrategy === NotificationStrategyType.BOTH))
      ) {
        if (
          optionsStrategy === 'both' ||
          configuredStrategy === NotificationStrategyType.BOTH
        ) {
          this.logger.log(
            `Using BOTH strategy - creating Linear ticket via NotificationContext`,
          );
        } else {
          // Linear strategy doesn't use SQS, it creates Linear tickets
          this.logger.log(
            `Using Linear strategy for campaign ${campaign.id} - creating Linear ticket via NotificationContext`,
          );
        }

        // Send Linear notification
        await this.sendBundleNotifications(campaign, 'published');
      }

      if (optionsStrategy === 'none') {
        this.logger.log(
          `Skipping all notifications for campaign ${campaign.id} - strategy: none`,
        );
      } else if (
        !shouldPublishToSqs &&
        optionsStrategy !== 'linear' &&
        optionsStrategy !== 'both' &&
        configuredStrategy !== NotificationStrategyType.LINEAR &&
        configuredStrategy !== NotificationStrategyType.BOTH
      ) {
        this.logger.log(
          `Skipping all notifications for campaign ${campaign.id} - strategy: ${optionsStrategy || configuredStrategy}`,
        );
      }

      // Update campaign status
      await this.campaignRepository.manager.transaction(async manager => {
        await manager.update(
          Campaign,
          { id: campaign.id },
          {
            status: CampaignStatus.SUCCESS,
            sqsMessageId: messageId || null,
          },
        );
        campaign.status = CampaignStatus.SUCCESS;
        campaign.sqsMessageId = messageId || null;
      });

      return {
        success: true,
        message: `Campaign published successfully${messageId ? ` with SQS message ID: ${messageId}` : ''}`,
        messageId,
      };
    } catch (error) {
      this.logger.error(`Failed to publish campaign ${campaign.id}:`, error);
      return {
        success: false,
        error: error.message || 'Failed to publish bundle',
      };
    }
  }

  /**
   * Executes the complete bundle lifecycle: generate → upload → publish
   */
  async executeFullLifecycle(
    campaign: Campaign,
    options?: {
      generation?: BundleGenerationOptions;
      upload?: BundleUploadOptions;
      publish?: BundlePublishOptions;
    },
  ): Promise<{
    generation: BundleOperationResult;
    upload: BundleOperationResult;
    publish: BundleOperationResult;
  }> {
    const results = {
      generation: {
        success: false,
        error: 'Not executed',
      } as BundleOperationResult,
      upload: {
        success: false,
        error: 'Not executed',
      } as BundleOperationResult,
      publish: {
        success: false,
        error: 'Not executed',
      } as BundleOperationResult,
    };

    try {
      // Step 1: Generate bundle
      this.logger.log(`Starting full lifecycle for campaign ${campaign.id}`);
      results.generation = await this.generatePromoBundle(
        campaign,
        options?.generation,
      );

      if (!results.generation.success) {
        this.logger.error(
          `Bundle generation failed for campaign ${campaign.id}`,
        );
        return results;
      }

      // Step 2: Upload bundle
      results.upload = await this.uploadBundle(campaign, options?.upload);

      if (!results.upload.success) {
        this.logger.error(`Bundle upload failed for campaign ${campaign.id}`);
        return results;
      }

      // Step 3: Publish bundle
      results.publish = await this.publishBundle(campaign, options?.publish);

      if (!results.publish.success) {
        this.logger.error(
          `Bundle publishing failed for campaign ${campaign.id}`,
        );
        return results;
      }

      this.logger.log(
        `Successfully completed full bundle lifecycle for campaign ${campaign.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Unexpected error in bundle lifecycle for campaign ${campaign.id}:`,
        error,
      );
    }

    return results;
  }

  /**
   * Sends notifications for bundle lifecycle events
   */
  private async sendBundleNotifications(
    campaign: Campaign,
    event: 'published' | 'uploaded' | 'generated',
  ): Promise<void> {
    try {
      const workItem: NotificationWorkItem = {
        campaignId: campaign.id,
        propertyId: campaign.propertyId,
        bundleUri: campaign.bundleUri,
        createdAt: campaign.createdAt,
        currency: campaign.currency,
        priceCents: campaign.priceCents,
      };

      const notificationResults = await this.notificationContext.notifyAll(
        workItem,
        campaign,
      );

      this.logger.log(
        `Sent ${notificationResults.length} notifications for campaign ${campaign.id} bundle ${event}`,
        {
          results: notificationResults.map(r => ({
            success: r.success,
            error: r.error,
          })),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send notifications for campaign ${campaign.id} bundle ${event}:`,
        error,
      );
      // Don't throw - notifications are not critical to bundle success
    }
  }
}
