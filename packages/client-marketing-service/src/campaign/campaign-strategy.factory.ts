import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import {
  CampaignOrchestrationStrategy,
  CampaignOrchestrationMode,
} from './interfaces/campaign-orchestration.interface';
import { FullOrchestrationStrategy } from './strategies/full-orchestration.strategy';
import { MockOrchestrationStrategy } from './strategies/mock-orchestration.strategy';
import { PaymentOnlyStrategy } from './strategies/payment-only.strategy';

@Injectable()
export class CampaignStrategyFactory {
  private readonly logger = new Logger(CampaignStrategyFactory.name);
  private readonly strategy: CampaignOrchestrationStrategy;
  private readonly mode: CampaignOrchestrationMode;

  constructor(
    private readonly configService: ConfigService,
    private readonly fullOrchestrationStrategy: FullOrchestrationStrategy,
    private readonly paymentOnlyStrategy: PaymentOnlyStrategy,
    private readonly mockOrchestrationStrategy: MockOrchestrationStrategy,
  ) {
    // Get configured mode from environment, defaulting to FULL for production
    const configuredMode = this.configService.get<string>(
      'campaignOrchestrationMode',
      CampaignOrchestrationMode.FULL,
    );

    this.mode =
      this.validateMode(configuredMode) || CampaignOrchestrationMode.FULL;

    // Select the strategy based on configuration
    switch (this.mode) {
      case CampaignOrchestrationMode.PAYMENT_ONLY:
        this.strategy = this.paymentOnlyStrategy;
        break;
      case CampaignOrchestrationMode.MOCK:
        this.strategy = this.mockOrchestrationStrategy;
        break;
      case CampaignOrchestrationMode.FULL:
      default:
        this.strategy = this.fullOrchestrationStrategy;
        break;
    }

    this.logger.log('CampaignStrategyFactory initialized', {
      mode: this.mode,
    });
  }

  /**
   * Get the configured strategy based on environment variable
   * @returns The configured campaign orchestration strategy
   */
  getStrategy(): CampaignOrchestrationStrategy {
    this.logger.log('Selected orchestration strategy', {
      mode: this.mode,
    });

    return this.strategy;
  }

  /**
   * Get the current orchestration mode
   */
  getMode(): CampaignOrchestrationMode {
    return this.mode;
  }

  /**
   * Validate if the provided string is a valid orchestration mode
   */
  private validateMode(mode: string): CampaignOrchestrationMode | null {
    const validModes = Object.values(CampaignOrchestrationMode) as string[];

    if (validModes.includes(mode)) {
      return mode as CampaignOrchestrationMode;
    }

    this.logger.warn('Invalid orchestration mode configured', {
      configuredMode: mode,
      validModes,
    });

    return null;
  }
}
