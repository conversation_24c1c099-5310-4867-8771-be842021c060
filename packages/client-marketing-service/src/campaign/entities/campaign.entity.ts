import { Field, ID, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

import { Payment } from '../../payment/entities/payment.entity';

export enum CampaignStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
  CANCELED = 'CANCELED',
}

registerEnumType(CampaignStatus, { name: 'CampaignStatus' });

@Entity('campaign')
@ObjectType()
export class Campaign {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column({ type: 'varchar', length: 255, name: 'property_id' })
  @Index()
  @Field(() => ID)
  propertyId: string;

  @Column({ type: 'integer', name: 'price_cents' })
  @Field(() => Int)
  priceCents: number;

  @Column({ type: 'varchar', length: 3, default: 'USD' })
  @Field(() => String)
  currency: string;

  @Column({ type: 'enum', enum: CampaignStatus })
  @Field(() => CampaignStatus)
  status: CampaignStatus;

  @Column({ type: 'text', name: 'bundle_uri', nullable: true })
  @Field(() => String, { nullable: true })
  bundleUri: string | null;

  @Column({ type: 'text', name: 'sqs_message_id', nullable: true })
  @Field(() => String, { nullable: true })
  sqsMessageId: string | null;

  @Column({ type: 'integer', name: 'retry_count', default: 0 })
  @Field(() => Int)
  retryCount: number;

  @Column({ type: 'varchar', length: 255, name: 'created_by' })
  @Field(() => String)
  createdBy: string;

  @Column({ type: 'jsonb', nullable: true })
  @Field(() => GraphQLJSONObject, { nullable: true })
  metadata: Record<string, any> | null;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @OneToMany(() => Payment, payment => payment.campaign)
  @Field(() => [Payment], { nullable: true })
  payments?: Payment[];
}
