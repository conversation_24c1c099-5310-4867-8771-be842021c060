import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import { CampaignStrategyFactory } from './campaign-strategy.factory';
import { CampaignOrchestrationMode } from './interfaces/campaign-orchestration.interface';
import { FullOrchestrationStrategy } from './strategies/full-orchestration.strategy';
import { MockOrchestrationStrategy } from './strategies/mock-orchestration.strategy';
import { PaymentOnlyStrategy } from './strategies/payment-only.strategy';

describe('CampaignStrategyFactory', () => {
  let factory: CampaignStrategyFactory;
  let configService: jest.Mocked<ConfigService>;
  let fullStrategy: FullOrchestrationStrategy;
  let paymentOnlyStrategy: PaymentOnlyStrategy;
  let mockStrategy: MockOrchestrationStrategy;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignStrategyFactory,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: FullOrchestrationStrategy,
          useValue: {
            getMode: () => CampaignOrchestrationMode.FULL,
            execute: jest.fn(),
          },
        },
        {
          provide: PaymentOnlyStrategy,
          useValue: {
            getMode: () => CampaignOrchestrationMode.PAYMENT_ONLY,
            execute: jest.fn(),
          },
        },
        {
          provide: MockOrchestrationStrategy,
          useValue: {
            getMode: () => CampaignOrchestrationMode.MOCK,
            execute: jest.fn(),
          },
        },
      ],
    }).compile();

    configService = module.get(ConfigService);
    factory = module.get<CampaignStrategyFactory>(CampaignStrategyFactory);
    fullStrategy = module.get<FullOrchestrationStrategy>(
      FullOrchestrationStrategy,
    );
    paymentOnlyStrategy = module.get<PaymentOnlyStrategy>(PaymentOnlyStrategy);
    mockStrategy = module.get<MockOrchestrationStrategy>(
      MockOrchestrationStrategy,
    );
  });

  it('should be defined', () => {
    expect(factory).toBeDefined();
  });

  describe('initialization', () => {
    it('should initialize with FULL mode by default', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.FULL);

      // Act
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Assert
      expect(newFactory.getMode()).toBe(CampaignOrchestrationMode.FULL);
    });

    it('should initialize with configured mode', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.PAYMENT_ONLY);

      // Act
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Assert
      expect(newFactory.getMode()).toBe(CampaignOrchestrationMode.PAYMENT_ONLY);
    });

    it('should handle invalid configuration and default to FULL', () => {
      // Arrange
      configService.get.mockReturnValue('invalid-mode');

      // Act
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Assert
      expect(newFactory.getMode()).toBe(CampaignOrchestrationMode.FULL);
    });
  });

  describe('getStrategy', () => {
    it('should return full strategy by default', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.FULL);
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Act
      const strategy = newFactory.getStrategy();

      // Assert
      expect(strategy).toBe(fullStrategy);
    });

    it('should return payment-only strategy when configured', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.PAYMENT_ONLY);
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Act
      const strategy = newFactory.getStrategy();

      // Assert
      expect(strategy).toBe(paymentOnlyStrategy);
    });

    it('should return mock strategy when configured', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.MOCK);
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Act
      const strategy = newFactory.getStrategy();

      // Assert
      expect(strategy).toBe(mockStrategy);
    });
  });

  describe('getMode', () => {
    it('should return the configured mode', () => {
      // Arrange
      configService.get.mockReturnValue(CampaignOrchestrationMode.MOCK);
      const newFactory = new CampaignStrategyFactory(
        configService,
        fullStrategy,
        paymentOnlyStrategy,
        mockStrategy,
      );

      // Act
      const mode = newFactory.getMode();

      // Assert
      expect(mode).toBe(CampaignOrchestrationMode.MOCK);
    });
  });
});
