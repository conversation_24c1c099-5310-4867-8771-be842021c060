import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';

import { CampaignStrategyFactory } from './campaign-strategy.factory';
import { CampaignService } from './campaign.service';
import { CreateCampaignWithPaymentDto } from './dto/create-campaign-with-payment.dto';
import { Campaign, CampaignStatus } from './entities/campaign.entity';
import { CampaignPaymentService } from './payment/campaign-payment.service';
import { NotificationContext } from './strategies/notification-context.service';
import { PaymentStatus } from '../payment/entities/payment.entity';

describe('CampaignService', () => {
  let service: CampaignService;
  let repository: jest.Mocked<Repository<Campaign>>;
  let dataSource: jest.Mocked<DataSource>;
  let mockTransaction: jest.Mock;
  let mockCampaignStrategyFactory: any;
  let mockCampaignPaymentService: any;

  const mockCampaign: Campaign = {
    id: 'camp-1',
    propertyId: 'prop-1',
    priceCents: 1000,
    currency: 'USD',
    status: CampaignStatus.PENDING,
    bundleUri: null,
    sqsMessageId: null,
    retryCount: 0,
    createdBy: 'tester',
    metadata: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    payments: [],
  };

  beforeEach(async () => {
    const mockRepo = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      remove: jest.fn(),
    } as unknown as jest.Mocked<Repository<Campaign>>;

    mockTransaction = jest.fn();
    const mockDataSource = {
      transaction: mockTransaction,
    };

    // Create a mock strategy
    const mockStrategy = {
      execute: jest.fn().mockResolvedValue({
        campaign: {
          id: 'campaign-123',
          status: CampaignStatus.SUCCESS,
          bundleUri: 'bundle-123',
        },
        orchestrationMode: 'mock',
        success: true,
        metadata: {
          transactionId: 'txn-123',
          bundleId: 'bundle-123',
          orchestrationStep: 'completed',
        },
      }),
      getMode: jest.fn().mockReturnValue('mock'),
    };

    mockCampaignStrategyFactory = {
      getStrategy: jest.fn().mockReturnValue(mockStrategy),
    };

    mockCampaignPaymentService = {
      processCampaignPayment: jest.fn().mockResolvedValue({
        campaign: mockCampaign,
        payment: {
          id: 'payment-123',
          status: PaymentStatus.SUCCEEDED,
        },
      }),
      validatePaymentForCampaign: jest.fn().mockResolvedValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignService,
        {
          provide: getRepositoryToken(Campaign),
          useValue: mockRepo,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: CampaignStrategyFactory,
          useValue: mockCampaignStrategyFactory,
        },
        {
          provide: CampaignPaymentService,
          useValue: mockCampaignPaymentService,
        },
        {
          provide: NotificationContext,
          useValue: {
            notifyAll: jest.fn().mockResolvedValue([]),
          },
        },
      ],
    }).compile();

    service = module.get<CampaignService>(CampaignService);
    repository = module.get(getRepositoryToken(Campaign));
    dataSource = module.get(DataSource);
  });

  describe('findAll', () => {
    it('returns campaigns with payments relation', async () => {
      repository.find.mockResolvedValue([mockCampaign]);

      const result = await service.findAll();
      expect(repository.find).toHaveBeenCalledWith({
        relations: ['payments'],
      });
      expect(result).toEqual([mockCampaign]);
    });
  });

  describe('findOne', () => {
    it('returns a campaign when found', async () => {
      repository.findOne.mockResolvedValue(mockCampaign);

      const result = await service.findOne('camp-1');
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 'camp-1' },
        relations: ['payments'],
      });
      expect(result).toEqual(mockCampaign);
    });

    it('throws when not found', async () => {
      repository.findOne.mockResolvedValue(null);

      await expect(service.findOne('missing')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('updates an existing campaign', async () => {
      repository.findOne.mockResolvedValue(mockCampaign);
      repository.save.mockResolvedValue(mockCampaign);

      const result = await service.update('camp-1', {
        status: CampaignStatus.SUCCESS,
      });
      expect(repository.save).toHaveBeenCalled();
      expect(result).toEqual(mockCampaign);
    });
  });

  describe('delete', () => {
    it('soft deletes the campaign by setting status to CANCELED', async () => {
      const campaignToDelete = { ...mockCampaign };
      repository.findOne.mockResolvedValue(campaignToDelete);
      repository.save.mockResolvedValue({
        ...campaignToDelete,
        status: CampaignStatus.CANCELED,
      });

      await service.delete('camp-1');

      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 'camp-1' },
        relations: ['payments'],
      });
      expect(repository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          status: CampaignStatus.CANCELED,
        }),
      );
    });
  });

  describe('create', () => {
    it('should throw error for direct campaign creation', () => {
      expect(() => service.create()).toThrow(BadRequestException);
      expect(() => service.create()).toThrow(
        'Direct campaign creation is not allowed. Use createWithPayment() instead to ensure campaigns are always created with payments.',
      );
    });
  });

  describe('createWithPayment', () => {
    it('should create campaign with payment in transaction', async () => {
      const createDto: CreateCampaignWithPaymentDto & { createdBy: string } = {
        propertyId: 'property-123',
        priceCents: 5000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        propertyId: 'property-123',
        priceCents: 5000,
        currency: 'USD',
        status: CampaignStatus.PENDING,
        bundleUri: null,
        sqsMessageId: null,
        retryCount: 0,
        createdBy: 'user-123',
        metadata: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        payments: [],
      } as Campaign;

      const mockCampaignWithOrchestration = {
        ...mockCampaign,
        status: CampaignStatus.SUCCESS,
        bundleUri: 'bundle-123',
        metadata: {
          orchestration: {
            mode: 'mock',
            success: true,
            transactionId: 'txn-123',
            bundleId: 'bundle-123',
            orchestrationStep: 'completed',
          },
        },
      };

      const mockManager = {
        getRepository: jest.fn().mockReturnValue({
          create: jest.fn().mockReturnValue(mockCampaign),
          save: jest
            .fn()
            .mockResolvedValueOnce(mockCampaign) // First save for creation
            .mockResolvedValueOnce(mockCampaignWithOrchestration), // Second save after orchestration
          findOne: jest.fn().mockResolvedValue(mockCampaign), // For reloading campaign
        }),
      };

      mockTransaction.mockImplementation(callback => callback(mockManager));
      repository.findOne.mockResolvedValue(mockCampaignWithOrchestration);

      const result = await service.createWithPayment(createDto);

      expect(dataSource.transaction).toHaveBeenCalledTimes(2); // Two separate transactions
      expect(mockCampaignStrategyFactory.getStrategy).toHaveBeenCalled();
      expect(result).toEqual(mockCampaignWithOrchestration);
    });

    it('should handle orchestration failure and mark campaign as ERROR', async () => {
      const createDto: CreateCampaignWithPaymentDto & { createdBy: string } = {
        propertyId: 'property-123',
        priceCents: 5000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const orchestrationError = new Error('Orchestration failed');

      // Mock strategy to throw error
      mockCampaignStrategyFactory.getStrategy.mockReturnValue({
        execute: jest.fn().mockRejectedValue(orchestrationError),
        getMode: jest.fn().mockReturnValue('full'),
      });

      const mockCampaignWithError = {
        ...mockCampaign,
        status: CampaignStatus.ERROR,
        metadata: {
          orchestration: {
            mode: 'full',
            success: false,
            error: 'Orchestration failed',
            timestamp: expect.any(String),
            failureStage: 'orchestration',
          },
        },
      };

      const mockManager = {
        getRepository: jest.fn().mockReturnValue({
          create: jest.fn().mockReturnValue(mockCampaign),
          save: jest
            .fn()
            .mockResolvedValueOnce(mockCampaign) // Initial campaign creation
            .mockResolvedValueOnce(mockCampaignWithError), // Error status update
          findOne: jest.fn().mockResolvedValue(mockCampaign),
        }),
      };

      mockTransaction.mockImplementation(callback => callback(mockManager));

      await expect(service.createWithPayment(createDto)).rejects.toThrow(
        'Orchestration failed',
      );

      expect(dataSource.transaction).toHaveBeenCalledTimes(2); // One for creation, one for error status update
    });

    it('should pass testCardNumber through orchestration context', async () => {
      const createDto: CreateCampaignWithPaymentDto & { createdBy: string } = {
        propertyId: 'property-123',
        priceCents: 5000,
        currency: 'USD',
        createdBy: 'user-123',
        testCardNumber: '****************', // Declined card for testing
      };

      const mockCampaign = {
        id: 'campaign-123',
        propertyId: 'property-123',
        priceCents: 5000,
        currency: 'USD',
        status: CampaignStatus.PENDING,
        bundleUri: null,
        sqsMessageId: null,
        retryCount: 0,
        createdBy: 'user-123',
        metadata: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        payments: [],
      } as Campaign;

      const mockCampaignWithOrchestration = {
        ...mockCampaign,
        status: CampaignStatus.SUCCESS,
        bundleUri: 'bundle-123',
        metadata: {
          orchestration: {
            mode: 'mock',
            success: true,
            transactionId: 'txn-123',
            bundleId: 'bundle-123',
            orchestrationStep: 'completed',
          },
        },
      };

      // Mock strategy to verify testCardNumber is passed through
      const mockStrategy = {
        execute: jest.fn().mockResolvedValue({
          campaign: {
            id: 'campaign-123',
            status: CampaignStatus.SUCCESS,
            bundleUri: 'bundle-123',
          },
          orchestrationMode: 'mock',
          success: true,
          metadata: {
            transactionId: 'txn-123',
            bundleId: 'bundle-123',
            orchestrationStep: 'completed',
          },
        }),
        getMode: jest.fn().mockReturnValue('mock'),
      };

      mockCampaignStrategyFactory.getStrategy.mockReturnValue(mockStrategy);

      const mockManager = {
        getRepository: jest.fn().mockReturnValue({
          create: jest.fn().mockReturnValue(mockCampaign),
          save: jest
            .fn()
            .mockResolvedValueOnce(mockCampaign)
            .mockResolvedValueOnce(mockCampaignWithOrchestration),
          findOne: jest.fn().mockResolvedValue(mockCampaign), // For reloading campaign
        }),
      };

      mockTransaction.mockImplementation(callback => callback(mockManager));
      repository.findOne.mockResolvedValue(mockCampaignWithOrchestration);

      await service.createWithPayment(createDto);

      // Verify that testCardNumber was passed to strategy
      expect(mockStrategy.execute).toHaveBeenCalledWith(
        expect.objectContaining({
          campaign: expect.any(Object),
          testCardNumber: '****************',
          metadata: expect.any(Object),
        }),
      );
    });
  });
});
