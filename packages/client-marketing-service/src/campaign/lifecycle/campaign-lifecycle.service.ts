import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import { NotificationWorkItem } from '../interfaces/notification-strategy.interface';
import { NotificationContext } from '../strategies/notification-context.service';

/**
 * Result of a state transition operation
 */
export interface StateTransitionResult {
  success: boolean;
  previousStatus?: CampaignStatus;
  newStatus?: CampaignStatus;
  error?: string;
  timestamp?: Date;
}

/**
 * Options for state transition
 */
export interface StateTransitionOptions {
  reason?: string;
  metadata?: Record<string, any>;
  skipValidation?: boolean;
}

/**
 * Unified service for managing campaign lifecycle and state transitions
 * Provides centralized state management with validation rules
 */
/**
 * Data required to create a new campaign
 */
export interface CreateCampaignData {
  propertyId: string;
  priceCents: number;
  currency: string;
  createdBy: string;
  metadata?: Record<string, any>;
}

/**
 * Result of processing a campaign
 */
export interface ProcessCampaignResult {
  success: boolean;
  campaign: Campaign;
  processingTime?: number;
  error?: string;
}

/**
 * Result of completing a campaign
 */
export interface CompleteCampaignResult extends StateTransitionResult {
  validationErrors?: string[];
}

/**
 * Result of handling an error
 */
export interface HandleErrorResult extends StateTransitionResult {
  retryCount?: number;
  shouldRetry?: boolean;
  suggestion?: string;
}

@Injectable()
export class CampaignLifecycleService {
  private readonly logger = new Logger(CampaignLifecycleService.name);
  private readonly stateTransitions = new Map<
    CampaignStatus,
    CampaignStatus[]
  >();

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    private readonly configService: ConfigService,
    @Inject('PaymentService')
    private readonly paymentService: any,
    @Inject('BundleLifecycleService')
    private readonly bundleLifecycleService: any,
    @Inject('RetryService')
    private readonly retryService: any,
    private readonly notificationContext: NotificationContext,
  ) {
    this.initializeStateTransitions();
    // ConfigService will be used in future PRs for feature flags and configuration
  }

  /**
   * Initializes the valid state transitions map
   */
  private initializeStateTransitions(): void {
    // PENDING can transition to SUCCESS, ERROR, or CANCELED
    this.stateTransitions.set(CampaignStatus.PENDING, [
      CampaignStatus.SUCCESS,
      CampaignStatus.ERROR,
      CampaignStatus.CANCELED,
    ]);

    // ERROR can transition back to PENDING (retry) or to CANCELED
    this.stateTransitions.set(CampaignStatus.ERROR, [
      CampaignStatus.PENDING,
      CampaignStatus.CANCELED,
    ]);

    // SUCCESS is a terminal state - no transitions allowed
    this.stateTransitions.set(CampaignStatus.SUCCESS, []);

    // CANCELED is a terminal state - no transitions allowed
    this.stateTransitions.set(CampaignStatus.CANCELED, []);
  }

  /**
   * Checks if a state transition is valid
   */
  canTransition(from: CampaignStatus, to: CampaignStatus): boolean {
    if (from === to) {
      return false; // No self-transitions
    }

    const validTransitions = this.stateTransitions.get(from);
    return validTransitions?.includes(to) || false;
  }

  /**
   * Transitions a campaign to a new state
   */
  async transitionState(
    campaign: Campaign,
    newStatus: CampaignStatus,
    options?: StateTransitionOptions,
  ): Promise<StateTransitionResult> {
    if (!campaign) {
      return {
        success: false,
        error: 'Campaign is required for state transition',
      };
    }

    const previousStatus = campaign.status;

    // Always reject self transitions, even when skipValidation is true
    if (previousStatus === newStatus) {
      return {
        success: false,
        previousStatus,
        error: `Self transition from ${previousStatus} to ${newStatus} is not allowed`,
      };
    }
    // Validate transition unless explicitly skipped
    if (
      !options?.skipValidation &&
      !this.canTransition(previousStatus, newStatus)
    ) {
      return {
        success: false,
        previousStatus,
        error: `Invalid state transition from ${previousStatus} to ${newStatus}`,
      };
    }

    try {
      // Update campaign status in transaction
      await this.campaignRepository.manager.transaction(async manager => {
        await manager.update(
          Campaign,
          { id: campaign.id },
          { status: newStatus },
        );
        campaign.status = newStatus;
      });

      this.logger.log(
        `Campaign ${campaign.id} transitioned from ${previousStatus} to ${newStatus}`,
        options?.metadata,
      );

      // Send notifications for significant state transitions
      await this.sendStateTransitionNotifications(
        campaign,
        previousStatus,
        newStatus,
      );

      return {
        success: true,
        previousStatus,
        newStatus,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to transition campaign ${campaign.id} from ${previousStatus} to ${newStatus}`,
        error,
      );

      return {
        success: false,
        previousStatus,
        error: `Failed to transition state: ${error.message || 'Unknown error'}`,
      };
    }
  }

  /**
   * Gets the current state of a campaign
   */
  async getCurrentState(campaignId: string): Promise<CampaignStatus | null> {
    const campaign = await this.campaignRepository.findOne({
      where: { id: campaignId },
      select: ['id', 'status'],
    });

    return campaign?.status || null;
  }

  /**
   * Gets valid transitions from a given state
   */
  getValidTransitions(status: CampaignStatus): CampaignStatus[] {
    return this.stateTransitions.get(status) || [];
  }

  /**
   * Creates a new campaign with PENDING status
   */
  async createCampaign(campaignData: CreateCampaignData): Promise<Campaign> {
    const campaign = {
      ...campaignData,
      status: CampaignStatus.PENDING,
      retryCount: 0,
      metadata: campaignData.metadata || null,
      bundleUri: null,
      sqsMessageId: null,
    } as Campaign;

    this.logger.log(
      `Creating new campaign for property ${campaignData.propertyId}`,
    );

    try {
      const savedCampaign = await this.campaignRepository.save(campaign);
      this.logger.log(`Campaign ${savedCampaign.id} created successfully`);
      return savedCampaign;
    } catch (error) {
      this.logger.error(`Failed to create campaign`, error);
      throw new Error(`Failed to create campaign: ${error.message}`);
    }
  }

  /**
   * Processes a campaign through its workflow
   */
  async processCampaign(campaign: Campaign): Promise<ProcessCampaignResult> {
    const startTime = Date.now();

    try {
      // Transition to SUCCESS
      const transitionResult = await this.transitionState(
        campaign,
        CampaignStatus.SUCCESS,
      );

      if (!transitionResult.success) {
        this.logger.error(
          `Failed to transition campaign ${campaign.id} to SUCCESS: ${transitionResult.error}`,
          { campaignId: campaign.id, error: transitionResult.error },
        );

        return {
          success: false,
          campaign,
          processingTime: Date.now() - startTime,
          error: transitionResult.error || 'State transition failed',
        };
      }

      // Only set status if transition was successful
      campaign.status = CampaignStatus.SUCCESS;

      return {
        success: true,
        campaign,
        processingTime: Date.now() - startTime,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Unknown error during campaign processing';

      this.logger.error(
        `Exception during campaign processing for campaign ${campaign.id}: ${errorMessage}`,
        { campaignId: campaign.id, error: errorMessage },
      );

      return {
        success: false,
        campaign,
        processingTime: Date.now() - startTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Completes a campaign with validation
   */
  async completeCampaign(campaign: Campaign): Promise<CompleteCampaignResult> {
    // Validate required fields
    const validationErrors: string[] = [];
    if (!campaign.bundleUri) {
      validationErrors.push('Bundle URI is required');
    }

    if (validationErrors.length > 0) {
      return {
        success: false,
        previousStatus: campaign.status,
        error: 'Campaign is not ready for completion',
        validationErrors,
      };
    }

    // Transition to SUCCESS
    return this.transitionState(campaign, CampaignStatus.SUCCESS);
  }

  /**
   * Cancels a campaign with reason tracking
   */
  async cancelCampaign(
    campaign: Campaign,
    reason: string,
  ): Promise<StateTransitionResult> {
    const previousStatus = campaign.status;

    // Verify the transition is allowed
    if (!this.canTransition(previousStatus, CampaignStatus.CANCELED)) {
      return {
        success: false,
        previousStatus,
        error: `Invalid state transition from ${previousStatus} to ${CampaignStatus.CANCELED}`,
      };
    }

    // Update metadata with cancel reason
    const metadata: Record<string, any> = {
      ...(campaign.metadata || {}),
      cancelReason: reason,
      canceledAt: new Date(),
    };

    try {
      // Update campaign with CANCELED status and metadata in transaction
      await this.campaignRepository.manager.transaction(async manager => {
        await manager.update(
          Campaign,
          { id: campaign.id },
          { status: CampaignStatus.CANCELED, metadata },
        );
      });

      // Update in-memory campaign object after successful transaction
      campaign.status = CampaignStatus.CANCELED;
      campaign.metadata = metadata;

      this.logger.log(`Campaign ${campaign.id} canceled: ${reason}`, {
        campaignId: campaign.id,
        reason,
        previousStatus,
      });

      return {
        success: true,
        previousStatus,
        newStatus: CampaignStatus.CANCELED,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to cancel campaign ${campaign.id}: ${error.message}`,
        { campaignId: campaign.id, error: error.message },
      );

      return {
        success: false,
        previousStatus,
        error: `Failed to cancel campaign: ${error.message || 'Unknown error'}`,
      };
    }
  }

  /**
   * Handles campaign errors with retry logic
   */
  async handleError(
    campaign: Campaign,
    error: Error,
    options?: { maxRetries?: number },
  ): Promise<HandleErrorResult> {
    const maxRetries = options?.maxRetries || 5;
    const newRetryCount = (campaign.retryCount ?? 0) + 1;

    if (!this.canTransition(campaign.status, CampaignStatus.ERROR)) {
      return {
        success: false,
        previousStatus: campaign.status,
        error: `Invalid state transition from ${campaign.status} to ${CampaignStatus.ERROR}`,
      };
    }

    // Update metadata with error information
    const metadata: Record<string, any> = {
      ...(campaign.metadata || {}),
      lastError: error.message,
      lastErrorAt: new Date(),
      errorHistory: [
        ...(campaign.metadata?.errorHistory || []),
        {
          error: error.message,
          timestamp: new Date(),
          retryCount: newRetryCount,
        },
      ],
    };

    // Update campaign with ERROR status and incremented retry count
    await this.campaignRepository.manager.transaction(async manager => {
      await manager.update(
        Campaign,
        { id: campaign.id },
        {
          status: CampaignStatus.ERROR,
          retryCount: newRetryCount,
          metadata,
        },
      );
    });

    const shouldRetry = newRetryCount < maxRetries;

    campaign.status = CampaignStatus.ERROR;
    campaign.retryCount = newRetryCount;
    campaign.metadata = metadata;

    return {
      success: true,
      previousStatus: campaign.status,
      newStatus: CampaignStatus.ERROR,
      retryCount: newRetryCount,
      shouldRetry,
      suggestion: !shouldRetry ? 'Consider canceling the campaign' : undefined,
      timestamp: new Date(),
    };
  }

  /**
   * Initializes a campaign with payment processing
   */
  async initializeWithPayment(campaignData: CreateCampaignData): Promise<{
    campaign: Campaign;
    payment?: any;
    success: boolean;
    error?: string;
  }> {
    this.logger.log(
      `Initializing campaign with payment for property ${campaignData.propertyId}`,
    );

    try {
      // Create the campaign
      const campaign = await this.createCampaign(campaignData);

      // Initialize payment
      try {
        const payment =
          await this.paymentService.createInitialPayment(campaign);
        this.logger.log(
          `Payment initialized successfully for campaign ${campaign.id}`,
        );

        return {
          campaign,
          payment,
          success: true,
        };
      } catch (paymentError) {
        this.logger.error(
          `Payment initialization failed for campaign ${campaign.id}`,
          paymentError,
        );

        // Transition campaign to ERROR state
        await this.transitionState(campaign, CampaignStatus.ERROR);

        return {
          campaign,
          success: false,
          error: paymentError.message,
        };
      }
    } catch (error) {
      this.logger.error('Failed to initialize campaign with payment', error);
      throw error;
    }
  }

  /**
   * Processes a campaign with bundle generation
   */
  async processWithBundle(campaign: Campaign): Promise<{
    campaign: Campaign;
    bundleUri?: string;
    sqsMessageId?: string;
    success: boolean;
    error?: string;
  }> {
    this.logger.log(
      `Processing campaign ${campaign.id} with bundle generation`,
    );

    try {
      // Execute full bundle lifecycle
      const result =
        await this.bundleLifecycleService.executeFullLifecycle(campaign);

      this.logger.log(
        `Bundle lifecycle completed for campaign ${campaign.id}`,
        {
          bundleUri: result.bundleUri,
          sqsMessageId: result.sqsMessageId,
        },
      );

      // Transition to SUCCESS
      await this.transitionState(campaign, CampaignStatus.SUCCESS);

      return {
        campaign: result.campaign,
        bundleUri: result.bundleUri,
        sqsMessageId: result.sqsMessageId,
        success: true,
      };
    } catch (error) {
      this.logger.error(
        `Bundle processing failed for campaign ${campaign.id}`,
        error,
      );

      // Transition to ERROR state
      await this.handleError(campaign, error);
      campaign.status = CampaignStatus.ERROR;

      return {
        campaign,
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Retries a failed campaign using RetryService
   */
  async retryFailedCampaign(campaign: Campaign): Promise<{
    campaign: Campaign;
    success: boolean;
    retryCount?: number;
    error?: string;
  }> {
    const maxRetries = this.configService.get<number>('campaign.maxRetries', 5);

    if (campaign.retryCount >= maxRetries) {
      this.logger.warn(
        `Campaign ${campaign.id} has exceeded max retries (${maxRetries})`,
      );

      return {
        campaign,
        success: false,
        error: `Max retries exceeded (${maxRetries})`,
      };
    }

    this.logger.log(
      `Retrying failed campaign ${campaign.id} (attempt ${campaign.retryCount + 1})`,
    );

    try {
      // Use RetryService to handle retry logic
      const result = await this.retryService.execute(async () => {
        // Transition back to PENDING for retry
        await this.transitionState(campaign, CampaignStatus.PENDING);

        // Re-execute bundle lifecycle
        return await this.bundleLifecycleService.executeFullLifecycle(campaign);
      });

      // Update retry count
      campaign.retryCount += 1;
      await this.campaignRepository.update(
        { id: campaign.id },
        { retryCount: campaign.retryCount },
      );

      // Transition to SUCCESS
      await this.transitionState(campaign, CampaignStatus.SUCCESS);

      this.logger.log(
        `Campaign ${campaign.id} retry successful after ${campaign.retryCount} attempts`,
      );

      return {
        campaign: result.campaign,
        success: true,
        retryCount: campaign.retryCount,
      };
    } catch (error) {
      this.logger.error(`Retry failed for campaign ${campaign.id}`, error);

      // Update error state
      await this.handleError(campaign, error);

      return {
        campaign,
        success: false,
        retryCount: campaign.retryCount,
        error: error.message,
      };
    }
  }

  /**
   * Orchestrates the complete campaign flow
   */
  async orchestrateCampaignFlow(campaignData: CreateCampaignData): Promise<{
    campaign: Campaign;
    payment?: any;
    bundleUri?: string;
    sqsMessageId?: string;
    steps?: string[];
    failedAtStep?: string;
    success: boolean;
    error?: string;
  }> {
    const steps: string[] = [];

    this.logger.log(
      `Starting campaign orchestration for property ${campaignData.propertyId}`,
    );

    try {
      // Step 1: Create campaign
      steps.push('create');
      const campaign = await this.createCampaign(campaignData);

      // Step 2: Initialize payment
      steps.push('payment');
      let payment: any;
      try {
        payment = await this.paymentService.createInitialPayment(campaign);
        await this.paymentService.createSuccessPayment(payment);
        this.logger.log(
          `Payment processed successfully for campaign ${campaign.id}`,
        );
      } catch (paymentError) {
        this.logger.error(
          `Payment failed for campaign ${campaign.id}`,
          paymentError,
        );

        // Transition to ERROR and return
        await this.transitionState(campaign, CampaignStatus.ERROR);

        return {
          campaign,
          steps,
          failedAtStep: 'payment',
          success: false,
          error: paymentError.message,
        };
      }

      // Step 3: Generate and publish bundle
      steps.push('bundle');
      let bundleResult: any;
      try {
        bundleResult =
          await this.bundleLifecycleService.executeFullLifecycle(campaign);
        this.logger.log(
          `Bundle processed successfully for campaign ${campaign.id}`,
        );
      } catch (bundleError) {
        this.logger.error(
          `Bundle processing failed for campaign ${campaign.id}`,
          bundleError,
        );

        // Mark payment as failed
        await this.paymentService.createFailedPayment(
          payment,
          bundleError.message,
        );

        // Transition to ERROR and return
        await this.transitionState(campaign, CampaignStatus.ERROR);

        return {
          campaign,
          payment,
          steps,
          failedAtStep: 'bundle',
          success: false,
          error: bundleError.message,
        };
      }

      // Step 4: Complete campaign
      steps.push('complete');
      await this.transitionState(campaign, CampaignStatus.SUCCESS);

      this.logger.log(
        `Campaign orchestration completed successfully for campaign ${campaign.id}`,
      );

      return {
        campaign: bundleResult.campaign,
        payment,
        bundleUri: bundleResult.bundleUri,
        sqsMessageId: bundleResult.sqsMessageId,
        steps,
        success: true,
      };
    } catch (error) {
      this.logger.error('Campaign orchestration failed', error);

      // Return empty campaign object with error status
      const failedCampaign = {
        id: null,
        status: CampaignStatus.ERROR,
        propertyId: campaignData.propertyId,
      } as unknown as Campaign;

      return {
        campaign: failedCampaign,
        steps,
        failedAtStep: steps[steps.length - 1],
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Sends notifications for significant state transitions
   */
  private async sendStateTransitionNotifications(
    campaign: Campaign,
    previousStatus: CampaignStatus,
    newStatus: CampaignStatus,
  ): Promise<void> {
    try {
      // Only send notifications for significant transitions
      const significantTransitions = [
        { from: CampaignStatus.PENDING, to: CampaignStatus.SUCCESS },
        { from: CampaignStatus.PENDING, to: CampaignStatus.ERROR },
        { from: CampaignStatus.PENDING, to: CampaignStatus.CANCELED },
        { from: CampaignStatus.SUCCESS, to: CampaignStatus.CANCELED },
      ];

      const isSignificant = significantTransitions.some(
        transition =>
          transition.from === previousStatus && transition.to === newStatus,
      );

      if (!isSignificant) {
        return;
      }

      const workItem: NotificationWorkItem = {
        campaignId: campaign.id,
        propertyId: campaign.propertyId,
        bundleUri: campaign.bundleUri,
        createdAt: campaign.createdAt,
        currency: campaign.currency,
        priceCents: campaign.priceCents,
      };

      const notificationResults = await this.notificationContext.notifyAll(
        workItem,
        campaign,
      );

      this.logger.log(
        `Sent ${notificationResults.length} notifications for campaign ${campaign.id} state transition ${previousStatus} -> ${newStatus}`,
        {
          results: notificationResults.map(r => ({
            success: r.success,
            error: r.error,
          })),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send notifications for campaign ${campaign.id} state transition ${previousStatus} -> ${newStatus}:`,
        error,
      );
      // Don't throw - notifications are not critical to state transition success
    }
  }
}
