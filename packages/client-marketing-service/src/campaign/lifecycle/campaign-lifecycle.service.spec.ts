import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CampaignLifecycleService } from './campaign-lifecycle.service';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';
import { NotificationContext } from '../strategies/notification-context.service';

describe('CampaignLifecycleService', () => {
  let service: CampaignLifecycleService;
  let campaignRepository: jest.Mocked<Repository<Campaign>>;
  let paymentService: jest.Mocked<any>;
  let bundleLifecycleService: jest.Mocked<any>;
  let retryService: jest.Mocked<any>;
  let configService: jest.Mocked<ConfigService>;
  let mockManager: any;

  beforeEach(async () => {
    mockManager = {
      transaction: jest.fn(),
      update: jest.fn(),
    };

    // Mock PaymentService
    paymentService = {
      createInitialPayment: jest.fn(),
      createSuccessPayment: jest.fn(),
      createFailedPayment: jest.fn(),
      findAllPaymentsForCampaign: jest.fn(),
    };

    // Mock BundleLifecycleService
    bundleLifecycleService = {
      generatePromoBundle: jest.fn(),
      uploadBundle: jest.fn(),
      publishBundle: jest.fn(),
      executeFullLifecycle: jest.fn(),
    };

    // Mock RetryService
    retryService = {
      execute: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignLifecycleService,
        {
          provide: getRepositoryToken(Campaign),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            manager: mockManager,
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: 'PaymentService',
          useValue: paymentService,
        },
        {
          provide: 'BundleLifecycleService',
          useValue: bundleLifecycleService,
        },
        {
          provide: 'RetryService',
          useValue: retryService,
        },
        {
          provide: NotificationContext,
          useValue: {
            notifyAll: jest.fn().mockResolvedValue([]),
          },
        },
      ],
    }).compile();

    service = module.get<CampaignLifecycleService>(CampaignLifecycleService);
    campaignRepository = module.get(getRepositoryToken(Campaign));
    configService = module.get(ConfigService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create an instance with required dependencies', () => {
      expect(service).toBeDefined();
    });
  });

  describe('state transitions', () => {
    it('should return valid transitions for PENDING status via public API', () => {
      const validTransitions = service.getValidTransitions(
        CampaignStatus.PENDING,
      );

      expect(validTransitions).toBeDefined();
      expect(validTransitions).toBeInstanceOf(Array);
      expect(validTransitions).toContain(CampaignStatus.SUCCESS);
      expect(validTransitions).toContain(CampaignStatus.ERROR);
      expect(validTransitions).toContain(CampaignStatus.CANCELED);
    });
  });

  describe('canTransition', () => {
    it('should return true for valid transitions', () => {
      expect(
        service.canTransition(CampaignStatus.PENDING, CampaignStatus.SUCCESS),
      ).toBe(true);
    });

    it('should return false for self-transitions', () => {
      expect(
        service.canTransition(CampaignStatus.PENDING, CampaignStatus.PENDING),
      ).toBe(false);
      expect(
        service.canTransition(CampaignStatus.SUCCESS, CampaignStatus.SUCCESS),
      ).toBe(false);
    });
  });

  describe('transitionState', () => {
    it('should handle null campaign gracefully', async () => {
      const result = await service.transitionState(
        null as any,
        CampaignStatus.SUCCESS,
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.previousStatus).toBeUndefined();
    });

    it('should reject invalid state transitions', async () => {
      const mockCampaign = {
        id: 'test-id',
        status: CampaignStatus.SUCCESS, // Terminal state
      } as Campaign;

      const result = await service.transitionState(
        mockCampaign,
        CampaignStatus.PENDING,
      );

      expect(result.success).toBe(false);
      expect(result.previousStatus).toBe(CampaignStatus.SUCCESS);
      expect(result.error).toContain('Invalid state transition');
      expect(result.error).toContain('SUCCESS to PENDING');
    });

    it('should successfully transition with database update', async () => {
      const mockCampaign = {
        id: 'test-campaign-id',
        status: CampaignStatus.PENDING,
      } as Campaign;

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.transitionState(
        mockCampaign,
        CampaignStatus.SUCCESS,
      );

      expect(result.success).toBe(true);
      expect(result.previousStatus).toBe(CampaignStatus.PENDING);
      expect(result.newStatus).toBe(CampaignStatus.SUCCESS);
      expect(result.timestamp).toBeInstanceOf(Date);
      expect(mockManager.transaction).toHaveBeenCalled();
      expect(mockManager.update).toHaveBeenCalledWith(
        Campaign,
        { id: 'test-campaign-id' },
        { status: CampaignStatus.SUCCESS },
      );
      expect(mockCampaign.status).toBe(CampaignStatus.SUCCESS);
    });

    it('should handle database errors gracefully', async () => {
      const mockCampaign = {
        id: 'test-campaign-id',
        status: CampaignStatus.PENDING,
      } as Campaign;

      const dbError = new Error('Database connection failed');
      mockManager.transaction.mockRejectedValue(dbError);

      const result = await service.transitionState(
        mockCampaign,
        CampaignStatus.SUCCESS,
      );

      expect(result.success).toBe(false);
      expect(result.previousStatus).toBe(CampaignStatus.PENDING);
      expect(result.error).toContain('Failed to transition state');
      expect(result.error).toContain('Database connection failed');
    });

    it('should reject self-transitions even when skipValidation is true', async () => {
      const mockCampaign = {
        id: 'test-campaign-id',
        status: CampaignStatus.PENDING,
      } as Campaign;

      const result = await service.transitionState(
        mockCampaign,
        CampaignStatus.PENDING,
        { skipValidation: true },
      );

      expect(result.success).toBe(false);
      expect(result.previousStatus).toBe(CampaignStatus.PENDING);
      expect(result.error).toBe(
        'Self transition from PENDING to PENDING is not allowed',
      );
    });
  });

  describe('getCurrentState', () => {
    it('should return campaign status when campaign exists', async () => {
      const mockCampaign = {
        id: 'test-id',
        status: CampaignStatus.SUCCESS,
      };
      campaignRepository.findOne.mockResolvedValue(mockCampaign as Campaign);

      const result = await service.getCurrentState('test-id');

      expect(result).toBe(CampaignStatus.SUCCESS);
      expect(campaignRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        select: ['id', 'status'],
      });
    });
  });

  describe('getValidTransitions', () => {
    it('should return empty array for invalid status', () => {
      const transitions = service.getValidTransitions(undefined as any);
      expect(transitions).toEqual([]);
    });
  });

  describe('createCampaign', () => {
    it('should create a new campaign with PENDING status', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockSavedCampaign = {
        id: 'generated-uuid',
        ...campaignData,
        status: CampaignStatus.PENDING,
        retryCount: 0,
        metadata: null,
        bundleUri: null,
        sqsMessageId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      campaignRepository.save.mockResolvedValue(mockSavedCampaign as Campaign);

      const result = await service.createCampaign(campaignData);

      expect(result).toEqual(mockSavedCampaign);
      expect(campaignRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          ...campaignData,
          status: CampaignStatus.PENDING,
          retryCount: 0,
          metadata: null,
        }),
      );
    });

    it('should create campaign with optional metadata', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
        metadata: { source: 'api', region: 'us-east' },
      };

      const mockSavedCampaign = {
        id: 'generated-uuid',
        ...campaignData,
        status: CampaignStatus.PENDING,
        retryCount: 0,
        bundleUri: null,
        sqsMessageId: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      campaignRepository.save.mockResolvedValue(mockSavedCampaign as Campaign);

      const result = await service.createCampaign(campaignData);

      expect(result.metadata).toEqual(campaignData.metadata);
      expect(campaignRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: campaignData.metadata,
        }),
      );
    });

    it('should handle database errors during creation', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const dbError = new Error('Database constraint violation');
      campaignRepository.save.mockRejectedValue(dbError);

      await expect(service.createCampaign(campaignData)).rejects.toThrow(
        'Failed to create campaign: Database constraint violation',
      );
    });
  });

  describe('processCampaign', () => {
    it('should process campaign and transition from PENDING to SUCCESS', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        propertyId: 'property-123',
      } as Campaign;

      // Mock the processing workflow methods that will be called
      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.processCampaign(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.processingTime).toBeGreaterThanOrEqual(0);
    });

    it('should propagate transition failure', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
      } as Campaign;
      mockManager.transaction.mockRejectedValue(new Error('DB fail'));
      const result = await service.processCampaign(mockCampaign);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Fail');
    });
  });

  describe('completeCampaign', () => {
    it('should complete campaign with validation', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        bundleUri: 'https://example.com/bundle.zip',
        sqsMessageId: 'sqs-123',
      } as Campaign;

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.completeCampaign(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.previousStatus).toBe(CampaignStatus.PENDING);
      expect(result.newStatus).toBe(CampaignStatus.SUCCESS);
    });
  });

  describe('cancelCampaign', () => {
    it('should cancel campaign with reason', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        metadata: {},
      } as Campaign;

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.cancelCampaign(
        mockCampaign,
        'User requested cancellation',
      );

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(CampaignStatus.CANCELED);
    });
  });

  describe('handleError', () => {
    it('should handle error and increment retry count', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        retryCount: 0,
        metadata: {},
      } as Campaign;

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const error = new Error('Connection timeout');
      const result = await service.handleError(mockCampaign, error);

      expect(result.success).toBe(true);
      expect(result.newStatus).toBe(CampaignStatus.ERROR);
      expect(result.retryCount).toBe(1);
      expect(result.shouldRetry).toBe(true);
    });
  });

  // Integration tests for new methods
  describe('initializeWithPayment', () => {
    it('should create campaign and initialize payment', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        ...campaignData,
        status: CampaignStatus.PENDING,
      };

      const mockPayment = {
        id: 'payment-123',
        campaignId: 'campaign-123',
        status: 'PENDING',
      };

      campaignRepository.save.mockResolvedValue(mockCampaign as Campaign);
      paymentService.createInitialPayment.mockResolvedValue(mockPayment);

      const result = await service.initializeWithPayment(campaignData);

      expect(result.campaign).toEqual(mockCampaign);
      expect(result.payment).toEqual(mockPayment);
      expect(result.success).toBe(true);
      expect(campaignRepository.save).toHaveBeenCalled();
      expect(paymentService.createInitialPayment).toHaveBeenCalledWith(
        mockCampaign,
      );
    });

    it('should handle payment service errors', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        ...campaignData,
        status: CampaignStatus.PENDING,
      };

      campaignRepository.save.mockResolvedValue(mockCampaign as Campaign);
      paymentService.createInitialPayment.mockRejectedValue(
        new Error('Payment service unavailable'),
      );

      // Should transition campaign to ERROR state on payment failure
      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.initializeWithPayment(campaignData);

      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toContain('Payment service unavailable');
    });
  });

  describe('processWithBundle', () => {
    it('should process campaign with bundle generation', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        propertyId: 'property-123',
      } as Campaign;

      const processedCampaign = {
        ...mockCampaign,
        bundleUri: 'https://s3.example.com/bundle.zip',
        sqsMessageId: 'sqs-msg-123',
        status: CampaignStatus.SUCCESS,
      };

      bundleLifecycleService.executeFullLifecycle.mockResolvedValue({
        campaign: processedCampaign,
        bundleUri: 'https://s3.example.com/bundle.zip',
        sqsMessageId: 'sqs-msg-123',
      });

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.processWithBundle(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.campaign).toEqual(processedCampaign);
      expect(result.bundleUri).toBe('https://s3.example.com/bundle.zip');
      expect(result.sqsMessageId).toBe('sqs-msg-123');
      expect(bundleLifecycleService.executeFullLifecycle).toHaveBeenCalledWith(
        mockCampaign,
      );
    });

    it('should handle bundle service errors', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.PENDING,
        propertyId: 'property-123',
      } as Campaign;

      bundleLifecycleService.executeFullLifecycle.mockRejectedValue(
        new Error('S3 upload failed'),
      );

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.processWithBundle(mockCampaign);

      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toContain('S3 upload failed');
    });
  });

  describe('retryFailedCampaign', () => {
    it('should retry failed campaign using RetryService', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.ERROR,
        retryCount: 2,
        propertyId: 'property-123',
      } as Campaign;

      const successCampaign = {
        ...mockCampaign,
        status: CampaignStatus.SUCCESS,
        bundleUri: 'https://s3.example.com/bundle.zip',
      };

      retryService.execute.mockImplementation(async callback => {
        const result = await callback();
        return result;
      });

      bundleLifecycleService.executeFullLifecycle.mockResolvedValue({
        campaign: successCampaign,
        bundleUri: 'https://s3.example.com/bundle.zip',
        sqsMessageId: 'sqs-123',
      });

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.retryFailedCampaign(mockCampaign);

      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.retryCount).toBe(3);
      expect(retryService.execute).toHaveBeenCalled();
    });

    it('should handle max retries exceeded', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.ERROR,
        retryCount: 5, // Already at max retries
        propertyId: 'property-123',
      } as Campaign;

      // Mock configService to return maxRetries
      (configService.get as jest.Mock).mockReturnValue(5);

      const result = await service.retryFailedCampaign(mockCampaign);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Max retries exceeded');
      expect(retryService.execute).not.toHaveBeenCalled();
    });

    it('should handle retry service errors', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        status: CampaignStatus.ERROR,
        retryCount: 2,
        propertyId: 'property-123',
      } as Campaign;

      retryService.execute.mockRejectedValue(
        new Error('All retry attempts failed'),
      );

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.retryFailedCampaign(mockCampaign);

      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toContain('All retry attempts failed');
    });
  });

  describe('orchestrateCampaignFlow', () => {
    it('should orchestrate complete campaign flow', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        ...campaignData,
        status: CampaignStatus.PENDING,
      };

      const mockPayment = {
        id: 'payment-123',
        status: 'SUCCESS',
      };

      const processedCampaign = {
        ...mockCampaign,
        status: CampaignStatus.SUCCESS,
        bundleUri: 'https://s3.example.com/bundle.zip',
        sqsMessageId: 'sqs-123',
      };

      // Mock the flow
      campaignRepository.save.mockResolvedValue(mockCampaign as Campaign);
      paymentService.createInitialPayment.mockResolvedValue(mockPayment);
      paymentService.createSuccessPayment.mockResolvedValue({
        ...mockPayment,
        status: 'SUCCESS',
      });
      bundleLifecycleService.executeFullLifecycle.mockResolvedValue({
        campaign: processedCampaign,
        bundleUri: 'https://s3.example.com/bundle.zip',
        sqsMessageId: 'sqs-123',
      });

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.orchestrateCampaignFlow(campaignData);

      expect(result.success).toBe(true);
      expect(result.campaign.status).toBe(CampaignStatus.SUCCESS);
      expect(result.payment).toBeDefined();
      expect(result.bundleUri).toBe('https://s3.example.com/bundle.zip');
      expect(result.sqsMessageId).toBe('sqs-123');
      expect(result.steps).toEqual(['create', 'payment', 'bundle', 'complete']);
    });

    it('should handle errors at payment step', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        ...campaignData,
        status: CampaignStatus.PENDING,
      };

      campaignRepository.save.mockResolvedValue(mockCampaign as Campaign);
      paymentService.createInitialPayment.mockRejectedValue(
        new Error('Payment declined'),
      );

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.orchestrateCampaignFlow(campaignData);

      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toContain('Payment declined');
      expect(result.failedAtStep).toBe('payment');
    });

    it('should handle errors at bundle step', async () => {
      const campaignData = {
        propertyId: 'property-123',
        priceCents: 10000,
        currency: 'USD',
        createdBy: 'user-123',
      };

      const mockCampaign = {
        id: 'campaign-123',
        ...campaignData,
        status: CampaignStatus.PENDING,
      };

      const mockPayment = {
        id: 'payment-123',
        status: 'SUCCESS',
      };

      campaignRepository.save.mockResolvedValue(mockCampaign as Campaign);
      paymentService.createInitialPayment.mockResolvedValue(mockPayment);
      paymentService.createSuccessPayment.mockResolvedValue(mockPayment);
      bundleLifecycleService.executeFullLifecycle.mockRejectedValue(
        new Error('Bundle generation failed'),
      );

      mockManager.transaction.mockImplementation(callback =>
        callback(mockManager),
      );
      mockManager.update.mockResolvedValue({ affected: 1 });

      const result = await service.orchestrateCampaignFlow(campaignData);

      expect(result.success).toBe(false);
      expect(result.campaign.status).toBe(CampaignStatus.ERROR);
      expect(result.error).toContain('Bundle generation failed');
      expect(result.failedAtStep).toBe('bundle');
      expect(paymentService.createFailedPayment).toHaveBeenCalled();
    });
  });
});
