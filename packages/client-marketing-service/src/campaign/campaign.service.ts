import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, DataSource } from 'typeorm';

import { CampaignStrategyFactory } from './campaign-strategy.factory';
import { CreateCampaignWithPaymentDto } from './dto/create-campaign-with-payment.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { Campaign, CampaignStatus } from './entities/campaign.entity';
import { CampaignOrchestrationContext } from './interfaces/campaign-orchestration.interface';
import { NotificationWorkItem } from './interfaces/notification-strategy.interface';
import { CampaignPaymentService } from './payment/campaign-payment.service';
import { NotificationContext } from './strategies/notification-context.service';
import {
  PaymentProvider,
  PaymentStatus,
} from '../payment/entities/payment.entity';

@Injectable()
export class CampaignService {
  private readonly logger = new Logger(CampaignService.name);

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    private readonly dataSource: DataSource,
    private readonly campaignStrategyFactory: CampaignStrategyFactory,
    private readonly campaignPaymentService: CampaignPaymentService,
    private readonly notificationContext: NotificationContext,
  ) {}

  async findAll(): Promise<Campaign[]> {
    return this.campaignRepository.find({
      relations: ['payments'],
    });
  }

  async findOne(id: string): Promise<Campaign> {
    const campaign = await this.campaignRepository.findOne({
      where: { id },
      relations: ['payments'],
    });

    if (!campaign) {
      throw new NotFoundException(`Campaign with ID "${id}" not found`);
    }

    return campaign;
  }

  create(): never {
    // Disallowed by design: campaigns must be created via createWithPayment()
    throw new BadRequestException(
      'Direct campaign creation is not allowed. Use createWithPayment() instead to ensure campaigns are always created with payments.',
    );
  }

  async createWithPayment(
    createCampaignWithPaymentDto: CreateCampaignWithPaymentDto & {
      createdBy: string;
    },
  ): Promise<Campaign> {
    // Get the configured strategy from environment
    const strategy = this.campaignStrategyFactory.getStrategy();
    const orchestrationMode = strategy.getMode();

    this.logger.log(
      `Creating campaign with payment using ${orchestrationMode} orchestration: ${JSON.stringify(
        {
          propertyId: createCampaignWithPaymentDto.propertyId,
          priceCents: createCampaignWithPaymentDto.priceCents,
          currency: createCampaignWithPaymentDto.currency,
          orchestrationMode,
          hasTestCard: !!createCampaignWithPaymentDto.testCardNumber, // Log presence, not value
        },
      )}`,
    );

    // Step 1: Create campaign with PENDING status in its own transaction
    let savedCampaign: Campaign | null = null;

    try {
      savedCampaign = await this.dataSource.transaction(async manager => {
        // Create campaign with only valid Campaign entity fields
        const campaign = manager.getRepository(Campaign).create({
          propertyId: createCampaignWithPaymentDto.propertyId,
          priceCents: createCampaignWithPaymentDto.priceCents,
          currency: createCampaignWithPaymentDto.currency || 'USD',
          createdBy: createCampaignWithPaymentDto.createdBy,
          status: CampaignStatus.PENDING, // Initial status
        });

        const saved = await manager.getRepository(Campaign).save(campaign);

        this.logger.log(
          `Created initial campaign ${saved.id} for orchestration`,
        );

        return saved;
      });
      if (!savedCampaign) {
        throw new Error(
          'Failed to create campaign record before orchestration',
        );
      }

      // Step 2: Execute orchestration strategy with the committed campaign
      // This happens outside the campaign creation transaction
      const orchestrationContext: CampaignOrchestrationContext = {
        campaign: savedCampaign,
        testCardNumber: createCampaignWithPaymentDto.testCardNumber,
        metadata: {
          createdBy: createCampaignWithPaymentDto.createdBy,
          initialStatus: savedCampaign.status,
        },
      };

      const orchestrationResult = await strategy.execute(orchestrationContext);

      // Step 3: Update campaign with orchestration results in a separate transaction
      const finalCampaign = await this.dataSource.transaction(async manager => {
        // Reload the campaign to ensure we have the latest version
        const reloadedCampaign = await manager.getRepository(Campaign).findOne({
          where: { id: savedCampaign!.id },
        });

        if (!reloadedCampaign) {
          throw new Error(
            `Campaign ${savedCampaign!.id} not found during status update`,
          );
        }

        // Update campaign with orchestration results
        reloadedCampaign.status = orchestrationResult.campaign.status;
        reloadedCampaign.bundleUri = orchestrationResult.campaign.bundleUri;

        // Store orchestration metadata in the campaign
        reloadedCampaign.metadata = {
          ...(reloadedCampaign.metadata ?? {}),
          orchestration: {
            mode: orchestrationResult.orchestrationMode,
            success: orchestrationResult.success,
            error: orchestrationResult.error,
            timestamp: new Date().toISOString(),
            ...orchestrationResult.metadata,
          },
        };

        const finalSavedCampaign = await manager
          .getRepository(Campaign)
          .save(reloadedCampaign);

        this.logger.log(
          `Campaign ${finalSavedCampaign.id} orchestration completed with status ${finalSavedCampaign.status}`,
          {
            orchestrationMode,
            success: orchestrationResult.success,
            error: orchestrationResult.error,
          },
        );

        // Send notifications after successful orchestration
        await this.sendCampaignNotifications(finalSavedCampaign);

        return finalSavedCampaign;
      });

      return this.findOne(finalCampaign.id);
    } catch (error) {
      this.logger.error(
        'Campaign creation and orchestration failed with unexpected error',
        {
          propertyId: createCampaignWithPaymentDto.propertyId,
          campaignId: savedCampaign?.id,
          error: error.message,
          stack: error.stack,
        },
      );

      // If we have a created campaign, try to mark it as ERROR in a separate transaction
      if (savedCampaign) {
        try {
          await this.dataSource.transaction(async manager => {
            const reloadedCampaign = await manager
              .getRepository(Campaign)
              .findOne({
                where: { id: savedCampaign!.id },
              });

            if (
              reloadedCampaign &&
              reloadedCampaign.status === CampaignStatus.PENDING
            ) {
              reloadedCampaign.status = CampaignStatus.ERROR;
              reloadedCampaign.metadata = {
                ...(reloadedCampaign.metadata ?? {}),
                orchestration: {
                  mode: orchestrationMode,
                  success: false,
                  error: error.message,
                  errorStack: error.stack,
                  timestamp: new Date().toISOString(),
                  failureStage: 'orchestration',
                },
              };

              await manager.getRepository(Campaign).save(reloadedCampaign);

              this.logger.log(
                `Marked campaign ${savedCampaign!.id} as ERROR after orchestration failure`,
              );
            }
          });
        } catch (updateError) {
          this.logger.error(
            'Failed to update campaign status to ERROR after orchestration failure',
            {
              campaignId: savedCampaign.id,
              originalError: error.message,
              updateError: updateError.message,
            },
          );
        }
      }

      // Rethrow the original error
      throw error;
    }
  }

  async update(
    id: string,
    updateCampaignDto: UpdateCampaignDto,
  ): Promise<Campaign> {
    this.logger.log(`Updating campaign with ID "${id}"`, updateCampaignDto);

    const campaign = await this.findOne(id);

    // Update only defined fields from DTO
    const updates = Object.fromEntries(
      Object.entries(updateCampaignDto).filter(([, v]) => v !== undefined),
    );
    Object.assign(campaign, updates);

    await this.campaignRepository.save(campaign);
    return this.findOne(id);
  }

  async delete(id: string): Promise<void> {
    this.logger.log(
      `Soft deleting campaign with ID "${id}" - setting status to CANCELED`,
    );

    const campaign = await this.findOne(id);

    // Soft delete: change status to CANCELED instead of deleting
    campaign.status = CampaignStatus.CANCELED;

    await this.campaignRepository.save(campaign);

    // NOTE: Campaign-payment links and payments are intentionally kept intact
    // They serve as a complete audit log of the campaign and all payment attempts
  }

  async findActiveByPropertyId(propertyId: string): Promise<Campaign | null> {
    return this.campaignRepository.findOne({
      where: {
        propertyId,
        status: In([CampaignStatus.PENDING, CampaignStatus.SUCCESS]),
      },
    });
  }

  async findActiveByPropertyIds(propertyIds: string[]): Promise<Campaign[]> {
    return this.campaignRepository.find({
      where: {
        propertyId: In(propertyIds),
        status: In([CampaignStatus.PENDING, CampaignStatus.SUCCESS]),
      },
    });
  }

  async createPaymentRetry(
    campaignId: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    paymentData: {
      provider: PaymentProvider;
      amountCents: number;
      currency: string;
      status: PaymentStatus;
      providerRef?: string;
    },
  ): Promise<Campaign> {
    // Use the new CampaignPaymentService stub for retries
    const result = await this.campaignPaymentService.createPaymentRetry(
      campaignId,
      'Manual retry attempt',
    );

    if (!result.success) {
      this.logger.warn(`Payment retry not supported in alpha: ${result.error}`);
    }

    return this.findOne(campaignId);
  }

  async createPaymentRefund(
    campaignId: string,
    refundData: {
      provider: PaymentProvider;
      amountCents: number; // Should be negative for refunds
      currency: string;
      providerRef?: string;
    },
  ): Promise<Campaign> {
    // Use the new CampaignPaymentService stub for refunds
    const result = await this.campaignPaymentService.createPaymentRefund(
      campaignId,
      refundData.amountCents,
      'Manual refund request',
    );

    if (!result.success) {
      this.logger.warn(
        `Payment refund not supported in alpha: ${result.error}`,
      );
    }

    return this.findOne(campaignId);
  }

  /**
   * Sends notifications for campaign orchestration completion
   */
  private async sendCampaignNotifications(campaign: Campaign): Promise<void> {
    try {
      const workItem: NotificationWorkItem = {
        campaignId: campaign.id,
        propertyId: campaign.propertyId,
        bundleUri: campaign.bundleUri,
        createdAt: campaign.createdAt,
        currency: campaign.currency,
        priceCents: campaign.priceCents,
      };

      const notificationResults = await this.notificationContext.notifyAll(
        workItem,
        campaign,
      );

      this.logger.log(
        `Sent ${notificationResults.length} notifications for campaign ${campaign.id}`,
        {
          results: notificationResults.map(r => ({
            success: r.success,
            error: r.error,
          })),
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send notifications for campaign ${campaign.id}:`,
        error,
      );
      // Don't throw - notifications are not critical to campaign success
    }
  }
}
