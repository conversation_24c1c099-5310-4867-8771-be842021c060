import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CampaignWatchdogService } from './campaign-watchdog.service';
import { Campaign, CampaignStatus } from './entities/campaign.entity';
import { CampaignLifecycleService } from './lifecycle/campaign-lifecycle.service';
import { RetryService } from '../common/services/retry/retry.service';

describe('CampaignWatchdogService', () => {
  let service: CampaignWatchdogService;
  let repository: jest.Mocked<Repository<Campaign>>;
  let lifecycleService: jest.Mocked<CampaignLifecycleService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignWatchdogService,
        {
          provide: CampaignLifecycleService,
          useValue: {
            processWithBundle: jest.fn().mockResolvedValue({
              campaign: {} as Campaign,
              success: true,
            }),
            handleError: jest.fn().mockResolvedValue({
              success: true,
              previousStatus: CampaignStatus.PENDING,
              newStatus: CampaignStatus.ERROR,
              retryCount: 5,
              shouldRetry: false,
            }),
            transitionState: jest.fn().mockResolvedValue({
              success: true,
              previousStatus: CampaignStatus.PENDING,
              newStatus: CampaignStatus.ERROR,
            }),
          },
        },
        {
          provide: getRepositoryToken(Campaign),
          useValue: {
            find: jest.fn(),
            save: jest.fn(),
            increment: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('5'),
          },
        },
        {
          provide: RetryService,
          useValue: {
            execute: jest.fn().mockImplementation(async fn => await fn()),
          },
        },
      ],
    }).compile();

    service = module.get(CampaignWatchdogService);
    repository = module.get(getRepositoryToken(Campaign));
    lifecycleService = module.get(CampaignLifecycleService);
  });

  it('should retry upload and publish for campaigns without bundleUri', async () => {
    const campaign = {
      id: '1',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 0,
      bundleUri: null,
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    lifecycleService.processWithBundle.mockResolvedValue({
      campaign: {
        ...campaign,
        bundleUri: 'uri',
        status: CampaignStatus.SUCCESS,
      },
      bundleUri: 'uri',
      success: true,
    });

    await service.processStuckCampaigns();

    expect(repository.increment).toHaveBeenCalledWith(
      { id: '1', status: CampaignStatus.PENDING },
      'retryCount',
      1,
    );
    expect(lifecycleService.processWithBundle).toHaveBeenCalled();
  });

  it('should publish campaigns that already have bundleUri', async () => {
    const campaign = {
      id: '2',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 0,
      bundleUri: 'uri',
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    lifecycleService.processWithBundle.mockResolvedValue({
      campaign: { ...campaign, status: CampaignStatus.SUCCESS },
      bundleUri: 'uri',
      success: true,
    });

    await service.processStuckCampaigns();

    expect(lifecycleService.processWithBundle).toHaveBeenCalledWith(
      expect.objectContaining({ id: '2', bundleUri: 'uri', retryCount: 1 }),
    );
  });

  it('should mark campaign as ERROR after reaching max retries', async () => {
    const campaign = {
      id: '3',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 4,
      bundleUri: 'uri',
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    lifecycleService.processWithBundle.mockRejectedValue(new Error('failed'));
    lifecycleService.handleError.mockResolvedValue({
      success: true,
      previousStatus: CampaignStatus.PENDING,
      newStatus: CampaignStatus.ERROR,
      retryCount: 5,
      shouldRetry: false,
    });

    await service.processStuckCampaigns();

    expect(repository.increment).toHaveBeenCalledWith(
      { id: '3', status: CampaignStatus.PENDING },
      'retryCount',
      1,
    );
    expect(lifecycleService.handleError).toHaveBeenCalledWith(
      expect.objectContaining({ id: '3' }),
      expect.any(Error),
      expect.objectContaining({ maxRetries: 5 }),
    );
  });

  it('should process campaigns with lifecycle service', async () => {
    const campaign = {
      id: '4',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 2,
      bundleUri: 'uri',
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    lifecycleService.processWithBundle.mockResolvedValue({
      campaign: { ...campaign, status: CampaignStatus.SUCCESS },
      success: true,
    });

    await service.processStuckCampaigns();

    // Verify lifecycle service is used instead of retry service
    expect(lifecycleService.processWithBundle).toHaveBeenCalledWith(
      expect.objectContaining({ id: '4' }),
    );
  });

  it('should properly process campaign and update with bundle URI', async () => {
    const campaign = {
      id: '5',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 0,
      bundleUri: null,
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    // Mock lifecycle service to return updated campaign with bundleUri
    lifecycleService.processWithBundle.mockResolvedValue({
      campaign: {
        ...campaign,
        bundleUri: 's3://bucket/bundle.json',
        status: CampaignStatus.SUCCESS,
      },
      bundleUri: 's3://bucket/bundle.json',
      sqsMessageId: 'msg-123',
      success: true,
    });

    await service.processStuckCampaigns();

    expect(lifecycleService.processWithBundle).toHaveBeenCalledWith(
      expect.objectContaining({ id: '5', retryCount: 1 }),
    );
  });

  it('should use lifecycle service processWithBundle for campaign processing', async () => {
    const campaign = {
      id: '6',
      status: CampaignStatus.PENDING,
      createdAt: new Date(Date.now() - 10 * 60 * 1000),
      retryCount: 0,
      bundleUri: null,
    } as Campaign;

    repository.find.mockResolvedValue([campaign]);
    repository.increment.mockResolvedValue({ affected: 1 } as any);

    lifecycleService.processWithBundle.mockResolvedValue({
      campaign: {
        ...campaign,
        bundleUri: 's3://bucket/bundle.json',
        status: CampaignStatus.SUCCESS,
      },
      bundleUri: 's3://bucket/bundle.json',
      success: true,
    });

    await service.processStuckCampaigns();

    expect(lifecycleService.processWithBundle).toHaveBeenCalledWith(
      expect.objectContaining({ id: '6', retryCount: 1 }),
    );
  });
});
