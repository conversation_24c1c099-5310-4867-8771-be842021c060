import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { UnauthorizedException } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';

import { AppPolicyRegistry } from '../auth.module';
import { AuthContext } from '../graphql.decorator';
import { CampaignService } from './campaign.service';
import { CreateCampaignWithPaymentDto } from './dto/create-campaign-with-payment.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { Campaign } from './entities/campaign.entity';

@Resolver(() => Campaign)
export class CampaignResolver {
  constructor(private readonly campaignService: CampaignService) {}

  @Query(() => [Campaign], {
    description: 'Fetches all campaigns.',
  })
  async campaigns() {
    return this.campaignService.findAll();
  }

  @Query(() => Campaign, {
    description: 'Fetches a campaign by ID.',
  })
  async campaign(@Args('id', { type: () => ID }) id: string) {
    return this.campaignService.findOne(id);
  }

  @Mutation(() => Campaign, {
    name: 'createCampaignWithPayment',
    description:
      'Create a new campaign with payment (campaigns require payments).',
  })
  async createCampaignWithPayment(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('input') input: CreateCampaignWithPaymentDto,
  ) {
    // Get the authenticated user ID from the context
    const userId = authContext.getUserId();
    if (!userId) {
      throw new UnauthorizedException('Missing authenticated user');
    }

    // Create a new object with the authenticated user ID, ignoring any client-provided createdBy
    const campaignData = {
      ...input,
      createdBy: userId,
    };

    return this.campaignService.createWithPayment(campaignData);
  }

  @Mutation(() => Campaign, {
    name: 'updateCampaign',
    description: 'Update an existing campaign.',
  })
  async updateCampaign(
    @Args('id', { type: () => ID }) id: string,
    @Args('input') input: UpdateCampaignDto,
  ) {
    return this.campaignService.update(id, input);
  }

  @Mutation(() => Boolean, {
    name: 'deleteCampaign',
    description: 'Delete a campaign.',
  })
  async deleteCampaign(@Args('id', { type: () => ID }) id: string) {
    await this.campaignService.delete(id);
    return true;
  }
}
