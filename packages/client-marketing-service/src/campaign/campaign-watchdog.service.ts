// Updated campaign-watchdog.service.ts to use CampaignLifecycleService
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThan, Repository } from 'typeorm';

import { Campaign, CampaignStatus } from './entities/campaign.entity';
import { CampaignLifecycleService } from './lifecycle/campaign-lifecycle.service';

const FIVE_MINUTES_MS = 5 * 60 * 1000;

@Injectable()
export class CampaignWatchdogService {
  private readonly logger = new Logger(CampaignWatchdogService.name);
  private readonly maxRetries: number;
  private isRunning = false;

  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    private readonly lifecycleService: CampaignLifecycleService,
    configService: ConfigService,
  ) {
    this.maxRetries = parseInt(
      configService.get<string>('campaign.maxRetries', '5'),
      10,
    );
    this.logger.log(
      `Watchdog initialized with max retries: ${this.maxRetries}`,
    );
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    disabled: process.env.NODE_ENV === 'test',
  })
  async processStuckCampaigns(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Watchdog run skipped: previous run still in progress');
      return;
    }
    this.isRunning = true;
    try {
      await this.runWatchdog();
    } catch (error) {
      this.logger.error(
        'Watchdog run failed',
        error instanceof Error ? error.stack : String(error),
      );
    } finally {
      this.isRunning = false;
    }
  }

  private async runWatchdog(): Promise<void> {
    const cutoff = new Date(Date.now() - FIVE_MINUTES_MS);
    const stuckCampaigns = await this.campaignRepository.find({
      where: {
        status: CampaignStatus.PENDING,
        createdAt: LessThan(cutoff),
        retryCount: LessThan(this.maxRetries),
      },
    });

    this.logger.log(`Found ${stuckCampaigns.length} stuck campaigns`);

    for (const campaign of stuckCampaigns) {
      await this.processCampaign(campaign);
    }
  }

  private async processCampaign(campaign: Campaign): Promise<void> {
    this.logger.log(`Processing stuck campaign ${campaign.id}`, {
      campaignId: campaign.id,
      retryCount: campaign.retryCount,
    });

    // Atomic increment guarded by status to avoid lost updates
    const res = await this.campaignRepository.increment(
      { id: campaign.id, status: CampaignStatus.PENDING },
      'retryCount',
      1,
    );
    if (!res.affected) {
      // Someone else progressed this campaign; skip
      this.logger.debug(`Campaign ${campaign.id} already being processed`);
      return;
    }
    campaign.retryCount += 1; // keep local object in sync

    try {
      // Use CampaignLifecycleService for processing
      this.logger.debug(
        `Using lifecycle service to process campaign ${campaign.id}`,
      );
      const result = await this.lifecycleService.processWithBundle(campaign);

      if (result.success) {
        this.logger.log(`Successfully processed campaign ${campaign.id}`, {
          campaignId: campaign.id,
          bundleUri: result.bundleUri,
          sqsMessageId: result.sqsMessageId,
        });
      } else {
        throw new Error(result.error || 'Processing failed');
      }
    } catch (error) {
      this.logger.error(
        `Failed to process campaign ${campaign.id} after retries`,
        error instanceof Error ? error.stack : error,
      );

      if (campaign.retryCount >= this.maxRetries) {
        // Use lifecycle service to handle error state
        await this.lifecycleService.handleError(
          campaign,
          error instanceof Error ? error : new Error(String(error)),
          { maxRetries: this.maxRetries },
        );
        this.logger.warn(`Campaign ${campaign.id} transitioned to ERROR state`);
      }
    }
  }
}
