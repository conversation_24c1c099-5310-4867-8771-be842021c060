import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from '../../payment/entities/payment.entity';
import { PaymentAdapter } from '../../payment/interfaces/payment-adapter.interface';
import { PaymentService } from '../../payment/payment.service';
import { Campaign } from '../entities/campaign.entity';

/**
 * Unified payment processing result
 */
export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  payment?: Payment;
}

/**
 * Payment request data for campaign payments
 */
export interface CampaignPaymentRequest {
  campaign: Campaign;
  amountCents?: number;
  currency?: string;
  provider?: PaymentProvider;
}

/**
 * Unified service for handling all campaign payment operations.
 * Consolidates payment logic previously scattered across multiple services.
 */
@Injectable()
export class CampaignPaymentService {
  private readonly logger = new Logger(CampaignPaymentService.name);

  constructor(
    private readonly paymentService: PaymentService,
    @Inject('PaymentAdapter')
    private readonly paymentAdapter: PaymentAdapter,
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
  ) {}

  /**
   * Process a payment for a campaign.
   * Handles payment creation, processing through the gateway, and status updates.
   *
   * @param request - Payment request containing campaign and payment details
   * @returns PaymentResult with success status and transaction details
   */
  async processCampaignPayment(
    request: CampaignPaymentRequest,
  ): Promise<PaymentResult> {
    const { campaign, amountCents, currency, provider } = request;

    // Use provided values or campaign defaults
    const paymentAmount = amountCents ?? campaign.priceCents;
    const paymentCurrency = currency ?? campaign.currency;
    const paymentProvider = provider ?? PaymentProvider.TEST;

    this.logger.log('Processing payment for campaign', {
      campaignId: campaign.id,
      amountCents: paymentAmount,
      currency: paymentCurrency,
      provider: paymentProvider,
    });

    // Create initial payment record in PENDING status
    const payment = await this.paymentService.createInitialPayment({
      provider: paymentProvider,
      amountCents: paymentAmount,
      currency: paymentCurrency,
      campaignId: campaign.id,
    });

    // Prepare payment request for the gateway
    // For alpha, we only support test gateway with test card numbers
    const paymentRequest = {
      cardNumber: '****************', // Default successful test card
      expiryMonth: '12',
      expiryYear: '2025',
      cvc: '123',
      amountCents: paymentAmount,
      currency: paymentCurrency,
    };

    try {
      // Process payment through the gateway adapter
      const response = await this.paymentAdapter.processPayment(paymentRequest);

      if (response.success) {
        // Create success payment record
        const successPayment = await this.paymentService.createSuccessPayment(
          payment,
          response.transactionId || null,
        );

        this.logger.log('Payment processed successfully', {
          campaignId: campaign.id,
          transactionId: response.transactionId,
          paymentId: successPayment.id,
        });

        return {
          success: true,
          transactionId: response.transactionId,
          payment: successPayment,
        };
      } else {
        // Create failed payment record
        const failedPayment =
          await this.paymentService.createFailedPayment(payment);

        this.logger.error('Payment failed for campaign', {
          campaignId: campaign.id,
          error: response.error,
          paymentId: failedPayment.id,
        });

        return {
          success: false,
          error: response.error || 'Payment processing failed',
          payment: failedPayment,
        };
      }
    } catch (error) {
      // Handle unexpected errors
      const failedPayment =
        await this.paymentService.createFailedPayment(payment);

      this.logger.error('Payment processing error', {
        campaignId: campaign.id,
        error: error.message,
        paymentId: failedPayment.id,
      });

      return {
        success: false,
        error: 'Payment processing failed unexpectedly',
        payment: failedPayment,
      };
    }
  }

  /**
   * Create a payment retry record.
   * For alpha, this is a stub that logs the retry attempt.
   *
   * @param campaignId - Campaign ID for the retry
   * @param reason - Reason for the retry
   * @returns PaymentResult with stub response
   */
  createPaymentRetry(
    campaignId: string,
    reason?: string,
  ): Promise<PaymentResult> {
    this.logger.log(`Payment retry stub called for campaign ${campaignId}`, {
      campaignId,
      reason,
    });

    return Promise.resolve({
      success: false,
      error: 'Payment retry not supported in alpha (test gateway only)',
    });
  }

  /**
   * Create a payment refund record.
   * For alpha, this is a stub that logs the refund attempt.
   *
   * @param campaignId - Campaign ID for the refund
   * @param amountCents - Amount to refund in cents
   * @param reason - Reason for the refund
   * @returns PaymentResult with stub response
   */
  createPaymentRefund(
    campaignId: string,
    amountCents: number,
    reason?: string,
  ): Promise<PaymentResult> {
    this.logger.log(`Payment refund stub called for campaign ${campaignId}`, {
      campaignId,
      amountCents,
      reason,
    });

    return Promise.resolve({
      success: false,
      error: 'Payment refunds not supported in alpha (test gateway only)',
    });
  }

  /**
   * Get payment history for a campaign
   *
   * @param campaignId - Campaign ID to get payment history for
   * @returns Array of payments for the campaign
   */
  async getPaymentHistory(campaignId: string): Promise<Payment[]> {
    return this.paymentRepository.find({
      where: { campaignId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Check if a campaign has a successful payment
   *
   * @param campaignId - Campaign ID to check
   * @returns True if campaign has at least one successful payment
   */
  async hasSuccessfulPayment(campaignId: string): Promise<boolean> {
    const successfulPayment = await this.paymentRepository.findOne({
      where: {
        campaignId,
        status: PaymentStatus.SUCCEEDED,
      },
    });

    return !!successfulPayment;
  }

  /**
   * Get the latest payment for a campaign
   *
   * @param campaignId - Campaign ID to get latest payment for
   * @returns Latest payment or null if no payments exist
   */
  async getLatestPayment(campaignId: string): Promise<Payment | null> {
    return this.paymentRepository.findOne({
      where: { campaignId },
      order: { createdAt: 'DESC' },
    });
  }
}
