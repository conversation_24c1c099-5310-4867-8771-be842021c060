import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { CampaignPaymentService } from './campaign-payment.service';
import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from '../../payment/entities/payment.entity';
import { PaymentAdapter } from '../../payment/interfaces/payment-adapter.interface';
import { PaymentService } from '../../payment/payment.service';
import { Campaign, CampaignStatus } from '../entities/campaign.entity';

describe('CampaignPaymentService', () => {
  let service: CampaignPaymentService;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let paymentService: PaymentService;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let paymentAdapter: PaymentAdapter;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let campaignRepository: Repository<Campaign>;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let paymentRepository: Repository<Payment>;

  const mockPaymentService = {
    createInitialPayment: jest.fn(),
    createSuccessPayment: jest.fn(),
    createFailedPayment: jest.fn(),
    createPaymentAttempt: jest.fn(),
  };

  const mockPaymentAdapter = {
    processPayment: jest.fn(),
  };

  const mockCampaignRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockPaymentRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignPaymentService,
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
        {
          provide: 'PaymentAdapter',
          useValue: mockPaymentAdapter,
        },
        {
          provide: getRepositoryToken(Campaign),
          useValue: mockCampaignRepository,
        },
        {
          provide: getRepositoryToken(Payment),
          useValue: mockPaymentRepository,
        },
      ],
    }).compile();

    service = module.get<CampaignPaymentService>(CampaignPaymentService);
    paymentService = module.get<PaymentService>(PaymentService);
    paymentAdapter = module.get<PaymentAdapter>('PaymentAdapter');
    campaignRepository = module.get<Repository<Campaign>>(
      getRepositoryToken(Campaign),
    );
    paymentRepository = module.get<Repository<Payment>>(
      getRepositoryToken(Payment),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPaymentRetry', () => {
    it('should return a stub error for payment retry', async () => {
      const result = await service.createPaymentRetry(
        'campaign-123',
        'Test reason',
      );

      expect(result).toEqual({
        success: false,
        error: 'Payment retry not supported in alpha (test gateway only)',
      });
    });

    it('should log the retry attempt', async () => {
      const logSpy = jest.spyOn(service['logger'], 'log');

      await service.createPaymentRetry('campaign-123', 'Network timeout');

      expect(logSpy).toHaveBeenCalledWith(
        'Payment retry stub called for campaign campaign-123',
        {
          campaignId: 'campaign-123',
          reason: 'Network timeout',
        },
      );
    });
  });

  describe('createPaymentRefund', () => {
    it('should return a stub error for payment refund', async () => {
      const result = await service.createPaymentRefund(
        'campaign-123',
        5000,
        'Customer request',
      );

      expect(result).toEqual({
        success: false,
        error: 'Payment refunds not supported in alpha (test gateway only)',
      });
    });

    it('should log the refund attempt', async () => {
      const logSpy = jest.spyOn(service['logger'], 'log');

      await service.createPaymentRefund(
        'campaign-123',
        5000,
        'Duplicate charge',
      );

      expect(logSpy).toHaveBeenCalledWith(
        'Payment refund stub called for campaign campaign-123',
        {
          campaignId: 'campaign-123',
          amountCents: 5000,
          reason: 'Duplicate charge',
        },
      );
    });
  });

  describe('getPaymentHistory', () => {
    it('should return payment history for a campaign', async () => {
      const mockPayments: Payment[] = [
        {
          id: 'payment-1',
          campaignId: 'campaign-123',
          status: PaymentStatus.SUCCEEDED,
          amountCents: 5000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          providerRef: 'ref-1',
          attemptNumber: 1,
          parentPaymentId: null,
          createdAt: new Date('2025-01-01'),
        } as Payment,
        {
          id: 'payment-2',
          campaignId: 'campaign-123',
          status: PaymentStatus.FAILED,
          amountCents: 5000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          providerRef: 'ref-2',
          attemptNumber: 2,
          parentPaymentId: 'payment-1',
          createdAt: new Date('2025-01-02'),
        } as Payment,
      ];

      mockPaymentRepository.find.mockResolvedValue(mockPayments);

      const result = await service.getPaymentHistory('campaign-123');

      expect(result).toEqual(mockPayments);
      expect(mockPaymentRepository.find).toHaveBeenCalledWith({
        where: { campaignId: 'campaign-123' },
        order: { createdAt: 'DESC' },
      });
    });

    it('should return empty array for campaign with no payments', async () => {
      mockPaymentRepository.find.mockResolvedValue([]);

      const result = await service.getPaymentHistory('campaign-456');

      expect(result).toEqual([]);
    });
  });

  describe('hasSuccessfulPayment', () => {
    it('should return true if campaign has a successful payment', async () => {
      const mockPayment: Payment = {
        id: 'payment-1',
        campaignId: 'campaign-123',
        status: PaymentStatus.SUCCEEDED,
      } as Payment;

      mockPaymentRepository.findOne.mockResolvedValue(mockPayment);

      const result = await service.hasSuccessfulPayment('campaign-123');

      expect(result).toBe(true);
      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: {
          campaignId: 'campaign-123',
          status: PaymentStatus.SUCCEEDED,
        },
      });
    });

    it('should return false if campaign has no successful payment', async () => {
      mockPaymentRepository.findOne.mockResolvedValue(null);

      const result = await service.hasSuccessfulPayment('campaign-123');

      expect(result).toBe(false);
    });
  });

  describe('getLatestPayment', () => {
    it('should return the latest payment for a campaign', async () => {
      const mockPayment: Payment = {
        id: 'payment-3',
        campaignId: 'campaign-123',
        status: PaymentStatus.SUCCEEDED,
        createdAt: new Date('2025-01-03'),
      } as Payment;

      mockPaymentRepository.findOne.mockResolvedValue(mockPayment);

      const result = await service.getLatestPayment('campaign-123');

      expect(result).toEqual(mockPayment);
      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { campaignId: 'campaign-123' },
        order: { createdAt: 'DESC' },
      });
    });

    it('should return null if campaign has no payments', async () => {
      mockPaymentRepository.findOne.mockResolvedValue(null);

      const result = await service.getLatestPayment('campaign-456');

      expect(result).toBeNull();
    });
  });

  describe('processCampaignPayment', () => {
    const mockCampaign: Campaign = {
      id: 'campaign-123',
      priceCents: 5000,
      currency: 'USD',
      status: CampaignStatus.PENDING,
    } as Campaign;

    const mockInitialPayment: Payment = {
      id: 'payment-initial',
      campaignId: 'campaign-123',
      status: PaymentStatus.PENDING,
      amountCents: 5000,
      currency: 'USD',
      provider: PaymentProvider.TEST,
      attemptNumber: 1,
      parentPaymentId: null,
      createdAt: new Date(),
    } as Payment;

    beforeEach(() => {
      mockPaymentService.createInitialPayment.mockResolvedValue(
        mockInitialPayment,
      );
    });

    it('should process a successful payment', async () => {
      const mockSuccessPayment: Payment = {
        ...mockInitialPayment,
        id: 'payment-success',
        status: PaymentStatus.SUCCEEDED,
        attemptNumber: 2,
        parentPaymentId: 'payment-initial',
      } as Payment;

      mockPaymentAdapter.processPayment.mockResolvedValue({
        success: true,
        transactionId: 'txn-123',
      });

      mockPaymentService.createSuccessPayment.mockResolvedValue(
        mockSuccessPayment,
      );

      const result = await service.processCampaignPayment({
        campaign: mockCampaign,
      });

      expect(result).toEqual({
        success: true,
        transactionId: 'txn-123',
        payment: mockSuccessPayment,
      });

      expect(mockPaymentService.createInitialPayment).toHaveBeenCalledWith({
        provider: PaymentProvider.TEST,
        amountCents: 5000,
        currency: 'USD',
        campaignId: 'campaign-123',
      });

      expect(mockPaymentAdapter.processPayment).toHaveBeenCalledWith({
        cardNumber: '****************',
        expiryMonth: expect.any(String),
        expiryYear: expect.any(String),
        cvc: '123',
        amountCents: 5000,
        currency: 'USD',
      });

      expect(mockPaymentService.createSuccessPayment).toHaveBeenCalledWith(
        mockInitialPayment,
        'txn-123',
      );
    });

    it('should handle a failed payment', async () => {
      const mockFailedPayment: Payment = {
        ...mockInitialPayment,
        id: 'payment-failed',
        status: PaymentStatus.FAILED,
        attemptNumber: 2,
        parentPaymentId: 'payment-initial',
      } as Payment;

      mockPaymentAdapter.processPayment.mockResolvedValue({
        success: false,
        error: 'Card declined',
      });

      mockPaymentService.createFailedPayment.mockResolvedValue(
        mockFailedPayment,
      );

      const result = await service.processCampaignPayment({
        campaign: mockCampaign,
      });

      expect(result).toEqual({
        success: false,
        error: 'Card declined',
        payment: mockFailedPayment,
      });

      expect(mockPaymentService.createFailedPayment).toHaveBeenCalledWith(
        mockInitialPayment,
      );
    });

    it('should handle gateway exceptions', async () => {
      const mockFailedPayment: Payment = {
        ...mockInitialPayment,
        id: 'payment-error',
        status: PaymentStatus.FAILED,
        attemptNumber: 2,
        parentPaymentId: 'payment-initial',
      } as Payment;

      mockPaymentAdapter.processPayment.mockRejectedValue(
        new Error('Network error'),
      );

      mockPaymentService.createFailedPayment.mockResolvedValue(
        mockFailedPayment,
      );

      const result = await service.processCampaignPayment({
        campaign: mockCampaign,
      });

      expect(result).toEqual({
        success: false,
        error: 'Payment processing failed unexpectedly',
        payment: mockFailedPayment,
      });

      expect(mockPaymentService.createFailedPayment).toHaveBeenCalledWith(
        mockInitialPayment,
      );
    });

    it('should use custom payment parameters if provided', async () => {
      await service.processCampaignPayment({
        campaign: mockCampaign,
        amountCents: 10000,
        currency: 'EUR',
      });

      expect(mockPaymentService.createInitialPayment).toHaveBeenCalledWith({
        provider: PaymentProvider.TEST,
        amountCents: 10000,
        currency: 'EUR',
        campaignId: 'campaign-123',
      });

      expect(mockPaymentAdapter.processPayment).toHaveBeenCalledWith(
        expect.objectContaining({
          amountCents: 10000,
          currency: 'EUR',
        }),
      );
    });
  });
});
