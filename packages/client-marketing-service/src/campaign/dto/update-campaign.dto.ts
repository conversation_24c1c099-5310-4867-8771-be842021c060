import { Field, InputType, Int } from '@nestjs/graphql';
import { IsString, IsInt, IsEnum, IsOptional, Min } from 'class-validator';

import { CampaignStatus } from '../entities/campaign.entity';

@InputType()
export class UpdateCampaignDto {
  @Field(() => Int, { nullable: true })
  @IsInt()
  @Min(0)
  @IsOptional()
  priceCents?: number;

  @Field(() => CampaignStatus, { nullable: true })
  @IsEnum(CampaignStatus)
  @IsOptional()
  status?: CampaignStatus;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  bundleUri?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  sqsMessageId?: string;
}
