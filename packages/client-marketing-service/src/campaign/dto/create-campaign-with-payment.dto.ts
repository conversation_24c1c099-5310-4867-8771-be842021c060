import { Field, ID, InputType, Int } from '@nestjs/graphql';
import { IsString, IsOptional, IsInt, Min, Matches } from 'class-validator';

@InputType()
export class CreateCampaignWithPaymentDto {
  @Field(() => ID)
  @IsString()
  propertyId: string;

  @Field(() => Int)
  @IsInt()
  @Min(0)
  priceCents: number;

  @Field(() => String, { defaultValue: 'USD' })
  @IsString()
  @IsOptional()
  currency?: string;

  @Field(() => String, {
    nullable: true,
    description:
      'Optional test card number for payment testing (development/testing only). Must be 13-19 digits.',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d{13,19}$/, {
    message: 'testCardNumber must be 13-19 digits only',
  })
  testCardNumber?: string;
}
