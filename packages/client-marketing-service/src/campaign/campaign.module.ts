import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BundleLifecycleService } from './bundle/bundle-lifecycle.service';
import { CampaignStrategyFactory } from './campaign-strategy.factory';
import { CampaignWatchdogService } from './campaign-watchdog.service';
import { CampaignResolver } from './campaign.resolver';
import { CampaignService } from './campaign.service';
import { Campaign } from './entities/campaign.entity';
import { CampaignLifecycleService } from './lifecycle/campaign-lifecycle.service';
import { CampaignPaymentService } from './payment/campaign-payment.service';
import { FullOrchestrationStrategy } from './strategies/full-orchestration.strategy';
import { LinearNotificationStrategy } from './strategies/linear-notification.strategy';
import { MockOrchestrationStrategy } from './strategies/mock-orchestration.strategy';
import { NotificationContext } from './strategies/notification-context.service';
import { NotificationStrategyFactory } from './strategies/notification-strategy.factory';
import { PaymentOnlyStrategy } from './strategies/payment-only.strategy';
import { SNSNotificationStrategy } from './strategies/sns-notification.strategy';
import { RetryModule } from '../common/services/retry/retry.module';
import { RetryService } from '../common/services/retry/retry.service';
import { Payment } from '../payment/entities/payment.entity';
import { PaymentModule } from '../payment/payment.module';
import { PaymentService } from '../payment/payment.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Campaign, Payment]),
    ConfigModule,
    PaymentModule,
    RetryModule,
  ],
  providers: [
    BundleLifecycleService,
    CampaignService,
    CampaignResolver,
    CampaignWatchdogService,
    CampaignLifecycleService,
    CampaignPaymentService,
    CampaignStrategyFactory,
    FullOrchestrationStrategy,
    LinearNotificationStrategy,
    MockOrchestrationStrategy,
    NotificationContext,
    NotificationStrategyFactory,
    PaymentOnlyStrategy,
    SNSNotificationStrategy,
    {
      provide: 'PaymentService',
      useExisting: PaymentService,
    },
    {
      provide: 'BundleLifecycleService',
      useExisting: BundleLifecycleService,
    },
    {
      provide: 'RetryService',
      useExisting: RetryService,
    },
  ],
  exports: [BundleLifecycleService, CampaignService, CampaignPaymentService],
})
export class CampaignModule {}
