import { TypeOrmModule } from '@nestjs/typeorm';
import * as dotenv from 'dotenv';
import { seeder } from 'nestjs-seeder';
import { BlogTopic } from 'src/blog-topic/entities/blog-topic.entity';
import { BrandProfile } from 'src/brand-profile/entities/brand-profile.entity';
import { AppDataSource } from 'src/data-source';
import { Group } from 'src/group/entities/group.entity';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { PageKeywordHistory } from 'src/page-keyword-history/entities/page-keyword-history.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { ScheduledAction } from 'src/scheduled-action/entities/scheduled-action.entity';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { BlogTopicSeeder } from 'src/seeders/blog-topic.seeder';
import { BrandProfileSeeder } from 'src/seeders/brand-profile.seeder';
import { DatabaseResetSeeder } from 'src/seeders/database-reset.seeder';
import { GroupSeeder } from 'src/seeders/group.seeder';
import { KeywordSeeder } from 'src/seeders/keyword.seeder';
import { PageKeywordHistorySeeder } from 'src/seeders/page-keyword-history.seeder';
import { PageKeywordSeeder } from 'src/seeders/page-keyword.seeder';
import { RecommendationSeeder } from 'src/seeders/recommendation.seeder';
import { ScheduledActionContentPayloadSeeder } from 'src/seeders/scheduled-action-content-payload.seeder';
import { ScheduledActionSeeder } from 'src/seeders/scheduled-action.seeder';
import { ScrapeSeeder } from 'src/seeders/scrape.seeder';
import { PageSeeder } from 'src/seeders/scraped-page.seeder';

dotenv.config();

// Check if we should refresh (clear) the database first
const isRefresh = process.argv.includes('--refresh');

// Define seeders based on whether we're refreshing or not
const seedersToRun = isRefresh
  ? [
      // Database reset seeder - handles all cleanup sequentially
      DatabaseResetSeeder,

      // Base entities
      KeywordSeeder,
      PageSeeder,
      BlogTopicSeeder,
      BrandProfileSeeder,

      // PageKeyword must come before Group since Group depends on it
      PageKeywordSeeder,

      // Entities that depend on base entities
      GroupSeeder,
      ScrapeSeeder,
      ScheduledActionSeeder, // Creates additional groups
      RecommendationSeeder, // Creates recommendations for ALL groups
      ScheduledActionContentPayloadSeeder, // Updates contentPayload with real entities (must run after RecommendationSeeder)

      // History seeder (triggers will populate automatically)
      PageKeywordHistorySeeder,
    ]
  : [
      // Skip database reset, just add more data
      KeywordSeeder,
      PageSeeder,
      BlogTopicSeeder,
      BrandProfileSeeder,
      PageKeywordSeeder,
      GroupSeeder,
      ScrapeSeeder,
      ScheduledActionSeeder, // Creates additional groups
      RecommendationSeeder, // Creates recommendations for ALL groups
      ScheduledActionContentPayloadSeeder, // Updates contentPayload with real entities (must run after RecommendationSeeder)
      PageKeywordHistorySeeder,
    ];

console.log(
  isRefresh
    ? '🔄 Running seeder with database reset (--refresh flag detected)'
    : '➕ Running seeder without database reset (adding to existing data)',
);

seeder({
  imports: [
    TypeOrmModule.forRoot(AppDataSource.options),
    TypeOrmModule.forFeature([
      Keyword,
      ScrapedPage,
      Group,
      GroupScheduledAction,
      PageKeyword,
      PageKeywordHistory, // Keep this so TypeORM creates the table
      Scrape,
      Recommendation,
      ScheduledAction,
      BlogTopic,
      BrandProfile,
    ]),
  ],
}).run(seedersToRun);
