import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeleteResult } from 'typeorm';

import { CreateScrapeDto } from './dto/create-scrape.dto';
import { UpdateScrapeDto } from './dto/update-scrape.dto';
import { Scrape } from './entities/scrape.entity';

@Injectable()
export class ScrapeService {
  constructor(
    @InjectRepository(Scrape)
    private readonly scrapeRepository: Repository<Scrape>,
  ) {}

  async create(createScrapeDto: CreateScrapeDto): Promise<Scrape> {
    return this.scrapeRepository.save({
      scrapedAt: createScrapeDto.scrapedAt ?? new Date(),
      ...createScrapeDto,
    });
  }

  async findAll(companyId?: string): Promise<Scrape[]> {
    const queryBuilder = this.scrapeRepository
      .createQueryBuilder('scrape')
      .leftJoinAndSelect('scrape.scrapedPage', 'scrapedPage')
      .orderBy('scrape.scrapedAt', 'DESC');

    if (companyId) {
      queryBuilder.where('scrape.companyId = :companyId', { companyId });
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<Scrape> {
    const scrape = await this.scrapeRepository.findOne({
      where: { id },
      relations: ['scrapedPage', 'recommendations'],
    });

    if (!scrape) {
      throw new NotFoundException(`Scrape with ID "${id}" not found`);
    }

    return scrape;
  }

  async findByPageId(pageId: string): Promise<Scrape[]> {
    return this.scrapeRepository.find({
      where: { scrapedPageId: pageId },
      relations: ['scrapedPage'],
      order: {
        scrapedAt: 'DESC',
      },
    });
  }

  async findLatestByPageId(pageId: string): Promise<Scrape | null> {
    return this.scrapeRepository.findOne({
      where: { scrapedPageId: pageId },
      relations: ['scrapedPage'],
      order: {
        scrapedAt: 'DESC',
      },
    });
  }

  async update(id: string, updateScrapeDto: UpdateScrapeDto): Promise<Scrape> {
    const scrape = await this.findOne(id);

    const { currentScrapedValues, ...otherProps } = updateScrapeDto;

    await this.scrapeRepository.update(id, {
      ...otherProps,
      ...(currentScrapedValues && {
        currentScrapedValues: {
          ...scrape.currentScrapedValues,
          ...currentScrapedValues,
        },
      }),
    });
    return this.findOne(id);
  }

  async remove(id: string): Promise<DeleteResult> {
    return await this.scrapeRepository.delete({ id });
  }
}
