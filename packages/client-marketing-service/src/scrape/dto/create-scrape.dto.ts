import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class CreateScrapeDto {
  @ApiProperty({
    description: 'The ID of the company',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({
    description: 'The ID of the scraped page',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  scrapedPageId: string;

  @ApiProperty({
    description: 'The raw HTML content from the scrape',
    example: '<html><body><h1>Page Title</h1><p>Content</p></body></html>',
    required: false,
  })
  @IsString()
  @IsOptional()
  rawHtml?: string;

  @ApiProperty({
    description: 'The markdown content converted from HTML',
    example: '# Page Title\n\nContent',
    required: false,
  })
  @IsString()
  @IsOptional()
  markdown?: string;

  @ApiProperty({
    description: 'The scraped values extracted from the page',
    example: { title: 'Page Title', metaDescription: 'Page description' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  currentScrapedValues?: Record<string, any>;

  @ApiProperty({
    description: 'The date when the page was scraped',
    example: '2025-05-19T10:00:00Z',
    required: false,
  })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  scrapedAt?: Date;
}
