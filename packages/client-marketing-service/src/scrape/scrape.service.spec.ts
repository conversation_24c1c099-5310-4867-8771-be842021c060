import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { CreateScrapeDto } from './dto/create-scrape.dto';
import { Scrape } from './entities/scrape.entity';
import { ScrapeService } from './scrape.service';
import {
  ScrapedPage,
  ScrapedPageType,
} from '../scraped-page/entities/scraped-page.entity';

describe('ScrapeService', () => {
  let service: ScrapeService;
  let scrapeRepository: any;

  const companyId = 'company-id-123';
  const scrapedPageId = 'page-id-456';
  const scrapeId = 'scrape-id-789';

  const mockPage: ScrapedPage = {
    id: scrapedPageId,
    companyId,
    url: 'https://example.com',
    pageName: 'Test Page',
    pageType: ScrapedPageType.HOMEPAGE,
    metadata: {},
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T10:00:00Z'),
    recommendations: [],
    groups: [],
    deletedAt: null,
    scrapes: [],
    pageKeywordHistory: [],
    pageKeywords: [],
  };

  const mockScrape: Scrape = {
    id: scrapeId,
    companyId,
    scrapedPageId,
    rawHtml:
      '<html><head><title>Test Page</title></head><body><h1>Main Heading</h1><p>Test content</p></body></html>',
    markdown: '# Main Heading\n\nTest content',
    currentScrapedValues: {
      title: 'Test Page',
      metaDescription: 'Test meta description',
      h1: 'Main Heading',
      keywords: ['test', 'seo', 'page'],
    },
    mediaId: 'media-id-123',
    scrapedAt: new Date('2024-01-15T10:00:00Z'),
    createdAt: new Date('2024-01-15T10:00:00Z'),
    updatedAt: new Date('2024-01-15T10:00:00Z'),
    scrapedPage: mockPage,
    recommendations: [],
  };

  const mockCreateScrapeDto: CreateScrapeDto = {
    companyId,
    scrapedPageId,
    rawHtml: mockScrape.rawHtml,
    markdown: mockScrape.markdown,
    currentScrapedValues: mockScrape.currentScrapedValues,
  };

  const mockScrapeRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScrapeService,
        { provide: getRepositoryToken(Scrape), useValue: mockScrapeRepository },
      ],
    }).compile();

    service = module.get<ScrapeService>(ScrapeService);
    scrapeRepository = module.get(getRepositoryToken(Scrape));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new scrape with all fields', async () => {
      const savedScrape = { id: scrapeId, ...mockCreateScrapeDto };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(mockCreateScrapeDto);

      expect(scrapeRepository.save).toHaveBeenCalledWith({
        scrapedAt: expect.any(Date),
        ...mockCreateScrapeDto,
      });
      expect(result).toEqual(savedScrape);
    });

    it('should create a scrape with minimal required fields', async () => {
      const minimalDto: CreateScrapeDto = {
        companyId,
        scrapedPageId,
        rawHtml: '<html><body>Minimal content</body></html>',
        markdown: 'Minimal content',
        currentScrapedValues: {},
      };
      const savedScrape = { id: 'minimal-scrape-id', ...minimalDto };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(minimalDto);

      expect(scrapeRepository.save).toHaveBeenCalledWith({
        scrapedAt: expect.any(Date),
        ...minimalDto,
      });
      expect(result).toEqual(savedScrape);
    });

    it('should create a scrape with complex currentScrapedValues', async () => {
      const complexDto: CreateScrapeDto = {
        companyId,
        scrapedPageId,
        rawHtml:
          '<html><head><title>Complex Page</title><meta name="description" content="Complex description"/></head><body><h1>Main</h1><h2>Sub</h2></body></html>',
        markdown: '# Main\n\n## Sub\n\nComplex content',
        currentScrapedValues: {
          title: 'Complex Page',
          metaDescription: 'Complex description',
          h1: 'Main',
          h2: ['Sub'],
          images: [
            { src: '/image1.jpg', alt: 'Image 1' },
            { src: '/image2.jpg', alt: 'Image 2' },
          ],
          links: [
            { href: '/page1', text: 'Page 1' },
            { href: '/page2', text: 'Page 2' },
          ],
          seoScore: {
            titleLength: 12,
            descriptionLength: 19,
            h1Count: 1,
            h2Count: 1,
          },
        },
      };
      const savedScrape = { id: 'complex-scrape-id', ...complexDto };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(complexDto);

      expect(scrapeRepository.save).toHaveBeenCalledWith({
        scrapedAt: expect.any(Date),
        ...complexDto,
      });
      expect(result).toEqual(savedScrape);
      expect(result.currentScrapedValues.images).toHaveLength(2);
      expect(result.currentScrapedValues.seoScore.titleLength).toBe(12);
    });

    it('should create a scrape with large HTML content', async () => {
      const largeHtml =
        '<html>' +
        '<div>'.repeat(1000) +
        'Large content' +
        '</div>'.repeat(1000) +
        '</html>';
      const largeDto: CreateScrapeDto = {
        companyId,
        scrapedPageId,
        rawHtml: largeHtml,
        markdown: 'Large content',
        currentScrapedValues: { size: 'large' },
      };
      const savedScrape = { id: 'large-scrape-id', ...largeDto };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(largeDto);

      expect(scrapeRepository.save).toHaveBeenCalledWith({
        scrapedAt: expect.any(Date),
        ...largeDto,
      });
      expect(result.rawHtml.length).toBeGreaterThan(10000);
    });

    it('should handle repository save errors', async () => {
      const saveError = new Error('Database constraint violation');
      mockScrapeRepository.save.mockRejectedValue(saveError);

      await expect(service.create(mockCreateScrapeDto)).rejects.toThrow(
        saveError,
      );
      expect(scrapeRepository.save).toHaveBeenCalledWith({
        scrapedAt: expect.any(Date),
        ...mockCreateScrapeDto,
      });
    });

    it('should handle foreign key constraint errors', async () => {
      const fkError = new Error('Foreign key constraint violation');
      fkError.name = 'ForeignKeyConstraintError';
      mockScrapeRepository.save.mockRejectedValue(fkError);

      await expect(service.create(mockCreateScrapeDto)).rejects.toThrow(
        fkError,
      );
    });

    it('should create scrapes with different media IDs', async () => {
      const dtoWithMedia = {
        ...mockCreateScrapeDto,
        mediaId: 'different-media-id',
      } as CreateScrapeDto & { mediaId: string };
      const savedScrape = { id: 'media-scrape-id', ...dtoWithMedia };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(dtoWithMedia);

      expect(result.mediaId).toBe('different-media-id');
    });

    it('should create scrapes without mediaId', async () => {
      const dtoWithoutMedia: CreateScrapeDto = {
        companyId,
        scrapedPageId,
        rawHtml: mockScrape.rawHtml,
        markdown: mockScrape.markdown,
        currentScrapedValues: mockScrape.currentScrapedValues,
      };
      const savedScrape = { id: 'no-media-scrape-id', ...dtoWithoutMedia };
      mockScrapeRepository.save.mockResolvedValue(savedScrape);

      const result = await service.create(dtoWithoutMedia);

      expect(result.mediaId).toBeUndefined();
    });
  });

  describe('findAll', () => {
    const mockQueryBuilder = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    };

    beforeEach(() => {
      mockScrapeRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });

    it('should return all scrapes when no companyId provided', async () => {
      const scrapes = [
        {
          id: '1',
          companyId: 'company-1',
          scrapedPageId: 'page-1',
          scrapedAt: new Date('2024-01-15T10:00:00Z'),
        },
        {
          id: '2',
          companyId: 'company-2',
          scrapedPageId: 'page-2',
          scrapedAt: new Date('2024-01-14T10:00:00Z'),
        },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(scrapes);

      const result = await service.findAll();

      expect(mockScrapeRepository.createQueryBuilder).toHaveBeenCalledWith(
        'scrape',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'scrape.scrapedPage',
        'scrapedPage',
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'scrape.scrapedAt',
        'DESC',
      );
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();
      expect(result).toEqual(scrapes);
    });

    it('should return scrapes filtered by companyId when provided', async () => {
      const scrapes = [
        { id: '1', companyId, scrapedPageId: 'page-1', scrapedAt: new Date() },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(scrapes);

      const result = await service.findAll(companyId);

      expect(mockScrapeRepository.createQueryBuilder).toHaveBeenCalledWith(
        'scrape',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrape.companyId = :companyId',
        { companyId },
      );
      expect(result).toEqual(scrapes);
    });

    it('should return empty array when no scrapes exist', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([]);

      const result = await service.findAll();

      expect(result).toEqual([]);
    });

    it('should return empty array for company with no scrapes', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([]);

      const result = await service.findAll('non-existent-company');

      expect(result).toEqual([]);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrape.companyId = :companyId',
        { companyId: 'non-existent-company' },
      );
    });

    it('should handle multiple scrapes for same company ordered by scrapedAt DESC', async () => {
      const scrapes = [
        {
          id: '3',
          companyId,
          scrapedPageId: 'page-1',
          scrapedAt: new Date('2024-01-17T10:00:00Z'),
        },
        {
          id: '2',
          companyId,
          scrapedPageId: 'page-2',
          scrapedAt: new Date('2024-01-16T10:00:00Z'),
        },
        {
          id: '1',
          companyId,
          scrapedPageId: 'page-1',
          scrapedAt: new Date('2024-01-15T10:00:00Z'),
        },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(scrapes);

      const result = await service.findAll(companyId);

      expect(result).toEqual(scrapes);
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'scrape.scrapedAt',
        'DESC',
      );
      // Verify the order - newest first
      expect(result[0].scrapedAt.getTime()).toBeGreaterThan(
        result[1].scrapedAt.getTime(),
      );
      expect(result[1].scrapedAt.getTime()).toBeGreaterThan(
        result[2].scrapedAt.getTime(),
      );
    });

    it('should include page relationships in query', async () => {
      const scrapesWithPages = [
        {
          id: '1',
          companyId,
          scrapedPageId: 'page-1',
          scrapedPage: { id: 'page-1', url: 'https://example.com/page1' },
        },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(scrapesWithPages);

      const result = await service.findAll(companyId);

      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'scrape.scrapedPage',
        'scrapedPage',
      );
      expect(result[0].scrapedPage).toBeDefined();
      expect(result[0].scrapedPage.url).toBe('https://example.com/page1');
    });

    it('should handle large number of scrapes', async () => {
      const manyScrapesCompanyId = 'large-company';
      const largeNumber = 1000;
      const manyScrapes = Array.from({ length: largeNumber }, (_, index) => ({
        id: `scrape-${index}`,
        companyId: manyScrapesCompanyId,
        scrapedPageId: `page-${index}`,
        scrapedAt: new Date(Date.now() - index * 1000),
      }));

      mockQueryBuilder.getMany.mockResolvedValue(manyScrapes);

      const result = await service.findAll(manyScrapesCompanyId);

      expect(result).toHaveLength(largeNumber);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrape.companyId = :companyId',
        { companyId: manyScrapesCompanyId },
      );
    });

    it('should handle query builder errors', async () => {
      const queryError = new Error('Database connection failed');
      mockQueryBuilder.getMany.mockRejectedValue(queryError);

      await expect(service.findAll()).rejects.toThrow(queryError);
    });

    it('should handle different companyId formats', async () => {
      const uuidCompanyId = '123e4567-e89b-12d3-a456-************';
      const shortCompanyId = 'company123';

      // Reset mock to successful state
      mockQueryBuilder.getMany.mockResolvedValue([]);

      // Test UUID format
      await service.findAll(uuidCompanyId);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrape.companyId = :companyId',
        { companyId: uuidCompanyId },
      );

      // Test short format
      await service.findAll(shortCompanyId);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrape.companyId = :companyId',
        { companyId: shortCompanyId },
      );
    });

    it('should handle null or undefined companyId as no filter', async () => {
      const scrapes = [{ id: '1', companyId: 'any-company' }];
      mockQueryBuilder.getMany.mockResolvedValue(scrapes);

      // Test null
      await service.findAll(null as any);
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();

      // Test undefined
      await service.findAll(undefined);
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a scrape when found', async () => {
      mockScrapeRepository.findOne.mockResolvedValue(mockScrape);

      const result = await service.findOne(scrapeId);

      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { id: scrapeId },
        relations: ['scrapedPage', 'recommendations'],
      });
      expect(result).toEqual(mockScrape);
    });

    it('should return scrape with relationships populated', async () => {
      const scrapeWithRelations = {
        ...mockScrape,
        scrapedPage: {
          id: scrapedPageId,
          url: 'https://example.com/test-page',
          companyId,
        },
        recommendations: [
          { id: 'rec-1', type: 'META_TITLE', status: 'PENDING' },
          { id: 'rec-2', type: 'META_DESCRIPTION', status: 'APPLIED' },
        ],
      };

      mockScrapeRepository.findOne.mockResolvedValue(scrapeWithRelations);

      const result = await service.findOne(scrapeId);

      expect(result.scrapedPage).toBeDefined();
      expect(result.recommendations).toHaveLength(2);
      expect(result.scrapedPage.url).toBe('https://example.com/test-page');
      expect(result.recommendations[0].type).toBe('META_TITLE');
    });

    it('should return scrape with complex currentScrapedValues', async () => {
      const complexScrape = {
        ...mockScrape,
        currentScrapedValues: {
          title: 'Complex Page Title',
          metaDescription: 'Detailed meta description',
          headings: {
            h1: ['Main Heading'],
            h2: ['Section 1', 'Section 2'],
            h3: ['Subsection 1', 'Subsection 2', 'Subsection 3'],
          },
          images: [
            { src: '/hero.jpg', alt: 'Hero Image', width: 1200, height: 600 },
            { src: '/logo.png', alt: 'Company Logo', width: 200, height: 100 },
          ],
          links: {
            internal: ['/about', '/contact', '/services'],
            external: ['https://partner.com', 'https://social.com'],
          },
          seo: {
            titleLength: 22,
            descriptionLength: 155,
            h1Count: 1,
            imagesMissingAlt: 0,
            internalLinksCount: 3,
            externalLinksCount: 2,
          },
        },
      };

      mockScrapeRepository.findOne.mockResolvedValue(complexScrape);

      const result = await service.findOne(scrapeId);

      expect(result.currentScrapedValues.headings.h2).toHaveLength(2);
      expect(result.currentScrapedValues.seo.titleLength).toBe(22);
      expect(result.currentScrapedValues.images[0].width).toBe(1200);
    });

    it('should throw NotFoundException when scrape not found', async () => {
      mockScrapeRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        'Scrape with ID "non-existent-id" not found',
      );
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-id' },
        relations: ['scrapedPage', 'recommendations'],
      });
    });

    it('should handle repository errors', async () => {
      const dbError = new Error('Database connection failed');
      mockScrapeRepository.findOne.mockRejectedValue(dbError);

      await expect(service.findOne(scrapeId)).rejects.toThrow(dbError);
    });

    it('should handle invalid UUID format', async () => {
      const invalidId = 'invalid-uuid';
      mockScrapeRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(invalidId)).rejects.toThrow(
        `Scrape with ID "${invalidId}" not found`,
      );
    });

    it('should handle empty string ID', async () => {
      const emptyId = '';
      mockScrapeRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(emptyId)).rejects.toThrow(
        'Scrape with ID "" not found',
      );
    });
  });

  describe('findByPageId', () => {
    it('should return scrapes for a specific page ordered by scrapedAt DESC', async () => {
      const scrapes = [
        {
          id: '3',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-17T10:00:00Z'),
        },
        {
          id: '2',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-16T10:00:00Z'),
        },
        {
          id: '1',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-15T10:00:00Z'),
        },
      ];

      mockScrapeRepository.find.mockResolvedValue(scrapes);

      const result = await service.findByPageId(scrapedPageId);

      expect(scrapeRepository.find).toHaveBeenCalledWith({
        where: { scrapedPageId },
        relations: ['scrapedPage'],
        order: {
          scrapedAt: 'DESC',
        },
      });
      expect(result).toEqual(scrapes);
      // Verify chronological order - newest first
      expect(result[0].scrapedAt.getTime()).toBeGreaterThan(
        result[1].scrapedAt.getTime(),
      );
      expect(result[1].scrapedAt.getTime()).toBeGreaterThan(
        result[2].scrapedAt.getTime(),
      );
    });

    it('should return empty array when no scrapes exist for page', async () => {
      mockScrapeRepository.find.mockResolvedValue([]);

      const result = await service.findByPageId('non-existent-page');

      expect(result).toEqual([]);
      expect(scrapeRepository.find).toHaveBeenCalledWith({
        where: { scrapedPageId: 'non-existent-page' },
        relations: ['scrapedPage'],
        order: {
          scrapedAt: 'DESC',
        },
      });
    });

    it('should return historical scrapes showing content evolution', async () => {
      const historicalScrapes = [
        {
          id: '3',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-17T10:00:00Z'),
          currentScrapedValues: { title: 'Latest Title', version: 3 },
        },
        {
          id: '2',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-16T10:00:00Z'),
          currentScrapedValues: { title: 'Updated Title', version: 2 },
        },
        {
          id: '1',
          scrapedPageId,
          companyId,
          scrapedAt: new Date('2024-01-15T10:00:00Z'),
          currentScrapedValues: { title: 'Original Title', version: 1 },
        },
      ];

      mockScrapeRepository.find.mockResolvedValue(historicalScrapes);

      const result = await service.findByPageId(scrapedPageId);

      expect(result).toHaveLength(3);
      expect(result[0].currentScrapedValues.title).toBe('Latest Title');
      expect(result[1].currentScrapedValues.title).toBe('Updated Title');
      expect(result[2].currentScrapedValues.title).toBe('Original Title');
    });

    it('should include page relationship data', async () => {
      const scrapesWithPage = [
        {
          id: '1',
          scrapedPageId,
          companyId,
          scrapedPage: {
            id: scrapedPageId,
            url: 'https://example.com/test',
            companyId,
          },
        },
      ];

      mockScrapeRepository.find.mockResolvedValue(scrapesWithPage);

      const result = await service.findByPageId(scrapedPageId);

      expect(result[0].scrapedPage).toBeDefined();
      expect(result[0].scrapedPage.url).toBe('https://example.com/test');
      expect(scrapeRepository.find).toHaveBeenCalledWith(
        expect.objectContaining({
          relations: ['scrapedPage'],
        }),
      );
    });

    it('should handle large number of scrapes for a page', async () => {
      const manyPageScrapes = Array.from({ length: 100 }, (_, index) => ({
        id: `scrape-${index}`,
        scrapedPageId,
        companyId,
        scrapedAt: new Date(Date.now() - index * 86400000), // Daily scrapes
      }));

      mockScrapeRepository.find.mockResolvedValue(manyPageScrapes);

      const result = await service.findByPageId(scrapedPageId);

      expect(result).toHaveLength(100);
      expect(scrapeRepository.find).toHaveBeenCalledWith({
        where: { scrapedPageId },
        relations: ['scrapedPage'],
        order: {
          scrapedAt: 'DESC',
        },
      });
    });

    it('should handle repository errors', async () => {
      const findError = new Error('Database query failed');
      mockScrapeRepository.find.mockRejectedValue(findError);

      await expect(service.findByPageId(scrapedPageId)).rejects.toThrow(
        findError,
      );
    });

    it('should handle different scrapedPageId formats', async () => {
      const uuidPageId = '123e4567-e89b-12d3-a456-************';
      const shortPageId = 'page123';

      // Reset mock to successful state
      mockScrapeRepository.find.mockResolvedValue([]);

      // Test UUID format
      await service.findByPageId(uuidPageId);
      expect(scrapeRepository.find).toHaveBeenCalledWith({
        where: { scrapedPageId: uuidPageId },
        relations: ['scrapedPage'],
        order: { scrapedAt: 'DESC' },
      });

      // Test short format
      await service.findByPageId(shortPageId);
      expect(scrapeRepository.find).toHaveBeenCalledWith({
        where: { scrapedPageId: shortPageId },
        relations: ['scrapedPage'],
        order: { scrapedAt: 'DESC' },
      });
    });
  });

  describe('findLatestByPageId', () => {
    it('should return the latest scrape for a specific page', async () => {
      const testPageId = 'page-1';
      const latestScrape = {
        id: '1',
        scrapedPageId: testPageId,
        companyId: 'company-1',
      };

      mockScrapeRepository.findOne.mockResolvedValue(latestScrape);

      const result = await service.findLatestByPageId(testPageId);

      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: testPageId },
        relations: ['scrapedPage'],
        order: {
          scrapedAt: 'DESC',
        },
      });
      expect(result).toEqual(latestScrape);
    });

    it('should return null when no scrape found for page', async () => {
      const testPageId = 'non-existent-page';

      mockScrapeRepository.findOne.mockResolvedValue(null);

      const result = await service.findLatestByPageId(testPageId);

      expect(result).toBeNull();
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: testPageId },
        relations: ['scrapedPage'],
        order: {
          scrapedAt: 'DESC',
        },
      });
    });

    it('should return latest scrape with complete data and relationships', async () => {
      const testPageId = 'page-with-data';
      const latestScrapeWithRelations = {
        id: 'latest-scrape-id',
        scrapedPageId: testPageId,
        companyId,
        rawHtml:
          '<html><head><title>Latest Content</title></head><body><h1>Current Header</h1></body></html>',
        markdown: '# Current Header\n\nLatest content',
        currentScrapedValues: {
          title: 'Latest Content',
          metaDescription: 'Latest description',
          h1: 'Current Header',
          lastUpdated: '2024-01-17T10:00:00Z',
          seo: {
            titleLength: 14,
            descriptionLength: 18,
            h1Count: 1,
          },
        },
        mediaId: 'latest-media-id',
        scrapedAt: new Date('2024-01-17T10:00:00Z'),
        scrapedPage: {
          id: testPageId,
          url: 'https://example.com/latest',
          companyId,
        },
        recommendations: [
          { id: 'rec-1', type: 'META_TITLE', status: 'PENDING' },
          { id: 'rec-2', type: 'CONTENT_OPTIMIZATION', status: 'APPLIED' },
        ],
      };

      mockScrapeRepository.findOne.mockResolvedValue(latestScrapeWithRelations);

      const result = await service.findLatestByPageId(testPageId);

      expect(result).toBeDefined();
      expect(result?.scrapedPage).toBeDefined();
      expect(result?.scrapedPage?.url).toBe('https://example.com/latest');
      expect(result?.recommendations).toHaveLength(2);
      expect(result?.currentScrapedValues?.seo?.titleLength).toBe(14);
    });

    it('should handle different scrapedPageId formats correctly', async () => {
      const uuidPageId = '123e4567-e89b-12d3-a456-************';
      const shortPageId = 'page123';
      const latestScrape = { id: 'scrape-1', scrapedPageId: uuidPageId };

      mockScrapeRepository.findOne.mockResolvedValue(latestScrape);

      // Test UUID format
      await service.findLatestByPageId(uuidPageId);
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: uuidPageId },
        relations: ['scrapedPage'],
        order: { scrapedAt: 'DESC' },
      });

      // Test short format
      await service.findLatestByPageId(shortPageId);
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: shortPageId },
        relations: ['scrapedPage'],
        order: { scrapedAt: 'DESC' },
      });
    });

    it('should handle repository errors gracefully', async () => {
      const testPageId = 'error-page';
      const repositoryError = new Error('Database connection failed');
      mockScrapeRepository.findOne.mockRejectedValue(repositoryError);

      await expect(service.findLatestByPageId(testPageId)).rejects.toThrow(
        repositoryError,
      );
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: testPageId },
        relations: ['scrapedPage'],
        order: { scrapedAt: 'DESC' },
      });
    });

    it('should return latest scrape from multiple historical entries', async () => {
      const testPageId = 'historical-page';
      const latestScrape = {
        id: 'latest-id',
        scrapedPageId: testPageId,
        scrapedAt: new Date('2024-01-17T10:00:00Z'),
        currentScrapedValues: { title: 'Most Recent Title', version: 'v3' },
      };

      mockScrapeRepository.findOne.mockResolvedValue(latestScrape);

      const result = await service.findLatestByPageId(testPageId);

      expect(result?.currentScrapedValues?.version).toBe('v3');
      expect(result?.currentScrapedValues?.title).toBe('Most Recent Title');
      expect(scrapeRepository.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          order: { scrapedAt: 'DESC' },
        }),
      );
    });

    it('should handle empty or invalid scrapedPageId values', async () => {
      const emptyPageId = '';
      const nullPageId = null as any;

      mockScrapeRepository.findOne.mockResolvedValue(null);

      // Test empty string
      const result1 = await service.findLatestByPageId(emptyPageId);
      expect(result1).toBeNull();

      // Test null (if supported by service)
      const result2 = await service.findLatestByPageId(nullPageId);
      expect(result2).toBeNull();
    });

    it('should return scrape with complex currentScrapedValues structure', async () => {
      const testPageId = 'complex-page';
      const complexLatestScrape = {
        id: 'complex-scrape',
        scrapedPageId: testPageId,
        currentScrapedValues: {
          title: 'Complex Page Title',
          metaDescription: 'Complex meta description',
          headings: {
            h1: ['Main Title'],
            h2: ['Section A', 'Section B'],
            h3: ['Sub A1', 'Sub A2', 'Sub B1'],
          },
          images: [
            {
              src: '/hero.jpg',
              alt: 'Hero Image',
              dimensions: { width: 1200, height: 600 },
            },
            {
              src: '/thumbnail.png',
              alt: 'Thumbnail',
              dimensions: { width: 300, height: 200 },
            },
          ],
          links: {
            internal: ['/about', '/contact'],
            external: ['https://partner.com'],
          },
          schema: {
            type: 'WebPage',
            breadcrumbs: ['Home', 'Category', 'Page'],
          },
          performance: {
            loadTime: 1.23,
            firstContentfulPaint: 0.8,
            cumulativeLayoutShift: 0.1,
          },
        },
        scrapedAt: new Date('2024-01-17T10:00:00Z'),
      };

      mockScrapeRepository.findOne.mockResolvedValue(complexLatestScrape);

      const result = await service.findLatestByPageId(testPageId);

      expect(result?.currentScrapedValues?.headings?.h2).toHaveLength(2);
      expect(result?.currentScrapedValues?.images[0].dimensions.width).toBe(
        1200,
      );
      expect(result?.currentScrapedValues?.schema?.type).toBe('WebPage');
      expect(result?.currentScrapedValues?.performance?.loadTime).toBe(1.23);
    });
  });

  describe('update', () => {
    it('should update a scrape successfully', async () => {
      const scrapeId = '1';
      const existingScrape = {
        id: scrapeId,
        rawHtml: '<html>old</html>',
        currentScrapedValues: { existing: 'data' },
      };
      const updateScrapeDto = {
        rawHtml: '<html>new</html>',
        currentScrapedValues: { new: 'values' },
      };
      const updatedScrape = {
        ...existingScrape,
        rawHtml: updateScrapeDto.rawHtml,
        currentScrapedValues: { existing: 'data', new: 'values' },
      };

      mockScrapeRepository.findOne
        .mockResolvedValueOnce(existingScrape) // First call in update method
        .mockResolvedValueOnce(updatedScrape); // Second call at end of update method
      mockScrapeRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(scrapeId, updateScrapeDto as any);

      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { id: scrapeId },
        relations: ['scrapedPage', 'recommendations'],
      });
      expect(scrapeRepository.update).toHaveBeenCalledWith(scrapeId, {
        rawHtml: updateScrapeDto.rawHtml,
        currentScrapedValues: { existing: 'data', new: 'values' },
      });
      expect(result).toEqual(updatedScrape);
    });

    it('should throw NotFoundException when scrape not found', async () => {
      const scrapeId = '1';
      const updateScrapeDto = { rawHtml: '<html>new</html>' };

      mockScrapeRepository.findOne.mockResolvedValue(null);

      await expect(
        service.update(scrapeId, updateScrapeDto as any),
      ).rejects.toThrow('Scrape with ID "1" not found');
    });

    it('should update without currentScrapedValues when not provided', async () => {
      const scrapeId = '1';
      const existingScrape = {
        id: scrapeId,
        rawHtml: '<html>old</html>',
        currentScrapedValues: { existing: 'data' },
      };
      const updateScrapeDto = { rawHtml: '<html>new</html>' };
      const updatedScrape = {
        ...existingScrape,
        rawHtml: updateScrapeDto.rawHtml,
      };

      mockScrapeRepository.findOne
        .mockResolvedValueOnce(existingScrape) // First call in update method
        .mockResolvedValueOnce(updatedScrape); // Second call at end of update method
      mockScrapeRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(scrapeId, updateScrapeDto as any);

      expect(scrapeRepository.update).toHaveBeenCalledWith(scrapeId, {
        rawHtml: updateScrapeDto.rawHtml,
      });
      expect(result).toEqual(updatedScrape);
    });

    it('should handle complex currentScrapedValues merge correctly', async () => {
      const scrapeId = 'complex-update-id';
      const existingScrape = {
        id: scrapeId,
        companyId,
        scrapedPageId,
        rawHtml:
          '<html><head><title>Old Title</title></head><body><h1>Old Header</h1></body></html>',
        markdown: '# Old Header\n\nOld content',
        currentScrapedValues: {
          title: 'Old Title',
          metaDescription: 'Old description',
          h1: 'Old Header',
          images: [{ src: '/old-image.jpg', alt: 'Old Image' }],
          seo: { titleLength: 9, h1Count: 1 },
        },
      };
      const updateScrapeDto = {
        rawHtml:
          '<html><head><title>New Title</title></head><body><h1>New Header</h1><h2>Section</h2></body></html>',
        markdown: '# New Header\n\n## Section\n\nNew content',
        currentScrapedValues: {
          title: 'New Title',
          h1: 'New Header',
          h2: ['Section'],
          images: [
            { src: '/old-image.jpg', alt: 'Old Image' },
            { src: '/new-image.jpg', alt: 'New Image' },
          ],
          seo: { titleLength: 9, h1Count: 1, h2Count: 1 },
        },
      };
      const expectedMergedValues = {
        title: 'New Title',
        metaDescription: 'Old description',
        h1: 'New Header',
        h2: ['Section'],
        images: [
          { src: '/old-image.jpg', alt: 'Old Image' },
          { src: '/new-image.jpg', alt: 'New Image' },
        ],
        seo: { titleLength: 9, h1Count: 1, h2Count: 1 }, // This replaces the entire seo object
      };
      const updatedScrape = {
        ...existingScrape,
        rawHtml: updateScrapeDto.rawHtml,
        markdown: updateScrapeDto.markdown,
        currentScrapedValues: expectedMergedValues,
      };

      mockScrapeRepository.findOne
        .mockResolvedValueOnce(existingScrape) // First call in update method
        .mockResolvedValueOnce(updatedScrape); // Second call at end of update method
      mockScrapeRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(scrapeId, updateScrapeDto as any);

      expect(result.currentScrapedValues.metaDescription).toBe(
        'Old description',
      );
      expect(result.currentScrapedValues.title).toBe('New Title');
      expect(result.currentScrapedValues.h2).toEqual(['Section']);
      expect(result.currentScrapedValues.images).toHaveLength(2);
      expect(result.currentScrapedValues.seo.h2Count).toBe(1);
    });

    it('should handle large content updates', async () => {
      const scrapeId = 'large-update-id';
      const existingScrape = {
        id: scrapeId,
        rawHtml: '<html>small content</html>',
        markdown: 'small content',
        currentScrapedValues: { size: 'small' },
      };
      const largeHtml =
        '<html>' +
        '<div>'.repeat(2000) +
        'Large updated content' +
        '</div>'.repeat(2000) +
        '</html>';
      const largeMarkdown =
        '#'.repeat(100) + ' Large Title\n\n' + 'Content '.repeat(1000);
      const updateScrapeDto = {
        rawHtml: largeHtml,
        markdown: largeMarkdown,
        currentScrapedValues: {
          size: 'large',
          contentLength: largeMarkdown.length,
        },
      };
      const updatedScrape = {
        ...existingScrape,
        rawHtml: updateScrapeDto.rawHtml,
        markdown: updateScrapeDto.markdown,
        currentScrapedValues: {
          size: 'large',
          contentLength: largeMarkdown.length,
        },
      };

      mockScrapeRepository.findOne
        .mockResolvedValueOnce(existingScrape) // First call in update method
        .mockResolvedValueOnce(updatedScrape); // Second call at end of update method
      mockScrapeRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(scrapeId, updateScrapeDto as any);

      expect(result.rawHtml.length).toBeGreaterThan(20000);
      expect(result.markdown.length).toBeGreaterThan(5000);
      expect(result.currentScrapedValues.size).toBe('large');
    });

    it('should handle repository update errors during update', async () => {
      const scrapeId = 'error-update-id';
      const existingScrape = { id: scrapeId, rawHtml: '<html>old</html>' };
      const updateScrapeDto = { rawHtml: '<html>new</html>' };
      const updateError = new Error('Database update failed');

      mockScrapeRepository.findOne.mockResolvedValue(existingScrape);
      mockScrapeRepository.update.mockRejectedValue(updateError);

      await expect(
        service.update(scrapeId, updateScrapeDto as any),
      ).rejects.toThrow(updateError);
      expect(scrapeRepository.findOne).toHaveBeenCalledWith({
        where: { id: scrapeId },
        relations: ['scrapedPage', 'recommendations'],
      });
    });

    it('should update only specified fields', async () => {
      const scrapeId = 'partial-update-id';
      const existingScrape = {
        id: scrapeId,
        companyId,
        scrapedPageId,
        rawHtml: '<html>original</html>',
        markdown: '# Original',
        currentScrapedValues: { title: 'Original', status: 'active' },
        mediaId: 'original-media',
      };
      const partialUpdateDto = {
        markdown: '# Updated',
        currentScrapedValues: { title: 'Updated' },
      };
      const updatedScrape = {
        ...existingScrape,
        markdown: partialUpdateDto.markdown,
        currentScrapedValues: { title: 'Updated', status: 'active' },
      };

      mockScrapeRepository.findOne
        .mockResolvedValueOnce(existingScrape) // First call in update method
        .mockResolvedValueOnce(updatedScrape); // Second call at end of update method
      mockScrapeRepository.update.mockResolvedValue({ affected: 1 });

      const result = await service.update(scrapeId, partialUpdateDto as any);

      expect(result.rawHtml).toBe('<html>original</html>');
      expect(result.markdown).toBe('# Updated');
      expect(result.mediaId).toBe('original-media');
      expect(result.currentScrapedValues.title).toBe('Updated');
      expect(result.currentScrapedValues.status).toBe('active');
    });
  });

  describe('remove', () => {
    it('should delete a scrape successfully', async () => {
      const scrapeId = '1';
      const deleteResult = { affected: 1 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
      expect(result).toEqual(deleteResult);
    });

    it('should return delete result when scrape not found', async () => {
      const scrapeId = 'non-existent-id';
      const deleteResult = { affected: 0 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
      expect(result).toEqual(deleteResult);
    });

    it('should delete scrape regardless of relationships', async () => {
      const scrapeId = 'scrape-with-relations';
      const deleteResult = { affected: 1 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
      expect(result).toEqual(deleteResult);
    });

    it('should handle cascade delete scenarios', async () => {
      const scrapeId = 'cascade-delete-id';
      const deleteResult = { affected: 1 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(result.affected).toBe(1);
      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
    });

    it('should handle repository delete errors', async () => {
      const scrapeId = 'error-delete-id';
      const deleteError = new Error('Foreign key constraint violation');

      mockScrapeRepository.delete.mockRejectedValue(deleteError);

      await expect(service.remove(scrapeId)).rejects.toThrow(deleteError);
      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
    });

    it('should handle delete result with no affected rows', async () => {
      const scrapeId = 'no-affect-id';
      const deleteResult = { affected: 0 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(result.affected).toBe(0);
      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
    });

    it('should directly attempt deletion without verification', async () => {
      const scrapeId = 'direct-delete-id';
      const deleteResult = { affected: 1 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(scrapeId);

      expect(scrapeRepository.delete).toHaveBeenCalledWith({ id: scrapeId });
      expect(result).toEqual(deleteResult);
      // Verify that findOne is never called in the simplified implementation
      expect(scrapeRepository.findOne).not.toHaveBeenCalled();
    });

    it('should handle different UUID formats in delete operation', async () => {
      const uuidScrapeId = '123e4567-e89b-12d3-a456-************';
      const deleteResult = { affected: 1 };

      mockScrapeRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(uuidScrapeId);

      expect(scrapeRepository.delete).toHaveBeenCalledWith({
        id: uuidScrapeId,
      });
      expect(result.affected).toBe(1);
    });
  });
});
