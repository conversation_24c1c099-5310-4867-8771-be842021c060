import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Scrape } from './entities/scrape.entity';
import { ScrapeController } from './scrape.controller';
import { ScrapeResolver } from './scrape.resolver';
import { ScrapeService } from './scrape.service';

@Module({
  controllers: [ScrapeController],
  providers: [ScrapeService, ScrapeResolver],
  imports: [TypeOrmModule.forFeature([Scrape])],
})
export class ScrapeModule {}
