import { Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { Company } from 'src/company/entities/company.entity';

import { Scrape } from './entities/scrape.entity';

@Resolver(() => Scrape)
export class ScrapeResolver {
  @ResolveField(() => Company, { nullable: true })
  company(@Parent() scrape: Scrape): Company | null {
    if (!scrape.companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: scrape.companyId } as Company;
  }
}
