import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID, IsNotEmpty, IsString, IsObject } from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Factory } from 'nestjs-seeder';
import { ScrapedValues } from 'src/common/types/scraped-element.type';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Company } from 'src/company/entities/company.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import {
  PrimaryGeneratedColumn,
  Column,
  Index,
  Entity,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('scrape')
@ObjectType()
export class Scrape {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'company_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  companyId: string;

  @Column({ type: 'uuid', name: 'scraped_page_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  scrapedPageId: string;

  @Column({ type: 'text', name: 'raw_html' })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.scrapeRawHtml)
  @Field(() => String)
  rawHtml: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.scrapeMarkdown)
  @Field(() => String)
  markdown: string;

  @Column({ type: 'jsonb', name: 'current_scraped_values', default: '{}' })
  @IsObject()
  @Factory(SeederFactories.scrapeCurrentValues)
  @Field(() => GraphQLJSONObject, { nullable: true })
  currentScrapedValues: ScrapedValues;

  @Column({ type: 'uuid', name: 'media_id', nullable: true })
  @IsUUID()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID, { nullable: true })
  mediaId: string;

  @CreateDateColumn({ name: 'scraped_at', type: 'timestamptz' })
  @Field(() => Date)
  scrapedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @ManyToOne(() => ScrapedPage, scrapedPage => scrapedPage.scrapes)
  @JoinColumn({ name: 'scraped_page_id' })
  @Field(() => ScrapedPage)
  scrapedPage: ScrapedPage;

  @OneToMany(() => Recommendation, recommendation => recommendation.scrape)
  @Field(() => [Recommendation], { nullable: true })
  recommendations: Recommendation[];

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}
