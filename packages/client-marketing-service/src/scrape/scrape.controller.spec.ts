import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CreateScrapeDto } from './dto/create-scrape.dto';
import { UpdateScrapeDto } from './dto/update-scrape.dto';
import { ScrapeController } from './scrape.controller';
import { ScrapeService } from './scrape.service';

describe('ScrapeController', () => {
  let controller: ScrapeController;
  let scrapeService: ScrapeService;

  const mockScrapeService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScrapeController],
      providers: [
        {
          provide: ScrapeService,
          useValue: mockScrapeService,
        },
      ],
    }).compile();

    controller = module.get<ScrapeController>(ScrapeController);
    scrapeService = module.get<ScrapeService>(ScrapeService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new scrape', async () => {
      const createScrapeDto: CreateScrapeDto = {
        companyId: 'company-1',
        scrapedPageId: 'page-1',
        rawHtml: '<html>test</html>',
        markdown: '# Test',
        currentScrapedValues: { title: 'Test' },
      } as any;
      const createdScrape = { id: '1', ...createScrapeDto };

      mockScrapeService.create.mockResolvedValue(createdScrape);

      const response = await controller.create(createScrapeDto);

      expect(scrapeService.create).toHaveBeenCalledWith(createScrapeDto);
      expect(response).toEqual(createdScrape);
    });

    it('should create a scrape with complex data', async () => {
      const complexCreateDto: CreateScrapeDto = {
        companyId: 'complex-company',
        scrapedPageId: 'complex-page',
        rawHtml:
          '<html><head><title>Complex Page</title><meta name="description" content="Complex description"/></head><body><h1>Main</h1><h2>Sub</h2></body></html>',
        markdown: '# Main\n\n## Sub\n\nComplex content',
        currentScrapedValues: {
          title: 'Complex Page',
          metaDescription: 'Complex description',
          h1: 'Main',
          h2: ['Sub'],
          images: [{ src: '/image.jpg', alt: 'Image' }],
          links: [{ href: '/page', text: 'Page' }],
          seoScore: { titleLength: 12, descriptionLength: 19 },
        },
        mediaId: 'complex-media-id',
      } as any;
      const createdComplexScrape = { id: 'complex-id', ...complexCreateDto };

      mockScrapeService.create.mockResolvedValue(createdComplexScrape);

      const response = await controller.create(complexCreateDto);

      expect(scrapeService.create).toHaveBeenCalledWith(complexCreateDto);
      expect(response.currentScrapedValues.seoScore.titleLength).toBe(12);
      expect(response.mediaId).toBe('complex-media-id');
    });

    it('should create a scrape with minimal required fields', async () => {
      const minimalCreateDto: CreateScrapeDto = {
        companyId: 'minimal-company',
        scrapedPageId: 'minimal-page',
        rawHtml: '<html><body>Minimal</body></html>',
        markdown: 'Minimal',
        currentScrapedValues: {},
      } as any;
      const createdMinimalScrape = { id: 'minimal-id', ...minimalCreateDto };

      mockScrapeService.create.mockResolvedValue(createdMinimalScrape);

      const response = await controller.create(minimalCreateDto);

      expect(scrapeService.create).toHaveBeenCalledWith(minimalCreateDto);
      expect(response.currentScrapedValues).toEqual({});
      expect(response.mediaId).toBeUndefined();
    });

    it('should handle service errors during creation', async () => {
      const createScrapeDto: CreateScrapeDto = {
        companyId: 'error-company',
        scrapedPageId: 'error-page',
        rawHtml: '<html>error</html>',
        markdown: '# Error',
        currentScrapedValues: { title: 'Error' },
      } as any;
      const serviceError = new Error('Database constraint violation');

      mockScrapeService.create.mockRejectedValue(serviceError);

      await expect(controller.create(createScrapeDto)).rejects.toThrow(
        serviceError,
      );
      expect(scrapeService.create).toHaveBeenCalledWith(createScrapeDto);
    });

    it('should create scrape with large content', async () => {
      const largeHtml =
        '<html>' +
        '<div>'.repeat(1000) +
        'Large content' +
        '</div>'.repeat(1000) +
        '</html>';
      const largeCreateDto: CreateScrapeDto = {
        companyId: 'large-company',
        scrapedPageId: 'large-page',
        rawHtml: largeHtml,
        markdown: 'Large content',
        currentScrapedValues: { size: 'large' },
      } as any;
      const createdLargeScrape = { id: 'large-id', ...largeCreateDto };

      mockScrapeService.create.mockResolvedValue(createdLargeScrape);

      const response = await controller.create(largeCreateDto);

      expect(scrapeService.create).toHaveBeenCalledWith(largeCreateDto);
      expect(response.rawHtml.length).toBeGreaterThan(10000);
    });
  });

  describe('findAll', () => {
    it('should return all scrapes', async () => {
      const scrapes = [
        { id: '1', companyId: 'company-1', scrapedPageId: 'page-1' },
        { id: '2', companyId: 'company-2', scrapedPageId: 'page-2' },
      ];

      mockScrapeService.findAll.mockResolvedValue(scrapes);

      const response = await controller.findAll();

      expect(scrapeService.findAll).toHaveBeenCalled();
      expect(response).toEqual(scrapes);
    });

    it('should return empty array when no scrapes exist', async () => {
      mockScrapeService.findAll.mockResolvedValue([]);

      const response = await controller.findAll();

      expect(scrapeService.findAll).toHaveBeenCalled();
      expect(response).toEqual([]);
    });

    it('should return scrapes with relationships populated', async () => {
      const scrapesWithRelations = [
        {
          id: '1',
          companyId: 'company-1',
          scrapedPageId: 'page-1',
          scrapedPage: { id: 'page-1', url: 'https://example.com/page1' },
          recommendations: [{ id: 'rec-1', type: 'META_TITLE' }],
        },
        {
          id: '2',
          companyId: 'company-1',
          scrapedPageId: 'page-2',
          scrapedPage: { id: 'page-2', url: 'https://example.com/page2' },
          recommendations: [],
        },
      ];

      mockScrapeService.findAll.mockResolvedValue(scrapesWithRelations);

      const response = await controller.findAll();

      expect(scrapeService.findAll).toHaveBeenCalled();
      expect(response).toHaveLength(2);
      expect(response[0].scrapedPage).toBeDefined();
      expect(response[0].recommendations).toHaveLength(1);
    });

    it('should handle large number of scrapes', async () => {
      const manyScrapes = Array.from({ length: 1000 }, (_, index) => ({
        id: `scrape-${index}`,
        companyId: 'bulk-company',
        scrapedPageId: `page-${index}`,
      }));

      mockScrapeService.findAll.mockResolvedValue(manyScrapes);

      const response = await controller.findAll();

      expect(scrapeService.findAll).toHaveBeenCalled();
      expect(response).toHaveLength(1000);
    });

    it('should handle service errors during findAll', async () => {
      const serviceError = new Error('Database connection failed');

      mockScrapeService.findAll.mockRejectedValue(serviceError);

      await expect(controller.findAll()).rejects.toThrow(serviceError);
      expect(scrapeService.findAll).toHaveBeenCalled();
    });

    it('should return scrapes with complex currentScrapedValues', async () => {
      const complexScrapes = [
        {
          id: '1',
          companyId: 'complex-company',
          scrapedPageId: 'complex-page',
          currentScrapedValues: {
            title: 'Complex Page',
            metaDescription: 'Complex description',
            headings: { h1: ['Main'], h2: ['Sub1', 'Sub2'] },
            images: [{ src: '/image1.jpg', alt: 'Image 1' }],
            seo: { titleLength: 12, h1Count: 1, h2Count: 2 },
          },
        },
      ];

      mockScrapeService.findAll.mockResolvedValue(complexScrapes);

      const response = await controller.findAll();

      expect(response[0].currentScrapedValues.seo.h2Count).toBe(2);
      expect(response[0].currentScrapedValues.headings.h2).toHaveLength(2);
    });
  });

  describe('findOne', () => {
    it('should return a single scrape', async () => {
      const scrapeId = '1';
      const scrape = {
        id: scrapeId,
        companyId: 'company-1',
        scrapedPageId: 'page-1',
      };

      mockScrapeService.findOne.mockResolvedValue(scrape);

      const response = await controller.findOne(scrapeId);

      expect(scrapeService.findOne).toHaveBeenCalledWith(scrapeId);
      expect(response).toEqual(scrape);
    });

    it('should return scrape with complete relationships', async () => {
      const scrapeId = 'detailed-scrape-id';
      const detailedScrape = {
        id: scrapeId,
        companyId: 'detailed-company',
        scrapedPageId: 'detailed-page',
        rawHtml:
          '<html><head><title>Detailed Page</title></head><body><h1>Content</h1></body></html>',
        markdown: '# Content\n\nDetailed content',
        currentScrapedValues: {
          title: 'Detailed Page',
          metaDescription: 'Detailed description',
          h1: 'Content',
          images: [{ src: '/detail.jpg', alt: 'Detail Image' }],
          seo: { titleLength: 13, h1Count: 1 },
        },
        mediaId: 'detail-media-id',
        scrapedAt: new Date('2024-01-15T10:00:00Z'),
        scrapedPage: {
          id: 'detailed-page',
          url: 'https://example.com/detailed',
          companyId: 'detailed-company',
        },
        recommendations: [
          { id: 'rec-1', type: 'META_TITLE', status: 'PENDING' },
          { id: 'rec-2', type: 'META_DESCRIPTION', status: 'APPLIED' },
        ],
      };

      mockScrapeService.findOne.mockResolvedValue(detailedScrape);

      const response = await controller.findOne(scrapeId);

      expect(response.scrapedPage).toBeDefined();
      expect(response.recommendations).toHaveLength(2);
      expect(response.currentScrapedValues.seo.titleLength).toBe(13);
      expect(response.mediaId).toBe('detail-media-id');
    });

    it('should handle different ID formats', async () => {
      const uuidScrapeId = '123e4567-e89b-12d3-a456-************';
      const scrape = { id: uuidScrapeId, companyId: 'uuid-company' };

      mockScrapeService.findOne.mockResolvedValue(scrape);

      const response = await controller.findOne(uuidScrapeId);

      expect(scrapeService.findOne).toHaveBeenCalledWith(uuidScrapeId);
      expect(response.id).toBe(uuidScrapeId);
    });

    it('should throw NotFoundException when scrape not found', async () => {
      const scrapeId = 'non-existent-id';

      mockScrapeService.findOne.mockRejectedValue(
        new NotFoundException(`Scrape with ID "${scrapeId}" not found`),
      );

      await expect(controller.findOne(scrapeId)).rejects.toThrow(
        NotFoundException,
      );
      expect(scrapeService.findOne).toHaveBeenCalledWith(scrapeId);
    });

    it('should handle service errors during findOne', async () => {
      const scrapeId = 'error-id';
      const serviceError = new Error('Database connection failed');

      mockScrapeService.findOne.mockRejectedValue(serviceError);

      await expect(controller.findOne(scrapeId)).rejects.toThrow(serviceError);
      expect(scrapeService.findOne).toHaveBeenCalledWith(scrapeId);
    });

    it('should return scrape with complex nested data structures', async () => {
      const scrapeId = 'complex-nested-id';
      const complexScrape = {
        id: scrapeId,
        currentScrapedValues: {
          title: 'Complex Nested Page',
          metaDescription: 'Complex nested description',
          headings: {
            h1: ['Main Title'],
            h2: ['Section A', 'Section B'],
            h3: ['Sub A1', 'Sub A2', 'Sub B1'],
          },
          images: [
            {
              src: '/hero.jpg',
              alt: 'Hero',
              dimensions: { width: 1200, height: 600 },
            },
            {
              src: '/thumb.png',
              alt: 'Thumbnail',
              dimensions: { width: 300, height: 200 },
            },
          ],
          links: {
            internal: ['/about', '/contact', '/services'],
            external: ['https://partner.com', 'https://social.com'],
          },
          schema: {
            type: 'WebPage',
            breadcrumbs: ['Home', 'Category', 'Page'],
            author: { name: 'John Doe', url: 'https://johndoe.com' },
          },
        },
      };

      mockScrapeService.findOne.mockResolvedValue(complexScrape);

      const response = await controller.findOne(scrapeId);

      expect(response.currentScrapedValues.headings.h3).toHaveLength(3);
      expect(response.currentScrapedValues.images[0].dimensions.width).toBe(
        1200,
      );
      expect(response.currentScrapedValues.schema.author.name).toBe('John Doe');
    });
  });

  describe('update', () => {
    it('should update a scrape', async () => {
      const scrapeId = '1';
      const updateScrapeDto: UpdateScrapeDto = {
        rawHtml: '<html>updated</html>',
        currentScrapedValues: { title: 'Updated' },
      };
      const updatedScrape = {
        id: scrapeId,
        ...updateScrapeDto,
      };

      mockScrapeService.update.mockResolvedValue(updatedScrape);

      const response = await controller.update(scrapeId, updateScrapeDto);

      expect(scrapeService.update).toHaveBeenCalledWith(
        scrapeId,
        updateScrapeDto,
      );
      expect(response).toEqual(updatedScrape);
    });

    it('should update scrape with complex merged data', async () => {
      const scrapeId = 'complex-update-id';
      const complexUpdateDto: UpdateScrapeDto = {
        rawHtml:
          '<html><head><title>Updated Complex</title></head><body><h1>Updated Header</h1><h2>New Section</h2></body></html>',
        markdown: '# Updated Header\n\n## New Section\n\nUpdated content',
        currentScrapedValues: {
          title: 'Updated Complex',
          h1: 'Updated Header',
          h2: ['New Section'],
          seo: { titleLength: 15, h1Count: 1, h2Count: 1 },
        },
      };
      const updatedComplexScrape = {
        id: scrapeId,
        companyId: 'complex-company',
        scrapedPageId: 'complex-page',
        rawHtml: complexUpdateDto.rawHtml,
        markdown: complexUpdateDto.markdown,
        currentScrapedValues: {
          title: 'Updated Complex',
          metaDescription: 'Original description',
          h1: 'Updated Header',
          h2: ['New Section'],
          seo: { titleLength: 15, h1Count: 1, h2Count: 1 },
        },
      };

      mockScrapeService.update.mockResolvedValue(updatedComplexScrape);

      const response = await controller.update(scrapeId, complexUpdateDto);

      expect(scrapeService.update).toHaveBeenCalledWith(
        scrapeId,
        complexUpdateDto,
      );
      expect(response.currentScrapedValues.title).toBe('Updated Complex');
      expect(response.currentScrapedValues.metaDescription).toBe(
        'Original description',
      );
    });

    it('should update scrape with partial data', async () => {
      const scrapeId = 'partial-update-id';
      const partialUpdateDto: UpdateScrapeDto = {
        markdown: '# Partially Updated',
      };
      const partiallyUpdatedScrape = {
        id: scrapeId,
        companyId: 'partial-company',
        scrapedPageId: 'partial-page',
        rawHtml: '<html>original</html>',
        markdown: '# Partially Updated',
        currentScrapedValues: { title: 'Original Title' },
      };

      mockScrapeService.update.mockResolvedValue(partiallyUpdatedScrape);

      const response = await controller.update(scrapeId, partialUpdateDto);

      expect(scrapeService.update).toHaveBeenCalledWith(
        scrapeId,
        partialUpdateDto,
      );
      expect(response.markdown).toBe('# Partially Updated');
      expect(response.rawHtml).toBe('<html>original</html>');
    });

    it('should update scrape with large content', async () => {
      const scrapeId = 'large-update-id';
      const largeHtml =
        '<html>' +
        '<div>'.repeat(2000) +
        'Large updated content' +
        '</div>'.repeat(2000) +
        '</html>';
      const largeUpdateDto: UpdateScrapeDto = {
        rawHtml: largeHtml,
        currentScrapedValues: { size: 'very-large' },
      };
      const largeUpdatedScrape = {
        id: scrapeId,
        rawHtml: largeHtml,
        currentScrapedValues: { size: 'very-large' },
      };

      mockScrapeService.update.mockResolvedValue(largeUpdatedScrape);

      const response = await controller.update(scrapeId, largeUpdateDto);

      expect(response.rawHtml.length).toBeGreaterThan(20000);
      expect(response.currentScrapedValues.size).toBe('very-large');
    });

    it('should throw NotFoundException when scrape not found', async () => {
      const scrapeId = 'non-existent-update-id';
      const updateScrapeDto: UpdateScrapeDto = {
        rawHtml: '<html>updated</html>',
      };

      mockScrapeService.update.mockRejectedValue(
        new NotFoundException(`Scrape with ID "${scrapeId}" not found`),
      );

      await expect(
        controller.update(scrapeId, updateScrapeDto),
      ).rejects.toThrow(NotFoundException);
      expect(scrapeService.update).toHaveBeenCalledWith(
        scrapeId,
        updateScrapeDto,
      );
    });

    it('should handle service errors during update', async () => {
      const scrapeId = 'error-update-id';
      const updateScrapeDto: UpdateScrapeDto = {
        rawHtml: '<html>error</html>',
      };
      const serviceError = new Error('Database update failed');

      mockScrapeService.update.mockRejectedValue(serviceError);

      await expect(
        controller.update(scrapeId, updateScrapeDto),
      ).rejects.toThrow(serviceError);
      expect(scrapeService.update).toHaveBeenCalledWith(
        scrapeId,
        updateScrapeDto,
      );
    });

    it('should update scrape with media ID changes', async () => {
      const scrapeId = 'media-update-id';
      const mediaUpdateDto: UpdateScrapeDto = {
        currentScrapedValues: { mediaUpdated: true },
      };
      const mediaUpdatedScrape = {
        id: scrapeId,
        mediaId: 'new-media-id',
        currentScrapedValues: { mediaUpdated: true },
      };

      mockScrapeService.update.mockResolvedValue(mediaUpdatedScrape);

      const response = await controller.update(scrapeId, mediaUpdateDto);

      expect(response.mediaId).toBe('new-media-id');
      expect(response.currentScrapedValues.mediaUpdated).toBe(true);
    });
  });

  describe('remove', () => {
    it('should remove a scrape', async () => {
      const scrapeId = '1';
      const deleteResult = { affected: 1 };

      mockScrapeService.remove.mockResolvedValue(deleteResult);

      const response = await controller.remove(scrapeId);

      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
      expect(response).toEqual(deleteResult);
    });

    it('should remove scrape and return affected count', async () => {
      const scrapeId = 'delete-success-id';
      const successDeleteResult = { affected: 1, raw: [] };

      mockScrapeService.remove.mockResolvedValue(successDeleteResult);

      const response = await controller.remove(scrapeId);

      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
      expect(response.affected).toBe(1);
      expect(response).toEqual(successDeleteResult);
    });

    it('should handle delete result with no affected rows', async () => {
      const scrapeId = 'no-affect-delete-id';
      const noAffectResult = { affected: 0 };

      mockScrapeService.remove.mockResolvedValue(noAffectResult);

      const response = await controller.remove(scrapeId);

      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
      expect(response.affected).toBe(0);
    });

    it('should handle different UUID formats for deletion', async () => {
      const uuidScrapeId = '123e4567-e89b-12d3-a456-************';
      const deleteResult = { affected: 1 };

      mockScrapeService.remove.mockResolvedValue(deleteResult);

      const response = await controller.remove(uuidScrapeId);

      expect(scrapeService.remove).toHaveBeenCalledWith(uuidScrapeId);
      expect(response.affected).toBe(1);
    });

    it('should throw NotFoundException when scrape not found', async () => {
      const scrapeId = 'non-existent-delete-id';

      mockScrapeService.remove.mockRejectedValue(
        new NotFoundException(`Scrape with ID "${scrapeId}" not found`),
      );

      await expect(controller.remove(scrapeId)).rejects.toThrow(
        NotFoundException,
      );
      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
    });

    it('should handle service errors during deletion', async () => {
      const scrapeId = 'error-delete-id';
      const serviceError = new Error('Foreign key constraint violation');

      mockScrapeService.remove.mockRejectedValue(serviceError);

      await expect(controller.remove(scrapeId)).rejects.toThrow(serviceError);
      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
    });

    it('should handle cascade deletion scenarios', async () => {
      const scrapeId = 'cascade-delete-id';
      const cascadeDeleteResult = {
        affected: 1,
        cascadeDeletedRecommendations: 3,
        message: 'Scrape and related recommendations deleted',
      };

      mockScrapeService.remove.mockResolvedValue(cascadeDeleteResult);

      const response = await controller.remove(scrapeId);

      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
      expect(response.affected).toBe(1);
      expect(response).toEqual(cascadeDeleteResult);
    });

    it('should handle deletion of scrape with complex relationships', async () => {
      const scrapeId = 'complex-relationship-delete-id';
      const complexDeleteResult = {
        affected: 1,
        relatedEntitiesAffected: {
          recommendations: 5,
          pageHistory: 1,
        },
      };

      mockScrapeService.remove.mockResolvedValue(complexDeleteResult);

      const response = await controller.remove(scrapeId);

      expect(response.affected).toBe(1);
      expect(response).toEqual(complexDeleteResult);
    });

    it('should handle database constraint errors during deletion', async () => {
      const scrapeId = 'constraint-error-id';
      const constraintError = new Error(
        'Cannot delete scrape: referenced by active recommendations',
      );
      constraintError.name = 'ForeignKeyConstraintError';

      mockScrapeService.remove.mockRejectedValue(constraintError);

      await expect(controller.remove(scrapeId)).rejects.toThrow(
        constraintError,
      );
      expect(scrapeService.remove).toHaveBeenCalledWith(scrapeId);
    });
  });
});
