import { ApiProperty, getSchemaPath } from '@nestjs/swagger';
import {
  SchemaObject,
  ReferenceObject,
} from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';

export function paginatedSchema(
  model: string | { new (...args: any[]): any },
): SchemaObject & Partial<ReferenceObject> {
  return {
    allOf: [
      { $ref: getSchemaPath(PaginatedResponse) },
      {
        properties: {
          data: {
            type: 'array',
            items: { $ref: getSchemaPath(model) },
          },
        },
      },
    ],
  };
}

export class PaginatedResponse<T> {
  // This field is to be overridden by consumers of the schema when defining
  // the response in OpenAPI. This is due to lack of support of generics
  // in OpenAPI (at least with the integration with NestJS).
  data: T[];

  @ApiProperty({
    type: 'object',
    additionalProperties: false,
    properties: {
      itemsPerPage: { type: 'number' },
      totalItems: { type: 'number' },
      currentPage: { type: 'number' },
      totalPages: { type: 'number' },
      sortBy: {
        type: 'array',
        items: {
          type: 'array',
          minItems: 2,
          maxItems: 2,
          items: {
            oneOf: [{ type: 'string' }, { enum: ['ASC', 'DESC'] }],
          },
        },
      },
      searchBy: { type: 'array', items: { type: 'string' } },
      search: { type: 'string' },
      filter: {
        type: 'object',
        additionalProperties: true,
      },
    },
  })
  meta: Record<string, any>;

  @ApiProperty({
    type: 'object',
    additionalProperties: false,
    properties: {
      first: { type: 'string' },
      previous: { type: 'string' },
      current: { type: 'string' },
      next: { type: 'string' },
      last: { type: 'string' },
    },
  })
  links: Record<string, any>;
}
