import { ExecutionContext } from '@nestjs/common';
import { Observable, of } from 'rxjs';

import { WrapResponseInterceptor } from './response-wrapper.interceptor';

describe('WrapResponseInterceptor', () => {
  let interceptor: WrapResponseInterceptor<any>;
  let mockExecutionContext: ExecutionContext;

  beforeEach(() => {
    interceptor = new WrapResponseInterceptor();
    mockExecutionContext = {
      getClass() {
        return { name: 'MockController' };
      },
    } as ExecutionContext;
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  it('should wrap response data in a data property', done => {
    const mockData = { message: 'Test message' };
    const mockCallHandler = {
      handle: () => of(mockData),
    };

    const result$ = interceptor.intercept(
      mockExecutionContext,
      mockCallHandler as any,
    );

    expect(result$).toBeInstanceOf(Observable);

    result$.subscribe(result => {
      expect(result).toEqual({ data: mockData });
      done();
    });
  });

  it('should preserve existing data property if it exists', done => {
    const mockData = { data: { message: 'Already wrapped' } };
    const mockCallHandler = {
      handle: () => of(mockData),
    };

    const result$ = interceptor.intercept(
      mockExecutionContext,
      mockCallHandler as any,
    );

    result$.subscribe(result => {
      expect(result).toEqual(mockData);
      done();
    });
  });

  it('should handle null or undefined response', done => {
    const mockCallHandler = {
      handle: () => of(null),
    };

    const result$ = interceptor.intercept(
      mockExecutionContext,
      mockCallHandler as any,
    );

    result$.subscribe(result => {
      expect(result).toEqual({ data: null });
      done();
    });
  });

  it('should handle array responses', done => {
    const mockData = [1, 2, 3];
    const mockCallHandler = {
      handle: () => of(mockData),
    };

    const result$ = interceptor.intercept(
      mockExecutionContext,
      mockCallHandler as any,
    );

    result$.subscribe(result => {
      expect(result).toEqual({ data: mockData });
      done();
    });
  });

  it('should handle primitive responses', done => {
    const mockData = 'string response';
    const mockCallHandler = {
      handle: () => of(mockData),
    };

    const result$ = interceptor.intercept(
      mockExecutionContext,
      mockCallHandler as any,
    );

    result$.subscribe(result => {
      expect(result).toEqual({ data: mockData });
      done();
    });
  });
});
