import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export class DataResponseDto<T> {
  @ApiProperty({ description: 'Wrapped response data' })
  @Type(() => Object)
  data: T;
}

@Injectable()
export class WrapResponseInterceptor<T>
  implements NestInterceptor<T, { data: T } | T>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler<T>,
  ): Observable<{ data: T } | T> {
    return next.handle().pipe(
      map(result => {
        // TODO: remove this interceptor once all controllers are replaced by GraphQL
        if (context.getClass().name.endsWith('Resolver')) {
          return result;
        }

        if (result && typeof result === 'object' && 'data' in result) {
          return result as { data: T }; // already has a data key
        }
        return { data: result };
      }),
    );
  }
}
