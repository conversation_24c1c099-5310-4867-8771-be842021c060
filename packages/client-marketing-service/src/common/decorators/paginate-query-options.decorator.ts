import { applyDecorators } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';
import { FilterOperator, FilterSuffix, PaginateConfig } from 'nestjs-paginate';

const asCode = (x: string) => `\`${x}\``;

const buildExamples = (operators: (FilterOperator | FilterSuffix)[]) => {
  const examples = {
    none: {
      summary: `No value`,
      value: null,
    },
    implicit: {
      summary: `Using implicit equals`,
      value: `value`,
    },
  };
  for (const operator of operators) {
    switch (operator) {
      case FilterOperator.IN:
        examples[operator] = {
          summary: `Using ${operator}`,
          value: `${operator}:value1,value2`,
        };
        break;
      case FilterOperator.BTW:
        examples[operator] = {
          summary: `Using ${operator}`,
          value: `${operator}:lower,upper`,
        };
        break;
      case FilterOperator.NULL:
        examples[operator] = {
          summary: `Using ${operator}`,
          value: `${operator}`,
        };
        break;
      case FilterOperator.LTE:
        examples[operator] = {
          summary: `Using ${operator}`,
          value: `${operator}:value`,
        };
        break;
      default:
        examples[operator] = {
          summary: `Using ${operator}`,
          value: `${operator}:value`,
        };
    }
  }

  return examples;
};

export function PaginateQueryOptions(config: PaginateConfig<any>) {
  type DecoratorType = MethodDecorator & ClassDecorator;

  const decoratorsWithUndefined: (DecoratorType | undefined)[] = [
    ApiQuery({
      name: 'page',
      required: false,
      type: 'integer',
      description: 'The page of results to return.',
      example: 1,
    }),
    ApiQuery({
      name: 'limit',
      required: false,
      type: 'integer',
      description: `The maximum amount of entities to return per page. The default limit is ${config.defaultLimit || 20} and the max limit is ${config.maxLimit || 100}`,
      example: Math.min(config.defaultLimit || 20, config.maxLimit || 100),
    }),
    config.searchableColumns?.length
      ? ApiQuery({
          name: 'search',
          required: false,
          type: 'string',
          description: `The text to search for. Searchable columns are: ${config.searchableColumns.map(asCode).join(', ')}`,
        })
      : undefined,
    ApiQuery({
      name: 'sortBy',
      required: false,
      type: 'string',
      description: `The order to display the sorted entities. Sortable columns are: ${config.sortableColumns.map(asCode).join(', ')}`,
      example:
        config.defaultSortBy?.[0]?.join(':') ||
        (config.sortableColumns?.[0]
          ? `${config.sortableColumns?.[0]}:DESC`
          : undefined),
    }),
  ];

  if (config.filterableColumns) {
    for (const [columnName, operators] of Object.entries<
      (FilterOperator | FilterSuffix)[] | true
    >(
      config.filterableColumns as Record<
        string,
        (FilterOperator | FilterSuffix)[] | true
      >,
    )) {
      if (Array.isArray(operators)) {
        decoratorsWithUndefined.push(
          ApiQuery({
            name: `filter.${columnName}`,
            required: false,
            type: 'string',
            description: `Filter by ${columnName}. Supported operations are: ${operators.map(asCode).join(', ')}`,
            examples: buildExamples(operators),
          }),
        );
      }
    }
  }

  const filteredDecorators = decoratorsWithUndefined.filter(
    (x): x is DecoratorType => x !== undefined,
  );

  return applyDecorators(...filteredDecorators);
}
