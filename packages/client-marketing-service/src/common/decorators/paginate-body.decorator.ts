import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export class PaginateBodyDto {
  page?: number;
  limit?: number;
  sortBy?: [string, 'ASC' | 'DESC'][];
  searchBy?: string[];
  search?: string;
  path: string;
  [key: string]: unknown;
}

const isString = param => typeof param === 'string';

const getSortBy = (body: PaginateBodyDto) => {
  const sortBy: [string, 'ASC' | 'DESC'][] = [];
  if (body.sortBy) {
    const params = !Array.isArray(body.sortBy) ? [body.sortBy] : body.sortBy;
    for (const param of params) {
      if (isString(param)) {
        const items = param.split(':');
        if (items.length === 2) {
          sortBy.push([items[0], items[1] as 'ASC' | 'DESC']);
        }
      }
    }
  }
  return sortBy;
};

const getSearchBy = (body: PaginateBodyDto) => {
  const searchBy: string[] = [];
  if (body.searchBy) {
    const params = !Array.isArray(body.searchBy)
      ? [body.searchBy]
      : body.searchBy;
    for (const param of params) {
      if (isString(param)) {
        searchBy.push(param);
      }
    }
  }
  return searchBy;
};

const getFilter = (body: PaginateBodyDto) => {
  const filterable: { [k: string]: any } = Object.entries(body)
    .filter(
      ([k, v]) =>
        k.includes('filter.') &&
        (isString(v) || (Array.isArray(v) && v.every(p => isString(p)))),
    )
    .reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {});

  return Object.entries(filterable)
    .map(([k, v]) => [k.replace('filter.', ''), v])
    .reduce((acc, [k, v]) => ({ ...acc, [k]: v }), {});
};

const getPostPaginationParams = (body: PaginateBodyDto) => {
  if (!Object.keys(body).length) return {};
  const sortBy = getSortBy(body);
  const searchBy = getSearchBy(body);
  const filter = getFilter(body);
  return {
    page: body.page ? parseInt(body.page.toString(), 10) : undefined,
    limit: body.limit ? parseInt(body.limit.toString(), 10) : undefined,
    sortBy: sortBy.length ? sortBy : undefined,
    search: body.search ? body.search.toString() : undefined,
    searchBy: searchBy.length ? searchBy : undefined,
    filter: Object.keys(filter).length ? filter : undefined,
  };
};

export const PaginateBody = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return getPostPaginationParams(request.body);
  },
);
