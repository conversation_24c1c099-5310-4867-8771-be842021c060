import { Module } from '@nestjs/common';
import { CompanyModule } from 'src/company/company.module';

import { GraphQLContextFactory } from './graphql-context.factory';
import { DataLoadersModule } from '../dataloaders/dataloaders.module';

@Module({
  imports: [CompanyModule, DataLoadersModule],
  providers: [GraphQLContextFactory],
  exports: [GraphQLContextFactory],
})
export class GraphQLContextModule {}
