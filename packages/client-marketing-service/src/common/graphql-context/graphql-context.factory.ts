import { Injectable } from '@nestjs/common';
import DataLoader from 'dataloader';
import { Request } from 'express';

import { Campaign } from '../../campaign/entities/campaign.entity';
// import { PropertyDetails } from '../../property/property.service';
import { CampaignDataLoader } from '../dataloaders/campaign.dataloader';
// import { PropertyDataLoader } from '../dataloaders/property.dataloader';

export interface GraphqlContext {
  req: Request;
  dataloaders: {
    campaign: DataLoader<string, Campaign | null>;
    // property: DataLoader<string, PropertyDetails>;
  };
}

@Injectable()
export class GraphQLContextFactory {
  constructor(
    private readonly campaignDataLoader: CampaignDataLoader,
    // private readonly propertyDataLoader: PropertyDataLoader,
  ) {}

  create(req: Request): GraphqlContext {
    return {
      req,
      dataloaders: {
        campaign: this.campaignDataLoader.createLoader(),
        // property: this.propertyDataLoader.createLoader(),
      },
    };
  }
}
