import { registerEnumType } from '@nestjs/graphql';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export enum RecommendationType {
  META_TITLE = 'META_TITLE',
  META_DESCRIPTION = 'META_DESCRIPTION',
  MAIN_HEADING = 'MAIN_HEADING',
}

registerEnumType(RecommendationType, { name: 'RecommendationType' });

export const scrapedElementToRecommendationType: Record<
  string,
  RecommendationType
> = {
  metaTitle: RecommendationType.META_TITLE,
  metaDescription: RecommendationType.META_DESCRIPTION,
  h1: RecommendationType.MAIN_HEADING,
};

export type ScrapedElementKey = keyof typeof scrapedElementToRecommendationType;

export type ScrapedValues = {
  [K in ScrapedElementKey]?: any;
};

export class ScrapedElement {
  @IsString()
  tag: string;

  @IsString()
  text: string;

  @IsString()
  @IsOptional()
  size?: string;

  @IsString()
  @IsOptional()
  parents?: string;

  @IsNumber()
  @IsOptional()
  aboveFold?: number;

  @IsString()
  @IsOptional()
  href?: string;

  @IsString()
  @IsOptional()
  xpath?: string;

  @IsString()
  @IsOptional()
  section?: string;
}
