export type PageResourceType =
  | 'properties'
  | 'neighborhoods'
  | 'posts'
  | 'postCategories'
  | 'agents'
  | 'developments'
  | 'offices'
  | 'teams'
  | null;

export interface PageInfoResource {
  id: string | null;
  sourceResource: PageResourceType;
}

export interface PageOutput {
  companyId: string;
  websiteId: string;
  pageId: string;
  name: string;
  path: string;
  displayOrder: number;
  pageType: string;
  seo: {
    seoTitle: string | null;
    seoDescription: string | null;
    seoImageUrl: string | null;
    seoCanonicalUrl: string | null;
  };
  resource: PageInfoResource | null;
}
