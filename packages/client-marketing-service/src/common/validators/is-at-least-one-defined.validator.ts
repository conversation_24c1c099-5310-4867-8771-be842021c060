import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

/**
 * how it works:
 * 1. Takes an array of property names to check
 * 2. Validates that at least one of those properties has a value that is:
 *    - Not `undefined`
 *    - Not `null`
 *    - Not an empty string `''`
 * 3. Returns a validation error if none of the specified properties have valid values
 */

@ValidatorConstraint({ name: 'isAtLeastOneDefined', async: false })
export class IsAtLeastOneDefinedConstraint
  implements ValidatorConstraintInterface
{
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyNames] = args.constraints;
    const obj = args.object as Record<string, any>;

    if (!Array.isArray(relatedPropertyNames)) {
      return false;
    }

    return relatedPropertyNames.some(propertyName => {
      const propertyValue = obj[propertyName];
      return (
        propertyValue !== undefined &&
        propertyValue !== null &&
        propertyValue !== ''
      );
    });
  }

  defaultMessage(args: ValidationArguments) {
    const [relatedPropertyNames] = args.constraints;
    return `At least one of the following fields must be defined and non-empty: ${relatedPropertyNames.join(', ')}`;
  }
}

export function IsAtLeastOneDefined(
  propertyNames: string[],
  validationOptions?: ValidationOptions,
) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [propertyNames],
      validator: IsAtLeastOneDefinedConstraint,
    });
  };
}
