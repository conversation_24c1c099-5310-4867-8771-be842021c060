import { validate } from 'class-validator';

import { IsAtLeastOneDefined } from '../is-at-least-one-defined.validator';

class TestClass {
  @IsAtLeastOneDefined(['field1', 'field2', 'field3'])
  value: any;

  field1?: any;
  field2?: any;
  field3?: any;
}

describe('IsAtLeastOneDefined', () => {
  let testInstance: TestClass;

  beforeEach(() => {
    testInstance = new TestClass();
  });

  it('should pass validation when at least one field is defined and non-empty', async () => {
    testInstance.field1 = 'test value';
    testInstance.field2 = undefined;
    testInstance.field3 = '';

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(0);
  });

  it('should pass validation when multiple fields are defined and non-empty', async () => {
    testInstance.field1 = 'test value 1';
    testInstance.field2 = 'test value 2';
    testInstance.field3 = 'test value 3';

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(0);
  });

  it('should fail validation when all fields are undefined', async () => {
    testInstance.field1 = undefined;
    testInstance.field2 = undefined;
    testInstance.field3 = undefined;

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(1);
    expect(errors[0].constraints?.isAtLeastOneDefined).toBe(
      'At least one of the following fields must be defined and non-empty: field1, field2, field3',
    );
  });

  it('should fail validation when all fields are null', async () => {
    testInstance.field1 = null;
    testInstance.field2 = null;
    testInstance.field3 = null;

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(1);
    expect(errors[0].constraints?.isAtLeastOneDefined).toBe(
      'At least one of the following fields must be defined and non-empty: field1, field2, field3',
    );
  });

  it('should fail validation when all fields are empty strings', async () => {
    testInstance.field1 = '';
    testInstance.field2 = '';
    testInstance.field3 = '';

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(1);
    expect(errors[0].constraints?.isAtLeastOneDefined).toBe(
      'At least one of the following fields must be defined and non-empty: field1, field2, field3',
    );
  });

  it('should pass validation when one field is defined and others are empty/null/undefined', async () => {
    testInstance.field1 = 'valid value';
    testInstance.field2 = '';
    testInstance.field3 = null;

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(0);
  });

  it('should pass validation when one field has a non-empty string and others are undefined', async () => {
    testInstance.field1 = undefined;
    testInstance.field2 = 'valid value';
    testInstance.field3 = undefined;

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(0);
  });

  it('should handle mixed types correctly', async () => {
    testInstance.field1 = 0; // falsy but defined
    testInstance.field2 = false; // falsy but defined
    testInstance.field3 = undefined;

    const errors = await validate(testInstance);
    expect(errors).toHaveLength(0);
  });
});
