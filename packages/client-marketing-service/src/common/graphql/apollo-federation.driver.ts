import { ApolloFederationDriver as DefaultApolloFederationDriver } from '@nestjs/apollo';
import { Injectable } from '@nestjs/common';

/**
 * Custom Apollo Federation Driver that disables automatic server shutdown.
 *
 * This driver extends the base ApolloFederationDriver but overrides the `stop()` method
 * to prevent the Apollo Server from automatically shutting down during application
 * lifecycle events. This allows the application to control GraphQL server shutdown
 * independently and coordinate it with other services and graceful shutdown processes.
 *
 * We need to do this as the ApolloFederationDriver will stop the graphql service
 * IMMEDIATELY when receiving a SIGTERM from kubernetes.
 * Which we do not want as the kubernetes ingress controller will still route requests
 * to the pod because of eventual consistency, but consumers of the service will get
 * ECONNREFUSED errors as the graphql module/service has already shut down.
 *
 * This should allow it to drain any connections to this server between kubernetes
 * sending a SIGTERM, and killing the pod.
 *
 * There is an open issue about this at https://github.com/nestjs/graphql/issues/3597
 * but it has been open for months with no movement on it.
 */
@Injectable()
export class ApolloFederationDriver extends DefaultApolloFederationDriver {
  async stop() {}
}
