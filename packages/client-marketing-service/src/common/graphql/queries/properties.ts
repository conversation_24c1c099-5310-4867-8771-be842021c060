import { AGENT_FIELDS } from '../fragments/agent';
import { MEDIA_FIELDS } from '../fragments/media';
import { PROPERTY_FIELDS, CMS_PROPERTY_FIELDS } from '../fragments/property';

export const GET_PROPERTIES_BY_COMPANY = `
  ${PROPERTY_FIELDS}
  
  query GetPropertiesByCompany($companyId: ID!) {
    properties(companyId: $companyId) {
      ...PropertyFields
    }
  }
`;

export const GET_PROPERTY_BY_ID = `
  ${PROPERTY_FIELDS}
  
  query GetPropertyById($id: ID!) {
    property(id: $id) {
      ...PropertyFields
    }
  }
`;

export const GET_PROPERTIES_FOR_CMS = `
  ${CMS_PROPERTY_FIELDS}
  
  query GetPropertiesForCms(
    $companyId: ID
    $agentIds: [ID]
    $neighborhoodIds: [ID]
    $search: String
    $bedroomCountGTE: Int
    $bathCountGTE: Int
    $salesPriceGTE: Int
    $salesPriceLTE: Int
    $openHouse: Boolean
    $archived: Boolean
    $forCMS: Boolean
    $limit: Int
    $offset: Int
  ) {
    properties(
      companyId: $companyId
      agentIds: $agentIds
      neighborhoodIds: $neighborhoodIds
      search: $search
      bedroomCountGTE: $bedroomCountGTE
      bathCountGTE: $bathCountGTE
      salesPriceGTE: $salesPriceGTE
      salesPriceLTE: $salesPriceLTE
      openHouse: $openHouse
      archived: $archived
      forCMS: $forCMS
      limit: $limit
      offset: $offset
    ) {
      count
      items {
        ...CmsPropertyFields
      }
    }
  }
`;

export const GET_PROPERTY_FOR_CMS = `
  ${CMS_PROPERTY_FIELDS}
  
  query GetPropertyForCms($id: ID!) {
    property(id: $id) {
      ...CmsPropertyFields
    }
  }
`;

export const GET_PROPERTY_WITH_MEDIA_AND_AGENTS = `
  ${MEDIA_FIELDS}
  ${AGENT_FIELDS}

  query GetPropertyWithMediaAndAgents($id: ID!, $forCMS: Boolean) {
    property(id: $id, forCMS: $forCMS) {
      id
      media {
        ...MediaFields
      }
      agents {
        ...AgentFields
      }
    }
  }
`;

export const GET_PROPERTY_FOR_CAMPAIGN_BUNDLE = `
  ${CMS_PROPERTY_FIELDS}

  query GetPropertyForCampaignBundle($id: ID!, $forCMS: Boolean = true) {
    property(id: $id, forCMS: $forCMS) {
      ...CmsPropertyFields
    }
  }
`;
