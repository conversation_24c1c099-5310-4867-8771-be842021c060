import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GraphQLClient } from 'graphql-request';

import { SCHEMA_INTROSPECTION } from './queries/schema';

@Injectable()
export class GraphQLClientService {
  private readonly logger = new Logger(GraphQLClientService.name);
  private readonly authHeaders: Record<string, string>;
  private readonly graphqlClient: GraphQLClient;

  constructor(private configService: ConfigService) {
    const cosmoUrl =
      process.env.COSMO_GQL_URL ||
      this.configService.get('apiGateway.url') ||
      'http://localhost:3001';
    const graphqlUrl = cosmoUrl.endsWith('/graphql')
      ? cosmoUrl
      : `${cosmoUrl}/graphql`;

    const rawApiKey =
      process.env.M2M_SUPER_API_KEY || this.configService.get('apiGateway.key');
    const apiKey = rawApiKey ? rawApiKey.split(',')[0].trim() : undefined;
    this.authHeaders = apiKey ? { 'x-lp-api-key': apiKey } : {};

    this.graphqlClient = new GraphQLClient(graphqlUrl);
  }

  public getClient(): GraphQLClient {
    return this.graphqlClient;
  }

  public async request<T>(
    query: string,
    variables?: Record<string, any>,
  ): Promise<T> {
    try {
      return await this.graphqlClient.request<T>(
        query,
        variables,
        this.authHeaders,
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`GraphQL request failed: ${errorMessage}`, {
        query: query.substring(0, 100),
        variables,
      });
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      const response = await this.graphqlClient.request<{
        __schema: { types: { name: string }[] };
      }>(SCHEMA_INTROSPECTION, {}, this.authHeaders);

      return !!response.__schema;
    } catch (error) {
      this.logger.error(
        `GraphQL health check failed: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined,
      );
      return false;
    }
  }
}
