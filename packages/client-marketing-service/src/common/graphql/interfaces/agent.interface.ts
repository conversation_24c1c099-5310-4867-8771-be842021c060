export interface Agent {
  agentId: string;
  companyId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  avatar?: string;
  position?: string;
  leadAgent: boolean;
  bioLong?: string;
  bioShort?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressCity?: string;
  addressState?: string;
  urlWebsite?: string;
  slug: string;
  seoTitle?: string;
  seoDescription?: string;
  license?: string;
  networkAgent: boolean;
  id: string;
}
