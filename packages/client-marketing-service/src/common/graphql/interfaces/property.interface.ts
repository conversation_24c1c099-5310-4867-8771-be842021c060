export interface Property {
  propertyId: string;
  name: string;
  companyId: string;
  description?: string;
  addressFull?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressCity?: string;
  addressState?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  salesPrice?: number;
  leasePrice?: number;
  bedroomCount?: number;
  bathCount?: number;
  livingSpaceSize?: number;
  livingSpaceUnits?: string;
  lotAreaSize?: number;
  lotAreaUnits?: string;
  yearBuilt?: number;
  slug?: string;
  id: string;
}
