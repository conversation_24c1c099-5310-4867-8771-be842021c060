import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { RetryConfig } from './interfaces/retry-config.interface';

@Injectable()
export class RetryService {
  private readonly logger = new Logger(RetryService.name);
  private readonly DEFAULT_MAX_ATTEMPTS = 3;

  constructor(private readonly configService: ConfigService) {}

  async execute<T>(fn: () => Promise<T>, config?: RetryConfig): Promise<T> {
    const rawAttempts =
      config?.maxAttempts ??
      this.configService.get<number>('retry.maxAttempts') ??
      this.DEFAULT_MAX_ATTEMPTS;
    const maxAttempts = Math.max(1, Math.floor(Number(rawAttempts)));
    const context = config?.context;
    let lastError: Error = new Error('No attempts made');

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        if (attempt < maxAttempts - 1) {
          const message = context
            ? `[${context}] Retry attempt ${attempt + 1}/${maxAttempts}`
            : `Retry attempt ${attempt + 1}/${maxAttempts}`;
          this.logger.warn(message);
        }
      }
    }

    const errorMessage = context
      ? `[${context}] All ${maxAttempts} retry attempts failed`
      : `All ${maxAttempts} retry attempts failed`;
    this.logger.error(errorMessage, lastError.stack);

    throw lastError;
  }
}
