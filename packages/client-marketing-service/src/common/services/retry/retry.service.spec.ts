import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import { RetryService } from './retry.service';

describe('RetryService', () => {
  let service: RetryService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RetryService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              const config: Record<string, any> = {
                'retry.maxAttempts': 3,
                'retry.baseDelayMs': 1000,
                'retry.maxDelayMs': 30000,
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    service = module.get<RetryService>(RetryService);

    // Mock logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation();
    jest.spyOn(Logger.prototype, 'warn').mockImplementation();
    jest.spyOn(Logger.prototype, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    it('should execute function successfully on first attempt', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');

      const result = await service.execute(mockFn);

      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and succeed on retry', async () => {
      const mockFn = jest
        .fn()
        .mockRejectedValueOnce(new Error('First attempt failed'))
        .mockResolvedValueOnce('success');

      const result = await service.execute(mockFn);

      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should accept custom retry configuration', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Always fails'));

      await expect(service.execute(mockFn, { maxAttempts: 2 })).rejects.toThrow(
        'Always fails',
      );
      expect(mockFn).toHaveBeenCalledTimes(2); // Custom maxAttempts
    });

    it('should throw error when maximum attempts reached', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Always fails'));

      await expect(service.execute(mockFn, { maxAttempts: 3 })).rejects.toThrow(
        'Always fails',
      );
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should log with context when provided', async () => {
      const mockFn = jest
        .fn()
        .mockRejectedValueOnce(new Error('First'))
        .mockResolvedValueOnce('success');

      const loggerWarnSpy = jest.spyOn(Logger.prototype, 'warn');

      await service.execute(mockFn, { context: 'TestOperation' });

      expect(loggerWarnSpy).toHaveBeenCalledWith(
        expect.stringContaining('[TestOperation] Retry attempt 1/3'),
      );
    });
  });
});
