import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';

import { ApiGatewayService } from './api-gateway.service';
import { HANDED_OFF_LEADS_QUERY } from './queries';

describe('ApiGatewayService', () => {
  const companyId = 'test-company-id';

  let service: ApiGatewayService;

  const mockHttpService: HttpService = {
    request: jest
      .fn()
      .mockReturnValue(of({ data: { success: true, data: [] } })),
  } as unknown as HttpService;

  const mockConfigService: ConfigService = {
    get: jest.fn().mockImplementation(key => {
      switch (key) {
        case 'apiGateway.url':
          return 'https://gw.luxurycoders.com';
        case 'apiGateway.key':
          return 'localkey';
        default:
          return '';
      }
    }),
  } as unknown as ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApiGatewayService,
        { provide: HttpService, useValue: mockHttpService },
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<ApiGatewayService>(ApiGatewayService);
  });

  it('should throw if request fails', () => {
    (mockHttpService.request as jest.Mock).mockReturnValueOnce(
      of({ data: { success: false, message: 'Error fetching leads' } }),
    );

    expect(service.findHandedOffLeads(companyId)).rejects.toThrow(
      'Failed to fetch handed off leads: Error fetching leads',
    );
  });

  it('should fetch data from CRM service', async () => {
    const handedOffStart = '2025-05-01T00:00:00.000Z';

    await service.findHandedOffLeads(companyId);

    expect(mockHttpService.request).toHaveBeenCalledWith(
      expect.objectContaining({
        method: 'POST',
        url: 'https://gw.luxurycoders.com/graphql',
        headers: {
          Authorization: expect.any(String),
          'Content-Type': 'application/json',
        },
        data: {
          query: HANDED_OFF_LEADS_QUERY,
          variables: { companyId, handedOffStart },
        },
      }),
    );
  });
});
