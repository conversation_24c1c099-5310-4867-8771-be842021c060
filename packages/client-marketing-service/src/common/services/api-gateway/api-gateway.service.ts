import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as JWT from 'jsonwebtoken';
import { lastValueFrom } from 'rxjs';

import { LeadDto } from './dto/lead.dto';
import { MediaDto } from './dto/media.dto';
import { HANDED_OFF_LEADS_QUERY, MEDIA_QUERY } from './queries';

const START_DATE = '2025-05-01T00:00:00.000Z';

@Injectable()
export class ApiGatewayService {
  private readonly logger = new Logger(ApiGatewayService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  private getM2MToken(companyId: string): string {
    const key = this.configService.get<string>('apiGateway.key');
    const token = JWT.sign({ id: companyId }, key!);

    return token;
  }

  private async executeGraphQLQuery<T = any>(
    query: string,
    variables: Record<string, any>,
    authToken: string,
    errorPrefix?: string,
  ): Promise<T> {
    const { data } = await lastValueFrom(
      this.httpService.request({
        method: 'POST',
        url: new URL(
          '/graphql',
          this.configService.get('apiGateway.url'),
        ).toString(),
        headers: {
          Authorization: authToken,
          'Content-Type': 'application/json',
        },
        data: {
          query,
          variables,
        },
      }),
    );

    if (data.success === false) {
      const errorMessage = errorPrefix
        ? `${errorPrefix}: ${data.message}`
        : `GraphQL query failed: ${data.message}`;
      throw new Error(errorMessage);
    }

    return data.data as T;
  }

  async findHandedOffLeads(companyId: string): Promise<LeadDto[]> {
    try {
      const handedOffStart = START_DATE;
      const authToken = this.getM2MToken(companyId);
      const variables = { companyId, handedOffStart };

      const data = await this.executeGraphQLQuery<{ leads: LeadDto[] }>(
        HANDED_OFF_LEADS_QUERY,
        variables,
        authToken,
        'Failed to fetch handed off leads',
      );

      return data?.leads || [];
    } catch (error) {
      this.logger.error('Error fetching handed off leads', error);
      throw error;
    }
  }

  async findMedia(mediaIds: string[]): Promise<MediaDto[]> {
    try {
      if (!mediaIds.length) {
        return [];
      }

      const authToken = this.getM2MToken('system');
      const variables = { id: mediaIds };

      const data = await this.executeGraphQLQuery<{ media: MediaDto[] }>(
        MEDIA_QUERY,
        variables,
        authToken,
        'Failed to fetch media',
      );

      return data?.media || [];
    } catch (error) {
      this.logger.error('Error fetching media', error);
      return [];
    }
  }
}
