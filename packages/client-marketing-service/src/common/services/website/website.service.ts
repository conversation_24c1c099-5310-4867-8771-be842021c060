import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { Website } from 'src/common/types/website';

import { WebsiteUpdateDto } from './dto/website-update.dto';
import { PageOutput } from '../../types/star-website';

@Injectable()
export class WebsiteService {
  private readonly baseUrl: string;
  private readonly logger = new Logger(WebsiteService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = `${this.configService.get<string>('websiteService.url')}/api/v1`;
  }

  async updateWebsite(
    data: WebsiteUpdateDto,
    recommendationId?: string,
    jobIds?: {
      groupJobId: string | undefined;
      recommendationJobId: string | undefined;
    },
  ): Promise<unknown> {
    const { groupJobId, recommendationJobId } = jobIds ?? {};
    try {
      this.logger.debug('Updating website and calling STAR API', {
        postBody: data,
        recommendationId,
        groupJobId: groupJobId ?? 'N/A',
        recommendationJobId: recommendationJobId ?? 'N/A',
      });
      const response = await firstValueFrom(
        this.httpService.patch(`${this.baseUrl}/star`, data, {
          headers: {
            'Content-Type': 'application/json',
          },
        }),
      );
      this.logger.log('Response from STAR API', {
        responseBody: response.data,
        groupJobId: groupJobId ?? 'N/A',
        recommendationJobId: recommendationJobId ?? 'N/A',
        recommendationId,
        starRequestId: response.data.requestId,
        starLLMJobId: response.data.jobId,
      });
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error calling ${this.baseUrl}/star: ${error.message}`,
        {
          code: error.code,
          requestUrl: error.config?.url,
          requestMethod: error.config?.method,
          requestData: data,
          responseData: error.response?.data,
          groupJobId: groupJobId ?? 'N/A',
          recommendationJobId: recommendationJobId ?? 'N/A',
          recommendationId,
          starRequestId: error.response?.data?.requestId,
        },
        WebsiteService.name,
      );
      if (error.response) {
        throw new HttpException(
          {
            error: 'Website Service Error',
            message: `Website service returned ${error.response.status}: ${error.message}`,
            code: error.code,
            requestUrl: error.config?.url
              ? new URL(error.config.url).pathname
              : undefined,
            requestMethod: error.config?.method,
            requestSummary: data?.url
              ? `Update for ${data.url}`
              : 'Website update',
            groupJobId: groupJobId ?? 'N/A',
            recommendationJobId: recommendationJobId ?? 'N/A',
            recommendationId,
            starRequestId: error.response?.data?.requestId,
            statusCode: error.response.status,
          },
          error.response.status,
        );
      } else {
        throw new HttpException(
          {
            error: 'Service Unavailable', // Category
            message: `Failed to communicate with website service: ${error.message}`,
            code: error.code,
            requestMethod: error.config?.method,
            requestUrl: error.config?.url
              ? new URL(error.config.url).pathname
              : undefined,
            groupJobId: groupJobId ?? 'N/A',
            recommendationJobId: recommendationJobId ?? 'N/A',
            recommendationId,
          },
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      }
    }
  }

  async getPage(url: string): Promise<PageOutput> {
    this.logger.log('Getting page info from STAR API', { url });
    try {
      if (!url || typeof url !== 'string') {
        throw new HttpException('Invalid URL provided', HttpStatus.BAD_REQUEST);
      }
      const response = await firstValueFrom(
        this.httpService.post(`${this.baseUrl}/star/website/page-info`, {
          url,
        }),
      );
      const pageData = response.data?.data || response.data;
      if (!pageData) {
        throw new Error('No page data returned from API');
      }
      return pageData;
    } catch (error) {
      this.logger.error('Error getting page info from STAR API', {
        error: error.message,
        url,
      });
      if (error.response) {
        throw new HttpException(
          `Failed to get page info: ${error.message}`,
          error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
      throw new HttpException(
        'Service unavailable for page info retrieval',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async getLiveBrandWebsites(companyId: string): Promise<Website[]> {
    try {
      const websites = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/websites?companyId=${companyId}&status=LIVE&category=BRAND`,
        ),
      );

      return websites.data.data;
    } catch (error) {
      this.logger.error('Error fetching live brand websites', {
        error: error.message,
        companyId,
      });

      if (error.response) {
        throw new HttpException(
          `Failed to get live brand websites: ${error.message}`,
          error.response.status || HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      throw new HttpException(
        'Service unavailable for live brand websites retrieval',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
