import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AxiosError, AxiosResponse } from 'axios';
import { of, throwError } from 'rxjs';
import { Website } from 'src/common/types/website';

import { WebsiteUpdateDto } from './dto/website-update.dto';
import { WebsiteService } from './website.service';

describe('WebsiteService', () => {
  let service: WebsiteService;

  const mockBaseUrl = 'http://website-service:3000';
  const mockWebsiteUpdateDto: WebsiteUpdateDto = {
    url: 'https://example.presencepreview.site/',
    prompt: 'update the seo description',
  };

  const mockHttpService = {
    patch: jest.fn(),
    get: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue(mockBaseUrl),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebsiteService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<WebsiteService>(WebsiteService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('updateWebsite', () => {
    it('should successfully update a website', async () => {
      const mockResponseData = {
        success: true,
        message: 'Website updated successfully',
      };
      mockHttpService.patch.mockReturnValue(of({ data: mockResponseData }));

      const result = await service.updateWebsite(mockWebsiteUpdateDto);

      expect(mockHttpService.patch).toHaveBeenCalledWith(
        `${mockBaseUrl}/api/v1/star`,
        mockWebsiteUpdateDto,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      expect(result).toEqual(mockResponseData);
    });

    it('should throw an HttpException with response data when API returns an error response', async () => {
      const mockErrorResponse = {
        status: 400,
        statusText: 'Bad Request',
        data: { error: 'Invalid prompt', details: 'The prompt is empty' },
      } as AxiosResponse;

      const axiosError = {
        message: 'Request failed with status code 400',
        response: mockErrorResponse,
        config: {
          url: `${mockBaseUrl}/api/v1/star`,
          method: 'patch',
        },
        isAxiosError: true,
      } as AxiosError;

      mockHttpService.patch.mockReturnValue(throwError(() => axiosError));

      const action = service.updateWebsite(mockWebsiteUpdateDto);
      await expect(action).rejects.toThrow(HttpException);
      await expect(action).rejects.toThrow(
        expect.objectContaining({
          status: 400,
          response: expect.objectContaining({
            error: 'Website Service Error',
            statusCode: 400,
          }),
        }),
      );
    });

    it('should throw a service unavailable HttpException when connection to API fails', async () => {
      const networkError = {
        message: 'connect ECONNREFUSED',
        code: 'ECONNREFUSED',
        config: {
          url: `${mockBaseUrl}/api/v1/star`,
          method: 'patch',
        },
        isAxiosError: true,
      } as unknown as AxiosError;

      mockHttpService.patch.mockReturnValue(throwError(() => networkError));

      const action = service.updateWebsite(mockWebsiteUpdateDto);
      await expect(action).rejects.toThrow(HttpException);
      await expect(action).rejects.toThrow(
        expect.objectContaining({
          status: HttpStatus.SERVICE_UNAVAILABLE,
          response: expect.objectContaining({
            error: 'Service Unavailable',
            code: 'ECONNREFUSED',
          }),
        }),
      );
    });

    it('should handle timeout errors appropriately', async () => {
      const timeoutError = {
        message: 'timeout of 5000ms exceeded',
        code: 'ETIMEDOUT',
        config: {
          url: `${mockBaseUrl}/api/v1/star`,
          method: 'patch',
        },
        isAxiosError: true,
      } as unknown as AxiosError;

      mockHttpService.patch.mockReturnValue(throwError(() => timeoutError));

      const action = service.updateWebsite(mockWebsiteUpdateDto);
      await expect(action).rejects.toThrow(HttpException);
      await expect(action).rejects.toThrow(
        expect.objectContaining({
          status: HttpStatus.SERVICE_UNAVAILABLE,
          response: expect.objectContaining({
            error: 'Service Unavailable',
            code: 'ETIMEDOUT',
          }),
        }),
      );
    });

    it('should handle errors with missing config or response data gracefully', async () => {
      const simpleError = {
        message: 'An unknown error occurred',
        isAxiosError: true,
      } as unknown as AxiosError;

      mockHttpService.patch.mockReturnValue(throwError(() => simpleError));

      const action = service.updateWebsite(mockWebsiteUpdateDto);
      await expect(action).rejects.toThrow(HttpException);
      await expect(action).rejects.toThrow(
        expect.objectContaining({
          status: HttpStatus.SERVICE_UNAVAILABLE,
          response: expect.objectContaining({
            error: 'Service Unavailable',
            message: expect.stringContaining('An unknown error occurred'),
          }),
        }),
      );
    });

    it('should handle non-Axios errors appropriately', async () => {
      const genericError = new Error('Some unexpected error');
      mockHttpService.patch.mockReturnValue(throwError(() => genericError));

      const action = service.updateWebsite(mockWebsiteUpdateDto);
      await expect(action).rejects.toThrow(HttpException);
      await expect(action).rejects.toThrow(
        expect.objectContaining({
          status: HttpStatus.SERVICE_UNAVAILABLE,
          response: expect.objectContaining({
            error: 'Service Unavailable',
            message: expect.stringContaining('Some unexpected error'),
          }),
        }),
      );
    });

    it('should log error data when network error occurs', async () => {
      const networkError = {
        message: 'Network Error',
        code: 'ENETUNREACH',
        config: {
          url: `${mockBaseUrl}/api/v1/star`,
          method: 'patch',
        },
        isAxiosError: true,
      } as unknown as AxiosError;

      // Create a spy for the logger's error method
      const errorLoggerSpy = jest.spyOn(console, 'error').mockImplementation();
      mockHttpService.patch.mockReturnValue(throwError(() => networkError));

      try {
        await service.updateWebsite(mockWebsiteUpdateDto);
        fail('Expected an error to be thrown');
      } catch (error) {
        // Verify the HttpException was thrown with appropriate status
        expect(error).toBeInstanceOf(HttpException);
        expect(error.getStatus()).toBe(HttpStatus.SERVICE_UNAVAILABLE);

        // Verify the response contains the expected error information
        const responseObj = error.getResponse();
        expect(responseObj).toHaveProperty('error', 'Service Unavailable');
        expect(responseObj).toHaveProperty('code', 'ENETUNREACH');

        // We can't directly check the logger output in the test, but we can verify
        // that the error response contains sufficient information
        expect(responseObj).toHaveProperty('message');
        expect(responseObj.message).toContain('Network Error');
      }

      errorLoggerSpy.mockRestore();
    });
  });

  describe('getLiveBrandWebsites', () => {
    const companyId = 'test-company-id';

    describe('if the request fails', () => {
      it('should throw an HttpException', async () => {
        mockHttpService.get.mockReturnValueOnce(throwError(() => new Error()));

        await expect(service.getLiveBrandWebsites(companyId)).rejects.toThrow(
          HttpException,
        );
      });
    });

    describe('if the request succeeds', () => {
      it('should return the list of websites', async () => {
        const websites: Website[] = [
          {
            hostname: 'ryan-s-beach-homes.ryansbeach.homes',
          },
          {
            hostname: 'ryan-s-luxury-homes.ryansbeach.homes',
          },
        ];

        mockHttpService.get.mockReturnValue(of({ data: { data: websites } }));

        const result = await service.getLiveBrandWebsites(companyId);

        expect(mockHttpService.get).toHaveBeenCalledWith(
          'http://website-service:3000/api/v1/websites?companyId=test-company-id&status=LIVE&category=BRAND',
        );
        expect(result).toEqual(websites);
      });
    });
  });
});
