import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUrl } from 'class-validator';

export class WebsiteUpdateDto {
  @ApiProperty({
    description: 'The URL of the website to update',
    example: 'https://example.presencepreview.site/',
  })
  @IsUrl()
  url: string;

  @ApiProperty({
    description: 'The prompt describing the update to perform',
    example: 'update the seo description to be "new description"',
  })
  @IsString()
  prompt: string;
}
