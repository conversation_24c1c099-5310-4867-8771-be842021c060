import { HttpService } from '@nestjs/axios';
import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import { Entitlement } from './tentant.types';

export type Filters = {
  roleId?: string;
  companyId?: string;
  displayId?: string;
};

export type Options = {
  offset?: number;
  limit?: number;
};

export type CompanyDTO = {
  displayId: string;
  firstName: string;
  lastName: string;
  email: string;
};

export type Company = CompanyDTO & {
  companyId: string;
  website?: string;
};

@Injectable()
export class TenantService {
  private readonly baseUrl: string;
  private readonly logger = new Logger(TenantService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = `${this.configService.get<string>('tenantService.url')}/api/v1`;
  }

  private async get(path: string, queryParams: Record<string, any> = {}) {
    this.logger.debug('Getting data from tenant service', {
      path,
      queryParams,
    });
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/${path}`, {
          params: queryParams,
        }),
      );
      return response.data;
    } catch (error) {
      throw new HttpException(
        {
          error: 'Tenant Service Error',
          message: `Failed to communicate with tenant service: ${error.message}`,
          code: error.code,
          requestMethod: error.config?.method,
          requestUrl: error.config?.url ? new URL(error.config.url) : undefined,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  async find(filters: Filters = {}, options: Options = {}): Promise<Company[]> {
    this.logger.debug('Finding companies', { filters, options });
    const { offset, limit } = options;

    const queryParams = this.prepareFilters(filters);

    const page = this.convertSeqPageParamsToIdentityPagination(offset, limit);
    queryParams.page = page;
    queryParams.limit = limit;

    // Get companies first
    const companiesRes = await this.get('companies', queryParams);
    const { data: companies } = companiesRes;

    // Get users for each company
    const userPromises = companies.map(async (company: CompanyDTO) => {
      const userRes = await this.get('users', {
        'filter.email': `$eq:${company.email}`,
      });
      const { data: users } = userRes;
      const user = users[0]; // Assuming one user per email

      return {
        ...company,
        companyId: company?.displayId,
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
        email: company.email,
      };
    });

    return Promise.all(userPromises);
  }

  private convertSeqPageParamsToIdentityPagination(
    offset: number | undefined,
    limit: number | undefined,
  ) {
    const computedOffset = offset ?? 0;
    const computedLimit = limit ?? 10;
    const page = Math.ceil(computedOffset / computedLimit) + 1;
    return page;
  }

  private prepareFilters(reqFilters: Filters) {
    const filters = { ...reqFilters };
    const queryParams: any = {};

    if (filters.roleId) {
      queryParams['filter.roleId'] = `$eq:${filters.roleId}`;
      delete filters.roleId;
    }

    Object.keys(filters).forEach(key => {
      if (Array.isArray(filters[key])) {
        queryParams[`filter.${key}`] = `$in:${filters[key].join(',')}`;
        return;
      }
      queryParams[`filter.${key}`] = `$eq:${filters[key]}`;
    });

    return queryParams;
  }

  async getEntitlements(companyId: string): Promise<Entitlement[]> {
    try {
      const response = await this.get('entitlements', {
        'filter.companyId': `$eq:${companyId}`,
      });

      return response.data;
    } catch (error) {
      const errorMessage = `Failed to fetch entitlements for company ${companyId}`;

      this.logger.error(errorMessage, error);

      throw Error(errorMessage);
    }
  }
}
