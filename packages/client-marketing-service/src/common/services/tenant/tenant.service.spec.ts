import { HttpService } from '@nestjs/axios';
import { HttpException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { of, throwError } from 'rxjs';

import { Filters, Options, TenantService } from './tenant.service';
import { Entitlement } from './tentant.types';

describe('TenantService', () => {
  let service: TenantService;
  let httpService: HttpService;

  const mockBaseUrl = 'http://tenant-service:3000';

  beforeEach(async () => {
    const mockHttpService = {
      get: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn().mockReturnValue(mockBaseUrl),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<TenantService>(TenantService);
    httpService = module.get<HttpService>(HttpService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('find', () => {
    const mockCompanies = [
      {
        displayId: 'company-1',
        email: '<EMAIL>',
      },
      {
        displayId: 'company-2',
        email: '<EMAIL>',
      },
    ];

    const mockUsers = [
      [
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
        },
      ],
      [
        {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
        },
      ],
    ];

    const expectedCompanies = [
      {
        displayId: 'company-1',
        companyId: 'company-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      },
      {
        displayId: 'company-2',
        companyId: 'company-2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
      },
    ];

    it('should fetch companies and their associated users', async () => {
      // Mock the first request to get companies
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: mockCompanies,
          },
        }),
      );

      // Mock subsequent requests to get users for each company
      mockCompanies.forEach((_, index) => {
        (httpService.get as jest.Mock).mockImplementationOnce(() =>
          of({
            data: {
              data: mockUsers[index],
            },
          }),
        );
      });

      const filters: Filters = { companyId: 'test-company' };
      const options: Options = { offset: 0, limit: 10 };

      const result = await service.find(filters, options);

      // Verify the first call to get companies
      expect(httpService.get).toHaveBeenNthCalledWith(
        1,
        `${mockBaseUrl}/api/v1/companies`,
        {
          params: {
            'filter.companyId': '$eq:test-company',
            page: 1,
            limit: 10,
          },
        },
      );

      // Verify calls to get users for each company
      mockCompanies.forEach((company, index) => {
        expect(httpService.get).toHaveBeenNthCalledWith(
          index + 2,
          `${mockBaseUrl}/api/v1/users`,
          {
            params: {
              'filter.email': `$eq:${company.email}`,
            },
          },
        );
      });

      // Verify the returned result
      expect(result).toEqual(expectedCompanies);
    });

    it('should handle pagination correctly', async () => {
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: [],
          },
        }),
      );

      const options: Options = { offset: 20, limit: 10 };
      await service.find({}, options);

      // Verify that page is calculated correctly (should be page 3)
      expect(httpService.get).toHaveBeenCalledWith(
        `${mockBaseUrl}/api/v1/companies`,
        {
          params: {
            page: 3,
            limit: 10,
          },
        },
      );
    });

    it('should handle role filter correctly', async () => {
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: [],
          },
        }),
      );

      const filters: Filters = { roleId: 'admin-role' };
      await service.find(filters);

      expect(httpService.get).toHaveBeenCalledWith(
        `${mockBaseUrl}/api/v1/companies`,
        {
          params: {
            'filter.roleId': '$eq:admin-role',
            page: 1,
            limit: undefined,
          },
        },
      );
    });

    it('should handle array filter values correctly', async () => {
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: [],
          },
        }),
      );

      const filters = {
        displayId: ['id1', 'id2', 'id3'],
      } as unknown as Filters;
      await service.find(filters);

      expect(httpService.get).toHaveBeenCalledWith(
        `${mockBaseUrl}/api/v1/companies`,
        {
          params: {
            'filter.displayId': '$in:id1,id2,id3',
            page: 1,
            limit: undefined,
          },
        },
      );
    });

    it('should throw HttpException when API call fails', async () => {
      const mockError = {
        message: 'Connection refused',
        code: 'ECONNREFUSED',
        config: {
          method: 'GET',
          url: `${mockBaseUrl}/api/v1/companies`,
        },
      };

      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        throwError(() => mockError),
      );

      await expect(service.find()).rejects.toThrow(HttpException);
    });

    it('should handle case when user not found', async () => {
      // Mock the first request to get companies
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: [{ displayId: 'company-1', email: '<EMAIL>' }],
          },
        }),
      );

      // Mock user lookup returning empty array
      (httpService.get as jest.Mock).mockImplementationOnce(() =>
        of({
          data: {
            data: [],
          },
        }),
      );

      const result = await service.find();

      // Should still return company with empty name fields
      expect(result).toEqual([
        {
          displayId: 'company-1',
          companyId: 'company-1',
          firstName: '',
          lastName: '',
          email: '<EMAIL>',
        },
      ]);
    });
  });

  describe('getEntitlements', () => {
    const companyId = 'test-company';

    describe('when fetching entitlements fails', () => {
      it('should throw an exception', async () => {
        await expect(service.getEntitlements(companyId)).rejects.toThrow(
          `Failed to fetch entitlements for company ${companyId}`,
        );
      });
    });

    describe('when fetching entitlements succeeds', () => {
      it('should return entitlements for the company', async () => {
        const mockEntitlements: Entitlement[] = [
          {
            displayId: '1e36a772-bce9-4bc7-9d8a-be5069d1ed60',
            companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
            productId: 'ddf4ce71-2e14-4a43-bbe9-2a976fa2aa44',
            startDate: '2025-07-02T22:21:07.255Z',
            endDate: null,
            units: 1,
            salesforceServiceId: '',
            createdAt: '2025-07-02T22:22:59.763Z',
            updatedAt: '2025-07-02T22:22:59.763Z',
            entitled: true,
            product: {
              id: 'ddf4ce71-2e14-4a43-bbe9-2a976fa2aa44',
              name: 'AI Lead Nurturing (AILN)',
            },
          },
          {
            displayId: '26737f94-6109-4311-adcb-7878bcb60f7c',
            companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
            productId: 'b1c2d3e4-5f6a-7b8c-9d0e-1f2a3b4c5d6e',
            startDate: '2025-06-25T00:00:00.000Z',
            endDate: null,
            units: 1,
            salesforceServiceId: null,
            createdAt: '2025-06-26T17:48:04.966Z',
            updatedAt: '2025-06-26T17:48:04.966Z',
            entitled: true,
            product: {
              id: 'b1c2d3e4-5f6a-7b8c-9d0e-1f2a3b4c5d6e',
              name: 'AI Advertising Specialist',
            },
          },
        ];

        (httpService.get as jest.Mock).mockImplementationOnce(() =>
          of({
            data: {
              data: mockEntitlements,
            },
          }),
        );

        const result = await service.getEntitlements(companyId);

        expect(result).toEqual(mockEntitlements);
      });
    });
  });
});
