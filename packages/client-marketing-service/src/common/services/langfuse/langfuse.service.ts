import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts';
import { Runnable } from '@langchain/core/runnables';
import { HttpException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Langfuse, TextPromptClient, ChatPromptClient } from 'langfuse';
import { CallbackHandler } from 'langfuse-langchain';

@Injectable()
export class LangfuseService {
  private readonly logger = new Logger(LangfuseService.name);
  private readonly langfuse: Langfuse;
  private readonly langfuseLangchainHandler: CallbackHandler;

  constructor(private configService: ConfigService) {
    this.langfuse = new Langfuse({
      secretKey: this.configService.get<string>('langfuse.secretKey'),
      publicKey: this.configService.get<string>('langfuse.publicKey'),
      baseUrl:
        this.configService.get<string>('langfuse.baseUrl') ||
        'https://us.cloud.langfuse.com',
    });
    this.langfuseLangchainHandler = new CallbackHandler({
      secretKey: this.configService.get<string>('langfuse.secretKey'),
      publicKey: this.configService.get<string>('langfuse.publicKey'),
      baseUrl: this.configService.get<string>('langfuse.baseUrl'),
      environment: this.configService.get<string>('langfuse.environment'),
      flushAt: this.configService.get<number>('langfuse.flushAt'),
    });
  }

  /**
   * Sanitize prompt content by replacing {{ with {
   */
  public sanitizePromptContent(content: string): string {
    this.logger.debug('Sanitizing prompt content');
    return content.replace(/{{(.*?)}}/g, '{$1}');
  }

  /**
   * Convert LangFuse chat prompt into LangChain ChatPromptTemplate
   */
  public convertLangfuseChatPromptToTemplate(
    messages: any[],
  ): ChatPromptTemplate {
    this.logger.debug(
      'Converting LangFuse prompt to LangChain template',
      messages,
    );
    return ChatPromptTemplate.fromMessages(
      messages.map(msg => {
        const role =
          msg.role === 'user'
            ? 'human'
            : msg.role === 'assistant'
              ? 'ai'
              : 'system';

        const content = this.sanitizePromptContent(msg.content);
        return [role, content];
      }),
    );
  }

  public convertLangfuseTextPromptToTemplate(
    prompt: TextPromptClient,
  ): PromptTemplate {
    this.logger.debug('Converting LangFuse text prompt to LangChain template');
    return PromptTemplate.fromTemplate(prompt.getLangchainPrompt());
  }

  private isTextPrompt(prompt: any): prompt is TextPromptClient {
    return prompt && !Array.isArray(prompt.prompt);
  }

  private isChatPrompt(prompt: any): prompt is ChatPromptClient {
    return prompt && Array.isArray(prompt.prompt);
  }

  /**
   * Get a text prompt from LangFuse by name
   */
  public async getTextPrompt(name: string): Promise<TextPromptClient> {
    this.logger.debug(`Fetching text prompt from LangFuse: ${name}`);
    try {
      const prompt = await this.langfuse.getPrompt(name);
      if (!this.isTextPrompt(prompt)) {
        throw new HttpException(
          `Expected text prompt but got chat prompt: ${name}`,
          400,
        );
      }
      return prompt;
    } catch (error) {
      this.logger.error(
        `Failed to fetch text prompt from LangFuse: ${error.message}`,
      );
      throw new HttpException(
        `Failed to fetch text prompt from LangFuse: ${name}`,
        500,
      );
    }
  }

  /**
   * Get a chat prompt from LangFuse by name
   */
  public async getChatPrompt(name: string): Promise<ChatPromptClient> {
    this.logger.debug(`Fetching chat prompt from LangFuse: ${name}`);
    try {
      const prompt = await this.langfuse.getPrompt(name);
      if (!this.isChatPrompt(prompt)) {
        throw new HttpException(
          `Expected chat prompt but got text prompt: ${name}`,
          400,
        );
      }
      return prompt;
    } catch (error) {
      this.logger.error(
        `Failed to fetch chat prompt from LangFuse: ${error.message}`,
      );
      throw new HttpException(
        `Failed to fetch chat prompt from LangFuse: ${name}`,
        500,
      );
    }
  }

  public async invoke(
    chain: Runnable,
    inputs: Record<string, unknown>,
    promptName: string,
    metadata?: Record<string, unknown>,
  ) {
    return await chain.invoke(inputs, {
      callbacks: [this.langfuseLangchainHandler],
      runName: promptName,
      ...(metadata && { metadata }),
    });
  }
}
