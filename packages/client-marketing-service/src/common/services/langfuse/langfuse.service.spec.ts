import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts';
import { Runnable } from '@langchain/core/runnables';
import { HttpException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';

import { LangfuseService } from './langfuse.service';

jest.mock('langfuse', () => {
  return {
    Langfuse: jest.fn().mockImplementation(() => ({
      getPrompt: jest.fn(),
    })),
    TextPromptClient: jest.fn(),
    ChatPromptClient: jest.fn(),
  };
});

jest.mock('langfuse-langchain', () => {
  return {
    CallbackHandler: jest.fn().mockImplementation(() => ({
      // Mock callback handler methods if needed
    })),
  };
});

describe('LangfuseService', () => {
  let service: LangfuseService;
  let mockLangfuse;
  let mockConfigService;

  beforeEach(async () => {
    mockConfigService = {
      get: jest.fn(key => {
        const config = {
          'langfuse.secretKey': 'mock-secret-key',
          'langfuse.publicKey': 'mock-public-key',
          'langfuse.baseUrl': 'https://mock-langfuse.com',
          'langfuse.environment': 'test',
          'langfuse.flushAt': 1,
        };
        return config[key];
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LangfuseService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<LangfuseService>(LangfuseService);
    mockLangfuse = (service as any).langfuse;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sanitizePromptContent', () => {
    it('should replace double curly braces with single curly braces', () => {
      const content = 'This is a {{variable}} in a prompt';
      const sanitized = service.sanitizePromptContent(content);
      expect(sanitized).toBe('This is a {variable} in a prompt');
    });

    it('should handle multiple replacements', () => {
      const content = 'This {{first}} and {{second}} should be replaced';
      const sanitized = service.sanitizePromptContent(content);
      expect(sanitized).toBe('This {first} and {second} should be replaced');
    });

    it('should handle nested curly braces correctly', () => {
      const content = 'This {{nested{{inner}}}} should work';
      const sanitized = service.sanitizePromptContent(content);
      expect(sanitized).toBe('This {nested{{inner}}} should work');
    });
  });

  describe('convertLangfuseChatPromptToTemplate', () => {
    it('should convert LangFuse chat prompt to LangChain ChatPromptTemplate', () => {
      const messages = [
        { role: 'system', content: 'You are an AI assistant' },
        { role: 'user', content: 'Hello {{name}}' },
        { role: 'assistant', content: 'Hi there!' },
      ];

      const template = service.convertLangfuseChatPromptToTemplate(messages);

      expect(template).toBeInstanceOf(ChatPromptTemplate);
      // Testing internal structure of ChatPromptTemplate is complicated,
      // so we're just verifying it's the right type
    });
  });

  describe('convertLangfuseTextPromptToTemplate', () => {
    it('should convert LangFuse text prompt to LangChain PromptTemplate', () => {
      const mockTextPrompt = {
        getLangchainPrompt: jest
          .fn()
          .mockReturnValue('This is a {variable} prompt'),
      };

      const template = service.convertLangfuseTextPromptToTemplate(
        mockTextPrompt as any,
      );

      expect(template).toBeInstanceOf(PromptTemplate);
      expect(mockTextPrompt.getLangchainPrompt).toHaveBeenCalled();
    });
  });

  describe('getTextPrompt', () => {
    it('should fetch text prompt from LangFuse successfully', async () => {
      const mockTextPrompt = {
        prompt: 'This is a mock text prompt',
        getLangchainPrompt: jest.fn(),
      };
      mockLangfuse.getPrompt = jest.fn().mockResolvedValue(mockTextPrompt);

      const result = await service.getTextPrompt('text-prompt-name');

      expect(mockLangfuse.getPrompt).toHaveBeenCalledWith('text-prompt-name');
      expect(result).toEqual(mockTextPrompt);
    });

    it('should throw HttpException when fetching invalid prompt type', async () => {
      // Mock a chat prompt when we expect a text prompt
      const mockChatPrompt = {
        prompt: [{ role: 'user', content: 'Hello' }],
      };
      mockLangfuse.getPrompt = jest.fn().mockResolvedValue(mockChatPrompt);

      await expect(service.getTextPrompt('text-prompt-name')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw HttpException when LangFuse API fails', async () => {
      mockLangfuse.getPrompt = jest
        .fn()
        .mockRejectedValue(new Error('API Error'));

      await expect(service.getTextPrompt('text-prompt-name')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('getChatPrompt', () => {
    it('should fetch chat prompt from LangFuse successfully', async () => {
      const mockChatPrompt = {
        prompt: [
          { role: 'system', content: 'You are an AI assistant' },
          { role: 'user', content: 'Hello' },
        ],
      };
      mockLangfuse.getPrompt = jest.fn().mockResolvedValue(mockChatPrompt);

      const result = await service.getChatPrompt('chat-prompt-name');

      expect(mockLangfuse.getPrompt).toHaveBeenCalledWith('chat-prompt-name');
      expect(result).toEqual(mockChatPrompt);
    });

    it('should throw HttpException when fetching invalid prompt type', async () => {
      // Mock a text prompt when we expect a chat prompt
      const mockTextPrompt = {
        prompt: 'This is a text prompt',
      };
      mockLangfuse.getPrompt = jest.fn().mockResolvedValue(mockTextPrompt);

      await expect(service.getChatPrompt('chat-prompt-name')).rejects.toThrow(
        HttpException,
      );
    });

    it('should throw HttpException when LangFuse API fails', async () => {
      mockLangfuse.getPrompt = jest
        .fn()
        .mockRejectedValue(new Error('API Error'));

      await expect(service.getChatPrompt('chat-prompt-name')).rejects.toThrow(
        HttpException,
      );
    });
  });

  describe('invoke', () => {
    it('should invoke a chain with the langfuse handler', async () => {
      const mockChain = {
        invoke: jest.fn().mockResolvedValue('response'),
      };
      const mockInputs = { input: 'test input' };
      const mockPromptName = 'test-prompt';
      const mockMetadata = { userId: '123' };

      const result = await service.invoke(
        mockChain as unknown as Runnable,
        mockInputs,
        mockPromptName,
        mockMetadata,
      );

      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [expect.any(Object)],
        runName: mockPromptName,
        metadata: mockMetadata,
      });
      expect(result).toBe('response');
    });

    it('should invoke a chain without metadata', async () => {
      const mockChain = {
        invoke: jest.fn().mockResolvedValue('response'),
      };
      const mockInputs = { input: 'test input' };
      const mockPromptName = 'test-prompt';

      const result = await service.invoke(
        mockChain as unknown as Runnable,
        mockInputs,
        mockPromptName,
      );

      expect(mockChain.invoke).toHaveBeenCalledWith(mockInputs, {
        callbacks: [expect.any(Object)],
        runName: mockPromptName,
      });
      expect(result).toBe('response');
    });
  });
});
