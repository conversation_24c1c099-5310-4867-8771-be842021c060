import {
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { AllExceptionsFilter } from './all-exceptions.filter';

describe('AllExceptionsFilter', () => {
  let filter: AllExceptionsFilter;
  let mockJson: jest.Mock;
  let mockStatus: jest.Mock;
  let mockGetRequest: jest.Mock;
  let mockGetResponse: jest.Mock;
  let mockArgumentsHost: ArgumentsHost;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AllExceptionsFilter],
    }).compile();

    filter = module.get<AllExceptionsFilter>(AllExceptionsFilter);

    // Mock the logger to avoid console output during tests
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});

    // Setup mocks for the HTTP context
    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    mockGetRequest = jest.fn().mockReturnValue({
      method: 'GET',
      url: '/test',
    });
    mockGetResponse = jest.fn().mockReturnValue({
      status: mockStatus,
    });

    // TODO We should also have tests for graphql type
    mockArgumentsHost = {
      getType: jest.fn().mockReturnValue('http'),
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: mockGetResponse,
        getRequest: mockGetRequest,
      }),
    } as unknown as ArgumentsHost;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  it('should handle HttpException correctly', () => {
    const mockException = new HttpException(
      'Test message',
      HttpStatus.BAD_REQUEST,
    );

    filter.catch(mockException, mockArgumentsHost);

    expect(mockStatus).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: HttpStatus.BAD_REQUEST,
        path: '/test',
        method: 'GET',
        message: 'Test message',
      }),
    );
  });

  it('should handle Error objects correctly', () => {
    const mockError = new Error('Test error');

    filter.catch(mockError, mockArgumentsHost);

    expect(mockStatus).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        path: '/test',
        method: 'GET',
        message: 'Test error',
        exception: {
          name: 'Error',
          message: 'Test error',
        },
      }),
    );
  });

  it('should handle unknown exceptions correctly', () => {
    const unknownException = { custom: 'exception' };

    filter.catch(unknownException, mockArgumentsHost);

    expect(mockStatus).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        path: '/test',
        method: 'GET',
        message: 'Internal server error',
        exception: unknownException,
      }),
    );
  });

  it('should extract message from HttpException response object', () => {
    const mockResponse = {
      message: 'Detailed error message',
      otherInfo: 'additional information',
    };
    const mockException = new HttpException(
      mockResponse,
      HttpStatus.BAD_REQUEST,
    );

    filter.catch(mockException, mockArgumentsHost);

    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Detailed error message',
        exception: mockResponse,
      }),
    );
  });

  it('should log the error with the request method and URL', () => {
    const loggerSpy = jest.spyOn(Logger.prototype, 'error');
    const mockError = new HttpException('Test error', 400);

    filter.catch(mockError, mockArgumentsHost);

    expect(loggerSpy).toHaveBeenCalledWith(
      'GET /test',
      expect.stringContaining('Test error'),
    );
  });

  it('should include stack trace in non-production environment', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const mockError = new Error('Test error');

    filter.catch(mockError, mockArgumentsHost);

    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        stack: expect.any(String),
      }),
    );

    // Restore original NODE_ENV
    process.env.NODE_ENV = originalNodeEnv;
  });
});
