// src/filters/all-exceptions.filter.ts
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { GqlArgumentsHost } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { GraphQLError } from 'graphql';

export class ExceptionType {
  @ApiProperty()
  type: string;

  @ApiProperty()
  displayMessage: string;

  @ApiProperty()
  statusCode: number;

  @ApiProperty()
  timestamp: string;

  @ApiProperty({ required: false })
  path?: string;

  @ApiProperty({ required: false })
  debug?: {
    message?: string;
    stack?: string | object;
  };
}

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    // @ts-expect-error graphql is the type of host
    if (host.getType() === 'graphql') {
      const gqlHost = GqlArgumentsHost.create(host);
      const info = gqlHost.getInfo();
      const path = info?.path?.key || undefined;

      const statusCode =
        exception instanceof HttpException
          ? exception.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;

      const responseBody: ExceptionType = {
        type: InternalServerErrorException.name,
        displayMessage: '',
        timestamp: new Date().toISOString(),
        path,
        statusCode,
      };

      if (exception instanceof Error) {
        const { displayMessage = exception.message } = exception as any;
        const stackTrace =
          exception instanceof HttpException
            ? (exception.getResponse() as any)?.debug?.stack
            : exception.stack;
        responseBody.displayMessage = displayMessage;
        responseBody.debug = {
          message: exception.message,
          stack: stackTrace,
        };
      }

      Logger.error(JSON.stringify(responseBody));

      throw new GraphQLError(
        responseBody.displayMessage || 'Internal server error',
        {
          extensions: {
            code: statusCode,
            exception: responseBody,
          },
        },
      );
    }
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Get status code and message
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    let message: string = 'Internal server error';
    let stack: string | undefined = undefined;
    const error = exception;

    // Extract message from HttpException
    if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();
      message =
        typeof exceptionResponse === 'object' && 'message' in exceptionResponse
          ? (exceptionResponse.message as string)
          : exception.message;

      // Log the error (very important for debugging)
      if (request) {
        this.logger.error(
          `${request.method} ${request.url}`,
          stack || JSON.stringify(error),
        );
      } else {
        this.logger.error(stack || JSON.stringify(error));
      }
    } else if (exception instanceof Error) {
      // Extract message from Error objects
      message = exception.message;
      stack = exception.stack || undefined;

      // Log the error (without request data for non-http errors)
      this.logger.error(stack || JSON.stringify(error));
    }

    if (response?.status) {
      // Send detailed response
      response.status(status).json({
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        method: request.method,
        exception:
          exception instanceof HttpException
            ? exception.getResponse()
            : exception instanceof Error
              ? {
                  name: exception.name,
                  message: exception.message,
                }
              : exception,
        message,
        ...(stack && { stack }),
        // Only include stack trace in development environment
        ...(process.env.NODE_ENV !== 'production' && { stack }),
      });
    }
  }
}
