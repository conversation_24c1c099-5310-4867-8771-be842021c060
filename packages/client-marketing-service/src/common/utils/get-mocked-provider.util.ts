import { Type, ValueProvider } from '@nestjs/common';

interface getMockedProvider {
  <T>(cls: Type<T>): ValueProvider;
}
interface getMockedProvider {
  <T>(token: string, cls: Type<T>): ValueProvider;
}
export const getMockedProvider = <T>(
  clsOrToken: Type<T> | (new (...args: any[]) => T) | string,
  cls?: Type<T>,
): ValueProvider => {
  let token: any;
  let prototype: any;

  if (typeof clsOrToken === 'string') {
    if (!cls) {
      throw new Error(
        'Expected class as the second argument when token is provided as the first argument',
      );
    }
    token = clsOrToken;
    prototype = cls.prototype;
  } else {
    token = clsOrToken;
    prototype = clsOrToken.prototype;
  }
  const mock = Object.getOwnPropertyNames(prototype).reduce(
    (acc, methodName) => {
      if (methodName === 'constructor') {
        return acc;
      }

      acc[methodName] = jest.fn();
      return acc;
    },
    {},
  );
  return {
    provide: token,
    useValue: mock,
  };
};
