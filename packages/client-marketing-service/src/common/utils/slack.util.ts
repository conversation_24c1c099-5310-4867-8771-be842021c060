import { Logger } from '@nestjs/common';
import { Email, TemplateData } from 'src/notification/notification.types';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

const SLACK_WEBHOOK_URL =
  '*******************************************************************************';

const logger = new Logger('SlackUtil');

/**
 * Sends a notification to Slack when a STAR update fails
 * This function handles errors internally and never throws
 */
export async function notifyStarFailure({
  recommendation,
  error,
  jobIds,
}: {
  recommendation: Recommendation;
  error: unknown;
  jobIds: {
    groupJobId: string | undefined;
    recommendationJobId: string | undefined;
  };
}): Promise<void> {
  // DELETED: seoPage property removed with SeoPage entity
  // const seoPage = recommendation.seoPage;
  // Use scrapedPage relation instead of seoPage for company information
  const scrapedPage = recommendation.scrapedPage;
  const presenceUrl = `https://app.luxurypresence.com/admin/accounts/${scrapedPage?.companyId ?? 'unknown'}`;
  await sendSlackMessage(`
    STAR failure\n
    <${presenceUrl}|${scrapedPage?.companyId ?? 'unknown'}>: ${scrapedPage?.url ?? 'unknown'}\n
    ${recommendation.type}: "${recommendation.recommendationValue}"\n
    Error: ${error instanceof Error ? error.message : String(error)}\n
    Group Job ID: ${jobIds.groupJobId ?? 'N/A'}\n
    Recommendation Job ID: ${jobIds.recommendationJobId ?? 'N/A'}\n
  `);
}

/**
 * Sends a notification to Slack when a STAR update fails
 * This function handles errors internally and never throws
 */
export async function notifySendEmailFailure({
  email,
  error,
  jobId,
}: {
  email: Email;
  error: unknown;
  jobId?: string | undefined;
}): Promise<void> {
  await sendSlackMessage(`
    Send email failure\n
    <${email.to as string}|${(email.dynamicTemplateData as TemplateData)?.user?.firstName} ${(email.dynamicTemplateData as TemplateData)?.user?.lastName}>\n
    <Group ID: ${(email.dynamicTemplateData as TemplateData)?.seoGroup?.id}>\n
    <Company ID: ${(email.dynamicTemplateData as TemplateData)?.seoGroup?.companyId}>\n
    Error: ${error instanceof Error ? error.message : String(error)}\n
    Job ID: ${jobId ?? 'N/A'}\n
  `);
}

/**
 * Generic function to send a message to Slack
 */
export async function sendSlackMessage(text: string): Promise<void> {
  if (process.env.ENV !== 'production' || text.includes('example.com')) {
    logger.log('Slack notification: ' + text);
    return;
  }
  try {
    await fetch(SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text }),
    });
    logger.log('Sent Slack notification');
  } catch (error) {
    logger.error(`Failed to send Slack notification: ${error.message}`);
  }
}
