import Tracer from 'dd-trace';

// Note: Do not use process.env.NODE_ENV to determine environment here
// because it is set to "production" in the staging environment.
// Which would result in staging traffic being labeled as production
// in datadog.

const { ENV } = process.env;

let tracer: typeof Tracer | null = null;

if (ENV === 'staging' || ENV === 'production') {
  // Initialize Datadog Javascript Tracer
  tracer = Tracer.init({
    tags: {
      env: ENV,
      group: 'client-marketing',
      team: 'SEO',
    },
    runtimeMetrics: true,
  });
  tracer.use('express', {
    service: 'seo-automation-api-gateway',
  });
}

export default tracer;
