export class SeederFactories {
  // Group entity factories
  static groupTitle(faker: any): string {
    const titles = [
      'Homepage SEO Optimization',
      'Property Listing Enhancement',
      'Local Search Improvements',
      'Mortgage Calculator Updates',
      'Agent Bio Optimization',
      'Neighborhood Guide Updates',
      'Contact Page Improvements',
      'Service Pages Enhancement',
    ];
    return faker?.helpers.arrayElement(titles) || 'SEO Optimization Group';
  }

  static groupDescription(faker: any): string {
    const descriptions = [
      'Optimize meta titles and descriptions for better search engine visibility',
      'Improve page content structure and keyword targeting',
      'Enhance local SEO elements including schema markup',
      'Update property listing pages for better conversion rates',
      'Optimize agent bio pages for local search results',
      'Improve neighborhood guide content and SEO performance',
      'Enhance contact page optimization and call-to-action elements',
    ];
    return (
      faker?.helpers.arrayElement(descriptions) ||
      'SEO optimization recommendations for improved search performance'
    );
  }

  static groupMetadata(): Record<string, any> {
    return {};
  }

  // Keyword entity factories
  static keywordValue(faker: any): string {
    const keywords = [
      'real estate agent',
      'homes for sale',
      'property search',
      'local realtor',
      'mortgage rates',
      'buy house',
      'sell property',
      'property investment',
      'rental properties',
      'home valuation',
      'market analysis',
      'first time buyer',
      'luxury homes',
      'foreclosure listings',
      'open houses',
      'home buying guide',
      'selling tips',
      'property management',
      'commercial real estate',
      'real estate market',
    ];
    return faker?.helpers.arrayElement(keywords) || 'real estate';
  }

  static keywordMetadata(): Record<string, any> {
    return {};
  }

  // Page entity factories
  static pageUrl(faker: any): string {
    const domains = [
      'realestate',
      'homes',
      'properties',
      'dream-homes',
      'luxury-real-estate',
    ];
    const paths = [
      'home',
      'properties',
      'listings',
      'about',
      'buy',
      'sell',
      'agents',
      'contact',
    ];
    const domain = faker?.helpers.arrayElement(domains) || 'realestate';
    const path = faker?.helpers.arrayElement(paths) || 'home';
    const uniqueId =
      faker?.string.alphanumeric(8) ||
      Math.random().toString(36).substring(2, 10);
    return `https://${domain}-${uniqueId}.com/${path}`;
  }

  static pageMetadata(): Record<string, any> {
    return {};
  }

  // Recommendation entity factories
  static recommendationCurrentValue(faker: any): string {
    const currentValues = [
      'Home | Real Estate Company',
      'Welcome to Our Website',
      'Real Estate Services',
      'About Us - Real Estate',
      'Contact Us Today',
    ];
    return faker?.helpers.arrayElement(currentValues) || 'Current SEO Value';
  }

  static recommendationValue(faker: any): string {
    const recommendationValues = [
      'Best Real Estate Deals in [City] | Top Agents & Properties',
      'Find Your Dream Home with Expert Real Estate Agents',
      'Professional Real Estate Services | Buy & Sell Properties',
      'Your Trusted Real Estate Partners in [City]',
      'Contact Our Expert Real Estate Team Today',
    ];
    return (
      faker?.helpers.arrayElement(recommendationValues) || 'Improved SEO Value'
    );
  }

  static recommendationReasoning(faker: any): string {
    const reasons = [
      'Current title lacks location-specific keywords and value proposition to improve local SEO performance',
      'Meta description is too short and missing compelling call-to-action elements',
      'Main heading should establish trust and include relevant real estate keywords',
      'Current content lacks keyword optimization and search intent alignment',
      'Page structure needs improvement for better user experience and SEO',
    ];
    return (
      faker?.helpers.arrayElement(reasons) ||
      'SEO optimization needed for better search performance'
    );
  }

  // Scrape entity factories
  static scrapeRawHtml(faker: any): string {
    const sampleHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <title>${
    faker?.company.name() || 'Real Estate Company'
  } - ${faker?.location.city() || 'City'} Real Estate</title>
  <meta name="description" content="${
    faker?.lorem.sentence() || 'Professional real estate services'
  }">
</head>
<body>
  <h1>${faker?.company.name() || 'Real Estate Company'}</h1>
  <p>${faker?.lorem.paragraph() || 'We help you find your dream home'}</p>
</body>
</html>`;
    return sampleHtml;
  }

  static scrapeMarkdown(faker: any): string {
    const sampleMarkdown = `# ${faker?.company.name() || 'Real Estate Company'}

## About Us
${faker?.lorem.paragraph() || 'We are a professional real estate company'}

## Services
- Home Buying
- Home Selling
- Property Investment
- Market Analysis

## Contact
Call us today: ${faker?.phone.number() || '(*************'}`;
    return sampleMarkdown;
  }

  static scrapeCurrentValues(faker: any): Record<string, string> {
    return {
      metaTitle: `${faker?.company.name() || 'Real Estate Company'} - ${
        faker?.location.city() || 'City'
      } Real Estate`,
      metaDescription:
        faker?.lorem.sentence() ||
        'Professional real estate services in your area',
      h1: faker?.company.name() || 'Real Estate Company',
    };
  }

  // Scheduled Action entity factories
  static scheduledActionContentPayload(): object {
    // For SURFACING_PENDING and later statuses, this would have content
    // But the seeder handles this logic, so we return empty by default
    return {};
  }

  static scheduledActionGenerationPayload(faker: any): object {
    // The seeder will override this with proper generation payloads
    // based on the action's status and stage in the pipeline
    const websiteUrl = `https://company-${faker?.number.int({ min: 1, max: 100 }) || 1}.com`;
    return {
      websiteUrl,
    };
  }

  static scheduledActionFailureReason(): object {
    // Only populated for FAILED status, handled by seeder
    return {};
  }

  // StoreSEODraftResponse entity factories
  static createStoredKeyword(faker: any, keyword?: string): any {
    const uuid =
      faker?.string.uuid() ||
      'keyword-' + Math.random().toString(36).substring(2, 10);
    return {
      id: uuid,
      keyword: keyword || SeederFactories.keywordValue(faker),
      metadata: SeederFactories.keywordMetadata(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  static createStoredScrapedPage(
    faker: any,
    companyId: string,
    url?: string,
  ): any {
    const uuid =
      faker?.string.uuid() ||
      'page-' + Math.random().toString(36).substring(2, 10);
    return {
      id: uuid,
      companyId,
      url: url || SeederFactories.pageUrl(faker),
      pageName: faker?.company.name() || 'Real Estate Company',
      pageType:
        faker?.helpers.arrayElement([
          'HOMEPAGE',
          'HOME_VALUATION',
          'PROPERTY_LISTING',
          'AGENT_BIO',
          'CONTACT',
        ]) || 'HOMEPAGE',
      metadata: SeederFactories.pageMetadata(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };
  }

  static createStoredScrape(
    faker: any,
    companyId: string,
    scrapedPageId: string,
  ): any {
    const uuid =
      faker?.string.uuid() ||
      'scrape-' + Math.random().toString(36).substring(2, 10);
    const mediaId =
      faker?.string.uuid() ||
      'media-' + Math.random().toString(36).substring(2, 10);
    const currentValues = SeederFactories.scrapeCurrentValues(faker);

    return {
      id: uuid,
      companyId,
      scrapedPageId,
      rawHtml: SeederFactories.scrapeRawHtml(faker),
      markdown: SeederFactories.scrapeMarkdown(faker),
      currentScrapedValues: {
        metaTitle: currentValues.metaTitle,
        metaDescription: currentValues.metaDescription,
        mainHeading: currentValues.h1,
      },
      mediaId,
      scrapedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  static createStoredPageKeyword(
    faker: any,
    scrapedPageId: string,
    keywordId: string,
  ): any {
    const uuid =
      faker?.string.uuid() ||
      'page-keyword-' + Math.random().toString(36).substring(2, 10);
    return {
      id: uuid,
      scrapedPageId,
      keywordId,
      originalRank: null,
      currentRank: null,
      rankCheckedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  static createStoredGroup(
    faker: any,
    companyId: string,
    keywordId: string,
  ): any {
    const uuid =
      faker?.string.uuid() ||
      'group-' + Math.random().toString(36).substring(2, 10);
    return {
      id: uuid,
      companyId,
      keywordId,
      title: SeederFactories.groupTitle(faker),
      description: SeederFactories.groupDescription(faker),
      metadata: SeederFactories.groupMetadata(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };
  }

  static createStoredRecommendations(
    faker: any,
    scrapeId: string,
    groupId: string,
  ): any[] {
    const recommendations: any[] = [];
    const types = ['META_TITLE', 'META_DESCRIPTION', 'MAIN_HEADING'];

    for (const type of types) {
      const uuid =
        faker?.string.uuid() ||
        'rec-' + Math.random().toString(36).substring(2, 10);
      recommendations.push({
        id: uuid,
        scrapeId,
        groupId,
        type,
        currentValue: SeederFactories.recommendationCurrentValue(faker),
        recommendationValue: SeederFactories.recommendationValue(faker),
        reasoning: SeederFactories.recommendationReasoning(faker),
        status: 'PENDING',
        rejectionReason: null,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return recommendations;
  }

  static createStoreSEODraftResponse(
    faker: any,
    companyId: string,
    keyword?: string,
    url?: string,
  ): any {
    const savedKeyword = SeederFactories.createStoredKeyword(faker, keyword);
    const savedPage = SeederFactories.createStoredScrapedPage(
      faker,
      companyId,
      url,
    );
    const savedScrape = SeederFactories.createStoredScrape(
      faker,
      companyId,
      savedPage.id,
    );
    const savedPageKeyword = SeederFactories.createStoredPageKeyword(
      faker,
      savedPage.id,
      savedKeyword.id,
    );
    const savedGroup = SeederFactories.createStoredGroup(
      faker,
      companyId,
      savedKeyword.id,
    );
    const savedRecommendations = SeederFactories.createStoredRecommendations(
      faker,
      savedScrape.id,
      savedGroup.id,
    );

    return {
      savedKeyword,
      savedRecommendations,
      savedScrape,
      savedPage: savedPage,
      savedPageKeyword,
      savedGroup,
    };
  }
}
