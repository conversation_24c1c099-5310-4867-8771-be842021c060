import { Type } from '@nestjs/common';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { Paginated } from 'nestjs-paginate';

export const PaginateToGQL = <T>(paginatedResult: Paginated<T>) => {
  const meta = paginatedResult.meta || {};
  return {
    data: paginatedResult.data || [],
    totalItems: meta.totalItems || 0,
    currentPage: meta.currentPage || 1,
    totalPages: meta.totalPages || 1,
    itemsPerPage: meta.itemsPerPage || 50,
    hasNextPage: (meta.currentPage || 1) < (meta.totalPages || 1),
    hasPreviousPage: (meta.currentPage || 1) > 1,
  };
};

export interface IPaginatedType<T> {
  data: T[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export function PaginatedEntity<T>(classRef: Type<T>): Type<IPaginatedType<T>> {
  @ObjectType({ isAbstract: true })
  abstract class PaginatedType implements IPaginatedType<T> {
    @Field(() => [classRef])
    data: T[];

    @Field(() => Int)
    totalItems: number;

    @Field(() => Int)
    currentPage: number;

    @Field(() => Int)
    totalPages: number;

    @Field(() => Int)
    itemsPerPage: number;

    @Field(() => Boolean)
    hasNextPage: boolean;

    @Field(() => Boolean)
    hasPreviousPage: boolean;
  }
  return PaginatedType as Type<IPaginatedType<T>>;
}
