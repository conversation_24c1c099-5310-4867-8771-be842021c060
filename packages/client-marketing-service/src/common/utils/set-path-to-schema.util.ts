import { AppDataSource } from 'src/data-source';
import { QueryRunner } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';

export async function setPathToSchema(queryRunner: QueryRunner): Promise<void> {
  const options = <PostgresConnectionOptions>AppDataSource.options;
  if (typeof options.schema === 'undefined') return;
  await queryRunner.query(`SET search_path TO ${options.schema}`);
}
