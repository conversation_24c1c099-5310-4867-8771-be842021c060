import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CampaignDataLoader } from './campaign.dataloader';
// import { PropertyDataLoader } from './property.dataloader';
import { Campaign } from '../../campaign/entities/campaign.entity';
// import { PropertyService } from '../../property/property.service';

@Module({
  imports: [TypeOrmModule.forFeature([Campaign])],
  providers: [
    CampaignDataLoader,
    // PropertyDataLoader, PropertyService
  ],
  exports: [
    CampaignDataLoader,
    //  PropertyDataLoader
  ],
})
export class DataLoadersModule {}
