import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';

import { CampaignDataLoader } from './campaign.dataloader';
import {
  Campaign,
  CampaignStatus,
} from '../../campaign/entities/campaign.entity';

describe('CampaignDataLoader', () => {
  let dataLoader: CampaignDataLoader;
  let mockRepository: jest.Mocked<Repository<Campaign>>;

  beforeEach(async () => {
    const mockRepo = {
      find: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CampaignDataLoader,
        {
          provide: getRepositoryToken(Campaign),
          useValue: mockRepo,
        },
      ],
    }).compile();

    dataLoader = module.get<CampaignDataLoader>(CampaignDataLoader);
    mockRepository = module.get(getRepositoryToken(Campaign));
  });

  it('should batch campaign queries', async () => {
    const campaigns = [
      {
        id: '1',
        propertyId: 'prop1',
        status: CampaignStatus.PENDING,
      } as Campaign,
      {
        id: '2',
        propertyId: 'prop2',
        status: CampaignStatus.SUCCESS,
      } as Campaign,
    ];
    mockRepository.find.mockResolvedValue(campaigns);

    const loader = dataLoader.createLoader();
    const results = await Promise.all([
      loader.load('prop1'),
      loader.load('prop2'),
      loader.load('prop3'),
    ]);

    expect(mockRepository.find).toHaveBeenCalledTimes(1);
    expect(mockRepository.find).toHaveBeenCalledWith({
      where: {
        propertyId: In(['prop1', 'prop2', 'prop3']),
        status: In([CampaignStatus.PENDING, CampaignStatus.SUCCESS]),
      },
    });
    expect(results).toEqual([campaigns[0], campaigns[1], null]);
  });

  it('should handle empty property ids', async () => {
    mockRepository.find.mockResolvedValue([]);

    const loader = dataLoader.createLoader();
    const results = await Promise.all([loader.load('nonexistent')]);

    expect(mockRepository.find).toHaveBeenCalledTimes(1);
    expect(results).toEqual([null]);
  });

  it('should create new loader instances', () => {
    const loader1 = dataLoader.createLoader();
    const loader2 = dataLoader.createLoader();

    expect(loader1).not.toBe(loader2);
  });
});
