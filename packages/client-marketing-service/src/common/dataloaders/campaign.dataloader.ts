import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import DataLoader from 'dataloader';
import { Repository, In } from 'typeorm';

import {
  Campaign,
  CampaignStatus,
} from '../../campaign/entities/campaign.entity';

@Injectable()
export class CampaignDataLoader {
  constructor(
    @InjectRepository(Campaign)
    private readonly campaignRepository: Repository<Campaign>,
  ) {}

  createLoader(): DataLoader<string, Campaign | null> {
    return new DataLoader<string, Campaign | null>(
      async (propertyIds: readonly string[]) => {
        const campaigns = await this.campaignRepository.find({
          where: {
            propertyId: In([...propertyIds]),
            status: In([CampaignStatus.PENDING, CampaignStatus.SUCCESS]),
          },
        });

        const campaignMap = new Map<string, Campaign>();
        campaigns.forEach(campaign => {
          campaignMap.set(campaign.propertyId, campaign);
        });

        return propertyIds.map(
          propertyId => campaignMap.get(propertyId) || null,
        );
      },
      {
        cache: false,
      },
    );
  }
}
