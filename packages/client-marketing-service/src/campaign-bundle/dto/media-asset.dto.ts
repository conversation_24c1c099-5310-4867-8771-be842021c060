import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsNotEmpty,
  IsDate,
} from 'class-validator';

export class MediaAssetDto {
  @ApiProperty({
    description: 'Unique identifier for the media asset',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Type of media resource',
    example: 'image',
  })
  @IsString()
  @IsNotEmpty()
  resourceType: string;

  @ApiProperty({
    description: 'Original filename of the media asset',
    example: 'house-photo.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  originalFileName?: string;

  @ApiProperty({
    description: 'Description of the media asset',
    example: 'Front view of the property',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Cloudinary public ID',
    example: 'property-photos/house-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  cloudinaryPublicId?: string;

  @ApiProperty({
    description: 'Alt text for the image',
    example: 'Beautiful two-story home with garden',
    required: false,
  })
  @IsOptional()
  @IsString()
  alt?: string;

  @ApiProperty({
    description: 'File format',
    example: 'jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  format?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  bytes?: number;

  @ApiProperty({
    description: 'Image width in pixels',
    example: 1920,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  width?: number;

  @ApiProperty({
    description: 'Image height in pixels',
    example: 1080,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  height?: number;

  @ApiProperty({
    description: 'Video duration in seconds',
    example: 120,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiProperty({
    description: 'Original URL of the media asset',
    example: 'https://example.com/original.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  originalUrl?: string;

  @ApiProperty({
    description: 'Thumbnail URL',
    example: 'https://example.com/thumb.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  thumbnailUrl?: string;

  @ApiProperty({
    description: 'Small size URL',
    example: 'https://example.com/small.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  smallUrl?: string;

  @ApiProperty({
    description: 'Medium size URL',
    example: 'https://example.com/medium.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  mediumUrl?: string;

  @ApiProperty({
    description: 'Large size URL',
    example: 'https://example.com/large.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  largeUrl?: string;

  @ApiProperty({
    description: 'Extra large size URL',
    example: 'https://example.com/xlarge.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  xLargeUrl?: string;

  @ApiProperty({
    description: 'Double extra large size URL',
    example: 'https://example.com/xxlarge.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  xxLargeUrl?: string;

  @ApiProperty({
    description: 'Author name',
    example: 'John Photographer',
    required: false,
  })
  @IsOptional()
  @IsString()
  authorName?: string;

  @ApiProperty({
    description: 'Author URL',
    example: 'https://photographer.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  authorUrl?: string;

  @ApiProperty({
    description: 'Source name',
    example: 'Property Photos Inc',
    required: false,
  })
  @IsOptional()
  @IsString()
  sourceName?: string;

  @ApiProperty({
    description: 'Source URL',
    example: 'https://propertyphotos.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  sourceUrl?: string;

  @ApiProperty({
    description: 'Whether the media is shared',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  shared?: boolean;

  @ApiProperty({
    description: 'Whether the media is curated',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  curated?: boolean;

  @ApiProperty({
    description: 'External link URL',
    example: 'https://external-site.com/photo',
    required: false,
  })
  @IsOptional()
  @IsString()
  externalLinkUrl?: string;

  @ApiProperty({
    description: 'Display name for the media',
    example: 'Property Front View',
    required: false,
  })
  @IsOptional()
  @IsString()
  displayName?: string;

  @ApiProperty({
    description: 'Alt tag text',
    example: 'Modern home exterior',
    required: false,
  })
  @IsOptional()
  @IsString()
  altTagText?: string;

  @ApiProperty({
    description: 'Whether the media can be edited',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  canEdit?: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-05-19T10:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-05-19T12:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}
