import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsNumber } from 'class-validator';

export class AddressInfoDto {
  @ApiProperty({
    description: 'Full formatted address',
    example: '123 Main Street, Los Angeles, CA 90210, USA',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressFull?: string | null;

  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main Street',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressLine1?: string | null;

  @ApiProperty({
    description: 'Address line 2',
    example: 'Unit 5A',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressLine2?: string | null;

  @ApiProperty({
    description: 'City',
    example: 'Los Angeles',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressCity?: string | null;

  @ApiProperty({
    description: 'State or province',
    example: 'CA',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressState?: string | null;

  @ApiProperty({
    description: 'Country',
    example: 'USA',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressCountry?: string | null;

  @ApiProperty({
    description: 'Postal code',
    example: '90210',
    required: false,
  })
  @IsOptional()
  @IsString()
  postalCode?: string | null;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: 34.0522,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  latitude?: number | null;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: -118.2437,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  longitude?: number | null;
}
