import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsNotEmpty,
  ValidateIf,
} from 'class-validator';

export class AgentInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the agent',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  agentId: string;

  @ApiProperty({
    description: 'Agent first name',
    example: 'John',
    required: false,
  })
  @ValidateIf((_, value) => value !== undefined)
  @IsNotEmpty()
  @IsString()
  firstName?: string;

  @ApiProperty({
    description: 'Agent last name',
    example: '<PERSON>',
    required: false,
  })
  @ValidateIf((_, value) => value !== undefined)
  @IsNotEmpty()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: 'Agent email address',
    example: '<EMAIL>',
    required: false,
  })
  @ValidateIf((_, value) => value !== undefined)
  @IsNotEmpty()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Primary phone number',
    example: '******-123-4567',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string | null;

  @ApiProperty({
    description: 'Secondary phone number',
    example: '******-987-6543',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber2?: string | null;

  @ApiProperty({
    description: 'Agent avatar URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  avatar?: string | null;

  @ApiProperty({
    description: 'Agent position/title',
    example: 'Senior Real Estate Agent',
    required: false,
  })
  @IsOptional()
  @IsString()
  position?: string | null;

  @ApiProperty({
    description: 'Long biography',
    example:
      'John Smith has been helping families find their dream homes for over 10 years...',
    required: false,
  })
  @IsOptional()
  @IsString()
  bioLong?: string | null;

  @ApiProperty({
    description: 'Short biography',
    example: 'Experienced real estate agent specializing in luxury homes.',
    required: false,
  })
  @IsOptional()
  @IsString()
  bioShort?: string | null;

  @ApiProperty({
    description: 'Real estate license number',
    example: 'RE123456789',
    required: false,
  })
  @IsOptional()
  @IsString()
  license?: string | null;

  @ApiProperty({
    description: 'Agent website URL',
    example: 'https://johnsmith-realty.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  urlWebsite?: string | null;

  @ApiProperty({
    description: 'Facebook profile URL',
    example: 'https://facebook.com/johnsmithrealty',
    required: false,
  })
  @IsOptional()
  @IsString()
  urlFacebook?: string | null;

  @ApiProperty({
    description: 'Instagram profile URL',
    example: 'https://instagram.com/johnsmithrealty',
    required: false,
  })
  @IsOptional()
  @IsString()
  urlInstagram?: string | null;

  @ApiProperty({
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/johnsmith',
    required: false,
  })
  @IsOptional()
  @IsString()
  urlLinkedin?: string | null;

  @ApiProperty({
    description: 'Twitter profile URL',
    example: 'https://twitter.com/johnsmithrealty',
    required: false,
  })
  @IsOptional()
  @IsString()
  urlTwitter?: string | null;
}
