import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsString,
  IsOptional,
  IsBoolean,
  Min,
} from 'class-validator';

export class PriceInfoDto {
  @ApiProperty({
    description: 'Current listing price',
    example: 750000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  listPrice?: number | null;

  @ApiProperty({
    description: 'Final sales price',
    example: 725000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  salesPrice?: number | null;

  @ApiProperty({
    description: 'Currency code',
    example: 'USD',
    required: false,
  })
  @IsOptional()
  @IsString()
  currency?: string | null;

  @ApiProperty({
    description: 'Whether price is available upon request',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  priceUponRequest?: boolean | null;
}
