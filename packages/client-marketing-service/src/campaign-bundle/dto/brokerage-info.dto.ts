import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsEmail,
  IsNotEmpty,
  ValidateIf,
} from 'class-validator';

export class BrokerageInfoDto {
  @ApiProperty({
    description: 'Unique identifier for the brokerage',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  brokerageId: string;

  @ApiProperty({
    description: 'Brokerage name',
    example: 'Premium Realty Group',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main Street',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressLine1?: string | null;

  @ApiProperty({
    description: 'Address line 2',
    example: 'Suite 100',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressLine2?: string | null;

  @ApiProperty({
    description: 'City',
    example: 'Los Angeles',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressCity?: string | null;

  @ApiProperty({
    description: 'State or province',
    example: 'CA',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressState?: string | null;

  @ApiProperty({
    description: 'Country',
    example: 'USA',
    required: false,
  })
  @IsOptional()
  @IsString()
  addressCountry?: string | null;

  @ApiProperty({
    description: 'Postal code',
    example: '90210',
    required: false,
  })
  @IsOptional()
  @IsString()
  postalCode?: string | null;

  @ApiProperty({
    description: 'Phone number',
    example: '******-123-4567',
    required: false,
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string | null;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
    required: false,
  })
  @ValidateIf((_, value) => value !== undefined)
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'Website URL',
    example: 'https://premiumrealty.com',
    required: false,
  })
  @IsOptional()
  @IsString()
  website?: string | null;

  @ApiProperty({
    description: 'Logo URL',
    example: 'https://premiumrealty.com/logo.png',
    required: false,
  })
  @IsOptional()
  @IsString()
  logo?: string | null;
}
