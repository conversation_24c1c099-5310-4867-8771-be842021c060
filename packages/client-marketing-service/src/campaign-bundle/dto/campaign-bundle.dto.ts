import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsArray,
  IsNotEmpty,
  IsDate,
  IsEnum,
  IsNumber,
  Min,
  ValidateNested,
} from 'class-validator';

import { AddressInfoDto } from './address-info.dto';
import { AgentInfoDto } from './agent-info.dto';
import { BrokerageInfoDto } from './brokerage-info.dto';
import { MediaAssetDto } from './media-asset.dto';
import { PriceInfoDto } from './price-info.dto';
import { OpenHouseStatus } from '../enums/open-house-status.enum';

export class CampaignBundleDto {
  @ApiProperty({
    description: 'Unique identifier for the property',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  propertyId: string;

  @ApiProperty({
    description: 'Property description',
    example: 'Beautiful 3-bedroom home with stunning views',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Array of media assets for the property',
    type: [MediaAssetDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MediaAssetDto)
  mediaUrls: MediaAssetDto[];

  @ApiProperty({
    description: 'Number of bedrooms',
    example: 3,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  bedroomCount?: number | null;

  @ApiProperty({
    description: 'Number of bathrooms',
    example: 2,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  bathCount?: number | null;

  @ApiProperty({
    description: 'Agent information',
    type: AgentInfoDto,
  })
  @ValidateNested()
  @Type(() => AgentInfoDto)
  agent: AgentInfoDto;

  @ApiProperty({
    description: 'Brokerage information',
    type: BrokerageInfoDto,
  })
  @ValidateNested()
  @Type(() => BrokerageInfoDto)
  brokerage: BrokerageInfoDto;

  @ApiProperty({
    description: 'Date when the property was listed',
    example: '2025-05-19T10:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  dateListed: Date;

  @ApiProperty({
    description: 'Open house status',
    enum: OpenHouseStatus,
    example: OpenHouseStatus.SCHEDULED,
  })
  @IsEnum(OpenHouseStatus)
  openHouseStatus: OpenHouseStatus;

  @ApiProperty({
    description: 'Price information',
    type: PriceInfoDto,
  })
  @ValidateNested()
  @Type(() => PriceInfoDto)
  price: PriceInfoDto;

  @ApiProperty({
    description: 'Address information',
    type: AddressInfoDto,
  })
  @ValidateNested()
  @Type(() => AddressInfoDto)
  address: AddressInfoDto;

  @ApiProperty({
    description: 'Type of property',
    example: 'Single Family Home',
  })
  @IsString()
  @IsNotEmpty()
  propertyType: string;
}
