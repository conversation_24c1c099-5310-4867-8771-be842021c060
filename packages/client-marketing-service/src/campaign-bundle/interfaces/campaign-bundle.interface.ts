import { AddressInfoDto } from '../dto/address-info.dto';
import { AgentInfoDto } from '../dto/agent-info.dto';
import { BrokerageInfoDto } from '../dto/brokerage-info.dto';
import { MediaAssetDto } from '../dto/media-asset.dto';
import { PriceInfoDto } from '../dto/price-info.dto';
import { OpenHouseStatus } from '../enums/open-house-status.enum';

export interface CampaignBundle {
  propertyId: string;
  description?: string;
  mediaUrls: MediaAssetDto[];
  bedroomCount?: number;
  bathCount?: number;
  agent: AgentInfoDto;
  brokerage: BrokerageInfoDto;
  dateListed: Date;
  openHouseStatus: OpenHouseStatus;
  price: PriceInfoDto;
  address: AddressInfoDto;
  propertyType: string;
}
