import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Mutation,
} from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { AppPolicyRegistry } from 'src/auth.module';
import { AuthContext } from 'src/graphql.decorator';

import { CreateKeywordDto } from './dto/create-keyword.dto';
import { UpdateKeywordDto } from './dto/update-keyword.dto';
import { UpsertPageKeywordInput } from './dto/upsert-page-keyword.input';
import { Keyword } from './entities/keyword.entity';
import { KeywordService } from './keyword.service';

@Resolver(() => Keyword)
export class KeywordResolver {
  constructor(private readonly keywordService: KeywordService) {}

  @Query(() => [Keyword], { name: 'keywords', description: 'Get all keywords' })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<Keyword[]> {
    const canRead = await authContext.can('keyword', 'read', undefined);

    if (!canRead) throw new ForbiddenException();

    return this.keywordService.findAll() as unknown as Keyword[];
  }

  @Query(() => Keyword, { name: 'keyword', description: 'Get a keyword by ID' })
  async findOne(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Keyword> {
    const keyword = await this.keywordService.findOne(id);

    const canRead = await authContext.can('keyword', 'read', undefined);

    if (!canRead) throw new ForbiddenException();

    return keyword as unknown as Keyword;
  }

  @ResolveField(() => GraphQLJSONObject, { nullable: true })
  async metadata(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Parent() keyword: Keyword,
  ) {
    const canRead = await authContext.can('keyword', 'read', undefined);

    if (!canRead) throw new ForbiddenException();

    return keyword.metadata;
  }

  @Mutation(() => Keyword, {
    name: 'createKeyword',
    description: 'Create a new keyword',
  })
  async create(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args() input: CreateKeywordDto,
  ): Promise<Keyword> {
    const canCreate = await authContext.can('keyword', 'create', undefined);

    if (!canCreate) throw new ForbiddenException();

    return this.keywordService.create(input) as unknown as Keyword;
  }

  @Mutation(() => Keyword, {
    name: 'updateKeyword',
    description: 'Update an existing keyword',
  })
  async update(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
    @Args() input: UpdateKeywordDto,
  ): Promise<Keyword> {
    const canUpdate = await authContext.can('keyword', 'update', undefined);

    if (!canUpdate) throw new ForbiddenException();

    return this.keywordService.update(id, input) as unknown as Keyword;
  }

  @Mutation(() => Keyword, {
    name: 'upsertPageKeyword',
    description: 'Create or update a page keyword record',
  })
  async upsertPageKeyword(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args() input: UpsertPageKeywordInput,
  ): Promise<Keyword> {
    const canWrite = await authContext.can('keyword', 'upsert', undefined);

    if (!canWrite) throw new ForbiddenException();

    return this.keywordService.upsertPageKeyword(input) as unknown as Keyword;
  }
}
