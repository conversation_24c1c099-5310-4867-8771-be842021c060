import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class KeywordPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  private canPerformKeywordOperation = (companyId?: string) => {
    if (!companyId) {
      return this.auth.isSuper();
    }
    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };

  /**
   * All Keywords can be accessed by anyone
   * @returns boolean
   */
  read = () => {
    return true;
  };

  /**
   * Checks if the user has permission to create keywords
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  create = (companyId?: string) => {
    return this.canPerformKeywordOperation(companyId);
  };

  /**
   * Checks if the user has permission to update keywords
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  update = (companyId?: string) => {
    return this.canPerformKeywordOperation(companyId);
  };

  /**
   * Checks if the user has permission to upsert page keywords
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  upsert = (companyId?: string) => {
    return this.canPerformKeywordOperation(companyId);
  };
}
