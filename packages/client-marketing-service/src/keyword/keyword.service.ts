import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeleteResult } from 'typeorm';

import { CreateKeywordDto } from './dto/create-keyword.dto';
import { UpdateKeywordDto } from './dto/update-keyword.dto';
import { UpsertPageKeywordInput } from './dto/upsert-page-keyword.input';
import { Keyword } from './entities/keyword.entity';
import { PageKeyword } from '../page-keyword/entities/page-keyword.entity';

@Injectable()
export class KeywordService {
  constructor(
    @InjectRepository(Keyword)
    private readonly keywordRepository: Repository<Keyword>,
    @InjectRepository(PageKeyword)
    private readonly pageKeywordRepository: Repository<PageKeyword>,
  ) {}

  async create(createKeywordDto: CreateKeywordDto): Promise<Keyword> {
    return this.keywordRepository.save(createKeywordDto);
  }

  async findAll(): Promise<Keyword[]> {
    return this.keywordRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async findOne(id: string): Promise<Keyword> {
    const keyword = await this.keywordRepository.findOne({
      where: { id },
    });

    if (!keyword) {
      throw new NotFoundException(`Keyword with ID "${id}" not found`);
    }

    return keyword;
  }

  async update(
    id: string,
    updateKeywordDto: UpdateKeywordDto,
  ): Promise<Keyword> {
    const keyword = await this.findOne(id);

    const { metadata, ...otherProps } = updateKeywordDto;

    // Update non-metadata properties
    Object.assign(keyword, otherProps);

    // Merge metadata if provided instead of completely replacing it
    if (metadata) {
      keyword.metadata = {
        ...keyword.metadata,
        ...metadata,
      };
    }

    return this.keywordRepository.save(keyword);
  }

  async remove(id: string): Promise<DeleteResult> {
    return await this.keywordRepository.delete({ id });
  }

  async upsertPageKeyword(input: UpsertPageKeywordInput): Promise<Keyword> {
    // First, find the page by URL to get the scrapedPageId
    const pageKeyword = await this.pageKeywordRepository.findOne({
      where: { scrapedPageId: input.scrapedPageId },
    });

    if (!pageKeyword) {
      // If no pageKeyword exists, create both keyword and pageKeyword records
      const newKeyword = await this.keywordRepository.save({
        keyword: input.keyword,
      });

      await this.pageKeywordRepository.save({
        scrapedPageId: input.scrapedPageId,
        keywordId: newKeyword.id,
      });

      return newKeyword;
    }

    // If pageKeyword exists, find the associated keyword
    const existingKeyword = await this.keywordRepository.findOne({
      where: { id: pageKeyword.keywordId },
    });

    if (!existingKeyword) {
      // This is an edge case where pageKeyword exists but keyword doesn't
      const newKeyword = await this.keywordRepository.save({
        keyword: input.keyword,
      });

      await this.pageKeywordRepository.update(pageKeyword.id, {
        keywordId: newKeyword.id,
      });

      return newKeyword;
    }

    if (existingKeyword.keyword === input.keyword) {
      // No change needed if keyword is the same
      return existingKeyword;
    }

    // Update existing keyword with new value
    return await this.update(existingKeyword.id, {
      keyword: input.keyword,
    });
  }
}
