import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CreateKeywordDto } from './dto/create-keyword.dto';
import { UpdateKeywordDto } from './dto/update-keyword.dto';
import { KeywordController } from './keyword.controller';
import { KeywordService } from './keyword.service';

describe('KeywordController', () => {
  let controller: KeywordController;
  let keywordService: KeywordService;

  const mockKeywordService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [KeywordController],
      providers: [
        {
          provide: KeywordService,
          useValue: mockKeywordService,
        },
      ],
    }).compile();

    controller = module.get<KeywordController>(KeywordController);
    keywordService = module.get<KeywordService>(KeywordService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new keyword', async () => {
      const createKeywordDto: CreateKeywordDto = {
        keyword: 'seo optimization',
        metadata: { searchVolume: 1000 },
      };
      const createdKeyword = { id: '1', ...createKeywordDto };

      mockKeywordService.create.mockResolvedValue(createdKeyword);

      const response = await controller.create(createKeywordDto);

      expect(keywordService.create).toHaveBeenCalledWith(createKeywordDto);
      expect(response).toEqual(createdKeyword);
    });
  });

  describe('findAll', () => {
    it('should return all keywords', async () => {
      const keywords = [
        { id: '1', keyword: 'seo', metadata: { searchVolume: 1000 } },
        { id: '2', keyword: 'marketing', metadata: { searchVolume: 2000 } },
      ];

      mockKeywordService.findAll.mockResolvedValue(keywords);

      const response = await controller.findAll();

      expect(keywordService.findAll).toHaveBeenCalled();
      expect(response).toEqual(keywords);
    });
  });

  describe('findOne', () => {
    it('should return a single keyword', async () => {
      const keywordId = '1';
      const keyword = { id: keywordId, keyword: 'seo', metadata: {} };

      mockKeywordService.findOne.mockResolvedValue(keyword);

      const response = await controller.findOne(keywordId);

      expect(keywordService.findOne).toHaveBeenCalledWith(keywordId);
      expect(response).toEqual(keyword);
    });

    it('should throw NotFoundException when keyword not found', async () => {
      const keywordId = '1';

      mockKeywordService.findOne.mockRejectedValue(
        new NotFoundException('Keyword with ID "1" not found'),
      );

      await expect(controller.findOne(keywordId)).rejects.toThrow(
        NotFoundException,
      );
      expect(keywordService.findOne).toHaveBeenCalledWith(keywordId);
    });
  });

  describe('update', () => {
    it('should update a keyword', async () => {
      const keywordId = '1';
      const updateKeywordDto: UpdateKeywordDto = {
        keyword: 'updated seo optimization',
        metadata: { searchVolume: 2000 },
      };
      const updatedKeyword = {
        id: keywordId,
        ...updateKeywordDto,
      };

      mockKeywordService.update.mockResolvedValue(updatedKeyword);

      const response = await controller.update(keywordId, updateKeywordDto);

      expect(keywordService.update).toHaveBeenCalledWith(
        keywordId,
        updateKeywordDto,
      );
      expect(response).toEqual(updatedKeyword);
    });

    it('should throw NotFoundException when keyword not found', async () => {
      const keywordId = '1';
      const updateKeywordDto: UpdateKeywordDto = { keyword: 'updated keyword' };

      mockKeywordService.update.mockRejectedValue(
        new NotFoundException('Keyword with ID "1" not found'),
      );

      await expect(
        controller.update(keywordId, updateKeywordDto),
      ).rejects.toThrow(NotFoundException);
      expect(keywordService.update).toHaveBeenCalledWith(
        keywordId,
        updateKeywordDto,
      );
    });
  });

  describe('remove', () => {
    it('should remove a keyword', async () => {
      const keywordId = '1';
      const deleteResult = { affected: 1 };

      mockKeywordService.remove.mockResolvedValue(deleteResult);

      const response = await controller.remove(keywordId);

      expect(keywordService.remove).toHaveBeenCalledWith(keywordId);
      expect(response).toEqual(deleteResult);
    });

    it('should throw NotFoundException when keyword not found', async () => {
      const keywordId = '1';

      mockKeywordService.remove.mockRejectedValue(
        new NotFoundException('Keyword with ID "1" not found'),
      );

      await expect(controller.remove(keywordId)).rejects.toThrow(
        NotFoundException,
      );
      expect(keywordService.remove).toHaveBeenCalledWith(keywordId);
    });
  });
});
