import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AppPolicyRegistry } from 'src/auth.module';

import { Keyword } from './entities/keyword.entity';
import { KeywordResolver } from './keyword.resolver';
import { KeywordService } from './keyword.service';

describe('KeywordResolver', () => {
  let resolver: KeywordResolver;
  let service: KeywordService;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockKeywordService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    upsertPageKeyword: jest.fn(),
  };

  // Helper function to mock authorization
  const mockCanAccess = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    // Create mock auth context
    mockAuthContext = {
      can: jest.fn(),
      isSuper: jest.fn(),
      belongsToCompany: jest.fn(),
      getCompanyIds: jest.fn(),
      getUserId: jest.fn(),
    } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeywordResolver,
        {
          provide: KeywordService,
          useValue: mockKeywordService,
        },
      ],
    }).compile();

    resolver = module.get<KeywordResolver>(KeywordResolver);
    service = module.get<KeywordService>(KeywordService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of Keywords when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const mockKeywords = [
        { id: '1', keyword: 'seo', metadata: { searchVolume: 1000 } },
        { id: '2', keyword: 'marketing', metadata: { searchVolume: 2000 } },
      ];
      jest.spyOn(service, 'findAll').mockResolvedValue(mockKeywords as any);

      expect(await resolver.findAll(mockAuthContext)).toEqual(mockKeywords);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
      expect(service.findAll).toHaveBeenCalled();
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockCanAccess(false);

      await expect(resolver.findAll(mockAuthContext)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
      expect(service.findAll).not.toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single Keyword when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const mockKeyword = {
        id: '1',
        keyword: 'seo',
        metadata: { searchVolume: 1000 },
      };
      jest.spyOn(service, 'findOne').mockResolvedValue(mockKeyword as any);

      expect(await resolver.findOne(mockAuthContext, '1')).toEqual(mockKeyword);
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      const mockKeyword = {
        id: '1',
        keyword: 'seo',
        metadata: { searchVolume: 1000 },
      };
      jest.spyOn(service, 'findOne').mockResolvedValue(mockKeyword as any);
      // Setup auth mock to deny access
      mockCanAccess(false);

      await expect(resolver.findOne(mockAuthContext, '1')).rejects.toThrow(
        ForbiddenException,
      );
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
    });
  });

  describe('metadata', () => {
    it('should return metadata from the Keyword when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const mockMetadata = { searchVolume: 1000 };
      const keyword = { metadata: mockMetadata } as unknown as Keyword;

      expect(await resolver.metadata(mockAuthContext, keyword)).toEqual(
        mockMetadata,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockCanAccess(false);

      const mockMetadata = { searchVolume: 1000 };
      const keyword = { metadata: mockMetadata } as unknown as Keyword;

      await expect(resolver.metadata(mockAuthContext, keyword)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'read',
        undefined,
      );
    });
  });

  describe('create', () => {
    it('should create a new keyword when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const createInput = {
        keyword: 'seo',
        metadata: { searchVolume: 1000 },
      };
      const mockCreatedKeyword = {
        id: '1',
        ...createInput,
      };
      jest
        .spyOn(service, 'create')
        .mockResolvedValue(mockCreatedKeyword as any);

      expect(await resolver.create(mockAuthContext, createInput)).toEqual(
        mockCreatedKeyword,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'create',
        undefined,
      );
      expect(service.create).toHaveBeenCalledWith(createInput);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockCanAccess(false);

      const createInput = {
        keyword: 'seo',
        metadata: { searchVolume: 1000 },
      };

      await expect(
        resolver.create(mockAuthContext, createInput),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'create',
        undefined,
      );
      expect(service.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update an existing keyword when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const updateInput = {
        keyword: 'updated seo',
        metadata: { searchVolume: 2000 },
      };
      const mockUpdatedKeyword = {
        id: '1',
        ...updateInput,
      };
      jest
        .spyOn(service, 'update')
        .mockResolvedValue(mockUpdatedKeyword as any);

      expect(await resolver.update(mockAuthContext, '1', updateInput)).toEqual(
        mockUpdatedKeyword,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'update',
        undefined,
      );
      expect(service.update).toHaveBeenCalledWith('1', updateInput);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockCanAccess(false);

      const updateInput = {
        keyword: 'updated seo',
        metadata: { searchVolume: 2000 },
      };

      await expect(
        resolver.update(mockAuthContext, '1', updateInput),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'update',
        undefined,
      );
      expect(service.update).not.toHaveBeenCalled();
    });
  });

  describe('upsertPageKeyword', () => {
    it('should upsert a page keyword when authorized', async () => {
      // Setup auth mock to allow access
      mockCanAccess(true);

      const upsertInput = {
        scrapedPageId: 'page1',
        keyword: 'seo',
      };
      const mockUpsertedKeyword = {
        id: 'generated-id',
        keyword: 'seo',
      };
      jest
        .spyOn(service, 'upsertPageKeyword')
        .mockResolvedValue(mockUpsertedKeyword as any);

      expect(
        await resolver.upsertPageKeyword(mockAuthContext, upsertInput),
      ).toEqual(mockUpsertedKeyword);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'upsert',
        undefined,
      );
      expect(service.upsertPageKeyword).toHaveBeenCalledWith(upsertInput);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockCanAccess(false);

      const upsertInput = {
        scrapedPageId: 'page1',
        keyword: 'seo',
      };

      await expect(
        resolver.upsertPageKeyword(mockAuthContext, upsertInput),
      ).rejects.toThrow(ForbiddenException);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'keyword',
        'upsert',
        undefined,
      );
      expect(service.upsertPageKeyword).not.toHaveBeenCalled();
    });
  });
});
