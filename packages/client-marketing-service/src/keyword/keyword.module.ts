import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';

import { Keyword } from './entities/keyword.entity';
import { KeywordController } from './keyword.controller';
import { KeywordResolver } from './keyword.resolver';
import { KeywordService } from './keyword.service';

@Module({
  imports: [TypeOrmModule.forFeature([Keyword, PageKeyword])],
  controllers: [KeywordController],
  providers: [KeywordService, KeywordResolver],
  exports: [KeywordService],
})
export class KeywordModule {}
