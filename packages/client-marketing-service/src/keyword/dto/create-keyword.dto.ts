import { Field, ArgsType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';

@ArgsType()
export class CreateKeywordDto {
  @Field(() => String)
  @ApiProperty({
    description: 'The keyword text',
    example: 'luxury homes',
  })
  @IsString()
  @IsNotEmpty()
  keyword: string;

  @Field(() => GraphQLJSONObject, { nullable: true })
  @ApiProperty({
    description: 'Additional metadata for the keyword',
    example: { searchVolume: 1000, difficulty: 'medium' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;
}
