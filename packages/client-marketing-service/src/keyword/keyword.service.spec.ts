import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UpsertPageKeywordInput } from './dto/upsert-page-keyword.input';
import { Keyword } from './entities/keyword.entity';
import { KeywordService } from './keyword.service';
import { PageKeyword } from '../page-keyword/entities/page-keyword.entity';

describe('KeywordService', () => {
  let service: KeywordService;
  let keywordRepository: Repository<Keyword>;
  let pageKeywordRepository: Repository<PageKeyword>;

  const mockKeywordRepository = {
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  };

  const mockPageKeywordRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeywordService,
        {
          provide: getRepositoryToken(Keyword),
          useValue: mockKeywordRepository,
        },
        {
          provide: getRepositoryToken(PageKeyword),
          useValue: mockPageKeywordRepository,
        },
      ],
    }).compile();

    service = module.get<KeywordService>(KeywordService);
    keywordRepository = module.get<Repository<Keyword>>(
      getRepositoryToken(Keyword),
    );
    pageKeywordRepository = module.get<Repository<PageKeyword>>(
      getRepositoryToken(PageKeyword),
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new keyword', async () => {
      const createKeywordDto = {
        keyword: 'seo optimization',
        metadata: { searchVolume: 1000 },
      };
      const savedKeyword = { id: '1', ...createKeywordDto };

      mockKeywordRepository.save.mockResolvedValue(savedKeyword);

      const result = await service.create(createKeywordDto as any);

      expect(keywordRepository.save).toHaveBeenCalledWith(createKeywordDto);
      expect(result).toEqual(savedKeyword);
    });
  });

  describe('findAll', () => {
    it('should return all keywords ordered by creation date', async () => {
      const keywords = [
        { id: '1', keyword: 'seo', metadata: { searchVolume: 1000 } },
        { id: '2', keyword: 'marketing', metadata: { searchVolume: 2000 } },
      ];

      mockKeywordRepository.find.mockResolvedValue(keywords);

      const result = await service.findAll();

      expect(keywordRepository.find).toHaveBeenCalledWith({
        order: {
          createdAt: 'DESC',
        },
      });
      expect(result).toEqual(keywords);
    });
  });

  describe('findOne', () => {
    it('should return a keyword when found', async () => {
      const keywordId = '1';
      const keyword = { id: keywordId, keyword: 'seo', metadata: {} };

      mockKeywordRepository.findOne.mockResolvedValue(keyword);

      const result = await service.findOne(keywordId);

      expect(keywordRepository.findOne).toHaveBeenCalledWith({
        where: { id: keywordId },
      });
      expect(result).toEqual(keyword);
    });

    it('should throw NotFoundException when keyword not found', async () => {
      const keywordId = '1';

      mockKeywordRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(keywordId)).rejects.toThrow(
        'Keyword with ID "1" not found',
      );
      expect(keywordRepository.findOne).toHaveBeenCalledWith({
        where: { id: keywordId },
      });
    });
  });

  describe('update', () => {
    it('should update a keyword successfully', async () => {
      const keywordId = '1';
      const existingKeyword = {
        id: keywordId,
        keyword: 'old seo',
        metadata: { existing: 'data' },
      };
      const updateKeywordDto = {
        keyword: 'updated seo',
        metadata: { new: 'metadata' },
      };
      const updatedKeyword = {
        ...existingKeyword,
        keyword: updateKeywordDto.keyword,
        metadata: { existing: 'data', new: 'metadata' },
      };

      mockKeywordRepository.findOne.mockResolvedValue(existingKeyword);
      mockKeywordRepository.save.mockResolvedValue(updatedKeyword);

      const result = await service.update(keywordId, updateKeywordDto as any);

      expect(keywordRepository.findOne).toHaveBeenCalledWith({
        where: { id: keywordId },
      });
      expect(keywordRepository.save).toHaveBeenCalledWith({
        ...existingKeyword,
        keyword: updateKeywordDto.keyword,
        metadata: { existing: 'data', new: 'metadata' },
      });
      expect(result).toEqual(updatedKeyword);
    });

    it('should throw NotFoundException when keyword not found', async () => {
      const keywordId = '1';
      const updateKeywordDto = { keyword: 'updated seo' };

      mockKeywordRepository.findOne.mockResolvedValue(null);

      await expect(
        service.update(keywordId, updateKeywordDto as any),
      ).rejects.toThrow('Keyword with ID "1" not found');
    });

    it('should update without metadata when metadata not provided', async () => {
      const keywordId = '1';
      const existingKeyword = {
        id: keywordId,
        keyword: 'old seo',
        metadata: { existing: 'data' },
      };
      const updateKeywordDto = { keyword: 'updated seo' };
      const updatedKeyword = {
        ...existingKeyword,
        keyword: updateKeywordDto.keyword,
      };

      mockKeywordRepository.findOne.mockResolvedValue(existingKeyword);
      mockKeywordRepository.save.mockResolvedValue(updatedKeyword);

      const result = await service.update(keywordId, updateKeywordDto as any);

      expect(keywordRepository.save).toHaveBeenCalledWith({
        ...existingKeyword,
        keyword: updateKeywordDto.keyword,
      });
      expect(result).toEqual(updatedKeyword);
    });
  });

  describe('remove', () => {
    it('should delete a keyword successfully', async () => {
      const keywordId = '1';
      const deleteResult = { affected: 1 };

      mockKeywordRepository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove(keywordId);

      expect(keywordRepository.delete).toHaveBeenCalledWith({
        id: keywordId,
      });
      expect(result).toEqual(deleteResult);
    });
  });

  describe('upsertPageKeyword', () => {
    const input: UpsertPageKeywordInput = {
      scrapedPageId: 'page-123',
      keyword: 'test keyword',
    };

    it('should create new keyword and pageKeyword records when none exist', async () => {
      // Arrange
      mockPageKeywordRepository.findOne.mockResolvedValue(null);
      const newKeyword = { id: 'keyword-123', keyword: input.keyword };
      mockKeywordRepository.save.mockResolvedValue(newKeyword);
      mockPageKeywordRepository.save.mockResolvedValue({
        id: 'page-keyword-123',
      });

      // Act
      const result = await service.upsertPageKeyword(input);

      // Assert
      expect(mockPageKeywordRepository.findOne).toHaveBeenCalledWith({
        where: { scrapedPageId: input.scrapedPageId },
      });
      expect(mockKeywordRepository.save).toHaveBeenCalledWith({
        keyword: input.keyword,
      });
      expect(mockPageKeywordRepository.save).toHaveBeenCalledWith({
        scrapedPageId: input.scrapedPageId,
        keywordId: newKeyword.id,
      });
      expect(result).toEqual(newKeyword);
    });

    it('should create new keyword and update pageKeyword when pageKeyword exists but keyword does not', async () => {
      // Arrange
      const existingPageKeyword = {
        id: 'page-keyword-123',
        keywordId: 'old-keyword-123',
      };
      mockPageKeywordRepository.findOne.mockResolvedValue(existingPageKeyword);
      mockKeywordRepository.findOne.mockResolvedValue(null);
      const newKeyword = { id: 'keyword-123', keyword: input.keyword };
      mockKeywordRepository.save.mockResolvedValue(newKeyword);

      // Act
      const result = await service.upsertPageKeyword(input);

      // Assert
      expect(mockKeywordRepository.save).toHaveBeenCalledWith({
        keyword: input.keyword,
      });
      expect(mockPageKeywordRepository.update).toHaveBeenCalledWith(
        existingPageKeyword.id,
        { keywordId: newKeyword.id },
      );
      expect(result).toEqual(newKeyword);
    });

    it('should return existing keyword when keyword is the same', async () => {
      // Arrange
      const existingPageKeyword = {
        id: 'page-keyword-123',
        keywordId: 'keyword-123',
      };
      const existingKeyword = { id: 'keyword-123', keyword: input.keyword };
      mockPageKeywordRepository.findOne.mockResolvedValue(existingPageKeyword);
      mockKeywordRepository.findOne.mockResolvedValue(existingKeyword);

      // Act
      const result = await service.upsertPageKeyword(input);

      // Assert
      expect(mockKeywordRepository.save).not.toHaveBeenCalled();
      expect(mockPageKeywordRepository.save).not.toHaveBeenCalled();
      expect(mockPageKeywordRepository.update).not.toHaveBeenCalled();
      expect(result).toEqual(existingKeyword);
    });

    it('should update existing keyword when keyword is different', async () => {
      // Arrange
      const existingPageKeyword = {
        id: 'page-keyword-123',
        keywordId: 'keyword-123',
      };
      const existingKeyword = { id: 'keyword-123', keyword: 'old keyword' };
      const updatedKeyword = { id: 'keyword-123', keyword: input.keyword };
      mockPageKeywordRepository.findOne.mockResolvedValue(existingPageKeyword);
      mockKeywordRepository.findOne.mockResolvedValue(existingKeyword);
      mockKeywordRepository.save.mockResolvedValue(updatedKeyword);

      // Act
      const result = await service.upsertPageKeyword(input);

      // Assert
      expect(mockKeywordRepository.save).toHaveBeenCalledWith({
        ...existingKeyword,
        keyword: input.keyword,
      });
      expect(mockPageKeywordRepository.update).not.toHaveBeenCalled();
      expect(result).toEqual(updatedKeyword);
      expect(pageKeywordRepository.save).toBeDefined();
    });
  });
});
