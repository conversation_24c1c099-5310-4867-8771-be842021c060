import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsDate, IsObject } from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Factory } from 'nestjs-seeder';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Group } from 'src/group/entities/group.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { PageKeywordHistory } from 'src/page-keyword-history/entities/page-keyword-history.entity';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  JoinColumn,
} from 'typeorm';

@Entity('keyword')
@ObjectType()
export class Keyword {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column({ type: 'text', unique: true })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.keywordValue)
  @Field(() => String)
  keyword: string;

  @IsObject()
  @Column({ type: 'jsonb', default: '{}' })
  @Factory(SeederFactories.keywordMetadata)
  @Field(() => GraphQLJSONObject)
  metadata: Record<string, any>;

  @IsDate()
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @OneToMany(() => Group, group => group.keyword)
  @Field(() => [Group], { nullable: true })
  groups: Group[];

  @OneToMany(() => PageKeyword, pageKeyword => pageKeyword.keyword, {
    eager: true,
  })
  @JoinColumn({ name: 'keyword_id' })
  @Field(() => [PageKeyword], { nullable: true })
  pageKeywords: PageKeyword[];

  @OneToMany(
    () => PageKeywordHistory,
    pageKeywordHistory => pageKeywordHistory.keyword,
  )
  @Field(() => [PageKeywordHistory], { nullable: true })
  pageKeywordHistory: PageKeywordHistory[];
}
