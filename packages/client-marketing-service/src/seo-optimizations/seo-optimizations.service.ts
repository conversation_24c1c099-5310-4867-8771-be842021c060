import { Injectable } from '@nestjs/common';
import { Group } from 'src/group/entities/group.entity';
import { GroupService } from 'src/group/group.service';
import { RecommendationStatus } from 'src/recommendation/entities/recommendation.entity';
import { RecommendationService } from 'src/recommendation/recommendation.service';

import { SeoOptimizationsGroup } from './types/seo-optimizations-group';

@Injectable()
export class SeoOptimizationsService {
  constructor(
    private readonly groupService: GroupService,
    private readonly recommendationService: RecommendationService,
  ) {}

  private transformGroup(group: Group): SeoOptimizationsGroup {
    const {
      id,
      companyId,
      recommendations,
      groupScheduledActions,
      scrapedPage,
    } = group;
    // FIXME: "keyword" should not be typed as nullable
    const keyword = group.keyword?.keyword || '';
    // Use direct scrapedPage relationship instead of going through recommendations
    const pageName = scrapedPage?.pageName || '';
    const url = scrapedPage?.url || '';
    const mediaId = recommendations?.[0]?.scrape?.mediaId;
    // FIXME: "groupScheduledActions" should not be typed as a nullable array
    const groupScheduledAction = groupScheduledActions?.[0];
    const appliedAt =
      // TODO: remove optional chaining once the types are fixed
      groupScheduledAction?.scheduledAction?.publishedAt || null;
    const scheduledToBeAppliedAt =
      // TODO: remove fallback date once the types are fixed
      groupScheduledAction?.scheduledAction?.scheduledToBePublishedAt ||
      new Date();

    const rank = {
      original: group.keyword?.pageKeywords?.[0]?.originalRank,
      current: group.keyword?.pageKeywords?.[0]?.currentRank,
    };
    const optimizations =
      recommendations?.map(recommendation => {
        const {
          id,
          type,
          status,
          reasoning,
          currentValue,
          recommendationValue,
          rejectionReason,
        } = recommendation;

        return {
          id,
          type,
          status,
          reasoning,
          currentValue,
          recommendationValue,
          rejectionReason,
          appliedAt,
          scheduledToBeAppliedAt,
        };
      }) || [];

    return {
      id,
      companyId,
      keyword,
      pageName,
      url,
      scrapedPage,
      mediaId,
      rank,
      appliedAt,
      scheduledToBeAppliedAt,
      optimizations,
    };
  }

  async findSurfaced(companyId: string): Promise<SeoOptimizationsGroup[]> {
    try {
      const groups =
        await this.groupService.findSurfacedGroupsByCompany(companyId);
      const transformedGroups = groups.map(group => this.transformGroup(group));

      return transformedGroups;
    } catch (error) {
      throw new Error(`Failed to fetch SEO optimizations: ${error.message}`);
    }
  }

  async findOne(groupId: string): Promise<SeoOptimizationsGroup> {
    try {
      const group = await this.groupService.findOne(groupId);

      if (!group) {
        throw new Error(`Group with ID ${groupId} not found`);
      }

      return this.transformGroup(group);
    } catch (error) {
      throw new Error(
        `Failed to fetch SEO optimization for group ${groupId}: ${error.message}`,
      );
    }
  }

  async reject(id: string, reasoning: string): Promise<{ id: string }> {
    try {
      const recommendation = await this.recommendationService.update(id, {
        status: RecommendationStatus.REJECTED,
        rejectionReason: reasoning,
      });

      return { id: recommendation.id };
    } catch (error) {
      throw new Error(`Failed to reject SEO optimization: ${error.message}`);
    }
  }
}
