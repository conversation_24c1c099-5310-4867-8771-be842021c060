import { Field, ID, ObjectType } from '@nestjs/graphql';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';

import { Rank } from './rank';
import { SeoOptimization } from './seo-optimization';

@ObjectType()
export class SeoOptimizationsGroup {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  companyId: string;

  @Field(() => String)
  keyword: string;

  @Field(() => String, {
    deprecationReason: 'Use "scrapedPage.pageName" field instead',
  })
  pageName: string;

  @Field(() => String, {
    deprecationReason: 'Use "scrapedPage.url" field instead',
  })
  url: string;

  @Field(() => ScrapedPage)
  scrapedPage: ScrapedPage;

  @Field(() => String, { nullable: true })
  mediaId?: string;

  @Field(() => Rank, { nullable: true })
  rank?: Rank;

  // TODO: remove after page is live
  @Field(() => String, {
    nullable: true,
    deprecationReason: 'Use "rank" field instead',
  })
  ranking?: string;

  @Field(() => Date, { nullable: true })
  appliedAt: Date | null;

  @Field(() => Date)
  scheduledToBeAppliedAt: Date;

  @Field(() => [SeoOptimization])
  optimizations: SeoOptimization[];
}
