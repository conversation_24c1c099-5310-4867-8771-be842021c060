import { ObjectType, Field, ID } from '@nestjs/graphql';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { RecommendationStatus } from 'src/recommendation/entities/recommendation.entity';

@ObjectType()
export class SeoOptimization {
  @Field(() => ID)
  id: string;

  @Field(() => RecommendationType)
  type: RecommendationType;

  @Field(() => RecommendationStatus)
  status: RecommendationStatus;

  @Field(() => String)
  reasoning: string;

  @Field(() => String, { nullable: true })
  currentValue?: string;

  @Field(() => String)
  recommendationValue: string;

  @Field(() => String, { nullable: true })
  rejectionReason?: string | null;

  @Field(() => Date, { nullable: true })
  appliedAt: Date | null;

  @Field(() => Date)
  scheduledToBeAppliedAt: Date;
}
