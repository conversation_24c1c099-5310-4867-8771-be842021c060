import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AppPolicyRegistry } from 'src/auth.module';
import { RecommendationService } from 'src/recommendation/recommendation.service';

import { SeoOptimizationsResolver } from './seo-optimizations.resolver';
import { SeoOptimizationsService } from './seo-optimizations.service';

describe('SeoOptimizationsResolver', () => {
  const companyId = 'test-company-id';

  let resolver: SeoOptimizationsResolver;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockSeoOptimizationsService: SeoOptimizationsService = {
    findSurfaced: jest.fn(),
    findOne: jest.fn().mockResolvedValue({ companyId }),
    reject: jest.fn().mockResolvedValue({ id: 'id' }),
  } as unknown as SeoOptimizationsService;

  const mockRecommendationService: RecommendationService = {
    findOne: jest.fn().mockResolvedValue({ group: { companyId } }),
  } as unknown as RecommendationService;

  const mockCan = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    mockAuthContext = { can: jest.fn() } as unknown as jest.Mocked<
      UnifiedAuthContext<AppPolicyRegistry>
    >;

    const module = await Test.createTestingModule({
      providers: [
        SeoOptimizationsResolver,
        {
          provide: SeoOptimizationsService,
          useValue: mockSeoOptimizationsService,
        },
        { provide: RecommendationService, useValue: mockRecommendationService },
      ],
    }).compile();

    resolver = module.get<SeoOptimizationsResolver>(SeoOptimizationsResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('#seoOptimizationGroups', () => {
    it('should check permissions', async () => {
      mockCan(false);

      await expect(
        resolver.seoOptimizationGroups(mockAuthContext, companyId),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'seoOptimizations',
        'read',
        companyId,
      );
    });

    it('should fetch data from service', async () => {
      mockCan(true);

      await resolver.seoOptimizationGroups(mockAuthContext, companyId);

      expect(mockSeoOptimizationsService.findSurfaced).toHaveBeenCalledWith(
        companyId,
      );
    });
  });

  describe('#seoOptimizationGroup', () => {
    const groupId = 'test-group-id';

    it('should check permissions', async () => {
      mockCan(false);

      await expect(
        resolver.seoOptimizationGroup(mockAuthContext, groupId),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'seoOptimizations',
        'read',
        companyId,
      );
    });

    it('should fetch data from service', async () => {
      mockCan(true);

      await resolver.seoOptimizationGroup(mockAuthContext, groupId);

      expect(mockSeoOptimizationsService.findOne).toHaveBeenCalledWith(groupId);
    });
  });

  describe('#rejectSeoOptimization', () => {
    const id = 'test-optimization-id';
    const reasoning = 'Test reasoning';

    it('should check permissions', async () => {
      mockCan(false);

      await expect(
        resolver.rejectSeoOptimization(mockAuthContext, id, reasoning),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'seoOptimizations',
        'update',
        companyId,
      );
    });

    it('should reject optimization', async () => {
      mockCan(true);

      await resolver.rejectSeoOptimization(mockAuthContext, id, reasoning);

      expect(mockSeoOptimizationsService.reject).toHaveBeenCalledWith(
        id,
        reasoning,
      );
    });
  });
});
