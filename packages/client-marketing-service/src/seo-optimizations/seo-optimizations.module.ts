import { Module } from '@nestjs/common';
import { GroupModule } from 'src/group/group.module';
import { RecommendationModule } from 'src/recommendation/recommendation.module';

import { SeoOptimizationsResolver } from './seo-optimizations.resolver';
import { SeoOptimizationsService } from './seo-optimizations.service';

@Module({
  imports: [GroupModule, RecommendationModule],
  providers: [SeoOptimizationsResolver, SeoOptimizationsService],
})
export class SeoOptimizationsModule {}
