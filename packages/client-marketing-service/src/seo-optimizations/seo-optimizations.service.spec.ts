import { Test } from '@nestjs/testing';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { Group } from 'src/group/entities/group.entity';
import { GroupService } from 'src/group/group.service';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import {
  Recommendation,
  RecommendationStatus,
} from 'src/recommendation/entities/recommendation.entity';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import {
  ScrapedPage,
  ScrapedPageType,
} from 'src/scraped-page/entities/scraped-page.entity';

import { SeoOptimizationsService } from './seo-optimizations.service';
import { SeoOptimizationsGroup } from './types/seo-optimizations-group';

describe('SeoOptimizationsService', () => {
  const companyId = 'test-company-id';

  const mockOptimization = {
    id: 'rec-1',
    type: RecommendationType.MAIN_HEADING,
    status: RecommendationStatus.APPLIED,
    reasoning: 'Reasoning for rec-1',
    currentValue: 'Current value for rec-1',
    recommendationValue: 'Recommendation value for rec-1',
    rejectionReason: undefined,
    appliedAt: new Date('2025-10-01T00:00:00Z'),
    scheduledToBeAppliedAt: new Date('2025-10-01T00:00:00Z'),
  };

  const mockGroup: Partial<Group> = {
    id: 'group-1',
    companyId,
    keyword: {
      keyword: 'keyword-1',
      pageKeywords: [{ originalRank: 10, currentRank: 1 }],
    } as unknown as Keyword,
    scrapedPage: {
      pageName: 'pageName',
      pageType: ScrapedPageType.NEIGHBORHOOD_GUIDE,
      url: 'https://app.luxurycoders.com',
    } as unknown as ScrapedPage,
    recommendations: [
      {
        id: 'rec-1',
        type: RecommendationType.MAIN_HEADING,
        status: RecommendationStatus.APPLIED,
        reasoning: 'Reasoning for rec-1',
        currentValue: 'Current value for rec-1',
        recommendationValue: 'Recommendation value for rec-1',
        scrape: {
          mediaId: 'media-1',
          scrapedPage: {
            pageName: 'pageName',
            url: 'https://app.luxurycoders.com',
          } as unknown as ScrapedPage,
        },
      } as unknown as Recommendation,
    ],
    groupScheduledActions: [
      {
        scheduledAction: {
          publishedAt: new Date('2025-10-01T00:00:00Z'),
          scheduledToBePublishedAt: new Date('2025-10-01T00:00:00Z'),
        },
      } as unknown as GroupScheduledAction,
    ],
    metadata: { ranking: '5' },
  };

  let service: SeoOptimizationsService;

  const mockGroupService: GroupService = {
    findSurfacedGroupsByCompany: jest.fn().mockResolvedValue([mockGroup]),
    findOne: jest.fn().mockResolvedValue(mockGroup),
  } as unknown as GroupService;

  const mockRecommendationService: RecommendationService = {
    update: jest.fn().mockResolvedValue({}),
  } as unknown as RecommendationService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SeoOptimizationsService,
        { provide: GroupService, useValue: mockGroupService },
        { provide: RecommendationService, useValue: mockRecommendationService },
      ],
    }).compile();

    service = module.get<SeoOptimizationsService>(SeoOptimizationsService);
  });

  describe('#findSurfaced', () => {
    it('should fetch data from GroupService', async () => {
      await service.findSurfaced(companyId);

      expect(mockGroupService.findSurfacedGroupsByCompany).toHaveBeenCalledWith(
        companyId,
      );
    });

    it('should transform data', async () => {
      const expectedResult: SeoOptimizationsGroup[] = [
        {
          id: 'group-1',
          companyId,
          keyword: 'keyword-1',
          pageName: 'pageName',
          url: 'https://app.luxurycoders.com',
          scrapedPage: {
            pageName: 'pageName',
            pageType: ScrapedPageType.NEIGHBORHOOD_GUIDE,
            url: 'https://app.luxurycoders.com',
          } as unknown as ScrapedPage,
          mediaId: 'media-1',
          rank: {
            original: 10,
            current: 1,
          },
          appliedAt: new Date('2025-10-01T00:00:00Z'),
          scheduledToBeAppliedAt: new Date('2025-10-01T00:00:00Z'),
          optimizations: [mockOptimization],
        },
      ];

      const result = await service.findSurfaced(companyId);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('#reject', () => {
    it('should update recommendation using RecommendationService', async () => {
      const id = 'test-optimization-id';
      const reasoning = 'Test reasoning';

      await service.reject(id, reasoning);

      expect(mockRecommendationService.update).toHaveBeenCalledWith(id, {
        status: RecommendationStatus.REJECTED,
        rejectionReason: reasoning,
      });
    });
  });

  describe('#findOne', () => {
    const groupId = 'test-group-id';

    it('should fetch data from GroupService', async () => {
      await service.findOne(groupId);

      expect(mockGroupService.findOne).toHaveBeenCalledWith(groupId);
    });

    it('should transform data', async () => {
      const expectedResult: SeoOptimizationsGroup = {
        id: 'group-1',
        companyId,
        keyword: 'keyword-1',
        pageName: 'pageName',
        url: 'https://app.luxurycoders.com',
        scrapedPage: {
          pageName: 'pageName',
          pageType: ScrapedPageType.NEIGHBORHOOD_GUIDE,
          url: 'https://app.luxurycoders.com',
        } as unknown as ScrapedPage,
        mediaId: 'media-1',
        rank: {
          original: 10,
          current: 1,
        },
        appliedAt: new Date('2025-10-01T00:00:00Z'),
        scheduledToBeAppliedAt: new Date('2025-10-01T00:00:00Z'),
        optimizations: [mockOptimization],
      };

      const result = await service.findOne(groupId);

      expect(result).toEqual(expectedResult);
    });
  });
});
