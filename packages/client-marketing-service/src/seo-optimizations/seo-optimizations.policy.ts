import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class SeoOptimizationsPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  private canExecute = (companyId: string) => {
    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };

  read = (companyId: string) => {
    return this.canExecute(companyId);
  };

  update = (companyId: string) => {
    return this.canExecute(companyId);
  };
}
