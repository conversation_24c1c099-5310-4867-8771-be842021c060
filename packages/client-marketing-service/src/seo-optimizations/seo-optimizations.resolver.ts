import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { AuthContext } from 'src/graphql.decorator';
import { RecommendationService } from 'src/recommendation/recommendation.service';

import { SeoOptimizationsService } from './seo-optimizations.service';
import { SeoOptimizationsGroup } from './types/seo-optimizations-group';

@Resolver()
export class SeoOptimizationsResolver {
  constructor(
    private readonly seoOptimizationsService: SeoOptimizationsService,
    private readonly recommendationService: RecommendationService,
  ) {}

  @Query(() => [SeoOptimizationsGroup], {
    description: 'Fetches SEO optimizations.',
  })
  async seoOptimizationGroups(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID }) companyId: string,
  ) {
    const canRead = await authContext.can(
      'seoOptimizations',
      'read',
      companyId,
    );

    if (!canRead) throw new ForbiddenException();

    const groups = await this.seoOptimizationsService.findSurfaced(companyId);

    return groups;
  }

  @Query(() => SeoOptimizationsGroup, {
    description: 'Fetches specific SEO optimizations group.',
  })
  async seoOptimizationGroup(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('groupId', { type: () => ID }) groupId: string,
  ) {
    // TODO: add guards; throw if group id is not present
    const group = await this.seoOptimizationsService.findOne(groupId);

    if (!group.companyId) throw new ForbiddenException('Company ID not found');

    const canRead = await authContext.can(
      'seoOptimizations',
      'read',
      group.companyId,
    );

    if (!canRead) throw new ForbiddenException();

    return group;
  }

  @Mutation(() => ID)
  async rejectSeoOptimization(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
    @Args('reasoning', { type: () => String }) reasoning: string,
  ) {
    const companyId = (await this.recommendationService.findOne(id)).group
      ?.companyId;

    if (!companyId) throw new ForbiddenException('Company ID not found');

    const canUpdate = await authContext.can(
      'seoOptimizations',
      'update',
      companyId,
    );

    if (!canUpdate) throw new ForbiddenException();

    return (await this.seoOptimizationsService.reject(id, reasoning)).id;
  }
}
