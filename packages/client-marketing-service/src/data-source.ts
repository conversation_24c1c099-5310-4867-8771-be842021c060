import { URL } from 'url';

import * as dotenv from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';

// Redundant dotenv to allow .env for migrations
dotenv.config();

// Extract schema from connection string if present
let schemaFromUrl: string | undefined;
if (process.env.PG_CONNECTION_STRING) {
  const url = new URL(process.env.PG_CONNECTION_STRING);
  schemaFromUrl = url.searchParams.get('schema') ?? undefined;
  console.log('schemaFromUrl', schemaFromUrl);
}

// Define the data source options
const dataSource: DataSourceOptions = {
  type: 'postgres',
  ...(process.env.PG_CONNECTION_STRING
    ? {
        url: process.env.PG_CONNECTION_STRING,
        schema: schemaFromUrl,
      }
    : {
        host: process.env.DATABASE_HOST || 'localhost',
        port: parseInt(process.env.DATABASE_PORT || '5432', 10),
        username: process.env.DATABASE_USER || 'postgres',
        password: process.env.DATABASE_PASSWORD || '123123',
        database: process.env.DATABASE_NAME || 'lp_local',
        schema: process.env.DATABASE_SCHEMA || 'crew_ai_seo_automation',
      }),
  entities: [__dirname + '/**/*.entity.ts', __dirname + '/**/*.entity.js'],
  migrationsRun: false,
  logging: ['error', 'info', 'schema'],
  migrations: [
    __dirname + '/migration/**/*.ts',
    __dirname + '/migration/**/*.js',
  ],
  synchronize: false,
  extra: {
    options: `-c statement_timeout=${process.env.STATEMENT_TIMEOUT || '5s'}`,
  },
  uuidExtension: 'pgcrypto',
};

export const AppDataSource = new DataSource(dataSource);
