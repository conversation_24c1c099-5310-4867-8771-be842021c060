import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class M2MAuthService {
  private readonly m2mKeys: string[];

  constructor(private readonly configService: ConfigService) {
    this.m2mKeys = this.configService.get<string[]>('m2mSuperApiKeys') || [];
  }

  /**
   * Get M2M API key for service-to-service authentication
   * @returns The first available M2M API key or undefined
   */
  getM2MApiKey(): string | undefined {
    return this.m2mKeys[0];
  }

  /**
   * Get authentication headers for GraphQL requests to internal services
   * @returns Headers object with M2M authentication
   */
  getM2MHeaders(): Record<string, string> {
    const apiKey = this.getM2MApiKey();
    return apiKey ? { 'x-lp-api-key': apiKey } : {};
  }

  /**
   * Check if M2M authentication is available
   * @returns True if M2M keys are configured
   */
  hasM2MAuth(): boolean {
    return this.m2mKeys.length > 0;
  }
}
