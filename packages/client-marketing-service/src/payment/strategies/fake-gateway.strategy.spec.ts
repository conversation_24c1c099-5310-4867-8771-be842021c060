import { Test, TestingModule } from '@nestjs/testing';

import { FakeGateway } from './fake-gateway.strategy';
import { PaymentRequest } from '../interfaces/payment-adapter.interface';

describe('FakeGateway', () => {
  let gateway: FakeGateway;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FakeGateway],
    }).compile();

    gateway = module.get<FakeGateway>(FakeGateway);
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });

  describe('processPayment', () => {
    const baseRequest: PaymentRequest = {
      cardNumber: '****************',
      expiryMonth: '12',
      expiryYear: '2025',
      cvc: '123',
      amountCents: 1000,
      currency: 'USD',
    };

    it('should process successful payment with Visa test card', async () => {
      const result = await gateway.processPayment(baseRequest);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.transactionId).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/,
      );
      expect(result.error).toBeUndefined();
    });

    it('should process successful payment with Visa debit test card', async () => {
      const request = { ...baseRequest, cardNumber: '****************' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should handle card number with spaces', async () => {
      const request = { ...baseRequest, cardNumber: '4242 4242 4242 4242' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
    });

    it('should handle declined card', async () => {
      const request = { ...baseRequest, cardNumber: '****************' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Your card was declined.');
      expect(result.transactionId).toBeUndefined();
    });

    it('should handle insufficient funds', async () => {
      const request = { ...baseRequest, cardNumber: '****************' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Your card has insufficient funds.');
      expect(result.transactionId).toBeUndefined();
    });

    it('should handle processing error', async () => {
      const request = { ...baseRequest, cardNumber: '****************' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(false);
      expect(result.error).toBe('A processing error occurred.');
      expect(result.transactionId).toBeUndefined();
    });

    it('should handle invalid card number', async () => {
      const request = { ...baseRequest, cardNumber: '1234567890123456' };
      const result = await gateway.processPayment(request);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid card number.');
      expect(result.transactionId).toBeUndefined();
    });
  });
});
