import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

import {
  PaymentAdapter,
  PaymentRequest,
  PaymentResponse,
} from '../interfaces/payment-adapter.interface';

@Injectable()
export class FakeGateway implements PaymentAdapter {
  processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    const { cardNumber } = request;
    const normalizedCardNumber = cardNumber.replace(/\s/g, '');

    switch (normalizedCardNumber) {
      case '****************':
      case '****************':
        return Promise.resolve({
          success: true,
          transactionId: uuidv4(),
        });

      case '****************':
        return Promise.resolve({
          success: false,
          error: 'Your card was declined.',
        });

      case '****************':
        return Promise.resolve({
          success: false,
          error: 'Your card has insufficient funds.',
        });

      case '****************':
        return Promise.resolve({
          success: false,
          error: 'A processing error occurred.',
        });

      default:
        return Promise.resolve({
          success: false,
          error: 'Invalid card number.',
        });
    }
  }
}
