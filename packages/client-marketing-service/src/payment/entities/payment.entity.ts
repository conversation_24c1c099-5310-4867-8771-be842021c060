import { Field, ID, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  JoinColumn,
  Unique,
} from 'typeorm';

import { Campaign } from '../../campaign/entities/campaign.entity';

export enum PaymentProvider {
  TEST = 'test',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  SUCCEEDED = 'SUCCEEDED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

registerEnumType(PaymentProvider, { name: 'PaymentProvider' });
registerEnumType(PaymentStatus, { name: 'PaymentStatus' });

@Entity('payment')
@ObjectType()
@Index('idx_payment_campaign_attempt_created', [
  'campaignId',
  'attemptNumber',
  'createdAt',
])
@Index('idx_payment_parent_created', ['parentPaymentId', 'createdAt'])
@Index(
  'idx_payment_parent_attempt_latest',
  ['parentPaymentId', 'attemptNumber'],
  {
    where: '"parent_payment_id" IS NOT NULL',
  },
)
@Unique('uq_payment_provider_ref', ['provider', 'providerRef'])
@Unique('uq_payment_parent_attempt', ['parentPaymentId', 'attemptNumber'])
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column({ type: 'enum', enum: PaymentProvider })
  @Field(() => PaymentProvider)
  provider: PaymentProvider;

  @Column({ type: 'text', name: 'provider_ref', nullable: true })
  @Field(() => String, { nullable: true })
  providerRef: string | null;

  @Column({ type: 'enum', enum: PaymentStatus })
  @Field(() => PaymentStatus)
  status: PaymentStatus;

  @Column({ type: 'integer', name: 'amount_cents' })
  @Field(() => Int)
  amountCents: number;

  @Column({ type: 'varchar', length: 3 })
  @Field(() => String)
  currency: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @Column({ type: 'integer', name: 'attempt_number', default: 1 })
  @Field(() => Int)
  attemptNumber: number;

  @Column({ type: 'uuid', name: 'parent_payment_id', nullable: true })
  @Field(() => ID, { nullable: true })
  parentPaymentId: string | null;

  @Column({ type: 'uuid', name: 'campaign_id', nullable: false })
  @Index()
  @Field(() => ID)
  campaignId: string;

  @ManyToOne(() => Payment, payment => payment.childPayments, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'parent_payment_id' })
  @Field(() => Payment, { nullable: true })
  parentPayment?: Payment;

  @OneToMany(() => Payment, payment => payment.parentPayment)
  @Field(() => [Payment])
  childPayments?: Payment[];

  @ManyToOne(() => Campaign, campaign => campaign.payments, {
    nullable: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'campaign_id' })
  @Field(() => Campaign)
  campaign?: Campaign;
}
