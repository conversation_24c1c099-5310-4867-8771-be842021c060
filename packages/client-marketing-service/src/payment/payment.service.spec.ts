import { NotFoundException, BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';

import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from './entities/payment.entity';
import { PaymentService } from './payment.service';
import { PaymentQueryRepository } from './repositories/payment-query.repository';

describe('PaymentService', () => {
  let service: PaymentService;
  let paymentRepository: jest.Mocked<Repository<Payment>>;
  let mockDataSource: any;

  const mockPayment: Payment = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    provider: PaymentProvider.TEST,
    providerRef: 'txn_123',
    status: PaymentStatus.SUCCEEDED,
    amountCents: 1000,
    currency: 'USD',
    createdAt: new Date(),
    attemptNumber: 1,
    parentPaymentId: null,
    campaignId: 'test-campaign-id',
  };

  beforeEach(async () => {
    const mockPaymentRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      find: jest.fn(),
      createQueryBuilder: jest.fn(),
    };

    const mockPaymentAdapter = {
      processPayment: jest.fn(),
    };

    mockDataSource = {
      transaction: jest.fn(async callback => {
        // Create a mock EntityManager for the transaction
        const mockManager = {
          save: jest.fn().mockImplementation((entity, data) => {
            // For Payment entities
            if (entity === Payment || entity?.name === 'Payment') {
              return mockPaymentRepository.save(data);
            }
            return data;
          }),
          create: jest.fn().mockImplementation((_entity, data) => {
            return data;
          }),
          findOne: jest.fn().mockImplementation((entity, options) => {
            if (entity === Payment || entity?.name === 'Payment') {
              return mockPaymentRepository.findOne(options);
            }
            return null;
          }),
          createQueryBuilder: jest.fn().mockImplementation(() => {
            const qb = mockPaymentRepository.createQueryBuilder();
            if (!qb) {
              return {
                innerJoin: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                setLock: jest.fn().mockReturnThis(),
                getOne: jest.fn().mockResolvedValue(null),
              };
            }
            return qb;
          }),
          getRepository: jest.fn().mockImplementation((entity: any) => {
            if (entity === Payment || entity?.name === 'Payment') {
              return mockPaymentRepository as unknown as Repository<Payment>;
            }
            // Minimal repo-like fallback
            return {
              createQueryBuilder: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnThis(),
                andWhere: jest.fn().mockReturnThis(),
                orderBy: jest.fn().mockReturnThis(),
                addOrderBy: jest.fn().mockReturnThis(),
                getOne: jest.fn().mockResolvedValue(null),
                getMany: jest.fn().mockResolvedValue([]),
              }),
              find: jest.fn().mockResolvedValue([]),
              findOne: jest.fn().mockResolvedValue(null),
              save: jest.fn().mockResolvedValue(undefined),
            } as Partial<Repository<any>>;
          }),
        };
        // Execute the callback with the mock manager and ensure async behavior
        return await callback(mockManager as any);
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        PaymentQueryRepository,
        {
          provide: getRepositoryToken(Payment),
          useValue: mockPaymentRepository,
        },
        {
          provide: 'PaymentAdapter',
          useValue: mockPaymentAdapter,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    paymentRepository = module.get(getRepositoryToken(Payment));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOne', () => {
    it('should return a payment', async () => {
      paymentRepository.findOne.mockResolvedValue(mockPayment);

      const result = await service.findOne('123');

      expect(paymentRepository.findOne).toHaveBeenCalledWith({
        where: { id: '123' },
        relations: ['campaign'],
      });
      expect(result).toEqual(mockPayment);
    });

    it('should throw NotFoundException when payment not found', async () => {
      paymentRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('123')).rejects.toThrow(NotFoundException);
      await expect(service.findOne('123')).rejects.toThrow(
        'Payment with ID "123" not found',
      );
    });
  });

  describe('createPaymentAttempt', () => {
    it('should create initial payment attempt when no parent provided', async () => {
      const newPayment = {
        ...mockPayment,
        attemptNumber: 1,
        parentPaymentId: null,
      };

      paymentRepository.save.mockResolvedValue(newPayment);

      const queryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.PENDING,
      });

      expect(result.attemptNumber).toBe(1);
      expect(result.parentPaymentId).toBeNull();
    });

    it('should create retry payment with incremented attempt number', async () => {
      const parentPayment = {
        ...mockPayment,
        attemptNumber: 2,
        campaignId: 'campaign-123',
      };
      const retryPayment = {
        ...mockPayment,
        id: 'retry-payment-id',
        attemptNumber: 3,
        parentPaymentId: parentPayment.id,
        status: PaymentStatus.SUCCEEDED,
      };

      paymentRepository.findOne.mockResolvedValue(parentPayment);
      paymentRepository.create.mockReturnValue(retryPayment);
      paymentRepository.save.mockResolvedValue(retryPayment);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.SUCCEEDED,
        parentPaymentId: parentPayment.id,
      });

      expect(result.attemptNumber).toBe(3);
      expect(result.parentPaymentId).toBe(parentPayment.id);
    });

    it('should throw NotFoundException for invalid parent payment ID', async () => {
      paymentRepository.findOne.mockResolvedValue(null);

      await expect(
        service.createPaymentAttempt({
          campaignId: 'campaign-123',
          amountCents: 1000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          status: PaymentStatus.PENDING,
          parentPaymentId: 'invalid-id',
        }),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.createPaymentAttempt({
          campaignId: 'campaign-123',
          amountCents: 1000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          status: PaymentStatus.PENDING,
          parentPaymentId: 'invalid-id',
        }),
      ).rejects.toThrow('Parent payment with ID "invalid-id" not found');
    });

    it('should throw BadRequestException when parent payment does not belong to campaign', async () => {
      const parentPayment = {
        ...mockPayment,
        campaignId: 'different-campaign',
      };

      paymentRepository.findOne.mockResolvedValue(parentPayment);

      await expect(
        service.createPaymentAttempt({
          campaignId: 'campaign-123',
          amountCents: 1000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          status: PaymentStatus.PENDING,
          parentPaymentId: parentPayment.id,
        }),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.createPaymentAttempt({
          campaignId: 'campaign-123',
          amountCents: 1000,
          currency: 'USD',
          provider: PaymentProvider.TEST,
          status: PaymentStatus.PENDING,
          parentPaymentId: parentPayment.id,
        }),
      ).rejects.toThrow(
        `Parent payment ${parentPayment.id} does not belong to campaign campaign-123`,
      );
    });

    it('should use latest payment from campaign when no parent specified', async () => {
      const latestPayment = {
        ...mockPayment,
        attemptNumber: 3,
      };
      const newPayment = {
        ...mockPayment,
        id: 'new-payment-id',
        attemptNumber: 4,
        parentPaymentId: latestPayment.id,
      };

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(latestPayment),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);
      paymentRepository.create.mockReturnValue(newPayment);
      paymentRepository.save.mockResolvedValue(newPayment);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.PENDING,
      });

      expect(queryBuilder.where).toHaveBeenCalledWith(
        'payment.campaignId = :campaignId',
        { campaignId: 'campaign-123' },
      );
      expect(queryBuilder.orderBy).toHaveBeenCalledWith(
        'payment.attemptNumber',
        'DESC',
      );
      expect(result.attemptNumber).toBe(4);
      expect(result.parentPaymentId).toBe(latestPayment.id);
    });

    it('should handle provider reference in payment attempt', async () => {
      const newPayment = {
        ...mockPayment,
        providerRef: 'test_ref_123',
        attemptNumber: 1,
        parentPaymentId: null,
      };

      paymentRepository.create.mockReturnValue(newPayment);
      paymentRepository.save.mockResolvedValue(newPayment);

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.SUCCEEDED,
        providerRef: 'test_ref_123',
      });

      expect(result.providerRef).toBe('test_ref_123');
    });
  });

  describe('findAllPaymentsForCampaign', () => {
    it('should return all payments for campaign ordered by attempt number', async () => {
      const payments = [
        { ...mockPayment, attemptNumber: 1 },
        { ...mockPayment, attemptNumber: 2 },
        { ...mockPayment, attemptNumber: 3 },
      ];

      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(payments),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);

      const result = await service.findAllPaymentsForCampaign('campaign-123');
      expect(queryBuilder.where).toHaveBeenCalledWith(
        'payment.campaignId = :campaignId',
        { campaignId: 'campaign-123' },
      );
      expect(queryBuilder.orderBy).toHaveBeenCalledWith(
        'payment.attemptNumber',
        'ASC',
      );
      expect(queryBuilder.addOrderBy).toHaveBeenCalledWith(
        'payment.createdAt',
        'ASC',
      );
      expect(result).toEqual(payments);
    });

    it('should return empty array when no payments found', async () => {
      const queryBuilder = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([]),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);

      const result = await service.findAllPaymentsForCampaign('campaign-123');

      expect(result).toEqual([]);
    });
  });

  describe('findByProvider', () => {
    it('should return payments by provider', async () => {
      paymentRepository.find.mockResolvedValue([mockPayment]);

      const result = await service.findByProvider(PaymentProvider.TEST);

      expect(paymentRepository.find).toHaveBeenCalledWith({
        where: { provider: PaymentProvider.TEST },
        relations: ['campaign'],
      });
      expect(result).toEqual([mockPayment]);
    });

    it('should return empty array when no payments found', async () => {
      paymentRepository.find.mockResolvedValue([]);

      const result = await service.findByProvider(PaymentProvider.TEST);

      expect(result).toEqual([]);
    });
  });

  describe('findByStatus', () => {
    it('should return payments by status', async () => {
      paymentRepository.find.mockResolvedValue([mockPayment]);

      const result = await service.findByStatus(PaymentStatus.SUCCEEDED);

      expect(paymentRepository.find).toHaveBeenCalledWith({
        where: { status: PaymentStatus.SUCCEEDED },
        relations: ['campaign'],
      });
      expect(result).toEqual([mockPayment]);
    });

    it('should return empty array when no payments found', async () => {
      paymentRepository.find.mockResolvedValue([]);

      const result = await service.findByStatus(PaymentStatus.FAILED);

      expect(result).toEqual([]);
    });
  });

  describe('findAll', () => {
    it('should return all payments with campaign relationships', async () => {
      const payments = [mockPayment, { ...mockPayment, id: 'payment-2' }];
      paymentRepository.find.mockResolvedValue(payments);

      const result = await service.findAll();

      expect(paymentRepository.find).toHaveBeenCalledWith({
        relations: ['campaign'],
      });
      expect(result).toEqual(payments);
    });

    it('should return empty array when no payments exist', async () => {
      paymentRepository.find.mockResolvedValue([]);

      const result = await service.findAll();

      expect(result).toEqual([]);
    });
  });

  describe('edge cases and error scenarios', () => {
    it('should handle null parentPaymentId correctly', async () => {
      const payment = {
        ...mockPayment,
        parentPaymentId: null,
        attemptNumber: 1,
      };

      paymentRepository.create.mockReturnValue(payment);
      paymentRepository.save.mockResolvedValue(payment);

      const queryBuilder = {
        innerJoin: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        setLock: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };
      paymentRepository.createQueryBuilder.mockReturnValue(queryBuilder as any);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.PENDING,
      });

      expect(result.parentPaymentId).toBeNull();
      expect(result.attemptNumber).toBe(1);
    });

    it('should handle different currency codes in payment attempts', async () => {
      const eurPayment = {
        ...mockPayment,
        currency: 'EUR',
        attemptNumber: 1,
        parentPaymentId: null,
      };

      const mockManager = {
        findOne: jest.fn().mockResolvedValue(null),
        save: jest.fn().mockResolvedValue(eurPayment),
        createQueryBuilder: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnThis(),
          orderBy: jest.fn().mockReturnThis(),
          addOrderBy: jest.fn().mockReturnThis(),
          setLock: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValue(null),
        }),
      };

      (mockDataSource.transaction as jest.Mock).mockImplementation(
        async callback => {
          return await callback(mockManager);
        },
      );

      const result = await service.createPaymentAttempt({
        campaignId: 'test-campaign-id',
        provider: PaymentProvider.TEST,
        amountCents: 2000,
        currency: 'EUR',
        status: PaymentStatus.PENDING,
      });

      expect(result.currency).toBe('EUR');
    });

    it('should handle large attempt numbers correctly', async () => {
      const parentPayment = {
        ...mockPayment,
        attemptNumber: 99,
        campaignId: 'campaign-123',
      };
      const retryPayment = {
        ...mockPayment,
        attemptNumber: 100,
        parentPaymentId: parentPayment.id,
      };

      paymentRepository.findOne.mockResolvedValue(parentPayment);
      paymentRepository.create.mockReturnValue(retryPayment);
      paymentRepository.save.mockResolvedValue(retryPayment);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.PENDING,
        parentPaymentId: parentPayment.id,
      });

      expect(result.attemptNumber).toBe(100);
    });

    it('should maintain payment chain integrity', async () => {
      const payment2 = {
        ...mockPayment,
        id: 'p2',
        attemptNumber: 2,
        parentPaymentId: 'p1',
      };
      const payment3 = {
        ...mockPayment,
        id: 'p3',
        attemptNumber: 3,
        parentPaymentId: 'p2',
      };

      paymentRepository.findOne.mockResolvedValue({
        ...payment2,
        campaignId: 'campaign-123',
      });
      paymentRepository.create.mockReturnValue(payment3);
      paymentRepository.save.mockResolvedValue(payment3);

      const result = await service.createPaymentAttempt({
        campaignId: 'campaign-123',
        amountCents: 1000,
        currency: 'USD',
        provider: PaymentProvider.TEST,
        status: PaymentStatus.PENDING,
        parentPaymentId: 'p2',
      });

      expect(result.parentPaymentId).toBe('p2');
      expect(result.attemptNumber).toBe(3);
    });
  });
});
