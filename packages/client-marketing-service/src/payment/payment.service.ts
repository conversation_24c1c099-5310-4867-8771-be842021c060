import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';

import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from './entities/payment.entity';
import { PaymentQueryRepository } from './repositories/payment-query.repository';

export interface CreatePaymentAttemptDto {
  campaignId: string;
  amountCents: number;
  currency: string;
  provider: PaymentProvider;
  providerRef?: string | null;
  status: PaymentStatus;
  parentPaymentId?: string;
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
    private readonly paymentQueryRepository: PaymentQueryRepository,
    private readonly dataSource: DataSource,
  ) {}

  async findOne(id: string): Promise<Payment> {
    const payment = await this.paymentQueryRepository.findById(id);

    if (!payment) {
      throw new NotFoundException(`Payment with ID "${id}" not found`);
    }

    return payment;
  }

  /**
   * Not yet used, for now, we are only using the TEST payment provider.
   * We only ever create one payment via the CampaignService.createWithPayment() method.
   * There is no current need to create additional payments for retries or refunds, etc.
   */

  async createPaymentAttempt(dto: CreatePaymentAttemptDto): Promise<Payment> {
    this.logger.log('Creating payment attempt', {
      campaignId: dto.campaignId,
      status: dto.status,
      attemptNumber: dto.parentPaymentId ? 'retry' : 'initial',
    });

    // Use a transaction to prevent race conditions
    return await this.dataSource.transaction(async manager => {
      // Validate and get base payment within the transaction
      const base = await this.getBasePaymentForAttemptInTransaction(
        dto,
        manager,
      );
      const attemptNumber = base ? base.attemptNumber + 1 : 1;
      const parentPaymentId = base ? base.id : null;

      // Create new payment attempt
      const payment = this.createPaymentAttemptEntity({
        provider: dto.provider,
        amountCents: dto.amountCents,
        currency: dto.currency,
        status: dto.status,
        providerRef: dto.providerRef,
        parentPaymentId: parentPaymentId || undefined,
        attemptNumber,
        campaignId: dto.campaignId,
      });

      const savedPayment = await manager.save(Payment, payment);
      return savedPayment;
    });
  }

  /**
   * Get the base payment for an attempt in a transaction.
   * If there is a parent payment, return the parent payment.
   * If there is no parent payment, return the latest payment for the campaign with pessimistic lock.
   */

  private async getBasePaymentForAttemptInTransaction(
    dto: CreatePaymentAttemptDto,
    manager: EntityManager,
  ): Promise<Payment | null> {
    if (dto.parentPaymentId) {
      const parentPayment = await manager.findOne(Payment, {
        where: { id: dto.parentPaymentId },
        lock: { mode: 'pessimistic_write' }, // Lock for update to prevent concurrent modifications
      });

      // Validate parent payment exists
      if (!parentPayment) {
        throw new NotFoundException(
          `Parent payment with ID "${dto.parentPaymentId}" not found`,
        );
      }

      // Validate parent payment belongs to the same campaign
      if (parentPayment.campaignId !== dto.campaignId) {
        throw new BadRequestException(
          `Parent payment ${dto.parentPaymentId} does not belong to campaign ${dto.campaignId}`,
        );
      }

      return parentPayment;
    }

    // If no parent payment, find latest payment for campaign with pessimistic lock
    // Return the latest payment for the campaign
    const result = await manager
      .createQueryBuilder(Payment, 'payment')
      .where('payment.campaignId = :campaignId', {
        campaignId: dto.campaignId,
      })
      .orderBy('payment.attemptNumber', 'DESC')
      .addOrderBy('payment.createdAt', 'DESC')
      .setLock('pessimistic_write')
      .getOne();

    return result || null;
  }

  async findAllPaymentsForCampaign(campaignId: string): Promise<Payment[]> {
    return this.paymentQueryRepository.findAllForCampaign(campaignId);
  }

  async findAll(): Promise<Payment[]> {
    return this.paymentQueryRepository.findAll();
  }

  create(): never {
    // Disallowed by design: payments must be created via CampaignService.createWithPayment()
    throw new BadRequestException(
      'Direct payment creation is not allowed. Payments must be created as part of a campaign using the campaign service createWithPayment() method.',
    );
  }

  async findByProvider(provider: PaymentProvider): Promise<Payment[]> {
    return this.paymentQueryRepository.findByProvider(provider);
  }

  async findByStatus(status: PaymentStatus): Promise<Payment[]> {
    return this.paymentQueryRepository.findByStatus(status);
  }

  async createInitialPayment(params: {
    provider: PaymentProvider;
    amountCents: number;
    currency: string;
    campaignId: string;
  }): Promise<Payment> {
    const payment = this.paymentRepository.create({
      provider: params.provider,
      amountCents: params.amountCents,
      currency: params.currency,
      status: PaymentStatus.PENDING,
      attemptNumber: 1,
      parentPaymentId: null,
      campaignId: params.campaignId,
    });
    return await this.paymentRepository.save(payment);
  }

  createPaymentAttemptEntity(params: {
    provider: PaymentProvider;
    amountCents: number;
    currency: string;
    status: PaymentStatus;
    providerRef?: string | null;
    parentPaymentId?: string;
    attemptNumber: number;
    campaignId: string;
  }): Payment {
    if (params.attemptNumber < 1) {
      throw new Error('attemptNumber must be >= 1');
    }
    return this.paymentRepository.create({
      provider: params.provider,
      amountCents: params.amountCents,
      currency: params.currency,
      status: params.status,
      providerRef: params.providerRef,
      parentPaymentId: params.parentPaymentId,
      attemptNumber: params.attemptNumber,
      campaignId: params.campaignId,
    });
  }

  async createSuccessPayment(
    basePayment: Payment,
    transactionId: string | null,
  ): Promise<Payment> {
    const payment = this.createPaymentAttemptEntity({
      provider: basePayment.provider,
      amountCents: basePayment.amountCents,
      currency: basePayment.currency,
      status: PaymentStatus.SUCCEEDED,
      providerRef: transactionId ?? null,
      parentPaymentId: basePayment.id,
      attemptNumber: basePayment.attemptNumber + 1,
      campaignId: basePayment.campaignId,
    });
    return await this.paymentRepository.save(payment);
  }

  async createFailedPayment(basePayment: Payment): Promise<Payment> {
    const payment = this.createPaymentAttemptEntity({
      provider: basePayment.provider,
      amountCents: basePayment.amountCents,
      currency: basePayment.currency,
      status: PaymentStatus.FAILED,
      providerRef: null,
      parentPaymentId: basePayment.id,
      attemptNumber: basePayment.attemptNumber + 1,
      campaignId: basePayment.campaignId,
    });
    return await this.paymentRepository.save(payment);
  }
}
