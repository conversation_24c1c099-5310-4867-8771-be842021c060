import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Payment } from './entities/payment.entity';
import { PaymentResolver } from './payment.resolver';
import { PaymentService } from './payment.service';
import { PaymentQueryRepository } from './repositories/payment-query.repository';
import { FakeGateway } from './strategies/fake-gateway.strategy';

@Module({
  imports: [TypeOrmModule.forFeature([Payment])],
  providers: [
    PaymentService,
    PaymentResolver,
    PaymentQueryRepository,
    {
      provide: 'PaymentAdapter',
      useClass: FakeGateway,
    },
  ],
  exports: [PaymentService, 'PaymentAdapter'],
})
export class PaymentModule {}
