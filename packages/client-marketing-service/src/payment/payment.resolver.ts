import { BadRequestException } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';

import { Payment } from './entities/payment.entity';
import { PaymentService } from './payment.service';

@Resolver(() => Payment)
export class PaymentResolver {
  constructor(private readonly paymentService: PaymentService) {}

  @Query(() => [Payment], {
    description: 'Fetches all payments.',
  })
  payments() {
    return this.paymentService.findAll();
  }

  @Query(() => Payment, {
    description: 'Fetches a payment by ID.',
  })
  payment(@Args('id', { type: () => ID }) id: string) {
    return this.paymentService.findOne(id);
  }

  @Mutation(() => Payment, {
    name: 'createPayment',
    description:
      'Create a new payment (disabled - payments must be created with campaigns).',
    deprecationReason:
      'Disabled. Create payments via createCampaign(input.payment).',
  })
  createPayment(): never {
    throw new BadRequestException(
      'Disabled. Create payments via createCampaign(input.payment).',
    );
  }
}
