import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsString, Min } from 'class-validator';

import { PaymentProvider, PaymentStatus } from '../entities/payment.entity';

@InputType()
export class CreatePaymentDto {
  @Field(() => PaymentProvider)
  @IsEnum(PaymentProvider)
  provider: PaymentProvider;

  @Field(() => String, { nullable: true })
  @IsString()
  providerRef?: string;

  @Field(() => PaymentStatus)
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @Field(() => Int)
  @IsInt()
  @Min(0)
  amountCents: number;

  @Field(() => String)
  @IsString()
  currency: string;
}
