import { PaymentStatus } from '../entities/payment.entity';

export interface PaymentRequest {
  readonly cardNumber: string;
  readonly expiryMonth: string;
  readonly expiryYear: string;
  readonly cvc: string;
  readonly amountCents: number;
  readonly currency: string;
  readonly idempotencyKey?: string;
}

export interface PaymentResult {
  success: boolean;
  status: PaymentStatus;
  transactionId?: string;
  errorMessage?: string;
}

export interface PaymentProcessor {
  processPayment(request: PaymentRequest): Promise<PaymentResult>;
}
