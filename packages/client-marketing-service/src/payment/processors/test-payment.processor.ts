import { Injectable, Logger } from '@nestjs/common';

import {
  PaymentProcessor,
  PaymentRequest,
  PaymentResult,
} from './payment-processor.interface';
import { PaymentStatus } from '../entities/payment.entity';
import { PaymentAdapter } from '../interfaces/payment-adapter.interface';

@Injectable()
export class TestPaymentProcessor implements PaymentProcessor {
  private readonly logger = new Logger(TestPaymentProcessor.name);

  constructor(private readonly paymentAdapter: PaymentAdapter) {}

  async processPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      const response = await this.paymentAdapter.processPayment(request);

      return {
        success: response.success,
        status: response.success
          ? PaymentStatus.SUCCEEDED
          : PaymentStatus.FAILED,
        transactionId: response.success ? response.transactionId : undefined,
        errorMessage: !response.success ? 'Payment failed' : undefined,
      };
    } catch (error) {
      this.logger.error('Payment processing failed', error);
      return {
        success: false,
        status: PaymentStatus.FAILED,
        errorMessage: error.message || 'Payment processing error',
      };
    }
  }
}
