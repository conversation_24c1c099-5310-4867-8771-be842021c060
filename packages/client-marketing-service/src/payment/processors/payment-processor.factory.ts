import { Injectable, Inject, Logger } from '@nestjs/common';

import { PaymentProcessor } from './payment-processor.interface';
import { TestPaymentProcessor } from './test-payment.processor';
import { PaymentProvider } from '../entities/payment.entity';
import { PaymentAdapter } from '../interfaces/payment-adapter.interface';

@Injectable()
export class PaymentProcessorFactory {
  private readonly logger = new Logger(PaymentProcessorFactory.name);

  constructor(
    @Inject('PaymentAdapter')
    private readonly paymentAdapter: PaymentAdapter,
  ) {}

  getProcessor(provider: PaymentProvider): PaymentProcessor {
    if (provider === PaymentProvider.TEST) {
      return new TestPaymentProcessor(this.paymentAdapter);
    }

    // Log for any non-test provider attempts (should not happen with current enum)
    this.logger.warn(
      `Non-test payment provider requested but not implemented: ${String(provider)}. Using test processor as fallback.`,
    );
    return new TestPaymentProcessor(this.paymentAdapter);
  }
}
