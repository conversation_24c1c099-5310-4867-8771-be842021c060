import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';

import { PaymentQueryRepository } from './payment-query.repository';
import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from '../entities/payment.entity';

describe('PaymentQueryRepository', () => {
  let repo: PaymentQueryRepository;
  let paymentRepository: jest.Mocked<Repository<Payment>>;

  const basePayment: Payment = {
    id: 'pay-1',
    provider: PaymentProvider.TEST,
    providerRef: null,
    status: PaymentStatus.PENDING,
    amountCents: 1000,
    currency: 'USD',
    createdAt: new Date(),
    attemptNumber: 1,
    parentPaymentId: null,
    campaignId: 'camp-1',
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    const mockRepo = {
      findOne: jest.fn(),
      find: jest.fn(),
      createQueryBuilder: jest.fn(),
    } as unknown as jest.Mocked<Repository<Payment>>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentQueryRepository,
        { provide: getRepositoryToken(Payment), useValue: mockRepo },
      ],
    }).compile();

    repo = module.get(PaymentQueryRepository);
    paymentRepository = module.get(getRepositoryToken(Payment));
  });

  it('findById returns payment with campaign relation', async () => {
    paymentRepository.findOne.mockResolvedValue(basePayment);
    const result = await repo.findById('pay-1');
    expect(paymentRepository.findOne).toHaveBeenCalledWith({
      where: { id: 'pay-1' },
      relations: ['campaign'],
    });
    expect(result).toEqual(basePayment);
  });

  it('findAllForCampaign queries by campaignId and orders asc', async () => {
    const payments = [basePayment];
    const qb = {
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue(payments),
    } as Partial<SelectQueryBuilder<Payment>>;
    paymentRepository.createQueryBuilder.mockReturnValue(
      qb as unknown as SelectQueryBuilder<Payment>,
    );

    const result = await repo.findAllForCampaign('camp-1');
    expect(qb.where).toHaveBeenCalledWith('payment.campaignId = :campaignId', {
      campaignId: 'camp-1',
    });
    expect(qb.orderBy).toHaveBeenCalledWith('payment.attemptNumber', 'ASC');
    expect(qb.addOrderBy).toHaveBeenCalledWith('payment.createdAt', 'ASC');
    expect(result).toEqual(payments);
  });

  it('findByProvider returns with campaign relation', async () => {
    paymentRepository.find.mockResolvedValue([basePayment]);
    const result = await repo.findByProvider(PaymentProvider.TEST);
    expect(paymentRepository.find).toHaveBeenCalledWith({
      where: { provider: PaymentProvider.TEST },
      relations: ['campaign'],
    });
    expect(result).toEqual([basePayment]);
  });

  it('findByStatus returns with campaign relation', async () => {
    paymentRepository.find.mockResolvedValue([basePayment]);
    const result = await repo.findByStatus(PaymentStatus.PENDING);
    expect(paymentRepository.find).toHaveBeenCalledWith({
      where: { status: PaymentStatus.PENDING },
      relations: ['campaign'],
    });
    expect(result).toEqual([basePayment]);
  });

  it('findAll returns with campaign relation', async () => {
    paymentRepository.find.mockResolvedValue([basePayment]);
    const result = await repo.findAll();
    expect(paymentRepository.find).toHaveBeenCalledWith({
      relations: ['campaign'],
    });
    expect(result).toEqual([basePayment]);
  });

  it('findPaymentChain returns ordered payments for the same campaign', async () => {
    // findById should return a payment with a campaignId
    paymentRepository.findOne.mockResolvedValue(basePayment);

    const chain = [
      { ...basePayment, id: 'p1', attemptNumber: 1 },
      { ...basePayment, id: 'p2', attemptNumber: 2 },
      { ...basePayment, id: 'p3', attemptNumber: 3 },
    ];

    const qb = {
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue(chain),
    } as Partial<SelectQueryBuilder<Payment>>;
    paymentRepository.createQueryBuilder.mockReturnValue(
      qb as unknown as SelectQueryBuilder<Payment>,
    );

    const result = await repo.findPaymentChain('p1');
    expect(result).toHaveLength(3);
    expect(result[0].attemptNumber).toBe(1);
    expect(result[2].attemptNumber).toBe(3);
  });
});
