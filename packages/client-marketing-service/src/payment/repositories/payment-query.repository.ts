import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';

import {
  Payment,
  PaymentProvider,
  PaymentStatus,
} from '../entities/payment.entity';

@Injectable()
export class PaymentQueryRepository {
  constructor(
    @InjectRepository(Payment)
    private readonly paymentRepository: Repository<Payment>,
  ) {}

  private getRepo(manager?: EntityManager): Repository<Payment> {
    return manager ? manager.getRepository(Payment) : this.paymentRepository;
  }

  async findAllForCampaign(
    campaignId: string,
    manager?: EntityManager,
  ): Promise<Payment[]> {
    return this.getRepo(manager)
      .createQueryBuilder('payment')
      .where('payment.campaignId = :campaignId', { campaignId })
      .orderBy('payment.attemptNumber', 'ASC')
      .addOrderBy('payment.createdAt', 'ASC')
      .getMany();
  }

  async findByProvider(
    provider: PaymentProvider,
    manager?: EntityManager,
  ): Promise<Payment[]> {
    return this.getRepo(manager).find({
      where: { provider },
      relations: ['campaign'],
    });
  }

  async findByStatus(
    status: PaymentStatus,
    manager?: EntityManager,
  ): Promise<Payment[]> {
    return this.getRepo(manager).find({
      where: { status },
      relations: ['campaign'],
    });
  }

  async findById(id: string, manager?: EntityManager): Promise<Payment | null> {
    return this.getRepo(manager).findOne({
      where: { id },
      relations: ['campaign'],
    });
  }

  async findAll(manager?: EntityManager): Promise<Payment[]> {
    return this.getRepo(manager).find({
      relations: ['campaign'],
    });
  }

  async findPaymentChain(
    paymentId: string,
    manager?: EntityManager,
  ): Promise<Payment[]> {
    // Find the payment to get its campaign
    const payment = await this.findById(paymentId, manager);
    if (!payment || !payment.campaignId) {
      return [];
    }

    // Get all payments for this campaign, ordered by attempt number and creation date
    return this.getRepo(manager)
      .createQueryBuilder('payment')
      .where('payment.campaignId = :campaignId', {
        campaignId: payment.campaignId,
      })
      .orderBy('payment.attemptNumber', 'ASC')
      .addOrderBy('payment.createdAt', 'ASC')
      .getMany();
  }
}
