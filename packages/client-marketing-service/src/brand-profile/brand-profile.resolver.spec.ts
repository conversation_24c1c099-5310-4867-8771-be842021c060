import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AppPolicyRegistry } from 'src/auth.module';

import { BrandProfileResolver } from './brand-profile.resolver';
import { BrandProfileService } from './brand-profile.service';
import { UpsertBrandProfileDto } from './dto/upsert-brand-profile.dto';

describe('BrandProfileResolver', () => {
  const companyId = 'test-company-id';
  const userId = 'user-1';

  let resolver: BrandProfileResolver;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockBrandProfileService: BrandProfileService = {
    findOne: jest.fn(),
    upsert: jest.fn(),
  } as unknown as BrandProfileService;

  const mockCan = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    mockAuthContext = {
      can: jest.fn(),
      isM2M: jest.fn().mockReturnValue(false),
      getUserId: jest.fn().mockReturnValue(userId),
    } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

    const module = await Test.createTestingModule({
      providers: [
        BrandProfileResolver,
        { provide: BrandProfileService, useValue: mockBrandProfileService },
      ],
    }).compile();

    resolver = module.get<BrandProfileResolver>(BrandProfileResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('queries', () => {
    describe('brandProfile', () => {
      it('should check permissions', async () => {
        mockCan(false);

        await expect(
          resolver.brandProfile(mockAuthContext, companyId),
        ).rejects.toThrow(ForbiddenException);

        expect(mockAuthContext.can).toHaveBeenCalledWith(
          'brandProfile',
          'read',
          companyId,
        );
      });

      it('should fetch data from service', async () => {
        mockCan(true);

        await resolver.brandProfile(mockAuthContext, companyId);

        expect(mockBrandProfileService.findOne).toHaveBeenCalledWith(companyId);
      });
    });
  });

  describe('mutations', () => {
    describe('upsertBrandProfile', () => {
      const input = { companyId } as UpsertBrandProfileDto;

      it('should check permissions', async () => {
        mockCan(false);

        await expect(
          resolver.upsertBrandProfile(mockAuthContext, input),
        ).rejects.toThrow(ForbiddenException);

        expect(mockAuthContext.can).toHaveBeenCalledWith(
          'brandProfile',
          'upsert',
          companyId,
        );
      });

      describe('when triggered by a machine', () => {
        it('should call service method with the "AI generated"', async () => {
          mockCan(true);

          mockAuthContext.isM2M.mockReturnValue(true);

          await resolver.upsertBrandProfile(mockAuthContext, input);

          expect(mockBrandProfileService.upsert).toHaveBeenCalledWith(
            input,
            'AI generated',
          );
        });
      });

      describe('when triggered by a human', () => {
        it("should call service method with the user's id", async () => {
          mockCan(true);

          await resolver.upsertBrandProfile(mockAuthContext, input);

          expect(mockBrandProfileService.upsert).toHaveBeenCalledWith(
            input,
            userId,
          );
        });
      });

      it('should return the upserted brand profile', async () => {
        const id = 'profile-id';

        mockCan(true);

        (mockBrandProfileService.upsert as jest.Mock).mockResolvedValue({
          ...input,
          id,
        });

        const result = await resolver.upsertBrandProfile(
          mockAuthContext,
          input,
        );

        expect(mockBrandProfileService.upsert).toHaveBeenCalledWith(
          input,
          userId,
        );
        expect(result.id).toBe(id);
      });
    });
  });
});
