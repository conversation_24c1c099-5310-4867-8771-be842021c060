import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BrandProfileResolver } from './brand-profile.resolver';
import { BrandProfileService } from './brand-profile.service';
import { BrandProfileHistory } from './entities/brand-profile-history.entity';
import { BrandProfile } from './entities/brand-profile.entity';

@Module({
  imports: [TypeOrmModule.forFeature([BrandProfile, BrandProfileHistory])],
  providers: [BrandProfileService, BrandProfileResolver],
})
export class BrandProfileModule {}
