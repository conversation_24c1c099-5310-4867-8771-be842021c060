import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { AuthContext } from 'src/graphql.decorator';

import { BrandProfileService } from './brand-profile.service';
import { UpsertBrandProfileDto } from './dto/upsert-brand-profile.dto';
import { BrandProfile } from './entities/brand-profile.entity';

@Resolver()
export class BrandProfileResolver {
  constructor(private readonly brandProfileService: BrandProfileService) {}

  @Query(() => BrandProfile, {
    description: "Fetches the company's brand profile.",
  })
  async brandProfile(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID }) companyId: string,
  ) {
    const canRead = await authContext.can('brandProfile', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    const data = this.brandProfileService.findOne(companyId);

    return data;
  }

  @Mutation(() => BrandProfile, {
    name: 'upsertBrandProfile',
    description: "Create or update a company's brand profile.",
  })
  async upsertBrandProfile(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('input') input: UpsertBrandProfileDto,
  ) {
    const { companyId } = input;

    const canUpsert = await authContext.can(
      'brandProfile',
      'upsert',
      companyId,
    );

    if (!canUpsert) throw new ForbiddenException();

    const updatedBy = authContext.isM2M()
      ? 'AI generated'
      : authContext.getUserId() || '';

    const newBrandProfile = await this.brandProfileService.upsert(
      input,
      updatedBy,
    );

    return newBrandProfile;
  }
}
