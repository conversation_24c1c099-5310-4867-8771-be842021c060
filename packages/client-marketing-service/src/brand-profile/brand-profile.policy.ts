import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

export const ADMIN_ROLE_ID = '8998af93-443c-49e3-9237-634583a947af';

@Injectable()
export class BrandProfilePolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  private canExecute = (companyId: string) => {
    const isCompanyAdmin =
      this.auth.belongsToCompany(companyId)?.roleId === ADMIN_ROLE_ID;

    return this.auth.isSuper() || isCompanyAdmin;
  };

  read = (companyId: string) => {
    return this.canExecute(companyId);
  };

  upsert = (companyId: string) => {
    return this.canExecute(companyId);
  };
}
