import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { AppPolicyRegistry } from 'src/auth.module';

import { ADMIN_ROLE_ID, BrandProfilePolicy } from './brand-profile.policy';

describe('BrandProfilePolicy', () => {
  const companyId = 'company-id';
  const mockAuthContext = {
    isSuper: jest.fn(),
    belongsToCompany: jest.fn(),
  } as unknown as UnifiedAuthContext<AppPolicyRegistry>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('read', () => {
    describe('should not allow when', () => {
      it('the user is not a super user', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue(null);

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.read(companyId)).toBe(false);
      });

      it('the user does not have the Admin role', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue({
          roleId: 'guest-role-id',
        });

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.read(companyId)).toBe(false);
      });
    });

    describe('should allow when', () => {
      it('the user is a super user', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(true);

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.read(companyId)).toBe(true);
      });

      it('the user does have the Admin role', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue({
          roleId: ADMIN_ROLE_ID,
        });

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.read(companyId)).toBe(true);
      });
    });
  });

  describe('upsert', () => {
    describe('should not allow when', () => {
      it('the user is not a super user', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue(null);

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.upsert(companyId)).toBe(false);
      });

      it('the user does not have the Admin role', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue({
          roleId: 'guest-role-id',
        });

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.upsert(companyId)).toBe(false);
      });
    });

    describe('should allow when', () => {
      it('the user is a super user', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(true);

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.upsert(companyId)).toBe(true);
      });

      it('the user does have the Admin role', () => {
        (mockAuthContext.isSuper as jest.Mock).mockReturnValue(false);
        (mockAuthContext.belongsToCompany as jest.Mock).mockReturnValue({
          roleId: ADMIN_ROLE_ID,
        });

        const policy = new BrandProfilePolicy(mockAuthContext);

        expect(policy.upsert(companyId)).toBe(true);
      });
    });
  });
});
