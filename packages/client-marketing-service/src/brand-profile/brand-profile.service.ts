import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UpsertBrandProfileDto } from './dto/upsert-brand-profile.dto';
import { BrandProfileHistory } from './entities/brand-profile-history.entity';
import { BrandProfile } from './entities/brand-profile.entity';

@Injectable()
export class BrandProfileService {
  constructor(
    @InjectRepository(BrandProfile)
    private readonly brandProfileRepository: Repository<BrandProfile>,
    @InjectRepository(BrandProfileHistory)
    private readonly brandProfileHistoryRepository: Repository<BrandProfileHistory>,
  ) {}

  async findOne(companyId: string): Promise<BrandProfile> {
    const brandProfile = await this.brandProfileRepository.findOne({
      where: { companyId },
    });

    if (!brandProfile) {
      throw new NotFoundException(
        'Brand profile not found for the given company ID',
      );
    }

    return brandProfile;
  }

  async upsert(
    brandProfile: UpsertBrandProfileDto,
    updatedBy: string,
  ): Promise<BrandProfile> {
    const companyId = brandProfile.companyId;

    await this.brandProfileRepository.upsert(
      { ...brandProfile, updatedBy } as BrandProfile,
      ['companyId'],
    );

    const newBrandProfile = await this.brandProfileRepository.findOne({
      where: { companyId },
    });

    if (!newBrandProfile) {
      throw new NotFoundException(
        'Could not upsert brand profile for the given company ID.',
      );
    }

    await this.brandProfileHistoryRepository.save({
      ...newBrandProfile,
      id: undefined,
    });

    return newBrandProfile;
  }
}
