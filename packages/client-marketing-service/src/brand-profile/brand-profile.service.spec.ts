import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';

import { BrandProfileService } from './brand-profile.service';
import { UpsertBrandProfileDto } from './dto/upsert-brand-profile.dto';
import { BrandProfileHistory } from './entities/brand-profile-history.entity';
import { BrandProfile } from './entities/brand-profile.entity';

describe('BrandProfileService', () => {
  let service: BrandProfileService;
  let brandProfileRepository: jest.Mocked<Repository<BrandProfile>>;
  let brandProfileHistoryRepository: jest.Mocked<
    Repository<BrandProfileHistory>
  >;

  const mockBrandProfile: BrandProfile = {
    id: '1',
    companyId: 'company-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    aboutTheBrand: 'Sample Brand Description',
    strategicFocus: 'Our primary focus is on...',
    valueProposition: 'We offer unique value to our customers with...',
    idealCustomerProfiles: 'Ideal customer profiles include...',
    missionAndCoreValues: 'Our mission is to...',
    brandPointOfView: 'We believe that...',
    toneOfVoice: 'Our tone of voice is professional and friendly.',
    ctaText: 'Discover more about our services!',
    authorPersona: 'Written by a seasoned marketing professional with...',
  };

  const mockBrandProfileRepository = {
    findOne: jest.fn(),
    upsert: jest.fn(),
  };

  const mockBrandProfileHistoryRepository = {
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrandProfileService,
        {
          provide: getRepositoryToken(BrandProfile),
          useValue: mockBrandProfileRepository,
        },
        {
          provide: getRepositoryToken(BrandProfileHistory),
          useValue: mockBrandProfileHistoryRepository,
        },
      ],
    }).compile();

    service = module.get<BrandProfileService>(BrandProfileService);
    brandProfileRepository = module.get<Repository<BrandProfile>>(
      getRepositoryToken(BrandProfile),
    ) as jest.Mocked<Repository<BrandProfile>>;
    brandProfileHistoryRepository = module.get<Repository<BrandProfileHistory>>(
      getRepositoryToken(BrandProfileHistory),
    ) as jest.Mocked<Repository<BrandProfileHistory>>;

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findOne', () => {
    it('should throw NotFoundException when brand profile not found', async () => {
      brandProfileRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('not-found')).rejects.toThrow(
        new NotFoundException(
          'Brand profile not found for the given company ID',
        ),
      );
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      brandProfileRepository.findOne.mockRejectedValue(error);

      await expect(service.findOne('company-id')).rejects.toThrow(
        'Database error',
      );
    });

    it('should return a brand profile when found', async () => {
      brandProfileRepository.findOne.mockResolvedValue(mockBrandProfile);

      const result = await service.findOne('company-id');

      expect(brandProfileRepository.findOne).toHaveBeenCalledWith({
        where: { companyId: 'company-id' },
      });
      expect(result).toEqual(mockBrandProfile);
    });
  });

  describe('upsert', () => {
    const companyId = 'company-1';
    const brandProfile = { companyId } as unknown as UpsertBrandProfileDto;
    const updatedBy = 'user-1';

    it('should handle repository errors', async () => {
      brandProfileRepository.upsert.mockRejectedValue(new Error());

      await expect(service.upsert(brandProfile, updatedBy)).rejects.toThrow();
    });

    it('should upsert the provided brand profile and save history', async () => {
      const upsertedBrandProfile = {
        ...mockBrandProfile,
        companyId,
        updatedBy,
      };
      brandProfileRepository.findOne.mockResolvedValue(upsertedBrandProfile);
      brandProfileRepository.upsert.mockResolvedValue({
        identifiers: [
          {
            companyId,
          },
        ],
      } as unknown as InsertResult);
      brandProfileHistoryRepository.save.mockResolvedValue(
        {} as BrandProfileHistory,
      );

      const result = await service.upsert(brandProfile, updatedBy);

      expect(mockBrandProfileRepository.upsert).toHaveBeenCalledWith(
        {
          ...brandProfile,
          updatedBy,
        },
        ['companyId'],
      );
      expect(brandProfileRepository.findOne).toHaveBeenCalledWith({
        where: { companyId },
      });
      expect(brandProfileHistoryRepository.save).toHaveBeenCalledWith({
        ...upsertedBrandProfile,
        id: undefined,
      });
      expect(result).toEqual(upsertedBrandProfile);
    });

    it('should throw NotFoundException when brand profile not found after upsert', async () => {
      brandProfileRepository.findOne.mockResolvedValue(null);
      brandProfileRepository.upsert.mockResolvedValue({
        identifiers: [{ companyId }],
      } as unknown as InsertResult);

      await expect(service.upsert(brandProfile, updatedBy)).rejects.toThrow(
        new NotFoundException(
          'Could not upsert brand profile for the given company ID.',
        ),
      );

      expect(brandProfileHistoryRepository.save).not.toHaveBeenCalled();
    });
  });
});
