import { Field, ID, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@ObjectType()
@Entity('brand_profile')
export class BrandProfile {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Index({ unique: true })
  @Column({ name: 'company_id', type: 'uuid', nullable: false })
  @Field(() => String)
  companyId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @Column({ name: 'updated_by', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  updatedBy?: string;

  @Column({ name: 'about_the_brand', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  aboutTheBrand?: string;

  @Column({ name: 'strategic_focus', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  strategicFocus?: string;

  @Column({ name: 'value_proposition', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  valueProposition?: string;

  @Column({ name: 'ideal_customer_profiles', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  idealCustomerProfiles?: string;

  @Column({ name: 'mission_and_core_values', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  missionAndCoreValues?: string;

  @Column({ name: 'brand_point_of_view', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  brandPointOfView?: string;

  @Column({ name: 'tone_of_voice', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  toneOfVoice?: string;

  @Column({ name: 'cta_text', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  ctaText?: string;

  @Column({ name: 'author_persona', type: 'text', nullable: true })
  @Field(() => String, { nullable: true })
  authorPersona?: string;
}
