import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('brand_profile_history')
export class BrandProfileHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ name: 'company_id', type: 'uuid', nullable: false })
  companyId: string;

  @Column({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Column({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'updated_by', type: 'text', nullable: true })
  updatedBy?: string;

  @Column({ name: 'about_the_brand', type: 'text', nullable: true })
  aboutTheBrand?: string;

  @Column({ name: 'strategic_focus', type: 'text', nullable: true })
  strategicFocus?: string;

  @Column({ name: 'value_proposition', type: 'text', nullable: true })
  valueProposition?: string;

  @Column({ name: 'ideal_customer_profiles', type: 'text', nullable: true })
  idealCustomerProfiles?: string;

  @Column({ name: 'mission_and_core_values', type: 'text', nullable: true })
  missionAndCoreValues?: string;

  @Column({ name: 'brand_point_of_view', type: 'text', nullable: true })
  brandPointOfView?: string;

  @Column({ name: 'tone_of_voice', type: 'text', nullable: true })
  toneOfVoice?: string;

  @Column({ name: 'cta_text', type: 'text', nullable: true })
  ctaText?: string;

  @Column({ name: 'author_persona', type: 'text', nullable: true })
  authorPersona?: string;
}
