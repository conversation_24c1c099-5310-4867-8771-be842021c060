import { Field, InputType } from '@nestjs/graphql';

@InputType()
export class UpsertBrandProfileDto {
  @Field(() => String)
  companyId: string;

  @Field(() => String, { nullable: true })
  aboutTheBrand?: string;

  @Field(() => String, { nullable: true })
  strategicFocus?: string;

  @Field(() => String, { nullable: true })
  valueProposition?: string;

  @Field(() => String, { nullable: true })
  idealCustomerProfiles?: string;

  @Field(() => String, { nullable: true })
  missionAndCoreValues?: string;

  @Field(() => String, { nullable: true })
  brandPointOfView?: string;

  @Field(() => String, { nullable: true })
  toneOfVoice?: string;

  @Field(() => String, { nullable: true })
  ctaText?: string;

  @Field(() => String, { nullable: true })
  authorPersona?: string;
}
