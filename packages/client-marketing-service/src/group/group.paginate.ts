import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { Group } from 'src/group/entities/group.entity';

export const paginateConfig: PaginateConfig<Group> = {
  sortableColumns: ['deletedAt', 'updatedAt'],
  defaultSortBy: [['updatedAt', 'DESC']],
  maxLimit: 100,
  defaultLimit: 50,
  filterableColumns: {
    id: [FilterOperator.EQ, FilterOperator.IN],
    companyId: [FilterOperator.EQ, FilterOperator.IN],
    title: [FilterOperator.EQ, FilterOperator.IN],
    deletedAt: [
      FilterOperator.BTW,
      FilterOperator.LTE,
      FilterOperator.NULL,
      FilterOperator.EQ,
    ],
    updatedAt: [FilterOperator.BTW, FilterOperator.LTE],
  },
  relations: ['recommendations', 'recommendations.scrape'],
};
