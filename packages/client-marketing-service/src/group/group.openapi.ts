import { ApiResponseOptions } from '@nestjs/swagger';
import { paginatedSchema } from 'src/common/openapi/paginated';
import { Group } from 'src/group/entities/group.entity';

export const responses = {
  findAll: {
    status: 200,
    description: 'Returns all groups',
    schema: paginatedSchema(Group),
  } as ApiResponseOptions,
  findOne: {
    status: 200,
    description: 'Returns a single group',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Group',
        },
      },
    },
  } as ApiResponseOptions,
  create: {
    status: 200,
    description: 'Creates a new group',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Group',
        },
      },
    },
  } as ApiResponseOptions,
  update: {
    status: 200,
    description: 'Updates a group',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Group',
        },
      },
    },
  } as ApiResponseOptions,
  remove: {
    status: 200,
    description: 'Deletes a group',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Group',
        },
      },
    },
  } as ApiResponseOptions,
};
