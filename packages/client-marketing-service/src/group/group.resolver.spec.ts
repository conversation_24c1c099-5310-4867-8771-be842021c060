import { ForbiddenException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  createMockAuthContext,
  mockAuthorizationSuccess,
  mockAuthorizationFailure,
  createGroupServiceMock,
  createTestModuleBuilder,
  createMockGroup,
  testServiceError,
  testAuthorizationError,
} from 'src/test-utils';

import { GroupResolver } from './group.resolver';
import { GroupService } from './group.service';

describe('GroupResolver', () => {
  let resolver: GroupResolver;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockGroupService: ReturnType<typeof createGroupServiceMock>;

  const mockGroup = createMockGroup();

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockGroupService = createGroupServiceMock();

    const module: TestingModule = await createTestModuleBuilder()
      .withResolver(GroupResolver)
      .withService(GroupService, mockGroupService)
      .build();

    resolver = module.get<GroupResolver>(GroupResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test resolver definition
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('surfacedGroupsByCompany', () => {
    it('should return surfaced groups for a company when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockGroupService.findSurfacedGroupsByCompany.mockResolvedValue([
        mockGroup,
      ]);

      const result = await resolver.surfacedGroupsByCompany(
        mockAuthContext,
        'company-id',
      );

      expect(result).toEqual([mockGroup]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        'company-id',
      );
      expect(mockGroupService.findSurfacedGroupsByCompany).toHaveBeenCalledWith(
        'company-id',
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.surfacedGroupsByCompany(mockAuthContext, 'company-id'),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        'company-id',
      );
    });

    it('should handle auth context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        (authCtx: any, companyId: string) =>
          resolver.surfacedGroupsByCompany(authCtx, companyId),
        mockAuthContext,
        authError,
        mockAuthContext,
        'company-id',
      )();
    });

    it('should handle service errors', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (authCtx: any, companyId: string) =>
          resolver.surfacedGroupsByCompany(authCtx, companyId),
        mockGroupService,
        'findSurfacedGroupsByCompany',
        new Error('Service error'),
        mockAuthContext,
        'company-id',
      )();
    });
  });

  describe('surfacedAndPublishedGroups', () => {
    it('should return all surfaced and published groups when user is superuser', async () => {
      mockAuthContext.can.mockResolvedValue(true); // superuser
      mockGroupService.findSurfacedAndPublishedGroups.mockResolvedValue([
        mockGroup,
      ]);

      const result = await resolver.surfacedAndPublishedGroups(mockAuthContext);

      expect(result).toEqual([mockGroup]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        undefined,
      );
      expect(
        mockGroupService.findSurfacedAndPublishedGroups,
      ).toHaveBeenCalledWith(); // called without companyId
    });

    it('should throw ForbiddenException when user is not superuser', async () => {
      mockAuthContext.can.mockResolvedValue(false); // not superuser

      await expect(
        resolver.surfacedAndPublishedGroups(mockAuthContext),
      ).rejects.toThrow(new ForbiddenException('Superuser access required'));

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        undefined,
      );
      expect(
        mockGroupService.findSurfacedAndPublishedGroups,
      ).not.toHaveBeenCalled();
    });

    it('should handle service errors for superuser', async () => {
      mockAuthContext.can.mockResolvedValue(true); // superuser
      const serviceError = new Error('Service error');
      mockGroupService.findSurfacedAndPublishedGroups.mockRejectedValue(
        serviceError,
      );

      await expect(
        resolver.surfacedAndPublishedGroups(mockAuthContext),
      ).rejects.toThrow('Service error');

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        undefined,
      );
      expect(
        mockGroupService.findSurfacedAndPublishedGroups,
      ).toHaveBeenCalledWith();
    });
  });

  describe('groups', () => {
    it('should return all groups when no companyId is provided', async () => {
      const mockGroups = [mockGroup, createMockGroup({ id: 'group-2' })];
      mockGroupService.findAll.mockResolvedValue(mockGroups);
      mockAuthorizationSuccess(mockAuthContext);

      const result = await resolver.groups(mockAuthContext);

      expect(result).toHaveLength(2);
      expect(mockGroupService.findAll).toHaveBeenCalledWith(undefined);
    });

    it('should return groups filtered by companyId', async () => {
      mockGroupService.findAll.mockResolvedValue([mockGroup]);
      mockAuthorizationSuccess(mockAuthContext);

      const result = await resolver.groups(mockAuthContext, 'company-id');

      expect(result).toEqual([mockGroup]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        'company-id',
      );
      expect(mockGroupService.findAll).toHaveBeenCalledWith('company-id');
    });

    it('should handle service errors', async () => {
      const serviceError = new Error('Service error');
      mockAuthorizationSuccess(mockAuthContext);
      mockGroupService.findAll.mockRejectedValue(serviceError);

      await expect(
        resolver.groups(mockAuthContext, 'company-id'),
      ).rejects.toThrow('Service error');

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendationGroup',
        'read',
        'company-id',
      );
      expect(mockGroupService.findAll).toHaveBeenCalledWith('company-id');
    });
  });
});
