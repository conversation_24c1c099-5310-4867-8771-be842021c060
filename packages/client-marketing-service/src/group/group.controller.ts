import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  Query,
} from '@nestjs/common';
import { ApiResponse, ApiExtraModels, ApiTags } from '@nestjs/swagger';
import { DataResponseDto } from 'src/common/interceptors/response-wrapper.interceptor';
import { PaginatedResponse } from 'src/common/openapi/paginated';
import { CreateGroupDto } from 'src/group/dto/create-group.dto';
import { UpdateGroupDto } from 'src/group/dto/update-group.dto';
import { Group } from 'src/group/entities/group.entity';
import { responses } from 'src/group/group.openapi';
import { GroupService } from 'src/group/group.service';
import { DeleteResult } from 'typeorm';

@ApiExtraModels(DataResponseDto, PaginatedResponse, Group)
@Controller('group')
@ApiTags('Group')
export class GroupController {
  constructor(private readonly groupService: GroupService) {}

  @Get()
  @ApiResponse(responses.findAll)
  async findAll(@Query('companyId') companyId: string): Promise<Group[]> {
    return await this.groupService.findSurfacedGroupsByCompany(companyId);
  }

  @Get(':id')
  @ApiResponse(responses.findOne)
  async findOne(@Param('id') id: string): Promise<Group> {
    return await this.groupService.findOne(id);
  }

  @Post()
  @ApiResponse(responses.create)
  async create(@Body() createGroupDto: CreateGroupDto): Promise<Group> {
    return await this.groupService.create(createGroupDto);
  }

  @Patch(':id')
  @ApiResponse(responses.update)
  async update(
    @Param('id') id: string,
    @Body() updateGroupDto: UpdateGroupDto,
  ): Promise<Group> {
    return await this.groupService.update(id, updateGroupDto);
  }

  @Delete(':id')
  @ApiResponse(responses.remove)
  async remove(@Param('id') id: string): Promise<DeleteResult> {
    return await this.groupService.remove(id);
  }
}
