import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { CreateGroupDto } from 'src/group/dto/create-group.dto';
import { UpdateGroupDto } from 'src/group/dto/update-group.dto';
import { Group } from 'src/group/entities/group.entity';
import { GroupService } from 'src/group/group.service';
import { LessThanOrEqual } from 'typeorm';

jest.mock('nestjs-paginate');

describe('GroupService', () => {
  let service: GroupService;

  const mockGroup = {
    id: 'test-id',
    companyId: 'company-id',
    scrapedPageId: 'scraped-page-id',
    title: 'Test Group',
    description: 'Test Description',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as Group;

  const mockCreateGroupDto: CreateGroupDto = {
    companyId: 'company-id',
    scrapedPageId: 'scraped-page-id',
    title: 'Test Group',
    description: 'Test Description',
    metadata: {},
  };

  const mockUpdateGroupDto: UpdateGroupDto = {
    title: 'Updated Test Group',
    description: 'Updated Test Description',
  };

  const mockGroupRepository = {
    create: jest.fn().mockReturnValue(mockGroup),
    save: jest.fn().mockResolvedValue(mockGroup),
    find: jest.fn().mockResolvedValue([mockGroup]),
    findOne: jest.fn().mockResolvedValue(mockGroup),
    remove: jest.fn().mockResolvedValue(mockGroup),
    softDelete: jest.fn().mockResolvedValue(mockGroup),
    createQueryBuilder: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupService,
        {
          provide: getRepositoryToken(Group),
          useValue: mockGroupRepository,
        },
      ],
    }).compile();

    service = module.get<GroupService>(GroupService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findSurfacedGroupsByCompany', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return an array of surfaced groups for a company with correct query parameters', async () => {
      const companyId = 'company-id';
      const mockDate = new Date('2024-01-01T00:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      const result = await service.findSurfacedGroupsByCompany(companyId);

      expect(result).toEqual([mockGroup]);
      expect(mockGroupRepository.find).toHaveBeenCalledWith({
        where: {
          companyId,
          groupScheduledActions: {
            scheduledAction: {
              surfacedAt: LessThanOrEqual(mockDate),
            },
          },
        },
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });

      jest.useRealTimers();
    });

    it('should sort recommendations by priority for groups with recommendations', async () => {
      const groupWithRecommendations = {
        ...mockGroup,
        recommendations: [
          { id: 'rec-3', type: 'main_heading', recommendationValue: 'Heading' },
          { id: 'rec-1', type: 'meta_title', recommendationValue: 'Title' },
          { id: 'rec-4', type: 'other_type', recommendationValue: 'Other' },
          {
            id: 'rec-2',
            type: 'meta_description',
            recommendationValue: 'Description',
          },
        ],
      };
      mockGroupRepository.find.mockResolvedValueOnce([
        groupWithRecommendations,
      ]);

      const result = await service.findSurfacedGroupsByCompany('company-id');

      expect(result[0].recommendations).toBeDefined();
      expect(result[0].recommendations![0].type).toBe('meta_title');
      expect(result[0].recommendations![1].type).toBe('meta_description');
      expect(result[0].recommendations![2].type).toBe('main_heading');
      expect(result[0].recommendations![3].type).toBe('other_type');
    });

    it('should handle groups without recommendations', async () => {
      const groupWithoutRecommendations = {
        ...mockGroup,
        recommendations: undefined,
      };
      mockGroupRepository.find.mockResolvedValueOnce([
        groupWithoutRecommendations,
      ]);

      const result = await service.findSurfacedGroupsByCompany('company-id');

      expect(result).toEqual([groupWithoutRecommendations]);
      expect(result[0].recommendations).toBeUndefined();
    });

    it('should handle empty results array', async () => {
      mockGroupRepository.find.mockResolvedValueOnce([]);

      const result = await service.findSurfacedGroupsByCompany('company-id');

      expect(result).toEqual([]);
    });

    it('should handle null group in results gracefully', async () => {
      mockGroupRepository.find.mockResolvedValueOnce([null, mockGroup]);

      const result = await service.findSurfacedGroupsByCompany('company-id');

      expect(result).toEqual([null, mockGroup]);
    });
  });

  describe('findAll', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return an array of groups without companyId filter', async () => {
      const result = await service.findAll();
      expect(result).toEqual([mockGroup]);
      expect(mockGroupRepository.find).toHaveBeenCalledWith({
        where: {},
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
    });

    it('should return groups filtered by companyId when provided', async () => {
      const companyId = 'specific-company-id';
      const result = await service.findAll(companyId);
      expect(result).toEqual([mockGroup]);
      expect(mockGroupRepository.find).toHaveBeenCalledWith({
        where: { companyId },
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
    });

    it('should handle empty string companyId as no filter', async () => {
      const result = await service.findAll('');
      expect(result).toEqual([mockGroup]);
      expect(mockGroupRepository.find).toHaveBeenCalledWith({
        where: {},
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
    });

    it('should return empty array when no groups found', async () => {
      mockGroupRepository.find.mockResolvedValueOnce([]);
      const result = await service.findAll('non-existent-company');
      expect(result).toEqual([]);
    });
  });

  describe('findOne', () => {
    it('should return a single group', async () => {
      const result = await service.findOne('test-id');
      expect(result).toEqual(mockGroup);
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
    });

    it('should throw NotFoundException when group is not found', async () => {
      mockGroupRepository.findOne.mockResolvedValueOnce(null);
      await expect(service.findOne('not-found')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should sort recommendations by type priority order', async () => {
      // Create a group with recommendations in random order
      const groupWithRecommendations = {
        ...mockGroup,
        recommendations: [
          // Random order
          {
            id: 'rec-3',
            type: 'main_heading',
            recommendationValue: 'Recommended Heading',
          },
          {
            id: 'rec-1',
            type: 'meta_title',
            recommendationValue: 'Recommended Title',
          },
          {
            id: 'rec-2',
            type: 'meta_description',
            recommendationValue: 'Recommended Description',
          },
        ],
      };

      // Mock the findOne to return our group with recommendations
      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithRecommendations,
      );

      // Call the service method
      const result = await service.findOne('group-with-recommendations');

      // Verify the recommendations are in the correct order
      expect(result.recommendations).toBeDefined();
      expect(result.recommendations![0].type).toBe('meta_title');
      expect(result.recommendations![1].type).toBe('meta_description');
      expect(result.recommendations![2].type).toBe('main_heading');
    });
  });

  describe('create', () => {
    it('should create a new group', async () => {
      const result = await service.create(mockCreateGroupDto);
      expect(result).toEqual(mockGroup);
      expect(mockGroupRepository.create).toHaveBeenCalledWith(
        mockCreateGroupDto,
      );
      expect(mockGroupRepository.save).toHaveBeenCalledWith(mockGroup);
    });
  });

  describe('update', () => {
    it('should update a group', async () => {
      const updatedGroup = { ...mockGroup, ...mockUpdateGroupDto };
      mockGroupRepository.save.mockResolvedValueOnce(updatedGroup);

      const result = await service.update('test-id', mockUpdateGroupDto);

      expect(result).toEqual(updatedGroup);
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
      expect(mockGroupRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'test-id',
          title: 'Updated Test Group',
          description: 'Updated Test Description',
        }),
      );
    });

    it('should properly merge metadata when updating', async () => {
      const existingGroup = {
        ...mockGroup,
        metadata: { existing: 'value' },
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(existingGroup);

      const updateDto: UpdateGroupDto = {
        metadata: { new: 'value' },
      };

      await service.update('test-id', updateDto);

      expect(mockGroupRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: {
            existing: 'value',
            new: 'value',
          },
        }),
      );
    });

    it('should throw NotFoundException when group to update is not found', async () => {
      mockGroupRepository.findOne.mockResolvedValueOnce(null);

      await expect(
        service.update('not-found', mockUpdateGroupDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should not merge metadata if not provided in update', async () => {
      const existingGroup = {
        ...mockGroup,
        metadata: { existing: 'value' },
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(existingGroup);

      const updateDto: UpdateGroupDto = {
        title: 'New Title',
      };

      await service.update('test-id', updateDto);

      expect(mockGroupRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'New Title',
          metadata: { existing: 'value' },
        }),
      );
    });

    it('should handle update with empty metadata object', async () => {
      const existingGroup = {
        ...mockGroup,
        metadata: { existing: 'value' },
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(existingGroup);

      const updateDto: UpdateGroupDto = {
        metadata: {},
      };

      await service.update('test-id', updateDto);

      expect(mockGroupRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: { existing: 'value' },
        }),
      );
    });

    it('should handle update with null existing metadata', async () => {
      const existingGroup = {
        ...mockGroup,
        metadata: null,
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(existingGroup);

      const updateDto: UpdateGroupDto = {
        metadata: { new: 'value' },
      };

      await service.update('test-id', updateDto);

      expect(mockGroupRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: { new: 'value' },
        }),
      );
    });
  });

  describe('remove', () => {
    it('should remove a group', async () => {
      const result = await service.remove('test-id');
      expect(result).toEqual(mockGroup);
      expect(mockGroupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        relations: [
          'keyword',
          'keyword.pageKeywords',
          'scrapedPage',
          'groupScheduledActions',
          'groupScheduledActions.scheduledAction',
          'recommendations',
          'recommendations.scrape',
          'recommendations.scrape.scrapedPage',
        ],
      });
      expect(mockGroupRepository.softDelete).toHaveBeenCalledWith({
        id: 'test-id',
      });
    });

    it('should throw NotFoundException when group to remove is not found', async () => {
      mockGroupRepository.findOne.mockResolvedValueOnce(null);
      await expect(service.remove('not-found')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should return the delete result from repository', async () => {
      const deleteResult = { affected: 1, raw: [] };
      mockGroupRepository.softDelete.mockResolvedValueOnce(deleteResult);

      const result = await service.remove('test-id');
      expect(result).toEqual(deleteResult);
    });
  });

  describe('sortRecommendationsByPriority (private method testing via public methods)', () => {
    it('should handle recommendations with unknown types', async () => {
      const groupWithMixedRecommendations = {
        ...mockGroup,
        recommendations: [
          { id: 'rec-1', type: 'unknown_type', recommendationValue: 'Unknown' },
          { id: 'rec-2', type: 'meta_title', recommendationValue: 'Title' },
          { id: 'rec-3', type: 'custom_type', recommendationValue: 'Custom' },
        ],
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithMixedRecommendations,
      );

      const result = await service.findOne('test-id');

      expect(result.recommendations![0].type).toBe('meta_title');
      expect(result.recommendations![1].type).toBe('unknown_type');
      expect(result.recommendations![2].type).toBe('custom_type');
    });

    it('should handle empty recommendations array', async () => {
      const groupWithEmptyRecommendations = {
        ...mockGroup,
        recommendations: [],
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithEmptyRecommendations,
      );

      const result = await service.findOne('test-id');

      expect(result.recommendations).toEqual([]);
    });

    it('should not mutate original recommendations array', async () => {
      const originalRecommendations = [
        { id: 'rec-1', type: 'main_heading', recommendationValue: 'Heading' },
        { id: 'rec-2', type: 'meta_title', recommendationValue: 'Title' },
      ];
      const groupWithRecommendations = {
        ...mockGroup,
        recommendations: originalRecommendations,
      };
      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithRecommendations,
      );

      await service.findOne('test-id');

      expect(originalRecommendations[0].type).toBe('main_heading');
      expect(originalRecommendations[1].type).toBe('meta_title');
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle repository errors gracefully', async () => {
      const repositoryError = new Error('Database connection failed');
      mockGroupRepository.find.mockRejectedValueOnce(repositoryError);

      await expect(service.findAll()).rejects.toThrow(
        'Database connection failed',
      );
    });

    it('should handle save errors during create', async () => {
      const saveError = new Error('Save failed');
      mockGroupRepository.save.mockRejectedValueOnce(saveError);

      await expect(service.create(mockCreateGroupDto)).rejects.toThrow(
        'Save failed',
      );
    });

    it('should handle soft delete errors', async () => {
      const deleteError = new Error('Delete failed');
      mockGroupRepository.softDelete.mockRejectedValueOnce(deleteError);

      await expect(service.remove('test-id')).rejects.toThrow('Delete failed');
    });
  });

  describe('findSurfacedAndPublishedGroups', () => {
    let mockQueryBuilder: any;

    beforeEach(() => {
      mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([mockGroup]),
      };
      mockGroupRepository.createQueryBuilder = jest
        .fn()
        .mockReturnValue(mockQueryBuilder);
    });

    it('should find all groups with SURFACED or PUBLISHED status when no companyId provided', async () => {
      const result = await service.findSurfacedAndPublishedGroups();

      expect(mockGroupRepository.createQueryBuilder).toHaveBeenCalledWith(
        'group',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'group.keyword',
        'keyword',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'group.scrapedPage',
        'scrapedPage',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'group.groupScheduledActions',
        'groupScheduledActions',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'groupScheduledActions.scheduledAction',
        'scheduledAction',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'group.recommendations',
        'recommendations',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'recommendations.scrape',
        'scrape',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'scrape.scrapedPage',
        'recommendationScrapedPage',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scheduledAction.status IN (:...statuses)',
        {
          statuses: ['SURFACED', 'PUBLISHED'],
        },
      );
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalled();
      expect(result).toEqual([mockGroup]);
    });

    it('should filter by companyId when provided', async () => {
      const companyId = 'test-company-id';
      await service.findSurfacedAndPublishedGroups(companyId);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'group.companyId = :companyId',
        { companyId },
      );
    });

    it('should sort recommendations by priority', async () => {
      const groupWithRecommendations = {
        ...mockGroup,
        recommendations: [
          { id: 'rec-3', type: 'main_heading', recommendationValue: 'Heading' },
          { id: 'rec-1', type: 'meta_title', recommendationValue: 'Title' },
          {
            id: 'rec-2',
            type: 'meta_description',
            recommendationValue: 'Description',
          },
        ],
      };
      mockQueryBuilder.getMany.mockResolvedValueOnce([
        groupWithRecommendations,
      ]);

      const result = await service.findSurfacedAndPublishedGroups();

      expect(result[0].recommendations![0].type).toBe('meta_title');
      expect(result[0].recommendations![1].type).toBe('meta_description');
      expect(result[0].recommendations![2].type).toBe('main_heading');
    });

    it('should handle empty results', async () => {
      mockQueryBuilder.getMany.mockResolvedValueOnce([]);

      const result = await service.findSurfacedAndPublishedGroups();

      expect(result).toEqual([]);
    });

    it('should handle groups without recommendations', async () => {
      const groupWithoutRecommendations = {
        ...mockGroup,
        recommendations: undefined,
      };
      mockQueryBuilder.getMany.mockResolvedValueOnce([
        groupWithoutRecommendations,
      ]);

      const result = await service.findSurfacedAndPublishedGroups();

      expect(result[0].recommendations).toBeUndefined();
    });

    it('should handle null groups in results', async () => {
      mockQueryBuilder.getMany.mockResolvedValueOnce([null, mockGroup]);

      const result = await service.findSurfacedAndPublishedGroups();

      expect(result).toEqual([null, mockGroup]);
    });
  });

  describe('scrapedPageId data integrity', () => {
    it('should include scrapedPage in relations when finding groups', async () => {
      await service.findAll();

      expect(mockGroupRepository.find).toHaveBeenCalledWith(
        expect.objectContaining({
          relations: expect.arrayContaining(['scrapedPage']),
        }),
      );
    });

    it('should verify group.scrapedPageId matches recommendations scraped page', async () => {
      const scrapedPageId = 'scraped-page-123';
      const groupWithScrapedPageAndRecommendations = {
        ...mockGroup,
        scrapedPageId,
        scrapedPage: {
          id: scrapedPageId,
          pageName: 'Test Page',
          url: 'https://example.com',
        },
        recommendations: [
          {
            id: 'rec-1',
            type: 'meta_title',
            scrape: {
              id: 'scrape-1',
              scrapedPageId,
              scrapedPage: {
                id: scrapedPageId,
                pageName: 'Test Page',
                url: 'https://example.com',
              },
            },
          },
          {
            id: 'rec-2',
            type: 'meta_description',
            scrape: {
              id: 'scrape-2',
              scrapedPageId,
              scrapedPage: {
                id: scrapedPageId,
                pageName: 'Test Page',
                url: 'https://example.com',
              },
            },
          },
        ],
      };

      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithScrapedPageAndRecommendations,
      );
      const result = await service.findOne('test-id');

      // Verify all recommendations point to the same scraped page as the group
      expect(result.scrapedPageId).toBe(scrapedPageId);
      result.recommendations?.forEach(rec => {
        expect(rec.scrape?.scrapedPageId).toBe(scrapedPageId);
      });
    });

    it('should handle groups with scrapedPageId but no recommendations', async () => {
      const groupWithScrapedPageNoRecommendations = {
        ...mockGroup,
        scrapedPageId: 'scraped-page-456',
        scrapedPage: {
          id: 'scraped-page-456',
          pageName: 'Another Page',
          url: 'https://example2.com',
        },
        recommendations: [],
      };

      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithScrapedPageNoRecommendations,
      );
      const result = await service.findOne('test-id');

      expect(result.scrapedPageId).toBe('scraped-page-456');
      expect(result.scrapedPage).toBeDefined();
      expect(result.recommendations).toEqual([]);
    });

    it('should handle groups with null scrapedPageId', async () => {
      const groupWithoutScrapedPage = {
        ...mockGroup,
        scrapedPageId: null,
        scrapedPage: null,
        recommendations: [],
      };

      mockGroupRepository.findOne.mockResolvedValueOnce(
        groupWithoutScrapedPage,
      );
      const result = await service.findOne('test-id');

      expect(result.scrapedPageId).toBeNull();
      expect(result.scrapedPage).toBeNull();
    });

    it('should create group with scrapedPageId when provided in DTO', async () => {
      const createDto = {
        ...mockCreateGroupDto,
        scrapedPageId: 'new-scraped-page-789',
      };

      await service.create(createDto);

      expect(mockGroupRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          scrapedPageId: 'new-scraped-page-789',
        }),
      );
    });
  });
});
