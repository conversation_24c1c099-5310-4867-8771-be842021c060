import { NotFoundException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateGroupDto } from 'src/group/dto/create-group.dto';
import { UpdateGroupDto } from 'src/group/dto/update-group.dto';
import { Group } from 'src/group/entities/group.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { DeleteResult, LessThanOrEqual, Repository } from 'typeorm';

@Injectable()
export class GroupService {
  private readonly logger = new Logger(GroupService.name);
  constructor(
    @InjectRepository(Group)
    private readonly groupRepository: Repository<Group>,
  ) {}

  async create(createGroupDto: CreateGroupDto): Promise<Group> {
    this.logger.log('Creating group', createGroupDto);
    const group = this.groupRepository.create(createGroupDto);
    return this.groupRepository.save(group);
  }

  /**
   * Find all groups with optional company filtering
   * @param companyId Optional company ID to filter by. If not provided, returns groups for all companies.
   * @returns Array of Group entities
   */
  async findAll(companyId?: string): Promise<Group[]> {
    // If companyId is provided, filter by it; otherwise return all groups
    const whereClause = companyId ? { companyId } : {};

    return await this.groupRepository.find({
      where: whereClause,
      relations: [
        'keyword',
        'keyword.pageKeywords',
        'scrapedPage',
        'groupScheduledActions',
        'groupScheduledActions.scheduledAction',
        'recommendations',
        'recommendations.scrape',
        'recommendations.scrape.scrapedPage',
      ],
    });
  }

  /**
   * Find surfaced groups for a specific company
   * @param companyId Company ID to filter by
   * @returns Array of Group entities with surfaced scheduled actions
   */
  async findSurfacedGroupsByCompany(companyId: string): Promise<Group[]> {
    this.logger.debug('Finding surfaced groups by company', { companyId });
    const now = new Date();

    const result = await this.groupRepository.find({
      where: {
        companyId,
        groupScheduledActions: {
          scheduledAction: {
            surfacedAt: LessThanOrEqual(now),
          },
        },
      },
      relations: [
        'keyword',
        'keyword.pageKeywords',
        'scrapedPage',
        'groupScheduledActions',
        'groupScheduledActions.scheduledAction',
        'recommendations',
        'recommendations.scrape',
        'recommendations.scrape.scrapedPage',
      ],
    });

    // Sort recommendations for each group if data exists
    if (Array.isArray(result)) {
      result.forEach(group => {
        if (group && group.recommendations) {
          group.recommendations = this.sortRecommendationsByPriority(
            group.recommendations,
          );
        }
      });
    }

    return result;
  }

  /**
   * Find all groups with surfaced or published status
   * @param companyId Optional company ID to filter by. If not provided, returns groups for all companies.
   * @returns Array of Group entities with surfaced or published scheduled actions
   */
  async findSurfacedAndPublishedGroups(companyId?: string): Promise<Group[]> {
    const queryBuilder = this.groupRepository
      .createQueryBuilder('group')
      .leftJoinAndSelect('group.keyword', 'keyword')
      .leftJoinAndSelect('keyword.pageKeywords', 'pageKeywords')
      .leftJoinAndSelect('group.scrapedPage', 'scrapedPage')
      .leftJoinAndSelect('group.groupScheduledActions', 'groupScheduledActions')
      .leftJoinAndSelect(
        'groupScheduledActions.scheduledAction',
        'scheduledAction',
      )
      .leftJoinAndSelect('group.recommendations', 'recommendations')
      .leftJoinAndSelect('recommendations.scrape', 'scrape')
      .leftJoinAndSelect('scrape.scrapedPage', 'recommendationScrapedPage')
      .where('scheduledAction.status IN (:...statuses)', {
        statuses: ['SURFACED', 'PUBLISHED'],
      });

    if (companyId) {
      queryBuilder.andWhere('group.companyId = :companyId', { companyId });
    }

    const result = await queryBuilder.getMany();

    // Sort recommendations for each group if data exists
    if (Array.isArray(result)) {
      result.forEach(group => {
        if (group && group.recommendations) {
          group.recommendations = this.sortRecommendationsByPriority(
            group.recommendations,
          );
        }
      });
    }

    return result;
  }

  /**
   * Find surfaced and published groups with minimal data for ranking operations
   * Optimized query that only loads necessary fields to prevent memory issues
   * @returns Array of lightweight group objects with only keyword and scraped page info
   */
  async findSurfacedAndPublishedGroupsForRanking(): Promise<
    {
      keyword: { keyword: string };
      scrapedPage: { id: string; url: string };
    }[]
  > {
    const result = await this.groupRepository
      .createQueryBuilder('group')
      .select([
        'group.id',
        'keyword.keyword',
        'scrapedPage.id',
        'scrapedPage.url',
      ])
      .leftJoin('group.keyword', 'keyword')
      .leftJoin('group.scrapedPage', 'scrapedPage')
      .leftJoin('group.groupScheduledActions', 'groupScheduledActions')
      .leftJoin('groupScheduledActions.scheduledAction', 'scheduledAction')
      .where('scheduledAction.status IN (:...statuses)', {
        statuses: ['SURFACED', 'PUBLISHED'],
      })
      .getMany();

    this.logger.debug('Fetched surfaced and published groups for ranking', {
      count: result.length,
    });

    return result;
  }

  /**
   * Find surfaced and published groups with pagination for ranking operations
   * Prevents memory issues by fetching data in manageable chunks
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Paginated response with groups, pagination info, and total count
   */
  async findSurfacedAndPublishedGroupsForRankingPaginated(
    page: number = 1,
    limit: number = 100,
  ): Promise<{
    data: {
      keyword: { keyword: string };
      scrapedPage: { id: string; url: string };
    }[];
    hasMore: boolean;
    totalCount: number;
    currentPage: number;
    pageSize: number;
  }> {
    const offset = (page - 1) * limit;

    // Create base query builder for consistent joins and where clause
    const createBaseQueryBuilder = () =>
      this.groupRepository
        .createQueryBuilder('group')
        .leftJoin('group.groupScheduledActions', 'groupScheduledActions')
        .leftJoin('groupScheduledActions.scheduledAction', 'scheduledAction')
        .where('scheduledAction.status IN (:...statuses)', {
          statuses: ['SURFACED', 'PUBLISHED'],
        });

    // Get total count using DISTINCT to avoid overcounting
    const totalCountResult = await createBaseQueryBuilder()
      .select('COUNT(DISTINCT("group"."id"))', 'count')
      .getRawOne();
    const totalCount = parseInt(totalCountResult.count, 10);

    // Get paginated data using DISTINCT ON to prevent duplicate rows
    const data = await createBaseQueryBuilder()
      .select([
        'group.id',
        'keyword.keyword',
        'scrapedPage.id',
        'scrapedPage.url',
      ])
      .leftJoin('group.keyword', 'keyword')
      .leftJoin('group.scrapedPage', 'scrapedPage')
      .distinctOn(['group.id'])
      .orderBy('group.id', 'ASC') // Required for DISTINCT ON
      .take(limit)
      .skip(offset)
      .getMany();

    this.logger.debug(
      'Fetched paginated surfaced and published groups for ranking',
      {
        page,
        limit,
        count: data.length,
        totalCount,
        hasMore: offset + limit < totalCount,
      },
    );

    return {
      data,
      hasMore: offset + limit < totalCount,
      totalCount,
      currentPage: page,
      pageSize: limit,
    };
  }

  async findOne(id: string): Promise<Group> {
    this.logger.log(`Fetching group with ID "${id}"`);
    const group = await this.groupRepository.findOne({
      where: { id },
      relations: [
        'keyword',
        'keyword.pageKeywords',
        'scrapedPage',
        'groupScheduledActions',
        'groupScheduledActions.scheduledAction',
        'recommendations',
        'recommendations.scrape',
        'recommendations.scrape.scrapedPage',
      ],
    });

    if (!group) {
      throw new NotFoundException(`Group with ID "${id}" not found`);
    }

    // Sort recommendations if they exist
    if (group.recommendations) {
      group.recommendations = this.sortRecommendationsByPriority(
        group.recommendations,
      );
    }

    return group;
  }

  async update(id: string, updateGroupDto: UpdateGroupDto): Promise<Group> {
    this.logger.log(`Updating group with ID "${id}"`, updateGroupDto);
    const group = await this.findOne(id);

    if (!group) {
      throw new NotFoundException(`Group with ID "${id}" not found`);
    }

    const { metadata, ...otherProps } = updateGroupDto;

    // Update non-metadata properties
    Object.assign(group, otherProps);

    // Merge metadata if provided instead of completely replacing it
    if (metadata) {
      group.metadata = {
        ...group.metadata,
        ...metadata,
      };
    }

    return this.groupRepository.save(group);
  }

  async remove(id: string): Promise<DeleteResult> {
    this.logger.log(`Removing group with ID "${id}"`);
    const group = await this.findOne(id);
    if (!group) {
      throw new NotFoundException(`Group with ID "${id}" not found`);
    }
    return await this.groupRepository.softDelete({ id });
  }

  /**
   * Sorts recommendations by type priority:
   * 1. meta_title
   * 2. meta_description
   * 3. main_heading
   * 4. All other types
   */
  private sortRecommendationsByPriority(
    recommendations: Recommendation[],
  ): Recommendation[] {
    return [...recommendations].sort((a, b) => {
      const getPriority = (type: string): number => {
        switch (type) {
          case 'meta_title':
            return 1;
          case 'meta_description':
            return 2;
          case 'main_heading':
            return 3;
          default:
            return 999;
        }
      };

      return getPriority(a.type) - getPriority(b.type);
    });
  }
}
