import { Field, ID, ObjectType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsUUID,
  IsNotEmpty,
  IsObject,
  IsString,
  IsDate,
} from 'class-validator';
import { Factory } from 'nestjs-seeder';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Company } from 'src/company/entities/company.entity';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { Rank } from 'src/seo-optimizations/types/rank';
import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  OneToMany,
  DeleteDateColumn,
  UpdateDateColumn,
  CreateDateColumn,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  ManyToOne,
} from 'typeorm';

@ObjectType()
class GroupMetadata {
  @Field(() => String)
  keyword: string;

  @Field(() => String, {
    nullable: true,
    deprecationReason: 'Use "rank" field instead',
  })
  ranking?: string;

  @Field(() => Rank, { nullable: true })
  rank?: Rank;
}

/**
 * Group Entity
 *
 * Represents a collection of SEO recommendations for a specific keyword and scraped page.
 *
 * Key Relationships:
 * - Direct relationship to ScrapedPage via scrapedPageId (added for query optimization)
 * - Many-to-one relationship with Keyword
 * - One-to-many relationship with Recommendations
 * - Linked to ScheduledActions through GroupScheduledAction junction table
 *
 * Data Integrity:
 * - The scrapedPageId should always match the scrapedPageId of all associated recommendations
 * - This is enforced during creation in the storeSEODraft mutation
 */
@Entity('group')
@ObjectType()
export class Group {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'company_id' })
  @IsUUID()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  companyId: string;

  @Column({ type: 'uuid', name: 'keyword_id', nullable: true })
  @IsUUID()
  // TODO make this required
  // @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID, { nullable: true })
  keywordId: string;

  /**
   * Direct reference to the scraped page associated with this group.
   * This field provides a direct path to the scraped page without needing to traverse
   * through recommendations -> scrape -> scrapedPage.
   *
   * This field should always match the scrapedPageId of all recommendations in this group.
   */
  @Column({ type: 'uuid', name: 'scraped_page_id', nullable: false })
  @IsUUID()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  scrapedPageId: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.groupTitle)
  @Field(() => String)
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.groupDescription)
  @Field(() => String)
  description: string;

  @IsObject()
  @Column({ type: 'jsonb', default: {} })
  @Factory(SeederFactories.groupMetadata)
  @Field(() => GroupMetadata)
  metadata: Record<string, any>;

  @IsDate()
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @IsDate()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  @Field(() => Date, { nullable: true })
  deletedAt: Date | null;

  @ApiProperty({
    type: () => [Recommendation],
    description: 'List of recommendations associated with this group',
    required: false,
  })
  @Type(() => Recommendation)
  @OneToMany(() => Recommendation, recommendation => recommendation.group, {
    eager: true,
    cascade: true,
  })
  @Field(() => [Recommendation])
  recommendations?: Recommendation[];

  @Type(() => Keyword)
  @ManyToOne(() => Keyword, Keyword => Keyword.groups)
  @Field(() => Keyword, { nullable: true })
  @JoinColumn({ name: 'keyword_id' })
  keyword: Keyword;

  @Type(() => ScrapedPage)
  @ManyToOne(() => ScrapedPage, scrapedPage => scrapedPage.groups)
  @Field(() => ScrapedPage, { nullable: true })
  @JoinColumn({ name: 'scraped_page_id' })
  scrapedPage: ScrapedPage;

  @Type(() => GroupScheduledAction)
  @OneToMany(
    () => GroupScheduledAction,
    groupScheduledAction => groupScheduledAction.group,
  )
  @Field(() => [GroupScheduledAction], { nullable: true })
  groupScheduledActions?: GroupScheduledAction[];

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}
