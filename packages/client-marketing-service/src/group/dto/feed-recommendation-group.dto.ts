import { Field, ID, ObjectType } from '@nestjs/graphql';
import { MediaObject } from 'src/cms/dto/feed-blog-post.dto';
import { MediaDto } from 'src/common/services/api-gateway/dto/media.dto';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { ScrapedPageType } from 'src/scraped-page/entities/scraped-page.entity';
import { Rank } from 'src/seo-optimizations/types/rank';

@ObjectType()
class FeedGroupMetadata {
  @Field(() => String, {
    nullable: true,
    deprecationReason: 'Use "rank" field instead',
  })
  ranking: string | null;

  @Field(() => Rank, { nullable: true })
  rank?: Rank;
}

@ObjectType()
class FeedPage {
  @Field(() => String)
  name: string;

  @Field(() => ScrapedPageType)
  type: ScrapedPageType;

  @Field(() => String)
  url: string;
}

@ObjectType()
class FeedRecommendation {
  @Field(() => RecommendationType)
  type: RecommendationType;
}

@ObjectType({ description: 'A group of recommended SEO optimizations.' })
export class FeedRecommendationGroup {
  @Field(() => ID)
  id: string;

  @Field(() => Date, { nullable: true })
  publishedAt: Date | null;

  @Field(() => Date)
  scheduledToBePublishedAt: Date;

  @Field(() => String)
  keyword: string;

  @Field(() => FeedGroupMetadata)
  metadata: FeedGroupMetadata;

  @Field(() => [FeedRecommendation])
  recommendations: FeedRecommendation[];

  @Field(() => FeedPage)
  page: FeedPage;

  @Field(() => String, {
    nullable: true,
    description: 'The id of the image to be used as a thumbnail.',
  })
  mediaId: string | null;

  @Field(() => MediaObject, {
    nullable: true,
    description: 'The media object containing thumbnail URLs.',
  })
  media: MediaDto | null;
}
