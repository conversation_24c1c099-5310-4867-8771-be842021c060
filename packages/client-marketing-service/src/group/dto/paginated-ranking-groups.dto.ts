import { Field, ObjectType, Int } from '@nestjs/graphql';

@ObjectType()
export class RankingGroupKeyword {
  @Field(() => String)
  keyword: string;
}

@ObjectType()
export class RankingGroupScrapedPage {
  @Field(() => String)
  id: string;

  @Field(() => String)
  url: string;
}

@ObjectType()
export class RankingGroup {
  @Field(() => RankingGroupKeyword)
  keyword: RankingGroupKeyword;

  @Field(() => RankingGroupScrapedPage)
  scrapedPage: RankingGroupScrapedPage;
}

@ObjectType()
export class SurfacedAndPublishedGroupsForRankingResponse {
  @Field(() => [RankingGroup])
  data: RankingGroup[];

  @Field(() => Boolean)
  hasMore: boolean;

  @Field(() => Int)
  totalCount: number;

  @Field(() => Int)
  currentPage: number;

  @Field(() => Int)
  pageSize: number;
}
