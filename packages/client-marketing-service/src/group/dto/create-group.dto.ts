import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsObject,
  IsOptional,
  IsString,
  IsNotEmpty,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { CreateRecommendationDto } from 'src/recommendation/dto/create-recommendation.dto';

export class CreateGroupDto {
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @IsUUID()
  @IsNotEmpty()
  scrapedPageId: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsOptional()
  scheduledToBeSurfacedAt?: Date;

  @IsOptional()
  scheduledToBePublishedAt?: Date;

  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'List of recommendations to create with this group',
    required: false,
    type: () => [CreateRecommendationDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateRecommendationDto)
  @IsOptional()
  recommendations?: CreateRecommendationDto[];
}
