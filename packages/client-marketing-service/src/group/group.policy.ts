import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class RecommendationGroup extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  // TODO: decide on whether we should updated the docs to reflect necessity of proper binding of "this"

  /**
   * Checks if the user has permission to read groups
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  read = (companyId?: string) => {
    if (!companyId) {
      return this.auth.isSuper();
    }
    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };
}
