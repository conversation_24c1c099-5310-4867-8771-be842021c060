import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanyModule } from 'src/company/company.module';
import { Group } from 'src/group/entities/group.entity';
import { GroupController } from 'src/group/group.controller';
import { GroupService } from 'src/group/group.service';

import { GroupResolver } from './group.resolver';

@Module({
  imports: [TypeOrmModule.forFeature([Group]), CompanyModule],
  controllers: [GroupController],
  providers: [GroupService, GroupResolver],
  exports: [GroupService],
})
export class GroupModule {}
