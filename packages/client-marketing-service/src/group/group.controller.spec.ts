import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { getMockedProvider } from 'src/common/utils/get-mocked-provider.util';
import { CreateGroupDto } from 'src/group/dto/create-group.dto';
import { UpdateGroupDto } from 'src/group/dto/update-group.dto';
import { Group } from 'src/group/entities/group.entity';
import { GroupController } from 'src/group/group.controller';
import { GroupService } from 'src/group/group.service';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { ScrapedPageType } from 'src/scraped-page/entities/scraped-page.entity';

describe('GroupController', () => {
  let controller: GroupController;
  let service: GroupService;

  const mockKeyword: Keyword = {
    id: 'keyword-id',
    keyword: 'Test Keyword',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    groups: [],
    pageKeywords: [],
    pageKeywordHistory: [],
  };

  const mockScrapedPage: ScrapedPage = {
    id: 'scraped-page-id',
    url: 'https://example.com',
    pageName: 'Test Page',
    pageType: ScrapedPageType.HOMEPAGE,
    companyId: 'company-id',
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: new Date(),
    recommendations: [],
    scrapes: [],
    groups: [],
    pageKeywordHistory: [],
    pageKeywords: [],
  };

  const mockGroup: Group = {
    id: 'test-id',
    companyId: 'company-id',
    keywordId: 'keyword-id',
    title: 'Test Group',
    description: 'Test Description',
    keyword: mockKeyword,
    metadata: {},
    scrapedPageId: 'scraped-page-id',
    scrapedPage: mockScrapedPage,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: new Date(),
  };

  const mockCreateGroupDto: CreateGroupDto = {
    companyId: 'company-id',
    scrapedPageId: 'scraped-page-id',
    title: 'Test Group',
    description: 'Test Description',
    metadata: {},
  };

  const mockUpdateGroupDto: UpdateGroupDto = {
    title: 'Updated Test Group',
    description: 'Updated Test Description',
  };

  const mockGroupRepository = {
    create: jest.fn().mockReturnValue(mockGroup),
    save: jest.fn().mockResolvedValue(mockGroup),
    find: jest.fn().mockResolvedValue([mockGroup]),
    findOne: jest.fn().mockResolvedValue(mockGroup),
    remove: jest.fn().mockResolvedValue(mockGroup),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GroupController],
      providers: [
        getMockedProvider(GroupService),
        {
          provide: getRepositoryToken(Group),
          useValue: mockGroupRepository,
        },
      ],
    }).compile();

    controller = module.get<GroupController>(GroupController);
    service = module.get<GroupService>(GroupService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of groups', async () => {
      jest
        .spyOn(service, 'findSurfacedGroupsByCompany')
        .mockResolvedValueOnce([mockGroup]);

      const result = await controller.findAll(mockGroup.companyId);
      expect(result).toEqual([mockGroup]);
      expect(service.findSurfacedGroupsByCompany).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single group with data wrapper', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValueOnce(mockGroup);
      const result = await controller.findOne('test-id');
      expect(result).toEqual(mockGroup);
      expect(service.findOne).toHaveBeenCalledWith('test-id');
    });

    it('should propagate NotFoundException from service', async () => {
      jest
        .spyOn(service, 'findOne')
        .mockRejectedValueOnce(new NotFoundException('Group not found'));
      await expect(controller.findOne('not-found')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('create', () => {
    it('should create a new group and return with data wrapper', async () => {
      jest.spyOn(service, 'create').mockResolvedValueOnce(mockGroup);
      const result = await controller.create(mockCreateGroupDto);
      expect(result).toEqual(mockGroup);
      expect(service.create).toHaveBeenCalledWith(mockCreateGroupDto);
    });
  });

  describe('update', () => {
    it('should update a group and return with data wrapper', async () => {
      const updatedGroup = {
        ...mockGroup,
        ...mockUpdateGroupDto,
        recommendations: mockGroup.recommendations || [],
      };
      jest
        .spyOn(service, 'update')
        .mockResolvedValueOnce(updatedGroup as Group);
      const result = await controller.update('test-id', mockUpdateGroupDto);
      expect(result).toEqual(updatedGroup);
      expect(service.update).toHaveBeenCalledWith(
        'test-id',
        mockUpdateGroupDto,
      );
    });

    it('should propagate NotFoundException from service', async () => {
      jest
        .spyOn(service, 'update')
        .mockRejectedValueOnce(new NotFoundException('Group not found'));
      await expect(
        controller.update('not-found', mockUpdateGroupDto),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should soft delete a group', async () => {
      jest.spyOn(service, 'remove');
      await controller.remove('test-id');
      expect(service.remove).toHaveBeenCalledWith('test-id');
    });

    it('should propagate NotFoundException from service', async () => {
      jest
        .spyOn(service, 'remove')
        .mockRejectedValueOnce(new NotFoundException('Group not found'));
      await expect(controller.remove('not-found')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
