import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Int,
  Query,
  Resolver,
  ResolveField,
  Parent,
} from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { Company } from 'src/company/entities/company.entity';
import { AuthContext } from 'src/graphql.decorator';

import { SurfacedAndPublishedGroupsForRankingResponse } from './dto/paginated-ranking-groups.dto';
import { Group } from './entities/group.entity';
import { GroupService } from './group.service';

@Resolver(() => Group)
export class GroupResolver {
  constructor(private groupService: GroupService) {}

  @Query(() => [Group])
  async surfacedGroupsByCompany(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID }) companyId: string,
  ) {
    const canRead = await authContext.can(
      'recommendationGroup',
      'read',
      companyId,
    );

    if (!canRead) throw new ForbiddenException();

    const groups =
      await this.groupService.findSurfacedGroupsByCompany(companyId);

    return groups;
  }

  @Query(() => [Group])
  async groups(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
  ) {
    const canRead = await authContext.can(
      'recommendationGroup',
      'read',
      companyId,
    );

    if (!canRead) throw new ForbiddenException();

    const groups = await this.groupService.findAll(companyId);

    return groups;
  }

  @Query(() => [Group], {
    description:
      'Get all groups with surfaced or published status (superuser only)',
  })
  async surfacedAndPublishedGroups(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ) {
    // This query is for superusers only - they can see all companies
    const canRead = await authContext.can(
      'recommendationGroup',
      'read',
      undefined,
    );

    if (!canRead) throw new ForbiddenException('Superuser access required');

    // Return groups from ALL companies
    const groups = await this.groupService.findSurfacedAndPublishedGroups();

    return groups;
  }

  @Query(() => [Group], {
    description:
      'Get surfaced and published groups with minimal data for ranking operations (superuser only)',
  })
  async surfacedAndPublishedGroupsForRanking(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ) {
    // This query is for superusers only - they can see all companies
    const canRead = await authContext.can(
      'recommendationGroup',
      'read',
      undefined,
    );

    if (!canRead) throw new ForbiddenException('Superuser access required');

    // Return lightweight groups from ALL companies for ranking operations
    const groups =
      await this.groupService.findSurfacedAndPublishedGroupsForRanking();

    return groups;
  }

  @Query(() => SurfacedAndPublishedGroupsForRankingResponse, {
    description:
      'Get paginated surfaced and published groups for ranking operations (superuser only)',
  })
  async surfacedAndPublishedGroupsForRankingPaginated(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('page', { type: () => Int, defaultValue: 1 }) page: number,
    @Args('limit', { type: () => Int, defaultValue: 100 }) limit: number,
  ) {
    // This query is for superusers only - they can see all companies
    const canRead = await authContext.can(
      'recommendationGroup',
      'read',
      undefined,
    );

    if (!canRead) throw new ForbiddenException('Superuser access required');

    // Validate pagination parameters
    if (page < 1) {
      throw new Error('Page must be greater than 0');
    }
    if (limit < 1 || limit > 1000) {
      throw new Error('Limit must be between 1 and 1000');
    }

    // Return paginated lightweight groups from ALL companies for ranking operations
    return await this.groupService.findSurfacedAndPublishedGroupsForRankingPaginated(
      page,
      limit,
    );
  }

  @ResolveField(() => Company, { nullable: true })
  company(@Parent() group: Group): Company | null {
    if (!group.companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: group.companyId } as Company;
  }
}
