import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID, IsNotEmpty } from 'class-validator';
import { Factory } from 'nestjs-seeder';
import { Company } from 'src/company/entities/company.entity';
import { Group } from 'src/group/entities/group.entity';
import { ScheduledAction } from 'src/scheduled-action/entities/scheduled-action.entity';
import {
  PrimaryGeneratedColumn,
  Column,
  Index,
  Entity,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('group_scheduled_action')
@Index(['groupId', 'scheduledActionId'], { unique: true })
@Index(['groupId'])
@Index(['scheduledActionId'])
@ObjectType()
export class GroupScheduledAction {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'group_id' })
  @IsUUID()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  groupId: string;

  @Column({ type: 'uuid', name: 'scheduled_action_id' })
  @IsUUID()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  scheduledActionId: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @ManyToOne(() => Group, group => group.groupScheduledActions)
  @JoinColumn({ name: 'group_id' })
  @Field(() => Group)
  group: Group;

  @ManyToOne(
    () => ScheduledAction,
    scheduledAction => scheduledAction.groupScheduledActions,
  )
  @JoinColumn({ name: 'scheduled_action_id' })
  @Field(() => ScheduledAction)
  scheduledAction: ScheduledAction;

  @Field(() => ID, { description: 'Company ID from the related group' })
  get companyId(): string {
    return this.group?.companyId;
  }

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}
