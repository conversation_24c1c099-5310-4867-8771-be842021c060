import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionResolver } from './group-scheduled-action.resolver';
import { GroupScheduledActionService } from './group-scheduled-action.service';

describe('GroupScheduledActionResolver', () => {
  let resolver: GroupScheduledActionResolver;
  let service: jest.Mocked<GroupScheduledActionService>;

  const mockGroupScheduledAction: GroupScheduledAction = {
    id: 'test-id',
    groupId: 'group-id',
    scheduledActionId: 'scheduled-action-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    group: {
      id: 'group-id',
      companyId: 'company-id',
      title: 'Test Group',
      description: 'Test Description',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    scheduledAction: {
      id: 'scheduled-action-id',
      type: 'SURFACE',
      surfacedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    companyId: 'company-id',
  };

  const mockAuthContext = {
    can: jest.fn(),
    isSuper: jest.fn(),
  };

  const mockCanAccess = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    const mockService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupScheduledActionResolver,
        {
          provide: GroupScheduledActionService,
          useValue: mockService,
        },
      ],
    }).compile();

    resolver = module.get<GroupScheduledActionResolver>(
      GroupScheduledActionResolver,
    );
    service = module.get<GroupScheduledActionService>(
      GroupScheduledActionService,
    ) as jest.Mocked<GroupScheduledActionService>;

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all group scheduled actions when authorized and no companyId provided', async () => {
      mockCanAccess(true);
      service.findAll.mockResolvedValue([mockGroupScheduledAction]);

      const result = await resolver.findAll(mockAuthContext as any);

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        undefined,
      );
      expect(service.findAll).toHaveBeenCalledWith(undefined);
    });

    it('should return group scheduled actions filtered by companyId when authorized', async () => {
      const companyId = 'test-company-id';
      mockCanAccess(true);
      service.findAll.mockResolvedValue([mockGroupScheduledAction]);

      const result = await resolver.findAll(mockAuthContext as any, companyId);

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        companyId,
      );
      expect(service.findAll).toHaveBeenCalledWith(companyId);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockCanAccess(false);

      await expect(
        resolver.findAll(mockAuthContext as any, 'company-id'),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        'company-id',
      );
    });

    it('should handle service errors', async () => {
      mockCanAccess(true);
      const error = new Error('Service error');
      service.findAll.mockRejectedValue(error);

      await expect(
        resolver.findAll(mockAuthContext as any, 'company-id'),
      ).rejects.toThrow('Service error');
    });

    it('should handle empty results', async () => {
      mockCanAccess(true);
      service.findAll.mockResolvedValue([]);

      const result = await resolver.findAll(mockAuthContext as any);

      expect(result).toEqual([]);
    });
  });

  describe('findOne', () => {
    it('should return a single group scheduled action when authorized', async () => {
      mockCanAccess(true);
      service.findOne.mockResolvedValue(mockGroupScheduledAction);

      const result = await resolver.findOne(mockAuthContext as any, 'test-id');

      expect(result).toEqual(mockGroupScheduledAction);
      expect(service.findOne).toHaveBeenCalledWith('test-id');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        mockGroupScheduledAction.companyId,
      );
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockCanAccess(false);
      service.findOne.mockResolvedValue(mockGroupScheduledAction);

      await expect(
        resolver.findOne(mockAuthContext as any, 'test-id'),
      ).rejects.toThrow(ForbiddenException);

      expect(service.findOne).toHaveBeenCalledWith('test-id');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        mockGroupScheduledAction.companyId,
      );
    });

    it('should handle service errors when finding the group scheduled action', async () => {
      const error = new Error('Service error');
      service.findOne.mockRejectedValue(error);

      await expect(
        resolver.findOne(mockAuthContext as any, 'test-id'),
      ).rejects.toThrow('Service error');
    });

    it('should handle authorization errors', async () => {
      const authError = new Error('Auth error');
      service.findOne.mockResolvedValue(mockGroupScheduledAction);
      mockAuthContext.can.mockRejectedValue(authError);

      await expect(
        resolver.findOne(mockAuthContext as any, 'test-id'),
      ).rejects.toThrow('Auth error');
    });

    it('should handle group scheduled action without companyId', async () => {
      const actionWithoutCompany = {
        ...mockGroupScheduledAction,
        group: null,
        companyId: undefined,
      };
      mockCanAccess(true);
      service.findOne.mockResolvedValue(actionWithoutCompany as any);

      const result = await resolver.findOne(mockAuthContext as any, 'test-id');

      expect(result).toEqual(actionWithoutCompany);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        undefined,
      );
    });
  });
});
