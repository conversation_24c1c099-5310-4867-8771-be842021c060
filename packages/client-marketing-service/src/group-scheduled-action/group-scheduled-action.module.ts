import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionController } from './group-scheduled-action.controller';
import { GroupScheduledActionResolver } from './group-scheduled-action.resolver';
import { GroupScheduledActionService } from './group-scheduled-action.service';

@Module({
  imports: [TypeOrmModule.forFeature([GroupScheduledAction])],
  controllers: [GroupScheduledActionController],
  providers: [GroupScheduledActionService, GroupScheduledActionResolver],
  exports: [GroupScheduledActionService],
})
export class GroupScheduledActionModule {}
