import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DeleteResult } from 'typeorm';

import { CreateGroupScheduledActionDto } from './dto/create-group-scheduled-action.dto';
import { UpdateGroupScheduledActionDto } from './dto/update-group-scheduled-action.dto';
import { GroupScheduledAction } from './entities/group-scheduled-action.entity';

@Injectable()
export class GroupScheduledActionService {
  constructor(
    @InjectRepository(GroupScheduledAction)
    private readonly groupScheduledActionRepository: Repository<GroupScheduledAction>,
  ) {}

  async create(
    createGroupScheduledActionDto: CreateGroupScheduledActionDto,
  ): Promise<GroupScheduledAction> {
    return this.groupScheduledActionRepository.save(
      createGroupScheduledActionDto,
    );
  }

  async findAll(companyId?: string): Promise<GroupScheduledAction[]> {
    const queryBuilder = this.groupScheduledActionRepository
      .createQueryBuilder('groupScheduledAction')
      .leftJoinAndSelect('groupScheduledAction.group', 'group')
      .leftJoinAndSelect(
        'groupScheduledAction.scheduledAction',
        'scheduledAction',
      )
      .orderBy('groupScheduledAction.createdAt', 'DESC');

    if (companyId) {
      queryBuilder.where('group.companyId = :companyId', { companyId });
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<GroupScheduledAction> {
    const groupScheduledAction =
      await this.groupScheduledActionRepository.findOne({
        where: { id },
        relations: ['group', 'scheduledAction'],
      });
    if (!groupScheduledAction) {
      throw new NotFoundException(`GroupScheduledAction #${id} not found`);
    }
    return groupScheduledAction;
  }

  async update(
    id: string,
    updateGroupScheduledActionDto: UpdateGroupScheduledActionDto,
  ): Promise<GroupScheduledAction> {
    await this.groupScheduledActionRepository.update(
      id,
      updateGroupScheduledActionDto,
    );
    return this.findOne(id);
  }

  async remove(id: string): Promise<DeleteResult> {
    return await this.groupScheduledActionRepository.delete({ id });
  }
}
