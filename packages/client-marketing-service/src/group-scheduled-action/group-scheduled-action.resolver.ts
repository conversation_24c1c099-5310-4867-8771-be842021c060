import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Query,
  Resolver,
  ResolveField,
  Parent,
} from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { Company } from 'src/company/entities/company.entity';
import { AuthContext } from 'src/graphql.decorator';

import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionService } from './group-scheduled-action.service';

@Resolver(() => GroupScheduledAction)
export class GroupScheduledActionResolver {
  constructor(
    private readonly groupScheduledActionService: GroupScheduledActionService,
  ) {}

  @Query(() => [GroupScheduledAction], {
    name: 'groupScheduledActions',
    description: 'Get all group scheduled actions',
  })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
  ): Promise<GroupScheduledAction[]> {
    const canRead = await authContext.can('recommendation', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    return this.groupScheduledActionService.findAll(companyId);
  }

  @Query(() => GroupScheduledAction, {
    name: 'groupScheduledAction',
    description: 'Get a group scheduled action by ID',
  })
  async findOne(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<GroupScheduledAction> {
    const groupScheduledAction =
      await this.groupScheduledActionService.findOne(id);
    const canRead = await authContext.can(
      'recommendation',
      'read',
      groupScheduledAction.companyId,
    );

    if (!canRead) throw new ForbiddenException();

    return groupScheduledAction;
  }

  @ResolveField(() => Company, { nullable: true })
  company(
    @Parent() groupScheduledAction: GroupScheduledAction,
  ): Company | null {
    // GroupScheduledAction derives companyId from group relation
    const companyId = groupScheduledAction.group?.companyId;
    if (!companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: companyId } as Company;
  }
}
