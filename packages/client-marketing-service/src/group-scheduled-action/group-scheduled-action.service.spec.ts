import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DeleteResult } from 'typeorm';

import { CreateGroupScheduledActionDto } from './dto/create-group-scheduled-action.dto';
import { UpdateGroupScheduledActionDto } from './dto/update-group-scheduled-action.dto';
import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionService } from './group-scheduled-action.service';

describe('GroupScheduledActionService', () => {
  let service: GroupScheduledActionService;
  let repository: jest.Mocked<Repository<GroupScheduledAction>>;

  const mockGroupScheduledAction: GroupScheduledAction = {
    id: 'test-id',
    groupId: 'group-id',
    scheduledActionId: 'scheduled-action-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    group: {
      id: 'group-id',
      companyId: 'company-id',
      title: 'Test Group',
      description: 'Test Description',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    scheduledAction: {
      id: 'scheduled-action-id',
      type: 'SURFACE',
      surfacedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    companyId: 'company-id',
  };

  const mockCreateDto: CreateGroupScheduledActionDto = {
    groupId: 'group-id',
    scheduledActionId: 'scheduled-action-id',
  };

  const mockUpdateDto: UpdateGroupScheduledActionDto = {
    groupId: 'updated-group-id',
  };

  const mockQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([mockGroupScheduledAction]),
  };

  beforeEach(async () => {
    const mockRepository = {
      save: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupScheduledActionService,
        {
          provide: getRepositoryToken(GroupScheduledAction),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<GroupScheduledActionService>(
      GroupScheduledActionService,
    );
    repository = module.get<Repository<GroupScheduledAction>>(
      getRepositoryToken(GroupScheduledAction),
    ) as jest.Mocked<Repository<GroupScheduledAction>>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new group scheduled action', async () => {
      repository.save.mockResolvedValue(mockGroupScheduledAction);

      const result = await service.create(mockCreateDto);

      expect(result).toEqual(mockGroupScheduledAction);
      expect(repository.save).toHaveBeenCalledWith(mockCreateDto);
    });

    it('should handle repository errors during create', async () => {
      const error = new Error('Database error');
      repository.save.mockRejectedValue(error);

      await expect(service.create(mockCreateDto)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findAll', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return all group scheduled actions without companyId filter', async () => {
      const result = await service.findAll();

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(repository.createQueryBuilder).toHaveBeenCalledWith(
        'groupScheduledAction',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'groupScheduledAction.group',
        'group',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'groupScheduledAction.scheduledAction',
        'scheduledAction',
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'groupScheduledAction.createdAt',
        'DESC',
      );
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();
      expect(mockQueryBuilder.getMany).toHaveBeenCalled();
    });

    it('should return group scheduled actions filtered by companyId', async () => {
      const companyId = 'test-company-id';

      const result = await service.findAll(companyId);

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'group.companyId = :companyId',
        { companyId },
      );
    });

    it('should handle empty results', async () => {
      mockQueryBuilder.getMany.mockResolvedValue([]);

      const result = await service.findAll();

      expect(result).toEqual([]);
    });

    it('should handle query builder errors', async () => {
      const error = new Error('Query failed');
      mockQueryBuilder.getMany.mockRejectedValue(error);

      await expect(service.findAll()).rejects.toThrow('Query failed');
    });
  });

  describe('findOne', () => {
    it('should return a single group scheduled action', async () => {
      repository.findOne.mockResolvedValue(mockGroupScheduledAction);

      const result = await service.findOne('test-id');

      expect(result).toEqual(mockGroupScheduledAction);
      expect(repository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
        relations: ['group', 'scheduledAction'],
      });
    });

    it('should throw NotFoundException when group scheduled action is not found', async () => {
      repository.findOne.mockResolvedValue(null);

      await expect(service.findOne('not-found')).rejects.toThrow(
        new NotFoundException('GroupScheduledAction #not-found not found'),
      );
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      repository.findOne.mockRejectedValue(error);

      await expect(service.findOne('test-id')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('update', () => {
    it('should update a group scheduled action', async () => {
      const updatedAction = {
        ...mockGroupScheduledAction,
        groupId: 'updated-group-id',
        companyId: 'company-id',
      };
      repository.update.mockResolvedValue({ affected: 1 } as any);
      repository.findOne.mockResolvedValue(updatedAction);

      const result = await service.update('test-id', mockUpdateDto);

      expect(repository.update).toHaveBeenCalledWith('test-id', mockUpdateDto);
      expect(result).toEqual(updatedAction);
    });

    it('should throw NotFoundException when group scheduled action to update is not found', async () => {
      repository.update.mockResolvedValue({ affected: 1 } as any);
      repository.findOne.mockResolvedValue(null);

      await expect(service.update('not-found', mockUpdateDto)).rejects.toThrow(
        new NotFoundException('GroupScheduledAction #not-found not found'),
      );
    });

    it('should handle update repository errors', async () => {
      const error = new Error('Update failed');
      repository.update.mockRejectedValue(error);

      await expect(service.update('test-id', mockUpdateDto)).rejects.toThrow(
        'Update failed',
      );
    });

    it('should handle findOne errors after update', async () => {
      repository.update.mockResolvedValue({ affected: 1 } as any);
      const error = new Error('FindOne failed');
      repository.findOne.mockRejectedValue(error);

      await expect(service.update('test-id', mockUpdateDto)).rejects.toThrow(
        'FindOne failed',
      );
    });
  });

  describe('remove', () => {
    it('should remove a group scheduled action', async () => {
      const deleteResult: DeleteResult = { affected: 1, raw: [] };
      repository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove('test-id');

      expect(result).toEqual(deleteResult);
      expect(repository.delete).toHaveBeenCalledWith({ id: 'test-id' });
    });

    it('should handle delete repository errors', async () => {
      const error = new Error('Delete failed');
      repository.delete.mockRejectedValue(error);

      await expect(service.remove('test-id')).rejects.toThrow('Delete failed');
    });

    it('should return delete result even when no rows affected', async () => {
      const deleteResult: DeleteResult = { affected: 0, raw: [] };
      repository.delete.mockResolvedValue(deleteResult);

      const result = await service.remove('non-existent-id');

      expect(result).toEqual(deleteResult);
    });
  });
});
