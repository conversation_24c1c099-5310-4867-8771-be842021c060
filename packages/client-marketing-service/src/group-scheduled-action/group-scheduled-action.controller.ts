import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { CreateGroupScheduledActionDto } from './dto/create-group-scheduled-action.dto';
import { UpdateGroupScheduledActionDto } from './dto/update-group-scheduled-action.dto';
import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionService } from './group-scheduled-action.service';

@ApiTags('group-scheduled-actions')
@Controller('group-scheduled-actions')
export class GroupScheduledActionController {
  constructor(
    private readonly groupScheduledActionService: GroupScheduledActionService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new group scheduled action' })
  @ApiResponse({
    status: 201,
    description: 'The group scheduled action has been successfully created.',
  })
  create(
    @Body() createGroupScheduledActionDto: CreateGroupScheduledActionDto,
  ): Promise<GroupScheduledAction> {
    return this.groupScheduledActionService.create(
      createGroupScheduledActionDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all group scheduled actions' })
  @ApiResponse({
    status: 200,
    description: 'Return all group scheduled actions.',
  })
  findAll(
    @Query('companyId') companyId?: string,
  ): Promise<GroupScheduledAction[]> {
    return this.groupScheduledActionService.findAll(companyId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a group scheduled action by ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the group scheduled action.',
  })
  findOne(@Param('id') id: string): Promise<GroupScheduledAction> {
    return this.groupScheduledActionService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a group scheduled action' })
  @ApiResponse({
    status: 200,
    description: 'The group scheduled action has been successfully updated.',
  })
  update(
    @Param('id') id: string,
    @Body() updateGroupScheduledActionDto: UpdateGroupScheduledActionDto,
  ): Promise<GroupScheduledAction> {
    return this.groupScheduledActionService.update(
      id,
      updateGroupScheduledActionDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a group scheduled action' })
  @ApiResponse({
    status: 200,
    description: 'The group scheduled action has been successfully deleted.',
  })
  remove(@Param('id') id: string) {
    return this.groupScheduledActionService.remove(id);
  }
}
