import { Test, TestingModule } from '@nestjs/testing';
import { DeleteResult } from 'typeorm';

import { CreateGroupScheduledActionDto } from './dto/create-group-scheduled-action.dto';
import { UpdateGroupScheduledActionDto } from './dto/update-group-scheduled-action.dto';
import { GroupScheduledAction } from './entities/group-scheduled-action.entity';
import { GroupScheduledActionController } from './group-scheduled-action.controller';
import { GroupScheduledActionService } from './group-scheduled-action.service';

describe('GroupScheduledActionController', () => {
  let controller: GroupScheduledActionController;
  let service: jest.Mocked<GroupScheduledActionService>;

  const mockGroupScheduledAction: GroupScheduledAction = {
    id: 'test-id',
    groupId: 'group-id',
    scheduledActionId: 'scheduled-action-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    group: {
      id: 'group-id',
      companyId: 'company-id',
      title: 'Test Group',
      description: 'Test Description',
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    scheduledAction: {
      id: 'scheduled-action-id',
      type: 'SURFACE',
      surfacedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any,
    companyId: 'company-id',
  };

  const mockCreateDto: CreateGroupScheduledActionDto = {
    groupId: 'group-id',
    scheduledActionId: 'scheduled-action-id',
  };

  const mockUpdateDto: UpdateGroupScheduledActionDto = {
    groupId: 'updated-group-id',
  };

  beforeEach(async () => {
    const mockService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [GroupScheduledActionController],
      providers: [
        {
          provide: GroupScheduledActionService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<GroupScheduledActionController>(
      GroupScheduledActionController,
    );
    service = module.get<GroupScheduledActionService>(
      GroupScheduledActionService,
    ) as jest.Mocked<GroupScheduledActionService>;
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new group scheduled action', async () => {
      service.create.mockResolvedValue(mockGroupScheduledAction);

      const result = await controller.create(mockCreateDto);

      expect(result).toEqual(mockGroupScheduledAction);
      expect(service.create).toHaveBeenCalledWith(mockCreateDto);
    });

    it('should handle service errors during create', async () => {
      const error = new Error('Service error');
      service.create.mockRejectedValue(error);

      await expect(controller.create(mockCreateDto)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('findAll', () => {
    it('should return all group scheduled actions without companyId', async () => {
      service.findAll.mockResolvedValue([mockGroupScheduledAction]);

      const result = await controller.findAll();

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(service.findAll).toHaveBeenCalledWith(undefined);
    });

    it('should return group scheduled actions filtered by companyId', async () => {
      const companyId = 'test-company-id';
      service.findAll.mockResolvedValue([mockGroupScheduledAction]);

      const result = await controller.findAll(companyId);

      expect(result).toEqual([mockGroupScheduledAction]);
      expect(service.findAll).toHaveBeenCalledWith(companyId);
    });

    it('should handle empty results', async () => {
      service.findAll.mockResolvedValue([]);

      const result = await controller.findAll();

      expect(result).toEqual([]);
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      service.findAll.mockRejectedValue(error);

      await expect(controller.findAll()).rejects.toThrow('Service error');
    });
  });

  describe('findOne', () => {
    it('should return a single group scheduled action', async () => {
      service.findOne.mockResolvedValue(mockGroupScheduledAction);

      const result = await controller.findOne('test-id');

      expect(result).toEqual(mockGroupScheduledAction);
      expect(service.findOne).toHaveBeenCalledWith('test-id');
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      service.findOne.mockRejectedValue(error);

      await expect(controller.findOne('test-id')).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('update', () => {
    it('should update a group scheduled action', async () => {
      const updatedAction = {
        ...mockGroupScheduledAction,
        groupId: 'updated-group-id',
        companyId: 'company-id',
      };
      service.update.mockResolvedValue(updatedAction);

      const result = await controller.update('test-id', mockUpdateDto);

      expect(result).toEqual(updatedAction);
      expect(service.update).toHaveBeenCalledWith('test-id', mockUpdateDto);
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      service.update.mockRejectedValue(error);

      await expect(controller.update('test-id', mockUpdateDto)).rejects.toThrow(
        'Service error',
      );
    });
  });

  describe('remove', () => {
    it('should remove a group scheduled action', async () => {
      const deleteResult: DeleteResult = { affected: 1, raw: [] };
      service.remove.mockResolvedValue(deleteResult);

      const result = await controller.remove('test-id');

      expect(result).toEqual(deleteResult);
      expect(service.remove).toHaveBeenCalledWith('test-id');
    });

    it('should handle service errors', async () => {
      const error = new Error('Service error');
      service.remove.mockRejectedValue(error);

      await expect(controller.remove('test-id')).rejects.toThrow(
        'Service error',
      );
    });
  });
});
