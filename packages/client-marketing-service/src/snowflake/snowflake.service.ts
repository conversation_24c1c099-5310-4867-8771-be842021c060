import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as snowflake from 'snowflake-sdk';

import { DailyAdPerformanceDto } from './dto/daily-ad-performance.dto';
import { PaidAdLeadDto } from './dto/paid-ad-lead.dto';
import { AD_PERFORMANCE_QUERY, PAID_AD_LEAD_QUERY } from './snowflake.queries';

@Injectable()
export class SnowflakeService implements OnModuleDestroy {
  private connectionPool!: snowflake.Pool<snowflake.Connection>;
  private initializingPromise: Promise<void> | null = null;
  private readonly logger = new Logger(SnowflakeService.name);
  private readonly maxRetries = 3;
  private readonly baseDelay = 1000; // 1 second
  private readonly stagingTestAccounts = [
    'a7b4401f-a8be-440d-922f-7b133d4f2197',
    'ff10ccc3-2ee5-4966-adda-d05302040a3b',
  ];

  constructor(private readonly configService: ConfigService) {}

  async getConnectionPool(): Promise<snowflake.Pool<snowflake.Connection>> {
    if (!this.connectionPool) {
      this.logger.warn(
        'Connection pool not initialized, attempting to initialize...',
      );
      await this.createConnection();
    }

    this.logger.log(
      `[SnowflakeStatus] Total: ${this.connectionPool.size}, Idle: ${this.connectionPool.available}, In Use: ${this.connectionPool.borrowed}, Waiting: ${this.connectionPool.pending}`,
    );

    return this.connectionPool;
  }

  async createConnection() {
    try {
      // If initialization is already in progress, wait for it to complete to avoid multiple initialization
      if (this.initializingPromise) {
        return this.initializingPromise;
      }

      // Create a new initialization promise
      this.initializingPromise = (() => {
        return new Promise(resolve => {
          try {
            // Create pool with required parameters
            this.connectionPool = snowflake.createPool(
              {
                account: this.configService.get<string>('snowflake.account')!,
                username: this.configService.get<string>('snowflake.username'),
                database: this.configService.get<string>('snowflake.database'),
                authenticator: 'SNOWFLAKE_JWT',
                privateKey: this.configService.get<string>(
                  'snowflake.privateKey',
                )!,
              },
              {
                min: 1,
                max: 10,
              },
            );
          } finally {
            // Clear the initializing promise regardless of success or failure
            this.initializingPromise = null;
            resolve();
          }
        });
      })();

      // Wait for initialization to complete
      return this.initializingPromise;
    } catch (error) {
      this.logger.error(
        'Failed to initialize Snowflake connection pool:',
        error,
      );
      throw error;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isTerminatedConnectionError(error: any): boolean {
    return error.message && error.message.includes('terminated connection');
  }

  async query(sql: string, params: string[] = []): Promise<any> {
    try {
      await this.getConnectionPool();

      return this.connectionPool.use(
        async (connection: snowflake.Connection) => {
          return new Promise((resolve, reject) => {
            connection.execute({
              sqlText: sql,
              binds: params,
              complete: (err, _, rows) => {
                if (err) {
                  return reject(
                    new Error(`Snowflake query error: ${err.message}`),
                  );
                } else {
                  return resolve(rows || []);
                }
              },
            });
          });
        },
      );
    } catch (error) {
      this.logger.error('Failed to query Snowflake:', error);
      throw error;
    }
  }

  async queryWithRetries(sql: string, params: string[] = []): Promise<any> {
    let lastError: Error | null = null;
    let hasRetried = false;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        await this.getConnectionPool();

        const result = await this.connectionPool.use(
          async (connection: snowflake.Connection) => {
            return new Promise((resolve, reject) => {
              connection.execute({
                sqlText: sql,
                binds: params,
                complete: (err, _, rows) => {
                  if (err) {
                    // Check if this is a terminated connection error
                    if (this.isTerminatedConnectionError(err)) {
                      return reject(err); // Pass the original error to be handled by retry logic
                    }
                    return reject(
                      new Error(`Snowflake query error: ${err.message}`),
                    );
                  } else {
                    return resolve(rows || []);
                  }
                },
              });
            });
          },
        );

        // If we get here, the query succeeded
        if (hasRetried) {
          this.logger.log(
            `Snowflake query succeeded after retry (attempt ${attempt + 1}/${this.maxRetries + 1})`,
          );
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        // Check if this is a terminated connection error and we haven't exceeded max retries
        if (
          this.isTerminatedConnectionError(lastError) &&
          attempt < this.maxRetries
        ) {
          hasRetried = true;
          const delayMs = this.baseDelay * Math.pow(2, attempt); // Exponential backoff
          this.logger.warn(
            `Snowflake query failed with terminated connection error (attempt ${attempt + 1}/${this.maxRetries + 1}). Retrying in ${delayMs}ms...`,
            lastError.message,
          );
          await this.delay(delayMs);
          continue;
        }

        // If it's not a terminated connection error or we've exceeded retries, break
        break;
      }
    }

    // If we get here, all retries failed or it's not a retryable error
    this.logger.error(
      'Failed to query Snowflake after all retries:',
      lastError,
    );
    throw new Error(
      `Snowflake query failed: ${lastError?.message || 'Unknown error'}`,
    );
  }

  async onModuleDestroy() {
    try {
      if (this.connectionPool) {
        await this.connectionPool.drain();
      }
    } catch (error) {
      this.logger.error('Error draining connection pool:', error);

      throw error;
    }
  }

  async findPaidAdLeads(companyId: string): Promise<PaidAdLeadDto[]> {
    try {
      const rows = await this.queryWithRetries(PAID_AD_LEAD_QUERY, [
        // TODO: remove once we have better test data
        this.stagingTestAccounts.includes(companyId)
          ? '1df06684-def6-41f0-9573-35d629b74c37'
          : companyId,
      ]);

      return rows;
    } catch (error) {
      this.logger.error('Error fetching paid ad leads', {
        companyId,
        error,
      });

      throw error;
    }
  }

  // TODO: move to separate service?
  async findDailyAdPerformance(
    companyId: string,
  ): Promise<DailyAdPerformanceDto[]> {
    try {
      const rows = await this.queryWithRetries(AD_PERFORMANCE_QUERY, [
        // TODO: remove once we have better test data
        this.stagingTestAccounts.includes(companyId)
          ? '1df06684-def6-41f0-9573-35d629b74c37'
          : companyId,
      ]);

      return rows;
    } catch (error) {
      this.logger.error('Error fetching daily ad performance', {
        companyId,
        error,
      });

      throw error;
    }
  }
}
