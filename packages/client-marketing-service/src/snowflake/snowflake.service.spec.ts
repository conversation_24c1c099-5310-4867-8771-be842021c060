import { ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import * as snowflake from 'snowflake-sdk';

import { AD_PERFORMANCE_QUERY, PAID_AD_LEAD_QUERY } from './snowflake.queries';
import { SnowflakeService } from './snowflake.service';

const mockConnection = {
  execute: jest.fn(),
};

jest.mock('snowflake-sdk', () => ({
  createPool: jest.fn(),
}));

describe('SnowflakeService', () => {
  const companyId = 'test-company-id';
  const specialCompanyId = 'a7b4401f-a8be-440d-922f-7b133d4f2197';
  const replacementCompanyId = '1df06684-def6-41f0-9573-35d629b74c37';

  let service: SnowflakeService;
  let configService: jest.Mocked<ConfigService>;
  let mockPool: any;

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn(),
    };

    // Create mockPool here
    mockPool = {
      size: 10,
      available: 2,
      borrowed: 8,
      pending: 0,
      use: jest.fn(),
      drain: jest.fn(),
    };

    // Set up the snowflake mock to return our mockPool
    (snowflake.createPool as jest.Mock).mockReturnValue(mockPool);

    const module = await Test.createTestingModule({
      providers: [
        SnowflakeService,
        { provide: ConfigService, useValue: mockConfigService },
      ],
    }).compile();

    service = module.get<SnowflakeService>(SnowflakeService);
    configService = module.get<ConfigService>(
      ConfigService,
    ) as jest.Mocked<ConfigService>;

    // Reset mocks first
    jest.clearAllMocks();

    // Mock the logger after clearing
    jest.spyOn(service['logger'], 'log').mockImplementation();
    jest.spyOn(service['logger'], 'error').mockImplementation();
    jest.spyOn(service['logger'], 'warn').mockImplementation();

    // Setup default config values
    configService.get.mockImplementation((key: string) => {
      switch (key) {
        case 'snowflake.account':
          return 'test-account';
        case 'snowflake.username':
          return 'test-username';
        case 'snowflake.database':
          return 'test-database';
        case 'snowflake.privateKey':
          return 'test-private-key';
        default:
          return 'mock-config-value';
      }
    });

    // Reset the createPool mock after clearing all mocks
    (snowflake.createPool as jest.Mock).mockReturnValue(mockPool);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createConnection', () => {
    it('should create connection pool with correct configuration', async () => {
      await service.createConnection();

      expect(snowflake.createPool).toHaveBeenCalledWith(
        {
          account: 'test-account',
          username: 'test-username',
          database: 'test-database',
          authenticator: 'SNOWFLAKE_JWT',
          privateKey: 'test-private-key',
        },
        {
          min: 1,
          max: 10,
        },
      );
    });

    it('should handle concurrent initialization attempts', async () => {
      const promises = [
        service.createConnection(),
        service.createConnection(),
        service.createConnection(),
      ];

      await Promise.all(promises);

      // Should only create pool once despite multiple calls
      expect(snowflake.createPool).toHaveBeenCalledTimes(1);
    });

    it('should handle initialization errors gracefully', async () => {
      (snowflake.createPool as jest.Mock).mockImplementationOnce(() => {
        throw new Error('Pool creation failed');
      });

      // The service catches errors in the promise, so we don't expect it to throw
      // Instead, the error is caught and logged, but initialization completes
      await expect(service.createConnection()).resolves.toBeUndefined();
    });
  });

  describe('getConnectionPool', () => {
    it('should return existing connection pool', async () => {
      // First call should create the pool
      await service.createConnection();

      const pool = await service.getConnectionPool();

      expect(pool).toBe(mockPool);
    });

    it('should create connection pool if not initialized', async () => {
      const pool = await service.getConnectionPool();

      expect(snowflake.createPool).toHaveBeenCalled();
      expect(pool).toBe(mockPool);
    });

    it('should log connection pool status', async () => {
      const logSpy = jest.spyOn(service['logger'], 'log').mockImplementation();

      await service.getConnectionPool();

      expect(logSpy).toHaveBeenCalledWith(
        '[SnowflakeStatus] Total: 10, Idle: 2, In Use: 8, Waiting: 0',
      );
    });
  });

  describe('query', () => {
    beforeEach(() => {
      mockPool.use.mockImplementation(async callback => {
        return await callback(mockConnection);
      });
    });

    it('should execute query successfully', async () => {
      const mockRows = [{ id: 1, name: 'test' }];
      mockConnection.execute.mockImplementation(({ complete }) => {
        complete(null, null, mockRows);
      });

      const result = await service.query('SELECT * FROM table', ['param1']);

      expect(mockConnection.execute).toHaveBeenCalledWith({
        sqlText: 'SELECT * FROM table',
        binds: ['param1'],
        complete: expect.any(Function),
      });
      expect(result).toEqual(mockRows);
    });

    it('should handle query execution errors', async () => {
      const error = new Error('Query execution failed');
      mockConnection.execute.mockImplementation(({ complete }) => {
        complete(error, null, null);
      });

      await expect(service.query('SELECT * FROM table')).rejects.toThrow(
        'Snowflake query error: Query execution failed',
      );
    });

    it('should handle empty result sets', async () => {
      mockConnection.execute.mockImplementation(({ complete }) => {
        complete(null, null, null);
      });

      const result = await service.query('SELECT * FROM table');

      expect(result).toEqual([]);
    });

    it('should handle query with default empty parameters', async () => {
      const mockRows = [{ id: 1 }];
      mockConnection.execute.mockImplementation(({ complete }) => {
        complete(null, null, mockRows);
      });

      const result = await service.query('SELECT * FROM table');

      expect(mockConnection.execute).toHaveBeenCalledWith({
        sqlText: 'SELECT * FROM table',
        binds: [],
        complete: expect.any(Function),
      });
      expect(result).toEqual(mockRows);
    });

    it('should handle connection pool errors', async () => {
      mockPool.use.mockRejectedValue(new Error('Pool error'));

      await expect(service.query('SELECT * FROM table')).rejects.toThrow(
        'Pool error',
      );
    });

    it('should log query errors when getConnectionPool fails', async () => {
      // Mock getConnectionPool to throw an error to test the catch block
      jest
        .spyOn(service, 'getConnectionPool')
        .mockRejectedValue(
          new Error('Snowflake query failed: Connection failed'),
        );

      await expect(service.query('SELECT * FROM table')).rejects.toThrow(
        'Snowflake query failed: Connection failed',
      );

      expect(service['logger'].error).toHaveBeenCalledWith(
        'Failed to query Snowflake:',
        expect.any(Error),
      );
    });

    it('should retry on terminated connection error', async () => {
      // Mock the connection pool to be initialized
      service['connectionPool'] = mockPool;

      let callCount = 0;
      const mockRows = [{ id: 1, name: 'test' }];

      mockConnection.execute.mockImplementation(({ complete }) => {
        callCount++;
        if (callCount === 1) {
          // First attempt fails with terminated connection error
          complete(
            new Error(
              'Unable to perform operation using terminated connection.',
            ),
            null,
            null,
          );
        } else {
          // Second attempt succeeds
          complete(null, null, mockRows);
        }
      });

      const result = await service.queryWithRetries('SELECT * FROM table');

      expect(callCount).toBe(2);
      expect(result).toEqual(mockRows);
      expect(service['logger'].warn).toHaveBeenCalledWith(
        'Snowflake query failed with terminated connection error (attempt 1/4). Retrying in 1000ms...',
        'Unable to perform operation using terminated connection.',
      );
      expect(service['logger'].log).toHaveBeenCalledWith(
        'Snowflake query succeeded after retry (attempt 2/4)',
      );
    });

    it('should not retry on non-terminated connection errors', async () => {
      // Mock the connection pool to be initialized
      service['connectionPool'] = mockPool;

      const error = new Error('Some other error');
      mockConnection.execute.mockImplementation(({ complete }) => {
        complete(error, null, null);
      });

      await expect(service.query('SELECT * FROM table')).rejects.toThrow(
        'Snowflake query error: Some other error',
      );

      expect(mockConnection.execute).toHaveBeenCalledTimes(1);
      expect(service['logger'].warn).not.toHaveBeenCalled();
    });
  });

  describe('onModuleDestroy', () => {
    it('should drain connection pool successfully', async () => {
      await service.createConnection();
      mockPool.drain.mockResolvedValue(undefined);

      await service.onModuleDestroy();

      expect(mockPool.drain).toHaveBeenCalled();
    });

    it('should handle drain errors', async () => {
      await service.createConnection();
      const error = new Error('Drain failed');
      mockPool.drain.mockRejectedValue(error);

      await expect(service.onModuleDestroy()).rejects.toThrow('Drain failed');

      expect(service['logger'].error).toHaveBeenCalledWith(
        'Error draining connection pool:',
        error,
      );
    });

    it('should handle case when connection pool is not initialized', async () => {
      await service.onModuleDestroy();

      expect(mockPool.drain).not.toHaveBeenCalled();
    });
  });

  describe('findDailyAdPerformance', () => {
    it('should fetch data from Snowflake with regular company ID', async () => {
      const mockRows = [{ date: '2023-01-01', impressions: 100 }];
      const querySpy = jest
        .spyOn(service, 'queryWithRetries')
        .mockResolvedValue(mockRows);

      const result = await service.findDailyAdPerformance(companyId);

      expect(querySpy).toHaveBeenCalledWith(AD_PERFORMANCE_QUERY, [companyId]);
      expect(result).toEqual(mockRows);
    });

    it('should use replacement company ID for special company ID', async () => {
      const mockRows = [{ date: '2023-01-01', impressions: 100 }];
      const querySpy = jest
        .spyOn(service, 'queryWithRetries')
        .mockResolvedValue(mockRows);

      const result = await service.findDailyAdPerformance(specialCompanyId);

      expect(querySpy).toHaveBeenCalledWith(AD_PERFORMANCE_QUERY, [
        replacementCompanyId,
      ]);
      expect(result).toEqual(mockRows);
    });

    it('should handle query errors', async () => {
      const error = new Error('Query failed');
      jest.spyOn(service, 'queryWithRetries').mockRejectedValue(error);
      const errorSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();

      await expect(service.findDailyAdPerformance(companyId)).rejects.toThrow(
        'Query failed',
      );

      expect(errorSpy).toHaveBeenCalledWith(
        'Error fetching daily ad performance',
        {
          companyId,
          error,
        },
      );
    });

    it('should return empty array when no data found', async () => {
      jest.spyOn(service, 'queryWithRetries').mockResolvedValue([]);

      const result = await service.findDailyAdPerformance(companyId);

      expect(result).toEqual([]);
    });
  });

  describe('findPaidAdLeads', () => {
    const mockRows = [
      {
        LEAD_CREATED_DATE: '2025-08-01',
        LP_COMPANY_ID: companyId,
        LP_LEAD_ID: 'lead-123',
      },
    ];

    it('should fetch data from Snowflake with regular company ID', async () => {
      const querySpy = jest
        .spyOn(service, 'queryWithRetries')
        .mockResolvedValue(mockRows);

      const result = await service.findPaidAdLeads(companyId);

      expect(querySpy).toHaveBeenCalledWith(PAID_AD_LEAD_QUERY, [companyId]);
      expect(result).toEqual(mockRows);
    });

    it('should use replacement company ID for special company ID', async () => {
      const querySpy = jest
        .spyOn(service, 'queryWithRetries')
        .mockResolvedValue(mockRows);

      const result = await service.findPaidAdLeads(specialCompanyId);

      expect(querySpy).toHaveBeenCalledWith(PAID_AD_LEAD_QUERY, [
        replacementCompanyId,
      ]);
      expect(result).toEqual(mockRows);
    });

    it('should handle query errors', async () => {
      const error = new Error('Query failed');
      jest.spyOn(service, 'queryWithRetries').mockRejectedValue(error);
      const errorSpy = jest
        .spyOn(service['logger'], 'error')
        .mockImplementation();

      await expect(service.findPaidAdLeads(companyId)).rejects.toThrow(
        'Query failed',
      );

      expect(errorSpy).toHaveBeenCalledWith('Error fetching paid ad leads', {
        companyId,
        error,
      });
    });

    it('should return empty array when no data found', async () => {
      jest.spyOn(service, 'queryWithRetries').mockResolvedValue([]);

      const result = await service.findPaidAdLeads(companyId);

      expect(result).toEqual([]);
    });
  });
});
