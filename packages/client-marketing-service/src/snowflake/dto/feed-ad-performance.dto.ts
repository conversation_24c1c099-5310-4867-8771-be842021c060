import { Field, Int, ObjectType } from '@nestjs/graphql';

@ObjectType()
class PaidLead {
  @Field(() => String)
  id: string;
}

@ObjectType()
export class FeedAdPerformance {
  @Field(() => Int)
  impressionsCount: number;

  @Field(() => Int)
  clicksCount: number;

  @Field(() => Int)
  leadsCount: number;

  @Field(() => String)
  impressionsCountFormatted: string;

  @Field(() => String)
  clicksCountFormatted: string;

  @Field(() => String)
  leadsCountFormatted: string;

  @Field(() => [PaidLead])
  paidLeads: PaidLead[];

  @Field(() => String)
  paidLeadsHref: string;
}
