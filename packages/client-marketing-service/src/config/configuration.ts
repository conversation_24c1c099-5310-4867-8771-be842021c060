export default () => ({
  nodeEnv: process.env.NODE_ENV || 'development',
  applicationShutdownDelay: '31000',
  websiteService: {
    url: process.env.WEBSITE_SERVICE_URL || 'http://localhost:8003',
  },
  tenantService: {
    url: process.env.TENANT_SERVICE_URL || 'http://localhost:8011',
  },
  cmsService: {
    url: process.env.CMS_SERVICE_URL || 'https://cms-service.luxurycoders.com',
  },
  apiGateway: {
    url: process.env.API_GATEWAY_URL || 'https://gw.luxurycoders.com',
    key: process.env.API_GATEWAY_KEY || 'localkey',
  },
  // notification service default port is the same as tenant service...
  // if you are running both, change either to a different port
  notificationService: {
    url: process.env.NOTIFICATION_SERVICE_URL || 'http://localhost:8011',
    emailTemplateId:
      process.env.EMAIL_TEMPLATE_ID || 'd-cc46493aba224cbcb2b41598c5654776',
  },
  langfuse: {
    secretKey: process.env.LANGFUSE_SECRET_KEY,
    publicKey: process.env.LANGFUSE_PUBLIC_KEY,
    baseUrl: process.env.LANGFUSE_HOST || 'https://us.cloud.langfuse.com',
    environment: process.env.LANGFUSE_ENVIRONMENT || 'default',
    flushAt: process.env.LANGFUSE_FLUSH_AT || 1,
  },
  recommendationQueue: {
    // default cron pattern is 12:00PM on weekdays
    cronPattern: process.env.RECOMMENDATION_CRON_PATTERN || '0 0 16 * * 1-5',
    recommendationApplyDelayDays: 7,
  },
  auth0Config: {
    auth0Domain: process.env.AUTH0_DOMAIN || 'presence-staging.us.auth0.com',
    hostname: process.env.AUTH0_HOSTNAME || 'https://graphql.luxurycoders.com',
    acceptedIssuers:
      process.env.AUTH0_ACCEPTED_ISSUERS ||
      'https://login.luxurycoders.com/ https://presence-staging.us.auth0.com/',
    namespace: process.env.AUTH0_NAMESPACE || 'https://luxurycoders.com/',
  },
  m2mSuperApiKeys: process.env.M2M_SUPER_API_KEYS
    ? process.env.M2M_SUPER_API_KEYS.split(',')
    : process.env.NODE_ENV === 'production' ||
        process.env.NODE_ENV === 'staging'
      ? [] // Empty array for staging/production when not explicitly set
      : ['localkey'], // Default for local development
  snowflake: {
    account: process.env.SNOWFLAKE_ACCOUNT,
    username: process.env.SNOWFLAKE_USER,
    privateKey: process.env.SNOWFLAKE_PRIVATE_KEY,
  },
  campaign: {
    promoBucket: process.env.PROMO_S3_BUCKET || 'luxury-presence-promo-dev',
    sqsUrl: process.env.PROMO_SQS_URL || '',
    snsTopicArn: process.env.PROMO_SNS_TOPIC_ARN || '',
    notificationStrategy:
      process.env.CAMPAIGN_NOTIFICATION_STRATEGY || 'linear', // 'sqs', 'linear', or 'both' - defaults to 'linear' to avoid runtime errors when SQS is not configured
    maxRetries: process.env.CAMPAIGN_MAX_RETRIES || '5',
  },
  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
  },
  linear: {
    apiKey: process.env.LINEAR_API_KEY || '',
    teamId: process.env.LINEAR_TEAM_ID || '',
    projectId: process.env.LINEAR_PROJECT_ID || '',
  },
  // Campaign orchestration mode: 'full' (production), 'payment-only' (testing), 'mock' (development)
  campaignOrchestrationMode: process.env.CAMPAIGN_ORCHESTRATION_MODE || 'full',
});
