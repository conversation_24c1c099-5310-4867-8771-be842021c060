import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ConfigValidationService implements OnModuleInit {
  private readonly logger = new Logger(ConfigValidationService.name);

  constructor(private readonly configService: ConfigService) {}

  onModuleInit(): void {
    this.validateCampaignNotificationConfig();
  }

  private validateCampaignNotificationConfig(): void {
    const notificationStrategy =
      this.configService.get<string>('campaign.notificationStrategy') ||
      'linear';
    const sqsUrl = this.configService.get<string>('campaign.sqsUrl', '');

    this.logger.log(
      `Campaign notification strategy configured as: '${notificationStrategy}'`,
    );

    // Check if strategy requires SQS configuration
    if (notificationStrategy === 'sqs' || notificationStrategy === 'both') {
      // For SQS strategy, we need the SQS URL configured
      if (!sqsUrl) {
        const errorMessage = `Configuration error: Notification strategy is set to '${notificationStrategy}' but CAMPAIGN_SQS_URL is not configured. Please either:
1. Set CAMPAIGN_SQS_URL environment variable
2. Change CAMPAIGN_NOTIFICATION_STRATEGY to 'linear' to use Linear tickets only
3. Set CAMPAIGN_NOTIFICATION_STRATEGY explicitly to avoid using SQS`;

        this.logger.error(errorMessage);

        // In production, we should fail fast to avoid silent failures
        if (process.env.NODE_ENV === 'production') {
          throw new Error(errorMessage);
        } else {
          // In development/test, warn but continue (no automatic fallback)
          this.logger.warn(
            `Unsupported strategy '${notificationStrategy}' detected but no automatic fallback will be performed in non-production environment`,
          );
        }
      } else {
        this.logger.log(
          `SQS URL configured for '${notificationStrategy}' strategy: ${sqsUrl.substring(0, 30)}...`,
        );
      }
    }

    // Log the current configuration for debugging
    this.logger.log(
      'Campaign notification configuration validated successfully',
    );
  }
}
