base:
  ENV: ${ENVIRONMENT}
  hostname: https://client-marketing-service.${BASE_DOMAIN}
  WEBSITE_SERVICE_URL: http://website-service.${INTERNAL_BASE_DOMAIN}
  CMS_SERVICE_URL: http://cms-service.${INTERNAL_BASE_DOMAIN}
  API_GATEWAY_URL: http://api-gateway.${INTERNAL_BASE_DOMAIN}
  TENANT_SERVICE_URL: http://tenant-service.${INTERNAL_BASE_DOMAIN}
  NOTIFICATION_SERVICE_URL: http://notification-service.${INTERNAL_BASE_DOMAIN}
  LANGFUSE_SECRET_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_SECRET_KEY
  LANGFUSE_PUBLIC_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_PUBLIC_KEY
  REDIS_HOST: seo-automation-api-gw-redis-1-staging.ys9bfe.ng.0001.use1.cache.amazonaws.com
  REDIS_PORT: 6379
  M2M_SUPER_API_KEYS: vault:secret/data/${VAULT_ENV}/standard#DOMAIN_M2M_KEY
  SNOWFLAKE_ACCOUNT: vault:secret/data/${VAULT_ENV}/client-marketing-service#SNOWFLAKE_ACCOUNT
  SNOWFLAKE_USER: vault:secret/data/${VAULT_ENV}/client-marketing-service#SNOWFLAKE_USER
  SNOWFLAKE_PRIVATE_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#SNOWFLAKE_PRIVATE_KEY
  API_GATEWAY_KEY: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_KEY
  AUTH0_DOMAIN: vault:secret/data/${VAULT_ENV}/standard#AUTH0_DOMAIN
  AUTH0_HOSTNAME: https://graphql.${BASE_DOMAIN}
  AUTH0_ACCEPTED_ISSUERS: https://login.${BASE_DOMAIN}/ https://#{vault:secret/data/${VAULT_ENV}/standard#AUTH0_DOMAIN}/
  AUTH0_NAMESPACE: https://${BASE_DOMAIN}/

production:
  REDIS_HOST: seo-automation-api-gw-redis-1-production.i5emba.ng.0001.use1.cache.amazonaws.com
