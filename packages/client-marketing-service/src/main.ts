/* eslint-disable import/order */
import * as dotenv from 'dotenv';

dotenv.config();
import './common/utils/dd-tracer.util';
import { ValidationPipe } from '@nestjs/common';
import { ClassSerializerInterceptor } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { LoggerService } from '@luxury-presence/nestjs-logger';
import { AppModule } from 'src/app.module';
import { AllExceptionsFilter } from 'src/common/filters/all-exceptions.filter';
import { WrapResponseInterceptor } from 'src/common/interceptors/response-wrapper.interceptor';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // TODO: Replace with library that validate client domains and trusted domains
  app.enableCors({
    origin: true,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    credentials: true,
    preflightContinue: false,
  });

  app.useLogger(app.get(LoggerService));

  const apiPrefix = '/api';
  const apiVersion = 'v1';

  app.setGlobalPrefix(apiPrefix + '/' + apiVersion, {
    exclude: ['queues/*splat', 'health', 'graphql'],
  });

  app.useGlobalFilters(new AllExceptionsFilter());
  app.useGlobalInterceptors(new WrapResponseInterceptor());
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
      enableDebugMessages: true,
    }),
  );

  const config = new DocumentBuilder()
    .setTitle('SEO Automation API')
    .setDescription('API for SEO automation')
    .setVersion(apiVersion)
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  await app.listen(process.env.PORT ?? 3000);
  console.log(`Listening on port ${process.env.PORT ?? 3000}`);
  console.log(`API documentation available at /api`);
}
bootstrap();
