import { Test, TestingModule } from '@nestjs/testing';

import { createPolicyMock } from './service-mock.factory';

/**
 * Builder class for creating test modules with common patterns
 */
export class TestModuleBuilder {
  private providers: any[] = [];

  /**
   * Adds a resolver to the test module
   * @param resolver Resolver class
   * @returns Builder instance for chaining
   */
  withResolver(resolver: any): TestModuleBuilder {
    this.providers.push(resolver);
    return this;
  }

  /**
   * Adds a service mock to the test module
   * @param serviceClass Service class
   * @param mockImplementation Mock implementation
   * @returns Builder instance for chaining
   */
  withService(serviceClass: any, mockImplementation: any): TestModuleBuilder {
    this.providers.push({
      provide: serviceClass,
      useValue: mockImplementation,
    });
    return this;
  }

  /**
   * Adds a policy mock to the test module
   * @param policyClass Policy class
   * @param mockImplementation Optional custom mock implementation
   * @returns Builder instance for chaining
   */
  withPolicy(policyClass: any, mockImplementation?: any): TestModuleBuilder {
    this.providers.push({
      provide: policyClass,
      useValue: mockImplementation || createPolicyMock(),
    });
    return this;
  }

  /**
   * Adds a DataLoader mock to the test module
   * @param dataLoaderClass DataLoader class
   * @param mockImplementation Mock implementation
   * @returns Builder instance for chaining
   */
  withDataLoader(
    dataLoaderClass: any,
    mockImplementation: any,
  ): TestModuleBuilder {
    this.providers.push({
      provide: dataLoaderClass,
      useValue: mockImplementation,
    });
    return this;
  }

  /**
   * Adds a custom provider to the test module
   * @param provider Provider configuration
   * @returns Builder instance for chaining
   */
  withProvider(provider: any): TestModuleBuilder {
    this.providers.push(provider);
    return this;
  }

  /**
   * Builds and compiles the test module
   * @returns Compiled testing module
   */
  async build(): Promise<TestingModule> {
    return Test.createTestingModule({
      providers: this.providers,
    }).compile();
  }
}

/**
 * Creates a new test module builder
 * @returns New TestModuleBuilder instance
 */
export const createTestModuleBuilder = (): TestModuleBuilder => {
  return new TestModuleBuilder();
};

/**
 * Helper function to create a basic resolver test module
 * @param resolver Resolver class
 * @param service Service class
 * @param serviceMock Service mock implementation
 * @param policyClass Optional policy class
 * @returns Compiled testing module
 */
export const createBasicResolverModule = async (
  resolver: any,
  service: any,
  serviceMock: any,
  policyClass?: any,
): Promise<TestingModule> => {
  const builder = createTestModuleBuilder()
    .withResolver(resolver)
    .withService(service, serviceMock);

  if (policyClass) {
    builder.withPolicy(policyClass);
  }

  return builder.build();
};
