import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

import {
  mockAuthorizationSuccess,
  mockAuthorizationFailure,
  mockAuthorizationError,
} from './auth.utils';

/**
 * Tests authorization success scenario for a resolver method
 * @param resolverMethod Function to test
 * @param authContext Mock auth context
 * @param resource Resource name for authorization
 * @param action Action name for authorization
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testAuthorizationSuccess = (
  resolverMethod: (...args: any[]) => Promise<any>,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  action: string,
  ...args: any[]
) => {
  return async () => {
    mockAuthorizationSuccess(authContext);

    await resolverMethod(...args);

    expect(authContext.can).toHaveBeenCalledWith(
      resource,
      action,
      expect.anything(),
    );
  };
};

/**
 * Tests authorization failure scenario for a resolver method
 * @param resolverMethod Function to test
 * @param authContext Mock auth context
 * @param resource Resource name for authorization
 * @param action Action name for authorization
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testAuthorizationFailure = (
  resolverMethod: (...args: any[]) => Promise<any>,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  action: string,
  ...args: any[]
) => {
  return async () => {
    mockAuthorizationFailure(authContext);

    await expect(resolverMethod(...args)).rejects.toThrow(ForbiddenException);

    expect(authContext.can).toHaveBeenCalledWith(
      resource,
      action,
      expect.anything(),
    );
  };
};

/**
 * Tests authorization error scenario for a resolver method
 * @param resolverMethod Function to test
 * @param authContext Mock auth context
 * @param error Error to throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testAuthorizationError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  error: Error,
  ...args: any[]
) => {
  return async () => {
    mockAuthorizationError(authContext, error);

    await expect(resolverMethod(...args)).rejects.toThrow(error);
  };
};

/**
 * Creates a suite of common authorization tests for a resolver method
 * @param testName Name for the test suite
 * @param resolverMethod Function to test
 * @param authContext Mock auth context
 * @param resource Resource name for authorization
 * @param action Action name for authorization
 * @param args Arguments to pass to resolver method
 */
export const createAuthorizationTestSuite = (
  testName: string,
  resolverMethod: (...args: any[]) => Promise<any>,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  action: string,
  ...args: any[]
) => {
  describe(`${testName} authorization`, () => {
    it(
      'should succeed when authorized',
      testAuthorizationSuccess(
        resolverMethod,
        authContext,
        resource,
        action,
        ...args,
      ),
    );

    it(
      'should throw ForbiddenException when not authorized',
      testAuthorizationFailure(
        resolverMethod,
        authContext,
        resource,
        action,
        ...args,
      ),
    );

    it('should handle authorization context errors', async () => {
      const authError = new Error('Auth context error');
      await testAuthorizationError(
        resolverMethod,
        authContext,
        authError,
        ...args,
      )();
    });
  });
};

/**
 * Test helper for super user only methods
 * @param resolverMethod Function to test
 * @param authContext Mock auth context
 * @param args Arguments to pass to resolver method
 */
export const testSuperUserOnly = (
  resolverMethod: (...args: any[]) => Promise<any>,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  ...args: any[]
) => {
  describe('super user authorization', () => {
    it('should succeed for super users', async () => {
      authContext.can.mockResolvedValue(true);

      await resolverMethod(...args);

      expect(authContext.can).toHaveBeenCalled();
    });

    it('should throw ForbiddenException for non-super users', async () => {
      authContext.can.mockResolvedValue(false);

      await expect(resolverMethod(...args)).rejects.toThrow(ForbiddenException);
    });
  });
};
