# Test Utilities

This directory contains reusable test utilities to reduce code duplication and standardize testing patterns across resolver and service tests.

## Overview

The test utilities are organized into several modules:

- **`auth.utils.ts`** - Authentication context mocking utilities
- **`service-mock.factory.ts`** - Service and repository mock factories
- **`module.builder.ts`** - Test module building utilities
- **`data.factory.ts`** - Mock data factories and deep copy utilities
- **`auth-test.helpers.ts`** - Authorization testing helpers
- **`error-test.helpers.ts`** - Error handling test helpers
- **`common-test-suites.ts`** - Complete test suites for common patterns

## Quick Start

### Basic Resolver Test Setup

```typescript
import {
  createMockAuthContext,
  mockAuthorizationSuccess,
  createPageServiceMock,
  createBasicResolverModule,
  createMockScrapedPage,
  testResolverDefinition,
} from 'src/test-utils';

describe('PageResolver', () => {
  let resolver: PageResolver;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockPageService: ReturnType<typeof createPageServiceMock>;

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockPageService = createPageServiceMock();

    const module = await createBasicResolverModule(
      PageResolver,
      PageService,
      mockPageService,
      PagePolicy,
    );

    resolver = module.get<PageResolver>(PageResolver);
  });

  testResolverDefinition(resolver);

  it('should return pages when authorized', async () => {
    mockAuthorizationSuccess(mockAuthContext);
    const mockPages = [createMockScrapedPage()];
    mockPageService.findAll.mockResolvedValue(mockPages);

    const result = await resolver.findAll(mockAuthContext);

    expect(result).toEqual(mockPages);
  });
});
```

## Utilities Reference

### Auth Utilities (`auth.utils.ts`)

#### `createMockAuthContext()`
Creates a mock authentication context with all common methods mocked.

```typescript
const mockAuthContext = createMockAuthContext();
```

#### `mockAuthorizationSuccess(authContext, value?)`
Sets up the auth context to return successful authorization.

```typescript
mockAuthorizationSuccess(mockAuthContext); // Returns true
mockAuthorizationSuccess(mockAuthContext, false); // Returns false
```

#### `mockAuthorizationFailure(authContext)`
Sets up the auth context to return failed authorization.

#### `mockAuthorizationError(authContext, error)`
Sets up the auth context to throw an error during authorization.

### Service Mock Factory (`service-mock.factory.ts`)

#### `createServiceMock(methods?)`
Creates a generic service mock with specified methods.

```typescript
const mockService = createServiceMock(['findAll', 'findOne', 'create']);
```

#### Specific Service Mocks
- `createPageServiceMock()` - Page service with common methods
- `createRecommendationServiceMock()` - Recommendation service with common methods
- `createGroupServiceMock()` - Group service with common methods
- `createScheduledActionServiceMock()` - Scheduled action service with common methods

#### `createRepositoryMock(methods?)`
Creates a TypeORM repository mock.

#### `createPolicyMock()`
Creates a policy mock with common authorization methods.

#### `createDataLoaderMock()`
Creates a DataLoader mock for testing GraphQL field resolvers.

### Module Builder (`module.builder.ts`)

#### `createTestModuleBuilder()`
Creates a fluent builder for test modules.

```typescript
const module = await createTestModuleBuilder()
  .withResolver(MyResolver)
  .withService(MyService, mockService)
  .withPolicy(MyPolicy)
  .withDataLoader(MyDataLoader, mockDataLoader)
  .build();
```

#### `createBasicResolverModule(resolver, service, serviceMock, policy?)`
Quick helper for common resolver test setup.

### Data Factory (`data.factory.ts`)

#### `createDeepCopy(base, overrides?)`
Creates a deep copy of an object with Date handling.

#### Mock Data Factories
- `createMockScrapedPage(overrides?)` - Creates mock scraped page data
- `createMockPage(overrides?)` - **Deprecated**: Use `createMockScrapedPage` instead
- `createMockRecommendation(overrides?)` - Creates mock recommendation data
- `createMockGroup(overrides?)` - Creates mock group data
- `createMockScheduledAction(overrides?)` - Creates mock scheduled action data
- `createMockCompany(overrides?)` - Creates mock company data

#### Input Factories
- `createMockCreateRecommendationInput(overrides?)` - Creates mock recommendation input
- `createMockCreateScheduledActionInput(overrides?)` - Creates mock scheduled action input
- `createMockUpdateScheduledActionInput(overrides?)` - Creates mock update input

### Authorization Test Helpers (`auth-test.helpers.ts`)

#### `testAuthorizationSuccess(resolverMethod, authContext, resource, action, ...args)`
Tests successful authorization scenario.

#### `testAuthorizationFailure(resolverMethod, authContext, resource, action, ...args)`
Tests authorization failure scenario.

#### `createAuthorizationTestSuite(testName, resolverMethod, authContext, resource, action, ...args)`
Creates a complete authorization test suite.

### Error Test Helpers (`error-test.helpers.ts`)

#### `testServiceError(resolverMethod, serviceMock, serviceMethod, error, ...args)`
Tests service error handling.

#### Specific Error Testers
- `testDatabaseError()` - Tests database connection errors
- `testTimeoutError()` - Tests service timeout errors
- `testNotFoundError()` - Tests not found errors
- `testValidationError()` - Tests validation errors

#### `createErrorTestSuite(testName, resolverMethod, serviceMock, serviceMethod, ...args)`
Creates a complete error handling test suite.

### Common Test Suites (`common-test-suites.ts`)

#### `testResolverDefinition(resolver)`
Tests that the resolver is properly defined.

#### `testBasicResolverMethods(resolver, service, authContext, resource, mockData)`
Tests common CRUD resolver methods.

#### `testResolveFieldMethods(resolver, service, mockEntity)`
Tests GraphQL ResolveField methods.

#### `createCompleteResolverTestSuite(resolver, service, authContext, resource, mockData)`
Creates a comprehensive test suite covering all common patterns.

## Migration Guide

### Before (Original Test)

```typescript
describe('PageResolver', () => {
  let resolver: PageResolver;
  let service: PageService;

  const mockPageService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    findByCompanyId: jest.fn(),
    getRecommendationsForPage: jest.fn(),
  };

  const mockAuthContext = {
    can: jest.fn(),
    isSuper: jest.fn(),
  } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockCanAccess = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PageResolver,
        {
          provide: PageService,
          useValue: mockPageService,
        },
        {
          provide: PagePolicy,
          useValue: {
            read: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<PageResolver>(PageResolver);
    service = module.get<PageService>(PageService);
  });

  // Tests...
});
```

### After (Using Test Utilities)

```typescript
import {
  createMockAuthContext,
  mockAuthorizationSuccess,
  createPageServiceMock,
  createBasicResolverModule,
  testResolverDefinition,
} from 'src/test-utils';

describe('PageResolver', () => {
  let resolver: PageResolver;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockPageService: ReturnType<typeof createPageServiceMock>;

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockPageService = createPageServiceMock();

    const module = await createBasicResolverModule(
      PageResolver,
      PageService,
      mockPageService,
      PagePolicy,
    );

    resolver = module.get<PageResolver>(PageResolver);
  });

  testResolverDefinition(resolver);

  // Tests...
});
```

## Benefits

1. **Reduced Boilerplate**: Eliminate repetitive mock setup code
2. **Consistency**: Standardized testing patterns across all resolvers
3. **Maintainability**: Central location for test utilities
4. **Type Safety**: Proper TypeScript types for all utilities
5. **Comprehensive Coverage**: Built-in test suites for common scenarios
6. **Easy Migration**: Gradual adoption possible

## Best Practices

1. **Use Specific Factories**: Prefer `createPageServiceMock()` over generic `createServiceMock()`
2. **Override When Needed**: Use the `overrides` parameter in data factories for test-specific data
3. **Combine Utilities**: Mix and match utilities as needed for your specific test cases
4. **Test Edge Cases**: Use the utilities for happy path, but add specific tests for edge cases
5. **Keep Tests Readable**: Don't over-abstract - clarity is more important than brevity

## Contributing

When adding new utilities:

1. Follow the existing patterns and naming conventions
2. Add proper TypeScript types
3. Include JSDoc documentation
4. Add examples to this README
5. Consider if the utility is general enough for reuse across multiple test files