import { RecommendationType } from 'src/common/types/scraped-element.type';
import { RecommendationStatus } from 'src/recommendation/entities/recommendation.entity';
import {
  Status,
  WorkflowType,
} from 'src/scheduled-action/entities/scheduled-action.entity';

/**
 * Creates a deep copy of an object with Date handling
 * @param base Base object to copy
 * @param overrides Properties to override
 * @returns Deep copied object
 */
export const createDeepCopy = <T>(base: T, overrides: Partial<T> = {}): T => {
  const merged = { ...base, ...overrides };
  const deepCopied = JSON.parse(
    JSON.stringify(merged, (_key, value) => {
      // Preserve Date objects
      if (value instanceof Date) {
        return { __type: 'Date', value: value.toISOString() };
      }
      return value;
    }),
    (_key, value) => {
      // Restore Date objects
      if (value && typeof value === 'object' && value.__type === 'Date') {
        return new Date(value.value);
      }
      return value;
    },
  ) as T;
  return deepCopied;
};

/**
 * Creates mock scraped page data
 * @param overrides Properties to override
 * @returns Mock scraped page object
 */
export const createMockScrapedPage = (overrides: any = {}) => ({
  id: 'page-1',
  url: 'https://example.com/page1',
  companyId: 'company-1',
  metadata: { customData: 'data1' },
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

/**
 * @deprecated Use createMockScrapedPage instead
 * Creates mock scraped page data
 * @param overrides Properties to override
 * @returns Mock page object
 */
export const createMockPage = (overrides: any = {}) =>
  createMockScrapedPage(overrides);

/**
 * Creates mock recommendation data
 * @param overrides Properties to override
 * @returns Mock recommendation object
 */
export const createMockRecommendation = (overrides: any = {}) => ({
  id: 'rec-1',
  type: RecommendationType.META_TITLE,
  currentValue: 'Current Title',
  recommendationValue: 'Recommended Title',
  status: RecommendationStatus.PENDING,
  reasoning: 'AI generated recommendation',
  metadata: { generationInfo: 'AI generated' },
  scrape: {
    id: 'scrape-1',
    scrapedPage: {
      id: 'page-1',
      companyId: 'company-1',
    },
  },
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

/**
 * Creates mock group data
 * @param overrides Properties to override
 * @returns Mock group object
 */
export const createMockGroup = (overrides: any = {}) => ({
  id: 'group-1',
  companyId: 'company-1',
  scrapedPageId: 'scraped-page-1',
  title: 'Test Group',
  description: 'Test Description',
  metadata: { keyword: 'test keyword' },
  createdAt: new Date(),
  updatedAt: new Date(),
  surfacedAt: new Date(),
  scheduledToBePublishedAt: new Date(),
  ...overrides,
});

/**
 * Creates mock scheduled action data
 * @param overrides Properties to override
 * @returns Mock scheduled action object
 */
export const createMockScheduledAction = (overrides: any = {}) => ({
  id: 'action-1',
  companyId: 'company-1',
  workflowType: WorkflowType.SEO,
  status: Status.DRAFT_PENDING,
  createdAt: new Date(),
  updatedAt: new Date(),
  surfacedAt: null,
  scheduledToBePublishedAt: new Date(Date.now() + 86400000), // tomorrow
  publishedAt: null,
  contentPayload: { type: 'test', data: 'content' },
  generationPayload: { model: 'test-model', temperature: 0.7 },
  failureReason: {},
  scheduledToBeSurfacedAt: null,
  lockedAt: null,
  executionName: null,
  executionArn: null,
  ...overrides,
});

/**
 * Creates mock company data
 * @param overrides Properties to override
 * @returns Mock company object
 */
export const createMockCompany = (overrides: any = {}) => ({
  id: 'company-1',
  name: 'Test Company',
  domain: 'example.com',
  ...overrides,
});

/**
 * Creates mock keyword data
 * @param overrides Properties to override
 * @returns Mock keyword object
 */
export const createMockKeyword = (overrides: any = {}) => ({
  id: 'keyword-1',
  keyword: 'test keyword',
  searchVolume: 1000,
  difficulty: 50,
  ...overrides,
});

/**
 * Creates mock scrape data
 * @param overrides Properties to override
 * @returns Mock scrape object
 */
export const createMockScrape = (overrides: any = {}) => ({
  id: 'scrape-1',
  scrapedPageId: 'page-1',
  scrapedPage: createMockScrapedPage(),
  mediaId: 'media-1',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

/**
 * Creates mock create recommendation input
 * @param overrides Properties to override
 * @returns Mock create recommendation input
 */
export const createMockCreateRecommendationInput = (overrides: any = {}) => ({
  groupId: 'group-123',
  scrapeId: 'scrape-123',
  type: RecommendationType.META_TITLE,
  currentValue: 'Old Title',
  recommendationValue: 'New Title',
  reasoning: 'AI generated recommendation',
  ...overrides,
});

/**
 * Creates mock create scheduled action input
 * @param overrides Properties to override
 * @returns Mock create scheduled action input
 */
export const createMockCreateScheduledActionInput = (overrides: any = {}) => ({
  companyId: 'company-id',
  workflowType: WorkflowType.SEO,
  scheduledToBePublishedAt: new Date(Date.now() + 86400000),
  contentPayload: { type: 'test', data: 'content' },
  generationPayload: { model: 'test-model', temperature: 0.7 },
  ...overrides,
});

/**
 * Creates mock update scheduled action input
 * @param overrides Properties to override
 * @returns Mock update scheduled action input
 */
export const createMockUpdateScheduledActionInput = (overrides: any = {}) => ({
  status: Status.HUMAN_QA_PENDING,
  contentPayload: { type: 'updated', data: 'updated content' },
  ...overrides,
});
