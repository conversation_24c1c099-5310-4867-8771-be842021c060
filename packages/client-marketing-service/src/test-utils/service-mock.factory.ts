/**
 * Common service methods for CRUD operations
 */
export const COMMON_SERVICE_METHODS = [
  'findAll',
  'findOne',
  'create',
  'update',
  'remove',
  'delete',
] as const;

/**
 * Common repository methods
 */
export const COMMON_REPOSITORY_METHODS = [
  'find',
  'findOne',
  'findOneBy',
  'save',
  'update',
  'delete',
  'remove',
  'count',
  'createQueryBuilder',
] as const;

/**
 * Creates a mock service object with specified methods
 * @param methods Array of method names to mock
 * @returns Mock service object
 */
export const createServiceMock = (
  methods: string[] = COMMON_SERVICE_METHODS as any,
) => {
  const mock: Record<string, jest.Mock> = {};

  methods.forEach(method => {
    mock[method] = jest.fn();
  });

  return mock;
};

/**
 * Creates a mock repository object with TypeORM methods
 * @param methods Array of method names to mock
 * @returns Mock repository object
 */
export const createRepositoryMock = (
  methods: string[] = COMMON_REPOSITORY_METHODS as any,
) => {
  const mock: Record<string, jest.Mock> = {};

  methods.forEach(method => {
    mock[method] = jest.fn();
  });

  return mock;
};

/**
 * Creates a mock policy object with common policy methods
 * @returns Mock policy object
 */
export const createPolicyMock = () => ({
  read: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
});

/**
 * Creates a mock DataLoader for testing
 * @returns Mock DataLoader object
 */
export const createDataLoaderMock = () => ({
  createLoader: jest.fn().mockReturnValue({
    load: jest.fn(),
    loadMany: jest.fn(),
  }),
});

/**
 * Page service specific mock
 */
export const createPageServiceMock = () =>
  createServiceMock([
    'findAll',
    'findOne',
    'findByCompanyId',
    'getRecommendationsForScrapedPage',
  ]);

/**
 * Recommendation service specific mock
 */
export const createRecommendationServiceMock = () =>
  createServiceMock([
    'findAll',
    'findOne',
    'create',
    'getCompanyIdForAuthorization',
    'update',
  ]);

/**
 * Group service specific mock
 */
export const createGroupServiceMock = () =>
  createServiceMock([
    'findAll',
    'findOne',
    'findSurfacedGroupsByCompany',
    'findSurfacedAndPublishedGroups',
  ]);

/**
 * Scheduled action service specific mock
 */
export const createScheduledActionServiceMock = () =>
  createServiceMock([
    'findAll',
    'findOne',
    'findDraftPending',
    'findUpcoming',
    'findById',
    'findSurfaced',
    'create',
    'createOwed',
    'update',
    'unsurfaceScheduledAction',
  ]);
