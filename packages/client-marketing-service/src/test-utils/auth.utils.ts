import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { AppPolicyRegistry } from 'src/auth.module';

/**
 * Creates a mock authentication context for testing
 * @returns Mock auth context with common methods
 */
export const createMockAuthContext = () => {
  const mockAuthContext = {
    can: jest.fn(),
    isSuper: jest.fn(),
    belongsToCompany: jest.fn(),
    getUserId: jest.fn(),
  } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  return mockAuthContext;
};

/**
 * Helper function to mock authorization success
 * @param authContext Mock auth context
 * @param value Authorization result (default: true)
 */
export const mockAuthorizationSuccess = (
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  value: boolean = true,
) => {
  authContext.can.mockImplementation(() => Promise.resolve(value));
};

/**
 * Helper function to mock authorization failure
 * @param authContext Mock auth context
 */
export const mockAuthorizationFailure = (
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
) => {
  authContext.can.mockImplementation(() => Promise.resolve(false));
};

/**
 * Helper function to mock authorization rejection with error
 * @param authContext Mock auth context
 * @param error Error to throw
 */
export const mockAuthorizationError = (
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  error: Error,
) => {
  authContext.can.mockRejectedValue(error);
};

/**
 * Helper function to mock super user status
 * @param authContext Mock auth context
 * @param isSuper Whether user is super user
 */
export const mockSuperUserStatus = (
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  isSuper: boolean = true,
) => {
  authContext.isSuper.mockReturnValue(isSuper);
};

/**
 * Resets all auth context mocks
 * @param authContext Mock auth context
 */
export const resetAuthContextMocks = (
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
) => {
  authContext.can.mockReset();
  authContext.isSuper.mockReset();
  authContext.belongsToCompany?.mockReset();
  authContext.getUserId?.mockReset();
};
