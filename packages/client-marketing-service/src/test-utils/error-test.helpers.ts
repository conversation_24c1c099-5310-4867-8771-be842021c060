import { NotFoundException, BadRequestException } from '@nestjs/common';

/**
 * Tests service error handling for a resolver method
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name that should throw
 * @param error Error to throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testServiceError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  error: Error,
  ...args: any[]
) => {
  return async () => {
    serviceMock[serviceMethod].mockRejectedValue(error);

    await expect(resolverMethod(...args)).rejects.toThrow(error);

    expect(serviceMock[serviceMethod]).toHaveBeenCalled();
  };
};

/**
 * Tests database connection error handling
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name that should throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testDatabaseError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  const dbError = new Error('Database connection failed');
  dbError.name = 'DatabaseError';
  return testServiceError(
    resolverMethod,
    serviceMock,
    serviceMethod,
    dbError,
    ...args,
  );
};

/**
 * Tests timeout error handling
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name that should throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testTimeoutError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  const timeoutError = new Error('Service timeout');
  timeoutError.name = 'TimeoutError';
  return testServiceError(
    resolverMethod,
    serviceMock,
    serviceMethod,
    timeoutError,
    ...args,
  );
};

/**
 * Tests not found error handling
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name that should throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testNotFoundError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  const notFoundError = new NotFoundException('Resource not found');
  return testServiceError(
    resolverMethod,
    serviceMock,
    serviceMethod,
    notFoundError,
    ...args,
  );
};

/**
 * Tests validation error handling
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name that should throw
 * @param args Arguments to pass to resolver method
 * @returns Test function
 */
export const testValidationError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  const validationError = new BadRequestException('Validation failed');
  return testServiceError(
    resolverMethod,
    serviceMock,
    serviceMethod,
    validationError,
    ...args,
  );
};

/**
 * Creates a suite of common error tests for a resolver method
 * @param testName Name for the test suite
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name
 * @param args Arguments to pass to resolver method
 */
export const createErrorTestSuite = (
  testName: string,
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  describe(`${testName} error handling`, () => {
    it(
      'should handle service errors',
      testServiceError(
        resolverMethod,
        serviceMock,
        serviceMethod,
        new Error('Service error'),
        ...args,
      ),
    );

    it(
      'should handle database errors',
      testDatabaseError(resolverMethod, serviceMock, serviceMethod, ...args),
    );

    it(
      'should handle timeout errors',
      testTimeoutError(resolverMethod, serviceMock, serviceMethod, ...args),
    );

    it(
      'should handle not found errors',
      testNotFoundError(resolverMethod, serviceMock, serviceMethod, ...args),
    );

    it(
      'should handle validation errors',
      testValidationError(resolverMethod, serviceMock, serviceMethod, ...args),
    );
  });
};

/**
 * Test helper for methods that should return empty results on service errors
 * @param resolverMethod Function to test
 * @param serviceMock Mock service object
 * @param serviceMethod Service method name
 * @param args Arguments to pass to resolver method
 */
export const testEmptyResultOnError = (
  resolverMethod: (...args: any[]) => Promise<any>,
  serviceMock: any,
  serviceMethod: string,
  ...args: any[]
) => {
  return async () => {
    serviceMock[serviceMethod].mockResolvedValue([]);

    const result = await resolverMethod(...args);

    expect(result).toEqual([]);
    expect(serviceMock[serviceMethod]).toHaveBeenCalled();
  };
};
