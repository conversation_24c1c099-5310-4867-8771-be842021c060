import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { AppPolicyRegistry } from 'src/auth.module';

import { createAuthorizationTestSuite } from './auth-test.helpers';
import { mockAuthorizationSuccess } from './auth.utils';
import { createErrorTestSuite } from './error-test.helpers';

/**
 * Tests basic resolver functionality (definition check)
 * @param resolver Resolver instance
 */
export const testResolverDefinition = (resolver: any) => {
  describe('resolver definition', () => {
    it('should be defined', () => {
      expect(resolver).toBeDefined();
    });
  });
};

/**
 * Tests basic CRUD resolver methods with common patterns
 * @param resolver Resolver instance
 * @param service Service instance
 * @param authContext Mock auth context
 * @param resource Resource name for authorization
 * @param mockData Mock data for testing
 */
export const testBasicResolverMethods = (
  resolver: any,
  service: any,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  mockData: any,
) => {
  describe('basic resolver methods', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    if (resolver.findAll) {
      describe('findAll', () => {
        it('should return array when authorized', async () => {
          mockAuthorizationSuccess(authContext);
          service.findAll.mockResolvedValue([mockData]);

          const result = await resolver.findAll(authContext);

          expect(result).toEqual([mockData]);
          expect(authContext.can).toHaveBeenCalledWith(
            resource,
            'read',
            expect.anything(),
          );
        });
      });
    }

    if (resolver.findOne) {
      describe('findOne', () => {
        it('should return single item when authorized', async () => {
          mockAuthorizationSuccess(authContext);
          service.findOne.mockResolvedValue(mockData);

          const result = await resolver.findOne(authContext, 'test-id');

          expect(result).toEqual(mockData);
          expect(service.findOne).toHaveBeenCalledWith('test-id');
        });
      });
    }

    if (resolver.create) {
      describe('create', () => {
        it('should create and return item when authorized', async () => {
          mockAuthorizationSuccess(authContext);
          service.create.mockResolvedValue(mockData);

          const result = await resolver.create(mockData, authContext);

          expect(result).toEqual(mockData);
          expect(authContext.can).toHaveBeenCalledWith(
            resource,
            'create',
            expect.anything(),
          );
        });
      });
    }

    if (resolver.update) {
      describe('update', () => {
        it('should update and return item when authorized', async () => {
          mockAuthorizationSuccess(authContext);
          service.update.mockResolvedValue(mockData);

          const result = await resolver.update(
            'test-id',
            mockData,
            authContext,
          );

          expect(result).toEqual(mockData);
          expect(authContext.can).toHaveBeenCalledWith(
            resource,
            'update',
            expect.anything(),
          );
        });
      });
    }
  });
};

/**
 * Tests ResolveField methods for GraphQL resolvers
 * @param resolver Resolver instance
 * @param service Service instance
 * @param mockEntity Mock entity with related data
 */
export const testResolveFieldMethods = (
  resolver: any,
  service: any,
  mockEntity: any,
) => {
  describe('resolve field methods', () => {
    if (resolver.metadata) {
      describe('metadata', () => {
        it('should return metadata from entity', () => {
          const result = resolver.metadata(mockEntity);
          expect(result).toEqual(mockEntity.metadata);
        });

        it('should handle null metadata', () => {
          const entityWithNullMetadata = { ...mockEntity, metadata: null };
          const result = resolver.metadata(entityWithNullMetadata);
          expect(result).toBeNull();
        });
      });
    }

    if (resolver.company) {
      describe('company', () => {
        it('should return null when no companyId', async () => {
          const entityWithoutCompany = { ...mockEntity, companyId: null };
          const result = await resolver.company(entityWithoutCompany);
          expect(result).toBeNull();
        });
      });
    }

    if (resolver.recommendations && service.getRecommendationsForPage) {
      describe('recommendations', () => {
        it('should return recommendations for entity', async () => {
          const mockRecommendations = [{ id: '1' }, { id: '2' }];
          service.getRecommendationsForPage.mockResolvedValue(
            mockRecommendations,
          );

          const result = await resolver.recommendations(mockEntity);

          expect(result).toEqual(mockRecommendations);
          expect(service.getRecommendationsForPage).toHaveBeenCalledWith(
            mockEntity.id,
          );
        });
      });
    }
  });
};

/**
 * Tests all authorization scenarios for a resolver
 * @param resolver Resolver instance
 * @param authContext Mock auth context
 * @param resource Resource name
 * @param mockData Mock data for testing
 */
export const testAllAuthorizationScenarios = (
  resolver: any,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  mockData: any,
) => {
  describe('authorization scenarios', () => {
    if (resolver.findAll) {
      createAuthorizationTestSuite(
        'findAll',
        resolver.findAll.bind(resolver),
        authContext,
        resource,
        'read',
        authContext,
      );
    }

    if (resolver.findOne) {
      createAuthorizationTestSuite(
        'findOne',
        resolver.findOne.bind(resolver),
        authContext,
        resource,
        'read',
        authContext,
        'test-id',
      );
    }

    if (resolver.create) {
      createAuthorizationTestSuite(
        'create',
        resolver.create.bind(resolver),
        authContext,
        resource,
        'create',
        mockData,
        authContext,
      );
    }

    if (resolver.update) {
      createAuthorizationTestSuite(
        'update',
        resolver.update.bind(resolver),
        authContext,
        resource,
        'update',
        'test-id',
        mockData,
        authContext,
      );
    }
  });
};

/**
 * Tests all error scenarios for a resolver
 * @param resolver Resolver instance
 * @param service Service instance
 * @param authContext Mock auth context
 * @param mockData Mock data for testing
 */
export const testAllErrorScenarios = (
  resolver: any,
  service: any,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  mockData: any,
) => {
  describe('error scenarios', () => {
    beforeEach(() => {
      mockAuthorizationSuccess(authContext);
    });

    if (resolver.findAll && service.findAll) {
      createErrorTestSuite(
        'findAll',
        resolver.findAll.bind(resolver),
        service,
        'findAll',
        authContext,
      );
    }

    if (resolver.findOne && service.findOne) {
      createErrorTestSuite(
        'findOne',
        resolver.findOne.bind(resolver),
        service,
        'findOne',
        authContext,
        'test-id',
      );
    }

    if (resolver.create && service.create) {
      createErrorTestSuite(
        'create',
        resolver.create.bind(resolver),
        service,
        'create',
        mockData,
        authContext,
      );
    }

    if (resolver.update && service.update) {
      createErrorTestSuite(
        'update',
        resolver.update.bind(resolver),
        service,
        'update',
        'test-id',
        mockData,
        authContext,
      );
    }
  });
};

/**
 * Creates a comprehensive test suite for a resolver
 * @param resolver Resolver instance
 * @param service Service instance
 * @param authContext Mock auth context
 * @param resource Resource name for authorization
 * @param mockData Mock data for testing
 */
export const createCompleteResolverTestSuite = (
  resolver: any,
  service: any,
  authContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>,
  resource: string,
  mockData: any,
) => {
  testResolverDefinition(resolver);
  testBasicResolverMethods(resolver, service, authContext, resource, mockData);
  testResolveFieldMethods(resolver, service, mockData);
  testAllAuthorizationScenarios(resolver, authContext, resource, mockData);
  testAllErrorScenarios(resolver, service, authContext, mockData);
};
