import { writeFileSync } from 'fs';
import { join } from 'path';
import { setTimeout } from 'timers/promises';

import { printSubgraphSchema } from '@apollo/subgraph';
import {
  LoggerModule,
  RequestLoggerMiddleware,
} from '@luxury-presence/nestjs-logger';
import { ApolloFederationDriverConfig } from '@nestjs/apollo';
import {
  BeforeApplicationShutdown,
  Logger,
  MiddlewareConsumer,
  Module,
  RequestMethod,
  ShutdownSignal,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppDataSource } from 'src/data-source';
import { GroupModule } from 'src/group/group.module';
import { HealthController } from 'src/health.controller';
import { NotificationModule } from 'src/notification/notification.module';
import { RecommendationModule } from 'src/recommendation/recommendation.module';
import { ScheduledActionModule } from 'src/scheduled-action/scheduled-action.module';

import { AuthModule } from './auth.module';
import { BlogTopicModule } from './blog-topic/blog-topic.module';
import { BrandProfileModule } from './brand-profile/brand-profile.module';
import { CampaignModule } from './campaign/campaign.module';
import { ApolloFederationDriver } from './common/graphql/apollo-federation.driver';
import { GraphQLContextFactory } from './common/graphql-context/graphql-context.factory';
import { GraphQLContextModule } from './common/graphql-context/graphql-context.module';
import { CompanyModule } from './company/company.module';
import { ConfigValidationService } from './config/config-validation.service';
import configuration from './config/configuration';
import { GroupScheduledActionModule } from './group-scheduled-action/group-scheduled-action.module';
import { HomepageFeedModule } from './homepage-feed/homepage-feed.module';
import { KeywordModule } from './keyword/keyword.module';
import { AuthMiddleware } from './middleware/auth.middleware';
import { PageKeywordModule } from './page-keyword/page-keyword.module';
import { PaymentModule } from './payment/payment.module';
import { Property } from './property/entities/property.entity';
import { PropertyModule } from './property/property.module';
import { ScrapeModule } from './scrape/scrape.module';
import { ScrapedPageModule } from './scraped-page/scraped-page.module';
import { SeoOptimizationsModule } from './seo-optimizations/seo-optimizations.module';

const isLocal =
  !process.env.NODE_ENV ||
  process.env.NODE_ENV === 'development' ||
  process.env.NODE_ENV === 'test';

@Module({
  imports: [
    AuthModule,
    ConfigModule.forRoot({
      isGlobal: true,
      ignoreEnvFile: true,
      load: [configuration],
    }),
    ScheduleModule.forRoot(),
    GraphQLModule.forRootAsync<ApolloFederationDriverConfig>({
      driver: ApolloFederationDriver,
      imports: [GraphQLContextModule],
      inject: [GraphQLContextFactory],
      useFactory: (
        contextFactory: GraphQLContextFactory,
      ): Omit<ApolloFederationDriverConfig, 'driver'> => ({
        autoSchemaFile: { federation: 2 },
        buildSchemaOptions: {
          orphanedTypes: [Property],
        },
        context: ({ req }) => {
          return contextFactory.create(req);
        },
        transformSchema: schema => {
          writeFileSync(
            join(process.cwd(), 'schema.graphql'),
            printSubgraphSchema(schema),
          );
          return schema;
        },
        graphiql: {
          shouldPersistHeaders: true,
        },
        introspection: true,
      }),
    }),
    TypeOrmModule.forRoot(AppDataSource.options),
    LoggerModule.register({
      format: isLocal ? 'basic' : 'json',
      level: 'debug',
    }),
    CampaignModule,
    CompanyModule,
    GroupModule,
    PaymentModule,
    RecommendationModule,
    NotificationModule,
    ScheduledActionModule,
    ScrapedPageModule,
    KeywordModule,
    ScrapeModule,
    PageKeywordModule,
    PropertyModule,
    GroupScheduledActionModule,
    HomepageFeedModule,
    SeoOptimizationsModule,
    BrandProfileModule,
    BlogTopicModule,
  ],
  controllers: [HealthController],
  providers: [AuthMiddleware, ConfigValidationService],
})
export class AppModule implements BeforeApplicationShutdown {
  private readonly logger = new Logger(AppModule.name);

  constructor(private readonly configService: ConfigService) {}

  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AuthMiddleware)
      .forRoutes({ path: '/graphql', method: RequestMethod.ALL });

    consumer.apply(RequestLoggerMiddleware).forRoutes('*');
  }

  async beforeApplicationShutdown(signal?: string) {
    if (signal === ShutdownSignal.SIGTERM) {
      const delayTime = parseInt(
        this.configService.get<string>('applicationShutdownDelay', '31000'),
      );
      this.logger.log(
        `Received SIGTERM. Waiting ${delayTime}ms before shutdown`,
      );
      await setTimeout(delayTime);
    } else {
      this.logger.log(`Received ${signal}. Continuing shutdown`);
    }
  }
}
