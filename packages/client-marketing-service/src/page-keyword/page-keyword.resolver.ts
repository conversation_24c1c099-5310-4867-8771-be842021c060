import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Query,
  Resolver,
  ResolveField,
  Parent,
  Mutation,
} from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { Company } from 'src/company/entities/company.entity';
import { AuthContext } from 'src/graphql.decorator';

import { UpdatePageKeywordRankInput } from './dto/update-page-keyword-rank.input';
import { PageKeyword } from './entities/page-keyword.entity';
import { PageKeywordService } from './page-keyword.service';

@Resolver(() => PageKeyword)
export class PageKeywordResolver {
  constructor(private readonly pageKeywordService: PageKeywordService) {}

  @Query(() => [PageKeyword], {
    name: 'pageKeywords',
    description: 'Get all page keywords',
  })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
  ): Promise<PageKeyword[]> {
    const canRead = await authContext.can('pageKeyword', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    return this.pageKeywordService.findAll(companyId);
  }

  @Query(() => PageKeyword, {
    name: 'pageKeyword',
    description: 'Get a page-keyword by ID',
  })
  async findOne(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<PageKeyword> {
    const pageKeyword = await this.pageKeywordService.findOne(id);
    const canRead = await authContext.can(
      'pageKeyword',
      'read',
      pageKeyword.companyId,
    );

    if (!canRead) throw new ForbiddenException();

    return pageKeyword;
  }

  @ResolveField(() => Company, { nullable: true })
  company(@Parent() pageKeyword: PageKeyword): Company | null {
    // PageKeyword derives companyId from scrapedPage relation
    const companyId = pageKeyword.scrapedPage?.companyId;
    if (!companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: companyId } as Company;
  }

  @Mutation(() => PageKeyword, {
    name: 'updatePageKeywordRank',
    description: 'Update the current rank of a page-keyword',
  })
  async updatePageKeywordRank(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('input') input: UpdatePageKeywordRankInput,
  ): Promise<PageKeyword> {
    // First, get the page-keyword to check permissions
    const pageKeyword = await this.pageKeywordService.findOne(input.id);
    const canUpdate = await authContext.can(
      'pageKeyword',
      'update',
      pageKeyword.companyId,
    );

    if (!canUpdate) throw new ForbiddenException();

    // Update the page-keyword rank
    // This only updates the rank, not the keyword association
    if (input.currentRank !== undefined) {
      return this.pageKeywordService.updateRank(input.id, input.currentRank);
    }

    // If no rank provided, just return the existing page-keyword
    return pageKeyword;
  }
}
