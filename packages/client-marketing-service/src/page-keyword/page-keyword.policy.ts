import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class PageKeywordPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  /**
   * Checks if the user has permission to read page keywords
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  read = (companyId?: string) => {
    if (!companyId) {
      return this.auth.isSuper();
    }

    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };

  /**
   * Checks if the user has permission to update page keywords
   * @param companyId Company ID to check permission for
   * @returns boolean
   */
  update = (companyId: string) => {
    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };
}
