import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CreatePageKeywordDto } from './dto/create-page-keyword.dto';
import { UpdatePageKeywordDto } from './dto/update-page-keyword.dto';
import { PageKeywordController } from './page-keyword.controller';
import { PageKeywordService } from './page-keyword.service';

describe('PageKeywordController', () => {
  let controller: PageKeywordController;
  let pageKeywordService: PageKeywordService;

  const mockPageKeywordService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    updatePageKeyword: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PageKeywordController],
      providers: [
        {
          provide: PageKeywordService,
          useValue: mockPageKeywordService,
        },
      ],
    }).compile();

    controller = module.get<PageKeywordController>(PageKeywordController);
    pageKeywordService = module.get<PageKeywordService>(PageKeywordService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new page keyword', async () => {
      const createPageKeywordDto: CreatePageKeywordDto = {
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
        currentRank: 1,
      } as any;
      const createdPageKeyword = { id: '1', ...createPageKeywordDto };

      mockPageKeywordService.create.mockResolvedValue(createdPageKeyword);

      const response = await controller.create(createPageKeywordDto);

      expect(pageKeywordService.create).toHaveBeenCalledWith(
        createPageKeywordDto,
      );
      expect(response).toEqual(createdPageKeyword);
    });
  });

  describe('findAll', () => {
    it('should return all page keywords', async () => {
      const pageKeywords = [
        {
          id: '1',
          scrapedPageId: 'page-1',
          keywordId: 'keyword-1',
          currentRank: 1,
        },
        {
          id: '2',
          scrapedPageId: 'page-2',
          keywordId: 'keyword-2',
          currentRank: 2,
        },
      ];

      mockPageKeywordService.findAll.mockResolvedValue(pageKeywords);

      const result = await controller.findAll();

      expect(pageKeywordService.findAll).toHaveBeenCalled();
      expect(result).toEqual(pageKeywords);
    });
  });

  describe('findOne', () => {
    it('should return a single page keyword', async () => {
      const pageKeywordId = '1';
      const pageKeyword = {
        id: pageKeywordId,
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
        currentRank: 1,
      };

      mockPageKeywordService.findOne.mockResolvedValue(pageKeyword);

      const result = await controller.findOne(pageKeywordId);

      expect(pageKeywordService.findOne).toHaveBeenCalledWith(pageKeywordId);
      expect(result).toEqual(pageKeyword);
    });

    it('should throw NotFoundException when page keyword not found', async () => {
      const pageKeywordId = '1';

      mockPageKeywordService.findOne.mockRejectedValue(
        new NotFoundException('PageKeyword #1 not found'),
      );

      await expect(controller.findOne(pageKeywordId)).rejects.toThrow(
        NotFoundException,
      );
      expect(pageKeywordService.findOne).toHaveBeenCalledWith(pageKeywordId);
    });
  });

  describe('update', () => {
    it('should update a page keyword', async () => {
      const pageKeywordId = '1';
      const updatePageKeywordDto: UpdatePageKeywordDto = {
        keywordId: 'new-keyword-id',
        currentRank: 5,
        changeNote: 'Updated rank',
        updatedBy: 'user-id',
      };
      const updatedPageKeyword = {
        id: pageKeywordId,
        ...updatePageKeywordDto,
      };

      mockPageKeywordService.updatePageKeyword.mockResolvedValue(
        updatedPageKeyword,
      );

      const result = await controller.update(
        pageKeywordId,
        updatePageKeywordDto,
      );

      expect(pageKeywordService.updatePageKeyword).toHaveBeenCalledWith(
        pageKeywordId,
        updatePageKeywordDto,
      );
      expect(result).toEqual(updatedPageKeyword);
    });

    it('should handle update errors', async () => {
      const pageKeywordId = '1';
      const updatePageKeywordDto: UpdatePageKeywordDto = {
        keywordId: 'new-keyword-id',
      };
      const error = new Error('Update failed');

      mockPageKeywordService.updatePageKeyword.mockRejectedValue(error);

      await expect(
        controller.update(pageKeywordId, updatePageKeywordDto),
      ).rejects.toThrow(error);
      expect(pageKeywordService.updatePageKeyword).toHaveBeenCalledWith(
        pageKeywordId,
        updatePageKeywordDto,
      );
    });
  });

  describe('remove', () => {
    it('should remove a page keyword', async () => {
      const pageKeywordId = '1';
      const deleteResult = { affected: 1 };

      mockPageKeywordService.remove.mockResolvedValue(deleteResult);

      const response = await controller.remove(pageKeywordId);

      expect(pageKeywordService.remove).toHaveBeenCalledWith(pageKeywordId);
      expect(response).toEqual(deleteResult);
    });

    it('should throw NotFoundException when page keyword not found', async () => {
      const pageKeywordId = '1';

      mockPageKeywordService.remove.mockRejectedValue(
        new NotFoundException('PageKeyword #1 not found'),
      );

      await expect(controller.remove(pageKeywordId)).rejects.toThrow(
        NotFoundException,
      );
      expect(pageKeywordService.remove).toHaveBeenCalledWith(pageKeywordId);
    });
  });
});
