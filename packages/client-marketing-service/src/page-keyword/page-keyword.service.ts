import { NotFoundException } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository, DeleteResult } from 'typeorm';

import { CreatePageKeywordDto } from './dto/create-page-keyword.dto';
import { UpdatePageKeywordDto } from './dto/update-page-keyword.dto';
import { PageKeyword } from './entities/page-keyword.entity';

@Injectable()
export class PageKeywordService {
  constructor(
    @InjectRepository(PageKeyword)
    private readonly pageKeywordRepository: Repository<PageKeyword>,
    private readonly dataSource: DataSource,
  ) {}

  async create(
    createPageKeywordDto: CreatePageKeywordDto,
  ): Promise<PageKeyword> {
    return this.pageKeywordRepository.save(createPageKeywordDto);
  }

  async findAll(companyId?: string): Promise<PageKeyword[]> {
    const queryBuilder = this.pageKeywordRepository
      .createQueryBuilder('pageKeyword')
      .leftJoinAndSelect('pageKeyword.scrapedPage', 'scrapedPage')
      .leftJoinAndSelect('pageKeyword.keyword', 'keyword')
      .orderBy('pageKeyword.createdAt', 'DESC');

    if (companyId) {
      queryBuilder.where('scrapedPage.companyId = :companyId', { companyId });
    }

    return queryBuilder.getMany();
  }

  async findOne(id: string): Promise<PageKeyword> {
    const pageKeyword = await this.pageKeywordRepository.findOne({
      where: { id },
      relations: ['scrapedPage', 'keyword'], // Load relations to access companyId and keyword data
    });
    if (!pageKeyword) {
      throw new NotFoundException(`PageKeyword #${id} not found`);
    }
    return pageKeyword;
  }

  /**
   * Updates a page-keyword relationship and records the change in the history table
   *
   * Audit Trail Mechanism:
   * 1. This method uses PostgreSQL session variables to pass audit information to database triggers
   * 2. The `changeNote` and `updatedBy` values are set as session variables using `SET LOCAL`
   * 3. Database triggers (defined in migration 1747673553390-page-keyword-history-triggers.ts) capture these values
   * 4. When the page_keyword record is updated, the triggers automatically:
   *    - Create a new entry in page_keyword_history with the audit information
   *    - Mark previous history records as removed if the keyword changes
   *    - Preserve the complete audit trail of all keyword ranking changes
   *
   * This approach ensures consistent audit trails even when records are modified directly in the database,
   * as long as the session variables are properly set.
   */
  async updatePageKeyword(
    id: string,
    updatePageKeywordDto: UpdatePageKeywordDto,
  ) {
    const { changeNote, updatedBy, keywordId, currentRank } =
      updatePageKeywordDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Only set audit variables if keywordId is being changed
      // Rank updates don't require audit trail entries
      if (keywordId) {
        // Set PostgreSQL session variables that will be captured by database triggers
        // These variables are used to record audit information in the page_keyword_history table
        if (changeNote) {
          await queryRunner.query(`SET LOCAL app.change_note = $1`, [
            changeNote,
          ]);
        }

        // The updatedBy field stores the UUID of the user who made the change
        // This is captured by the database trigger and stored in the history record
        if (updatedBy) {
          await queryRunner.query(`SET LOCAL app.updated_by = $1`, [updatedBy]);
        }
      }

      // Update page_keyword entry
      const updateData: Partial<PageKeyword> = {};
      if (keywordId) {
        updateData.keywordId = keywordId;
      }
      if (typeof currentRank === 'number') {
        updateData.currentRank = currentRank;
      }

      const qb = await queryRunner.manager
        .createQueryBuilder()
        .update(PageKeyword)
        .set(updateData)
        .where('id = :id', { id })
        .returning('*')
        .execute();

      await queryRunner.commitTransaction();
      return qb.raw[0];
    } catch (err) {
      await queryRunner.rollbackTransaction();
      throw err;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Updates only the current rank of a page-keyword
   * This method does not trigger history tracking as rank changes are not audited
   *
   * @param id The ID of the page-keyword to update
   * @param currentRank The new current rank value
   * @returns The updated page-keyword entity
   */
  async updateRank(id: string, currentRank: number): Promise<PageKeyword> {
    await this.pageKeywordRepository.update(id, {
      currentRank,
      rankCheckedAt: new Date(),
    });
    return this.findOne(id);
  }

  async remove(id: string): Promise<DeleteResult> {
    return await this.pageKeywordRepository.delete({ id });
  }
}
