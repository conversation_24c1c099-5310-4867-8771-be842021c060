import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsUUID,
  IsDate,
  Min,
  Max,
} from 'class-validator';

export class CreatePageKeywordDto {
  @ApiProperty({
    description: 'The ID of the scraped page',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  scrapedPageId: string;

  @ApiProperty({
    description: 'The ID of the keyword',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  keywordId: string;

  @ApiProperty({
    description: 'The original rank of the page for this keyword',
    example: 15,
    required: false,
  })
  @IsNumber()
  @Min(-1)
  @Max(100)
  @IsOptional()
  originalRank?: number;

  @ApiProperty({
    description: 'The current rank of the page for this keyword',
    example: 5,
    required: false,
  })
  @IsNumber()
  @Min(-1)
  @Max(100)
  @IsOptional()
  currentRank?: number;

  @ApiPropertyOptional({
    description: 'The rank checked at of the page for this keyword',
    example: '2025-05-19T11:11:11.111Z',
  })
  @IsDate()
  @IsOptional()
  rankCheckedAt?: Date;
}
