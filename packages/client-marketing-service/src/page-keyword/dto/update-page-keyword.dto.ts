import { PartialType, OmitType, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID } from 'class-validator';

import { CreatePageKeywordDto } from './create-page-keyword.dto';

export class UpdatePageKeywordDto extends PartialType(
  OmitType(CreatePageKeywordDto, ['scrapedPageId'] as const),
) {
  @ApiPropertyOptional({
    description: 'The change note of the page for this keyword',
    example: 'Client request',
  })
  @IsString()
  @IsOptional()
  changeNote?: string;

  @ApiPropertyOptional({
    description: 'The updated by of the page for this keyword',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  updatedBy?: string;
}
