import { Field, InputType, Int, ID } from '@nestjs/graphql';
import { IsInt, IsOptional, IsUUID, Min, Max } from 'class-validator';

@InputType()
export class UpdatePageKeywordRankInput {
  @Field(() => ID, { description: 'The ID of the page-keyword to update' })
  @IsUUID()
  id: string;

  @Field(() => Int, {
    description: 'The new current rank for the page-keyword',
    nullable: true,
  })
  @IsInt()
  @Min(-1)
  @Max(100)
  @IsOptional()
  currentRank?: number;
}
