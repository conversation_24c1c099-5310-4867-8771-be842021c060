import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AppPolicyRegistry } from 'src/auth.module';

import { PageKeyword } from './entities/page-keyword.entity';
import { PageKeywordPolicy } from './page-keyword.policy';
import { PageKeywordResolver } from './page-keyword.resolver';
import { PageKeywordService } from './page-keyword.service';

describe('PageKeywordResolver', () => {
  let resolver: PageKeywordResolver;
  let service: PageKeywordService;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockPageKeywordService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    updatePageKeyword: jest.fn(),
    updateRank: jest.fn(),
  };

  beforeEach(async () => {
    // Create mock auth context
    mockAuthContext = {
      can: jest.fn(),
      isSuper: jest.fn(),
      belongsToCompany: jest.fn(),
      getCompanyIds: jest.fn(),
      getUserId: jest.fn(),
    } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PageKeywordResolver,
        {
          provide: PageKeywordService,
          useValue: mockPageKeywordService,
        },
        {
          provide: PageKeywordPolicy,
          useValue: {
            read: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<PageKeywordResolver>(PageKeywordResolver);
    service = module.get<PageKeywordService>(PageKeywordService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of PageKeywords when authorized', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);

      const mockPageKeywords = [
        {
          id: '1',
          scrapedPageId: 'page1',
          keywordId: 'keyword1',
          currentRank: 5,
        },
        {
          id: '2',
          scrapedPageId: 'page2',
          keywordId: 'keyword2',
          currentRank: 10,
        },
      ];
      jest
        .spyOn(service, 'findAll')
        .mockResolvedValue(mockPageKeywords as unknown as PageKeyword[]);

      expect(await resolver.findAll(mockAuthContext)).toEqual(mockPageKeywords);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'read',
        undefined,
      );
      expect(service.findAll).toHaveBeenCalledWith(undefined);
    });

    it('should return page keywords filtered by companyId when provided', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);
      const companyId = '123';

      const mockPageKeywords = [
        {
          id: '1',
          scrapedPageId: 'page1',
          keywordId: 'keyword1',
          currentRank: 5,
          companyId,
        },
      ];
      jest
        .spyOn(service, 'findAll')
        .mockResolvedValue(mockPageKeywords as unknown as PageKeyword[]);

      expect(await resolver.findAll(mockAuthContext, companyId)).toEqual(
        mockPageKeywords,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'read',
        companyId,
      );
      expect(service.findAll).toHaveBeenCalledWith(companyId);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      // Setup auth mock to deny access
      mockAuthContext.can.mockResolvedValue(false);

      await expect(resolver.findAll(mockAuthContext)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'read',
        undefined,
      );
      expect(service.findAll).not.toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single PageKeyword when authorized', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);
      const companyId = '123';

      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        companyId,
      };
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockPageKeyword as unknown as PageKeyword);

      expect(await resolver.findOne(mockAuthContext, '1')).toEqual(
        mockPageKeyword,
      );
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'read',
        companyId,
      );
    });

    it('should throw ForbiddenException when not authorized to read page keyword', async () => {
      const companyId = '123';
      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        companyId,
      };
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockPageKeyword as unknown as PageKeyword);
      // Setup auth mock to deny access
      mockAuthContext.can.mockResolvedValue(false);

      await expect(resolver.findOne(mockAuthContext, '1')).rejects.toThrow(
        ForbiddenException,
      );
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'read',
        companyId,
      );
    });
  });

  describe('updatePageKeywordRank', () => {
    it('should update page keyword rank when authorized', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);
      const companyId = '123';

      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        originalRank: 10,
        companyId,
      };

      const updatedPageKeyword = {
        ...mockPageKeyword,
        currentRank: 3,
      };

      const updateInput = {
        id: '1',
        currentRank: 3,
      };

      // Mock service calls
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValueOnce(mockPageKeyword as unknown as PageKeyword);

      jest
        .spyOn(service, 'updateRank')
        .mockResolvedValue(updatedPageKeyword as unknown as PageKeyword);

      const result = await resolver.updatePageKeywordRank(
        mockAuthContext,
        updateInput,
      );

      expect(result).toEqual(updatedPageKeyword);
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'update',
        companyId,
      );
      expect(service.updateRank).toHaveBeenCalledWith('1', 3);
      expect(service.findOne).toHaveBeenCalledTimes(1);
    });

    it('should throw ForbiddenException when not authorized to update page keyword', async () => {
      const companyId = '123';
      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        companyId,
      };

      const updateInput = {
        id: '1',
        currentRank: 3,
      };

      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockPageKeyword as unknown as PageKeyword);

      // Setup auth mock to deny access
      mockAuthContext.can.mockResolvedValue(false);

      await expect(
        resolver.updatePageKeywordRank(mockAuthContext, updateInput),
      ).rejects.toThrow(ForbiddenException);

      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'update',
        companyId,
      );
      expect(service.updateRank).not.toHaveBeenCalled();
    });

    it('should return existing page keyword when no currentRank provided', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);
      const companyId = '123';

      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        originalRank: 10,
        companyId,
      };

      const updateInput = {
        id: '1',
        // No currentRank provided
      };

      // Mock service calls
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockPageKeyword as unknown as PageKeyword);

      const result = await resolver.updatePageKeywordRank(
        mockAuthContext,
        updateInput,
      );

      expect(result).toEqual(mockPageKeyword);
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'update',
        companyId,
      );
      expect(service.updateRank).not.toHaveBeenCalled();
    });

    it('should update page keyword rank with -1 for non-ranking pages', async () => {
      // Setup auth mock to allow access
      mockAuthContext.can.mockResolvedValue(true);
      const companyId = '123';

      const mockPageKeyword = {
        id: '1',
        scrapedPageId: 'page1',
        keywordId: 'keyword1',
        currentRank: 5,
        companyId,
      };

      const updateInput = {
        id: '1',
        currentRank: -1,
      };

      const updatedPageKeyword = {
        ...mockPageKeyword,
        currentRank: -1,
        originalRank: -1,
      };

      jest
        .spyOn(service, 'findOne')
        .mockResolvedValueOnce(mockPageKeyword as unknown as PageKeyword);

      jest
        .spyOn(service, 'updateRank')
        .mockResolvedValue(updatedPageKeyword as unknown as PageKeyword);

      const result = await resolver.updatePageKeywordRank(
        mockAuthContext,
        updateInput,
      );

      expect(result).toEqual(updatedPageKeyword);
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'pageKeyword',
        'update',
        companyId,
      );
      expect(service.updateRank).toHaveBeenCalledWith('1', -1);
    });
  });
});
