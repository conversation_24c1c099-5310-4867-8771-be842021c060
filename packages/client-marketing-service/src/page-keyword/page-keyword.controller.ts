import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';

import { CreatePageKeywordDto } from './dto/create-page-keyword.dto';
import { UpdatePageKeywordDto } from './dto/update-page-keyword.dto';
import { PageKeywordService } from './page-keyword.service';

@Controller('page-keyword')
export class PageKeywordController {
  constructor(private readonly pageKeywordService: PageKeywordService) {}

  @Post()
  create(@Body() createPageKeywordDto: CreatePageKeywordDto) {
    return this.pageKeywordService.create(createPageKeywordDto);
  }

  @Get()
  findAll() {
    return this.pageKeywordService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.pageKeywordService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updatePageKeywordDto: UpdatePageKeywordDto,
  ) {
    return this.pageKeywordService.updatePageKeyword(id, updatePageKeywordDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.pageKeywordService.remove(id);
  }
}
