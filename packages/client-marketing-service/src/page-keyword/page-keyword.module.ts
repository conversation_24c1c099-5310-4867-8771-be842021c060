import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { PageKeyword } from './entities/page-keyword.entity';
import { PageKeywordController } from './page-keyword.controller';
import { Page<PERSON>eywordResolver } from './page-keyword.resolver';
import { PageKeywordService } from './page-keyword.service';

@Module({
  imports: [TypeOrmModule.forFeature([PageKeyword])],
  controllers: [PageKeywordController],
  providers: [PageKeywordService, PageKeywordResolver],
  exports: [PageKeywordService],
})
export class PageKeywordModule {}
