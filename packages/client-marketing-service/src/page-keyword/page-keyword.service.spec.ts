import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

import { PageKeyword } from './entities/page-keyword.entity';
import { PageKeywordService } from './page-keyword.service';

describe('PageKeywordService', () => {
  let service: PageKeywordService;

  // Mock the PageKeyword repository
  const mockPageKeywordRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    delete: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  // Mock the QueryRunner
  const mockQueryRunner = {
    connect: jest.fn().mockResolvedValue(undefined),
    startTransaction: jest.fn().mockResolvedValue(undefined),
    commitTransaction: jest.fn().mockResolvedValue(undefined),
    rollbackTransaction: jest.fn().mockResolvedValue(undefined),
    release: jest.fn().mockResolvedValue(undefined),
    manager: {
      createQueryBuilder: jest.fn().mockReturnValue({
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockResolvedValue({ raw: [{ id: 'test-id' }] }),
      }),
    },
    query: jest.fn().mockResolvedValue(undefined),
  };

  // Mock the DataSource
  const mockDataSource = {
    createQueryRunner: jest.fn().mockReturnValue(mockQueryRunner),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PageKeywordService,
        {
          provide: getRepositoryToken(PageKeyword),
          useValue: mockPageKeywordRepository,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<PageKeywordService>(PageKeywordService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new page keyword', async () => {
      const createPageKeywordDto = {
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
        currentRank: 1,
      };
      const savedPageKeyword = { id: '1', ...createPageKeywordDto };

      mockPageKeywordRepository.save.mockResolvedValue(savedPageKeyword);

      const result = await service.create(createPageKeywordDto as any);

      expect(mockPageKeywordRepository.save).toHaveBeenCalledWith(
        createPageKeywordDto,
      );
      expect(result).toEqual(savedPageKeyword);
    });

    it('should create a page keyword with -1 rank for non-ranking pages', async () => {
      const createPageKeywordDto = {
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
        currentRank: -1,
        originalRank: -1,
      };
      const savedPageKeyword = { id: '1', ...createPageKeywordDto };

      mockPageKeywordRepository.save.mockResolvedValue(savedPageKeyword);

      const result = await service.create(createPageKeywordDto as any);

      expect(mockPageKeywordRepository.save).toHaveBeenCalledWith(
        createPageKeywordDto,
      );
      expect(result).toEqual(savedPageKeyword);
    });
  });

  describe('findAll', () => {
    const mockQueryBuilder = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    };

    beforeEach(() => {
      mockPageKeywordRepository.createQueryBuilder.mockReturnValue(
        mockQueryBuilder,
      );
    });

    it('should return all page keywords when no companyId provided', async () => {
      const pageKeywords = [
        {
          id: '1',
          scrapedPageId: 'page-1',
          keywordId: 'keyword-1',
          currentRank: 1,
        },
        {
          id: '2',
          scrapedPageId: 'page-2',
          keywordId: 'keyword-2',
          currentRank: 2,
        },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(pageKeywords);

      const result = await service.findAll();

      expect(mockPageKeywordRepository.createQueryBuilder).toHaveBeenCalledWith(
        'pageKeyword',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'pageKeyword.scrapedPage',
        'scrapedPage',
      );
      expect(mockQueryBuilder.leftJoinAndSelect).toHaveBeenCalledWith(
        'pageKeyword.keyword',
        'keyword',
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'pageKeyword.createdAt',
        'DESC',
      );
      expect(mockQueryBuilder.where).not.toHaveBeenCalled();
      expect(result).toEqual(pageKeywords);
    });

    it('should return page keywords filtered by companyId when provided', async () => {
      const companyId = '123';
      const pageKeywords = [
        { id: '1', scrapedPageId: 'page-1', keywordId: 'keyword-1', companyId },
      ];

      mockQueryBuilder.getMany.mockResolvedValue(pageKeywords);

      const result = await service.findAll(companyId);

      expect(mockPageKeywordRepository.createQueryBuilder).toHaveBeenCalledWith(
        'pageKeyword',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'scrapedPage.companyId = :companyId',
        { companyId },
      );
      expect(result).toEqual(pageKeywords);
    });
  });

  describe('findOne', () => {
    it('should return a page keyword when found', async () => {
      const pageKeywordId = '1';
      const pageKeyword = {
        id: pageKeywordId,
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
        currentRank: 1,
      };

      mockPageKeywordRepository.findOne.mockResolvedValue(pageKeyword);

      const result = await service.findOne(pageKeywordId);

      expect(mockPageKeywordRepository.findOne).toHaveBeenCalledWith({
        where: { id: pageKeywordId },
        relations: ['scrapedPage', 'keyword'],
      });
      expect(result).toEqual(pageKeyword);
    });

    it('should throw NotFoundException when page keyword not found', async () => {
      const pageKeywordId = '1';

      mockPageKeywordRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(pageKeywordId)).rejects.toThrow(
        'PageKeyword #1 not found',
      );
      expect(mockPageKeywordRepository.findOne).toHaveBeenCalledWith({
        where: { id: pageKeywordId },
        relations: ['scrapedPage', 'keyword'],
      });
    });
  });

  describe('updatePageKeyword', () => {
    it('should update a page keyword and return the updated entity', async () => {
      const id = 'test-id';
      const updateDto = {
        keywordId: 'new-keyword-id',
        currentRank: 5,
        changeNote: 'Test change',
        updatedBy: 'user-id',
      };

      const result = await service.updatePageKeyword(id, updateDto);

      expect(mockQueryRunner.connect).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        `SET LOCAL app.change_note = $1`,
        [updateDto.changeNote],
      );
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        `SET LOCAL app.updated_by = $1`,
        [updateDto.updatedBy],
      );
      expect(
        mockQueryRunner.manager.createQueryBuilder().update,
      ).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
      expect(result).toEqual({ id: 'test-id' });
    });

    it('should handle updates without optional fields', async () => {
      const id = 'test-id';
      const updateDto = {
        keywordId: 'new-keyword-id',
      };

      await service.updatePageKeyword(id, updateDto);

      expect(mockQueryRunner.connect).toHaveBeenCalled();
      expect(mockQueryRunner.startTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.commitTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });

    it('should rollback transaction on error', async () => {
      const id = 'test-id';
      const updateDto = {
        keywordId: 'new-keyword-id',
      };
      const error = new Error('Database error');

      mockQueryRunner.manager.createQueryBuilder.mockReturnValue({
        update: jest.fn().mockReturnThis(),
        set: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        returning: jest.fn().mockReturnThis(),
        execute: jest.fn().mockRejectedValue(error),
      });

      await expect(service.updatePageKeyword(id, updateDto)).rejects.toThrow(
        error,
      );

      expect(mockQueryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(mockQueryRunner.release).toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should delete a page keyword successfully', async () => {
      const pageKeywordId = '1';
      const pageKeyword = {
        id: pageKeywordId,
        scrapedPageId: 'page-1',
        keywordId: 'keyword-1',
      };

      mockPageKeywordRepository.findOne.mockResolvedValue(pageKeyword);

      await service.remove(pageKeywordId);

      expect(mockPageKeywordRepository.delete).toHaveBeenCalledWith({
        id: pageKeywordId,
      });
    });
  });

  describe('updateRank', () => {
    it('should update the rank to -1 for non-ranking pages', async () => {
      const id = 'test-id';
      const newRank = -1;
      const updatedPageKeyword = {
        id,
        currentRank: newRank,
        rankCheckedAt: expect.any(Date),
      };

      mockPageKeywordRepository.update.mockResolvedValue({ affected: 1 });
      mockPageKeywordRepository.findOne.mockResolvedValue(updatedPageKeyword);

      const result = await service.updateRank(id, newRank);

      expect(mockPageKeywordRepository.update).toHaveBeenCalledWith(id, {
        currentRank: newRank,
        rankCheckedAt: expect.any(Date),
      });
      expect(mockPageKeywordRepository.findOne).toHaveBeenCalledWith({
        where: { id },
        relations: ['scrapedPage', 'keyword'],
      });
      expect(result).toEqual(updatedPageKeyword);
    });

    it('should update the rank from positive to -1', async () => {
      const id = 'test-id';
      const newRank = -1;
      const updatedPageKeyword = {
        id,
        currentRank: newRank,
        originalRank: 5,
        rankCheckedAt: expect.any(Date),
      };

      mockPageKeywordRepository.update.mockResolvedValue({ affected: 1 });
      mockPageKeywordRepository.findOne.mockResolvedValue(updatedPageKeyword);

      const result = await service.updateRank(id, newRank);

      expect(mockPageKeywordRepository.update).toHaveBeenCalledWith(id, {
        currentRank: newRank,
        rankCheckedAt: expect.any(Date),
      });
      expect(result).toEqual(updatedPageKeyword);
      expect(result.currentRank).toBe(-1);
    });
  });
});
