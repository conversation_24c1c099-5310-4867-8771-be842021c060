import { Field, ID, ObjectType } from '@nestjs/graphql';
import { IsUUID, IsNotEmpty, <PERSON>Date, <PERSON>Int, <PERSON>, Max } from 'class-validator';
import { Factory } from 'nestjs-seeder';
import { Company } from 'src/company/entities/company.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import {
  PrimaryGeneratedColumn,
  Column,
  Index,
  Entity,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

/**
 * PageKeyword entity
 *
 * Database Triggers:
 * 1. trigger_set_original_rank: BEFORE INSERT OR UPDATE
 *    - Automatically sets originalRank to currentRank if originalRank is null and currentRank is not null
 *    - Defined in migration: 1747773553390-page-keyword-original-rank-trigger.ts
 *
 * 2. trigger_track_keyword_insert: AFTER INSERT
 *    - Tracks keyword assignments by inserting records into page_keyword_history
 *    - Defined in migration: 1747673553390-page-keyword-history-triggers.ts
 *
 * 3. trigger_track_keyword_update: AFTER UPDATE
 *    - Tracks keyword updates by inserting records into page_keyword_history
 *    - Defined in migration: 1747673553390-page-keyword-history-triggers.ts
 */
@Entity('page_keyword')
@Index(['scrapedPageId', 'keywordId'], { unique: true })
@Index(['keywordId'])
@ObjectType()
export class PageKeyword {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'scraped_page_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  scrapedPageId: string;

  @Column({ type: 'uuid', name: 'keyword_id' })
  @IsUUID()
  @IsNotEmpty()
  @Factory(faker => faker?.string.uuid())
  @Field(() => ID)
  keywordId: string;

  /**
   * The original rank of the keyword when it was first associated with the page
   * Note: This field is automatically set to the same value as currentRank when originalRank is null
   * via the trigger_set_original_rank database trigger
   */
  @Column({ type: 'int', name: 'original_rank', nullable: true })
  @IsInt()
  @Min(-1)
  @Max(100)
  @Factory(faker => faker?.number.int({ min: 5, max: 95 }))
  @Field(() => Number, { nullable: true })
  originalRank: number | null;

  @Column({ type: 'int', name: 'current_rank', nullable: true })
  @IsInt()
  @Min(-1)
  @Max(100)
  @Factory(faker => faker?.number.int({ min: 5, max: 95 }))
  @Field(() => Number, { nullable: true })
  currentRank: number | null;

  @IsDate()
  @Column({
    name: 'rank_checked_at',
    type: 'timestamptz',
    default: null,
  })
  @Factory(faker => faker?.date.recent({ days: 7 }))
  @Field(() => Date, { nullable: true })
  rankCheckedAt: Date | null;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @ManyToOne(() => Keyword, keyword => keyword.pageKeywords)
  @JoinColumn({ name: 'keyword_id' })
  @Field(() => Keyword)
  keyword: Keyword;

  @ManyToOne(() => ScrapedPage, scrapedPage => scrapedPage.pageKeywords)
  @JoinColumn({ name: 'scraped_page_id' })
  @Field(() => ScrapedPage)
  scrapedPage: ScrapedPage;

  @Field(() => ID, { description: 'Company ID from the related page' })
  get companyId(): string {
    return this.scrapedPage?.companyId;
  }

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}
