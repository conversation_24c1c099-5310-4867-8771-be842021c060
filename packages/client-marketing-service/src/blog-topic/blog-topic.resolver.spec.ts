import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { BlogTopicResolver } from './blog-topic.resolver';
import { BlogTopicService } from './blog-topic.service';
import {
  UpdateBlogTopicInput,
  CreateBlogTopicsBulkInput,
  CreateBlogTopicsBulkResponse,
} from './dto';
import { BlogTopic, BlogTopicType } from './entities/blog-topic.entity';
import { PaginatedBlogTopic } from './entities/paginated-blog-topic.entity';

describe('BlogTopicResolver', () => {
  let resolver: BlogTopicResolver;
  let service: BlogTopicService;

  const mockBlogTopic: BlogTopic = {
    id: 'test-id',
    companyId: 'company-id',
    topic: 'Test Topic',
    blogPostJobId: null,
    airopsExecutionId: null,
    cmsPostId: null,
    neighborhoodId: null,
    parentTopic: null,
    blogTitle: null,
    rationale: null,
    type: BlogTopicType.article,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPaginatedResult: PaginatedBlogTopic = {
    data: [mockBlogTopic],
    totalItems: 1,
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPreviousPage: false,
  };

  const mockAuthContext = {
    can: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogTopicResolver,
        {
          provide: BlogTopicService,
          useValue: {
            findAll: jest.fn(),
            findOne: jest.fn(),
            createBulk: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    resolver = module.get<BlogTopicResolver>(BlogTopicResolver);
    service = module.get<BlogTopicService>(BlogTopicService);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated blog topics when user has permission', async () => {
      mockAuthContext.can.mockResolvedValue(true);
      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedResult);

      const result = await resolver.findAll(mockAuthContext as any);

      expect(result).toEqual(mockPaginatedResult);
      expect(service.findAll).toHaveBeenCalledWith(undefined, undefined, 1, 50);
    });

    it('should apply filters and pagination when provided', async () => {
      mockAuthContext.can.mockResolvedValue(true);
      jest.spyOn(service, 'findAll').mockResolvedValue(mockPaginatedResult);

      const filters = { topic: 'Test' };
      const pagination = { page: 2, limit: 20 };

      await resolver.findAll(
        mockAuthContext as any,
        'company-id',
        filters,
        pagination,
      );

      expect(service.findAll).toHaveBeenCalledWith(
        'company-id',
        filters,
        2,
        20,
      );
    });

    it('should throw ForbiddenException when user lacks permission', async () => {
      mockAuthContext.can.mockResolvedValue(false);

      await expect(resolver.findAll(mockAuthContext as any)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('findOne', () => {
    it('should return a blog topic when user has permission', async () => {
      mockAuthContext.can.mockResolvedValue(true);
      jest.spyOn(service, 'findOne').mockResolvedValue(mockBlogTopic);

      const result = await resolver.findOne('test-id', mockAuthContext as any);

      expect(result).toEqual(mockBlogTopic);
      expect(service.findOne).toHaveBeenCalledWith('test-id');
    });

    it('should throw ForbiddenException when user lacks permission', async () => {
      mockAuthContext.can.mockResolvedValue(false);

      await expect(
        resolver.findOne('test-id', mockAuthContext as any),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('createBulk', () => {
    const mockInput: CreateBlogTopicsBulkInput = {
      blogTopics: [
        {
          topic: 'Test Topic 1',
          blogTitle: 'Test Title 1',
          companyId: 'company-1',
          rationale: 'Test rationale 1',
          type: BlogTopicType.article,
        },
        {
          topic: 'Test Topic 2',
          blogTitle: 'Test Title 2',
          companyId: 'company-2',
          rationale: 'Test rationale 2',
          type: BlogTopicType.listicle,
        },
      ],
    };

    const mockResponse: CreateBlogTopicsBulkResponse = {
      createdBlogTopics: [
        {
          id: 'topic-1',
          topic: 'Test Topic 1',
          companyId: 'company-1',
          type: BlogTopicType.article,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as BlogTopic,
        {
          id: 'topic-2',
          topic: 'Test Topic 2',
          companyId: 'company-2',
          type: BlogTopicType.listicle,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as BlogTopic,
      ],
    };

    it('should create blog topics in bulk when user has permission', async () => {
      mockAuthContext.can.mockResolvedValue(true);
      jest.spyOn(service, 'createBulk').mockResolvedValue(mockResponse);

      const result = await resolver.createBulk(
        mockInput,
        mockAuthContext as any,
      );

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'blogTopic',
        'create',
        undefined,
      );
      expect(service.createBulk).toHaveBeenCalledWith(mockInput.blogTopics);
      expect(result).toEqual(mockResponse);
    });

    it('should throw ForbiddenException when user lacks permission', async () => {
      mockAuthContext.can.mockResolvedValue(false);

      await expect(
        resolver.createBulk(mockInput, mockAuthContext as any),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'blogTopic',
        'create',
        undefined,
      );
      expect(service.createBulk).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    const mockId = 'topic-1';
    const mockInput: UpdateBlogTopicInput = {
      blogPostJobId: 'job-1',
      airopsExecutionId: 'exec-1',
      type: BlogTopicType.listicle,
    };

    const mockUpdatedBlogTopic: BlogTopic = {
      id: mockId,
      topic: 'Updated Topic',
      companyId: 'company-1',
      blogPostJobId: 'job-1',
      airopsExecutionId: 'exec-1',
      type: BlogTopicType.listicle,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as BlogTopic;

    it('should update blog topic when user has permission', async () => {
      mockAuthContext.can.mockResolvedValue(true);
      jest.spyOn(service, 'update').mockResolvedValue(mockUpdatedBlogTopic);

      const result = await resolver.update(
        mockId,
        mockInput,
        mockAuthContext as any,
      );

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'blogTopic',
        'update',
        undefined,
      );
      expect(service.update).toHaveBeenCalledWith(mockId, mockInput);
      expect(result).toEqual(mockUpdatedBlogTopic);
    });

    it('should throw ForbiddenException when user lacks permission', async () => {
      mockAuthContext.can.mockResolvedValue(false);

      await expect(
        resolver.update(mockId, mockInput, mockAuthContext as any),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'blogTopic',
        'update',
        undefined,
      );
      expect(service.update).not.toHaveBeenCalled();
    });
  });

  describe('type field handling', () => {
    it('should pass type field correctly to service in createBulk', async () => {
      mockAuthContext.can.mockResolvedValue(true);

      const inputWithType: CreateBlogTopicsBulkInput = {
        blogTopics: [
          {
            topic: 'Article Topic',
            companyId: 'company-1',
            type: BlogTopicType.article,
          },
        ],
      };

      const responseWithType: CreateBlogTopicsBulkResponse = {
        createdBlogTopics: [
          {
            id: 'topic-1',
            topic: 'Article Topic',
            companyId: 'company-1',
            type: BlogTopicType.article,
            createdAt: new Date(),
            updatedAt: new Date(),
          } as BlogTopic,
        ],
      };

      jest.spyOn(service, 'createBulk').mockResolvedValue(responseWithType);

      const result = await resolver.createBulk(
        inputWithType,
        mockAuthContext as any,
      );

      expect(service.createBulk).toHaveBeenCalledWith([
        {
          topic: 'Article Topic',
          companyId: 'company-1',
          type: BlogTopicType.article,
        },
      ]);
      expect(result.createdBlogTopics[0].type).toBe(BlogTopicType.article);
    });

    it('should pass type field correctly to service in update', async () => {
      mockAuthContext.can.mockResolvedValue(true);

      const updateInputWithType: UpdateBlogTopicInput = {
        type: BlogTopicType.listicle,
      };

      const updatedTopicWithType: BlogTopic = {
        id: 'topic-1',
        topic: 'Updated Topic',
        companyId: 'company-1',
        type: BlogTopicType.listicle,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BlogTopic;

      jest.spyOn(service, 'update').mockResolvedValue(updatedTopicWithType);

      const result = await resolver.update(
        'topic-1',
        updateInputWithType,
        mockAuthContext as any,
      );

      expect(service.update).toHaveBeenCalledWith('topic-1', {
        type: BlogTopicType.listicle,
      });
      expect(result.type).toBe(BlogTopicType.listicle);
    });
  });
});
