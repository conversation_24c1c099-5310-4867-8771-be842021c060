import { Field, InputType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

import { BlogTopicType } from '../entities/blog-topic.entity';

@InputType()
export class BlogTopicFiltersInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  topic?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsUUID()
  blogPostJobId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  airopsExecutionId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsUUID()
  cmsPostId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsUUID()
  neighborhoodId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  parentTopic?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  blogTitle?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  rationale?: string;

  @Field(() => BlogTopicType, { nullable: true })
  @IsOptional()
  @IsEnum(BlogTopicType)
  type?: BlogTopicType;
}
