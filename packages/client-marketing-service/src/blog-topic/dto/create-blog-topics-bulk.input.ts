import { Field, InputType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  ValidateNested,
  ArrayMaxSize,
} from 'class-validator';

import { CreateBlogTopicInput } from './create-blog-topic.input';

@InputType()
export class CreateBlogTopicsBulkInput {
  @Field(() => [CreateBlogTopicInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateBlogTopicInput)
  @ArrayNotEmpty()
  @ArrayMaxSize(50, { message: 'Cannot create more than 50 topics at once' })
  blogTopics: CreateBlogTopicInput[];
}
