import { Field, ID, InputType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { IsAtLeastOneDefined } from 'src/common/validators';

import { BlogTopicType } from '../entities/blog-topic.entity';

@InputType()
export class UpdateBlogTopicInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  topic?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  blogTitle?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsUUID()
  blogPostJobId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  airopsExecutionId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsUUID()
  cmsPostId?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsUUID()
  neighborhoodId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  parentTopic?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  rationale?: string;

  @Field(() => BlogTopicType, { nullable: true })
  @IsOptional()
  @IsEnum(BlogTopicType)
  type?: BlogTopicType;

  // Dummy property to trigger the at-least-one validation
  @IsAtLeastOneDefined([
    'topic',
    'blogTitle',
    'blogPostJobId',
    'airopsExecutionId',
    'cmsPostId',
    'neighborhoodId',
    'parentTopic',
    'rationale',
    'type',
  ])
  private _atLeastOneDefined?: any;
}
