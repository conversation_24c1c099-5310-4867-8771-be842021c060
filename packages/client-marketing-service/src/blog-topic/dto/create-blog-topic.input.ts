import { Field, ID, InputType } from '@nestjs/graphql';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

import { BlogTopicType } from '../entities/blog-topic.entity';

@InputType()
export class CreateBlogTopicInput {
  @Field(() => String)
  @IsNotEmpty()
  @IsString()
  topic: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  blogTitle?: string;

  @Field(() => ID, { nullable: true })
  @IsOptional()
  @IsUUID()
  neighborhoodId?: string;

  @Field(() => ID)
  @IsNotEmpty()
  @IsUUID()
  companyId: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  rationale?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  airopsExecutionId?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  parentTopic?: string;

  @Field(() => BlogTopicType)
  @IsNotEmpty()
  @IsEnum(BlogTopicType)
  type: BlogTopicType;
}
