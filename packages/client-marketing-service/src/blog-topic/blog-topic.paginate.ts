import { FilterOperator, PaginateConfig } from 'nestjs-paginate';

import { BlogTopic } from './entities/blog-topic.entity';

export const paginateConfig: PaginateConfig<BlogTopic> = {
  sortableColumns: [
    'companyId',
    'topic',
    'blogPostJobId',
    'airopsExecutionId',
    'cmsPostId',
    'neighborhoodId',
    'parentTopic',
    'blogTitle',
    'rationale',
    'createdAt',
    'updatedAt',
  ],
  defaultSortBy: [['createdAt', 'DESC']],
  maxLimit: 100,
  defaultLimit: 50,
  filterableColumns: {
    id: [FilterOperator.EQ, FilterOperator.IN],
    companyId: [FilterOperator.EQ, FilterOperator.IN],
    topic: [FilterOperator.ILIKE],
    blogPostJobId: [FilterOperator.EQ, FilterOperator.IN],
    airopsExecutionId: [FilterOperator.EQ, FilterOperator.ILIKE],
    cmsPostId: [FilterOperator.EQ, FilterOperator.IN],
    neighborhoodId: [FilterOperator.EQ, FilterOperator.IN],
    parentTopic: [FilterOperator.ILIKE],
    blogTitle: [FilterOperator.ILIKE],
    rationale: [FilterOperator.ILIKE],
    createdAt: [
      FilterOperator.BTW,
      FilterOperator.LTE,
      FilterOperator.GTE,
      FilterOperator.EQ,
    ],
    updatedAt: [
      FilterOperator.BTW,
      FilterOperator.LTE,
      FilterOperator.GTE,
      FilterOperator.EQ,
    ],
  },
};
