import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { BlogTopicResolver } from './blog-topic.resolver';
import { BlogTopicService } from './blog-topic.service';
import { BlogTopic } from './entities/blog-topic.entity';

@Module({
  imports: [TypeOrmModule.forFeature([BlogTopic])],
  providers: [BlogTopicService, BlogTopicResolver],
  exports: [BlogTopicService],
})
export class BlogTopicModule {}
