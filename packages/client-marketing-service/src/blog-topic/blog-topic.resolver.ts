import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { AuthContext } from 'src/graphql.decorator';

import { BlogTopicService } from './blog-topic.service';
import {
  BlogTopicFiltersInput,
  BlogTopicPaginationInput,
  UpdateBlogTopicInput,
  CreateBlogTopicsBulkInput,
  CreateBlogTopicsBulkResponse,
} from './dto';
import { BlogTopic } from './entities/blog-topic.entity';
import { PaginatedBlogTopic } from './entities/paginated-blog-topic.entity';

@Resolver(() => BlogTopic)
export class BlogTopicResolver {
  constructor(private readonly blogTopicService: BlogTopicService) {}

  @Query(() => PaginatedBlogTopic, {
    name: 'blogTopics',
    description: 'Get all blog topics with filters and pagination',
  })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
    @Args('filters', { type: () => BlogTopicFiltersInput, nullable: true })
    filters?: BlogTopicFiltersInput,
    @Args('pagination', {
      type: () => BlogTopicPaginationInput,
      nullable: true,
    })
    pagination?: BlogTopicPaginationInput,
  ): Promise<PaginatedBlogTopic> {
    const canRead = await authContext.can('blogTopic', 'read', undefined);

    if (!canRead) {
      throw new ForbiddenException();
    }

    const page = pagination?.page || 1;
    const limit = pagination?.limit || 50;

    const result = await this.blogTopicService.findAll(
      companyId,
      filters,
      page,
      limit,
    );

    return result as PaginatedBlogTopic;
  }

  @Query(() => BlogTopic, {
    name: 'blogTopic',
    description: 'Get a blog topic by ID',
  })
  async findOne(
    @Args('id', { type: () => ID }) id: string,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<BlogTopic> {
    const canRead = await authContext.can('blogTopic', 'read', undefined);

    if (!canRead) {
      throw new ForbiddenException();
    }

    return this.blogTopicService.findOne(id);
  }

  @Query(() => BlogTopic, {
    name: 'blogTopicSelector',
    description:
      'Select a blog topic for the company (currently random, may become smarter in the future)',
    nullable: true,
  })
  async selectBlogTopic(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID }) companyId: string,
    @Args('preferredNeighborhoods', {
      type: () => [ID],
      nullable: true,
    })
    preferredNeighborhoods?: string[],
  ): Promise<BlogTopic | null> {
    const canRead = await authContext.can('blogTopic', 'read', undefined);

    if (!canRead) {
      throw new ForbiddenException();
    }

    return this.blogTopicService.findBlogTopicSelector(
      companyId,
      preferredNeighborhoods,
    );
  }

  @Mutation(() => CreateBlogTopicsBulkResponse, {
    name: 'createBlogTopicsBulk',
    description: 'Create multiple blog topics in bulk using single INSERT',
  })
  async createBulk(
    @Args('input') input: CreateBlogTopicsBulkInput,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<CreateBlogTopicsBulkResponse> {
    const canCreate = await authContext.can('blogTopic', 'create', undefined);

    if (!canCreate) {
      throw new ForbiddenException();
    }

    return this.blogTopicService.createBulk(input.blogTopics);
  }

  @Mutation(() => BlogTopic, {
    name: 'updateBlogTopic',
    description:
      'Update an existing blog topic (usage for locking with job id)',
  })
  async update(
    @Args('id', { type: () => ID }) id: string,
    @Args('input') input: UpdateBlogTopicInput,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
  ): Promise<BlogTopic> {
    const canUpdate = await authContext.can('blogTopic', 'update', undefined);

    if (!canUpdate) {
      throw new ForbiddenException();
    }

    return this.blogTopicService.update(id, input);
  }
}
