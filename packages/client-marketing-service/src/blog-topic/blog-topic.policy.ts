import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';

import type { AppPolicyRegistry } from '../auth.module';

@Injectable()
export class BlogTopicPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  /**
   * Checks if the user has permission to read blog topics
   * Only super users can access this functionality
   * @returns boolean
   */
  read = () => {
    return this.auth.isSuper();
  };

  /**
   * Checks if the user has permission to create blog topics
   * Only super users can access this functionality
   * @returns boolean
   */
  create = () => {
    return this.auth.isSuper();
  };

  /**
   * Checks if the user has permission to update blog topics
   * Only super users can access this functionality
   * @returns boolean
   */
  update = () => {
    return this.auth.isSuper();
  };

  /**
   * Checks if the user has permission to delete blog topics
   * Only super users can access this functionality
   * @returns boolean
   */
  delete = () => {
    return this.auth.isSuper();
  };
}
