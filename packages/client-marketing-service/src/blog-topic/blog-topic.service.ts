import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, PaginateQuery } from 'nestjs-paginate';
import { FindOptionsWhere, Repository } from 'typeorm';

import { paginateConfig } from './blog-topic.paginate';
import {
  CreateBlogTopicInput,
  CreateBlogTopicsBulkResponse,
  UpdateBlogTopicInput,
} from './dto';
import { BlogTopic, BlogTopicType } from './entities/blog-topic.entity';
import { PaginateToGQL } from '../common/utils/pagination';

interface PaginatedBlogTopicResult {
  data: BlogTopic[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface BlogTopicFilters {
  topic?: string;
  blogPostJobId?: string;
  airopsExecutionId?: string;
  cmsPostId?: string;
  neighborhoodId?: string;
  parentTopic?: string;
  blogTitle?: string;
  rationale?: string;
  type?: BlogTopicType;
}

@Injectable()
export class BlogTopicService {
  private readonly logger = new Logger(BlogTopicService.name);

  constructor(
    @InjectRepository(BlogTopic)
    private readonly blogTopicRepository: Repository<BlogTopic>,
  ) {}

  async findAll(
    companyId?: string,
    filters?: BlogTopicFilters,
    page: number = 1,
    limit: number = 50,
  ): Promise<PaginatedBlogTopicResult> {
    try {
      // Create paginate query object
      const paginateQuery: PaginateQuery = {
        page,
        limit,
        path: '',
      };

      // Build where clause for TypeORM
      const whereClause = this.buildWhereClause(companyId, filters);

      // Use nestjs-paginate to get paginated results
      const paginatedResult = await paginate<BlogTopic>(
        paginateQuery,
        this.blogTopicRepository,
        {
          ...paginateConfig,
          where: whereClause,
        },
      );

      // Transform the result to match our GraphQL schema
      const paginatedBlogTopic: PaginatedBlogTopicResult =
        PaginateToGQL(paginatedResult);
      return paginatedBlogTopic;
    } catch (error) {
      this.logger.error(
        `Failed to fetch blog topics: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to fetch blog topics');
    }
  }

  private buildWhereClause(
    companyId?: string,
    filters?: BlogTopicFilters,
  ): FindOptionsWhere<BlogTopic> {
    const whereClause: FindOptionsWhere<BlogTopic> = {};

    if (!companyId && !filters) {
      return {};
    }

    if (companyId) {
      whereClause.companyId = companyId;
    }

    if (filters) {
      if (filters.topic) {
        whereClause.topic = filters.topic;
      }
      if (filters.blogPostJobId) {
        whereClause.blogPostJobId = filters.blogPostJobId;
      }
      if (filters.airopsExecutionId) {
        whereClause.airopsExecutionId = filters.airopsExecutionId;
      }
      if (filters.cmsPostId) {
        whereClause.cmsPostId = filters.cmsPostId;
      }
      if (filters.neighborhoodId) {
        whereClause.neighborhoodId = filters.neighborhoodId;
      }
      if (filters.parentTopic) {
        whereClause.parentTopic = filters.parentTopic;
      }
      if (filters.blogTitle) {
        whereClause.blogTitle = filters.blogTitle;
      }
      if (filters.rationale) {
        whereClause.rationale = filters.rationale;
      }
      if (filters.type) {
        whereClause.type = filters.type;
      }
    }

    return whereClause;
  }

  // Future enhancement: Make this smarter about topic selection based on
  // recent posts, neighborhoods, topics and parent topics.
  async findBlogTopicSelector(
    companyId: string,
    preferredNeighborhoods?: string[],
  ): Promise<BlogTopic | null> {
    try {
      // First try to find a topic in preferred neighborhoods if provided
      if (preferredNeighborhoods && preferredNeighborhoods.length > 0) {
        const preferredResult = await this.blogTopicRepository
          .createQueryBuilder('blogTopic')
          .where('blogTopic.companyId = :companyId', { companyId })
          .andWhere('blogTopic.cmsPostId IS NULL')
          .andWhere(
            'blogTopic.neighborhoodId IN (:...preferredNeighborhoods)',
            {
              preferredNeighborhoods,
            },
          )
          .orderBy('RANDOM()')
          .limit(1)
          .getOne();

        if (preferredResult) {
          return preferredResult;
        }
      }

      // Fallback to any available topic
      const result = await this.blogTopicRepository
        .createQueryBuilder('blogTopic')
        .where('blogTopic.companyId = :companyId', { companyId })
        .andWhere('blogTopic.cmsPostId IS NULL')
        .orderBy('RANDOM()')
        .limit(1)
        .getOne();

      return result;
    } catch (error) {
      this.logger.error(
        `Failed to select blog topic: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException('Failed to select blog topic');
    }
  }

  async findOne(id: string): Promise<BlogTopic> {
    this.logger.log(`Fetching blog topic with ID: ${id}`);

    try {
      const blogTopic = await this.blogTopicRepository.findOne({
        where: { id },
      });

      if (!blogTopic) {
        this.logger.error(`Blog topic with ID ${id} not found`);
        throw new NotFoundException(`Blog topic with ID ${id} not found`);
      }

      return blogTopic;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error fetching blog topic with ID ${id}:`, error);
      throw new InternalServerErrorException('Error fetching blog topic');
    }
  }

  async createBulk(
    blogTopics: CreateBlogTopicInput[],
  ): Promise<CreateBlogTopicsBulkResponse> {
    if (!blogTopics || blogTopics.length === 0) {
      throw new BadRequestException('Blog topics array cannot be empty');
    }

    this.logger.log(`Creating ${blogTopics.length} blog topics in bulk`);

    try {
      // Create entities from inputs
      const entities = blogTopics.map(input =>
        this.blogTopicRepository.create({
          topic: input.topic,
          blogTitle: input.blogTitle,
          neighborhoodId: input.neighborhoodId,
          companyId: input.companyId,
          rationale: input.rationale,
          airopsExecutionId: input.airopsExecutionId,
          parentTopic: input.parentTopic,
          type: input.type,
        }),
      );

      // Single INSERT with multiple VALUES - if any fails, all fail
      const savedEntities = await this.blogTopicRepository.save(entities);

      this.logger.log(
        `Successfully created ${savedEntities.length} blog topics in bulk`,
      );

      return {
        createdBlogTopics: savedEntities,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create blog topics in bulk: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to create blog topics in bulk: ${error.message}`,
      );
    }
  }

  /**
   * Optimized update method using partial updates
   */
  async update(
    id: string,
    updateData: UpdateBlogTopicInput,
  ): Promise<BlogTopic> {
    this.logger.log(`Updating blog topic with ID: ${id}`);

    try {
      // Use update for better performance instead of find + save
      const result = await this.blogTopicRepository.update(id, updateData);

      if (result.affected === 0) {
        throw new NotFoundException(`Blog topic with ID ${id} not found`);
      }

      // Return the updated entity
      const updatedBlogTopic = await this.findOne(id);

      this.logger.log(`Successfully updated blog topic with ID: ${id}`);
      return updatedBlogTopic;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating blog topic with ID ${id}:`, error);
      throw new InternalServerErrorException(
        `Error updating blog topic: ${error.message}`,
      );
    }
  }
}
