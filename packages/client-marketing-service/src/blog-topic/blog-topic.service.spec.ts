/* eslint-disable @typescript-eslint/no-require-imports */
import {
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';

import { BlogTopicService } from './blog-topic.service';
import { CreateBlogTopicInput, UpdateBlogTopicInput } from './dto';
import { BlogTopic, BlogTopicType } from './entities/blog-topic.entity';

// Mock the paginate function
jest.mock('nestjs-paginate', () => ({
  paginate: jest.fn(),
}));

// Mock the PaginateToGQL utility
jest.mock('../common/utils/pagination', () => ({
  PaginateToGQL: jest.fn(),
}));

// Mock the paginate config
jest.mock('./blog-topic.paginate', () => ({
  paginateConfig: {
    sortableColumns: ['id', 'companyId', 'topic'],
    defaultSortBy: [['createdAt', 'DESC']],
    maxLimit: 100,
    defaultLimit: 50,
    filterableColumns: {},
  },
}));

describe('BlogTopicService', () => {
  let service: BlogTopicService;

  const mockBlogTopic: BlogTopic = {
    id: 'test-id',
    companyId: 'company-id',
    topic: 'Test Topic',
    blogPostJobId: null,
    airopsExecutionId: null,
    cmsPostId: null,
    neighborhoodId: null,
    parentTopic: null,
    blogTitle: null,
    rationale: null,
    type: BlogTopicType.article,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
  };

  const mockRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogTopicService,
        {
          provide: getRepositoryToken(BlogTopic),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<BlogTopicService>(BlogTopicService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should call paginate with correct parameters', async () => {
      const { paginate } = require('nestjs-paginate');
      const { PaginateToGQL } = require('../common/utils/pagination');

      const mockPaginatedResult = {
        data: [mockBlogTopic],
        meta: {
          totalItems: 1,
          currentPage: 1,
          totalPages: 1,
          itemsPerPage: 10,
        },
      };

      paginate.mockResolvedValue(mockPaginatedResult);
      PaginateToGQL.mockReturnValue({
        data: [mockBlogTopic],
        totalItems: 1,
        currentPage: 1,
        totalPages: 1,
        itemsPerPage: 10,
        hasNextPage: false,
        hasPreviousPage: false,
      });

      await service.findAll('company-id', { topic: 'test' }, 1, 10);

      expect(paginate).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a blog topic by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockBlogTopic);

      const result = await service.findOne('test-id');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
      expect(result).toEqual(mockBlogTopic);
    });

    it('should throw NotFoundException when blog topic not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'non-existent-id' },
      });
    });

    it('should handle repository errors and throw InternalServerErrorException', async () => {
      const mockError = new Error('Database connection failed');
      mockRepository.findOne.mockRejectedValue(mockError);

      await expect(service.findOne('test-id')).rejects.toThrow(
        InternalServerErrorException,
      );

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'test-id' },
      });
    });
  });

  describe('createBulk', () => {
    const mockBlogTopics: CreateBlogTopicInput[] = [
      {
        topic: 'Test Topic 1',
        blogTitle: 'Test Title 1',
        companyId: 'company-1',
        rationale: 'Test rationale 1',
        type: BlogTopicType.article,
      },
      {
        topic: 'Test Topic 2',
        blogTitle: 'Test Title 2',
        companyId: 'company-2',
        rationale: 'Test rationale 2',
        type: BlogTopicType.listicle,
      },
    ];

    const mockCreatedBlogTopics: BlogTopic[] = [
      {
        id: 'topic-1',
        topic: 'Test Topic 1',
        blogTitle: 'Test Title 1',
        companyId: 'company-1',
        rationale: 'Test rationale 1',
        type: BlogTopicType.article,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BlogTopic,
      {
        id: 'topic-2',
        topic: 'Test Topic 2',
        blogTitle: 'Test Title 2',
        companyId: 'company-2',
        rationale: 'Test rationale 2',
        type: BlogTopicType.listicle,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BlogTopic,
    ];

    it('should create multiple blog topics successfully using single INSERT', async () => {
      // Mock create to return different entities for each call
      mockRepository.create
        .mockReturnValueOnce(mockCreatedBlogTopics[0])
        .mockReturnValueOnce(mockCreatedBlogTopics[1]);
      mockRepository.save.mockResolvedValue(mockCreatedBlogTopics);

      const result = await service.createBulk(mockBlogTopics);

      expect(mockRepository.create).toHaveBeenCalledTimes(2);
      expect(mockRepository.create).toHaveBeenNthCalledWith(1, {
        topic: 'Test Topic 1',
        blogTitle: 'Test Title 1',
        neighborhoodId: undefined,
        companyId: 'company-1',
        rationale: 'Test rationale 1',
        airopsExecutionId: undefined,
        parentTopic: undefined,
        type: BlogTopicType.article,
      });
      expect(mockRepository.create).toHaveBeenNthCalledWith(2, {
        topic: 'Test Topic 2',
        blogTitle: 'Test Title 2',
        neighborhoodId: undefined,
        companyId: 'company-2',
        rationale: 'Test rationale 2',
        airopsExecutionId: undefined,
        parentTopic: undefined,
        type: BlogTopicType.listicle,
      });
      expect(mockRepository.save).toHaveBeenCalledWith([
        mockCreatedBlogTopics[0],
        mockCreatedBlogTopics[1],
      ]);
      expect(result.createdBlogTopics).toHaveLength(2);
      expect(result.createdBlogTopics).toEqual(mockCreatedBlogTopics);
    });

    it('should throw BadRequestException for empty input', async () => {
      await expect(service.createBulk([])).rejects.toThrow(BadRequestException);
      expect(mockRepository.create).not.toHaveBeenCalled();
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should throw BadRequestException for null input', async () => {
      await expect(service.createBulk(null as any)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockRepository.create).not.toHaveBeenCalled();
      expect(mockRepository.save).not.toHaveBeenCalled();
    });

    it('should accept up to 50 topics without validation errors', async () => {
      const largeArray = Array.from({ length: 50 }, (_, i) => ({
        topic: `Test Topic ${i}`,
        companyId: `company-${i}`,
        rationale: `Test rationale ${i}`,
        type: BlogTopicType.article,
      }));

      mockRepository.create.mockReturnValue(mockCreatedBlogTopics[0]);
      mockRepository.save.mockResolvedValue(
        largeArray.map((_, i) => ({
          ...mockCreatedBlogTopics[0],
          id: `topic-${i}`,
          topic: `Test Topic ${i}`,
        })),
      );

      const result = await service.createBulk(largeArray);

      expect(result.createdBlogTopics).toHaveLength(50);
      expect(mockRepository.create).toHaveBeenCalledTimes(50);
      expect(mockRepository.save).toHaveBeenCalledTimes(1);
    });

    it('should fail entirely if any single topic fails', async () => {
      mockRepository.create.mockReturnValue(mockCreatedBlogTopics[0]);
      mockRepository.save.mockRejectedValue(
        new Error('Database constraint violation'),
      );

      await expect(service.createBulk(mockBlogTopics)).rejects.toThrow(
        InternalServerErrorException,
      );

      expect(mockRepository.create).toHaveBeenCalledTimes(2);
      expect(mockRepository.save).toHaveBeenCalledTimes(1);
    });
  });

  describe('update', () => {
    const mockId = 'topic-1';
    const mockUpdateData: UpdateBlogTopicInput = {
      blogPostJobId: 'job-1',
      airopsExecutionId: 'exec-1',
      type: BlogTopicType.listicle,
    };

    const mockUpdatedBlogTopic: BlogTopic = {
      id: mockId,
      topic: 'Updated Topic',
      companyId: 'company-1',
      blogPostJobId: 'job-1',
      airopsExecutionId: 'exec-1',
      type: BlogTopicType.listicle,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as BlogTopic;

    it('should update blog topic successfully using optimized update', async () => {
      mockRepository.update.mockResolvedValue({ affected: 1 });
      mockRepository.findOne.mockResolvedValue(mockUpdatedBlogTopic);

      const result = await service.update(mockId, mockUpdateData);

      expect(mockRepository.update).toHaveBeenCalledWith(
        mockId,
        mockUpdateData,
      );
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: mockId },
      });
      expect(result).toEqual(mockUpdatedBlogTopic);
    });

    it('should throw NotFoundException when blog topic does not exist', async () => {
      mockRepository.update.mockResolvedValue({ affected: 0 });

      await expect(service.update(mockId, mockUpdateData)).rejects.toThrow(
        'Blog topic with ID topic-1 not found',
      );

      expect(mockRepository.update).toHaveBeenCalledWith(
        mockId,
        mockUpdateData,
      );
      expect(mockRepository.findOne).not.toHaveBeenCalled();
    });

    it('should handle repository errors and throw InternalServerErrorException', async () => {
      const mockError = new Error('Database connection failed');
      mockRepository.update.mockRejectedValue(mockError);

      await expect(service.update(mockId, mockUpdateData)).rejects.toThrow(
        InternalServerErrorException,
      );

      expect(mockRepository.update).toHaveBeenCalledWith(
        mockId,
        mockUpdateData,
      );
      expect(mockRepository.findOne).not.toHaveBeenCalled();
    });
  });

  describe('type field validation', () => {
    it('should handle ARTICLE type correctly', async () => {
      const mockBlogTopicWithArticle: CreateBlogTopicInput = {
        topic: 'Article Topic',
        companyId: 'company-1',
        type: BlogTopicType.article,
      };

      const mockCreatedTopic: BlogTopic = {
        id: 'topic-1',
        topic: 'Article Topic',
        companyId: 'company-1',
        type: BlogTopicType.article,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BlogTopic;

      mockRepository.create.mockReturnValue(mockCreatedTopic);
      mockRepository.save.mockResolvedValue([mockCreatedTopic]);

      const result = await service.createBulk([mockBlogTopicWithArticle]);

      expect(mockRepository.create).toHaveBeenCalledWith({
        topic: 'Article Topic',
        companyId: 'company-1',
        type: BlogTopicType.article,
        neighborhoodId: undefined,
        airopsExecutionId: undefined,
        parentTopic: undefined,
      });
      expect(result.createdBlogTopics[0].type).toBe(BlogTopicType.article);
    });

    it('should handle LISTICLE type correctly', async () => {
      const mockBlogTopicWithListicle: CreateBlogTopicInput = {
        topic: 'Listicle Topic',
        companyId: 'company-1',
        type: BlogTopicType.listicle,
      };

      const mockCreatedTopic: BlogTopic = {
        id: 'topic-1',
        topic: 'Listicle Topic',
        companyId: 'company-1',
        type: BlogTopicType.listicle,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as BlogTopic;

      mockRepository.create.mockReturnValue(mockCreatedTopic);
      mockRepository.save.mockResolvedValue([mockCreatedTopic]);

      const result = await service.createBulk([mockBlogTopicWithListicle]);

      expect(mockRepository.create).toHaveBeenCalledWith({
        topic: 'Listicle Topic',
        companyId: 'company-1',
        type: BlogTopicType.listicle,
        neighborhoodId: undefined,
        airopsExecutionId: undefined,
        parentTopic: undefined,
      });
      expect(result.createdBlogTopics[0].type).toBe(BlogTopicType.listicle);
    });
  });

  describe('findBlogTopicSelector', () => {
    const mockTopicWithNeighborhood: BlogTopic = {
      id: 'topic-1',
      companyId: 'company-1',
      topic: 'Downtown Restaurant Guide',
      neighborhoodId: 'neighborhood-1',
      blogPostJobId: null,
      airopsExecutionId: null,
      cmsPostId: null,
      parentTopic: null,
      blogTitle: null,
      rationale: null,
      type: BlogTopicType.article,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockTopicWithoutNeighborhood: BlogTopic = {
      id: 'topic-2',
      companyId: 'company-1',
      topic: 'General Business Guide',
      neighborhoodId: null,
      blogPostJobId: null,
      airopsExecutionId: null,
      cmsPostId: null,
      parentTopic: null,
      blogTitle: null,
      rationale: null,
      type: BlogTopicType.article,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    beforeEach(() => {
      // Reset query builder mocks before each test
      jest.clearAllMocks();
      mockQueryBuilder.getOne.mockReset();
      mockRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);
    });

    it('should return topic from preferred neighborhood when available', async () => {
      // Mock first query (preferred neighborhoods) to return a topic
      mockQueryBuilder.getOne.mockResolvedValueOnce(mockTopicWithNeighborhood);

      const result = await service.findBlogTopicSelector('company-1', [
        'neighborhood-1',
      ]);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith(
        'blogTopic',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'blogTopic.companyId = :companyId',
        { companyId: 'company-1' },
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.cmsPostId IS NULL',
      );
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.neighborhoodId IN (:...preferredNeighborhoods)',
        { preferredNeighborhoods: ['neighborhood-1'] },
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('RANDOM()');
      expect(mockQueryBuilder.limit).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockTopicWithNeighborhood);
    });

    it('should fallback to any topic when no preferred neighborhood topics available', async () => {
      // Mock first query (preferred neighborhoods) to return null
      // Mock second query (fallback) to return a topic
      mockQueryBuilder.getOne
        .mockResolvedValueOnce(null) // First call returns null
        .mockResolvedValueOnce(mockTopicWithoutNeighborhood); // Second call returns topic

      const result = await service.findBlogTopicSelector('company-1', [
        'neighborhood-1',
      ]);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
      expect(mockQueryBuilder.getOne).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockTopicWithoutNeighborhood);
    });

    it('should work without preferred neighborhoods and return any available topic', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(mockTopicWithoutNeighborhood);

      const result = await service.findBlogTopicSelector('company-1');

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.cmsPostId IS NULL',
      );
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        'blogTopic.neighborhoodId IN (:...preferredNeighborhoods)',
        expect.anything(),
      );
      expect(result).toEqual(mockTopicWithoutNeighborhood);
    });

    it('should work with empty preferred neighborhoods array', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(mockTopicWithoutNeighborhood);

      const result = await service.findBlogTopicSelector('company-1', []);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledTimes(1);
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.cmsPostId IS NULL',
      );
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        'blogTopic.neighborhoodId IN (:...preferredNeighborhoods)',
        expect.anything(),
      );
      expect(result).toEqual(mockTopicWithoutNeighborhood);
    });

    it('should work with multiple preferred neighborhoods', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(mockTopicWithNeighborhood);

      const result = await service.findBlogTopicSelector('company-1', [
        'neighborhood-1',
        'neighborhood-2',
      ]);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.neighborhoodId IN (:...preferredNeighborhoods)',
        { preferredNeighborhoods: ['neighborhood-1', 'neighborhood-2'] },
      );
      expect(result).toEqual(mockTopicWithNeighborhood);
    });

    it('should return null when no topics are available', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(null);

      const result = await service.findBlogTopicSelector('company-1');

      expect(result).toBeNull();
    });

    it('should return null when no topics are available in preferred neighborhoods or fallback', async () => {
      mockQueryBuilder.getOne
        .mockResolvedValueOnce(null) // First call for preferred neighborhoods
        .mockResolvedValueOnce(null); // Second call for fallback

      const result = await service.findBlogTopicSelector('company-1', [
        'neighborhood-1',
      ]);

      expect(mockRepository.createQueryBuilder).toHaveBeenCalledTimes(2);
      expect(result).toBeNull();
    });

    it('should handle database errors and throw InternalServerErrorException', async () => {
      const mockError = new Error('Database connection failed');
      mockQueryBuilder.getOne.mockRejectedValue(mockError);

      await expect(service.findBlogTopicSelector('company-1')).rejects.toThrow(
        InternalServerErrorException,
      );
      await expect(service.findBlogTopicSelector('company-1')).rejects.toThrow(
        'Failed to select blog topic',
      );
    });

    it('should handle database errors in preferred neighborhoods query', async () => {
      const mockError = new Error('Database query failed');
      mockQueryBuilder.getOne.mockRejectedValue(mockError);

      await expect(
        service.findBlogTopicSelector('company-1', ['neighborhood-1']),
      ).rejects.toThrow(InternalServerErrorException);
    });

    it('should only query cmsPostId IS NULL condition', async () => {
      mockQueryBuilder.getOne.mockResolvedValue(mockTopicWithoutNeighborhood);

      await service.findBlogTopicSelector('company-1');

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'blogTopic.cmsPostId IS NULL',
      );
      expect(mockQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        expect.stringContaining('airopsExecutionId'),
        expect.anything(),
      );
    });
  });
});
