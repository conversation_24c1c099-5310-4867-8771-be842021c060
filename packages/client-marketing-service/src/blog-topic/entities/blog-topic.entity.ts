import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';

export enum BlogTopicType {
  listicle = 'listicle',
  article = 'article',
}

registerEnumType(BlogTopicType, {
  name: 'BlogTopicType',
});

/**
 * BlogTopic Entity
 * As part of Super Bloom's Blog Topic Generation feature,
 * this entity is used to track the topic generation process and rationale for content creation.
 */
@Entity('blog_topic')
@Unique(['companyId', 'topic'])
@ObjectType()
export class BlogTopic {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'company_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Field(() => ID)
  companyId: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  @IsString()
  @Field(() => String)
  topic: string;

  @Column({ type: 'uuid', name: 'blog_post_job_id', nullable: true })
  @IsUUID()
  @Field(() => ID, { nullable: true })
  blogPostJobId: string | null;

  @Column({ type: 'text', name: 'airops_execution_id', nullable: true })
  @IsString()
  @Field(() => String, { nullable: true })
  airopsExecutionId: string | null;

  @Column({ type: 'uuid', name: 'cms_post_id', nullable: true })
  @IsUUID()
  @Field(() => ID, { nullable: true })
  cmsPostId: string | null;

  @Column({ type: 'uuid', name: 'neighborhood_id', nullable: true })
  @IsUUID()
  @Field(() => ID, { nullable: true })
  neighborhoodId: string | null;

  @Column({ type: 'text', name: 'parent_topic', nullable: true })
  @IsString()
  @Field(() => String, { nullable: true })
  parentTopic: string | null;

  @Column({ type: 'text', name: 'blog_title', nullable: true })
  @IsString()
  @Field(() => String, { nullable: true })
  blogTitle: string | null;

  @Column({ type: 'text', name: 'rationale', nullable: true })
  @IsString()
  @Field(() => String, { nullable: true })
  rationale: string | null;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @Column({ type: 'enum', enum: BlogTopicType })
  @Field(() => BlogTopicType)
  @IsEnum(BlogTopicType)
  type: BlogTopicType;
}
