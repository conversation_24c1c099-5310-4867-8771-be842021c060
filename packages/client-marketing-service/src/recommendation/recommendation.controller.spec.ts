import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Paginated } from 'nestjs-paginate';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { getMockedProvider } from 'src/common/utils/get-mocked-provider.util';
import { Group } from 'src/group/entities/group.entity';
import { CreateRecommendationDto } from 'src/recommendation/dto/create-recommendation.dto';
import {
  Recommendation,
  RecommendationStatus,
} from 'src/recommendation/entities/recommendation.entity';
import { RecommendationController } from 'src/recommendation/recommendation.controller';
import { RecommendationService } from 'src/recommendation/recommendation.service';

describe('RecommendationController', () => {
  let controller: RecommendationController;
  let service: RecommendationService;

  const groupId = 'group-id';
  const companyId = 'company-id';

  const mockRecommendation: Recommendation = {
    id: 'test-id',
    scrapeId: 'scrape-id',
    groupId: groupId,
    type: RecommendationType.META_TITLE,
    currentValue: 'Current Title',
    recommendationValue: 'Recommended Title',
    reasoning: 'Better for SEO',
    status: RecommendationStatus.PENDING,
    rejectionReason: null,
    metadata: {},
    createdAt: new Date(),
    updatedAt: new Date(),
    statusUpdatedAt: new Date(),
    rememberStatus: () => {},
    updateStatusUpdatedAtTimestamp: () => {},
    scrape: {
      id: 'scrape-id',
      scrapedPageId: 'page-id',
      page: {
        id: 'page-id',
        companyId: companyId,
      },
    } as any,
  };

  const mockCreateRecommendationDto: CreateRecommendationDto = {
    groupId: groupId,
    type: RecommendationType.META_TITLE,
    currentValue: 'Current Title',
    recommendationValue: 'Recommended Title',
    reasoning: 'Better for SEO',
    scrapeId: 'scrape-id',
  };

  const mockFindAllResult: Paginated<Recommendation> = {
    data: [mockRecommendation],
    meta: {
      itemsPerPage: 20,
      totalItems: 1,
      currentPage: 1,
      totalPages: 1,
      sortBy: [['createdAt', 'DESC']],
      searchBy: [],
      search: '',
      filter: {},
      select: [],
    },
    links: {
      first: '?page=1&limit=20',
      previous: '',
      current: '?page=1&limit=20',
      next: '',
      last: '?page=1&limit=20',
    },
  };

  const mockRecommendationRepository = {
    create: jest.fn().mockReturnValue(mockRecommendation),
    save: jest.fn().mockResolvedValue(mockRecommendation),
    find: jest.fn().mockResolvedValue([mockRecommendation]),
    findOne: jest.fn().mockResolvedValue(mockRecommendation),
    remove: jest.fn().mockResolvedValue(mockRecommendation),
  };

  const mockGroup = {
    id: 'group-id',
  };

  const mockGroupRepository = {
    findOne: jest.fn().mockResolvedValue(mockGroup),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RecommendationController],
      providers: [
        getMockedProvider(RecommendationService),
        {
          provide: getRepositoryToken(Recommendation),
          useValue: mockRecommendationRepository,
        },
        {
          provide: getRepositoryToken(Group),
          useValue: mockGroupRepository,
        },
      ],
    }).compile();

    controller = module.get<RecommendationController>(RecommendationController);
    service = module.get<RecommendationService>(RecommendationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated recommendations', async () => {
      (service.findAll as jest.Mock).mockResolvedValue(mockFindAllResult);

      const queryParams = { page: 1, limit: 20, path: '/recommendation' };
      const result = await controller.findAll(queryParams);

      expect(result).toEqual(mockFindAllResult);
      expect(service.findAll).toHaveBeenCalledWith(queryParams);
    });

    it('should handle service errors', async () => {
      const error = new Error('Database error');
      (service.findAll as jest.Mock).mockRejectedValue(error);

      const queryParams = { page: 1, limit: 20, path: '/recommendation' };
      await expect(controller.findAll(queryParams)).rejects.toThrow(error);
      expect(service.findAll).toHaveBeenCalledWith(queryParams);
    });

    it('should pass pagination parameters correctly', async () => {
      (service.findAll as jest.Mock).mockResolvedValue(mockFindAllResult);

      const queryParams = {
        page: 2,
        limit: 10,
        search: 'test',
        path: '/recommendation',
      };
      const result = await controller.findAll(queryParams);

      expect(result).toEqual(mockFindAllResult);
      expect(service.findAll).toHaveBeenCalledWith(queryParams);
    });
  });

  describe('findOne', () => {
    it('should return a single recommendation wrapped in data property', async () => {
      (service.findOne as jest.Mock).mockResolvedValue(mockRecommendation);

      const result = await controller.findOne('test-id');

      expect(result).toEqual({ data: mockRecommendation });
      expect(service.findOne).toHaveBeenCalledWith('test-id');
    });

    it('should handle service errors for non-existent recommendation', async () => {
      const error = new Error('Recommendation not found');
      (service.findOne as jest.Mock).mockRejectedValue(error);

      await expect(controller.findOne('non-existent-id')).rejects.toThrow(
        error,
      );
      expect(service.findOne).toHaveBeenCalledWith('non-existent-id');
    });
  });

  describe('create', () => {
    it('should create a recommendation and wrap in data property', async () => {
      (service.create as jest.Mock).mockResolvedValue(mockRecommendation);

      const result = await controller.create(mockCreateRecommendationDto);

      expect(result).toEqual({ data: mockRecommendation });
      expect(service.create).toHaveBeenCalledWith(mockCreateRecommendationDto);
    });

    it('should handle service errors during creation', async () => {
      const error = new Error('Creation failed');
      (service.create as jest.Mock).mockRejectedValue(error);

      await expect(
        controller.create(mockCreateRecommendationDto),
      ).rejects.toThrow(error);
      expect(service.create).toHaveBeenCalledWith(mockCreateRecommendationDto);
    });

    it('should create recommendation with minimal data', async () => {
      const minimalDto: CreateRecommendationDto = {
        groupId: 'group-id',
        type: RecommendationType.META_DESCRIPTION,
        currentValue: 'Current Desc',
        recommendationValue: 'New Desc',
        reasoning: 'Better SEO',
        scrapeId: 'scrape-id-2',
      };

      const createdRecommendation = { ...mockRecommendation, ...minimalDto };
      (service.create as jest.Mock).mockResolvedValue(createdRecommendation);

      const result = await controller.create(minimalDto);

      expect(result).toEqual({ data: createdRecommendation });
      expect(service.create).toHaveBeenCalledWith(minimalDto);
    });
  });
});
