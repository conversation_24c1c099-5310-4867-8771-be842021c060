import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiExtraModels, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PaginateQuery, Paginate } from 'nestjs-paginate';
import { PaginateQueryOptions } from 'src/common/decorators/paginate-query-options.decorator';
import { DataResponseDto } from 'src/common/interceptors/response-wrapper.interceptor';
import { PaginatedResponse } from 'src/common/openapi/paginated';
import { CreateRecommendationDto } from 'src/recommendation/dto/create-recommendation.dto';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { responses } from 'src/recommendation/recommendation.openapi';
import { paginateConfig } from 'src/recommendation/recommendation.paginate';
import { RecommendationService } from 'src/recommendation/recommendation.service';

@ApiExtraModels(DataResponseDto, PaginatedResponse, Recommendation)
@Controller('recommendation')
@ApiTags('Recommendation')
export class RecommendationController {
  constructor(private readonly recommendationsService: RecommendationService) {}

  @Get()
  @PaginateQueryOptions(paginateConfig)
  @ApiResponse(responses.findAll)
  async findAll(@Paginate() query: PaginateQuery) {
    // Paginate wraps the result in a data property
    return await this.recommendationsService.findAll(query);
  }

  @Get(':id')
  @ApiResponse(responses.findOne)
  async findOne(@Param('id') id: string) {
    return { data: await this.recommendationsService.findOne(id) };
  }

  @Post()
  @ApiResponse(responses.create)
  async create(@Body() createRecommendationDto: CreateRecommendationDto) {
    return {
      data: await this.recommendationsService.create(createRecommendationDto),
    };
  }

  // @Post('generate')
  // @ApiResponse({
  //   status: 200,
  //   description: 'Generate SEO recommendations for title, description, and H1',
  // })
  // generate(@Body() generateDto: GenerateRecommendationDto) {
  //   try {
  //     return {
  //       data: this.recommendationsService.generateRecommendation(generateDto),
  //     };
  //   } catch (error) {
  //     throw new InternalServerErrorException(
  //       'Failed to generate recommendation',
  //       error,
  //     );
  //   }
  // }

  // @Patch(':id')
  // @ApiResponse(responses.update)
  // @ApiOperation({
  //   summary: 'Update recommendation',
  //   description:
  //     'Update recommendation. Use dryRun=true to update the recommendation without sending the update to the STAR API. Useful for updating recommendations without side effects.',
  // })
  // async update(
  //   @Param('id') id: string,
  //   @Body() updateRecommendationDto: UpdateRecommendationDto,
  // ) {
  //   return {
  //     data: await this.recommendationsService.update(
  //       id,
  //       updateRecommendationDto,
  //     ),
  //   };
  // }

  // @Delete(':id')
  // @ApiResponse(responses.remove)
  // async remove(@Param('id') id: string) {
  //   return { data: await this.recommendationsService.remove(id) };
  // }
}
