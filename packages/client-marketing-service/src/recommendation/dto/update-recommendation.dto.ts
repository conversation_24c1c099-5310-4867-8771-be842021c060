import { PartialType, ApiProperty, OmitType } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean } from 'class-validator';
import { CreateRecommendationDto } from 'src/recommendation/dto/create-recommendation.dto';

export class UpdateRecommendationDto extends PartialType(
  OmitType(CreateRecommendationDto, ['groupId'] as const),
) {
  @IsString()
  @IsOptional()
  rejectionReason?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({
    description: 'Skip STAR API website update',
    example: false,
  })
  skipStarWebsiteUpdate?: boolean;
}
