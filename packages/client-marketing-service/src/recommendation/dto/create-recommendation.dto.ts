import { Type } from 'class-transformer';
import {
  IsUUID,
  IsString,
  IsObject,
  IsEnum,
  IsOptional,
  IsNotEmpty,
} from 'class-validator';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { RecommendationStatus } from 'src/recommendation/entities/recommendation.entity';

export class CreateRecommendationDto {
  @IsUUID()
  @IsNotEmpty()
  groupId: string;

  @IsUUID()
  @IsNotEmpty()
  scrapeId: string;

  @IsEnum(RecommendationType)
  @IsNotEmpty()
  type: RecommendationType;

  @IsString()
  @IsOptional()
  currentValue?: string;

  @IsString()
  @IsNotEmpty()
  recommendationValue: string;

  @IsString()
  @IsNotEmpty()
  reasoning: string;

  @IsEnum(RecommendationStatus)
  @IsOptional()
  status?: RecommendationStatus;

  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;
}
