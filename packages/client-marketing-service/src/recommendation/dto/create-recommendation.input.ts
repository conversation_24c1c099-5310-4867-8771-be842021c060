import { Field, ID, InputType } from '@nestjs/graphql';
import {
  IsUUID,
  IsString,
  IsObject,
  IsEnum,
  IsOptional,
  IsNotEmpty,
} from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { RecommendationStatus } from 'src/recommendation/entities/recommendation.entity';

@InputType()
export class CreateRecommendationInput {
  @IsUUID()
  @IsNotEmpty()
  @Field(() => ID)
  groupId: string;

  @IsUUID()
  @IsNotEmpty()
  @Field(() => ID)
  scrapeId: string;

  @IsEnum(RecommendationType)
  @IsNotEmpty()
  @Field(() => RecommendationType)
  type: RecommendationType;

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  currentValue?: string;

  @IsString()
  @IsNotEmpty()
  @Field()
  recommendationValue: string;

  @IsString()
  @IsNotEmpty()
  @Field()
  reasoning: string;

  @IsEnum(RecommendationStatus)
  @IsOptional()
  @Field(() => RecommendationStatus, { nullable: true })
  status?: RecommendationStatus;

  @IsObject()
  @IsOptional()
  @Field(() => GraphQLJSONObject, { nullable: true })
  metadata?: Record<string, any>;
}
