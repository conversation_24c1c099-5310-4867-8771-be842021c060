import { ApiResponseOptions } from '@nestjs/swagger';
import { paginatedSchema } from 'src/common/openapi/paginated';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

export const responses = {
  findAll: {
    status: 200,
    description: 'Returns a paginated list of recommendations',
    schema: paginatedSchema(Recommendation),
  } as ApiResponseOptions,
  findOne: {
    status: 200,
    description: 'Returns a single recommendation',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Recommendation',
        },
      },
    },
  } as ApiResponseOptions,
  create: {
    status: 200,
    description: 'Returns the created recommendation',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Recommendation',
        },
      },
    },
  } as ApiResponseOptions,
  update: {
    status: 200,
    description: 'Returns the updated recommendation',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Recommendation',
        },
      },
    },
  } as ApiResponseOptions,
  remove: {
    status: 200,
    description: 'Returns the removed recommendation',
    schema: {
      properties: {
        data: {
          $ref: '#/components/schemas/Recommendation',
        },
      },
    },
  } as ApiResponseOptions,
};
