import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Paginated } from 'nestjs-paginate/lib/paginate';
import {
  createGroupServiceMock,
  createMockAuthContext,
  createMockCreateRecommendationInput,
  createMockRecommendation,
  createRecommendationServiceMock,
  mockAuthorizationFailure,
  mockAuthorizationSuccess,
} from 'src/test-utils';

import {
  Recommendation,
  RecommendationStatus,
} from './entities/recommendation.entity';
import { RecommendationResolver } from './recommendation.resolver';
import { RecommendationService } from './recommendation.service';
import { GroupService } from '../group/group.service';

describe('RecommendationResolver', () => {
  let resolver: RecommendationResolver;
  let service: RecommendationService;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockRecommendationService: ReturnType<
    typeof createRecommendationServiceMock
  >;
  let mockGroupService: ReturnType<typeof createGroupServiceMock>;

  const mockRecommendation = createMockRecommendation();
  const mockCreateInput = createMockCreateRecommendationInput();

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockRecommendationService = createRecommendationServiceMock();
    mockGroupService = createGroupServiceMock();

    const module = await Test.createTestingModule({
      providers: [
        RecommendationResolver,
        { provide: RecommendationService, useValue: mockRecommendationService },
        { provide: GroupService, useValue: mockGroupService },
      ],
    }).compile();

    resolver = module.get<RecommendationResolver>(RecommendationResolver);
    service = module.get<RecommendationService>(RecommendationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test resolver definition
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of Recommendations when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      const mockRecommendations = [mockRecommendation];
      const result = { data: mockRecommendations };
      mockRecommendationService.findAll.mockResolvedValue(
        result as unknown as Paginated<Recommendation>,
      );

      const response = await resolver.findAll(mockAuthContext);

      expect(response).toEqual(result.data);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        undefined,
      );
      expect(service.findAll).toHaveBeenCalledWith({
        path: '',
        limit: 100,
        page: 1,
        filter: {},
      });
    });

    it('should return filtered recommendations when companyId is provided', async () => {
      const companyId = 'test-company-id';
      mockAuthorizationSuccess(mockAuthContext);
      const mockRecommendations = [mockRecommendation];
      const result = { data: mockRecommendations };
      mockRecommendationService.findAll.mockResolvedValue(
        result as unknown as Paginated<Recommendation>,
      );

      const response = await resolver.findAll(mockAuthContext, companyId);

      expect(response).toEqual(result.data);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        companyId,
      );
      expect(service.findAll).toHaveBeenCalledWith({
        path: '',
        limit: 100,
        page: 1,
        filter: { 'scrape.scrapedPage.companyId': companyId },
      });
    });

    it('should return filtered recommendations when groupId is provided', async () => {
      const groupId = 'test-group-id';
      mockAuthorizationSuccess(mockAuthContext);
      const mockRecommendations = [mockRecommendation];
      const result = { data: mockRecommendations };
      mockRecommendationService.findAll.mockResolvedValue(
        result as unknown as Paginated<Recommendation>,
      );

      const response = await resolver.findAll(
        mockAuthContext,
        undefined,
        groupId,
      );

      expect(response).toEqual(result.data);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        undefined,
      );
      expect(service.findAll).toHaveBeenCalledWith({
        path: '',
        limit: 100,
        page: 1,
        filter: { groupId: groupId },
      });
    });

    it('should return filtered recommendations when both companyId and groupId are provided', async () => {
      const companyId = 'test-company-id';
      const groupId = 'test-group-id';
      mockAuthorizationSuccess(mockAuthContext);
      const mockRecommendations = [mockRecommendation];
      const result = { data: mockRecommendations };
      mockRecommendationService.findAll.mockResolvedValue(
        result as unknown as Paginated<Recommendation>,
      );

      const response = await resolver.findAll(
        mockAuthContext,
        companyId,
        groupId,
      );

      expect(response).toEqual(result.data);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        companyId,
      );
      expect(service.findAll).toHaveBeenCalledWith({
        path: '',
        limit: 100,
        page: 1,
        filter: {
          'scrape.scrapedPage.companyId': companyId,
          groupId: groupId,
        },
      });
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(resolver.findAll(mockAuthContext)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        undefined,
      );
      expect(service.findAll).not.toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single Recommendation when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockRecommendationService.findOne.mockResolvedValue(mockRecommendation);

      const result = await resolver.findOne(mockAuthContext, '1');

      expect(result).toEqual(mockRecommendation);
      expect(service.findOne).toHaveBeenCalledWith('1');
      const companyId = resolver.companyId(mockRecommendation);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'read',
        companyId,
      );
    });
  });

  describe('ResolveField methods', () => {
    describe('metadata', () => {
      it('should return metadata from the Recommendation', () => {
        const mockMetadata = { generationInfo: 'AI generated' };
        const recommendation = {
          metadata: mockMetadata,
        } as unknown as Recommendation;
        expect(resolver.metadata(recommendation)).toEqual(mockMetadata);
      });

      it('should return null metadata when metadata is null', () => {
        const recommendation = { metadata: null } as unknown as Recommendation;
        expect(resolver.metadata(recommendation)).toBeNull();
      });
    });

    describe('scrapedPage', () => {
      it('should return scrapedPage from recommendation.scrape.scrapedPage', () => {
        const mockPage = { id: 'page-1', companyId: 'company-1' };
        const recommendation = {
          scrape: { scrapedPage: mockPage },
        } as unknown as Recommendation;
        expect(resolver.scrapedPage(recommendation)).toEqual(mockPage);
      });

      it('should return null when scrape is null', () => {
        const recommendation = { scrape: null } as unknown as Recommendation;
        expect(resolver.scrapedPage(recommendation)).toBeNull();
      });
    });

    describe('companyId', () => {
      it('should return companyId from page', () => {
        const recommendation = {
          scrape: { scrapedPage: { companyId: 'test-company-id' } },
        } as unknown as Recommendation;
        expect(resolver.companyId(recommendation)).toBe('test-company-id');
      });

      it('should return null when scrape is null', () => {
        const recommendation = { scrape: null } as unknown as Recommendation;
        expect(resolver.companyId(recommendation)).toBeNull();
      });
    });

    describe('group', () => {
      it('should return group from recommendation if present', async () => {
        const mockGroup = { id: 'group-1', name: 'Test Group' };
        const recommendation = {
          group: mockGroup,
        } as unknown as Recommendation;
        const result = await resolver.group(recommendation);
        expect(result).toBe(mockGroup);
      });

      it('should fetch group from groupService if not present on recommendation', async () => {
        const mockGroup = { id: 'group-2', name: 'Fetched Group' };
        const recommendation = {
          groupId: 'group-2',
          group: null,
        } as unknown as Recommendation;
        mockRecommendationService.findOne.mockResolvedValue(recommendation);
        mockGroupService.findOne.mockResolvedValue(mockGroup);
        const result = await resolver.group(recommendation);
        expect(mockGroupService.findOne).toHaveBeenCalledWith('group-2');
        expect(result).toBe(mockGroup);
      });
    });
  });

  describe('createRecommendation', () => {
    it('should create recommendation when authorized', async () => {
      const companyId = 'company-123';
      const createdRecommendation = {
        id: '1',
        ...mockCreateInput,
        status: RecommendationStatus.PENDING,
      };

      mockRecommendationService.getCompanyIdForAuthorization.mockResolvedValue(
        companyId,
      );
      mockAuthorizationSuccess(mockAuthContext);
      mockRecommendationService.create.mockResolvedValue(createdRecommendation);

      const result = await resolver.createRecommendation(
        mockAuthContext,
        mockCreateInput,
      );

      expect(result).toEqual(createdRecommendation);
      expect(
        mockRecommendationService.getCompanyIdForAuthorization,
      ).toHaveBeenCalledWith(undefined, mockCreateInput.groupId);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'create',
        companyId,
      );
      expect(mockRecommendationService.create).toHaveBeenCalledWith(
        mockCreateInput,
      );
    });

    it('should throw ForbiddenException when not authorized to create', async () => {
      const companyId = 'company-123';

      mockRecommendationService.getCompanyIdForAuthorization.mockResolvedValue(
        companyId,
      );
      mockAuthorizationFailure(mockAuthContext);

      await expect(
        resolver.createRecommendation(mockAuthContext, mockCreateInput),
      ).rejects.toThrow(ForbiddenException);
      expect(
        mockRecommendationService.getCompanyIdForAuthorization,
      ).toHaveBeenCalledWith(undefined, mockCreateInput.groupId);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'recommendation',
        'create',
        companyId,
      );
      expect(mockRecommendationService.create).not.toHaveBeenCalled();
    });
  });

  describe('applyRecommendation', () => {
    const id = '1';

    describe('when not authorized', () => {
      it('should throw ForbiddenException', async () => {
        mockAuthorizationFailure(mockAuthContext);

        await expect(
          resolver.applyRecommendation(mockAuthContext, id),
        ).rejects.toThrow(ForbiddenException);

        expect(mockAuthContext.can).toHaveBeenCalledWith(
          'recommendation',
          'apply',
          undefined,
        );

        expect(mockRecommendationService.update).not.toHaveBeenCalled();
      });
    });

    describe('when authorized', () => {
      it('should apply recommendation', async () => {
        const status = RecommendationStatus.APPLIED;
        mockRecommendationService.update = jest
          .fn()
          .mockResolvedValue({ id, status });

        mockAuthorizationSuccess(mockAuthContext);

        const result = await resolver.applyRecommendation(mockAuthContext, id);

        expect(mockRecommendationService.update).toHaveBeenCalledWith(id, {
          status,
        });

        expect(result).toEqual({ id, status });
      });
    });
  });
});
