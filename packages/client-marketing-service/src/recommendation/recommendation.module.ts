import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LangfuseModule } from 'src/common/services/langfuse/langfuse.module';
import { Group } from 'src/group/entities/group.entity';
import { GroupService } from 'src/group/group.service';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { RecommendationController } from 'src/recommendation/recommendation.controller';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';

import { RecommendationResolver } from './recommendation.resolver';

@Module({
  imports: [
    TypeOrmModule.forFeature([Recommendation, ScrapedPage, Group]),
    LangfuseModule,
  ],
  controllers: [RecommendationController],
  providers: [RecommendationService, RecommendationResolver, GroupService],
  exports: [RecommendationService],
})
export class RecommendationModule {}
