import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

export const paginateConfig: PaginateConfig<Recommendation> = {
  sortableColumns: ['updatedAt', 'type', 'status'],
  defaultSortBy: [['updatedAt', 'DESC']],
  maxLimit: 100,
  defaultLimit: 50,
  filterableColumns: {
    id: [FilterOperator.EQ, FilterOperator.IN],
    groupId: [FilterOperator.EQ, FilterOperator.IN],
    type: [FilterOperator.EQ, FilterOperator.IN],
    status: [FilterOperator.EQ, FilterOperator.IN],
    createdAt: [FilterOperator.EQ, FilterOperator.IN],
    updatedAt: [FilterOperator.EQ, FilterOperator.IN],
    'scrape.scrapedPage.companyId': [FilterOperator.EQ, FilterOperator.IN],
  },
  relations: ['scrape', 'scrape.scrapedPage'],
};
