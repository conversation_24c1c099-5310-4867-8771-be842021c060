import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { paginate } from 'nestjs-paginate';
import { Repository } from 'typeorm';

import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { UpdateRecommendationDto } from './dto/update-recommendation.dto';
import {
  Recommendation,
  RecommendationStatus,
} from './entities/recommendation.entity';
import { RecommendationService } from './recommendation.service';
import { RecommendationType } from '../common/types/scraped-element.type';
import { Group } from '../group/entities/group.entity';

jest.mock('nestjs-paginate');

describe('RecommendationService', () => {
  let service: RecommendationService;
  let recommendationRepository: jest.Mocked<Repository<Recommendation>>;
  let groupRepository: jest.Mocked<Repository<Group>>;

  const mockGroup: Group = {
    id: 'group-id',
    companyId: 'company-id',
    title: 'Test Group',
    description: 'Test Description',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as Group;

  const mockRecommendation = {
    id: 'recommendation-id',
    groupId: 'group-id',
    scrapeId: 'scrape-id',
    type: RecommendationType.META_TITLE,
    currentValue: 'Current Title',
    recommendationValue: 'New Title',
    reasoning: 'SEO improvement',
    status: RecommendationStatus.PENDING,
    rejectionReason: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    statusUpdatedAt: new Date(),
    metadata: { test: 'value' },
    group: mockGroup,
    scrape: {
      id: 'scrape-id',
      page: {
        id: 'page-id',
        companyId: 'company-id',
      },
    },
    updateStatusUpdatedAtTimestamp: jest.fn(),
    rememberStatus: jest.fn(),
  } as any;

  const mockCreateDto: CreateRecommendationDto = {
    groupId: 'group-id',
    scrapeId: 'scrape-id',
    type: RecommendationType.META_TITLE,
    currentValue: 'Current Title',
    recommendationValue: 'New Title',
    reasoning: 'SEO improvement',
    status: RecommendationStatus.PENDING,
    metadata: { test: 'value' },
  };

  const mockUpdateDto: UpdateRecommendationDto = {
    status: RecommendationStatus.APPLIED,
    metadata: { updated: 'value' },
  };

  beforeEach(async () => {
    const mockRecommendationRepository = {
      create: jest.fn(),
      save: jest.fn(),
      findOne: jest.fn(),
      remove: jest.fn(),
    };

    const mockGroupRepository = {
      findOne: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RecommendationService,
        {
          provide: getRepositoryToken(Recommendation),
          useValue: mockRecommendationRepository,
        },
        {
          provide: getRepositoryToken(Group),
          useValue: mockGroupRepository,
        },
      ],
    }).compile();

    service = module.get<RecommendationService>(RecommendationService);
    recommendationRepository = module.get<Repository<Recommendation>>(
      getRepositoryToken(Recommendation),
    ) as jest.Mocked<Repository<Recommendation>>;
    groupRepository = module.get<Repository<Group>>(
      getRepositoryToken(Group),
    ) as jest.Mocked<Repository<Group>>;

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new recommendation when group exists', async () => {
      groupRepository.findOne.mockResolvedValue(mockGroup);
      recommendationRepository.create.mockReturnValue(mockRecommendation);
      recommendationRepository.save.mockResolvedValue(mockRecommendation);

      const result = await service.create(mockCreateDto);

      expect(groupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group-id' },
      });
      expect(recommendationRepository.create).toHaveBeenCalledWith({
        scrapeId: 'scrape-id',
        type: RecommendationType.META_TITLE,
        currentValue: 'Current Title',
        recommendationValue: 'New Title',
        reasoning: 'SEO improvement',
        status: RecommendationStatus.PENDING,
        metadata: { test: 'value' },
        groupId: 'group-id',
      });
      expect(recommendationRepository.save).toHaveBeenCalledWith(
        mockRecommendation,
      );
      expect(result).toEqual(mockRecommendation);
    });

    it('should throw NotFoundException when group does not exist', async () => {
      groupRepository.findOne.mockResolvedValue(null);

      await expect(service.create(mockCreateDto)).rejects.toThrow(
        new NotFoundException('Group with ID "group-id" not found'),
      );

      expect(groupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group-id' },
      });
      expect(recommendationRepository.create).not.toHaveBeenCalled();
      expect(recommendationRepository.save).not.toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      groupRepository.findOne.mockResolvedValue(mockGroup);
      const error = new Error('Database error');
      recommendationRepository.save.mockRejectedValue(error);

      await expect(service.create(mockCreateDto)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('findAll', () => {
    const mockQuery = {
      page: 1,
      limit: 10,
      path: '/api/recommendations',
    } as any;
    const mockPaginatedResult = {
      data: [mockRecommendation],
      meta: {
        itemCount: 1,
        totalItems: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      },
      links: {
        first: 'http://example.com?page=1',
        previous: '',
        next: '',
        last: 'http://example.com?page=1',
      },
    };

    it('should return paginated recommendations', async () => {
      (paginate as jest.Mock).mockResolvedValue(mockPaginatedResult);

      const result = await service.findAll(mockQuery);

      expect(paginate).toHaveBeenCalledWith(
        mockQuery,
        recommendationRepository,
        expect.objectContaining({
          relations: ['scrape', 'scrape.scrapedPage'],
        }),
      );
      expect(result).toEqual(mockPaginatedResult);
    });

    it('should handle paginate errors', async () => {
      const error = new Error('Pagination error');
      (paginate as jest.Mock).mockRejectedValue(error);

      await expect(service.findAll(mockQuery)).rejects.toThrow(
        'Pagination error',
      );
    });
  });

  describe('findOne', () => {
    it('should return a recommendation when found', async () => {
      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);

      const result = await service.findOne('recommendation-id');

      expect(recommendationRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'recommendation-id' },
        relations: ['scrape', 'scrape.scrapedPage', 'group'],
      });
      expect(result).toEqual(mockRecommendation);
    });

    it('should throw NotFoundException when recommendation not found', async () => {
      recommendationRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('not-found')).rejects.toThrow(
        new NotFoundException('Recommendation with ID "not-found" not found'),
      );
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      recommendationRepository.findOne.mockRejectedValue(error);

      await expect(service.findOne('recommendation-id')).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('update', () => {
    it('should update a recommendation successfully', async () => {
      const updatedRecommendation = {
        ...mockRecommendation,
        status: RecommendationStatus.APPLIED,
        metadata: { test: 'value', updated: 'value' },
        group: { ...mockGroup, appliedAt: expect.any(Date) },
      };

      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      groupRepository.findOne.mockResolvedValue(mockGroup);
      groupRepository.save.mockResolvedValue(mockGroup);
      recommendationRepository.save.mockResolvedValue(updatedRecommendation);

      const result = await service.update('recommendation-id', mockUpdateDto);

      expect(recommendationRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'recommendation-id' },
        relations: ['scrape', 'scrape.scrapedPage', 'group'],
      });
      expect(groupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group-id' },
      });
      expect(groupRepository.save).toHaveBeenCalled();
      expect(recommendationRepository.save).toHaveBeenCalled();
      expect(result).toEqual(updatedRecommendation);
    });

    it('should update recommendation with job IDs for logging', async () => {
      const jobIds = {
        recommendationJobId: 'rec-job-id',
        groupJobId: 'group-job-id',
      };

      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      groupRepository.findOne.mockResolvedValue(mockGroup);
      groupRepository.save.mockResolvedValue(mockGroup);
      recommendationRepository.save.mockResolvedValue(mockRecommendation);

      const result = await service.update(
        'recommendation-id',
        mockUpdateDto,
        jobIds,
      );

      expect(result).toEqual(mockRecommendation);
    });

    it('should handle skipStarWebsiteUpdate flag', async () => {
      const updateDtoWithSkip = {
        ...mockUpdateDto,
        skipStarWebsiteUpdate: true,
      };

      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      groupRepository.findOne.mockResolvedValue(mockGroup);
      groupRepository.save.mockResolvedValue(mockGroup);
      recommendationRepository.save.mockResolvedValue(mockRecommendation);

      const result = await service.update(
        'recommendation-id',
        updateDtoWithSkip,
      );

      expect(result).toEqual(mockRecommendation);
    });

    it('should merge metadata correctly', async () => {
      const recommendationWithMetadata = {
        ...mockRecommendation,
        metadata: { existing: 'value', test: 'oldValue' },
      };

      recommendationRepository.findOne.mockResolvedValue(
        recommendationWithMetadata,
      );
      groupRepository.findOne.mockResolvedValue(mockGroup);
      groupRepository.save.mockResolvedValue(mockGroup);
      recommendationRepository.save.mockImplementation(rec =>
        Promise.resolve(rec as any),
      );

      await service.update('recommendation-id', {
        metadata: { updated: 'newValue', test: 'updatedValue' },
      });

      // The service logic assigns new metadata from DTO first, then merges with old metadata if it exists
      // Since metadata exists, it merges old metadata with new metadata (DTO metadata overwrites)
      expect(recommendationRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: {
            existing: 'value',
            test: 'oldValue',
            updated: 'newValue',
          },
        }),
      );
    });

    it('should throw NotFoundException when recommendation not found', async () => {
      recommendationRepository.findOne.mockResolvedValue(null);

      await expect(service.update('not-found', mockUpdateDto)).rejects.toThrow(
        new NotFoundException('Recommendation with ID "not-found" not found'),
      );
    });

    it('should throw NotFoundException when group not found', async () => {
      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      groupRepository.findOne.mockResolvedValue(null);

      await expect(
        service.update('recommendation-id', mockUpdateDto),
      ).rejects.toThrow(
        new NotFoundException(
          'Group with ID "group-id" not found. GroupJobId: N/A, RecommendationJobId: N/A',
        ),
      );
    });

    it('should handle recommendation without groupId', async () => {
      const recommendationWithoutGroup = {
        ...mockRecommendation,
        groupId: undefined,
        group: null,
      };

      recommendationRepository.findOne.mockResolvedValue(
        recommendationWithoutGroup,
      );
      recommendationRepository.save.mockResolvedValue(
        recommendationWithoutGroup,
      );

      const result = await service.update('recommendation-id', mockUpdateDto);

      expect(groupRepository.findOne).not.toHaveBeenCalled();
      expect(result).toEqual(recommendationWithoutGroup);
    });

    it('should handle skipStarWebsiteUpdate in metadata', async () => {
      const recommendationWithSkipMeta = {
        ...mockRecommendation,
        metadata: { skipStarWebsiteUpdate: true },
      };

      recommendationRepository.findOne.mockResolvedValue(
        recommendationWithSkipMeta,
      );
      groupRepository.findOne.mockResolvedValue(mockGroup);
      groupRepository.save.mockResolvedValue(mockGroup);
      recommendationRepository.save.mockResolvedValue(
        recommendationWithSkipMeta,
      );

      const result = await service.update('recommendation-id', mockUpdateDto);

      expect(result).toEqual(recommendationWithSkipMeta);
    });
  });

  describe('remove', () => {
    it('should remove a recommendation successfully', async () => {
      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      recommendationRepository.remove.mockResolvedValue(mockRecommendation);

      const result = await service.remove('recommendation-id');

      expect(recommendationRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'recommendation-id' },
        relations: ['scrape', 'scrape.scrapedPage', 'group'],
      });
      expect(recommendationRepository.remove).toHaveBeenCalledWith(
        mockRecommendation,
      );
      expect(result).toEqual(mockRecommendation);
    });

    it('should throw NotFoundException when recommendation not found', async () => {
      recommendationRepository.findOne.mockResolvedValue(null);

      await expect(service.remove('not-found')).rejects.toThrow(
        new NotFoundException('Recommendation with ID "not-found" not found'),
      );

      expect(recommendationRepository.remove).not.toHaveBeenCalled();
    });

    it('should handle repository errors', async () => {
      recommendationRepository.findOne.mockResolvedValue(mockRecommendation);
      const error = new Error('Remove failed');
      recommendationRepository.remove.mockRejectedValue(error);

      await expect(service.remove('recommendation-id')).rejects.toThrow(
        'Remove failed',
      );
    });
  });

  describe('getCompanyIdForAuthorization', () => {
    it('should return companyId when groupId is provided and group exists', async () => {
      groupRepository.findOne.mockResolvedValue(mockGroup);

      const result = await service.getCompanyIdForAuthorization(
        undefined,
        'group-id',
      );

      expect(groupRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'group-id' },
      });
      expect(result).toBe('company-id');
    });

    it('should return undefined when groupId is provided but group not found', async () => {
      groupRepository.findOne.mockResolvedValue(null);

      const result = await service.getCompanyIdForAuthorization(
        undefined,
        'group-id',
      );

      expect(result).toBeUndefined();
    });

    it('should return undefined when no groupId is provided', async () => {
      const result = await service.getCompanyIdForAuthorization('page-id');

      expect(groupRepository.findOne).not.toHaveBeenCalled();
      expect(result).toBeUndefined();
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      groupRepository.findOne.mockRejectedValue(error);

      await expect(
        service.getCompanyIdForAuthorization(undefined, 'group-id'),
      ).rejects.toThrow('Database error');
    });
  });
});
