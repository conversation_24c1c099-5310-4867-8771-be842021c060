import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Repository } from 'typeorm';

import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { UpdateRecommendationDto } from './dto/update-recommendation.dto';
import { Recommendation } from './entities/recommendation.entity';
import { paginateConfig } from './recommendation.paginate';
import { Group } from '../group/entities/group.entity';

@Injectable()
export class RecommendationService {
  private readonly logger = new Logger(RecommendationService.name);

  constructor(
    @InjectRepository(Recommendation)
    private readonly recommendationRepository: Repository<Recommendation>,
    @InjectRepository(Group)
    private readonly groupRepository: Repository<Group>,
  ) {}

  async create(createRecommendationDto: CreateRecommendationDto) {
    const { groupId, ...recommendationData } = createRecommendationDto;

    // Verify the group exists
    const group = await this.groupRepository.findOne({
      where: { id: groupId },
    });
    if (!group) {
      throw new NotFoundException(`Group with ID "${groupId}" not found`);
    }

    // Create the recommendation with IDs instead of entity references
    const recommendation = this.recommendationRepository.create({
      ...recommendationData,
      groupId,
    });

    return this.recommendationRepository.save(recommendation);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<Recommendation>> {
    const result = await paginate<Recommendation>(
      query,
      this.recommendationRepository,
      {
        ...paginateConfig,
        relations: ['scrape', 'scrape.scrapedPage'], // Load scrape and page through scrape
      },
    );

    return result;
  }

  async findOne(id: string): Promise<Recommendation> {
    const recommendation = await this.recommendationRepository.findOne({
      where: { id },
      relations: ['scrape', 'scrape.scrapedPage', 'group'], // Load scrape and page through scrape
    });

    if (!recommendation) {
      throw new NotFoundException(`Recommendation with ID "${id}" not found`);
    }

    return recommendation;
  }

  async update(
    id: string,
    updateRecommendationDto: UpdateRecommendationDto,
    jobIds?: {
      recommendationJobId: string | undefined;
      groupJobId: string | undefined;
    },
  ) {
    const { recommendationJobId, groupJobId } = jobIds ?? {};

    this.logger.debug('Updating recommendation', {
      updateRecommendationDto,
      groupJobId: groupJobId ?? 'N/A',
      recommendationJobId: recommendationJobId ?? 'N/A',
    });
    const recommendation = await this.findOne(id);

    this.logger.debug('Recommendation found', {
      recommendationId: id,
      recommendation,
      groupJobId: groupJobId ?? 'N/A',
      recommendationJobId: recommendationJobId ?? 'N/A',
    });

    const { skipStarWebsiteUpdate, ...recDtoData } = updateRecommendationDto;

    const { groupId, metadata } = recommendation;

    if (groupId) {
      const group = await this.groupRepository.findOne({
        where: { id: groupId },
      });
      if (!group) {
        throw new NotFoundException(
          `Group with ID "${groupId}" not found. GroupJobId: ${groupJobId ?? 'N/A'}, RecommendationJobId: ${recommendationJobId ?? 'N/A'}`,
        );
      }
      recommendation.group = group;
    }

    const oldStatus = recommendation.status;
    // Update non-metadata properties
    Object.assign(recommendation, recDtoData);

    // Merge metadata if provided instead of completely replacing it
    if (metadata) {
      recommendation.metadata = {
        ...recommendation.metadata,
        ...metadata,
      };
    }

    // Skip star API website update if skipStarWebsiteUpdate is true in either the request or metadata
    this.logger.debug('Checking skipStar flags', {
      skipStarWebsiteUpdate,
      metadataSkipStar: recommendation.metadata?.skipStarWebsiteUpdate,
      metadata: recommendation.metadata,
      groupJobId: groupJobId ?? 'N/A',
      recommendationJobId: recommendationJobId ?? 'N/A',
    });
    const skipStar =
      skipStarWebsiteUpdate || recommendation.metadata?.skipStarWebsiteUpdate;

    if (skipStar) {
      this.logger.log(
        'Skipping STAR API website update on recommendation status update',
        {
          oldStatus,
          newStatus: recDtoData.status,
          companyId: recommendation.group?.companyId,
          groupJobId: groupJobId ?? 'N/A',
          recommendationJobId: recommendationJobId ?? 'N/A',
        },
      );
    }

    if (recommendation.group) {
      this.logger.log(
        `Updating group appliedAt for group: ${recommendation.group.id}`,
        {
          groupJobId: groupJobId ?? 'N/A',
          recommendationJobId: recommendationJobId ?? 'N/A',
        },
      );
      await this.groupRepository.save(recommendation.group);
    }
    this.logger.log(`Saving recommendation`, {
      recommendation,
      groupJobId: groupJobId ?? 'N/A',
      recommendationJobId: recommendationJobId ?? 'N/A',
    });
    const savedRecommendation =
      await this.recommendationRepository.save(recommendation);
    this.logger.log(`Saved recommendation`, {
      savedRecommendation,
      groupJobId: groupJobId ?? 'N/A',
      recommendationJobId: recommendationJobId ?? 'N/A',
    });
    return savedRecommendation;
  }

  async remove(id: string) {
    this.logger.log(`Removing recommendation with ID ${id}`);
    const recommendation = await this.findOne(id);
    return this.recommendationRepository.remove(recommendation);
  }

  async getCompanyIdForAuthorization(
    _pageId?: string,
    groupId?: string,
  ): Promise<string | undefined> {
    if (groupId) {
      const group = await this.groupRepository.findOne({
        where: { id: groupId },
      });
      return group?.companyId;
    }

    return undefined;
  }
}
