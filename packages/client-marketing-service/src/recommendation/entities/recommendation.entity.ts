import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import { ApiHideProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsUUID,
  IsNotEmpty,
  IsEnum,
  IsString,
  IsDate,
  IsObject,
} from 'class-validator';
import { GraphQLJSONObject } from 'graphql-type-json';
import { Factory } from 'nestjs-seeder';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Group } from 'src/group/entities/group.entity';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  BeforeUpdate,
  AfterLoad,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';

export enum RecommendationStatus {
  PENDING = 'PENDING',
  APPLIED = 'APPLIED',
  REJECTED = 'REJECTED',
}

registerEnumType(RecommendationStatus, { name: 'RecommendationStatus' });

@Entity('recommendation')
@ObjectType()
export class Recommendation {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'scrape_id', nullable: true })
  @IsUUID()
  @IsNotEmpty()
  @Field(() => ID, { nullable: true })
  scrapeId: string | null;

  @Column({ type: 'uuid', name: 'group_id' })
  @IsUUID()
  @IsNotEmpty()
  @Field(() => ID)
  groupId: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  @IsEnum(RecommendationType)
  @Factory(
    faker =>
      faker?.helpers.arrayElement(Object.values(RecommendationType)) ??
      RecommendationType.META_TITLE,
  )
  @Field(() => RecommendationType)
  type: RecommendationType;

  @IsString()
  @Column({ type: 'text', name: 'current_value', default: '' })
  @Factory(SeederFactories.recommendationCurrentValue)
  @Field(() => String, { nullable: true })
  currentValue?: string;

  @IsString()
  @Column({ type: 'text', name: 'recommendation_value' })
  @IsNotEmpty()
  @Factory(SeederFactories.recommendationValue)
  @Field(() => String)
  recommendationValue: string;

  @IsString()
  @Column({ type: 'text' })
  @IsNotEmpty()
  @Factory(SeederFactories.recommendationReasoning)
  @Field(() => String)
  reasoning: string;

  @Column({ type: 'text', default: RecommendationStatus.PENDING })
  @IsEnum(RecommendationStatus)
  @Factory(() => {
    // Weighted random selection: 60% PENDING, 25% APPLIED, 15% REJECTED
    const random = Math.random() * 100;
    if (random < 60) return RecommendationStatus.PENDING;
    if (random < 85) return RecommendationStatus.APPLIED;
    return RecommendationStatus.REJECTED;
  })
  @Field(() => RecommendationStatus)
  status: RecommendationStatus;

  @Column({ type: 'text', name: 'rejection_reason', nullable: true })
  @IsNotEmpty()
  @Field(() => String, { nullable: true })
  rejectionReason: string | null;

  @IsObject()
  @Column({ type: 'jsonb', default: {} })
  @Field(() => GraphQLJSONObject)
  metadata: Record<string, any>;

  @IsDate()
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @Column({
    name: 'status_updated_at',
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
  })
  @Field(() => Date)
  statusUpdatedAt: Date;

  @IsDate()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  private previousStatus?: RecommendationStatus;

  @BeforeUpdate()
  updateStatusUpdatedAtTimestamp() {
    const currentDate = new Date();
    this.updatedAt = currentDate;

    // If status has changed, update statusUpdatedAt
    if (
      this.previousStatus !== undefined &&
      this.previousStatus !== this.status
    ) {
      this.statusUpdatedAt = currentDate;
    }
  }

  @AfterLoad()
  rememberStatus() {
    // Store the status after loading from database
    this.previousStatus = this.status;
  }

  @Type(() => ScrapedPage)
  @Field(() => ScrapedPage, { nullable: true })
  scrapedPage?: ScrapedPage;

  @ApiHideProperty()
  @ManyToOne(() => Group, group => group.recommendations)
  @JoinColumn({ name: 'group_id' })
  group?: Group;

  @Type(() => Scrape)
  @ManyToOne(() => Scrape, scrape => scrape.recommendations, {
    eager: true,
  })
  @JoinColumn({ name: 'scrape_id' })
  scrape?: Scrape;
}
