import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Mutation,
  Parent,
  Query,
  ResolveField,
  registerEnumType,
  Resolver,
} from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { PaginateQuery } from 'nestjs-paginate';
import { AppPolicyRegistry } from 'src/auth.module';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { AuthContext } from 'src/graphql.decorator';
import { Group } from 'src/group/entities/group.entity';
import { GroupService } from 'src/group/group.service';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';

import { CreateRecommendationInput } from './dto/create-recommendation.input';
import {
  Recommendation,
  RecommendationStatus,
} from './entities/recommendation.entity';
import { RecommendationService } from './recommendation.service';

registerEnumType(RecommendationType, {
  name: 'RecommendationType',
});

registerEnumType(RecommendationStatus, {
  name: 'RecommendationStatus',
});

@Resolver(() => Recommendation)
export class RecommendationResolver {
  constructor(
    private readonly recommendationService: RecommendationService,
    private readonly groupService: GroupService,
  ) {}

  @Query(() => [Recommendation], {
    name: 'recommendations',
    description: 'Get all recommendations',
  })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
    @Args('groupId', { type: () => ID, nullable: true }) groupId?: string,
  ): Promise<Recommendation[]> {
    const canRead = await authContext.can('recommendation', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    const filter: Record<string, any> = {};
    if (companyId) {
      filter['scrape.scrapedPage.companyId'] = companyId;
    }
    if (groupId) {
      filter['groupId'] = groupId;
    }

    const paginateQuery: PaginateQuery = {
      path: '',
      limit: 100,
      page: 1,
      filter,
    };
    const result = await this.recommendationService.findAll(paginateQuery);
    return result.data;
  }

  @Query(() => Recommendation, {
    name: 'recommendation',
    description: 'Get a recommendation by ID',
  })
  async findOne(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Recommendation> {
    const recommendation = await this.recommendationService.findOne(id);
    const companyId = this.companyId(recommendation);
    const canRead = await authContext.can(
      'recommendation',
      'read',
      companyId || undefined,
    );

    if (!canRead) throw new ForbiddenException();

    return recommendation;
  }

  @ResolveField(() => GraphQLJSONObject, { nullable: true })
  metadata(@Parent() recommendation: Recommendation) {
    return recommendation.metadata;
  }

  @ResolveField(() => ScrapedPage, { nullable: true })
  scrapedPage(@Parent() recommendation: Recommendation): ScrapedPage | null {
    if (!recommendation.scrape) {
      return null;
    }
    return recommendation.scrape.scrapedPage || null;
  }

  @ResolveField(() => ID, { nullable: true })
  companyId(@Parent() recommendation: Recommendation): string | null {
    const scrapedPage = this.scrapedPage(recommendation);
    return scrapedPage?.companyId || null;
  }

  @ResolveField(() => Group, { nullable: true })
  async group(@Parent() recommendation: Recommendation): Promise<Group | null> {
    if (recommendation.group) return recommendation.group;
    if (!recommendation.groupId) return null;
    return await this.groupService.findOne(recommendation.groupId);
  }

  @Mutation(() => Recommendation, {
    name: 'createRecommendation',
    description: 'Create a new recommendation',
  })
  async createRecommendation(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('input') input: CreateRecommendationInput,
  ): Promise<Recommendation> {
    // For authorization, we need to get the companyId from the group
    const companyId =
      await this.recommendationService.getCompanyIdForAuthorization(
        undefined,
        input.groupId,
      );

    const canCreate = await authContext.can(
      'recommendation',
      'create',
      companyId,
    );

    if (!canCreate) throw new ForbiddenException();

    return this.recommendationService.create(input);
  }

  @Mutation(() => Recommendation, {
    name: 'applyRecommendation',
    description:
      'Allows systems and super users to apply a recommendation. Regular users are not allowed to apply recommendations.',
  })
  async applyRecommendation(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Recommendation> {
    const canApply = await authContext.can(
      'recommendation',
      'apply',
      undefined,
    );

    if (!canApply) throw new ForbiddenException();

    const recommendation = await this.recommendationService.update(id, {
      status: RecommendationStatus.APPLIED,
    });

    return recommendation;
  }
}
