import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class UpdateSeoPageGroupColumns1743455678947
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`
      ALTER TABLE seo_page DROP COLUMN scrape_status;

      ALTER TABLE "group" ADD COLUMN expired_at TIMESTAMPTZ;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`
      ALTER TABLE seo_page ADD COLUMN scrape_status VARCHAR NOT NULL DEFAULT 'pending';

      ALTER TABLE "group" DROP COLUMN expired_at;
    `);
  }
}
