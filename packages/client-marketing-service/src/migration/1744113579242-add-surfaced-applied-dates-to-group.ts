import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSurfacedAppliedDatesToGroup1744113579242
  implements MigrationInterface
{
  name = 'AddSurfacedAppliedDatesToGroup1744113579242';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First add the new columns
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );

    // Migrate data from published_at to surfaced_at
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."group" SET "surfaced_at" = "published_at"`,
    );

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "expired_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "scheduled_to_be_surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );
    // Migrate data from published_at to scheduled_to_be_surfaced_at
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."group" SET "scheduled_to_be_surfaced_at" = "published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "applied_at" TIMESTAMP WITH TIME ZONE`,
    );
    // Migrate data from published_at to applied_at
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."group" SET "applied_at" = "published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "scheduled_to_be_applied_at" TIMESTAMP WITH TIME ZONE`,
    );
    // Migrate data from published_at to scheduled_to_be_applied_at
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."group" SET "scheduled_to_be_applied_at" = "published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "deleted_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "name" SET NOT NULL`,
    );
    // Alter company_agent timestamps preserving data
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent"
       ALTER COLUMN "created_at" TYPE TIMESTAMP WITH TIME ZONE
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP WITH TIME ZONE
       USING updated_at AT TIME ZONE 'UTC'`,
    );

    // Alter company_neighborhood timestamps preserving data
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "name" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood"
       ALTER COLUMN "created_at" TYPE TIMESTAMP WITH TIME ZONE
       USING created_at AT TIME ZONE 'UTC'`,
    );
    // Update company_neighborhood timestamps
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP WITH TIME ZONE
       USING updated_at AT TIME ZONE 'UTC'`,
    );

    // Update company_seo_data columns
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data"
       ALTER COLUMN "created_at" TYPE TIMESTAMP WITH TIME ZONE
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP WITH TIME ZONE
       USING updated_at AT TIME ZONE 'UTC'`,
    );

    // Update company timestamps
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company"
       ALTER COLUMN "created_at" TYPE TIMESTAMP WITH TIME ZONE
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP WITH TIME ZONE
       USING updated_at AT TIME ZONE 'UTC'`,
    );

    // Update seo_page metadata
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "metadata" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "metadata" SET DEFAULT '{}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Revert seo_page metadata changes
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "metadata" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "metadata" DROP DEFAULT`,
    );

    // Revert company timestamps to regular timestamp
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP
       USING updated_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company"
       ALTER COLUMN "created_at" TYPE TIMESTAMP
       USING created_at AT TIME ZONE 'UTC'`,
    );

    // Revert company_seo_data changes
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP
       USING updated_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data"
       ALTER COLUMN "created_at" TYPE TIMESTAMP
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" DROP NOT NULL`,
    );

    // Revert company_neighborhood changes
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP
       USING updated_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood"
       ALTER COLUMN "created_at" TYPE TIMESTAMP
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "name" DROP NOT NULL`,
    );

    // Revert company_agent changes
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent"
       ALTER COLUMN "updated_at" TYPE TIMESTAMP
       USING updated_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent"
       ALTER COLUMN "created_at" TYPE TIMESTAMP
       USING created_at AT TIME ZONE 'UTC'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "name" DROP NOT NULL`,
    );

    // Revert group changes - migrate data back before dropping columns
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "published_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."group" SET "published_at" = "surfaced_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "expired_at" TIMESTAMP WITH TIME ZONE`,
    );

    // Only drop the new columns after data migration
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "deleted_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "scheduled_to_be_applied_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "applied_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "scheduled_to_be_surfaced_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "surfaced_at"`,
    );
  }
}
