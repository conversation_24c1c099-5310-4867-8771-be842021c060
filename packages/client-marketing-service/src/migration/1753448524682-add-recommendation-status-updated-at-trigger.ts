import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRecommendationStatusUpdatedAtTrigger1753448524682
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create function that will be called by the trigger
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_recommendation_status_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Only update status_updated_at if status column has changed
        IF OLD.status IS DISTINCT FROM NEW.status THEN
          NEW.status_updated_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create trigger that calls the function before an update on recommendation table
    await queryRunner.query(`
      CREATE TRIGGER recommendation_status_updated_at_trigger
      BEFORE UPDATE ON "crew_ai_seo_automation"."recommendation"
      FOR EACH ROW
      EXECUTE FUNCTION update_recommendation_status_updated_at();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the trigger
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS recommendation_status_updated_at_trigger ON "crew_ai_seo_automation"."recommendation";
    `);

    // Drop the function
    await queryRunner.query(`
      DROP FUNCTION IF EXISTS update_recommendation_status_updated_at();
    `);
  }
}
