import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePageIdFromRecommendation1750440550696
  implements MigrationInterface
{
  name = 'RemovePageIdFromRecommendation1750440550696';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_ee466abd4424ef88961326d16ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP COLUMN "seo_page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP COLUMN "page_id"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD "page_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD "seo_page_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_ee466abd4424ef88961326d16ae" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
