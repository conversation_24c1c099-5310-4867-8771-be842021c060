import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEnumFields1747315742313 implements MigrationInterface {
  name = 'AddEnumFields1747315742313';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."scheduled_action_workflow_type_enum" AS ENUM('SEO', 'BLOG')`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "workflow_type" "crew_ai_seo_automation"."scheduled_action_workflow_type_enum" NOT NULL`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."scheduled_action_status_enum" AS ENUM('DRAFT_PENDING', 'HUMAN_QA_PENDING', 'SURFACING_PENDING', 'SURFACED', 'PUBLISHED', 'FAILED', 'INVALIDATED')`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "status" "crew_ai_seo_automation"."scheduled_action_status_enum" NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "status"`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."scheduled_action_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "workflow_type"`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."scheduled_action_workflow_type_enum"`,
    );
  }
}
