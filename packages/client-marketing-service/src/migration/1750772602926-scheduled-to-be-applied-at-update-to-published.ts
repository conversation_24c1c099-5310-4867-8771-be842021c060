import { MigrationInterface, QueryRunner } from 'typeorm';

export class ScheduledToBeAppliedAtUpdateToPublished1750772602926
  implements MigrationInterface
{
  name = 'ScheduledToBeAppliedAtUpdateToPublished1750772602926';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" RENAME COLUMN "scheduled_to_be_applied_at" TO "scheduled_to_be_published_at"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" RENAME COLUMN "scheduled_to_be_published_at" TO "scheduled_to_be_applied_at"`,
    );
  }
}
