import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class PageKeywordHistoryTriggers1747673553390
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
        CREATE OR REPLACE FUNCTION track_keyword_insert() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        BEGIN
          INSERT INTO page_keyword_history (
            page_id,
            keyword_id,
            assigned_at,
            current_rank,
            change_note,
            updated_by
          )
          VALUES (
            NEW.page_id,
            NEW.keyword_id,
            NOW(),
            NEW.current_rank,
            current_setting('app.change_note', true),
            (SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID)
          );
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE OR REPLACE FUNCTION track_keyword_update() <PERSON><PERSON><PERSON><PERSON> trigger AS $$
        DECLARE
          note TEXT;
          editor UUID;
        BEGIN
          IF NEW.keyword_id IS DISTINCT FROM OLD.keyword_id THEN
            SELECT current_setting('app.change_note', true) INTO note;
            SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID INTO editor;

            -- Mark the previous record as removed
            UPDATE page_keyword_history
            SET removed_at = NOW()
            WHERE page_id = OLD.page_id
              AND keyword_id = OLD.keyword_id
              AND removed_at IS NULL;

            -- Insert new record with current rank and audit metadata
            INSERT INTO page_keyword_history (
              page_id,
              keyword_id,
              assigned_at,
              current_rank,
              change_note,
              updated_by
            )
            VALUES (
              NEW.page_id,
              NEW.keyword_id,
              NOW(), NEW.current_rank,
              note, editor
            );
          END IF;
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER trigger_track_keyword_insert
        AFTER INSERT ON page_keyword
        FOR EACH ROW
        EXECUTE FUNCTION track_keyword_insert();

        CREATE TRIGGER trigger_track_keyword_update
        AFTER UPDATE ON page_keyword
        FOR EACH ROW
        EXECUTE FUNCTION track_keyword_update();
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_track_keyword_insert ON page_keyword;
      DROP TRIGGER IF EXISTS trigger_track_keyword_update ON page_keyword;
      DROP FUNCTION IF EXISTS track_keyword_insert();
      DROP FUNCTION IF EXISTS track_keyword_update();
    `);
  }
}
