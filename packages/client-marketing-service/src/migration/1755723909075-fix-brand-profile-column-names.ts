import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixBrandProfileColumnNames1755723909075
  implements MigrationInterface
{
  name = 'FixBrandProfileColumnNames1755723909075';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_8175ffdf0a376594b41cf46a73"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "companyId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "aboutTheBrand"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "strategicFocus"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "valueProposition"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "idealCustomerProfiles"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "missionAndCoreValues"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "brandPointOfView"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "toneOfVoice"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "ctaText"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "authorPersona"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "company_id" uuid NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "about_the_brand" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "strategic_focus" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "value_proposition" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "ideal_customer_profiles" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "mission_and_core_values" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "brand_point_of_view" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "tone_of_voice" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "cta_text" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "author_persona" text`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_1d8f3d97d4fd100f48803ea132" ON "crew_ai_seo_automation"."brand_profile" ("company_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_1d8f3d97d4fd100f48803ea132"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "author_persona"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "cta_text"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "tone_of_voice"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "brand_point_of_view"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "mission_and_core_values"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "ideal_customer_profiles"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "value_proposition"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "strategic_focus"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "about_the_brand"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "company_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "authorPersona" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "ctaText" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "toneOfVoice" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "brandPointOfView" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "missionAndCoreValues" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "idealCustomerProfiles" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "valueProposition" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "strategicFocus" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "aboutTheBrand" text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "companyId" uuid NOT NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_8175ffdf0a376594b41cf46a73" ON "crew_ai_seo_automation"."brand_profile" ("companyId") `,
    );
  }
}
