import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenamePageToScrapedPage1750775533275
  implements MigrationInterface
{
  name = 'RenamePageToScrapedPage1750775533275';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_bf08d215963980edd9f4c66cafc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_368cec42a9ced3d442f55391740"`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."scraped_page_page_type_enum" AS ENUM('HOMEPAGE', 'HOME_VALUATION', 'MORTGAGE_CALCULATOR', 'BUYERS_GUIDE', 'SELLERS_GUIDE', 'NEIGHBORHOOD_GUIDE', 'AGENT_BIO', 'BLOG')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."scraped_page" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "company_id" uuid NOT NULL, "url" text NOT NULL, "page_name" text NOT NULL, "page_type" "crew_ai_seo_automation"."scraped_page_page_type_enum" NOT NULL DEFAULT 'HOMEPAGE', "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "UQ_b1226c5384a032f107675e8010e" UNIQUE ("url"), CONSTRAINT "PK_53ecc7708061378893f03ad4b78" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_30dbded3bab8863e6b3dd07dbf" ON "crew_ai_seo_automation"."scraped_page" ("company_id") `,
    );

    // Copy data from the old 'page' table to the new 'scraped_page' table
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."scraped_page" ("id", "company_id", "url", "page_name", "page_type", "metadata", "created_at", "updated_at", "deleted_at")
       SELECT "id", "company_id", "url", "page_name", "page_type"::text::"crew_ai_seo_automation"."scraped_page_page_type_enum", "metadata", "created_at", "updated_at", "deleted_at"
       FROM "crew_ai_seo_automation"."page"`,
    );

    // Drop the old 'page' table
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."page"`);
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."page_page_type_enum"`,
    );

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_bf08d215963980edd9f4c66cafc" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_368cec42a9ced3d442f55391740" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_368cec42a9ced3d442f55391740"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_bf08d215963980edd9f4c66cafc"`,
    );

    // Recreate the original 'page' table and enum
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."page_page_type_enum" AS ENUM('HOMEPAGE', 'HOME_VALUATION', 'MORTGAGE_CALCULATOR', 'BUYERS_GUIDE', 'SELLERS_GUIDE', 'NEIGHBORHOOD_GUIDE', 'AGENT_BIO', 'BLOG')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."page" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "company_id" uuid NOT NULL, "url" text NOT NULL, "page_name" text NOT NULL, "page_type" "crew_ai_seo_automation"."page_page_type_enum" NOT NULL DEFAULT 'HOMEPAGE', "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "UQ_b1226c5384a032f107675e8010e" UNIQUE ("url"), CONSTRAINT "PK_53ecc7708061378893f03ad4b78" PRIMARY KEY ("id"))`,
    );

    // Copy data back from 'scraped_page' to 'page' table
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."page" ("id", "company_id", "url", "page_name", "page_type", "metadata", "created_at", "updated_at", "deleted_at")
       SELECT "id", "company_id", "url", "page_name", "page_type"::text::"crew_ai_seo_automation"."page_page_type_enum", "metadata", "created_at", "updated_at", "deleted_at"
       FROM "crew_ai_seo_automation"."scraped_page"`,
    );

    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_30dbded3bab8863e6b3dd07dbf"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."scraped_page"`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."scraped_page_page_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_368cec42a9ced3d442f55391740" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_bf08d215963980edd9f4c66cafc" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
