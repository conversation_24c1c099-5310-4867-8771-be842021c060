import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCampaignMetadataColumn1757800000000
  implements MigrationInterface
{
  name = 'AddCampaignMetadataColumn1757800000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign" ADD "metadata" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign" DROP COLUMN "metadata"`,
    );
  }
}
