import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldsToScheduledAction1749591869499
  implements MigrationInterface
{
  name = 'AddFieldsToScheduledAction1749591869499';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" 
        ADD COLUMN "locked_at" TIMESTAMP WITH TIME ZONE,
        ADD COLUMN "execution_name" TEXT,
        ADD COLUMN "execution_arn" TEXT`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" 
        DROP COLUMN "locked_at",
        DROP COLUMN "execution_name",
        DROP COLUMN "execution_arn"`,
    );
  }
}
