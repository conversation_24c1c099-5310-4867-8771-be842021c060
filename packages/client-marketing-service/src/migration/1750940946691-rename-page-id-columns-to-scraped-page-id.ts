import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenamePageIdColumnsToScrapedPageId1750940946691
  implements MigrationInterface
{
  name = 'RenamePageIdColumnsToScrapedPageId1750940946691';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_bf08d215963980edd9f4c66cafc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_368cec42a9ced3d442f55391740"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_bf08d215963980edd9f4c66caf"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_368cec42a9ced3d442f5539174"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_fa173a1c5de48c5e2fca0322ba"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_98ccdb8d054fa275593c9aa07e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" RENAME COLUMN "page_id" TO "scraped_page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" RENAME COLUMN "page_id" TO "scraped_page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" RENAME COLUMN "page_id" TO "scraped_page_id"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6ee19559502a7e0962cdce395f" ON "crew_ai_seo_automation"."page_keyword_history" ("scraped_page_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_012c9489e0b4bffbedd5ee3345" ON "crew_ai_seo_automation"."page_keyword" ("scraped_page_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_f53050fc759a74f33263150c13" ON "crew_ai_seo_automation"."page_keyword" ("scraped_page_id", "keyword_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ea4eb152704b2bf05b79a68658" ON "crew_ai_seo_automation"."scrape" ("scraped_page_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_6ee19559502a7e0962cdce395f9" FOREIGN KEY ("scraped_page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_012c9489e0b4bffbedd5ee33451" FOREIGN KEY ("scraped_page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_ea4eb152704b2bf05b79a686582" FOREIGN KEY ("scraped_page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_ea4eb152704b2bf05b79a686582"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_012c9489e0b4bffbedd5ee33451"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_6ee19559502a7e0962cdce395f9"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_ea4eb152704b2bf05b79a68658"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_f53050fc759a74f33263150c13"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_012c9489e0b4bffbedd5ee3345"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_6ee19559502a7e0962cdce395f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" RENAME COLUMN "scraped_page_id" TO "page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" RENAME COLUMN "scraped_page_id" TO "page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" RENAME COLUMN "scraped_page_id" TO "page_id"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_98ccdb8d054fa275593c9aa07e" ON "crew_ai_seo_automation"."scrape" ("page_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_fa173a1c5de48c5e2fca0322ba" ON "crew_ai_seo_automation"."page_keyword" ("page_id", "keyword_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_368cec42a9ced3d442f5539174" ON "crew_ai_seo_automation"."page_keyword" ("page_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bf08d215963980edd9f4c66caf" ON "crew_ai_seo_automation"."page_keyword_history" ("page_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_368cec42a9ced3d442f55391740" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_bf08d215963980edd9f4c66cafc" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
