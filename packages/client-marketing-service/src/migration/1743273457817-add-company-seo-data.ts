import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddCompanySeoData1743273457817 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Create company_seo_data table
    await queryRunner.query(
      `CREATE TABLE company_seo_data (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        company_id UUID NOT NULL REFERENCES company(id) ON DELETE CASCADE,
        location TEXT NOT NULL,
        is_team BOOLEAN NOT NULL DEFAULT false,
        name TEXT NOT NULL,
        business_name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
    );

    // Create agents table
    await queryRunner.query(
      `CREATE TABLE company_agent (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        company_id UUID NOT NULL REFERENCES company(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(company_id, name)
      )`,
    );

    // Create neighborhoods table
    await queryRunner.query(
      `CREATE TABLE company_neighborhood (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        company_id UUID NOT NULL REFERENCES company(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(company_id, name)
      )`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`DROP TABLE company_neighborhood`);
    await queryRunner.query(`DROP TABLE company_agent`);
    await queryRunner.query(`DROP TABLE company_seo_data`);
  }
}
