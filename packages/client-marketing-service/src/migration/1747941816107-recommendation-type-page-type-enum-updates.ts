import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class RecommendationTypePageTypeEnumUpdates1747941816107
  implements MigrationInterface
{
  name = 'RecommendationTypePageTypeEnumUpdates1747941816107';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Update recommendation type values to match the uppercase format in the RecommendationType enum
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'META_TITLE' WHERE "type" = 'meta_title'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'META_DESCRIPTION' WHERE "type" = 'meta_description'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'MAIN_HEADING' WHERE "type" = 'main_heading'`,
    );

    // Update pagetype values in seo_page table to match the uppercase format in the PageType enum
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'HOMEPAGE' WHERE "page_type" = 'homepage'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'HOME_VALUATION' WHERE "page_type" = 'homeValuation'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'MORTGAGE_CALCULATOR' WHERE "page_type" = 'mortgageCalculator'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'BUYERS_GUIDE' WHERE "page_type" = 'buyersGuide'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'SELLERS_GUIDE' WHERE "page_type" = 'sellersGuide'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'NEIGHBORHOOD_GUIDE' WHERE "page_type" = 'neighborhoodGuide'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'AGENT_BIO' WHERE "page_type" = 'agentBio'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'BLOG' WHERE "page_type" = 'blog'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Revert recommendation type values back to camelCase
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'meta_title' WHERE "type" = 'META_TITLE'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'meta_description' WHERE "type" = 'META_DESCRIPTION'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "type" = 'main_heading' WHERE "type" = 'MAIN_HEADING'`,
    );

    // Revert pagetype values in seo_page table back to lowercase
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'homepage' WHERE "page_type" = 'HOMEPAGE'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'homeValuation' WHERE "page_type" = 'HOME_VALUATION'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'mortgageCalculator' WHERE "page_type" = 'MORTGAGE_CALCULATOR'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'buyersGuide' WHERE "page_type" = 'BUYERS_GUIDE'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'sellersGuide' WHERE "page_type" = 'SELLERS_GUIDE'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'neighborhoodGuide' WHERE "page_type" = 'NEIGHBORHOOD_GUIDE'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'agentBio' WHERE "page_type" = 'AGENT_BIO'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."seo_page" SET "page_type" = 'blog' WHERE "page_type" = 'BLOG'`,
    );
  }
}
