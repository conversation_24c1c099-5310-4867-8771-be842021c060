import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTitleToRecommendation1742222298092
  implements MigrationInterface
{
  name = 'AddTitleToRecommendation1742222298092';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `ALTER TABLE recommendation ADD COLUMN title VARCHAR NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`ALTER TABLE recommendation DROP COLUMN title`);
  }
}
