import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddJ<PERSON>bFields1747313981088 implements MigrationInterface {
  name = 'AddJsonbFields1747313981088';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "content_payload" jsonb NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "generation_payload" jsonb NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "failure_reason" jsonb`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "failure_reason"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "generation_payload"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "content_payload"`,
    );
  }
}
