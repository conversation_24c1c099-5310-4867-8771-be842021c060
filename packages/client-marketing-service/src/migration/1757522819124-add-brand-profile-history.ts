import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBrandProfileHistory1757522819124 implements MigrationInterface {
  name = 'AddBrandProfileHistory1757522819124';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."brand_profile_history" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "company_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL, "updated_by" text, "about_the_brand" text, "strategic_focus" text, "value_proposition" text, "ideal_customer_profiles" text, "mission_and_core_values" text, "brand_point_of_view" text, "tone_of_voice" text, "cta_text" text, "author_persona" text, CONSTRAINT "PK_a8340f61bfe7c82e86d8a29f258" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6049c5f6c97a86f7a60be2dfa3" ON "crew_ai_seo_automation"."brand_profile_history" ("company_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_6049c5f6c97a86f7a60be2dfa3"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."brand_profile_history"`,
    );
  }
}
