import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialCampaignPayment1756201000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // At this point, the previous migration 1756149435780 has already
    // created the campaign and payment tables and their enums. Here we
    // adapt the existing schema instead of recreating tables/types.

    // 1) Drop obsolete join table and enum from previous design, if present
    await queryRunner.query(`DROP TABLE IF EXISTS "campaign_payment"`);
    await queryRunner.query(
      `DO $$ BEGIN
         IF EXISTS (
           SELECT 1 FROM pg_type t
           JOIN pg_namespace n ON n.oid = t.typnamespace
           WHERE t.typname = 'campaign_payment_role_enum'
         ) THEN
           EXECUTE 'DROP TYPE "campaign_payment_role_enum"';
         END IF;
       END $$;`,
    );

    // 2) Extend existing payment table with new columns
    await queryRunner.query(
      `ALTER TABLE "payment"
       ADD COLUMN IF NOT EXISTS "attempt_number" integer NOT NULL DEFAULT 1`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment"
       ADD COLUMN IF NOT EXISTS "parent_payment_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment"
       ADD COLUMN IF NOT EXISTS "campaign_id" uuid`,
    );

    // 3) Add indexes/constraints aligned with the entities
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_payment_campaign_id" ON "payment" ("campaign_id")`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "uq_payment_provider_ref"
       ON "payment" ("provider", "provider_ref")
       WHERE "provider_ref" IS NOT NULL`,
    );

    await queryRunner.query(
      `DO $$
       BEGIN
         IF NOT EXISTS (
           SELECT 1 FROM pg_constraint WHERE conname = 'FK_payment_parent_payment_id'
         ) THEN
           ALTER TABLE "payment"
             ADD CONSTRAINT "FK_payment_parent_payment_id"
             FOREIGN KEY ("parent_payment_id")
             REFERENCES "payment"("id")
             ON DELETE CASCADE;
         END IF;
       END$$;`,
    );

    await queryRunner.query(
      `DO $$
       BEGIN
         IF NOT EXISTS (
           SELECT 1 FROM pg_constraint WHERE conname = 'FK_payment_campaign_id'
         ) THEN
           ALTER TABLE "payment"
             ADD CONSTRAINT "FK_payment_campaign_id"
             FOREIGN KEY ("campaign_id")
             REFERENCES "campaign"("id")
             ON DELETE SET NULL;
         END IF;
       END$$;`,
    );

    await queryRunner.query(
      `DO $$
       BEGIN
         IF NOT EXISTS (
           SELECT 1 FROM pg_constraint WHERE conname = 'chk_payment_attempt_number_positive'
         ) THEN
           ALTER TABLE "payment"
             ADD CONSTRAINT "chk_payment_attempt_number_positive"
             CHECK ("attempt_number" >= 1) NOT VALID;
         END IF;
       END$$;`,
    );

    await queryRunner.query(
      `DO $$
       BEGIN
         IF NOT EXISTS (
           SELECT 1 FROM pg_constraint WHERE conname = 'chk_payment_parent_not_self'
         ) THEN
           ALTER TABLE "payment"
             ADD CONSTRAINT "chk_payment_parent_not_self"
             CHECK ("parent_payment_id" IS NULL OR "parent_payment_id" <> "id") NOT VALID;
         END IF;
       END$$;`,
    );

    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "idx_payment_parent_attempt_latest"
       ON "payment" ("parent_payment_id", "attempt_number" DESC)
       WHERE "parent_payment_id" IS NOT NULL`,
    );

    await queryRunner.query(
      `CREATE UNIQUE INDEX IF NOT EXISTS "uq_payment_parent_attempt"
       ON "payment" ("parent_payment_id", "attempt_number")
       WHERE "parent_payment_id" IS NOT NULL`,
    );

    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "idx_payment_campaign_attempt_created"
       ON "payment" ("campaign_id", "attempt_number", "created_at")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Reverse operations from up(): remove added constraints/indexes/columns
    await queryRunner.query(`DROP INDEX IF EXISTS "uq_payment_parent_attempt"`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_payment_parent_attempt_latest"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "chk_payment_parent_not_self" CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "chk_payment_attempt_number_positive" CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "FK_payment_campaign_id" CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "FK_payment_parent_payment_id" CASCADE`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_payment_campaign_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "uq_payment_provider_ref"`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS "idx_payment_campaign_attempt_created"`,
    );

    await queryRunner.query(
      `ALTER TABLE "payment" DROP COLUMN IF EXISTS "campaign_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP COLUMN IF EXISTS "parent_payment_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment" DROP COLUMN IF EXISTS "attempt_number"`,
    );

    // Recreate the old join table and enum from the previous design
    await queryRunner.query(
      `DO $$ BEGIN
         IF NOT EXISTS (
           SELECT 1 FROM pg_type t
           JOIN pg_namespace n ON n.oid = t.typnamespace
           WHERE t.typname = 'campaign_payment_role_enum'
         ) THEN
           EXECUTE 'CREATE TYPE "campaign_payment_role_enum" AS ENUM (''primary'', ''retry'', ''refund'')';
         END IF;
       END $$;`,
    );

    await queryRunner.query(
      `CREATE TABLE IF NOT EXISTS "campaign_payment" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "campaign_id" uuid NOT NULL,
        "payment_id" uuid NOT NULL,
        "role" "campaign_payment_role_enum" NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        CONSTRAINT "PK_campaign_payment_id" PRIMARY KEY ("id")
      )`,
    );

    await queryRunner.query(
      `ALTER TABLE "campaign_payment" ADD CONSTRAINT IF NOT EXISTS "FK_campaign_payment_campaign_id" FOREIGN KEY ("campaign_id") REFERENCES "campaign"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "campaign_payment" ADD CONSTRAINT IF NOT EXISTS "FK_campaign_payment_payment_id" FOREIGN KEY ("payment_id") REFERENCES "payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
