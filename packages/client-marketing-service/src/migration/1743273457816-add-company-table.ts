import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddCompanyTable1743273457816 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `CREATE TABLE company (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            domain TEXT NOT NULL,
            metadata JSONB NOT NULL DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      )`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`DROP TABLE company`);
  }
}
