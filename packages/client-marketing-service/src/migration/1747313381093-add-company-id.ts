import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCompanyId1747313381093 implements MigrationInterface {
  name = 'AddCompanyId1747313381093';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "company_id" uuid NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "company_id"`,
    );
  }
}
