import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddGroupScheduledActionTable1750168737382
  implements MigrationInterface
{
  name = 'AddGroupScheduledActionTable1750168737382';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."group_scheduled_action" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "group_id" uuid NOT NULL, "scheduled_action_id" uuid NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_f46feae3cff54b132c80784128f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a2db7b512b3ea76dabc4631599" ON "crew_ai_seo_automation"."group_scheduled_action" ("scheduled_action_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_df1771792b2394d89525209721" ON "crew_ai_seo_automation"."group_scheduled_action" ("group_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_53b92203b1c658b5e15da075ed" ON "crew_ai_seo_automation"."group_scheduled_action" ("group_id", "scheduled_action_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group_scheduled_action" ADD CONSTRAINT "FK_df1771792b2394d895252097215" FOREIGN KEY ("group_id") REFERENCES "crew_ai_seo_automation"."group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group_scheduled_action" ADD CONSTRAINT "FK_a2db7b512b3ea76dabc46315995" FOREIGN KEY ("scheduled_action_id") REFERENCES "crew_ai_seo_automation"."scheduled_action"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group_scheduled_action" DROP CONSTRAINT "FK_a2db7b512b3ea76dabc46315995"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group_scheduled_action" DROP CONSTRAINT "FK_df1771792b2394d895252097215"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_53b92203b1c658b5e15da075ed"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_df1771792b2394d89525209721"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_a2db7b512b3ea76dabc4631599"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."group_scheduled_action"`,
    );
  }
}
