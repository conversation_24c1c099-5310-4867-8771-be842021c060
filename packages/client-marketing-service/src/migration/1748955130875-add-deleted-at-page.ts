import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDeletedAtPage1748955130875 implements MigrationInterface {
  name = 'AddDeletedAtPage1748955130875';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ADD "deleted_at" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" DROP COLUMN "deleted_at"`,
    );
  }
}
