import { MigrationInterface, QueryRunner } from 'typeorm';

export class MoreDbSyncs1744215789890 implements MigrationInterface {
  name = 'MoreDbSyncs1744215789890';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ADD CONSTRAINT "UQ_012465b03194abeedaf6967caa6" UNIQUE ("domain")`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_markdown_scraped_json_id" ON "crew_ai_seo_automation"."markdown" ("scraped_json_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "idx_scraped_json_seo_page_id" ON "crew_ai_seo_automation"."scraped_json" ("seo_page_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_09736b17c38745cd60a23b339f" ON "crew_ai_seo_automation"."seo_page" ("company_id") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_09736b17c38745cd60a23b339f"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."idx_scraped_json_seo_page_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."idx_markdown_scraped_json_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" DROP CONSTRAINT "UQ_012465b03194abeedaf6967caa6"`,
    );
  }
}
