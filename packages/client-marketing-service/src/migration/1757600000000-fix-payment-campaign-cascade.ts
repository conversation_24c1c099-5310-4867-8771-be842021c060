import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixPaymentCampaignCascade1757600000000
  implements MigrationInterface
{
  name = 'FixPaymentCampaignCascade1757600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Drop the existing foreign key constraint with ON DELETE SET NULL
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "FK_payment_campaign_id"`,
    );

    // Add the new foreign key constraint with ON DELETE CASCADE
    // to match the entity definition
    await queryRunner.query(
      `ALTER TABLE "payment" 
       ADD CONSTRAINT "FK_payment_campaign_id" 
       FOREIGN KEY ("campaign_id") 
       REFERENCES "campaign"("id") 
       ON DELETE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Drop the CASCADE constraint
    await queryRunner.query(
      `ALTER TABLE "payment" DROP CONSTRAINT IF EXISTS "FK_payment_campaign_id"`,
    );

    // Restore the original SET NULL constraint
    await queryRunner.query(
      `ALTER TABLE "payment" 
       ADD CONSTRAINT "FK_payment_campaign_id" 
       FOREIGN KEY ("campaign_id") 
       REFERENCES "campaign"("id") 
       ON DELETE SET NULL`,
    );
  }
}
