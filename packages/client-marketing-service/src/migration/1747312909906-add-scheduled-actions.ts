import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddScheduledActions1747312909906 implements MigrationInterface {
  name = 'AddScheduledActions1747312909906';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."scheduled_action" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), CONSTRAINT "PK_710ff448187d3d81ad6ee6a5806" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."scheduled_action"`,
    );
  }
}
