import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBrandProfile1755612462033 implements MigrationInterface {
  name = 'AddBrandProfile1755612462033';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."brand_profile" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "companyId" uuid NOT NULL, "aboutTheBrand" text, "strategicFocus" text, "valueProposition" text, "idealCustomerProfiles" text, "missionAndCoreValues" text, "brandPointOfView" text, "toneOfVoice" text, "ctaText" text, "authorPersona" text, CONSTRAINT "PK_b293356a3488bdac7b90ed5e75c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_8175ffdf0a376594b41cf46a73" ON "crew_ai_seo_automation"."brand_profile" ("companyId") `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_8175ffdf0a376594b41cf46a73"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."brand_profile"`,
    );
  }
}
