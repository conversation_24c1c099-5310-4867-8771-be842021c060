import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1708876900000 implements MigrationInterface {
  name = 'InitialSchema1708876900000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`CREATE SCHEMA IF NOT EXISTS "extensions"`);
    await queryRunner.query(
      `CREATE EXTENSION IF NOT EXISTS "uuid-ossp" SCHEMA "extensions"`,
    );

    await queryRunner.query(`
            CREATE TABLE page (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                company_id UUID NOT NULL,
                domain VARCHAR NOT NULL,
                path VARCHAR NOT NULL
            )
        `);

    await queryRunner.query(`
            CREATE INDEX idx_page_company_id ON page(company_id)
        `);

    await queryRunner.query(`
            CREATE TYPE recommendation_status AS ENUM ('suggested', 'implemented', 'rejected')
        `);

    await queryRunner.query(`
            CREATE TABLE recommendation (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                page_id UUID NOT NULL REFERENCES page(id) ON DELETE CASCADE,
                topic VARCHAR NOT NULL,
                current TEXT NOT NULL,
                recommendation TEXT NOT NULL,
                reasoning TEXT NOT NULL,
                status recommendation_status NOT NULL DEFAULT 'suggested',
                created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
            )
        `);

    await queryRunner.query(`
            CREATE INDEX idx_recommendation_page_id ON recommendation(page_id)
        `);

    await queryRunner.query(`
            CREATE INDEX idx_recommendation_status ON recommendation(status)
        `);

    await queryRunner.query(`
            CREATE INDEX idx_recommendation_topic ON recommendation(topic)
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Drop indexes first
    await queryRunner.query(`DROP INDEX IF EXISTS idx_page_company_id`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_recommendation_page_id`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS public.idx_recommendation_status`,
    );
    await queryRunner.query(`DROP INDEX IF EXISTS idx_recommendation_topic`);

    // Drop tables
    await queryRunner.query(`DROP TABLE IF EXISTS recommendation`);
    await queryRunner.query(`DROP TABLE IF EXISTS page`);

    // Drop enum type
    await queryRunner.query(`DROP TYPE IF EXISTS recommendation_status`);
  }
}
