import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTablesKeywordPageScrape1747672970372
  implements MigrationInterface
{
  name = 'AddTablesKeywordPageScrape1747672970372';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."page_keyword_history" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "page_id" uuid NOT NULL, "keyword_id" uuid NOT NULL, "change_note" text, "updated_by" uuid, "assigned_at" TIMESTAMP WITH TIME ZONE, "removed_at" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_73d3c53ba75a35e5894f8f2913d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bf08d215963980edd9f4c66caf" ON "crew_ai_seo_automation"."page_keyword_history" ("page_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."scrape" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "company_id" uuid NOT NULL, "page_id" uuid NOT NULL, "raw_html" text NOT NULL, "markdown" text NOT NULL, "current_scraped_values" jsonb NOT NULL DEFAULT '{}', "scraped_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_1428a194a4207c1631898dd0d80" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_82cc9c8bd633e1e668ae8ba907" ON "crew_ai_seo_automation"."scrape" ("company_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_98ccdb8d054fa275593c9aa07e" ON "crew_ai_seo_automation"."scrape" ("page_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."page_page_type_enum" AS ENUM('HOMEPAGE', 'HOME-VALUATION', 'MORTGAGE-CALCULATOR', 'BUYERS-GUIDE', 'SELLERS-GUIDE', 'NEIGHBORHOOD-GUIDE', 'AGENT-BIO', 'BLOG')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."page" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "company_id" uuid NOT NULL, "url" text NOT NULL, "page_name" text NOT NULL, "page_type" "crew_ai_seo_automation"."page_page_type_enum" NOT NULL DEFAULT 'HOMEPAGE', "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "UQ_f4fdc8c67d3e22af7f60e07540f" UNIQUE ("url"), CONSTRAINT "PK_742f4117e065c5b6ad21b37ba1f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ee221827b225f949f9df5babf" ON "crew_ai_seo_automation"."page" ("company_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."page_keyword" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "page_id" uuid NOT NULL, "keyword_id" uuid NOT NULL, "original_rank" integer, "current_rank" integer, "rank_checked_at" TIMESTAMP WITH TIME ZONE, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_6dc19679e90a32b939ad45dba4e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_368cec42a9ced3d442f5539174" ON "crew_ai_seo_automation"."page_keyword" ("page_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_04aa0db1d6a450f25fbe0e818a" ON "crew_ai_seo_automation"."page_keyword" ("keyword_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_fa173a1c5de48c5e2fca0322ba" ON "crew_ai_seo_automation"."page_keyword" ("page_id", "keyword_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."keyword" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "keyword" text NOT NULL, "metadata" jsonb NOT NULL DEFAULT '{}', "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "UQ_35e3ff88225eef1d85c951e2295" UNIQUE ("keyword"), CONSTRAINT "PK_affdb8c8fa5b442900cb3aa21dc" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD "page_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD "scrape_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "keyword_id" uuid`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_bf08d215963980edd9f4c66cafc" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" ADD CONSTRAINT "FK_ca73f0b3d695f1d967b6190b147" FOREIGN KEY ("keyword_id") REFERENCES "crew_ai_seo_automation"."keyword"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_82cc9c8bd633e1e668ae8ba9070" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_ee466abd4424ef88961326d16ae" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_b1b335e09a9929232fa5f2d7282" FOREIGN KEY ("scrape_id") REFERENCES "crew_ai_seo_automation"."scrape"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ADD CONSTRAINT "FK_5ee221827b225f949f9df5babf1" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_04aa0db1d6a450f25fbe0e818aa" FOREIGN KEY ("keyword_id") REFERENCES "crew_ai_seo_automation"."keyword"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" ADD CONSTRAINT "FK_368cec42a9ced3d442f55391740" FOREIGN KEY ("page_id") REFERENCES "crew_ai_seo_automation"."page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD CONSTRAINT "FK_5c0ffba77658962a3eaf39de200" FOREIGN KEY ("keyword_id") REFERENCES "crew_ai_seo_automation"."keyword"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP CONSTRAINT "FK_5c0ffba77658962a3eaf39de200"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_368cec42a9ced3d442f55391740"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword" DROP CONSTRAINT "FK_04aa0db1d6a450f25fbe0e818aa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" DROP CONSTRAINT "FK_5ee221827b225f949f9df5babf1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_b1b335e09a9929232fa5f2d7282"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_ee466abd4424ef88961326d16ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_82cc9c8bd633e1e668ae8ba9070"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_98ccdb8d054fa275593c9aa07e5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_ca73f0b3d695f1d967b6190b147"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page_keyword_history" DROP CONSTRAINT "FK_bf08d215963980edd9f4c66cafc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "keyword_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP COLUMN "scrape_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP COLUMN "page_id"`,
    );
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."keyword"`);
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_fa173a1c5de48c5e2fca0322ba"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_04aa0db1d6a450f25fbe0e818a"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_368cec42a9ced3d442f5539174"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."page_keyword"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_5ee221827b225f949f9df5babf"`,
    );
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."page"`);
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."page_page_type_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_98ccdb8d054fa275593c9aa07e"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_82cc9c8bd633e1e668ae8ba907"`,
    );
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."scrape"`);
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_bf08d215963980edd9f4c66caf"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."page_keyword_history"`,
    );
  }
}
