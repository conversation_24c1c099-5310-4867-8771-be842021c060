import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateOriginalRankTrigger1748020317290
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Set schema explicitly to crew_ai_seo_automation
    await queryRunner.query(`SET search_path TO crew_ai_seo_automation;`);

    // Drop existing trigger
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_set_original_rank ON page_keyword;
    `);

    // Recreate the function with explicit schema references
    await queryRunner.query(`
      -- Function to set originalRank to currentRank if originalRank is null
      CREATE OR REPLACE FUNCTION crew_ai_seo_automation.set_original_rank() RETURNS trigger AS $$
      BEGIN
        -- Only set originalRank if it's null and currentRank is not null
        IF NEW.original_rank IS NULL AND NEW.current_rank IS NOT NULL THEN
          NEW.original_rank := NEW.current_rank;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Trigger that runs before insert or update on page_keyword with explicit schema
      CREATE TRIGGER trigger_set_original_rank
      BEFORE INSERT OR UPDATE ON crew_ai_seo_automation.page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION crew_ai_seo_automation.set_original_rank();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Set schema explicitly to crew_ai_seo_automation
    await queryRunner.query(`SET search_path TO crew_ai_seo_automation;`);

    // We don't want to drop the trigger in the down migration since we're just updating it
    // Instead, we'll restore the original behavior without explicit schema references
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_set_original_rank ON page_keyword;
      DROP FUNCTION IF EXISTS set_original_rank();
    `);
  }
}
