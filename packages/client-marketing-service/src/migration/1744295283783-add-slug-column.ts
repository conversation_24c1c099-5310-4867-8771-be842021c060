import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSlugColumn1744295283783 implements MigrationInterface {
  name = 'AddSlugColumn1744295283783';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ADD "slug" character varying NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ADD "slug" character varying NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" DROP COLUMN "slug"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" DROP COLUMN "slug"`,
    );
  }
}
