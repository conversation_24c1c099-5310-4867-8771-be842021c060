import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBlogTopicTable1755805505992 implements MigrationInterface {
  name = 'CreateBlogTopicTable1755805505992';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."blog_topic" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(), 
        "company_id" uuid NOT NULL, 
        "topic" text NOT NULL, 
        "blog_post_job_id" uuid, 
        "airops_execution_id" text, 
        "cms_post_id" uuid, 
        "neighborhood_id" uuid, 
        "parent_topic" text, 
        "blog_title" text, 
        "rationale" text, 
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), 
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), 
        CONSTRAINT "PK_blog_topic_id" PRIMARY KEY ("id")
      )`,
    );

    // Create index on company_id for performance (foreign key, high cardinality)
    await queryRunner.query(
      `CREATE INDEX "IDX_blog_topic_company_id" ON "crew_ai_seo_automation"."blog_topic" ("company_id")`,
    );

    // Add foreign key constraint for company_id
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" ADD CONSTRAINT "FK_blog_topic_company_id" FOREIGN KEY ("company_id") REFERENCES "identity"."company"("display_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    // Add unique constraint on (company_id, topic) to prevent duplicates
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" ADD CONSTRAINT "uq_blog_topic_company_topic" UNIQUE ("company_id", "topic")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP CONSTRAINT "FK_blog_topic_company_id"`,
    );

    // Drop the unique constraint
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP CONSTRAINT "uq_blog_topic_company_topic"`,
    );

    // Drop index
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_blog_topic_company_id"`,
    );

    // Drop table
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."blog_topic"`);
  }
}
