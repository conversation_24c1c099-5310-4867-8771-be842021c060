import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddListingPromosCampaignPaymentTables1756149435780
  implements MigrationInterface
{
  name = 'AddListingPromosCampaignPaymentTables1756149435780';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop constraints and indexes if they exist
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP CONSTRAINT IF EXISTS "FK_blog_topic_company_id"`,
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS "crew_ai_seo_automation"."IDX_blog_topic_company_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP CONSTRAINT IF EXISTS "uq_blog_topic_company_topic"`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."campaign_status_enum" AS ENUM('PENDING', 'SUCCESS', 'ERROR', 'CANCELED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."campaign" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "property_id" character varying(255) NOT NULL, "price_cents" integer NOT NULL, "currency" character varying(3) NOT NULL DEFAULT 'USD', "status" "crew_ai_seo_automation"."campaign_status_enum" NOT NULL, "bundle_uri" text, "sqs_message_id" text, "retry_count" integer NOT NULL DEFAULT '0', "created_by" character varying(255) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_0ce34d26e7f2eb316a3a592cdc4" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d6e503ba33b4a480cf97d63073" ON "crew_ai_seo_automation"."campaign" ("property_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."campaign_payment_role_enum" AS ENUM('primary', 'retry', 'refund')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."campaign_payment" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "campaign_id" uuid NOT NULL, "payment_id" uuid NOT NULL, "role" "crew_ai_seo_automation"."campaign_payment_role_enum" NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_4ac39423f6d344de5854e89f2e7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."payment_provider_enum" AS ENUM('test')`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."payment_status_enum" AS ENUM('PENDING', 'SUCCEEDED', 'FAILED', 'REFUNDED')`,
    );
    await queryRunner.query(
      `CREATE TABLE "crew_ai_seo_automation"."payment" ("id" uuid NOT NULL DEFAULT gen_random_uuid(), "provider" "crew_ai_seo_automation"."payment_provider_enum" NOT NULL, "provider_ref" text, "status" "crew_ai_seo_automation"."payment_status_enum" NOT NULL, "amount_cents" integer NOT NULL, "currency" character varying(3) NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_fcaec7df5adf9cac408c686b2ab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1bd9388cbde71164133c06a46a" ON "crew_ai_seo_automation"."blog_topic" ("company_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" ADD CONSTRAINT "UQ_a978d0c1fe5797d2dc5b2dc8fb5" UNIQUE ("company_id", "topic")`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign_payment" ADD CONSTRAINT "FK_93c2d576829972d565c6bdccb8c" FOREIGN KEY ("campaign_id") REFERENCES "crew_ai_seo_automation"."campaign"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign_payment" ADD CONSTRAINT "FK_deaf4ec09d462cb073565832efa" FOREIGN KEY ("payment_id") REFERENCES "crew_ai_seo_automation"."payment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign_payment" DROP CONSTRAINT "FK_deaf4ec09d462cb073565832efa"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."campaign_payment" DROP CONSTRAINT "FK_93c2d576829972d565c6bdccb8c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP CONSTRAINT "UQ_a978d0c1fe5797d2dc5b2dc8fb5"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_1bd9388cbde71164133c06a46a"`,
    );
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."payment"`);
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."payment_status_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."payment_provider_enum"`,
    );
    await queryRunner.query(
      `DROP TABLE "crew_ai_seo_automation"."campaign_payment"`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."campaign_payment_role_enum"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."IDX_d6e503ba33b4a480cf97d63073"`,
    );
    await queryRunner.query(`DROP TABLE "crew_ai_seo_automation"."campaign"`);
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."campaign_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" ADD CONSTRAINT "uq_blog_topic_company_topic" UNIQUE ("company_id", "topic")`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_blog_topic_company_id" ON "crew_ai_seo_automation"."blog_topic" ("company_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" ADD CONSTRAINT "FK_blog_topic_company_id" FOREIGN KEY ("company_id") REFERENCES "identity"."company"("display_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
