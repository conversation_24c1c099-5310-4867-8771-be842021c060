import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class ChangeDefaultRecommendationStatus1742579774229
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `ALTER TABLE recommendation ALTER COLUMN status SET DEFAULT 'pending'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `ALTER TABLE recommendation ALTER COLUMN status SET DEFAULT 'suggested'`,
    );
  }
}
