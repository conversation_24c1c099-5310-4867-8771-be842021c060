import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropNotNullSeopageidOnRecommendation1748019109322
  implements MigrationInterface
{
  name = 'DropNotNullSeopageidOnRecommendation1748019109322';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "seo_page_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "seo_page_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
