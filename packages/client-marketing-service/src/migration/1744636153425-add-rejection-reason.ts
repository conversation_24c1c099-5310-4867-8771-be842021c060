import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRejectionReason1744636153425 implements MigrationInterface {
  name = 'AddRejectionReason1744636153425';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD "rejection_reason" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP COLUMN "rejection_reason"`,
    );
  }
}
