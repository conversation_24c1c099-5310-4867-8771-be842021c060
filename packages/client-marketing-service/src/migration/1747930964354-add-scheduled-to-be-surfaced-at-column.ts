import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddScheduledToBeSurfacedAtColumn1747930964354
  implements MigrationInterface
{
  name = 'AddScheduledToBeSurfacedAtColumn1747930964354';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "scheduled_to_be_surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "scheduled_to_be_surfaced_at"`,
    );
  }
}
