import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMediaIdScrapeTable1748014685978 implements MigrationInterface {
  name = 'AddMediaIdScrapeTable1748014685978';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD "media_id" uuid`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP COLUMN "media_id"`,
    );
  }
}
