import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTimestampzFields1747314982735 implements MigrationInterface {
  name = 'AddTimestampzFields1747314982735';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "scheduled_to_be_published_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ADD "published_at" TIMESTAMP WITH TIME ZONE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "scheduled_to_be_published_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "surfaced_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" DROP COLUMN "created_at"`,
    );
  }
}
