import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddScrapedJsonTable1743433482536 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`
      CREATE TABLE "scraped_json" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "seo_page_id" uuid NOT NULL,
        "scraped_current_values" jsonb NOT NULL DEFAULT '{}'::jsonb,
        "content" jsonb NOT NULL DEFAULT '[]'::jsonb,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "pk_scraped_json" PRIMARY KEY ("id"),
        CONSTRAINT "fk_scraped_json_seo_page" FOREIGN KEY ("seo_page_id")
          REFERENCES "seo_page"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      )
    `);

    // Create index on foreign key
    await queryRunner.query(`
      CREATE INDEX "idx_scraped_json_seo_page_id" ON "scraped_json" ("seo_page_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`DROP TABLE "scraped_json"`);
  }
}
