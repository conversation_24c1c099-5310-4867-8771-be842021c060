import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePageCompanyFk1748016447305 implements MigrationInterface {
  name = 'RemovePageCompanyFk1748016447305';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP CONSTRAINT "FK_b0e24ac390aa434be1a9932e6b6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" DROP CONSTRAINT "FK_5ee221827b225f949f9df5babf1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" DROP CONSTRAINT "FK_82cc9c8bd633e1e668ae8ba9070"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scrape" ADD CONSTRAINT "FK_82cc9c8bd633e1e668ae8ba9070" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ADD CONSTRAINT "FK_5ee221827b225f949f9df5babf1" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD CONSTRAINT "FK_b0e24ac390aa434be1a9932e6b6" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
