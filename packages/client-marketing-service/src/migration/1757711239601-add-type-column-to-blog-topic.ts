import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTypeColumnToBlogTopic1757711239601
  implements MigrationInterface
{
  name = 'AddTypeColumnToBlogTopic1757711239601';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the enum type if it doesn't exist
    await queryRunner.query(
      `DO $$ BEGIN
        CREATE TYPE "crew_ai_seo_automation"."blog_topic_type_enum" AS ENUM ('listicle', 'article');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;`,
    );

    // Add the type column with default value for existing rows
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" 
       ADD COLUMN IF NOT EXISTS "type" "crew_ai_seo_automation"."blog_topic_type_enum" NOT NULL DEFAULT 'article'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the type column
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."blog_topic" DROP COLUMN "type"`,
    );

    // Drop the enum type
    await queryRunner.query(
      `DROP TYPE IF EXISTS "crew_ai_seo_automation"."blog_topic_type_enum"`,
    );
  }
}
