import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenamePageFksToScrapedPageId1750791140621
  implements MigrationInterface
{
  name = 'RenamePageFksToScrapedPageId1750791140621';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "surfaced_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "scheduled_to_be_surfaced_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "applied_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "scheduled_to_be_published_at"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "scheduled_to_be_published_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "applied_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "scheduled_to_be_surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "surfaced_at" TIMESTAMP WITH TIME ZONE`,
    );
  }
}
