import { MigrationInterface, QueryRunner } from 'typeorm';

export class MakeGroupScrapedPageIdRequired1751908590000
  implements MigrationInterface
{
  name = 'MakeGroupScrapedPageIdRequired1751908590000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, ensure all existing groups have a scrapedPageId
    // This query will set scrapedPageId for any groups that still have NULL values
    await queryRunner.query(`
      UPDATE "crew_ai_seo_automation"."group" g
      SET scraped_page_id = subquery.scraped_page_id
      FROM (
        SELECT DISTINCT 
          g.id as group_id,
          sp.id as scraped_page_id
        FROM "crew_ai_seo_automation"."group" g
        INNER JOIN "crew_ai_seo_automation"."recommendation" r ON r.group_id = g.id
        INNER JOIN "crew_ai_seo_automation"."scrape" s ON s.id = r.scrape_id
        INNER JOIN "crew_ai_seo_automation"."scraped_page" sp ON sp.id = s.scraped_page_id
        WHERE g.scraped_page_id IS NULL
          AND r.scrape_id IS NOT NULL
      ) subquery
      WHERE g.id = subquery.group_id
    `);

    // Check if there are still any groups without scrapedPageId
    const groupsWithoutScrapedPage = await queryRunner.query(`
      SELECT COUNT(*) as count 
      FROM "crew_ai_seo_automation"."group" 
      WHERE scraped_page_id IS NULL
    `);

    if (groupsWithoutScrapedPage[0].count > 0) {
      throw new Error(
        `Cannot make scraped_page_id NOT NULL: ${groupsWithoutScrapedPage[0].count} groups still have NULL scraped_page_id`,
      );
    }

    // Now make the column NOT NULL
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "scraped_page_id" SET NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Make the column nullable again
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "scraped_page_id" DROP NOT NULL`,
    );
  }
}
