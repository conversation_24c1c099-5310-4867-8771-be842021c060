import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddMarkdownTable1743437951290 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`
      CREATE TABLE "markdown" (
        "id" uuid NOT NULL DEFAULT gen_random_uuid(),
        "scraped_json_id" uuid NOT NULL,
        "markdown" text NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "pk_markdown" PRIMARY KEY ("id"),
        CONSTRAINT "fk_markdown_scraped_json" FOREIGN KEY ("scraped_json_id")
          REFERENCES "scraped_json"("id") ON DELETE CASCADE ON UPDATE NO ACTION
      )
    `);

    // Create index on foreign key
    await queryRunner.query(`
      CREATE INDEX "idx_markdown_scraped_json_id" ON "markdown" ("scraped_json_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query('DROP TABLE "markdown"');
  }
}
