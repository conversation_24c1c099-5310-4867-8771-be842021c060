import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class GroupMetadataHotfix1743451746078 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
        UPDATE "group"
        SET metadata = jsonb_set(
            jsonb_set(
                metadata - 'rank',
                '{ranking}',
                coalesce(to_jsonb(metadata->'rank'), 'null'::jsonb)
            ) - 'primaryKeyword',
            '{keyword}',
            coalesce(to_jsonb(metadata->'primaryKeyword'), 'null'::jsonb)
        )
        WHERE metadata ? 'rank' OR metadata ? 'primaryKeyword'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
        UPDATE "group"
        SET metadata = jsonb_set(
            jsonb_set(
                metadata - 'ranking',
                '{rank}',
                coalesce(to_jsonb(metadata->'ranking'), 'null'::jsonb)
            ) - 'keyword',
            '{primaryKeyword}',
            coalesce(to_jsonb(metadata->'keyword'), 'null'::jsonb)
        )
        WHERE metadata ? 'ranking' OR metadata ? 'keyword'
    `);
  }
}
