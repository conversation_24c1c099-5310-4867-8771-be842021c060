import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeprecateTables1750256527433 implements MigrationInterface {
  name = 'DeprecateTables1750256527433';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
