import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFkGroupCompany1744307483088 implements MigrationInterface {
  name = 'AddFkGroupCompany1744307483088';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD CONSTRAINT "FK_b0e24ac390aa434be1a9932e6b6" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP CONSTRAINT "FK_b0e24ac390aa434be1a9932e6b6"`,
    );
  }
}
