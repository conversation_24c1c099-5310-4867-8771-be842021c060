import { MigrationInterface, QueryRunner } from 'typeorm';

export class UppercasedEnums1747927810520 implements MigrationInterface {
  name = 'UppercasedEnums1747927810520';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'PENDING' where "status" = 'pending'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'APPLIED' where "status" = 'applied'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'REJECTED' where "status" = 'rejected'`,
    );

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "status" SET DEFAULT 'PENDING'`,
    );
    await queryRunner.query(
      `ALTER TYPE "crew_ai_seo_automation"."page_page_type_enum" RENAME TO "page_page_type_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."page_page_type_enum" AS ENUM('HOMEPAGE', 'HOME_VALUATION', 'MORTGAGE_CALCULATOR', 'BUYERS_GUIDE', 'SELLERS_GUIDE', 'NEIGHBORHOOD_GUIDE', 'AGENT_BIO', 'BLOG')`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" TYPE "crew_ai_seo_automation"."page_page_type_enum" USING "page_type"::"text"::"crew_ai_seo_automation"."page_page_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" SET DEFAULT 'HOMEPAGE'`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."page_page_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "content_payload" SET DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "generation_payload" SET DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "failure_reason" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "failure_reason" SET DEFAULT '{}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'pending' where "status" = 'PENDING'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'applied' where "status" = 'APPLIED'`,
    );
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'rejected' where "status" = 'REJECTED'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "failure_reason" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "failure_reason" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "generation_payload" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scheduled_action" ALTER COLUMN "content_payload" DROP DEFAULT`,
    );
    await queryRunner.query(
      `CREATE TYPE "crew_ai_seo_automation"."page_page_type_enum_old" AS ENUM('AGENT-BIO', 'BLOG', 'BUYERS-GUIDE', 'HOME-VALUATION', 'HOMEPAGE', 'MORTGAGE-CALCULATOR', 'NEIGHBORHOOD-GUIDE', 'SELLERS-GUIDE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" TYPE "crew_ai_seo_automation"."page_page_type_enum_old" USING "page_type"::"text"::"crew_ai_seo_automation"."page_page_type_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."page" ALTER COLUMN "page_type" SET DEFAULT 'HOMEPAGE'`,
    );
    await queryRunner.query(
      `DROP TYPE "crew_ai_seo_automation"."page_page_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "crew_ai_seo_automation"."page_page_type_enum_old" RENAME TO "page_page_type_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
  }
}
