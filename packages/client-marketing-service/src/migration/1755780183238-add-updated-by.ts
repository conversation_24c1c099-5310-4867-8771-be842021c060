import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUpdatedBy1755780183238 implements MigrationInterface {
  name = 'AddUpdatedBy1755780183238';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "updated_by" text`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "updated_by"`,
    );
  }
}
