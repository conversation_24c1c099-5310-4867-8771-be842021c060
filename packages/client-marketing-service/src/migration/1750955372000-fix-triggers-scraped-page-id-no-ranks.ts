import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixTriggersScrapedPageIdNoRanks1750955372000
  implements MigrationInterface
{
  name = 'FixTriggersScrapedPageIdNoRanks1750955372000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing triggers
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS trigger_track_keyword_insert ON "crew_ai_seo_automation"."page_keyword"`,
    );
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS trigger_track_keyword_update ON "crew_ai_seo_automation"."page_keyword"`,
    );

    // Update the insert trigger function to use scraped_page_id and remove rank columns
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION crew_ai_seo_automation.track_keyword_insert() RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO crew_ai_seo_automation.page_keyword_history (
          scraped_page_id,
          keyword_id,
          assigned_at,
          change_note,
          updated_by
        )
        VALUES (
          NEW.scraped_page_id,
          NEW.keyword_id,
          NOW(),
          current_setting('app.change_note', true),
          (SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID)
        );
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Update the update trigger function to use scraped_page_id and remove rank columns
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION crew_ai_seo_automation.track_keyword_update() RETURNS TRIGGER AS $$
      DECLARE
        note TEXT;
        editor UUID;
      BEGIN
        IF NEW.keyword_id IS DISTINCT FROM OLD.keyword_id THEN
          SELECT current_setting('app.change_note', true) INTO note;
          SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID INTO editor;

          -- Mark the previous record as removed
          UPDATE crew_ai_seo_automation.page_keyword_history
          SET removed_at = NOW()
          WHERE scraped_page_id = OLD.scraped_page_id
            AND keyword_id = OLD.keyword_id
            AND removed_at IS NULL;

          -- Insert new record without rank columns
          INSERT INTO crew_ai_seo_automation.page_keyword_history (
            scraped_page_id,
            keyword_id,
            assigned_at,
            change_note,
            updated_by
          )
          VALUES (
            NEW.scraped_page_id,
            NEW.keyword_id,
            NOW(),
            note,
            editor
          );
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Recreate triggers
    await queryRunner.query(`
      CREATE TRIGGER trigger_track_keyword_insert
      AFTER INSERT ON crew_ai_seo_automation.page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION crew_ai_seo_automation.track_keyword_insert();
    `);

    await queryRunner.query(`
      CREATE TRIGGER trigger_track_keyword_update
      AFTER UPDATE ON crew_ai_seo_automation.page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION crew_ai_seo_automation.track_keyword_update();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop triggers
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS trigger_track_keyword_insert ON "crew_ai_seo_automation"."page_keyword"`,
    );
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS trigger_track_keyword_update ON "crew_ai_seo_automation"."page_keyword"`,
    );

    // Restore original functions - no changes needed as other migrations handle this
  }
}
