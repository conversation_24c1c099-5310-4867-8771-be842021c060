import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddScrapedPageIdToGroup1751900052373
  implements MigrationInterface
{
  name = 'AddScrapedPageIdToGroup1751900052373';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD "scraped_page_id" uuid`,
    );

    // Populate existing groups with scrapedPageId from their recommendations
    await queryRunner.query(`
            UPDATE "crew_ai_seo_automation"."group" g
            SET scraped_page_id = subquery.scraped_page_id
            FROM (
                SELECT DISTINCT 
                    g.id as group_id,
                    sp.id as scraped_page_id
                FROM "crew_ai_seo_automation"."group" g
                INNER JOIN "crew_ai_seo_automation"."recommendation" r ON r.group_id = g.id
                INNER JOIN "crew_ai_seo_automation"."scrape" s ON s.id = r.scrape_id
                INNER JOIN "crew_ai_seo_automation"."scraped_page" sp ON sp.id = s.scraped_page_id
                WHERE g.scraped_page_id IS NULL
                  AND r.scrape_id IS NOT NULL
            ) subquery
            WHERE g.id = subquery.group_id
        `);

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ADD CONSTRAINT "FK_1913c037e5e845fcdac468b10cf" FOREIGN KEY ("scraped_page_id") REFERENCES "crew_ai_seo_automation"."scraped_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP CONSTRAINT "FK_1913c037e5e845fcdac468b10cf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" DROP COLUMN "scraped_page_id"`,
    );
  }
}
