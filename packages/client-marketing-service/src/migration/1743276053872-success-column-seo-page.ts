import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddScrapeStatusAndMetadataColumnToSeoPage1743276053872
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `ALTER TABLE seo_page ADD COLUMN scrape_status VARCHAR NOT NULL DEFAULT 'pending';
       ALTER TABLE seo_page ADD COLUMN metadata JSONB NOT NULL DEFAULT '{}';
      `,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`ALTER TABLE seo_page DROP COLUMN scrape_status;
       ALTER TABLE seo_page DROP COLUMN metadata`);
  }
}
