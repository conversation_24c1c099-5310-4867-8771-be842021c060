import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropDeprecatedTables1750258304392 implements MigrationInterface {
  name = 'DropDeprecatedTables1750258304392';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop tables and constraints for entities that no longer exist
    // Order is important due to foreign key dependencies

    // First, drop dependent tables (tables that reference other deprecated tables)

    // Drop markdown table (references scraped_json)
    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."markdown" CASCADE`,
    );

    // Drop scraped_json table (references seo_page)
    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."scraped_json" CASCADE`,
    );

    // Drop company-related dependent tables (reference company)
    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."company_agent" CASCADE`,
    );

    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."company_neighborhood" CASCADE`,
    );

    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."company_seo_data" CASCADE`,
    );

    // Drop seo_page table (references company)
    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."seo_page" CASCADE`,
    );

    // Finally, drop the company table (referenced by many others)
    await queryRunner.query(
      `DROP TABLE IF EXISTS "crew_ai_seo_automation"."company" CASCADE`,
    );

    // Note: The CASCADE option automatically drops any remaining constraints,
    // indexes, and dependent objects associated with these tables
  }

  public down(): Promise<void> {
    // For the down migration, we would need to recreate all the tables
    // and their constraints. However, since these entities no longer exist
    // in the codebase, there's no practical way to accurately recreate them.
    // This migration is considered irreversible for data safety.

    throw new Error(
      'This migration is irreversible. The deprecated tables and their schemas ' +
        'cannot be safely recreated as their corresponding entities no longer exist ' +
        'in the codebase. Please restore from a backup if you need to revert this change.',
    );
  }
}
