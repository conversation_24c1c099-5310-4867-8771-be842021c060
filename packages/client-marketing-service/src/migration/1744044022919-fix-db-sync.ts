import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixDbSync1744044038842 implements MigrationInterface {
  name = 'FixDbSync1744044038842';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" DROP CONSTRAINT "fk_markdown_scraped_json"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" DROP CONSTRAINT "company_agent_company_id_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" DROP CONSTRAINT "company_neighborhood_company_id_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" DROP CONSTRAINT "company_seo_data_company_id_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "recommendation_group_id_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "recommendation_seo_page_id_fkey"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" DROP CONSTRAINT "fk_scraped_json_seo_page"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."idx_markdown_scraped_json_id"`,
    );
    await queryRunner.query(
      `DROP INDEX "crew_ai_seo_automation"."idx_scraped_json_seo_page_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" DROP CONSTRAINT "company_agent_company_id_name_key"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" DROP CONSTRAINT "company_neighborhood_company_id_name_key"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ADD CONSTRAINT "UQ_af5e78cde6daec855fcb61c4c2e" UNIQUE ("scraped_json_id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "name" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "name" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ADD CONSTRAINT "UQ_11e85e47c0d3b160e580689d852" UNIQUE ("company_id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "business_name" TYPE character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "title" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "description" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "type" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" SET DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "recommendation_value" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "reasoning" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "status" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "page_type" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ADD CONSTRAINT "UQ_812108c67b1eb05d0c810010fc8" UNIQUE ("seo_page_id")`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "scraped_current_values" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "content" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ADD CONSTRAINT "FK_af5e78cde6daec855fcb61c4c2e" FOREIGN KEY ("scraped_json_id") REFERENCES "crew_ai_seo_automation"."scraped_json"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ADD CONSTRAINT "FK_55e71237daa0b5c267e8aae3f47" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ADD CONSTRAINT "FK_d8e7e20bc45cb2164a6ca488f67" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ADD CONSTRAINT "FK_11e85e47c0d3b160e580689d852" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "FK_f578a651888ffa1aac2ca94f6f3" FOREIGN KEY ("group_id") REFERENCES "crew_ai_seo_automation"."group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );

    // STAGING DATA BACKFILL
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('73b64e58-08e9-4d6d-8055-0a6d6dfa108d', 'michaelrifflesawesome.website')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('23785caf-cce3-48c9-9769-148fad372d4f', 'pisciolari.com.ar')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('a7b4401f-a8be-440d-922f-7b133d4f2197', 'ryansbeach.homes')`,
    );

    // PROD DATA BACKFILL

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('59acc31b-df7e-4766-a4a2-db21cf456207', 'orlandohomesquad.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('8690f306-17d5-4c21-b51e-47d59f7fb6ca', '15westhomes.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('e69e8d0b-14c7-43b0-b8c4-9a123c8839e6', 'thekupresgroup.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('0c03aeb4-5cb7-4959-888a-bca38de4ec11', 'weirproperties.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('ae2e7dad-5b8e-415e-b425-ef3c6fff17c9', 'thesprigggroup.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('a4cbb578-5958-4a5a-84bc-948c71d51415', 'homesbyverso.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('d865a4b9-02cb-44cd-897a-b2472fa3c020', 'sullivanbrownell.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('0fda388e-1ef6-4b42-8471-5d85887a5db1', 'maximprops.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('1eb586d5-e890-40f0-b55a-c6daa7074bdd', 'cynthiaannettesmith.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('1af1eb9c-7ea4-4539-ac03-7ee31b5893cc', 'monica.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('ab48e2c9-4132-49e3-8980-00ad308c54d7', 'theelevategroupfl.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('e44ee2a6-c4e2-4a24-84a7-caaf5f9f8d2d', 'lisaadkins.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('94ce3560-af3a-4118-9da9-f43a469b91b8', 'lorilanerealestate.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('28376e64-e5f7-4bec-8902-75d791d2da1e', 'lindafaille.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('596d7cd5-9511-4a0e-8b8c-9575bbf4f483', 'robkotelsky.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('2819c4d4-33d4-4c38-be64-f1e02fd07524', 'silversgriffinnaples.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('2a81ce00-0a59-4241-a684-b9a628ac55d7', 'lankheetgroup.com')`,
    );

    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('0a3add92-f90e-48c2-9c6d-ad8de92e0f25', 'chrisadlam.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('dbae6686-1b6a-4939-b87d-79ed58adbca3', 'rubaandhaifa.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('1439c8f2-8252-4980-9b04-53417518a19d', 'stacyhanan.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('23ed80c7-8d51-4f7b-bfc7-0fedd9ae3e58', 'galluccihomes.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('7f624868-fa24-4b17-86b2-aa13b1e6d39b', 'bouhaus.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('7430d32c-5461-4f71-9cc6-02dafc1e9c91', 'taeyaharle.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('d6f60f25-f6f8-4c95-9628-d9ce79a6babe', 'aviablumtopagent.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('2107be3e-a1ca-4ef7-b89b-1d466e217ada', 'moderncollectiveteam.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('71cc134b-9b59-419d-9354-c4aecb4c7d26', 'gerlockhomes.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('a4de0bde-62fe-48b2-af11-766ac8aa0cf5', 'karentorrente.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('6c8cb883-35c6-4b84-a6b0-a5f4347c386c', 'thearizonalbiltmorerealty.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('9396d422-3942-4b0a-b114-fd6a1a0deef0', 'claytonwolfe.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('2f1743c6-ffce-46e8-9b13-a2a8b9429423', 'jessicastencel.com')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('75663dfe-9a4b-4643-b0b7-6c81cf8b001d', 'wangteam.ca')`,
    );
    await queryRunner.query(
      `INSERT INTO "crew_ai_seo_automation"."company" ("id", "domain") VALUES ('5fed8db3-3543-4f5a-b48d-7e2900f0e485', 'gregcorvi.com')`,
    );

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ADD CONSTRAINT "FK_09736b17c38745cd60a23b339fc" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ADD CONSTRAINT "FK_812108c67b1eb05d0c810010fc8" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" DROP CONSTRAINT "FK_812108c67b1eb05d0c810010fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" DROP CONSTRAINT "FK_09736b17c38745cd60a23b339fc"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_f578a651888ffa1aac2ca94f6f3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" DROP CONSTRAINT "FK_7d28011971c93fb8a81eaed7a10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" DROP CONSTRAINT "FK_11e85e47c0d3b160e580689d852"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" DROP CONSTRAINT "FK_d8e7e20bc45cb2164a6ca488f67"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" DROP CONSTRAINT "FK_55e71237daa0b5c267e8aae3f47"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" DROP CONSTRAINT "FK_af5e78cde6daec855fcb61c4c2e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "content" SET DEFAULT '[]'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ALTER COLUMN "scraped_current_values" SET DEFAULT '{}'`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" DROP CONSTRAINT "UQ_812108c67b1eb05d0c810010fc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."seo_page" ALTER COLUMN "page_type" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "status" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "reasoning" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "recommendation_value" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "current_value" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ALTER COLUMN "type" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "description" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."group" ALTER COLUMN "title" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "business_name" TYPE text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" TYPE text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" TYPE text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ALTER COLUMN "location" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" DROP CONSTRAINT "UQ_11e85e47c0d3b160e580689d852"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ALTER COLUMN "name" TYPE text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "name" TYPE text`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ALTER COLUMN "updated_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ALTER COLUMN "created_at" SET DEFAULT CURRENT_TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" DROP CONSTRAINT "UQ_af5e78cde6daec855fcb61c4c2e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ADD CONSTRAINT "company_neighborhood_company_id_name_key" UNIQUE ("company_id", "name")`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ADD CONSTRAINT "company_agent_company_id_name_key" UNIQUE ("company_id", "name")`,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_scraped_json_seo_page_id" ON "crew_ai_seo_automation"."scraped_json" ("seo_page_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "idx_markdown_scraped_json_id" ON "crew_ai_seo_automation"."markdown" ("scraped_json_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."scraped_json" ADD CONSTRAINT "fk_scraped_json_seo_page" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "recommendation_seo_page_id_fkey" FOREIGN KEY ("seo_page_id") REFERENCES "crew_ai_seo_automation"."seo_page"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."recommendation" ADD CONSTRAINT "recommendation_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "crew_ai_seo_automation"."group"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_seo_data" ADD CONSTRAINT "company_seo_data_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_neighborhood" ADD CONSTRAINT "company_neighborhood_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."company_agent" ADD CONSTRAINT "company_agent_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "crew_ai_seo_automation"."company"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."markdown" ADD CONSTRAINT "fk_markdown_scraped_json" FOREIGN KEY ("scraped_json_id") REFERENCES "crew_ai_seo_automation"."scraped_json"("id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }
}
