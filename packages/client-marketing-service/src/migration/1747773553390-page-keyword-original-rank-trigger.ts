import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class PageKeywordOriginalRankTrigger1747773553390
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
      -- Function to set originalRank to currentRank if originalRank is null
      CREATE OR REPLACE FUNCTION set_original_rank() RETURNS trigger AS $$
      BEGIN
        -- Only set originalRank if it's null and currentRank is not null
        IF NEW.original_rank IS NULL AND NEW.current_rank IS NOT NULL THEN
          NEW.original_rank := NEW.current_rank;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Trigger that runs before insert or update on page_keyword
      CREATE TRIGGER trigger_set_original_rank
      BEFORE INSERT OR UPDATE ON page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION set_original_rank();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_set_original_rank ON page_keyword;
      DROP FUNCTION IF EXISTS set_original_rank();
    `);
  }
}
