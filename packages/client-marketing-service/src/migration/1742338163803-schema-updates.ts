import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdates1742338163803 implements MigrationInterface {
  name = 'SchemaUpdates1742338163803';
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`DROP TABLE IF EXISTS recommendation`);
    await queryRunner.query(`DROP TABLE IF EXISTS page`);

    await queryRunner.query(
      `CREATE TABLE seo_page (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            company_id UUID NOT NULL,
            domain TEXT NOT NULL,
            path TEXT NOT NULL,
            page_type TEXT,
            created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
        )`,
    );

    await queryRunner.query(
      `CREATE TABLE "group" (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                company_id UUID NOT NULL,
                title TEXT,
                description TEXT,
                metadata JSONB NOT NULL DEFAULT '{}',
                published_at TIMESTAMPTZ,
                created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
                updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
            )`,
    );

    await queryRunner.query(
      `CREATE TABLE recommendation (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            seo_page_id UUID NOT NULL REFERENCES seo_page(id) ON DELETE CASCADE,
            group_id UUID NOT NULL REFERENCES "group"(id) ON DELETE CASCADE,
            type TEXT,
            current_value TEXT,
            recommendation_value TEXT,
            reasoning TEXT,
            status TEXT DEFAULT 'suggested',
            metadata JSONB NOT NULL DEFAULT '{}',
            status_updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
        )`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    await queryRunner.query(`DROP TABLE IF EXISTS recommendation`);
    await queryRunner.query(`DROP TABLE IF EXISTS seo_page`);
    await queryRunner.query(`DROP TABLE IF EXISTS "group"`);
  }
}
