import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateKeywordHistoryTriggers1748020217290
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Set schema explicitly to crew_ai_seo_automation
    await queryRunner.query(`SET search_path TO crew_ai_seo_automation;`);

    // Drop existing triggers
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_track_keyword_insert ON page_keyword;
      DROP TRIGGER IF EXISTS trigger_track_keyword_update ON page_keyword;
    `);

    // Recreate the functions with explicit schema references
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION crew_ai_seo_automation.track_keyword_insert() R<PERSON><PERSON>NS trigger AS $$
      BEGIN
        INSERT INTO crew_ai_seo_automation.page_keyword_history (
          page_id,
          keyword_id,
          assigned_at,
          change_note,
          updated_by
        )
        VALUES (
          NEW.page_id,
          NEW.keyword_id,
          NOW(),
          current_setting('app.change_note', true),
          (SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID)
        );
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE OR REPLACE FUNCTION crew_ai_seo_automation.track_keyword_update() RETURNS trigger AS $$
      DECLARE
        note TEXT;
        editor UUID;
      BEGIN
        IF NEW.keyword_id IS DISTINCT FROM OLD.keyword_id THEN
          SELECT current_setting('app.change_note', true) INTO note;
          SELECT NULLIF(current_setting('app.updated_by', true), '')::UUID INTO editor;

          -- Mark the previous record as removed
          UPDATE crew_ai_seo_automation.page_keyword_history
          SET removed_at = NOW()
          WHERE page_id = OLD.page_id
            AND keyword_id = OLD.keyword_id
            AND removed_at IS NULL;

          -- Insert new record with current rank and audit metadata
          INSERT INTO crew_ai_seo_automation.page_keyword_history (
            page_id,
            keyword_id,
            assigned_at,
            change_note,
            updated_by
          )
          VALUES (
            NEW.page_id,
            NEW.keyword_id,
            NOW(),
            note, editor
          );
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Recreate the triggers with explicit schema references
    await queryRunner.query(`
      CREATE TRIGGER trigger_track_keyword_insert
      AFTER INSERT ON crew_ai_seo_automation.page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION crew_ai_seo_automation.track_keyword_insert();

      CREATE TRIGGER trigger_track_keyword_update
      AFTER UPDATE ON crew_ai_seo_automation.page_keyword
      FOR EACH ROW
      EXECUTE FUNCTION crew_ai_seo_automation.track_keyword_update();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Set schema explicitly to crew_ai_seo_automation
    await queryRunner.query(`SET search_path TO crew_ai_seo_automation;`);

    // We don't want to drop the triggers in the down migration since we're just updating them
    // Instead, we'll restore the original behavior without explicit schema references
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS trigger_track_keyword_insert ON page_keyword;
      DROP TRIGGER IF EXISTS trigger_track_keyword_update ON page_keyword;
      DROP FUNCTION IF EXISTS track_keyword_insert();
      DROP FUNCTION IF EXISTS track_keyword_update();
    `);
  }
}
