import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRecStatusToApplied1744743049335
  implements MigrationInterface
{
  name = 'UpdateRecStatusToApplied1744743049335';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'applied' WHERE "status" = 'approved';`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE "crew_ai_seo_automation"."recommendation" SET "status" = 'approved' WHERE "status" = 'applied';`,
    );
  }
}
