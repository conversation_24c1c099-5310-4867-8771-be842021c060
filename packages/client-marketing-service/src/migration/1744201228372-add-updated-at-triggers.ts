import { MigrationInterface, QueryRunner } from 'typeorm';

import { setPathToSchema } from '../common/utils/set-path-to-schema.util';

export class AddUpdatedAtTriggers1744201228372 implements MigrationInterface {
  name = 'AddUpdatedAtTriggers1744201228372';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Create the trigger function first
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION trigger_set_timestamp()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON company
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON company_agent
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON company_neighborhood
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON company_seo_data
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON "group"
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON markdown
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON recommendation
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON scraped_json
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
    await queryRunner.query(
      `CREATE TRIGGER trigger_set_timestamp
       BEFORE UPDATE ON seo_page
       FOR EACH ROW
       EXECUTE PROCEDURE trigger_set_timestamp();`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);

    // Drop triggers
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON company;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON company_agent;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON company_neighborhood;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON company_seo_data;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON "group";',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON markdown;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON recommendation;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON scraped_json;',
    );
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS trigger_set_timestamp ON seo_page;',
    );

    // Drop the trigger function
    await queryRunner.query(
      'DROP FUNCTION IF EXISTS trigger_set_timestamp() CASCADE;',
    );
  }
}
