import { setPathToSchema } from 'src/common/utils/set-path-to-schema.util';
import { MigrationInterface, QueryRunner } from 'typeorm';

import { RecommendationType } from '../common/types/scraped-element.type';

enum CurrentRecommendationType {
  META_TITLE = 'Meta Title',
  META_DESCRIPTION = 'Meta Description',
  MAIN_HEADING = 'Main Heading',
}

export class UpdateRecType1743438629457 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(
      `UPDATE recommendation SET type = '${RecommendationType.META_TITLE}' WHERE type = '${CurrentRecommendationType.META_TITLE}'`,
    );
    await queryRunner.query(
      `UPDATE recommendation SET type = '${RecommendationType.META_DESCRIPTION}' WHERE type = '${CurrentRecommendationType.META_DESCRIPTION}'`,
    );
    await queryRunner.query(
      `UPDATE recommendation SET type = '${RecommendationType.MAIN_HEADING}' WHERE type = '${CurrentRecommendationType.MAIN_HEADING}'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await setPathToSchema(queryRunner);
    await queryRunner.query(
      `UPDATE recommendation SET type = '${CurrentRecommendationType.META_TITLE}' WHERE type = '${RecommendationType.META_TITLE}'`,
    );
    await queryRunner.query(
      `UPDATE recommendation SET type = '${CurrentRecommendationType.META_DESCRIPTION}' WHERE type = '${RecommendationType.META_DESCRIPTION}'`,
    );
    await queryRunner.query(
      `UPDATE recommendation SET type = '${CurrentRecommendationType.MAIN_HEADING}' WHERE type = '${RecommendationType.MAIN_HEADING}'`,
    );
  }
}
