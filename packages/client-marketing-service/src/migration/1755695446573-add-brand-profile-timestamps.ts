import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBrandProfileTimestamps1755695446573
  implements MigrationInterface
{
  name = 'AddBrandProfileTimestamps1755695446573';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" ADD "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "updated_at"`,
    );
    await queryRunner.query(
      `ALTER TABLE "crew_ai_seo_automation"."brand_profile" DROP COLUMN "created_at"`,
    );
  }
}
