import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CreateScrapedPageDto } from './dto/create-scraped-page.dto';
import { UpdateScrapedPageDto } from './dto/update-scraped-page.dto';
import { ScrapedPageController } from './scraped-page.controller';
import { ScrapedPageService } from './scraped-page.service';

describe('ScrapedPageController', () => {
  let controller: ScrapedPageController;
  let scrapedPageService: ScrapedPageService;

  const mockScrapedPageService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScrapedPageController],
      providers: [
        {
          provide: ScrapedPageService,
          useValue: mockScrapedPageService,
        },
      ],
    }).compile();

    controller = module.get<ScrapedPageController>(ScrapedPageController);
    scrapedPageService = module.get<ScrapedPageService>(ScrapedPageService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new page', async () => {
      const createPageDto: CreateScrapedPageDto = {
        url: 'https://example.com',
        pageName: 'Test Page',
        pageType: 'HOMEPAGE' as any,
        companyId: '123',
      };
      const createdPage = { id: '1', ...createPageDto };

      mockScrapedPageService.create.mockResolvedValue(createdPage);

      const result = await controller.create(createPageDto);

      expect(scrapedPageService.create).toHaveBeenCalledWith(createPageDto);
      expect(result).toEqual(createdPage);
    });
  });

  describe('findAll', () => {
    it('should return all pages', async () => {
      const pages = [
        { id: '1', url: 'https://example.com/1' },
        { id: '2', url: 'https://example.com/2' },
      ];

      mockScrapedPageService.findAll.mockResolvedValue(pages);

      const result = await controller.findAll();

      expect(scrapedPageService.findAll).toHaveBeenCalled();
      expect(result).toEqual(pages);
    });
  });

  describe('findOne', () => {
    it('should return a single page', async () => {
      const pageId = '1';
      const page = { id: pageId, url: 'https://example.com' };

      mockScrapedPageService.findOne.mockResolvedValue(page);

      const result = await controller.findOne(pageId);

      expect(scrapedPageService.findOne).toHaveBeenCalledWith(pageId);
      expect(result).toEqual(page);
    });

    it('should throw NotFoundException when page not found', async () => {
      const pageId = '1';

      mockScrapedPageService.findOne.mockRejectedValue(
        new NotFoundException('Page with ID "1" not found'),
      );

      await expect(controller.findOne(pageId)).rejects.toThrow(
        NotFoundException,
      );
      expect(scrapedPageService.findOne).toHaveBeenCalledWith(pageId);
    });
  });

  describe('update', () => {
    it('should update a page', async () => {
      const pageId = '1';
      const updatePageDto: UpdateScrapedPageDto = {
        pageName: 'Updated Page Name',
        metadata: { updated: 'data' },
      };
      const updatedPage = {
        id: pageId,
        url: 'https://example.com',
        ...updatePageDto,
      };

      mockScrapedPageService.update.mockResolvedValue(updatedPage);

      const result = await controller.update(pageId, updatePageDto);

      expect(scrapedPageService.update).toHaveBeenCalledWith(
        pageId,
        updatePageDto,
      );
      expect(result).toEqual(updatedPage);
    });

    it('should throw NotFoundException when page not found', async () => {
      const pageId = '1';
      const updatePageDto: UpdateScrapedPageDto = {
        pageName: 'Updated Page Name',
      };

      mockScrapedPageService.update.mockRejectedValue(
        new NotFoundException('Page with ID "1" not found'),
      );

      await expect(controller.update(pageId, updatePageDto)).rejects.toThrow(
        NotFoundException,
      );
      expect(scrapedPageService.update).toHaveBeenCalledWith(
        pageId,
        updatePageDto,
      );
    });
  });

  describe('remove', () => {
    it('should remove a page', async () => {
      const pageId = '1';
      const deleteResult = { affected: 1 };

      mockScrapedPageService.remove.mockResolvedValue(deleteResult);

      const result = await controller.remove(pageId);

      expect(scrapedPageService.remove).toHaveBeenCalledWith(pageId);
      expect(result).toEqual(deleteResult);
    });

    it('should throw NotFoundException when page not found', async () => {
      const pageId = '1';

      mockScrapedPageService.remove.mockRejectedValue(
        new NotFoundException('Page with ID "1" not found'),
      );

      await expect(controller.remove(pageId)).rejects.toThrow(
        NotFoundException,
      );
      expect(scrapedPageService.remove).toHaveBeenCalledWith(pageId);
    });
  });
});
