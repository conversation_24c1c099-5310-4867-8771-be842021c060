import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';

import { CreateScrapedPageDto } from './dto/create-scraped-page.dto';
import { UpdateScrapedPageDto } from './dto/update-scraped-page.dto';
import { ScrapedPageService } from './scraped-page.service';

@Controller('scraped-page')
export class ScrapedPageController {
  constructor(private readonly scrapedPageService: ScrapedPageService) {}

  @Post()
  create(@Body() createScrapedPageDto: CreateScrapedPageDto) {
    return this.scrapedPageService.create(createScrapedPageDto);
  }

  @Get()
  findAll() {
    return this.scrapedPageService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.scrapedPageService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateScrapedPageDto: UpdateScrapedPageDto,
  ) {
    return this.scrapedPageService.update(id, updateScrapedPageDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.scrapedPageService.remove(id);
  }
}
