import { Field, ID, ObjectType, registerEnumType } from '@nestjs/graphql';
import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsEnum,
  IsObject,
  IsDate,
} from 'class-validator';
import { Graph<PERSON>JSONObject } from 'graphql-type-json';
import { Factory } from 'nestjs-seeder';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Company } from 'src/company/entities/company.entity';
import { Group } from 'src/group/entities/group.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { PageKeywordHistory } from 'src/page-keyword-history/entities/page-keyword-history.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  DeleteDateColumn,
} from 'typeorm';

export enum ScrapedPageType {
  HOMEPAGE = 'HOMEPAGE',
  HOME_VALUATION = 'HOME_VALUATION',
  MORTGAGE_CALCULATOR = 'MORTGAGE_CALCULATOR',
  BUYERS_GUIDE = 'BUYERS_GUIDE',
  SELLERS_GUIDE = 'SELLERS_GUIDE',
  NEIGHBORHOOD_GUIDE = 'NEIGHBORHOOD_GUIDE',
  AGENT_BIO = 'AGENT_BIO',
  BLOG = 'BLOG',
}

registerEnumType(ScrapedPageType, { name: 'ScrapedPageType' });

@Entity('scraped_page')
@ObjectType()
export class ScrapedPage {
  @PrimaryGeneratedColumn('uuid')
  @IsUUID()
  @Field(() => ID)
  id: string;

  @Column({ type: 'uuid', name: 'company_id' })
  @IsUUID()
  @Index()
  @IsNotEmpty()
  @Field(() => ID)
  companyId: string;

  @Column({ type: 'text', unique: true })
  @IsNotEmpty()
  @IsString()
  @Factory(SeederFactories.pageUrl)
  @Field(() => String)
  url: string;

  @Column({ type: 'text', name: 'page_name' })
  @IsNotEmpty()
  @IsString()
  @Factory(faker => {
    const pageNames = [
      'Home Page',
      'Property Listings',
      'About Our Team',
      'Contact Us',
      'Mortgage Calculator',
      'Home Valuation',
      'Buyer Guide',
      'Seller Guide',
      'Neighborhood Guide',
      'Agent Bio',
    ];
    return faker?.helpers.arrayElement(pageNames) || 'Home Page';
  })
  @Field(() => String)
  pageName: string;

  @Column({
    type: 'enum',
    enum: ScrapedPageType,
    default: ScrapedPageType.HOMEPAGE,
    name: 'page_type',
  })
  @IsEnum(ScrapedPageType)
  @IsNotEmpty()
  @Factory(
    faker =>
      faker?.helpers.arrayElement(Object.values(ScrapedPageType)) ??
      ScrapedPageType.HOMEPAGE,
  )
  @Field(() => ScrapedPageType)
  pageType: ScrapedPageType;

  @IsObject()
  @Column({ type: 'jsonb', default: '{}' })
  @Factory(SeederFactories.pageMetadata)
  @Field(() => GraphQLJSONObject)
  metadata: Record<string, any>;

  @IsDate()
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  @Field(() => Date)
  createdAt: Date;

  @IsDate()
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  @Field(() => Date)
  updatedAt: Date;

  @IsDate()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  @Field(() => Date, { nullable: true })
  deletedAt: Date | null;

  @Field(() => [Recommendation], { nullable: true })
  recommendations: Recommendation[];

  @OneToMany(() => Scrape, scrape => scrape.scrapedPage, {
    cascade: true,
  })
  @Field(() => [Scrape], { nullable: true })
  scrapes: Scrape[];

  @OneToMany(
    () => PageKeywordHistory,
    pageKeywordHistory => pageKeywordHistory.scrapedPage,
  )
  @Field(() => [PageKeywordHistory], { nullable: true })
  pageKeywordHistory: PageKeywordHistory[];

  @OneToMany(() => PageKeyword, pageKeyword => pageKeyword.scrapedPage)
  @Field(() => [PageKeyword], { nullable: true })
  pageKeywords: PageKeyword[];

  @OneToMany(() => Group, group => group.scrapedPage)
  @Field(() => [Group], { nullable: true })
  groups: Group[];

  // Federation: Company field that references external Company entity
  @Field(() => Company, { nullable: true })
  company?: Company;
}
