import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class ScrapedPagePolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  /**
   * Checks if the user has permission to read scraped pages
   * @param companyId Optional company ID to filter by. If not provided, returns true for super users
   * @returns boolean
   */
  read = (companyId?: string) => {
    if (!companyId) {
      return this.auth.isSuper();
    }

    return this.auth.isSuper() || this.auth.belongsToCompany(companyId);
  };
}
