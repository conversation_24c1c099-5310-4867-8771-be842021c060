import { ForbiddenException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import {
  createMockAuthContext,
  mockAuthorizationSuccess,
  mockAuthorizationFailure,
  createPageServiceMock,
  createBasicResolverModule,
  createMockScrapedPage,
  testServiceError,
} from 'src/test-utils';

import { ScrapedPage } from './entities/scraped-page.entity';
import { ScrapedPagePolicy } from './scraped-page.policy';
import { ScrapedPageResolver } from './scraped-page.resolver';
import { ScrapedPageService } from './scraped-page.service';

describe('ScrapedPageResolver', () => {
  let resolver: ScrapedPageResolver;
  let service: ScrapedPageService;
  let mockAuthContext: ReturnType<typeof createMockAuthContext>;
  let mockScrapedPageService: ReturnType<typeof createPageServiceMock>;

  beforeEach(async () => {
    mockAuthContext = createMockAuthContext();
    mockScrapedPageService = createPageServiceMock();

    const module: TestingModule = await createBasicResolverModule(
      ScrapedPageResolver,
      ScrapedPageService,
      mockScrapedPageService,
      ScrapedPagePolicy,
    );

    resolver = module.get<ScrapedPageResolver>(ScrapedPageResolver);
    service = module.get<ScrapedPageService>(ScrapedPageService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Test resolver definition
  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('findAll', () => {
    const mockPages = [
      createMockScrapedPage(),
      createMockScrapedPage({ id: 'page-2' }),
    ];

    it('should return an array of Pages when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScrapedPageService.findAll.mockResolvedValue(mockPages);

      const result = await resolver.findAll(mockAuthContext);

      expect(result).toEqual(mockPages);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scrapedPage',
        'read',
        undefined,
      );
    });

    it('should return pages filtered by companyId when provided', async () => {
      const companyId = '123';
      mockAuthorizationSuccess(mockAuthContext);
      mockScrapedPageService.findAll.mockResolvedValue(mockPages);

      const result = await resolver.findAll(mockAuthContext, companyId);

      expect(result).toEqual(mockPages);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scrapedPage',
        'read',
        companyId,
      );
      expect(service.findAll).toHaveBeenCalledWith(companyId);
    });

    it('should throw ForbiddenException when not authorized', async () => {
      mockAuthorizationFailure(mockAuthContext);

      await expect(resolver.findAll(mockAuthContext)).rejects.toThrow(
        ForbiddenException,
      );
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scrapedPage',
        'read',
        undefined,
      );
    });

    it('should handle service errors when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      await testServiceError(
        (authCtx: any, companyId?: string) =>
          resolver.findAll(authCtx, companyId),
        mockScrapedPageService,
        'findAll',
        new Error('Service error'),
        mockAuthContext,
      )();
    });
  });

  describe('findOne', () => {
    const mockPage = createMockScrapedPage();

    it('should return a single Page when authorized', async () => {
      mockAuthorizationSuccess(mockAuthContext);
      mockScrapedPageService.findOne.mockResolvedValue(mockPage);

      const result = await resolver.findOne(mockAuthContext, '1');

      expect(result).toEqual(mockPage);
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scrapedPage',
        'read',
        mockPage.companyId,
      );
    });

    it('should throw ForbiddenException when not authorized to read page', async () => {
      mockScrapedPageService.findOne.mockResolvedValue(mockPage);
      mockAuthorizationFailure(mockAuthContext);

      await expect(resolver.findOne(mockAuthContext, '1')).rejects.toThrow(
        ForbiddenException,
      );
      expect(service.findOne).toHaveBeenCalledWith('1');
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'scrapedPage',
        'read',
        mockPage.companyId,
      );
    });
  });

  describe('metadata ResolveField', () => {
    it('should return metadata from the Page', () => {
      const mockMetadata = { customData: 'data1' };
      const page = { metadata: mockMetadata } as unknown as ScrapedPage;
      expect(resolver.metadata(page)).toEqual(mockMetadata);
    });
  });

  describe('recommendations ResolveField', () => {
    it('should return recommendations for a page', async () => {
      const mockRecommendations = [
        { id: '1', title: 'Recommendation 1' },
        { id: '2', title: 'Recommendation 2' },
      ];
      mockScrapedPageService.getRecommendationsForScrapedPage.mockResolvedValue(
        mockRecommendations,
      );

      const page = { id: '1' } as unknown as ScrapedPage;
      const result = await resolver.recommendations(page);

      expect(result).toEqual(mockRecommendations);
      expect(service.getRecommendationsForScrapedPage).toHaveBeenCalledWith(
        '1',
      );
    });

    it('should handle service error when getting recommendations', async () => {
      await testServiceError(
        page => resolver.recommendations(page),
        mockScrapedPageService,
        'getRecommendationsForScrapedPage',
        new Error('Recommendations service error'),
        { id: '1' } as unknown as ScrapedPage,
      )();
    });
  });
});
