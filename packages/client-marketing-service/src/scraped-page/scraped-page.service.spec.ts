import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

import { ScrapedPage } from './entities/scraped-page.entity';
import { ScrapedPageService } from './scraped-page.service';

describe('ScrapedPageService', () => {
  let service: ScrapedPageService;
  let scrapedPageRepository: any;
  let recommendationRepository: any;

  const mockScrapedPageRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    softDelete: jest.fn(),
  };

  const mockRecommendationRepository = {
    find: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScrapedPageService,
        {
          provide: getRepositoryToken(ScrapedPage),
          useValue: mockScrapedPageRepository,
        },
        {
          provide: getRepositoryToken(Recommendation),
          useValue: mockRecommendationRepository,
        },
      ],
    }).compile();

    service = module.get<ScrapedPageService>(ScrapedPageService);
    scrapedPageRepository = module.get(getRepositoryToken(ScrapedPage));
    recommendationRepository = module.get(getRepositoryToken(Recommendation));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new page', async () => {
      const createPageDto = {
        url: 'https://example.com',
        pageName: 'Test Page',
        pageType: 'HOMEPAGE',
        companyId: '123',
      };
      const savedPage = { id: '1', ...createPageDto };

      scrapedPageRepository.save.mockResolvedValue(savedPage);

      const result = await service.create(createPageDto as any);

      expect(scrapedPageRepository.save).toHaveBeenCalledWith(createPageDto);
      expect(result).toEqual(savedPage);
    });
  });

  describe('findAll', () => {
    it('should return all pages when no companyId provided', async () => {
      const pages = [
        { id: '1', url: 'https://example.com/1' },
        { id: '2', url: 'https://example.com/2' },
      ];

      scrapedPageRepository.find.mockResolvedValue(pages);

      const result = await service.findAll();

      expect(scrapedPageRepository.find).toHaveBeenCalledWith({ where: {} });
      expect(result).toEqual(pages);
    });

    it('should return pages filtered by companyId when provided', async () => {
      const companyId = '123';
      const pages = [{ id: '1', url: 'https://example.com/1', companyId }];

      scrapedPageRepository.find.mockResolvedValue(pages);

      const result = await service.findAll(companyId);

      expect(scrapedPageRepository.find).toHaveBeenCalledWith({
        where: { companyId },
      });
      expect(result).toEqual(pages);
    });
  });

  describe('findOne', () => {
    it('should return a page when found', async () => {
      const pageId = '1';
      const page = { id: pageId, url: 'https://example.com' };

      scrapedPageRepository.findOne.mockResolvedValue(page);

      const result = await service.findOne(pageId);

      expect(scrapedPageRepository.findOne).toHaveBeenCalledWith({
        where: { id: pageId },
        relations: ['pageKeywords', 'pageKeywords.keyword'],
      });
      expect(result).toEqual(page);
    });

    it('should throw NotFoundException when page not found', async () => {
      const pageId = '1';

      scrapedPageRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne(pageId)).rejects.toThrow(
        'ScrapedPage with ID "1" not found',
      );
      expect(scrapedPageRepository.findOne).toHaveBeenCalledWith({
        where: { id: pageId },
        relations: ['pageKeywords', 'pageKeywords.keyword'],
      });
    });
  });

  describe('update', () => {
    it('should update a page successfully', async () => {
      const pageId = '1';
      const existingPage = {
        id: pageId,
        url: 'https://example.com',
        pageName: 'Old Page Name',
        metadata: { existing: 'data' },
      };
      const updatePageDto = {
        pageName: 'New Page Name',
        metadata: { new: 'metadata' },
      };
      const updatedPage = {
        ...existingPage,
        pageName: updatePageDto.pageName,
        metadata: { existing: 'data', new: 'metadata' },
      };

      scrapedPageRepository.findOne.mockResolvedValue(existingPage);
      scrapedPageRepository.save.mockResolvedValue(updatedPage);

      const result = await service.update(pageId, updatePageDto);

      expect(scrapedPageRepository.findOne).toHaveBeenCalledWith({
        where: { id: pageId },
        relations: ['pageKeywords', 'pageKeywords.keyword'],
      });
      expect(scrapedPageRepository.save).toHaveBeenCalledWith({
        ...existingPage,
        pageName: updatePageDto.pageName,
        metadata: { existing: 'data', new: 'metadata' },
      });
      expect(result).toEqual(updatedPage);
    });

    it('should throw NotFoundException when page not found', async () => {
      const pageId = '1';
      const updatePageDto = { pageName: 'New Page Name' };

      scrapedPageRepository.findOne.mockResolvedValue(null);

      await expect(service.update(pageId, updatePageDto)).rejects.toThrow(
        'ScrapedPage with ID "1" not found',
      );
    });

    it('should update without metadata when metadata not provided', async () => {
      const pageId = '1';
      const existingPage = {
        id: pageId,
        url: 'https://example.com',
        pageName: 'Old Page Name',
        metadata: { existing: 'data' },
      };
      const updatePageDto = { pageName: 'New Page Name' };
      const updatedPage = { ...existingPage, pageName: updatePageDto.pageName };

      scrapedPageRepository.findOne.mockResolvedValue(existingPage);
      scrapedPageRepository.save.mockResolvedValue(updatedPage);

      const result = await service.update(pageId, updatePageDto);

      expect(scrapedPageRepository.save).toHaveBeenCalledWith({
        ...existingPage,
        pageName: updatePageDto.pageName,
      });
      expect(result).toEqual(updatedPage);
    });
  });

  describe('remove', () => {
    it('should soft delete a page successfully', async () => {
      const pageId = '1';
      const deleteResult = { affected: 1 };

      scrapedPageRepository.softDelete.mockResolvedValue(deleteResult);

      const result = await service.remove(pageId);

      expect(scrapedPageRepository.softDelete).toHaveBeenCalledWith({
        id: pageId,
      });
      expect(result).toEqual(deleteResult);
    });
  });

  describe('getRecommendationsForScrapedPage', () => {
    it('should return recommendations for a given page', async () => {
      const pageId = '1';
      const mockRecommendations = [
        {
          id: 'rec-1',
          type: 'META_TITLE',
          scrape: {
            scrapedPageId: pageId,
          },
          group: {
            id: 'group-1',
          },
        },
        {
          id: 'rec-2',
          type: 'META_DESCRIPTION',
          scrape: {
            scrapedPageId: pageId,
          },
          group: {
            id: 'group-2',
          },
        },
      ];

      recommendationRepository.find.mockResolvedValue(mockRecommendations);

      const result = await service.getRecommendationsForScrapedPage(pageId);

      expect(recommendationRepository.find).toHaveBeenCalledWith({
        where: {
          scrape: {
            scrapedPageId: pageId,
          },
        },
        relations: ['scrape', 'group'],
      });
      expect(result).toEqual(mockRecommendations);
    });

    it('should return empty array when no recommendations found', async () => {
      const pageId = '1';

      recommendationRepository.find.mockResolvedValue([]);

      const result = await service.getRecommendationsForScrapedPage(pageId);

      expect(recommendationRepository.find).toHaveBeenCalledWith({
        where: {
          scrape: {
            scrapedPageId: pageId,
          },
        },
        relations: ['scrape', 'group'],
      });
      expect(result).toEqual([]);
    });
  });
});
