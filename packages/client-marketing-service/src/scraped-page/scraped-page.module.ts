import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

import { ScrapedPage } from './entities/scraped-page.entity';
import { ScrapedPageController } from './scraped-page.controller';
import { ScrapedPageResolver } from './scraped-page.resolver';
import { ScrapedPageService } from './scraped-page.service';

@Module({
  imports: [TypeOrmModule.forFeature([ScrapedPage, Recommendation])],
  controllers: [ScrapedPageController],
  providers: [ScrapedPageService, ScrapedPageResolver],
  exports: [ScrapedPageService],
})
export class ScrapedPageModule {}
