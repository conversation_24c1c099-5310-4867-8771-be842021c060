import { NotFoundException } from '@nestjs/common';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { DeleteResult, Repository } from 'typeorm';

import { CreateScrapedPageDto } from './dto/create-scraped-page.dto';
import { UpdateScrapedPageDto } from './dto/update-scraped-page.dto';
import { ScrapedPage } from './entities/scraped-page.entity';

@Injectable()
export class ScrapedPageService {
  constructor(
    @InjectRepository(ScrapedPage)
    private readonly scrapedPageRepository: Repository<ScrapedPage>,
    @InjectRepository(Recommendation)
    private readonly recommendationRepository: Repository<Recommendation>,
  ) {}

  async create(createPageDto: CreateScrapedPageDto): Promise<ScrapedPage> {
    return this.scrapedPageRepository.save(createPageDto);
  }

  async findAll(companyId?: string): Promise<ScrapedPage[]> {
    const whereClause = companyId ? { companyId } : {};
    return this.scrapedPageRepository.find({
      where: whereClause,
    });
  }

  async findOne(id: string): Promise<ScrapedPage> {
    const scrapedPage = await this.scrapedPageRepository.findOne({
      where: { id },
      relations: ['pageKeywords', 'pageKeywords.keyword'],
    });

    if (!scrapedPage) {
      throw new NotFoundException(`ScrapedPage with ID "${id}" not found`);
    }

    return scrapedPage;
  }

  async update(
    id: string,
    updatePageDto: UpdateScrapedPageDto,
  ): Promise<ScrapedPage> {
    const scrapedPage = await this.findOne(id);

    const { metadata, ...otherProps } = updatePageDto;

    // Update non-metadata properties
    Object.assign(scrapedPage, otherProps);

    // Merge metadata if provided instead of completely replacing it
    if (metadata) {
      scrapedPage.metadata = {
        ...scrapedPage.metadata,
        ...metadata,
      };
    }

    return this.scrapedPageRepository.save(scrapedPage);
  }

  async remove(id: string): Promise<DeleteResult> {
    return await this.scrapedPageRepository.softDelete({ id });
  }

  async getRecommendationsForScrapedPage(
    scrapedPageId: string,
  ): Promise<Recommendation[]> {
    return this.recommendationRepository.find({
      where: {
        scrape: {
          scrapedPageId: scrapedPageId,
        },
      },
      relations: ['scrape', 'group'],
    });
  }
}
