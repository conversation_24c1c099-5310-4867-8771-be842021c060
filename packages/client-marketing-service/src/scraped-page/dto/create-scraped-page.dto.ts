import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
} from 'class-validator';

import { ScrapedPageType } from '../entities/scraped-page.entity';

export class CreateScrapedPageDto {
  @ApiProperty({
    description: 'The ID of the company this page belongs to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  companyId: string;

  @ApiProperty({
    description: 'The URL of the page',
    example: 'https://example.com/about',
  })
  @IsUrl({
    require_protocol: true,
    require_valid_protocol: true,
    protocols: ['http', 'https'],
  })
  @IsNotEmpty()
  @IsString()
  url: string;

  @ApiProperty({
    description: 'The name of the page',
    example: 'About Us',
  })
  @IsNotEmpty()
  @IsString()
  pageName: string;

  @ApiProperty({
    description: 'The type of the page',
    enum: ScrapedPageType,
    example: ScrapedPageType.HOMEPAGE,
  })
  @IsEnum(ScrapedPageType)
  @IsNotEmpty()
  pageType: ScrapedPageType;

  @ApiProperty({
    description: 'Additional metadata for the page',
    example: { lastScraped: '2025-05-19T10:00:00Z' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;
}
