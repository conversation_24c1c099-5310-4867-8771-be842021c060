import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import {
  Args,
  ID,
  Parent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { GraphQLJSONObject } from 'graphql-type-json';
import { AppPolicyRegistry } from 'src/auth.module';
import { Company } from 'src/company/entities/company.entity';
import { AuthContext } from 'src/graphql.decorator';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';

import { ScrapedPage } from './entities/scraped-page.entity';
import { ScrapedPageService } from './scraped-page.service';

@Resolver(() => ScrapedPage)
export class ScrapedPageResolver {
  constructor(private readonly scrapedPageService: ScrapedPageService) {}

  @Query(() => [ScrapedPage], {
    name: 'scrapedPages',
    description: 'Get all scraped pages',
  })
  async findAll(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID, nullable: true }) companyId?: string,
  ): Promise<ScrapedPage[]> {
    const canRead = await authContext.can('scrapedPage', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    return this.scrapedPageService.findAll(companyId);
  }

  @Query(() => ScrapedPage, {
    name: 'scrapedPage',
    description: 'Get a scraped page by ID',
  })
  async findOne(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('id', { type: () => ID }) id: string,
  ): Promise<ScrapedPage> {
    const scrapedPage = await this.scrapedPageService.findOne(id);

    const canRead = await authContext.can(
      'scrapedPage',
      'read',
      scrapedPage.companyId,
    );

    if (!canRead) throw new ForbiddenException();

    return scrapedPage;
  }

  @ResolveField(() => GraphQLJSONObject, { nullable: true })
  metadata(@Parent() scrapedPage: ScrapedPage) {
    return scrapedPage.metadata;
  }

  @ResolveField(() => [Recommendation], { nullable: true })
  recommendations(
    @Parent() scrapedPage: ScrapedPage,
  ): Promise<Recommendation[]> {
    return this.scrapedPageService.getRecommendationsForScrapedPage(
      scrapedPage.id,
    );
  }

  @ResolveField(() => Company, { nullable: true })
  company(@Parent() scrapedPage: ScrapedPage): Company | null {
    if (!scrapedPage.companyId) return null;

    // Return a Company reference with just the displayId
    // The gateway will resolve the full Company data from the tenant service
    return { displayId: scrapedPage.companyId } as Company;
  }
}
