import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';

import { AssetAggregator } from './asset-aggregator.service';
import { GraphQLClientService } from '../common/graphql/apollo-client.service';
import { PropertyCMSService } from '../property/property-cms.service';

describe('AssetAggregator', () => {
  let service: AssetAggregator;

  const mockHttpService: HttpService = {
    request: jest.fn().mockReturnValue(of({ data: { data: [] } })),
  } as unknown as HttpService;

  const mockConfigService: ConfigService = {
    get: jest.fn().mockReturnValue('https://some-api-url.com'),
  } as unknown as ConfigService;

  const mockPropertyCMSService = {
    getPropertyById: jest.fn(),
    searchProperties: jest.fn(),
  };

  const mockGraphQLClientService = {
    query: jest.fn(),
    mutate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AssetAggregator,
        { provide: HttpService, useValue: mockHttpService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: PropertyCMSService, useValue: mockPropertyCMSService },
        { provide: GraphQLClientService, useValue: mockGraphQLClientService },
      ],
    }).compile();

    service = module.get<AssetAggregator>(AssetAggregator);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
