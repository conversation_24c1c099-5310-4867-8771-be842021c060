import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { AssetAggregator } from './asset-aggregator.service';
import { GraphQLClientModule } from '../common/graphql/graphql.module';
import { PropertyModule } from '../property/property.module';

@Module({
  imports: [HttpModule, PropertyModule, GraphQLClientModule],
  providers: [AssetAggregator],
  exports: [AssetAggregator],
})
export class AssetAggregatorModule {}
