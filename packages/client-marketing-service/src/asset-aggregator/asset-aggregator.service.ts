import { HttpService } from '@nestjs/axios';
import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { TransformOptions } from './interfaces/bundle-builder-input.interface';
import { CmsPropertyResponse } from './interfaces/cms-property-response.interface';
import { mapPropertyToBundle } from './mappers/property-bundle.mapper';
import { CampaignBundleDto } from '../campaign-bundle/dto/campaign-bundle.dto';
import { GraphQLClientService } from '../common/graphql/apollo-client.service';
import { GET_PROPERTY_FOR_CAMPAIGN_BUNDLE } from '../common/graphql/queries/properties';
import { PropertyCMSService } from '../property/property-cms.service';

@Injectable()
export class AssetAggregator {
  private readonly logger = new Logger(AssetAggregator.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly propertyCMSService: PropertyCMSService,
    private readonly graphqlClient: GraphQLClientService,
  ) {}

  /**
   * Builds a CampaignBundle by fetching property data from CMS and transforming it
   * @param propertyId - The ID of the property to fetch and transform
   * @param options - Optional transformation options
   * @returns Promise<CampaignBundleDto> - The transformed campaign bundle
   * @throws NotFoundException if property is not found
   * @throws BadRequestException if required data is missing
   */
  async buildCampaignBundle(
    propertyId: string,
    options?: TransformOptions,
  ): Promise<CampaignBundleDto> {
    // Log the incoming request for debugging
    this.logger.debug(`Building campaign bundle for property: ${propertyId}`, {
      propertyId,
      hasOptions: !!options,
    });

    // Validate input
    if (!propertyId || propertyId.trim() === '') {
      throw new BadRequestException('Property ID is required');
    }

    try {
      // Fetch property data from CMS via GraphQL
      this.logger.debug(`Fetching property data from CMS for: ${propertyId}`);

      const response = await this.graphqlClient.request<{
        property: CmsPropertyResponse;
      }>(GET_PROPERTY_FOR_CAMPAIGN_BUNDLE, {
        id: propertyId,
        forCMS: true,
      });

      // Check if property exists
      if (!response?.property) {
        this.logger.warn(`Property not found in CMS for ID: ${propertyId}`);
        throw new NotFoundException(`Property with ID ${propertyId} not found`);
      }

      const propertyData = response.property;

      // Validate required fields
      this.validateRequiredFields(propertyData);

      // Log warnings for missing optional fields
      this.logMissingOptionalFields(propertyData);

      // Set default transformation options if not provided
      const transformOptions: TransformOptions = options || {
        parseAddress: true,
        prioritizeLeadAgent: true,
        mediaLimit: 50,
        includeOpenHouses: true,
      };

      // Transform the property data to CampaignBundle format
      this.logger.debug(
        `Transforming property data to CampaignBundle for: ${propertyId}`,
      );
      const campaignBundle = mapPropertyToBundle(
        propertyData,
        transformOptions,
      );

      this.logger.debug(
        `Successfully built campaign bundle for property: ${propertyId}`,
        {
          propertyId,
          hasMedia: campaignBundle.mediaUrls?.length > 0,
          hasAgent: !!campaignBundle.agent,
          hasBrokerage: !!campaignBundle.brokerage,
        },
      );

      return campaignBundle;
    } catch (error) {
      // Re-throw known exceptions
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      // Log and throw unexpected errors
      this.logger.error(
        `Error building campaign bundle for property: ${propertyId}`,
        {
          propertyId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        },
      );

      throw new BadRequestException(
        `Failed to build campaign bundle for property ${propertyId}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
      );
    }
  }

  /**
   * Validates that all required fields are present in the property data
   * @param propertyData - The property data from CMS
   * @throws BadRequestException if required fields are missing
   */
  private validateRequiredFields(propertyData: CmsPropertyResponse): void {
    const missingFields: string[] = [];

    if (!propertyData.propertyId) {
      missingFields.push('propertyId');
    }
    if (!propertyData.addressFull) {
      missingFields.push('addressFull');
    }

    if (missingFields.length > 0) {
      throw new BadRequestException(
        `Missing required fields for property transformation: ${missingFields.join(', ')}`,
      );
    }
  }

  /**
   * Logs warnings for missing optional fields that might impact the campaign
   * @param propertyData - The property data from CMS
   */
  private logMissingOptionalFields(propertyData: CmsPropertyResponse): void {
    const warnings: string[] = [];

    if (!propertyData.description) {
      warnings.push('description is missing');
    }
    if (!propertyData.media || propertyData.media.length === 0) {
      warnings.push('no media assets available');
    }
    if (!propertyData.agents || propertyData.agents.length === 0) {
      warnings.push('no agents assigned');
    }
    if (!propertyData.brokerage?.name) {
      warnings.push('brokerage information missing');
    }
    if (!propertyData.salesPrice && !propertyData.leasePrice) {
      warnings.push('no price information available');
    }
    if (!propertyData.dateListed) {
      warnings.push('date listed is missing');
    }

    if (warnings.length > 0) {
      this.logger.warn(
        `Property ${propertyData.propertyId} has missing optional fields`,
        { warnings },
      );
    }
  }
}
