import { CmsPropertyResponse } from './cms-property-response.interface';

/**
 * Options for media asset transformation
 */
export interface MediaTransformOptions {
  mediaLimit?: number;
}

/**
 * Options for agent data transformation
 */
export interface AgentTransformOptions {
  prioritizeLeadAgent?: boolean;
  defaultAgentInfo?: DefaultAgentInfo;
}

/**
 * Options for address parsing and formatting
 */
export interface AddressTransformOptions {
  parseAddress?: boolean;
  addressComponents?: AddressComponentOptions;
}

/**
 * Options for price formatting and display
 */
export interface PriceTransformOptions {
  priceFormat?: PriceFormatOptions;
}

/**
 * Options for open house data inclusion
 */
export interface OpenHouseTransformOptions {
  includeOpenHouses?: boolean;
}

/**
 * Default agent information for fallback when no agents are provided
 */
export interface DefaultAgentInfo {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
}

/**
 * Default brokerage information for fallback when no brokerage is provided
 */
export interface DefaultBrokerageInfo {
  name: string;
  website?: string;
  phoneNumber?: string;
}

/**
 * Options for parsing and formatting address components
 */
export interface AddressComponentOptions {
  includeUnit?: boolean;
  includeZipPlus4?: boolean;
}

/**
 * Options for formatting price display
 */
export interface PriceFormatOptions {
  currency?: string;
  locale?: string;
  showCents?: boolean;
  abbreviate?: boolean;
}

/**
 * Comprehensive transformation options for building the campaign bundle
 * Composed of domain-specific option interfaces for better separation of concerns
 */
export interface TransformOptions
  extends MediaTransformOptions,
    AgentTransformOptions,
    AddressTransformOptions,
    PriceTransformOptions,
    OpenHouseTransformOptions {
  defaultBrokerageInfo?: DefaultBrokerageInfo;
}

/**
 * Input interface for the bundle builder transformation
 * Combines property data with transformation options
 */
export interface BundleBuilderInput {
  propertyData: CmsPropertyResponse;
  transformOptions: TransformOptions;
}
