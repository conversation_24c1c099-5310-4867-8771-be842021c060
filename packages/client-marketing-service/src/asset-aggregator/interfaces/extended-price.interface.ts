/**
 * Extended price information interface for internal transformation
 * This extends the base PriceInfoDto with additional fields needed during transformation
 */
/**
 * Internal price transform input used by mappers.
 * Note: renamed to avoid collision with ExtendedPriceInfo in extended-dto.interface.ts.
 */
export interface PriceTransformInput {
  listPrice?: number | null;
  salesPrice?: number | null;
  /** @deprecated Use `salesPrice` */
  salePrice?: number | null; // deprecated alias
  rentPrice?: number | null; // For rental properties
  currency?: string | null;
  priceUponRequest?: boolean | null;
  displayPrice?: string; // Formatted price for display
  priceType?: 'SALE' | 'RENT' | 'UNKNOWN'; // Type of pricing
}
