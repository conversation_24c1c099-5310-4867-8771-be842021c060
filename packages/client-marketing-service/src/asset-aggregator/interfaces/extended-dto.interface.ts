/**
 * Extended interfaces that add additional fields to base DTOs for transformation
 */

import { AddressInfoDto } from '../../campaign-bundle/dto/address-info.dto';
import { PriceInfoDto } from '../../campaign-bundle/dto/price-info.dto';

/**
 * Extended address with additional fields for transformation
 */
export interface ExtendedAddressInfo extends AddressInfoDto {
  fullAddress?: string;
  streetAddress?: string;
  unit?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  zipPlus4?: string;
  neighborhood?: string;
  country?: string;
}

/**
 * Extended price information for transformation
 */
export interface ExtendedPriceInfo extends PriceInfoDto {
  salePrice?: number | null;
  rentPrice?: number | null;
  displayPrice?: string;
  priceType?: 'SALE' | 'RENT' | 'UNKNOWN';
}
