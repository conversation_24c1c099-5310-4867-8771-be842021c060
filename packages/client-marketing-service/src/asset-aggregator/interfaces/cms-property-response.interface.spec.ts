import { CmsPropertyResponse } from './cms-property-response.interface';

describe('CmsPropertyResponse Interface', () => {
  it('should accept valid CMS property data structure', () => {
    const validProperty: CmsPropertyResponse = {
      propertyId: 'prop-123',
      id: 'id-123',
      companyId: 'company-123',
      description: 'Beautiful 3BR home',
      bedroomCount: 3,
      bathCount: 2,
      dateListed: '2024-01-15',
      addressFull: '123 Main St, City, ST 12345',
      salesPrice: 500000,
      leasePrice: null,
      slug: 'beautiful-3br-home',
      status: {
        name: 'Active',
      },
      neighborhood: {
        name: 'Downtown',
      },
      brokerage: {
        name: 'Luxury Realty',
      },
      media: [
        { url: 'https://example.com/image1.jpg' },
        { url: 'https://example.com/image2.jpg' },
      ],
      agents: [
        { firstName: 'John', lastName: 'Doe', leadAgent: true },
        { firstName: 'Jane', lastName: 'Smith', leadAgent: false },
      ],
      openHouses: [
        {
          startTime: '2024-01-20T10:00:00Z',
          endTime: '2024-01-20T14:00:00Z',
        },
      ],
    };

    // Type checking happens at compile time
    expect(validProperty.propertyId).toBe('prop-123');
    expect(validProperty.media).toHaveLength(2);
    expect(validProperty.agents).toHaveLength(2);
  });

  it('should accept property without optional fields', () => {
    const minimalProperty: CmsPropertyResponse = {
      propertyId: 'prop-456',
      id: 'id-456',
      companyId: 'company-456',
      description: null,
      bedroomCount: null,
      bathCount: null,
      dateListed: null,
      addressFull: '456 Oak Ave',
      salesPrice: null,
      leasePrice: 2500,
      slug: 'oak-ave-rental',
      status: null,
      neighborhood: null,
      brokerage: null,
      media: [],
      agents: [],
      openHouses: [],
    };

    expect(minimalProperty.propertyId).toBe('prop-456');
    expect(minimalProperty.media).toEqual([]);
  });

  it('should handle properties with nested structures', () => {
    const propertyWithNestedData: CmsPropertyResponse = {
      propertyId: 'prop-789',
      id: 'id-789',
      companyId: 'company-789',
      description: 'Luxury estate',
      bedroomCount: 5,
      bathCount: 4,
      dateListed: '2024-02-01',
      addressFull: '789 Luxury Lane',
      salesPrice: 1200000,
      leasePrice: null,
      slug: 'luxury-estate',
      status: {
        name: 'Pending',
      },
      neighborhood: {
        name: 'Uptown Heights',
      },
      brokerage: {
        name: 'Premium Properties',
      },
      media: [
        { url: 'https://example.com/luxury1.jpg' },
        { url: 'https://example.com/luxury2.jpg' },
        { url: 'https://example.com/luxury3.jpg' },
      ],
      agents: [{ firstName: 'Agent', lastName: 'One', leadAgent: true }],
      openHouses: [
        {
          startTime: '2024-02-10T12:00:00Z',
          endTime: '2024-02-10T16:00:00Z',
        },
        {
          startTime: '2024-02-11T12:00:00Z',
          endTime: '2024-02-11T16:00:00Z',
        },
      ],
    };

    expect(propertyWithNestedData.status?.name).toBe('Pending');
    expect(propertyWithNestedData.openHouses).toHaveLength(2);
  });

  it('should support GraphQL null values', () => {
    const propertyWithNulls: CmsPropertyResponse = {
      propertyId: 'prop-null',
      id: 'id-null',
      companyId: 'company-null',
      description: null,
      bedroomCount: null,
      bathCount: null,
      dateListed: null,
      addressFull: '999 Null Street',
      salesPrice: null,
      leasePrice: null,
      slug: 'null-property',
      status: null,
      neighborhood: null,
      brokerage: null,
      media: [],
      agents: [],
      openHouses: [],
    };

    expect(propertyWithNulls.description).toBeNull();
    expect(propertyWithNulls.salesPrice).toBeNull();
    expect(propertyWithNulls.status).toBeNull();
  });
});
