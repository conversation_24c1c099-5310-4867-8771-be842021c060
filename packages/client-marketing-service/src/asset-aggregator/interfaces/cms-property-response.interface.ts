/**
 * Interface representing the GraphQL response structure for CMS property data
 * Based on the CMS_PROPERTY_FIELDS fragment
 */
export interface CmsPropertyResponse {
  propertyId: string;
  id: string;
  companyId: string;
  description: string | null;
  bedroomCount: number | null;
  bathCount: number | null;
  dateListed: string | null;
  addressFull: string;
  salesPrice: number | null;
  leasePrice: number | null;
  slug: string;
  status: {
    name: string;
  } | null;
  neighborhood: {
    name: string;
  } | null;
  brokerage: {
    name: string;
  } | null;
  media: Array<{
    url: string;
  }>;
  agents: Array<{
    firstName: string;
    lastName: string;
    leadAgent: boolean;
  }>;
  openHouses: Array<{
    startTime: string;
    endTime: string;
  }>;
}
