import { BundleBuilderInput } from './bundle-builder-input.interface';

describe('BundleBuilderInput Interface', () => {
  it('should accept valid bundle builder input with all fields', () => {
    const validInput: BundleBuilderInput = {
      propertyData: {
        propertyId: 'prop-123',
        id: 'id-123',
        companyId: 'company-123',
        description: 'Beautiful 3BR home',
        bedroomCount: 3,
        bathCount: 2,
        dateListed: '2024-01-15',
        addressFull: '123 Main St, City, ST 12345',
        salesPrice: 500000,
        leasePrice: null,
        slug: 'beautiful-3br-home',
        status: {
          name: 'Active',
        },
        neighborhood: {
          name: 'Downtown',
        },
        brokerage: {
          name: 'Luxury Realty',
        },
        media: [
          { url: 'https://example.com/image1.jpg' },
          { url: 'https://example.com/image2.jpg' },
        ],
        agents: [
          { firstName: 'John', lastName: 'Doe', leadAgent: true },
          { firstName: 'Jane', lastName: 'Smith', leadAgent: false },
        ],
        openHouses: [
          {
            startTime: '2024-01-20T10:00:00Z',
            endTime: '2024-01-20T14:00:00Z',
          },
        ],
      },
      transformOptions: {
        includeOpenHouses: true,
        mediaLimit: 10,
        prioritizeLeadAgent: true,
      },
    };

    expect(validInput.propertyData.propertyId).toBe('prop-123');
    expect(validInput.transformOptions.includeOpenHouses).toBe(true);
    expect(validInput.transformOptions.mediaLimit).toBe(10);
  });

  it('should accept input with minimal transform options', () => {
    const minimalInput: BundleBuilderInput = {
      propertyData: {
        propertyId: 'prop-456',
        id: 'id-456',
        companyId: 'company-456',
        description: null,
        bedroomCount: null,
        bathCount: null,
        dateListed: null,
        addressFull: '456 Oak Ave',
        salesPrice: null,
        leasePrice: 2500,
        slug: 'oak-ave-rental',
        status: null,
        neighborhood: null,
        brokerage: null,
        media: [],
        agents: [],
        openHouses: [],
      },
      transformOptions: {},
    };

    expect(minimalInput.propertyData.propertyId).toBe('prop-456');
    expect(minimalInput.transformOptions).toEqual({});
  });

  it('should support optional transform options', () => {
    const inputWithOptions: BundleBuilderInput = {
      propertyData: {
        propertyId: 'prop-789',
        id: 'id-789',
        companyId: 'company-789',
        description: 'Luxury estate',
        bedroomCount: 5,
        bathCount: 4,
        dateListed: '2024-02-01',
        addressFull: '789 Luxury Lane',
        salesPrice: 1200000,
        leasePrice: null,
        slug: 'luxury-estate',
        status: {
          name: 'Pending',
        },
        neighborhood: {
          name: 'Uptown Heights',
        },
        brokerage: {
          name: 'Premium Properties',
        },
        media: [
          { url: 'https://example.com/luxury1.jpg' },
          { url: 'https://example.com/luxury2.jpg' },
        ],
        agents: [{ firstName: 'Agent', lastName: 'One', leadAgent: true }],
        openHouses: [],
      },
      transformOptions: {
        includeOpenHouses: false,
        mediaLimit: 5,
        prioritizeLeadAgent: false,
        defaultAgentInfo: {
          firstName: 'Default',
          lastName: 'Agent',
          email: '<EMAIL>',
        },
        defaultBrokerageInfo: {
          name: 'Default Brokerage',
          website: 'https://brokerage.com',
        },
      },
    };

    expect(inputWithOptions.transformOptions.includeOpenHouses).toBe(false);
    expect(inputWithOptions.transformOptions.mediaLimit).toBe(5);
    expect(inputWithOptions.transformOptions.defaultAgentInfo?.firstName).toBe(
      'Default',
    );
    expect(
      inputWithOptions.transformOptions.defaultBrokerageInfo?.website,
    ).toBe('https://brokerage.com');
  });

  it('should handle address parsing options', () => {
    const inputWithAddressOptions: BundleBuilderInput = {
      propertyData: {
        propertyId: 'prop-addr',
        id: 'id-addr',
        companyId: 'company-addr',
        description: 'Test property',
        bedroomCount: 2,
        bathCount: 1,
        dateListed: '2024-03-01',
        addressFull: '100 Test St, Suite 200, TestCity, TS 12345',
        salesPrice: 300000,
        leasePrice: null,
        slug: 'test-property',
        status: { name: 'Active' },
        neighborhood: null,
        brokerage: null,
        media: [],
        agents: [],
        openHouses: [],
      },
      transformOptions: {
        parseAddress: true,
        addressComponents: {
          includeUnit: true,
          includeZipPlus4: false,
        },
      },
    };

    expect(inputWithAddressOptions.transformOptions.parseAddress).toBe(true);
    expect(
      inputWithAddressOptions.transformOptions.addressComponents?.includeUnit,
    ).toBe(true);
    expect(
      inputWithAddressOptions.transformOptions.addressComponents
        ?.includeZipPlus4,
    ).toBe(false);
  });

  it('should support price formatting options', () => {
    const inputWithPriceOptions: BundleBuilderInput = {
      propertyData: {
        propertyId: 'prop-price',
        id: 'id-price',
        companyId: 'company-price',
        description: 'Price test property',
        bedroomCount: 3,
        bathCount: 2,
        dateListed: '2024-03-15',
        addressFull: '200 Price Lane',
        salesPrice: 1500000,
        leasePrice: null,
        slug: 'price-test',
        status: { name: 'Active' },
        neighborhood: null,
        brokerage: null,
        media: [],
        agents: [],
        openHouses: [],
      },
      transformOptions: {
        priceFormat: {
          currency: 'USD',
          locale: 'en-US',
          showCents: false,
          abbreviate: true,
        },
      },
    };

    expect(inputWithPriceOptions.transformOptions.priceFormat?.currency).toBe(
      'USD',
    );
    expect(inputWithPriceOptions.transformOptions.priceFormat?.abbreviate).toBe(
      true,
    );
  });
});
