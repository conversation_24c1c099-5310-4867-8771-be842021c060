import {
  generateUniqueId,
  generateAgentId,
  generateBrokerageId,
} from './id-generator.util';

describe('IdGeneratorUtil', () => {
  describe('generateUniqueId', () => {
    it('should generate ID with correct prefix', () => {
      const prefix = 'test';
      const result = generateUniqueId(prefix);

      expect(result).toMatch(/^test-[a-z0-9]{9}$/);
    });

    it('should generate unique IDs on multiple calls', () => {
      const prefix = 'test';
      const id1 = generateUniqueId(prefix);
      const id2 = generateUniqueId(prefix);

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^test-[a-z0-9]{9}$/);
      expect(id2).toMatch(/^test-[a-z0-9]{9}$/);
    });

    it('should handle different prefixes', () => {
      const id1 = generateUniqueId('prefix1');
      const id2 = generateUniqueId('prefix2');

      expect(id1).toMatch(/^prefix1-[a-z0-9]{9}$/);
      expect(id2).toMatch(/^prefix2-[a-z0-9]{9}$/);
    });

    it('should handle empty prefix', () => {
      const result = generateUniqueId('');

      expect(result).toMatch(/^-[a-z0-9]{9}$/);
    });
  });

  describe('generateAgentId', () => {
    it('should generate agent ID with correct format', () => {
      const result = generateAgentId();

      expect(result).toMatch(/^agent-[a-z0-9]{9}$/);
    });

    it('should generate unique agent IDs', () => {
      const id1 = generateAgentId();
      const id2 = generateAgentId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^agent-[a-z0-9]{9}$/);
      expect(id2).toMatch(/^agent-[a-z0-9]{9}$/);
    });
  });

  describe('generateBrokerageId', () => {
    it('should generate brokerage ID with correct format', () => {
      const result = generateBrokerageId();

      expect(result).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });

    it('should generate unique brokerage IDs', () => {
      const id1 = generateBrokerageId();
      const id2 = generateBrokerageId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^brokerage-[a-z0-9]{9}$/);
      expect(id2).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });
  });
});
