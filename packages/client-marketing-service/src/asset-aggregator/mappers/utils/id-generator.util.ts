/**
 * Generate a unique ID with a given prefix
 */
export const generateUniqueId = (prefix: string): string => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Generate a unique agent ID
 */
export const generateAgentId = (): string => {
  return generateUniqueId('agent');
};

/**
 * Generate a unique brokerage ID
 */
export const generateBrokerageId = (): string => {
  return generateUniqueId('brokerage');
};
