import { plainToInstance } from 'class-transformer';

import { AgentInfoDto } from '../../campaign-bundle/dto/agent-info.dto';
import { AgentTransformOptions } from '../interfaces/bundle-builder-input.interface';
import { generateAgentId } from './utils/id-generator.util';

/**
 * Create an AgentInfoDto from agent data
 */
export const createAgentInfo = (data: {
  id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}): AgentInfoDto => {
  const agentData = {
    agentId: data.id || generateAgentId(),
    firstName: data.firstName,
    lastName: data.lastName,
    email: data.email,
    phoneNumber: data.phone,
  };

  return plainToInstance(AgentInfoDto, agentData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};

/**
 * Check if an agent has valid name data
 */
const isValidAgent = (agent: any): boolean => {
  return !!(agent?.firstName && agent?.lastName);
};

/**
 * Select the primary agent from a list of agents
 * Applies business logic for agent selection based on options
 */
export const selectPrimaryAgent = (
  agents:
    | Array<{ firstName: string; lastName: string; leadAgent?: boolean }>
    | null
    | undefined,
  options: AgentTransformOptions,
): AgentInfoDto => {
  // Handle null/undefined agents
  if (!agents || !Array.isArray(agents)) {
    agents = [];
  }

  // Filter out invalid agents
  const validAgents = agents.filter(isValidAgent);

  // Prefer lead agent when requested or by default
  const preferLead =
    options.prioritizeLeadAgent === undefined
      ? true
      : !!options.prioritizeLeadAgent;
  if (preferLead) {
    const lead = validAgents.find(a => (a as any).leadAgent === true);
    if (lead) {
      return createAgentInfo({
        firstName: lead.firstName,
        lastName: lead.lastName,
      });
    }
  }

  // If we have valid agents, select based on priority
  if (validAgents.length > 0) {
    // For now, always select the first agent
    // prioritizeLeadAgent option could be used for more complex logic in the future
    const selectedAgent = validAgents[0];

    return createAgentInfo({
      firstName: selectedAgent.firstName,
      lastName: selectedAgent.lastName,
    });
  }

  // No valid agents found, use default if provided
  if (options.defaultAgentInfo) {
    return createAgentInfo({
      firstName: options.defaultAgentInfo.firstName,
      lastName: options.defaultAgentInfo.lastName,
      email: options.defaultAgentInfo.email,
      phone: options.defaultAgentInfo.phone,
    });
  }

  // No agents and no default, create placeholder
  return createAgentInfo({
    firstName: 'Agent',
    lastName: 'Unavailable',
  });
};
