import { plainToInstance } from 'class-transformer';

import { MediaAssetDto } from '../../campaign-bundle/dto/media-asset.dto';
import { MediaTransformOptions } from '../interfaces/bundle-builder-input.interface';

/**
 * Video file extensions for resource type detection
 */
const VIDEO_EXTENSIONS = [
  'mp4',
  'avi',
  'mov',
  'wmv',
  'flv',
  'webm',
  'mkv',
  'm4v',
];

/**
 * Extract file extension from URL
 */
const getFileExtension = (url: string): string | undefined => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const match = pathname.match(/\.([a-zA-Z0-9]+)(?:\?|$)/);
    return match ? match[1].toLowerCase() : undefined;
  } catch {
    // Fallback for non-URL strings
    const match = url.match(/\.([a-zA-Z0-9]+)(?:\?|$)/);
    return match ? match[1].toLowerCase() : undefined;
  }
};

/**
 * Extract filename from URL
 */
const getFileName = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const segments = pathname.split('/');
    return segments[segments.length - 1] || 'media';
  } catch {
    // Fallback for non-URL strings
    const segments = url.split('/');
    return segments[segments.length - 1] || 'media';
  }
};

/**
 * Determine resource type based on file extension
 */
const getResourceType = (url: string): string => {
  const extension = getFileExtension(url);
  if (extension && VIDEO_EXTENSIONS.includes(extension)) {
    return 'video';
  }
  return 'image';
};

/**
 * Generate responsive image URLs
 * In a real implementation, this would use a CDN service like Cloudinary
 */
const generateResponsiveUrls = (url: string, resourceType: string) => {
  if (resourceType !== 'image') {
    return {};
  }

  // For now, just return the same URL
  // In production, this would generate actual responsive URLs
  return {
    thumbnailUrl: url,
    smallUrl: url,
    mediumUrl: url,
    largeUrl: url,
    xLargeUrl: url,
    xxLargeUrl: url,
  };
};

/**
 * Create a single media asset DTO from a URL
 */
export const createMediaAsset = (url: string, index: number): MediaAssetDto => {
  const fileName = getFileName(url);
  const extension = getFileExtension(url);
  const resourceType = getResourceType(url);
  const responsiveUrls = generateResponsiveUrls(url, resourceType);
  const now = new Date();
  const displayIndex = index + 1;

  const mediaData = {
    id: `media-${index}`,
    resourceType,
    originalFileName: fileName,
    originalUrl: url,
    format: extension,
    alt: `Property ${resourceType} ${displayIndex}`,
    altTagText: `Property ${resourceType} ${displayIndex}`,
    displayName: `Property ${resourceType === 'image' ? 'Photo' : 'Video'} ${displayIndex}`,
    shared: false,
    curated: false,
    canEdit: false,
    createdAt: now,
    updatedAt: now,
    ...responsiveUrls,
  };

  return plainToInstance(MediaAssetDto, mediaData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};

/**
 * Map media array from CMS to MediaAssetDto array
 */
export const mapMediaAssets = (
  media: Array<{ url: string }> | null | undefined,
  options: MediaTransformOptions,
): MediaAssetDto[] => {
  if (!media || !Array.isArray(media)) {
    return [];
  }

  // Filter out empty URLs
  const validMedia = media.filter(item => item?.url);

  // Apply media limit if specified
  const limitedMedia = options.mediaLimit
    ? validMedia.slice(0, options.mediaLimit)
    : validMedia;

  return limitedMedia.map((item, index) => createMediaAsset(item.url, index));
};
