import { determineOpenHouseStatus } from './open-house.mapper';
import { OpenHouseStatus } from '../../campaign-bundle/enums/open-house-status.enum';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';

describe('OpenHouseMapper', () => {
  const mockOptions: TransformOptions = {
    includeOpenHouses: true,
    mediaLimit: 10,
    priceFormat: { currency: 'USD', locale: 'en-US' },
  };

  beforeEach(() => {
    // Mock current date to 2024-06-15 12:00:00 UTC
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-06-15T12:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('determineOpenHouseStatus', () => {
    it('should return CANCELLED when includeOpenHouses is false', () => {
      const openHouses = [
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' },
      ];
      const optionsDisabled = { ...mockOptions, includeOpenHouses: false };

      const result = determineOpenHouseStatus(openHouses, optionsDisabled);

      expect(result).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should return CANCELLED when no open houses provided', () => {
      const result = determineOpenHouseStatus(null, mockOptions);
      expect(result).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should return CANCELLED when empty array provided', () => {
      const result = determineOpenHouseStatus([], mockOptions);
      expect(result).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should return ACTIVE when open house is currently active', () => {
      const openHouses = [
        { startTime: '2024-06-15T11:00:00Z', endTime: '2024-06-15T13:00:00Z' }, // Active now
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.ACTIVE);
    });

    it('should return SCHEDULED when open house is in the future', () => {
      const openHouses = [
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' }, // Future
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.SCHEDULED);
    });

    it('should return COMPLETED when all open houses are past', () => {
      const openHouses = [
        { startTime: '2024-06-14T10:00:00Z', endTime: '2024-06-14T12:00:00Z' }, // Past
        { startTime: '2024-06-13T10:00:00Z', endTime: '2024-06-13T12:00:00Z' }, // Past
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.COMPLETED);
    });

    it('should prioritize ACTIVE over other statuses', () => {
      const openHouses = [
        { startTime: '2024-06-14T10:00:00Z', endTime: '2024-06-14T12:00:00Z' }, // Past
        { startTime: '2024-06-15T11:00:00Z', endTime: '2024-06-15T13:00:00Z' }, // Active now
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' }, // Future
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.ACTIVE);
    });

    it('should return SCHEDULED when has future but no active open houses', () => {
      const openHouses = [
        { startTime: '2024-06-14T10:00:00Z', endTime: '2024-06-14T12:00:00Z' }, // Past
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' }, // Future
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.SCHEDULED);
    });

    it('should handle invalid date strings gracefully', () => {
      const openHouses = [
        { startTime: 'invalid-date', endTime: 'invalid-date' },
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' }, // Valid future
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.SCHEDULED);
    });

    it('should handle missing startTime or endTime', () => {
      const openHouses = [
        { startTime: '', endTime: '2024-06-16T12:00:00Z' },
        { startTime: '2024-06-16T10:00:00Z', endTime: '' },
        { startTime: '2024-06-16T10:00:00Z', endTime: '2024-06-16T12:00:00Z' }, // Valid
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.SCHEDULED);
    });

    it('should return CANCELLED when all open houses have invalid data', () => {
      const openHouses = [
        { startTime: '', endTime: '' },
        { startTime: 'invalid', endTime: 'invalid' },
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should handle edge case of exact boundary times', () => {
      // Mock current time to exactly match start time
      jest.setSystemTime(new Date('2024-06-15T10:00:00Z'));

      const openHouses = [
        { startTime: '2024-06-15T10:00:00Z', endTime: '2024-06-15T12:00:00Z' },
      ];

      const result = determineOpenHouseStatus(openHouses, mockOptions);

      expect(result).toBe(OpenHouseStatus.ACTIVE);
    });
  });
});
