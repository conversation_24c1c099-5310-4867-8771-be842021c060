import { selectPrimaryAgent, createAgentInfo } from './agent.mapper';
import { AgentInfoDto } from '../../campaign-bundle/dto/agent-info.dto';
import { AgentTransformOptions } from '../interfaces/bundle-builder-input.interface';

describe('AgentMapper', () => {
  describe('createAgentInfo', () => {
    it('should create agent info from complete data', () => {
      const result = createAgentInfo({
        id: 'agent-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '555-1234',
      });

      expect(result).toBeInstanceOf(AgentInfoDto);
      expect(result.agentId).toBe('agent-123');
      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
      expect(result.email).toBe('<EMAIL>');
      expect(result.phoneNumber).toBe('555-1234');
    });

    it('should handle partial data', () => {
      const result = createAgentInfo({
        firstName: 'Jane',
        lastName: 'Smith',
      });

      expect(result.agentId).toMatch(/^agent-/);
      expect(result.firstName).toBe('Jane');
      expect(result.lastName).toBe('Smith');
      expect(result.email).toBeUndefined();
      expect(result.phoneNumber).toBeUndefined();
    });

    it('should generate ID when not provided', () => {
      const result = createAgentInfo({
        firstName: 'Test',
        lastName: 'Agent',
      });

      expect(result.agentId).toBeDefined();
      expect(result.agentId).toMatch(/^agent-[a-z0-9]+$/);
    });
  });

  describe('selectPrimaryAgent', () => {
    const mockAgents = [
      { firstName: 'John', lastName: 'Doe' },
      { firstName: 'Jane', lastName: 'Smith' },
      { firstName: 'Bob', lastName: 'Johnson' },
    ];

    it('should select first agent when prioritizeLeadAgent is true', () => {
      const options: AgentTransformOptions = {
        prioritizeLeadAgent: true,
      };

      const result = selectPrimaryAgent(mockAgents, options);

      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
    });

    it('should select first agent when prioritizeLeadAgent is false (default)', () => {
      const options: AgentTransformOptions = {
        prioritizeLeadAgent: false,
      };

      const result = selectPrimaryAgent(mockAgents, options);

      expect(result.firstName).toBe('John');
      expect(result.lastName).toBe('Doe');
    });

    it('should use default agent when no agents provided', () => {
      const options: AgentTransformOptions = {
        defaultAgentInfo: {
          firstName: 'Default',
          lastName: 'Agent',
          email: '<EMAIL>',
          phone: '555-0000',
        },
      };

      const result = selectPrimaryAgent([], options);

      expect(result.firstName).toBe('Default');
      expect(result.lastName).toBe('Agent');
      expect(result.email).toBe('<EMAIL>');
      expect(result.phoneNumber).toBe('555-0000');
    });

    it('should create placeholder agent when no agents and no default', () => {
      const options: AgentTransformOptions = {};

      const result = selectPrimaryAgent([], options);

      expect(result.firstName).toBe('Agent');
      expect(result.lastName).toBe('Unavailable');
      expect(result.email).toBeUndefined();
    });

    it('should handle null agents array', () => {
      const options: AgentTransformOptions = {
        defaultAgentInfo: {
          firstName: 'Fallback',
          lastName: 'Agent',
        },
      };

      const result = selectPrimaryAgent(null as any, options);

      expect(result.firstName).toBe('Fallback');
      expect(result.lastName).toBe('Agent');
    });

    it('should handle agents with missing names', () => {
      const incompleteAgents = [
        { firstName: null as any, lastName: 'Smith' },
        { firstName: 'John', lastName: null as any },
        { firstName: 'Bob', lastName: 'Johnson' },
      ];

      const options: AgentTransformOptions = {
        prioritizeLeadAgent: true,
      };

      const result = selectPrimaryAgent(incompleteAgents, options);

      // Should skip incomplete agents and select the first complete one
      expect(result.firstName).toBe('Bob');
      expect(result.lastName).toBe('Johnson');
    });

    it('should handle all incomplete agents', () => {
      const incompleteAgents = [
        { firstName: null as any, lastName: 'Smith' },
        { firstName: 'John', lastName: null as any },
      ];

      const options: AgentTransformOptions = {
        defaultAgentInfo: {
          firstName: 'Backup',
          lastName: 'Agent',
        },
      };

      const result = selectPrimaryAgent(incompleteAgents, options);

      // Should fall back to default
      expect(result.firstName).toBe('Backup');
      expect(result.lastName).toBe('Agent');
    });
  });
});
