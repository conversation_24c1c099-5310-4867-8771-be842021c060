import { OpenHouseStatus } from '../../campaign-bundle/enums/open-house-status.enum';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';

/**
 * Open house data from CMS
 */
interface OpenHouseData {
  startTime: string;
  endTime: string;
}

/**
 * Check if a date is in the past
 */
const isPastDate = (date: Date): boolean => {
  return date < new Date();
};

/**
 * Check if a date is currently active (between start and end)
 */
const isActiveDate = (startDate: Date, endDate: Date): boolean => {
  const now = new Date();
  return now >= startDate && now <= endDate;
};

/**
 * Determine the open house status based on open house data
 */
export const determineOpenHouseStatus = (
  openHouses: OpenHouseData[] | null | undefined,
  options: TransformOptions,
): OpenHouseStatus => {
  // If open houses are disabled in options, return cancelled
  if (options.includeOpenHouses === false) {
    return OpenHouseStatus.CANCELLED;
  }

  // No open houses data
  if (!openHouses || !Array.isArray(openHouses) || openHouses.length === 0) {
    return OpenHouseStatus.CANCELLED;
  }

  // Check each open house for status
  let hasActiveOpenHouse = false;
  let hasFutureOpenHouse = false;
  let hasValidOpenHouse = false;

  for (const openHouse of openHouses) {
    if (!openHouse.startTime || !openHouse.endTime) {
      continue;
    }

    const startDate = new Date(openHouse.startTime);
    const endDate = new Date(openHouse.endTime);

    // Check if dates are valid
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      continue;
    }

    hasValidOpenHouse = true;

    // Check status
    if (isActiveDate(startDate, endDate)) {
      hasActiveOpenHouse = true;
    } else if (!isPastDate(startDate)) {
      hasFutureOpenHouse = true;
    }
  }

  // Determine final status
  if (hasActiveOpenHouse) {
    return OpenHouseStatus.ACTIVE;
  }

  if (hasFutureOpenHouse) {
    return OpenHouseStatus.SCHEDULED;
  }

  if (hasValidOpenHouse) {
    // All valid open houses are in the past
    return OpenHouseStatus.COMPLETED;
  }

  // No valid open houses found
  return OpenHouseStatus.CANCELLED;
};
