import { plainToInstance } from 'class-transformer';

import { AddressInfoDto } from '../../campaign-bundle/dto/address-info.dto';

/**
 * Address components parsed from full address string
 */
interface ParsedAddressComponents {
  streetAddress?: string;
  unit?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  zipPlus4?: string;
}

/**
 * Regular expressions for address parsing
 */
const ADDRESS_PATTERNS = {
  // Match apartment/unit patterns
  unit: /(?:apt|apartment|unit|suite|ste|#)\s*([A-Z0-9]+)/i,
  // Match zip+4 pattern
  zipPlus4: /(\d{5})-(\d{4})/,
  // Match standard US address format
  usAddress: /^([^,]+),\s*([^,]+),\s*([A-Z]{2})\s*(\d{5}(?:-\d{4})?)?$/i,
  // Match state abbreviation
  stateAbbr: /\b([A-Z]{2})\b/,
};

/**
 * Extract unit/apartment number from street address
 */
const extractUnit = (streetAddress: string): string | undefined => {
  const match = streetAddress.match(ADDRESS_PATTERNS.unit);
  return match ? match[1] : undefined;
};

/**
 * Extract zip code components
 */
const extractZipComponents = (
  zipString: string,
): { zipCode?: string; zipPlus4?: string } => {
  if (!zipString) {
    return {};
  }

  const zipPlus4Match = zipString.match(ADDRESS_PATTERNS.zipPlus4);
  if (zipPlus4Match) {
    return {
      zipCode: zipPlus4Match[1],
      zipPlus4: zipPlus4Match[2],
    };
  }

  // Just a regular 5-digit zip
  const zipMatch = zipString.match(/(\d{5})/);
  return zipMatch ? { zipCode: zipMatch[1] } : {};
};

/**
 * Parse address components from a full address string
 * Attempts to extract street, city, state, and zip
 */
export const extractAddressComponents = (
  fullAddress: string,
): ParsedAddressComponents => {
  if (!fullAddress) {
    return {};
  }

  // Try to match standard US address format
  const usMatch = fullAddress.match(ADDRESS_PATTERNS.usAddress);
  if (usMatch) {
    const [, street, city, state, zipString] = usMatch;
    const { zipCode, zipPlus4 } = extractZipComponents(zipString);

    return {
      streetAddress: street.trim(),
      unit: extractUnit(street),
      city: city.trim(),
      state: state.trim(),
      zipCode,
      zipPlus4,
    };
  }

  // Fallback: Try to parse with less strict rules
  const parts = fullAddress.split(',').map(part => part.trim());

  if (parts.length >= 2) {
    const streetAddress = parts[0];
    const lastPart = parts[parts.length - 1];

    // Look for state and zip in the last part
    const stateZipMatch = lastPart.match(/([A-Z]{2})\s*(\d{5}(?:-\d{4})?)?/i);

    if (stateZipMatch) {
      const [, state, zipString] = stateZipMatch;
      const { zipCode, zipPlus4 } = extractZipComponents(zipString);

      // City is everything between street and state/zip
      const city = parts.length > 2 ? parts.slice(1, -1).join(', ') : undefined;

      return {
        streetAddress,
        unit: extractUnit(streetAddress),
        city,
        state,
        zipCode,
        zipPlus4,
      };
    }
  }

  // Last resort: just return what we can
  return {
    streetAddress: parts[0] || fullAddress,
    unit: extractUnit(parts[0] || fullAddress),
  };
};

/**
 * Parse a full address string into components and create AddressInfoDto
 */
export const parseAddress = (
  fullAddress: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  neighborhood?: string | null,
): AddressInfoDto => {
  const components = extractAddressComponents(fullAddress || '');

  // Create address data matching the AddressInfoDto structure
  const addressData = {
    addressFull: fullAddress || '',
    addressLine1: components.streetAddress,
    addressLine2: components.unit ? `Unit ${components.unit}` : undefined,
    addressCity: components.city,
    addressState: components.state,
    addressCountry: 'USA',
    postalCode: components.zipCode,
  };

  return plainToInstance(AddressInfoDto, addressData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};

/**
 * Format address without parsing (simpler approach)
 */
export const formatAddress = (
  fullAddress: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  neighborhood?: string | null,
): AddressInfoDto => {
  const addressData = {
    addressFull: fullAddress || '',
    addressCountry: 'USA',
  };

  return plainToInstance(AddressInfoDto, addressData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};
