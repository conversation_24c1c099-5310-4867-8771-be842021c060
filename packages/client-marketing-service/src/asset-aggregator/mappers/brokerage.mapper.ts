import { plainToInstance } from 'class-transformer';

import { BrokerageInfoDto } from '../../campaign-bundle/dto/brokerage-info.dto';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';
import { generateBrokerageId } from './utils/id-generator.util';

/**
 * Determine brokerage information from CMS data or use defaults
 */
export const determineBrokerage = (
  brokerage: { name: string } | null | undefined,
  options: TransformOptions,
): BrokerageInfoDto => {
  let brokerageData;

  if (brokerage?.name) {
    // Use provided brokerage
    brokerageData = {
      brokerageId: generateBrokerageId(),
      name: brokerage.name,
    };
  } else if (options.defaultBrokerageInfo) {
    // Use default brokerage
    brokerageData = {
      brokerageId: generateBrokerageId(),
      name: options.defaultBrokerageInfo.name,
      website: options.defaultBrokerageInfo.website,
      phoneNumber: options.defaultBrokerageInfo.phoneNumber,
    };
  } else {
    // Fallback brokerage
    brokerageData = {
      brokerageId: generateBrokerageId(),
      name: 'Independent Brokerage',
    };
  }

  return plainToInstance(BrokerageInfoDto, brokerageData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};
