import { determineBrokerage } from './brokerage.mapper';
import { BrokerageInfoDto } from '../../campaign-bundle/dto/brokerage-info.dto';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';

describe('BrokerageMapper', () => {
  const mockOptions: TransformOptions = {
    includeOpenHouses: true,
    mediaLimit: 10,
    priceFormat: { currency: 'USD', locale: 'en-US' },
  };

  describe('determineBrokerage', () => {
    it('should use provided brokerage data when available', () => {
      const brokerage = { name: 'Premium Real Estate' };

      const result = determineBrokerage(brokerage, mockOptions);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Premium Real Estate');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
      expect(result.website).toBeUndefined();
      expect(result.phoneNumber).toBeUndefined();
    });

    it('should use default brokerage when provided and no CMS data', () => {
      const optionsWithDefault: TransformOptions = {
        ...mockOptions,
        defaultBrokerageInfo: {
          name: 'Default Brokerage LLC',
          website: 'https://defaultbrokerage.com',
          phoneNumber: '******-123-4567',
        },
      };

      const result = determineBrokerage(null, optionsWithDefault);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Default Brokerage LLC');
      expect(result.website).toBe('https://defaultbrokerage.com');
      expect(result.phoneNumber).toBe('******-123-4567');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });

    it('should use fallback when no data and no defaults', () => {
      const result = determineBrokerage(null, mockOptions);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Independent Brokerage');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
      expect(result.website).toBeUndefined();
      expect(result.phoneNumber).toBeUndefined();
    });

    it('should use fallback when brokerage has empty name', () => {
      const brokerage = { name: '' };

      const result = determineBrokerage(brokerage, mockOptions);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Independent Brokerage');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });

    it('should use fallback when brokerage is undefined', () => {
      const result = determineBrokerage(undefined, mockOptions);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Independent Brokerage');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });

    it('should prefer CMS data over default options', () => {
      const brokerage = { name: 'CMS Brokerage' };
      const optionsWithDefault: TransformOptions = {
        ...mockOptions,
        defaultBrokerageInfo: {
          name: 'Default Brokerage LLC',
          website: 'https://defaultbrokerage.com',
          phoneNumber: '******-123-4567',
        },
      };

      const result = determineBrokerage(brokerage, optionsWithDefault);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('CMS Brokerage');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
      // Should not include default website/phoneNumber when using CMS data
      expect(result.website).toBeUndefined();
      expect(result.phoneNumber).toBeUndefined();
    });

    it('should generate unique brokerage IDs', () => {
      const brokerage = { name: 'Test Brokerage' };

      const result1 = determineBrokerage(brokerage, mockOptions);
      const result2 = determineBrokerage(brokerage, mockOptions);

      expect(result1.brokerageId).not.toBe(result2.brokerageId);
      expect(result1.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
      expect(result2.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
    });

    it('should handle partial default brokerage info', () => {
      const optionsWithPartialDefault: TransformOptions = {
        ...mockOptions,
        defaultBrokerageInfo: {
          name: 'Partial Brokerage',
          // website and phone intentionally omitted
        },
      };

      const result = determineBrokerage(null, optionsWithPartialDefault);

      expect(result).toBeInstanceOf(BrokerageInfoDto);
      expect(result.name).toBe('Partial Brokerage');
      expect(result.brokerageId).toMatch(/^brokerage-[a-z0-9]{9}$/);
      expect(result.website).toBeUndefined();
      expect(result.phoneNumber).toBeUndefined();
    });
  });
});
