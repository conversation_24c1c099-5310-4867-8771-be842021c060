import { mapPropertyToBundle } from './property-bundle.mapper';
import { CampaignBundleDto } from '../../campaign-bundle/dto';
import { OpenHouseStatus } from '../../campaign-bundle/enums/open-house-status.enum';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';
import { CmsPropertyResponse } from '../interfaces/cms-property-response.interface';

describe('PropertyBundleMapper', () => {
  describe('mapPropertyToBundle', () => {
    const mockPropertyData: CmsPropertyResponse = {
      propertyId: 'prop-123',
      id: 'id-123',
      companyId: 'company-123',
      description: 'Beautiful 3-bedroom home',
      bedroomCount: 3,
      bathCount: 2,
      dateListed: '2024-01-15T10:00:00Z',
      addressFull: '123 Main St, San Francisco, CA 94105',
      salesPrice: 1500000,
      leasePrice: null,
      slug: 'beautiful-home',
      status: {
        name: 'Active',
      },
      neighborhood: {
        name: 'Mission District',
      },
      brokerage: {
        name: 'Premier Realty',
      },
      media: [
        { url: 'https://example.com/photo1.jpg' },
        { url: 'https://example.com/photo2.jpg' },
      ],
      agents: [
        { firstName: 'John', lastName: 'Doe', leadAgent: true },
        { firstName: 'Jane', lastName: 'Smith', leadAgent: false },
      ],
      openHouses: [
        {
          startTime: '2024-01-20T14:00:00Z',
          endTime: '2024-01-20T16:00:00Z',
        },
      ],
    };

    const defaultOptions: TransformOptions = {
      mediaLimit: 10,
      prioritizeLeadAgent: true,
      parseAddress: true,
      includeOpenHouses: true,
    };

    it('should transform basic property data correctly', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result).toBeInstanceOf(CampaignBundleDto);
      expect(result.propertyId).toBe('prop-123');
      expect(result.description).toBe('Beautiful 3-bedroom home');
      expect(result.bedroomCount).toBe(3);
      expect(result.bathCount).toBe(2);
      expect(result.dateListed).toEqual(new Date('2024-01-15T10:00:00Z'));
      expect(result.propertyType).toBe('Active');
    });

    it('should transform media assets correctly', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result.mediaUrls).toHaveLength(2);
      expect(result.mediaUrls[0].originalUrl).toBe(
        'https://example.com/photo1.jpg',
      );
      expect(result.mediaUrls[1].originalUrl).toBe(
        'https://example.com/photo2.jpg',
      );
    });

    it('should respect media limit option', () => {
      const optionsWithLimit: TransformOptions = {
        ...defaultOptions,
        mediaLimit: 1,
      };
      const result = mapPropertyToBundle(mockPropertyData, optionsWithLimit);

      expect(result.mediaUrls).toHaveLength(1);
    });

    it('should select the first agent when prioritizeLeadAgent is true', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result.agent.firstName).toBe('John');
      expect(result.agent.lastName).toBe('Doe');
    });

    it('should use default agent when no agents are provided', () => {
      const propertyNoAgents: CmsPropertyResponse = {
        ...mockPropertyData,
        agents: [],
      };
      const optionsWithDefault: TransformOptions = {
        ...defaultOptions,
        defaultAgentInfo: {
          firstName: 'Default',
          lastName: 'Agent',
          email: '<EMAIL>',
        },
      };

      const result = mapPropertyToBundle(propertyNoAgents, optionsWithDefault);

      expect(result.agent.firstName).toBe('Default');
      expect(result.agent.lastName).toBe('Agent');
      expect(result.agent.email).toBe('<EMAIL>');
    });

    it('should transform brokerage information', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result.brokerage.name).toBe('Premier Realty');
    });

    it('should use default brokerage when none provided', () => {
      const propertyNoBrokerage: CmsPropertyResponse = {
        ...mockPropertyData,
        brokerage: null,
      };
      const optionsWithDefault: TransformOptions = {
        ...defaultOptions,
        defaultBrokerageInfo: {
          name: 'Default Brokerage',
          website: 'https://default.com',
        },
      };

      const result = mapPropertyToBundle(
        propertyNoBrokerage,
        optionsWithDefault,
      );

      expect(result.brokerage.name).toBe('Default Brokerage');
      expect(result.brokerage.website).toBe('https://default.com');
    });

    it('should determine open house status correctly', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);
      const propertyWithFutureOpenHouse: CmsPropertyResponse = {
        ...mockPropertyData,
        openHouses: [
          {
            startTime: futureDate.toISOString(),
            endTime: new Date(
              futureDate.getTime() + 2 * 60 * 60 * 1000,
            ).toISOString(),
          },
        ],
      };

      const result = mapPropertyToBundle(
        propertyWithFutureOpenHouse,
        defaultOptions,
      );
      expect(result.openHouseStatus).toBe(OpenHouseStatus.SCHEDULED);
    });

    it('should handle no open houses', () => {
      const propertyNoOpenHouses: CmsPropertyResponse = {
        ...mockPropertyData,
        openHouses: [],
      };

      const result = mapPropertyToBundle(propertyNoOpenHouses, defaultOptions);
      expect(result.openHouseStatus).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should transform price information correctly for sales', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result.price.salesPrice).toBe(1500000);
      expect(result.price.listPrice).toBe(1500000);
      expect(result.price.currency).toBe('USD');
    });

    it('should transform price information correctly for lease', () => {
      const leaseProperty: CmsPropertyResponse = {
        ...mockPropertyData,
        salesPrice: null,
        leasePrice: 5000,
      };

      const result = mapPropertyToBundle(leaseProperty, defaultOptions);

      expect(result.price.salesPrice).toBeNull();
      expect(result.price.listPrice).toBe(5000);
      expect(result.price.currency).toBe('USD');
    });

    it('should parse and transform address correctly', () => {
      const result = mapPropertyToBundle(mockPropertyData, defaultOptions);

      expect(result.address.addressFull).toBe(
        '123 Main St, San Francisco, CA 94105',
      );
      expect(result.address.addressLine1).toBe('123 Main St');
      expect(result.address.addressCity).toBe('San Francisco');
      expect(result.address.addressState).toBe('CA');
      expect(result.address.postalCode).toBe('94105');
    });

    it('should handle null values gracefully', () => {
      const propertyWithNulls: CmsPropertyResponse = {
        ...mockPropertyData,
        description: null,
        bedroomCount: null,
        bathCount: null,
        dateListed: null,
        status: null,
        neighborhood: null,
      };

      const result = mapPropertyToBundle(propertyWithNulls, defaultOptions);

      expect(result.description).toBeUndefined();
      expect(result.bedroomCount).toBeNull();
      expect(result.bathCount).toBeNull();
      expect(result.propertyType).toBe('Unknown');
    });

    it('should handle empty arrays gracefully', () => {
      const propertyWithEmptyArrays: CmsPropertyResponse = {
        ...mockPropertyData,
        media: [],
        agents: [],
        openHouses: [],
      };

      const result = mapPropertyToBundle(
        propertyWithEmptyArrays,
        defaultOptions,
      );

      expect(result.mediaUrls).toEqual([]);
      expect(result.openHouseStatus).toBe(OpenHouseStatus.CANCELLED);
    });

    it('should use abbreviated price format when specified', () => {
      const optionsWithAbbreviation: TransformOptions = {
        ...defaultOptions,
        priceFormat: {
          abbreviate: true,
          showCents: false,
        },
      };

      const result = mapPropertyToBundle(
        mockPropertyData,
        optionsWithAbbreviation,
      );
      // Price abbreviation is handled in the mapper but not exposed in the DTO
      expect(result.price.currency).toBe('USD');
    });
  });
});
