import { plainToInstance } from 'class-transformer';

import { PriceInfoDto } from '../../campaign-bundle/dto/price-info.dto';
import { PriceTransformOptions } from '../interfaces/bundle-builder-input.interface';

/**
 * Currency symbol mapping
 */
const CURRENCY_SYMBOLS: Record<string, string> = {
  USD: '$',
  EUR: '€',
  GBP: '£',
  CAD: '$',
  AUD: '$',
};

/**
 * Get currency symbol from currency code or symbol
 */
const getCurrencySymbol = (currency?: string): string => {
  if (!currency) return '$';

  // If already a symbol, return it
  if (Object.values(CURRENCY_SYMBOLS).includes(currency)) {
    return currency;
  }

  // Map currency code to symbol
  return CURRENCY_SYMBOLS[currency.toUpperCase()] || '$';
};

/**
 * Get currency code from symbol or code
 */
const getCurrencyCode = (currency?: string): string => {
  if (!currency) return 'USD';

  // If it's a symbol, find the code
  if (currency === '€') return 'EUR';
  if (currency === '£') return 'GBP';
  if (currency === '$') return 'USD';

  return currency.toUpperCase();
};

/**
 * Format number with thousands separators
 */
const formatWithCommas = (num: number): string => {
  return num.toLocaleString('en-US');
};

/**
 * Abbreviate large numbers
 */
const abbreviateNumber = (num: number): string => {
  if (num >= 1000000000) {
    const billions = num / 1000000000;
    return billions % 1 === 0
      ? `${billions}B`
      : `${billions.toFixed(2).replace(/\.?0+$/, '')}B`;
  }
  if (num >= 1000000) {
    const millions = num / 1000000;
    return millions % 1 === 0
      ? `${millions}M`
      : `${millions.toFixed(2).replace(/\.?0+$/, '')}M`;
  }
  if (num >= 1000) {
    const thousands = num / 1000;
    return thousands % 1 === 0
      ? `${thousands}K`
      : `${thousands.toFixed(1).replace(/\.?0+$/, '')}K`;
  }
  return num.toString();
};

/**
 * Format price for display based on options
 */
export const formatPriceDisplay = (
  price: number | null | undefined,
  type: 'sale' | 'rent',
  options: PriceTransformOptions,
): string => {
  // Handle null/undefined price
  if (price === null || price === undefined) {
    return 'Price Upon Request';
  }

  // Handle zero price
  if (price === 0) {
    return `${getCurrencySymbol(options.priceFormat?.currency)}0`;
  }

  const symbol = getCurrencySymbol(options.priceFormat?.currency);

  let formatted: string;

  if (options.priceFormat?.abbreviate) {
    formatted = `${symbol}${abbreviateNumber(price)}`;
  } else if (options.priceFormat?.showCents) {
    formatted = `${symbol}${formatWithCommas(price)}.00`;
  } else {
    formatted = `${symbol}${formatWithCommas(price)}`;
  }

  // Add rental suffix
  if (type === 'rent') {
    formatted += '/mo';
  }

  return formatted;
};

/**
 * Transform price information from CMS data
 * Creates an extended price object with display formatting
 */
export const transformPrice = (
  salesPrice: number | null | undefined,
  leasePrice: number | null | undefined,
  options: PriceTransformOptions,
): any => {
  const hasSalesPrice = salesPrice !== null && salesPrice !== undefined;
  const hasLeasePrice = leasePrice !== null && leasePrice !== undefined;

  let effectiveSalePrice: number | null = null;
  let effectiveRentPrice: number | null = null;
  let priceUponRequest = false;

  if (hasSalesPrice) {
    effectiveSalePrice = salesPrice;
  } else if (hasLeasePrice) {
    effectiveRentPrice = leasePrice;
  } else {
    priceUponRequest = true;
  }

  const currencyCode = getCurrencyCode(options.priceFormat?.currency);

  // Create price data matching the PriceInfoDto structure
  const priceData = {
    listPrice: effectiveSalePrice || effectiveRentPrice,
    salesPrice: effectiveSalePrice,
    currency: currencyCode,
    priceUponRequest,
  };

  // Return as DTO instance
  return plainToInstance(PriceInfoDto, priceData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};
