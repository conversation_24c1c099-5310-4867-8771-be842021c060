import { mapMediaAssets, createMediaAsset } from './media-asset.mapper';
import { MediaAssetDto } from '../../campaign-bundle/dto/media-asset.dto';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';

describe('MediaAssetMapper', () => {
  describe('createMediaAsset', () => {
    it('should create a basic media asset from URL', () => {
      const url = 'https://example.com/photo.jpg';
      const index = 0;

      const result = createMediaAsset(url, index);

      expect(result).toBeInstanceOf(MediaAssetDto);
      expect(result.id).toBe('media-0');
      expect(result.resourceType).toBe('image');
      expect(result.originalUrl).toBe(url);
      expect(result.originalFileName).toBe('photo.jpg');
      expect(result.format).toBe('jpg');
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });

    it('should detect video resource type', () => {
      const videoUrl = 'https://example.com/video.mp4';
      const result = createMediaAsset(videoUrl, 0);

      expect(result.resourceType).toBe('video');
      expect(result.format).toBe('mp4');
    });

    it('should handle URLs without extensions', () => {
      const url = 'https://example.com/image';
      const result = createMediaAsset(url, 0);

      expect(result.resourceType).toBe('image');
      expect(result.format).toBeUndefined();
      expect(result.originalFileName).toBe('image');
    });

    it('should handle complex URLs with query parameters', () => {
      const url = 'https://example.com/photo.jpg?width=800&height=600';
      const result = createMediaAsset(url, 0);

      expect(result.originalFileName).toBe('photo.jpg');
      expect(result.format).toBe('jpg');
    });

    it('should generate responsive URLs for images', () => {
      const url = 'https://example.com/photo.jpg';
      const result = createMediaAsset(url, 0);

      expect(result.thumbnailUrl).toContain('photo.jpg');
      expect(result.smallUrl).toContain('photo.jpg');
      expect(result.mediumUrl).toContain('photo.jpg');
      expect(result.largeUrl).toContain('photo.jpg');
      expect(result.xLargeUrl).toContain('photo.jpg');
      expect(result.xxLargeUrl).toContain('photo.jpg');
    });

    it('should not generate responsive URLs for videos', () => {
      const url = 'https://example.com/video.mp4';
      const result = createMediaAsset(url, 0);

      expect(result.thumbnailUrl).toBeUndefined();
      expect(result.smallUrl).toBeUndefined();
      expect(result.mediumUrl).toBeUndefined();
    });

    it('should set default alt text', () => {
      const url = 'https://example.com/photo.jpg';
      const result = createMediaAsset(url, 2);

      expect(result.alt).toBe('Property image 3');
      expect(result.altTagText).toBe('Property image 3');
      expect(result.displayName).toBe('Property Photo 3');
    });
  });

  describe('mapMediaAssets', () => {
    const mockMedia = [
      { url: 'https://example.com/photo1.jpg' },
      { url: 'https://example.com/photo2.jpg' },
      { url: 'https://example.com/photo3.jpg' },
      { url: 'https://example.com/video.mp4' },
    ];

    it('should map all media assets', () => {
      const options: TransformOptions = {};
      const result = mapMediaAssets(mockMedia, options);

      expect(result).toHaveLength(4);
      expect(result[0].originalUrl).toBe('https://example.com/photo1.jpg');
      expect(result[3].resourceType).toBe('video');
    });

    it('should respect media limit', () => {
      const options: TransformOptions = { mediaLimit: 2 };
      const result = mapMediaAssets(mockMedia, options);

      expect(result).toHaveLength(2);
      expect(result[0].originalUrl).toBe('https://example.com/photo1.jpg');
      expect(result[1].originalUrl).toBe('https://example.com/photo2.jpg');
    });

    it('should handle empty media array', () => {
      const options: TransformOptions = {};
      const result = mapMediaAssets([], options);

      expect(result).toEqual([]);
    });

    it('should handle null media array', () => {
      const options: TransformOptions = {};
      const result = mapMediaAssets(null as any, options);

      expect(result).toEqual([]);
    });

    it('should filter out invalid URLs', () => {
      const invalidMedia = [
        { url: 'https://example.com/valid.jpg' },
        { url: '' },
        { url: null as any },
        { url: 'not-a-url' },
        { url: 'https://example.com/valid2.jpg' },
      ];

      const options: TransformOptions = {};
      const result = mapMediaAssets(invalidMedia, options);

      expect(result).toHaveLength(3);
      expect(result[0].originalUrl).toBe('https://example.com/valid.jpg');
      expect(result[1].originalUrl).toBe('not-a-url');
      expect(result[2].originalUrl).toBe('https://example.com/valid2.jpg');
    });

    it('should assign unique IDs to each asset', () => {
      const options: TransformOptions = {};
      const result = mapMediaAssets(mockMedia, options);

      const ids = result.map(asset => asset.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });
  });
});
