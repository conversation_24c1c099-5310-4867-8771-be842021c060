import { plainToInstance } from 'class-transformer';

import { parseAddress, formatAddress } from './address.mapper';
import { selectPrimaryAgent } from './agent.mapper';
import { determineBrokerage } from './brokerage.mapper';
import { mapMediaAssets } from './media-asset.mapper';
import { determineOpenHouseStatus } from './open-house.mapper';
import { transformPrice } from './price.mapper';
import { CampaignBundleDto } from '../../campaign-bundle/dto';
import { TransformOptions } from '../interfaces/bundle-builder-input.interface';
import { CmsPropertyResponse } from '../interfaces/cms-property-response.interface';

/**
 * Maps a CMS property response to a CampaignBundleDto
 * Uses functional composition with individual mappers for each domain
 */
export const mapPropertyToBundle = (
  propertyData: CmsPropertyResponse,
  options: TransformOptions,
): CampaignBundleDto => {
  // Transform media assets
  const mediaUrls = mapMediaAssets(propertyData.media, options);

  // Select primary agent
  const agent = selectPrimaryAgent(propertyData.agents, options);

  // Determine brokerage
  const brokerage = determineBrokerage(propertyData.brokerage, options);

  // Determine open house status
  const openHouseStatus = determineOpenHouseStatus(
    propertyData.openHouses,
    options,
  );

  // Transform price information
  const price = transformPrice(
    propertyData.salesPrice,
    propertyData.leasePrice,
    options,
  );

  // Parse and format address
  const address = options.parseAddress
    ? parseAddress(propertyData.addressFull, propertyData.neighborhood?.name)
    : formatAddress(propertyData.addressFull, propertyData.neighborhood?.name);

  // Build the bundle DTO
  const bundleData = {
    propertyId: propertyData.propertyId,
    description: propertyData.description || undefined,
    mediaUrls,
    bedroomCount: propertyData.bedroomCount,
    bathCount: propertyData.bathCount,
    agent,
    brokerage,
    dateListed: propertyData.dateListed
      ? new Date(propertyData.dateListed)
      : new Date(),
    openHouseStatus,
    price,
    address,
    propertyType: propertyData.status?.name || 'Unknown',
  };

  // Use class-transformer to create a proper instance with validation
  return plainToInstance(CampaignBundleDto, bundleData, {
    excludeExtraneousValues: false,
    enableImplicitConversion: true,
  });
};
