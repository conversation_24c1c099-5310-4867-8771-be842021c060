import {
  parseAddress,
  formatAddress,
  extractAddressComponents,
} from './address.mapper';
import { AddressInfoDto } from '../../campaign-bundle/dto/address-info.dto';

describe('AddressMapper', () => {
  describe('extractAddressComponents', () => {
    it('should parse standard US address format', () => {
      const address = '123 Main St, San Francisco, CA 94105';
      const result = extractAddressComponents(address);

      expect(result.streetAddress).toBe('123 Main St');
      expect(result.city).toBe('San Francisco');
      expect(result.state).toBe('CA');
      expect(result.zipCode).toBe('94105');
    });

    it('should handle apartment/unit numbers', () => {
      const address = '456 Oak Ave Apt 2B, Los Angeles, CA 90001';
      const result = extractAddressComponents(address);

      expect(result.streetAddress).toBe('456 Oak Ave Apt 2B');
      expect(result.city).toBe('Los Angeles');
      expect(result.state).toBe('CA');
      expect(result.zipCode).toBe('90001');
      expect(result.unit).toBe('2B');
    });

    it('should handle suite numbers', () => {
      const address = '789 Business Blvd Suite 300, New York, NY 10001';
      const result = extractAddressComponents(address);

      expect(result.streetAddress).toBe('789 Business Blvd Suite 300');
      expect(result.unit).toBe('300');
      expect(result.city).toBe('New York');
      expect(result.state).toBe('NY');
    });

    it('should handle missing zip code', () => {
      const address = '123 Main St, San Francisco, CA';
      const result = extractAddressComponents(address);

      expect(result.streetAddress).toBe('123 Main St');
      expect(result.city).toBe('San Francisco');
      expect(result.state).toBe('CA');
      expect(result.zipCode).toBeUndefined();
    });

    it('should handle zip+4 format', () => {
      const address = '123 Main St, San Francisco, CA 94105-1234';
      const result = extractAddressComponents(address);

      expect(result.zipCode).toBe('94105');
      expect(result.zipPlus4).toBe('1234');
    });

    it('should handle multi-word city names', () => {
      const address = '123 Main St, San Luis Obispo, CA 93401';
      const result = extractAddressComponents(address);

      expect(result.city).toBe('San Luis Obispo');
      expect(result.state).toBe('CA');
    });

    it('should handle addresses without commas', () => {
      const address = '123 Main St San Francisco CA 94105';
      const result = extractAddressComponents(address);

      // Should still attempt to parse
      expect(result.streetAddress).toBeTruthy();
    });

    it('should handle international addresses gracefully', () => {
      const address = '10 Downing Street, London, UK SW1A 2AA';
      const result = extractAddressComponents(address);

      expect(result.streetAddress).toBe('10 Downing Street');
      expect(result.city).toBe('London');
    });
  });

  describe('parseAddress', () => {
    it('should create AddressInfoDto with parsed components', () => {
      const fullAddress = '123 Main St, San Francisco, CA 94105';
      const neighborhood = 'Mission District';

      const result = parseAddress(fullAddress, neighborhood);

      expect(result).toBeInstanceOf(AddressInfoDto);
      expect(result.addressFull).toBe(fullAddress);
      expect(result.addressLine1).toBe('123 Main St');
      expect(result.addressCity).toBe('San Francisco');
      expect(result.addressState).toBe('CA');
      expect(result.postalCode).toBe('94105');
      expect(result.addressCountry).toBe('USA');
    });

    it('should handle null neighborhood', () => {
      const fullAddress = '456 Oak Ave, Los Angeles, CA 90001';

      const result = parseAddress(fullAddress, null);

      expect(result.addressCity).toBe('Los Angeles');
    });

    it('should handle empty address', () => {
      const result = parseAddress('', null);

      expect(result.addressFull).toBe('');
      expect(result.addressLine1).toBeUndefined();
      expect(result.addressCity).toBeUndefined();
      expect(result.addressState).toBeUndefined();
    });

    it('should include unit information when present', () => {
      const fullAddress = '789 Pine St Unit 5, Seattle, WA 98101';

      const result = parseAddress(fullAddress, null);

      expect(result.addressLine1).toBe('789 Pine St Unit 5');
      expect(result.addressLine2).toBe('Unit 5');
      expect(result.addressCity).toBe('Seattle');
    });
  });

  describe('formatAddress', () => {
    it('should create AddressInfoDto without parsing', () => {
      const fullAddress = '123 Complex Address Format';
      const neighborhood = 'Downtown';

      const result = formatAddress(fullAddress, neighborhood);

      expect(result).toBeInstanceOf(AddressInfoDto);
      expect(result.addressFull).toBe(fullAddress);
      expect(result.addressCountry).toBe('USA');
      // Should not have parsed components
      expect(result.addressLine1).toBeUndefined();
      expect(result.addressCity).toBeUndefined();
      expect(result.addressState).toBeUndefined();
      expect(result.postalCode).toBeUndefined();
    });

    it('should handle null values', () => {
      const result = formatAddress(null as any, null);

      expect(result.addressFull).toBe('');
      expect(result.addressCountry).toBe('USA');
    });
  });
});
