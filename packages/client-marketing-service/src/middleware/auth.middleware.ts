import {
  AuthContextMiddleware,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(private readonly authMiddleware: AuthContextMiddleware) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      req.authContext = (await this.authMiddleware.middleware(
        req,
      )) as UnifiedAuthContext<AppPolicyRegistry>;
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(401).json({ error: 'Unauthorized' });
    }
  }
}
