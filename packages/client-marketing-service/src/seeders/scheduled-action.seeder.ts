import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { SeederFactories } from 'src/common/utils/seeder-factories.util';
import { Group } from 'src/group/entities/group.entity';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import {
  ScheduledAction,
  Status,
} from 'src/scheduled-action/entities/scheduled-action.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ScheduledActionSeeder implements Seeder {
  constructor(
    @InjectRepository(ScheduledAction)
    private readonly repo: Repository<ScheduledAction>,
    @InjectRepository(Group)
    private readonly groupRepo: Repository<Group>,
    @InjectRepository(GroupScheduledAction)
    private readonly groupScheduledActionRepo: Repository<GroupScheduledAction>,
    @InjectRepository(PageKeyword)
    private readonly pageKeywordRepo: Repository<PageKeyword>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting scheduled-action seeding...');

    // Check existing scheduled actions
    const existingActions = await this.repo.find();
    console.log(`Found ${existingActions.length} existing scheduled actions`);

    // Use company IDs from company-data.ts to ensure consistency
    const companyIds = [
      'a7b4401f-a8be-440d-922f-7b133d4f2197',
      '56be239a-0cb8-4c0f-8553-0156e96900ce',
      'dc5107de-ae4e-471c-8435-e39ecad45be8',
    ];

    // Ensure at least one of every status gets generated (except HUMAN_QA_PENDING)
    const allStatuses = Object.values(Status).filter(
      status => status !== Status.HUMAN_QA_PENDING,
    );

    // Check which statuses each company already has
    const companyStatusMap = new Map<string, Set<Status>>();
    companyIds.forEach(companyId => {
      companyStatusMap.set(companyId, new Set());
    });

    existingActions.forEach(action => {
      if (companyStatusMap.has(action.companyId)) {
        companyStatusMap.get(action.companyId)!.add(action.status);
      }
    });

    // Generate actions to ensure each company has at least one of each status
    const guaranteedActions: any[] = [];

    companyIds.forEach(companyId => {
      const existingStatusesForCompany =
        companyStatusMap.get(companyId) || new Set();
      const missingStatuses = allStatuses.filter(
        status => !existingStatusesForCompany.has(status),
      );

      if (missingStatuses.length > 0) {
        console.log(
          `Company ${companyId} is missing statuses: ${missingStatuses.join(', ')}`,
        );

        missingStatuses.forEach(status => {
          const action =
            DataFactory.createForClass(ScheduledAction).generate(1)[0];
          action.status = status;
          action.companyId = companyId;
          guaranteedActions.push(action);
        });
      }
    });

    console.log(
      `Creating ${guaranteedActions.length} guaranteed actions to ensure each company has all statuses`,
    );

    // If no actions need to be created, still need to update existing actions' contentPayload
    if (guaranteedActions.length === 0) {
      console.log(
        'All companies already have at least one action of each status type',
      );

      // Note: contentPayload will be updated later by ScheduledActionContentPayloadSeeder after recommendations are created

      return existingActions;
    }

    // Add some additional random actions for variety
    const totalActions = Math.max(
      30,
      existingActions.length + guaranteedActions.length + 10,
    );
    const remainingCount = Math.max(
      0,
      totalActions - existingActions.length - guaranteedActions.length,
    );

    const randomActions: any[] = [];
    if (remainingCount > 0) {
      // Create a good distribution of statuses
      const statusDistribution = [
        Status.DRAFT_PENDING,
        Status.DRAFT_PENDING,
        Status.DRAFT_PENDING,
        Status.DRAFT_PENDING,
        Status.SURFACING_PENDING,
        Status.SURFACING_PENDING,
        Status.SURFACING_PENDING,
        Status.SURFACED,
        Status.SURFACED,
        Status.SURFACED,
        Status.PUBLISHED,
        Status.FAILED,
        Status.INVALIDATED,
      ];

      for (let i = 0; i < remainingCount; i++) {
        const action =
          DataFactory.createForClass(ScheduledAction).generate(1)[0];
        action.status = statusDistribution[i % statusDistribution.length];
        action.companyId = companyIds[i % companyIds.length];
        randomActions.push(action);
      }
    }

    // Combine guaranteed and random actions
    const allActions = [...guaranteedActions, ...randomActions];

    // Log status distribution
    const statusCounts: Record<string, number> = {};
    allActions.forEach(action => {
      const status = action.status as string;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    console.log('Status distribution for new actions:', statusCounts);

    // Get existing scraped pages and scrapes to link with scheduled actions
    const scrapedPages = await this.repo.manager.find('ScrapedPage');
    const scrapes = await this.repo.manager.find('Scrape');

    // Apply conditional logic for each action based on workflow
    const scheduledActions = allActions.map((action, index) => {
      // companyId is already set in the guaranteed actions

      // Create proper generation payload based on status
      // Lambda pipeline flow from seoKeywordsStateMachine:
      // 1. ScrapeWebPages: Initial state with only websiteUrl
      // 2. ExtractKeywords: After scraper, adds scraperResult
      // 3. GenerateRecommendations: After keywords, adds keywords
      // 4. StoreSEODraft: After recommendations, adds recommendations
      // Status progression: DRAFT_PENDING -> SURFACING_PENDING -> SURFACED -> PUBLISHED
      let generationPayload: any = {};

      // Helper function to extract base domain from URL
      const extractBaseDomain = (url: string): string => {
        try {
          const urlObj = new URL(url);
          // Remove www. if present and return just the hostname
          return urlObj.hostname.replace(/^www\./, '');
        } catch {
          // Fallback if URL parsing fails
          return 'example.com';
        }
      };

      // Get company's scraped pages to extract domain
      const companyScrapedPages = scrapedPages.filter(
        page => page.companyId === action.companyId,
      );

      // Extract base domain from the first scraped page URL for this company
      let baseDomain = 'example.com';
      if (companyScrapedPages.length > 0 && companyScrapedPages[0].url) {
        baseDomain = extractBaseDomain(companyScrapedPages[0].url);
      }

      // Determine the stage of the action in the pipeline
      if (action.status === Status.DRAFT_PENDING) {
        // For DRAFT_PENDING, create different stages of the pipeline
        // Based on the Step Functions pipeline, actions can be at different stages
        const draftStages = [
          'initial', // Just started - only websiteUrl
          'scraping', // In ScrapeWebPages lambda
          'extracting_keywords', // In ExtractKeywords lambda
          'generating_recommendations', // In GenerateRecommendations lambda
          'storing_draft', // In StoreSEODraft lambda (about to become SURFACING_PENDING)
        ];

        // Get which stage this DRAFT_PENDING action should be at
        const draftPendingCount = allActions
          .filter(a => a.status === Status.DRAFT_PENDING)
          .indexOf(action);
        const stage = draftStages[draftPendingCount % draftStages.length];

        console.log(`Creating DRAFT_PENDING action at stage: ${stage}`);

        // Start with websiteUrl
        generationPayload = {
          websiteUrl: `https://${baseDomain}`,
        };

        // Get a scraped page for this company if available
        const scrapedPage =
          companyScrapedPages.length > 0
            ? companyScrapedPages[
                draftPendingCount % companyScrapedPages.length
              ]
            : undefined;

        // Build generation payload based on the stage
        switch (stage) {
          case 'scraping':
            // Still in scraper, only has websiteUrl
            break;

          case 'extracting_keywords':
            if (scrapedPage) {
              const pageScrapes = scrapes.filter(
                scrape => scrape.scrapedPageId === scrapedPage.id,
              );
              const scrape = pageScrapes[0];

              generationPayload.scraperResult = {
                firecrawlId: `firecrawl-draft-${draftPendingCount + 1}`,
                url: scrapedPage.url,
                metaTitle:
                  scrape?.currentScrapedValues?.metaTitle ||
                  `${scrapedPage.pageName} - Real Estate`,
                metaDescription:
                  scrape?.currentScrapedValues?.metaDescription ||
                  `Professional real estate services in your area.`,
                mainHeading: {
                  tag: 'h1',
                  index: 0,
                  content:
                    scrape?.currentScrapedValues?.h1 || scrapedPage.pageName,
                },
                type: scrapedPage.pageType,
                markdown:
                  scrape?.markdown ||
                  `# ${scrapedPage.pageName}\n\nWelcome to our real estate services...`,
                mediaId: `media-draft-${draftPendingCount + 1}`,
                companyId: action.companyId,
              };
            }
            break;

          case 'generating_recommendations':
            if (scrapedPage) {
              const pageScrapes = scrapes.filter(
                scrape => scrape.scrapedPageId === scrapedPage.id,
              );
              const scrape = pageScrapes[0];

              generationPayload.scraperResult = {
                firecrawlId: `firecrawl-draft-${draftPendingCount + 1}`,
                url: scrapedPage.url,
                metaTitle:
                  scrape?.currentScrapedValues?.metaTitle ||
                  `${scrapedPage.pageName} - Real Estate`,
                metaDescription:
                  scrape?.currentScrapedValues?.metaDescription ||
                  `Find your dream home with our expert real estate agents.`,
                mainHeading: {
                  tag: 'h1',
                  index: 0,
                  content:
                    scrape?.currentScrapedValues?.h1 || scrapedPage.pageName,
                },
                type: scrapedPage.pageType,
                markdown:
                  scrape?.markdown ||
                  `# ${scrapedPage.pageName}\n\nExpert real estate services...`,
                mediaId: `media-draft-${draftPendingCount + 1}`,
                companyId: action.companyId,
              };
              // Generate more realistic keywords based on page type
              const keywordsByPageType: Record<string, string> = {
                HOMEPAGE:
                  'real estate, homes for sale, property search, local realtors, real estate agents',
                HOME_VALUATION:
                  'home value, property valuation, house worth, real estate appraisal, home estimate',
                MORTGAGE_CALCULATOR:
                  'mortgage calculator, home loan, interest rates, monthly payments, affordability',
                AGENT_BIO:
                  'real estate agent, realtor, property expert, local agent, experienced broker',
                BUYERS_GUIDE:
                  'home buying guide, first time buyer, property purchase, buying process, real estate tips',
                SELLERS_GUIDE:
                  'selling home, property sale, home staging, listing price, seller tips',
                NEIGHBORHOOD_GUIDE:
                  'neighborhood guide, local area, community info, schools, amenities',
                BLOG: 'real estate blog, market trends, property news, housing market, real estate advice',
              };
              generationPayload.keywords =
                keywordsByPageType[scrapedPage.pageType] ||
                'real estate, property, homes for sale, local experts';
            }
            break;

          case 'storing_draft':
            if (scrapedPage) {
              const pageScrapes = scrapes.filter(
                scrape => scrape.scrapedPageId === scrapedPage.id,
              );
              const scrape = pageScrapes[0];

              const scraperResult = {
                firecrawlId: `firecrawl-draft-${draftPendingCount + 1}`,
                url: scrapedPage.url,
                metaTitle:
                  scrape?.currentScrapedValues?.metaTitle ||
                  `${scrapedPage.pageName} - Real Estate`,
                metaDescription:
                  scrape?.currentScrapedValues?.metaDescription ||
                  `Trusted real estate professionals helping you buy or sell.`,
                mainHeading: {
                  tag: 'h1',
                  index: 0,
                  content:
                    scrape?.currentScrapedValues?.h1 || scrapedPage.pageName,
                },
                type: scrapedPage.pageType,
                markdown:
                  scrape?.markdown ||
                  `# ${scrapedPage.pageName}\n\nYour trusted real estate partner...`,
                mediaId: `media-draft-${draftPendingCount + 1}`,
                companyId: action.companyId,
              };

              generationPayload.scraperResult = scraperResult;

              // Extract company name from page name
              const companyName =
                scrapedPage.pageName.split(' - ')[1] || 'Real Estate Company';

              // Use the same keyword generation logic
              const keywordsByPageType: Record<string, string> = {
                HOMEPAGE:
                  'real estate, homes for sale, property search, local realtors, real estate agents',
                HOME_VALUATION:
                  'home value, property valuation, house worth, real estate appraisal, home estimate',
                MORTGAGE_CALCULATOR:
                  'mortgage calculator, home loan, interest rates, monthly payments, affordability',
                AGENT_BIO:
                  'real estate agent, realtor, property expert, local agent, experienced broker',
                BUYERS_GUIDE:
                  'home buying guide, first time buyer, property purchase, buying process, real estate tips',
                SELLERS_GUIDE:
                  'selling home, property sale, home staging, listing price, seller tips',
                NEIGHBORHOOD_GUIDE:
                  'neighborhood guide, local area, community info, schools, amenities',
                BLOG: 'real estate blog, market trends, property news, housing market, real estate advice',
              };
              generationPayload.keywords =
                keywordsByPageType[scrapedPage.pageType] ||
                'real estate, property, homes for sale, local experts';

              // Generate page-type-specific recommendations
              const recommendationsByPageType: Record<string, any> = {
                HOMEPAGE: {
                  metaTitle: {
                    currentValue: scraperResult.metaTitle,
                    recommendationValue: `${companyName} - Premier Real Estate Agents & Homes for Sale`,
                    reasoning:
                      'Incorporated primary keywords "real estate agents" and "homes for sale" while maintaining brand prominence for better CTR and local SEO',
                  },
                  metaDescription: {
                    currentValue: scraperResult.metaDescription,
                    recommendationValue: `Find your dream home with ${companyName}. Expert real estate agents, exclusive property listings, and personalized service. Call (************* for a free consultation.`,
                    reasoning:
                      'Added specific call-to-action with phone number, incorporated target keywords, and stayed within 160 character limit for optimal SERP display',
                  },
                  mainHeading: {
                    currentValue: scraperResult.mainHeading.content,
                    recommendationValue: `Find Your Dream Home with ${companyName}'s Expert Real Estate Team`,
                    reasoning:
                      'Combined action-oriented language with target keywords and brand name for improved user engagement and SEO relevance',
                  },
                },
                HOME_VALUATION: {
                  metaTitle: {
                    currentValue: scraperResult.metaTitle,
                    recommendationValue: `Free Home Valuation - Get Your Property Value Instantly | ${companyName}`,
                    reasoning:
                      'Led with primary service keyword "home valuation" and added urgency with "instantly" for better CTR',
                  },
                  metaDescription: {
                    currentValue: scraperResult.metaDescription,
                    recommendationValue: `Get a free, instant home valuation from ${companyName}. Accurate property values based on recent sales and market data. Start your valuation now!`,
                    reasoning:
                      'Emphasized "free" and "instant" benefits, included trust signals with "accurate" and "market data", clear CTA',
                  },
                  mainHeading: {
                    currentValue: scraperResult.mainHeading.content,
                    recommendationValue: `Get Your Free Home Valuation in Seconds`,
                    reasoning:
                      'Created urgency with time-based language and emphasized the free aspect to increase conversions',
                  },
                },
                MORTGAGE_CALCULATOR: {
                  metaTitle: {
                    currentValue: scraperResult.metaTitle,
                    recommendationValue: `Mortgage Calculator - Calculate Home Loan Payments | ${companyName}`,
                    reasoning:
                      'Front-loaded with high-search-volume keyword "mortgage calculator" and included related term "home loan payments"',
                  },
                  metaDescription: {
                    currentValue: scraperResult.metaDescription,
                    recommendationValue: `Calculate your monthly mortgage payments with ${companyName}'s free calculator. Compare rates, estimate affordability, and plan your home purchase today.`,
                    reasoning:
                      'Included multiple calculator-related keywords and benefits, with action-oriented CTA',
                  },
                  mainHeading: {
                    currentValue: scraperResult.mainHeading.content,
                    recommendationValue: `Calculate Your Monthly Mortgage Payment`,
                    reasoning:
                      "Direct, keyword-rich heading that clearly states the tool's purpose",
                  },
                },
              };

              generationPayload.recommendations = recommendationsByPageType[
                scrapedPage.pageType
              ] || {
                metaTitle: {
                  currentValue: scraperResult.metaTitle,
                  recommendationValue: `${scraperResult.metaTitle} | ${companyName} - Local Real Estate Experts`,
                  reasoning:
                    'Added location and expertise signals to improve local SEO and trust',
                },
                metaDescription: {
                  currentValue: scraperResult.metaDescription,
                  recommendationValue: `${scraperResult.metaDescription} Contact ${companyName} today for expert real estate services. Call (*************.`,
                  reasoning:
                    'Added clear call-to-action with contact information while maintaining keyword relevance',
                },
                mainHeading: {
                  currentValue: scraperResult.mainHeading.content,
                  recommendationValue: `${scraperResult.mainHeading.content} - Your Trusted Real Estate Partner`,
                  reasoning:
                    'Enhanced with trust-building language for better user engagement',
                },
              };
            }
            break;

          case 'initial':
          default:
            // Just websiteUrl, already set above
            break;
        }

        // DRAFT_PENDING always has empty content payload
        action.contentPayload = {};
      } else {
        // For non-DRAFT_PENDING statuses, DO NOT include websiteUrl
        // Only add scraperResult and other fields as needed
        const scrapedPage =
          companyScrapedPages.length > 0
            ? companyScrapedPages[index % companyScrapedPages.length]
            : undefined;

        if (scrapedPage) {
          // Find a scrape for this page
          const pageScrapes = scrapes.filter(
            scrape => scrape.scrapedPageId === scrapedPage.id,
          );
          const scrape = pageScrapes[0];

          generationPayload.scraperResult = {
            firecrawlId: `firecrawl-${index + 1}`,
            url: scrapedPage.url,
            metaTitle:
              scrape?.currentScrapedValues?.metaTitle ||
              `${scrapedPage.pageName} - Real Estate`,
            metaDescription:
              scrape?.currentScrapedValues?.metaDescription ||
              `Professional real estate services. Find your dream home today.`,
            mainHeading: {
              tag: 'h1',
              index: 0,
              content: scrape?.currentScrapedValues?.h1 || scrapedPage.pageName,
            },
            type: scrapedPage.pageType,
            markdown:
              scrape?.markdown ||
              `# ${scrapedPage.pageName}\n\nProfessional real estate content...`,
            mediaId: `media-${index + 1}`,
            companyId: action.companyId,
          };

          // For SURFACING_PENDING and later, add keywords and recommendations
          if (
            action.status === Status.SURFACING_PENDING ||
            action.status === Status.SURFACED ||
            action.status === Status.PUBLISHED
          ) {
            // Use the enhanced keyword generation
            const keywordsByPageType: Record<string, string> = {
              HOMEPAGE:
                'real estate, homes for sale, property search, local realtors, real estate agents',
              HOME_VALUATION:
                'home value, property valuation, house worth, real estate appraisal, home estimate',
              MORTGAGE_CALCULATOR:
                'mortgage calculator, home loan, interest rates, monthly payments, affordability',
              AGENT_BIO:
                'real estate agent, realtor, property expert, local agent, experienced broker',
              BUYERS_GUIDE:
                'home buying guide, first time buyer, property purchase, buying process, real estate tips',
              SELLERS_GUIDE:
                'selling home, property sale, home staging, listing price, seller tips',
              NEIGHBORHOOD_GUIDE:
                'neighborhood guide, local area, community info, schools, amenities',
              BLOG: 'real estate blog, market trends, property news, housing market, real estate advice',
            };
            generationPayload.keywords =
              keywordsByPageType[scrapedPage.pageType] ||
              'real estate, property, homes for sale, local experts';

            // Extract company name from page name
            const companyName =
              scrapedPage.pageName.split(' - ')[1] || 'Real Estate Company';

            // Use enhanced recommendations based on page type
            const recommendationsByPageType: Record<string, any> = {
              HOMEPAGE: {
                metaTitle: {
                  currentValue: generationPayload.scraperResult.metaTitle,
                  recommendationValue: `${companyName} - Premier Real Estate Agents & Homes for Sale`,
                  reasoning:
                    'Incorporated primary keywords "real estate agents" and "homes for sale" while maintaining brand prominence for better CTR and local SEO',
                },
                metaDescription: {
                  currentValue: generationPayload.scraperResult.metaDescription,
                  recommendationValue: `Find your dream home with ${companyName}. Expert real estate agents, exclusive property listings, and personalized service. Call (************* for a free consultation.`,
                  reasoning:
                    'Added specific call-to-action with phone number, incorporated target keywords, and stayed within 160 character limit for optimal SERP display',
                },
                mainHeading: {
                  currentValue:
                    generationPayload.scraperResult.mainHeading.content,
                  recommendationValue: `Find Your Dream Home with ${companyName}'s Expert Real Estate Team`,
                  reasoning:
                    'Combined action-oriented language with target keywords and brand name for improved user engagement and SEO relevance',
                },
              },
              HOME_VALUATION: {
                metaTitle: {
                  currentValue: generationPayload.scraperResult.metaTitle,
                  recommendationValue: `Free Home Valuation - Get Your Property Value Instantly | ${companyName}`,
                  reasoning:
                    'Led with primary service keyword "home valuation" and added urgency with "instantly" for better CTR',
                },
                metaDescription: {
                  currentValue: generationPayload.scraperResult.metaDescription,
                  recommendationValue: `Get a free, instant home valuation from ${companyName}. Accurate property values based on recent sales and market data. Start your valuation now!`,
                  reasoning:
                    'Emphasized "free" and "instant" benefits, included trust signals with "accurate" and "market data", clear CTA',
                },
                mainHeading: {
                  currentValue:
                    generationPayload.scraperResult.mainHeading.content,
                  recommendationValue: `Get Your Free Home Valuation in Seconds`,
                  reasoning:
                    'Created urgency with time-based language and emphasized the free aspect to increase conversions',
                },
              },
              MORTGAGE_CALCULATOR: {
                metaTitle: {
                  currentValue: generationPayload.scraperResult.metaTitle,
                  recommendationValue: `Mortgage Calculator - Calculate Home Loan Payments | ${companyName}`,
                  reasoning:
                    'Front-loaded with high-search-volume keyword "mortgage calculator" and included related term "home loan payments"',
                },
                metaDescription: {
                  currentValue: generationPayload.scraperResult.metaDescription,
                  recommendationValue: `Calculate your monthly mortgage payments with ${companyName}'s free calculator. Compare rates, estimate affordability, and plan your home purchase today.`,
                  reasoning:
                    'Included multiple calculator-related keywords and benefits, with action-oriented CTA',
                },
                mainHeading: {
                  currentValue:
                    generationPayload.scraperResult.mainHeading.content,
                  recommendationValue: `Calculate Your Monthly Mortgage Payment`,
                  reasoning:
                    "Direct, keyword-rich heading that clearly states the tool's purpose",
                },
              },
            };

            generationPayload.recommendations = recommendationsByPageType[
              scrapedPage.pageType
            ] || {
              metaTitle: {
                currentValue: generationPayload.scraperResult.metaTitle,
                recommendationValue: `${generationPayload.scraperResult.metaTitle} | ${companyName} - Local Real Estate Experts`,
                reasoning:
                  'Added location and expertise signals to improve local SEO and trust',
              },
              metaDescription: {
                currentValue: generationPayload.scraperResult.metaDescription,
                recommendationValue: `${generationPayload.scraperResult.metaDescription} Contact ${companyName} today for expert real estate services. Call (*************.`,
                reasoning:
                  'Added clear call-to-action with contact information while maintaining keyword relevance',
              },
              mainHeading: {
                currentValue:
                  generationPayload.scraperResult.mainHeading.content,
                recommendationValue: `${generationPayload.scraperResult.mainHeading.content} - Your Trusted Real Estate Partner`,
                reasoning:
                  'Enhanced with trust-building language for better user engagement',
              },
            };

            // Content payload should be StoreSEODraftResponse structure
            if (
              action.status === Status.SURFACING_PENDING ||
              action.status === Status.SURFACED ||
              action.status === Status.PUBLISHED
            ) {
              // Extract keyword and URL from generation payload
              const keyword = generationPayload.keywords || 'real estate';
              const url =
                generationPayload.scraperResult?.url || scrapedPage.url;

              // Create StoreSEODraftResponse structure
              action.contentPayload =
                SeederFactories.createStoreSEODraftResponse(
                  null,
                  action.companyId,
                  keyword,
                  url,
                );
            } else {
              // For FAILED, INVALIDATED before SURFACING_PENDING
              // content payload should be empty
              action.contentPayload = {};
            }
          } else {
            // For FAILED, INVALIDATED before SURFACING_PENDING
            // content payload should be empty
            action.contentPayload = {};
          }
        } else {
          // No scraped page available, use minimal data
          action.contentPayload = {};
        }
      }

      action.generationPayload = generationPayload;

      const now = new Date();
      const msPerDay = 24 * 60 * 60 * 1000;

      // Apply workflow logic based on status
      switch (action.status) {
        case Status.DRAFT_PENDING:
          // No values for scheduled_to_be_*, surfaced_at, or published_at
          action.scheduledToBeSurfacedAt = null as any;
          action.surfacedAt = null as any;
          action.scheduledToBePublishedAt = null as any;
          action.publishedAt = null as any;
          break;

        case Status.SURFACING_PENDING:
          // scheduled_to_be_surfaced_at should be set and in the future (up to 7 days)
          action.scheduledToBeSurfacedAt = new Date(
            now.getTime() + Math.random() * 7 * msPerDay,
          );
          action.surfacedAt = null as any;
          action.scheduledToBePublishedAt = null as any;
          action.publishedAt = null as any;
          break;

        case Status.SURFACED: {
          // scheduled_to_be_surfaced_at and surfaced_at set for same date
          // scheduled_to_be_published_at set for 7 days after surfaced_at
          const surfacedDate = new Date(
            now.getTime() - Math.random() * 3 * msPerDay,
          ); // Surfaced in past 3 days
          action.scheduledToBeSurfacedAt = surfacedDate;
          action.surfacedAt = surfacedDate;
          action.scheduledToBePublishedAt = new Date(
            surfacedDate.getTime() + 7 * msPerDay,
          );
          action.publishedAt = null as any;
          break;
        }

        case Status.PUBLISHED: {
          // Same as surfaced but with published_at equal to scheduled_to_be_published_at
          const publishedSurfacedDate = new Date(
            now.getTime() - Math.random() * 10 * msPerDay,
          ); // Surfaced in past 10 days
          const publishedDate = new Date(
            publishedSurfacedDate.getTime() + 7 * msPerDay,
          );
          action.scheduledToBeSurfacedAt = publishedSurfacedDate;
          action.surfacedAt = publishedSurfacedDate;
          action.scheduledToBePublishedAt = publishedDate;
          action.publishedAt = publishedDate;
          break;
        }

        case Status.FAILED:
        case Status.INVALIDATED: {
          // Determine where in the workflow the action failed
          const failurePoint = Math.random();

          if (failurePoint < 0.4) {
            // Failed at scraper, keyword, or recommendations (early in pipeline)
            // Should have no date fields
            action.scheduledToBeSurfacedAt = null as any;
            action.surfacedAt = null as any;
            action.scheduledToBePublishedAt = null as any;
            action.publishedAt = null as any;
          } else if (failurePoint < 0.6) {
            // Failed at surfacing_pending stage
            // Should only have scheduled_to_be_surfaced_at
            action.scheduledToBeSurfacedAt = new Date(
              now.getTime() + Math.random() * 7 * msPerDay,
            );
            action.surfacedAt = null as any;
            action.scheduledToBePublishedAt = null as any;
            action.publishedAt = null as any;
          } else if (failurePoint < 0.8) {
            // Failed at surfaced stage
            // Should have scheduled_to_be_surfaced_at and surfaced_at
            const failedSurfacedDate = new Date(
              now.getTime() - Math.random() * 5 * msPerDay,
            );
            action.scheduledToBeSurfacedAt = failedSurfacedDate;
            action.surfacedAt = failedSurfacedDate;
            action.scheduledToBePublishedAt = null as any;
            action.publishedAt = null as any;
          } else {
            // Failed at published stage
            // Should have scheduled_to_be_surfaced_at, surfaced_at, and scheduled_to_be_published_at
            const failedSurfacedDate = new Date(
              now.getTime() - Math.random() * 10 * msPerDay,
            );
            const failedPublishDate = new Date(
              failedSurfacedDate.getTime() + 7 * msPerDay,
            );
            action.scheduledToBeSurfacedAt = failedSurfacedDate;
            action.surfacedAt = failedSurfacedDate;
            action.scheduledToBePublishedAt = failedPublishDate;
            action.publishedAt = null as any; // Never gets published since it failed
          }

          if (action.status === Status.FAILED) {
            let failureReasons: string[] = [];

            if (failurePoint < 0.4) {
              // Early pipeline failures
              failureReasons = [
                'Scraper timeout - could not fetch page content',
                'Invalid URL provided to scraper',
                'Keyword extraction failed - insufficient content',
                'Recommendation generation failed - AI service unavailable',
                'Content analysis error - page structure invalid',
              ];
            } else if (failurePoint < 0.6) {
              // Surfacing pending failures
              failureReasons = [
                'Surfacing queue timeout',
                'Database connection lost during surfacing',
                'Content validation failed before surfacing',
              ];
            } else if (failurePoint < 0.8) {
              // Surfaced stage failures
              failureReasons = [
                'Failed to prepare content for publishing',
                'Content review flagged issues',
                'Publishing queue initialization failed',
              ];
            } else {
              // Publishing stage failures
              failureReasons = [
                'Publishing API timeout',
                'Authentication failed with publishing service',
                'Content deployment verification failed',
                'Rate limit exceeded during publishing',
              ];
            }

            action.failureReason = {
              error:
                failureReasons[
                  Math.floor(Math.random() * failureReasons.length)
                ],
              timestamp: new Date().toISOString(),
              retryable: Math.random() > 0.5,
            };
          }
          break;
        }
      }

      return action;
    });

    const newActions =
      allActions.length > 0 ? await this.repo.save(scheduledActions) : [];
    const allCurrentActions = [...existingActions, ...newActions];

    // Log final status distribution
    const finalStatusCounts: Record<string, number> = {};
    allCurrentActions.forEach(action => {
      const status = action.status as string;
      finalStatusCounts[status] = (finalStatusCounts[status] || 0) + 1;
    });

    console.log('Final status distribution (all actions):', finalStatusCounts);

    // Create groups and link them to scheduled actions with SURFACING_PENDING, SURFACED, or PUBLISHED status
    const actionsNeedingGroups = allCurrentActions.filter(
      action =>
        action.status === Status.SURFACING_PENDING ||
        action.status === Status.SURFACED ||
        action.status === Status.PUBLISHED,
    );

    // Check existing group-scheduled-action links
    const existingLinks = await this.groupScheduledActionRepo.find();
    const linkedActionIds = new Set(
      existingLinks.map(link => link.scheduledActionId),
    );

    // Only create groups for actions that don't already have groups
    const actionsToCreateGroupsFor = actionsNeedingGroups.filter(
      action => !linkedActionIds.has(action.id),
    );

    if (actionsToCreateGroupsFor.length > 0) {
      console.log(
        `Creating ${actionsToCreateGroupsFor.length} groups for SURFACING_PENDING, SURFACED, and PUBLISHED scheduled actions...`,
      );

      // Fetch existing keywords to assign to groups
      const keywords = await this.groupRepo.manager.find('Keyword');

      // Load all page-keyword associations to ensure groups use the same keywords
      const pageKeywords = await this.pageKeywordRepo.find({
        relations: ['keyword'],
      });
      const pageKeywordMap = new Map(
        pageKeywords.map(pk => [pk.scrapedPageId, pk]),
      );

      // Create a mapping object to track which group belongs to which scheduled action
      const actionToGroupMapping: Record<
        string,
        { group: Partial<Group>; actionId: string }
      > = {};

      // Create groups for each scheduled action that needs one
      const groupsToCreate = actionsToCreateGroupsFor.map(action => {
        const group = DataFactory.createForClass(Group).generate(1)[0];

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { keywordId, ...groupWithoutKeyword } = group;

        // Find a scraped page from the action's generation payload if available
        let scrapedPageId: string | undefined;
        if (action.generationPayload?.scraperResult?.url) {
          const matchingPage = scrapedPages.find(
            page => page.url === action.generationPayload.scraperResult.url,
          );
          scrapedPageId = matchingPage?.id;
        }

        // If no scraped page found in payload, pick one for the same company
        if (!scrapedPageId) {
          const companyPages = scrapedPages.filter(
            page => page.companyId === action.companyId,
          );
          if (companyPages.length > 0) {
            scrapedPageId =
              companyPages[Math.floor(Math.random() * companyPages.length)].id;
          }
        }

        // Get the keyword that's already associated with this scraped page
        let selectedKeywordId: string | undefined;
        if (scrapedPageId) {
          const pageKeyword = pageKeywordMap.get(scrapedPageId);
          if (pageKeyword) {
            selectedKeywordId = pageKeyword.keywordId;
          } else {
            console.log(
              `Warning: No page-keyword association found for scraped page ${scrapedPageId}. Using random keyword.`,
            );
            // Fallback to random keyword if no association exists
            selectedKeywordId =
              keywords.length > 0
                ? keywords[Math.floor(Math.random() * keywords.length)].id
                : undefined;
          }
        } else {
          // No scraped page, use random keyword as fallback
          selectedKeywordId =
            keywords.length > 0
              ? keywords[Math.floor(Math.random() * keywords.length)].id
              : undefined;
        }

        const newGroup = {
          ...groupWithoutKeyword,
          companyId: action.companyId, // Use same companyId as the scheduled action
          keywordId: selectedKeywordId,
          scrapedPageId, // Set the scraped page ID
        };

        // Store the mapping between the group and its scheduled action
        // We'll use a unique identifier to track this relationship
        const mappingKey = `group_${(group.name as string) || 'default'}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        actionToGroupMapping[mappingKey] = {
          group: newGroup,
          actionId: action.id,
        };

        // Add the mapping key as a temporary property to track this group
        return { ...newGroup, _mappingKey: mappingKey };
      });

      // Save the groups
      const savedGroups = await this.groupRepo.save(groupsToCreate);

      // Create the joining table records using our mapping
      const groupScheduledActions = savedGroups.map(savedGroup => {
        // Get the mapping key from the saved group
        const mappingKey = (savedGroup as any)._mappingKey;
        // Get the corresponding scheduled action ID from our mapping
        const actionId = actionToGroupMapping[mappingKey].actionId;

        return {
          groupId: savedGroup.id,
          scheduledActionId: actionId,
        };
      });

      await this.groupScheduledActionRepo.save(groupScheduledActions);
      console.log(
        `Created ${savedGroups.length} groups and ${groupScheduledActions.length} group-scheduled-action links`,
      );
    } else {
      console.log(
        'All SURFACING_PENDING, SURFACED, and PUBLISHED actions already have groups, skipping group creation',
      );
    }

    // Note: contentPayload will be updated later by a separate seeder after recommendations are created

    console.log('Scheduled-action seeding complete!');
    return allCurrentActions;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
