import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { Repository } from 'typeorm';

@Injectable()
export class KeywordSeeder implements Seeder {
  constructor(
    @InjectRepository(Keyword) private readonly repo: Repository<Keyword>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting keyword seeding...');

    // Check existing keywords to avoid duplicates
    const existingKeywords = await this.repo.find();
    const existingKeywordValues = new Set(existingKeywords.map(k => k.keyword));

    console.log(`Found ${existingKeywords.length} existing keywords`);

    // If we already have enough keywords, skip seeding
    if (existingKeywords.length >= 20) {
      console.log(
        'Sufficient keywords already exist, skipping keyword seeding',
      );
      return existingKeywords;
    }

    // Generate unique keywords by using a Set to avoid duplicates
    const uniqueKeywords = new Set<string>(existingKeywordValues);
    const keywordData: Keyword[] = [];
    const targetCount = 20;

    while (uniqueKeywords.size < targetCount) {
      const randomKeywords = DataFactory.createForClass(Keyword).generate(
        30,
      ) as unknown as Keyword[];

      for (const kw of randomKeywords) {
        if (uniqueKeywords.size >= targetCount) break;
        if (!uniqueKeywords.has(kw.keyword)) {
          uniqueKeywords.add(kw.keyword);
          keywordData.push(kw);
        }
      }
    }

    // Only save new keywords (exclude existing ones)
    const newKeywords = keywordData.filter(
      kw => !existingKeywordValues.has(kw.keyword),
    );

    if (newKeywords.length === 0) {
      console.log('No new keywords to add');
      return existingKeywords;
    }

    const result = await this.repo.save(newKeywords);
    console.log(`Added ${result.length} new keywords`);
    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
