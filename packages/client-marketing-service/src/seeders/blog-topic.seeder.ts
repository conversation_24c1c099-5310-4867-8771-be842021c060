import * as fs from 'fs';
import * as path from 'path';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import csv from 'csv-parser';
import { Seeder } from 'nestjs-seeder';
import { BlogTopic } from 'src/blog-topic/entities/blog-topic.entity';
import { Repository } from 'typeorm';

import { companyData } from './company-data';

interface CsvBlogTopic {
  Location: string;
  Topic: string;
  'Blog Title': string;
  Rationale: string;
  Writer: string;
  '1st Draft': string;
  'Fact Checker': string;
  '2nd Draft': string;
  'Image Gen': string;
  Image: string;
}

@Injectable()
export class BlogTopicSeeder implements Seeder {
  constructor(
    @InjectRepository(BlogTopic)
    private readonly repo: Repository<BlogTopic>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting blog topic seeding...');

    // Check existing topics to avoid duplicates - select only needed columns
    const existingTopics = await this.repo
      .createQueryBuilder('blogTopic')
      .select(['blogTopic.companyId', 'blogTopic.topic'])
      .getRawMany();

    // Create composite dedupe keys combining companyId and normalized topic
    const existingTopicKeys = new Set(
      existingTopics.map(
        t =>
          `${t.blogTopic_companyId}|${t.blogTopic_topic.trim().toLowerCase()}`,
      ),
    );

    // Read CSV file
    const csvFilePath = path.join(__dirname, 'csv/blog-topics.csv');
    const csvData: CsvBlogTopic[] = [];

    try {
      const fileStream = fs.createReadStream(csvFilePath);
      await new Promise<void>((resolve, reject) => {
        fileStream
          .pipe(csv())
          .on('data', (row: CsvBlogTopic) => {
            // Filter out rows with empty topics
            if (row.Topic && row.Topic.trim()) {
              csvData.push(row);
            }
          })
          .on('end', () => resolve())
          .on('error', reject);
      });
    } catch (error) {
      console.error('Error reading CSV file:', error);
      return [];
    }

    console.log(`Read ${csvData.length} topics from CSV`);

    // Create blog topics from CSV data
    const blogTopics: Partial<BlogTopic>[] = [];

    for (const csvRow of csvData) {
      // Assign to a company (round-robin distribution)
      const companyIndex = blogTopics.length % companyData.length;
      const company = companyData[companyIndex];

      // Create composite dedupe key for this topic
      const normalizedTopic = csvRow.Topic.trim().toLowerCase();
      const compositeKey = `${company.id}|${normalizedTopic}`;

      // Skip if topic already exists for this company (case-insensitive, whitespace-insensitive)
      if (existingTopicKeys.has(compositeKey)) {
        continue;
      }

      const blogTopic: Partial<BlogTopic> = {
        companyId: company.id,
        topic: csvRow.Topic,
        rationale:
          csvRow.Rationale || 'Generated from SuperBloom topics export',
        // Set nullable fields to null as requested
        blogPostJobId: null,
        airopsExecutionId: null,
        cmsPostId: null,
        neighborhoodId: null,
        parentTopic: null,
        blogTitle: csvRow['Blog Title'],
      };

      blogTopics.push(blogTopic);
    }

    if (blogTopics.length === 0) {
      console.log('No new blog topics to add');
      return existingTopics;
    }

    // Upsert to database - will insert new topics or update existing ones
    const result = await this.repo.upsert(blogTopics, {
      conflictPaths: ['companyId', 'topic'],
      skipUpdateIfNoValuesChanged: true,
    });
    console.log(`Upserted ${blogTopics.length} blog topics`);
    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
