import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { Group } from 'src/group/entities/group.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import { Repository } from 'typeorm';

@Injectable()
export class RecommendationSeeder implements Seeder {
  constructor(
    @InjectRepository(Recommendation)
    private readonly repo: Repository<Recommendation>,
    @InjectRepository(Group)
    private readonly groupRepo: Repository<Group>,
    @InjectRepository(Scrape)
    private readonly scrapeRepo: Repository<Scrape>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting recommendation seeding...');

    // Load groups with their scraped page relationship
    const groups = await this.groupRepo.find({
      relations: ['scrapedPage'],
    });
    const scrapes = await this.scrapeRepo.find();

    if (groups.length === 0) {
      console.log('No groups found, skipping recommendation seeding');
      return [];
    }

    // Create one recommendation of each type per group
    const recommendationTypes = Object.values(RecommendationType);
    console.log(
      `Creating ${recommendationTypes.length} recommendations per group for ${groups.length} groups...`,
    );

    const allRecommendations: any[] = [];

    for (const group of groups) {
      // Find scrapes for the same scraped page (ensuring data integrity)
      const scrapesForPage = scrapes.filter(
        s => s.scrapedPageId === group.scrapedPageId,
      );

      if (scrapesForPage.length === 0) {
        console.log(
          `No scrapes found for scraped page ${group.scrapedPageId}, using company fallback`,
        );
        // Fallback to company-based matching for backward compatibility
        const scrapeForCompany = scrapes.find(
          s => s.companyId === group.companyId,
        );
        if (scrapeForCompany) {
          scrapesForPage.push(scrapeForCompany);
        }
      }

      // Use the most recent scrape for the page
      const selectedScrape = scrapesForPage.sort(
        (a, b) =>
          new Date(b.scrapedAt).getTime() - new Date(a.scrapedAt).getTime(),
      )[0];

      // Create one recommendation for each type
      for (const recType of recommendationTypes) {
        const randomRec =
          DataFactory.createForClass(Recommendation).generate(1)[0];

        // Override the type to ensure we get one of each
        randomRec.type = recType;

        // Set rejection reason only if status is REJECTED
        const rejectionReason =
          randomRec.status === 'REJECTED'
            ? [
                'Not relevant to target audience',
                'Content quality concerns',
                'Brand guidelines violation',
                'Technical implementation issues',
              ][Math.floor(Math.random() * 4)]
            : null;

        // Set realistic status updated time
        const statusUpdatedAt =
          randomRec.status === 'PENDING'
            ? new Date() // Just created
            : new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000); // Updated within past week

        // Get company name from group metadata or title for more realistic content
        const companyName = this.extractCompanyName(
          group.title || group.description,
        );

        // Set type-specific content with company context
        const typeSpecificContent = this.getTypeSpecificContent(
          recType,
          companyName,
        );

        allRecommendations.push({
          ...randomRec,
          type: recType,
          groupId: group.id,
          scrapeId: selectedScrape?.id || null,
          rejectionReason,
          statusUpdatedAt,
          currentValue: typeSpecificContent.currentValue,
          recommendationValue: typeSpecificContent.recommendationValue,
          reasoning: typeSpecificContent.reasoning,
        });
      }
    }

    const result = await this.repo.save(allRecommendations);
    console.log('Recommendation seeding complete!');
    return result;
  }

  private extractCompanyName(groupText: string): string {
    // Extract company name from group title (format: "Title - Company Name")
    const parts = groupText.split(' - ');
    if (parts.length > 1) {
      return parts[parts.length - 1]; // Get the last part as company name
    }

    // Fallback to known company names if extraction fails
    const knownCompanies = [
      'Luxury Real Estate Partners',
      'Metro Homes Realty',
      'Coastal Properties Group',
    ];

    for (const company of knownCompanies) {
      if (groupText.includes(company)) {
        return company;
      }
    }

    return 'Real Estate Company'; // Default fallback
  }

  private getTypeSpecificContent(
    type: RecommendationType,
    companyName?: string,
  ) {
    const company = companyName || 'Real Estate Company';

    switch (type) {
      case RecommendationType.META_TITLE:
        return {
          currentValue: `Home | ${company}`,
          recommendationValue: `${company} - Premier Real Estate Services & Expert Agents`,
          reasoning: `Current title is too generic and lacks compelling keywords. Recommended title includes the company brand, service keywords, and value proposition to improve click-through rates and brand recognition in search results.`,
        };

      case RecommendationType.META_DESCRIPTION:
        return {
          currentValue: `Welcome to ${company}. We are a real estate company.`,
          recommendationValue: `Discover your dream home with ${company}'s expert agents. Browse premium listings, get personalized market insights, and experience exceptional real estate service. Free consultation available.`,
          reasoning: `Current description lacks compelling call-to-action and key benefits. Recommended description highlights specific services, includes relevant keywords, and has a clear CTA within optimal 150-160 character length for better search snippet performance.`,
        };

      case RecommendationType.MAIN_HEADING:
        return {
          currentValue: 'Welcome to Our Website',
          recommendationValue: `Your Trusted Real Estate Partners - ${company}`,
          reasoning: `Current heading is generic and not SEO-optimized. Recommended heading establishes trust and authority, includes company branding, and clearly communicates the business focus to improve user engagement and search relevance.`,
        };

      default:
        return {
          currentValue: `Current ${company} content`,
          recommendationValue: `Optimized ${company} content for better SEO`,
          reasoning: `This content should be optimized with company-specific messaging and relevant keywords for better SEO performance and brand consistency.`,
        };
    }
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
