import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Seeder } from 'nestjs-seeder';
import { PageKeywordHistory } from 'src/page-keyword-history/entities/page-keyword-history.entity';
import { Repository } from 'typeorm';

@Injectable()
export class Page<PERSON>eywordHistorySeeder implements Seeder {
  constructor(
    @InjectRepository(PageKeywordHistory)
    private readonly repo: Repository<PageKeywordHistory>,
  ) {}

  async seed(): Promise<any> {
    // This table is populated automatically by database triggers
    // No manual seeding needed
    console.log('Page-keyword-history populated automatically by triggers');
    return Promise.resolve([]);
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
