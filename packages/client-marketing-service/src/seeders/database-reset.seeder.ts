import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { Seeder } from 'nestjs-seeder';
import { DataSource } from 'typeorm';

@Injectable()
export class DatabaseResetSeeder implements Seeder {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting complete database reset...');
    return this.clearAllTables();
  }

  async drop(): Promise<any> {
    // Do nothing - we only want to clear tables in seed method
    return Promise.resolve([]);
  }

  /**
   * Clears all tables in reverse dependency order to avoid foreign key constraint issues
   * @returns Empty array on success
   */
  private async clearAllTables(): Promise<any> {
    try {
      // Tables are cleared in reverse dependency order to avoid foreign key constraint issues
      const tablesToClear = [
        'crew_ai_seo_automation.page_keyword_history',
        'crew_ai_seo_automation.recommendation',
        'crew_ai_seo_automation.page_keyword',
        'crew_ai_seo_automation.scrape',
        'crew_ai_seo_automation.group_scheduled_action',
        'crew_ai_seo_automation.group',
        'crew_ai_seo_automation.scheduled_action',
        'crew_ai_seo_automation.scraped_page',
        'crew_ai_seo_automation.keyword',
      ];

      console.log('Clearing all tables in dependency order...');
      for (const table of tablesToClear) {
        console.log(`Clearing ${table}...`);
        await this.dataSource.query(
          `TRUNCATE TABLE ${table} RESTART IDENTITY CASCADE`,
        );
      }

      console.log('Database reset complete!');
      return [];
    } catch (error) {
      console.error('Error during database reset:', error);
      throw error;
    }
  }
}
