import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { Repository } from 'typeorm';

import { companyData } from './company-data';

@Injectable()
export class PageSeeder implements Seeder {
  constructor(
    @InjectRepository(ScrapedPage)
    private readonly repo: Repository<ScrapedPage>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting page seeding...');

    // Check existing pages to avoid URL duplicates
    const existingPages = await this.repo.find();
    const existingUrls = new Set(existingPages.map(p => p.url));

    console.log(`Found ${existingPages.length} existing pages`);

    const pageTemplates = [
      { path: '', name: 'Homepage', type: 'HOMEPAGE' },
      { path: 'valuation', name: 'Home Valuation', type: 'HOME_VALUATION' },
      {
        path: 'calculator',
        name: 'Mortgage Calculator',
        type: 'MORTGAGE_CALCULATOR',
      },
      { path: 'agents', name: 'Our Agents', type: 'AGENT_BIO' },
      { path: 'buyers-guide', name: 'Buyers Guide', type: 'BUYERS_GUIDE' },
      { path: 'sellers-guide', name: 'Sellers Guide', type: 'SELLERS_GUIDE' },
      {
        path: 'neighborhoods',
        name: 'Neighborhood Guide',
        type: 'NEIGHBORHOOD_GUIDE',
      },
      { path: 'blog', name: 'Real Estate Blog', type: 'BLOG' },
    ];

    const allPages: any[] = [];

    // Create 5 pages per company using realistic templates
    companyData.forEach(company => {
      pageTemplates.forEach(template => {
        const url = `https://${company.domain}/${template.path}`;

        // Skip if URL already exists
        if (existingUrls.has(url)) {
          console.log(`Skipping existing page: ${url}`);
          return;
        }

        const randomPage =
          DataFactory.createForClass(ScrapedPage).generate(1)[0];
        allPages.push({
          ...randomPage,
          companyId: company.id,
          url: url,
          pageName: `${template.name} - ${company.name}`,
          pageType: template.type,
        });
      });
    });

    if (allPages.length === 0) {
      console.log('No new pages to add (all URLs already exist)');
      return existingPages;
    }

    const result = await this.repo.save(allPages);
    console.log(`Added ${result.length} new pages`);
    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
