import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { Repository } from 'typeorm';

@Injectable()
export class PageKeywordSeeder implements Seeder {
  constructor(
    @InjectRepository(PageKeyword)
    private readonly repo: Repository<PageKeyword>,
    @InjectRepository(ScrapedPage)
    private readonly scrapedPageRepo: Repository<ScrapedPage>,
    @InjectRepository(Keyword)
    private readonly keywordRepo: Repository<Keyword>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting page-keyword seeding...');

    const pages = await this.scrapedPageRepo.find();
    const keywords = await this.keywordRepo.find();

    // Check existing page-keyword relationships
    const existingPageKeywords = await this.repo.find();
    const existingCombinations = new Set(
      existingPageKeywords.map(pk => `${pk.scrapedPageId}-${pk.keywordId}`),
    );

    console.log(
      `Found ${existingPageKeywords.length} existing page-keyword relationships`,
    );

    if (pages.length === 0 || keywords.length === 0) {
      console.log('No pages or keywords found, skipping page-keyword seeding');
      return [];
    }

    // Check if we already have page-keyword records for all pages
    const pagesWithKeywords = new Set(
      existingPageKeywords.map(pk => pk.scrapedPageId),
    );
    const pagesNeedingKeywords = pages.filter(
      p => !pagesWithKeywords.has(p.id),
    );

    if (pagesNeedingKeywords.length === 0) {
      console.log(
        'All pages already have keyword relationships, skipping creation',
      );
      return existingPageKeywords;
    }

    // Create exactly one page_keyword record per page that doesn't have one
    console.log(
      `Creating page-keyword records for ${pagesNeedingKeywords.length} pages...`,
    );

    // Track used page-keyword combinations to avoid duplicates (including existing ones)
    const usedCombinations = new Set<string>(existingCombinations);
    const pageKeywordData: any[] = [];

    for (const page of pagesNeedingKeywords) {
      const randomPageKeyword =
        DataFactory.createForClass(PageKeyword).generate(1)[0];

      // Find an unused keyword for this page
      let selectedKeyword: any;
      let attempts = 0;
      const maxAttempts = keywords.length;

      do {
        selectedKeyword = keywords[Math.floor(Math.random() * keywords.length)];
        const combination = `${page.id}-${selectedKeyword.id}`;

        if (!usedCombinations.has(combination)) {
          usedCombinations.add(combination);
          break;
        }

        attempts++;
      } while (attempts < maxAttempts);

      // If we couldn't find an unused combination, skip this page
      if (attempts >= maxAttempts) {
        console.log(
          `Warning: Could not find unique keyword for page ${page.id}`,
        );
        continue;
      }

      pageKeywordData.push({
        ...randomPageKeyword,
        scrapedPageId: page.id,
        keywordId: selectedKeyword.id,
      });
    }

    // Save initial page-keyword records if we have any new ones
    const initialRecords: any[] = [];
    if (pageKeywordData.length > 0) {
      // Save records one by one with a small delay to ensure triggers work properly
      console.log(
        `Creating ${pageKeywordData.length} initial page-keyword records...`,
      );
      for (const pageKeyword of pageKeywordData) {
        const saved = await this.repo.save(pageKeyword);
        initialRecords.push(saved);
        // Small delay to ensure database triggers complete
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      console.log(
        `Created ${initialRecords.length} initial page-keyword records`,
      );
    } else {
      console.log('No new page-keyword records to create');
    }

    // Combine existing and new records for update processing
    const allRecords = [...existingPageKeywords, ...initialRecords];

    // Wait a moment to ensure different timestamps
    await new Promise(resolve => setTimeout(resolve, 100));

    // Create keyword change history for some pages
    // Select up to 5 records to have keyword changes over time
    const recordsToUpdate = Math.min(5, Math.floor(allRecords.length * 0.3)); // Update up to 5 records or 30% of records
    console.log(
      `Creating keyword change history for ${recordsToUpdate} pages...`,
    );

    const changeScenarios = [
      {
        changeNote:
          'SEO team updated target keyword after competitive analysis',
        updatedBy: '550e8400-e29b-41d4-a716-446655440999',
      },
      {
        changeNote: 'Keyword changed due to low search volume performance',
        updatedBy: '550e8400-e29b-41d4-a716-446655441000',
      },
      {
        changeNote: 'Updated to focus on higher converting long-tail keyword',
        updatedBy: '550e8400-e29b-41d4-a716-446655441001',
      },
      {
        changeNote: 'Quarterly keyword optimization based on analytics data',
        updatedBy: '550e8400-e29b-41d4-a716-446655441002',
      },
      {
        changeNote: 'Keyword refined based on competitor research',
        updatedBy: '550e8400-e29b-41d4-a716-446655441003',
      },
    ];

    // Maximum number of keyword changes per page
    const maxChangesPerPage = 3;

    // Shuffle records and take the first few to ensure we don't update the same page multiple times
    const shuffledRecords = [...allRecords].sort(() => Math.random() - 0.5);
    const recordsToProcess = shuffledRecords.slice(0, recordsToUpdate);

    for (let i = 0; i < recordsToProcess.length; i++) {
      const recordToUpdate = recordsToProcess[i];

      // Determine how many keyword changes this page should have (1-3)
      const numberOfChanges = Math.floor(Math.random() * maxChangesPerPage) + 1;

      // Track current keyword for this page
      let currentKeywordId = recordToUpdate.keywordId;

      for (let changeNum = 0; changeNum < numberOfChanges; changeNum++) {
        // Find a different keyword that won't create a duplicate combination
        const availableKeywords = keywords.filter(k => {
          const combination = `${recordToUpdate.scrapedPageId}-${k.id}`;
          return (
            k.id !== currentKeywordId && !usedCombinations.has(combination)
          );
        });

        if (availableKeywords.length === 0) {
          console.log(
            `Warning: No available keywords for updating page ${recordToUpdate.scrapedPageId}`,
          );
          break;
        }

        const newKeyword =
          availableKeywords[
            Math.floor(Math.random() * availableKeywords.length)
          ];
        const scenario =
          changeScenarios[Math.floor(Math.random() * changeScenarios.length)];

        // Update the used combinations tracking
        const oldCombination = `${recordToUpdate.scrapedPageId}-${currentKeywordId}`;
        const newCombination = `${recordToUpdate.scrapedPageId}-${newKeyword.id}`;
        usedCombinations.delete(oldCombination);
        usedCombinations.add(newCombination);

        // Execute the session variable setting and update within a single transaction
        const newRank = Math.floor(Math.random() * 100) + 1;

        await this.repo.manager.transaction(
          async transactionalEntityManager => {
            // Set PostgreSQL session variables for audit tracking
            await transactionalEntityManager.query(
              'SELECT set_config($1, $2, true)',
              ['app.change_note', scenario.changeNote],
            );
            await transactionalEntityManager.query(
              'SELECT set_config($1, $2, true)',
              ['app.updated_by', scenario.updatedBy],
            );

            // Update the record - this will trigger the history tracking
            await transactionalEntityManager.update(
              PageKeyword,
              recordToUpdate.id,
              {
                keywordId: newKeyword.id,
                currentRank: newRank,
              },
            );
          },
        );

        console.log(
          `Updated page ${recordToUpdate.scrapedPageId} from keyword ${currentKeywordId} to ${newKeyword.id} - ${scenario.changeNote}`,
        );

        // Update current keyword for next iteration
        currentKeywordId = newKeyword.id;

        // Wait 2 seconds between updates to ensure different timestamps in the database
        // This is important because the trigger uses NOW() which needs actual time to pass
        console.log('  Waiting 2 seconds for timestamp differentiation...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    const result = allRecords;
    console.log('Page-keyword seeding complete!');
    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
