import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { Scrape } from 'src/scrape/entities/scrape.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ScrapeSeeder implements Seeder {
  constructor(
    @InjectRepository(Scrape) private readonly repo: Repository<Scrape>,
    @InjectRepository(ScrapedPage)
    private readonly scrapedPageRepo: Repository<ScrapedPage>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting scrape seeding...');

    const pages = await this.scrapedPageRepo.find();
    const existingScrapes = await this.repo.find();

    console.log(`Found ${existingScrapes.length} existing scrapes`);

    if (pages.length === 0) {
      console.log('No pages found, skipping scrape seeding');
      return [];
    }

    // Check how many scrapes we need based on existing data
    const targetTotalScrapes = 100; // Increased to ensure multiple scrapes per page
    const currentScrapeCount = existingScrapes.length;
    const scrapesToAdd = Math.max(0, targetTotalScrapes - currentScrapeCount);

    if (scrapesToAdd === 0) {
      console.log('Already have sufficient scrapes, skipping scrape creation');
      return existingScrapes;
    }

    console.log(`Creating ${scrapesToAdd} additional scrapes...`);

    // Create 3-5 scrapes per page for better temporal data
    const minScrapesPerPage = 3;
    const maxScrapesPerPage = 5;

    const allScrapes: any[] = [];
    let totalScrapesCreated = 0;

    // Create scrapes systematically for better distribution
    for (const page of pages) {
      if (totalScrapesCreated >= scrapesToAdd) break;

      // Determine number of scrapes for this page
      const existingScrapesForPage = existingScrapes.filter(
        s => s.scrapedPageId === page.id,
      ).length;
      const targetScrapesForPage =
        Math.floor(
          Math.random() * (maxScrapesPerPage - minScrapesPerPage + 1),
        ) + minScrapesPerPage;
      const scrapesToCreate = Math.max(
        0,
        Math.min(
          targetScrapesForPage - existingScrapesForPage,
          scrapesToAdd - totalScrapesCreated,
        ),
      );

      for (let i = 0; i < scrapesToCreate; i++) {
        const randomScrape = DataFactory.createForClass(Scrape).generate(1)[0];

        // Generate company-specific content based on page
        const companyName =
          page.pageName.split(' - ')[1] || 'Real Estate Company';
        const pageType = page.pageType || 'HOMEPAGE';

        // Calculate scraped_at date - spread scrapes over the last 30 days
        const daysAgo = Math.floor(Math.random() * 30);
        const scrapedAt = new Date();
        scrapedAt.setDate(scrapedAt.getDate() - daysAgo);
        scrapedAt.setHours(
          Math.floor(Math.random() * 24),
          Math.floor(Math.random() * 60),
          Math.floor(Math.random() * 60),
        );

        // Add variation index for content generation
        const contentVariation = i + existingScrapesForPage;

        allScrapes.push({
          ...randomScrape,
          scrapedPageId: page.id,
          companyId: page.companyId,
          scrapedAt: scrapedAt,
          rawHtml: this.generateRealisticHtml(
            companyName,
            pageType,
            page.url,
            contentVariation,
          ),
          markdown: this.generateRealisticMarkdown(
            companyName,
            pageType,
            contentVariation,
          ),
          currentScrapedValues: this.generateRealisticScrapedValues(
            companyName,
            pageType,
            contentVariation,
          ),
        });

        totalScrapesCreated++;
      }
    }

    if (allScrapes.length === 0) {
      console.log('No new scrapes to add');
      return existingScrapes;
    }

    const result = await this.repo.save(allScrapes);
    console.log(`Added ${result.length} new scrapes`);
    return [...existingScrapes, ...result];
  }

  private generateRealisticHtml(
    companyName: string,
    pageType: string,
    url: string,
    variation: number = 0,
  ): string {
    const pageTypeContent = this.getPageTypeContent(
      companyName,
      pageType,
      variation,
    );

    // Add variations to the HTML
    const additionalMeta =
      variation > 0
        ? `
  <meta name="revised" content="${new Date().toISOString()}">
  <meta property="og:updated_time" content="${new Date().toISOString()}">`
        : '';

    const additionalContent =
      variation > 1
        ? `
    <section>
      <h2>Recent Updates</h2>
      <p>We've recently updated our ${pageType === 'HOMEPAGE' ? 'services' : 'information'} to better serve you.</p>
    </section>`
        : '';

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <title>${pageTypeContent.title}</title>
  <meta name="description" content="${pageTypeContent.description}">
  <meta property="og:title" content="${pageTypeContent.title}">
  <meta property="og:description" content="${pageTypeContent.description}">
  <meta property="og:url" content="${url}">${additionalMeta}
</head>
<body>
  <header>
    <nav>
      <a href="/">Home</a>
      <a href="/listings">Listings</a>
      <a href="/about">About</a>
      <a href="/contact">Contact</a>
    </nav>
  </header>
  <main>
    <h1>${pageTypeContent.h1}</h1>
    <p>${pageTypeContent.content}</p>${additionalContent}
  </main>
  <footer>
    <p>&copy; 2024 ${companyName}. All rights reserved.</p>
  </footer>
</body>
</html>`;
  }

  private generateRealisticMarkdown(
    companyName: string,
    pageType: string,
    variation: number = 0,
  ): string {
    const pageTypeContent = this.getPageTypeContent(
      companyName,
      pageType,
      variation,
    );

    // Add variations to markdown content
    const phoneVariations = [
      '(*************',
      '(*************',
      '(*************',
    ];
    const phone = phoneVariations[variation % phoneVariations.length];

    const additionalServices =
      variation > 0
        ? `
- Investment Consulting
- Market Trends Analysis`
        : '';

    const lastUpdated =
      variation > 1
        ? `

*Last updated: ${new Date().toLocaleDateString()}*`
        : '';

    return `# ${pageTypeContent.h1}

${pageTypeContent.content}

## Contact Information
- Phone: ${phone}
- Email: info@${companyName.toLowerCase().replace(/\s+/g, '')}.com

## Services
- Residential Real Estate
- Commercial Properties
- Property Management
- Market Analysis${additionalServices}${lastUpdated}`;
  }

  private generateRealisticScrapedValues(
    companyName: string,
    pageType: string,
    variation: number = 0,
  ): Record<string, string> {
    const pageTypeContent = this.getPageTypeContent(
      companyName,
      pageType,
      variation,
    );
    return {
      metaTitle: pageTypeContent.title,
      metaDescription: pageTypeContent.description,
      h1: pageTypeContent.h1,
    };
  }

  private getPageTypeContent(
    companyName: string,
    pageType: string,
    variation: number = 0,
  ) {
    // Add variations to titles and descriptions
    const titleVariations = ['Premier', 'Professional', 'Trusted', 'Leading'];
    const titleVariant = titleVariations[variation % titleVariations.length];

    const yearsVariations = ['20', '25', '30', '15'];
    const years = yearsVariations[variation % yearsVariations.length];

    switch (pageType) {
      case 'HOMEPAGE':
        return {
          title: `${companyName} - ${titleVariant} Real Estate Services`,
          description: `Discover your dream home with ${companyName}. ${variation > 0 ? 'Award-winning' : 'Expert'} real estate agents, comprehensive property listings, and personalized service.`,
          h1: `Welcome to ${companyName}`,
          content: `Find your perfect home with our ${variation > 1 ? 'award-winning' : 'expert'} real estate team. We offer comprehensive services including buying, selling, and property management${variation > 0 ? ', plus investment consulting' : ''}.`,
        };
      case 'ABOUT':
        return {
          title: `About ${companyName} - ${variation > 0 ? 'Award-Winning' : 'Experienced'} Real Estate Professionals`,
          description: `Learn about ${companyName}'s experienced team of real estate professionals committed to exceptional service and ${variation > 1 ? 'award-winning' : 'proven'} results.`,
          h1: `About ${companyName}`,
          content: `Our experienced team has been serving the community for over ${years} years, providing exceptional real estate services with integrity and expertise${variation > 0 ? '. Winner of multiple industry awards' : ''}.`,
        };
      case 'HOME_VALUATION':
        return {
          title: `Free Home Valuation - ${companyName}`,
          description: `Get your ${variation > 0 ? 'instant' : 'free'} home valuation from ${companyName}. Accurate property values and ${variation > 1 ? 'AI-powered' : 'detailed'} market analysis from local experts.`,
          h1: `${variation > 0 ? 'Advanced' : 'Free'} Home Valuation Tool`,
          content: `Discover your home's current market value with our ${variation > 1 ? 'AI-enhanced' : 'comprehensive'} valuation tool. Get instant estimates and detailed market analysis${variation > 0 ? ', plus trend predictions' : ''}.`,
        };
      case 'MORTGAGE_CALCULATOR':
        return {
          title: `${variation > 0 ? 'Advanced' : ''} Mortgage Calculator - ${companyName}`,
          description: `Calculate your mortgage payments with ${companyName}'s ${variation > 1 ? 'advanced' : 'easy-to-use'} calculator. Estimate rates, payments, and ${variation > 0 ? 'total costs' : 'affordability'}.`,
          h1: `${variation > 0 ? 'Advanced' : ''} Mortgage Payment Calculator`,
          content: `Plan your home purchase with our ${variation > 1 ? 'sophisticated' : 'mortgage'} calculator. Get accurate payment estimates and explore ${variation > 0 ? 'multiple' : 'different'} loan scenarios${variation > 1 ? ' with amortization schedules' : ''}.`,
        };
      case 'AGENT_BIO':
        return {
          title: `Our ${variation > 0 ? 'Award-Winning' : ''} Real Estate Agents - ${companyName}`,
          description: `Meet our ${variation > 1 ? 'top-rated' : 'professional'} real estate agents at ${companyName}. ${variation > 0 ? 'Award-winning,' : 'Experienced,'} dedicated, and ready to help you achieve your goals.`,
          h1: `Meet Our ${variation > 0 ? 'Award-Winning' : 'Expert'} Agents`,
          content: `Our ${variation > 1 ? 'top-performing' : 'certified'} agents bring ${variation > 0 ? 'decades of combined' : 'years of'} experience and local market knowledge to help you buy or sell with confidence${variation > 1 ? '. Recognized leaders in the industry' : ''}.`,
        };
      case 'BUYERS_GUIDE':
        return {
          title: `${variation > 0 ? 'Ultimate' : ''} Home Buying Guide - ${companyName}`,
          description: `${variation > 1 ? 'The ultimate' : 'Complete'} home buying guide from ${companyName}. Learn the process, ${variation > 0 ? 'insider' : ''} tips, and strategies for successful home purchases.`,
          h1: `Your ${variation > 0 ? 'Ultimate' : 'Complete'} Home Buying Guide`,
          content: `Navigate the home buying process with ${variation > 1 ? 'expert' : ''} confidence. Our ${variation > 0 ? 'comprehensive, updated' : 'comprehensive'} guide covers everything from pre-approval to closing${variation > 1 ? ', plus negotiation tactics' : ''}.`,
        };
      default:
        return {
          title: `${companyName} - ${titleVariant} Real Estate Services`,
          description: `${titleVariant} real estate services with ${companyName}. Your ${variation > 0 ? 'award-winning' : 'trusted'} partner in buying, selling, and property management.`,
          h1: `${titleVariant} Real Estate Services`,
          content: `${titleVariant} real estate services tailored to your needs${variation > 0 ? ', backed by industry recognition' : ''}. Contact us today to learn more about how we can help${variation > 1 ? ' achieve your real estate goals' : ''}.`,
        };
    }
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
