import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Seeder } from 'nestjs-seeder';
import { BrandProfile } from 'src/brand-profile/entities/brand-profile.entity';
import { Repository } from 'typeorm';

import { companyData } from './company-data';

@Injectable()
export class BrandProfileSeeder implements Seeder {
  constructor(
    @InjectRepository(BrandProfile)
    private readonly repo: Repository<BrandProfile>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting brand profile seeding...');

    const existingProfiles = await this.repo.find();
    const existingCompanyIds = new Set(
      existingProfiles.map(profile => profile.companyId),
    );

    console.log(`Found ${existingProfiles.length} existing brand profiles`);

    const newProfiles: Partial<BrandProfile>[] = [];

    for (const company of companyData) {
      if (!existingCompanyIds.has(company.id)) {
        const brandProfile: Partial<BrandProfile> = {
          companyId: company.id,
          aboutTheBrand: `${company.name} is a premier real estate company specializing in ${company.focus} properties. We are committed to providing exceptional service and expertise to our clients in the ${company.domain.replace('.com', '').replace('.homes', '')} market.`,
          strategicFocus: `Our strategic focus is on ${company.focus} real estate, leveraging market insights and innovative marketing strategies to deliver outstanding results for our clients.`,
          valueProposition: `We offer unparalleled expertise in ${company.focus} real estate, combining local market knowledge with cutting-edge technology to provide our clients with a competitive advantage in buying and selling properties.`,
          idealCustomerProfiles: `Our ideal customers are discerning buyers and sellers in the ${company.focus} real estate market who value professional expertise, personalized service, and proven results.`,
          missionAndCoreValues: `Our mission is to transform the real estate experience through integrity, innovation, and exceptional service. We value transparency, professionalism, and building lasting relationships with our clients.`,
          brandPointOfView: `We believe that real estate is more than just transactions - it's about helping people achieve their dreams and build their futures. Every property has a story, and we're here to help write the next chapter.`,
          toneOfVoice: `Professional yet approachable, knowledgeable and trustworthy. We communicate with confidence while maintaining warmth and accessibility in all our interactions.`,
          ctaText: `Contact us today to discover how we can help you achieve your real estate goals.`,
          authorPersona: `Experienced real estate professional with deep local market knowledge and a passion for helping clients navigate their property journey with confidence and success.`,
        };

        newProfiles.push(brandProfile);
      }
    }

    if (newProfiles.length === 0) {
      console.log('No new brand profiles to add');
      return existingProfiles;
    }

    console.log(`Creating ${newProfiles.length} new brand profiles...`);
    const result = await this.repo.save(newProfiles);
    console.log(`Added ${result.length} new brand profiles`);
    console.log('Brand profile seeding complete!');

    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
