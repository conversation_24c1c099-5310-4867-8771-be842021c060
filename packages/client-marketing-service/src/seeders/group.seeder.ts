import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataFactory, Seeder } from 'nestjs-seeder';
import { Group } from 'src/group/entities/group.entity';
import { Keyword } from 'src/keyword/entities/keyword.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { ScrapedPage } from 'src/scraped-page/entities/scraped-page.entity';
import { Repository } from 'typeorm';

import { companyData } from './company-data';

@Injectable()
export class GroupSeeder implements Seeder {
  constructor(
    @InjectRepository(Group) private readonly repo: Repository<Group>,
    @InjectRepository(Keyword)
    private readonly keywordRepo: Repository<Keyword>,
    @InjectRepository(ScrapedPage)
    private readonly scrapedPageRepo: Repository<ScrapedPage>,
    @InjectRepository(PageKeyword)
    private readonly pageKeywordRepo: Repository<PageKeyword>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting group seeding...');

    // Check existing groups
    const existingGroups = await this.repo.find();
    const existingGroupTitles = new Set(existingGroups.map(g => g.title));

    console.log(`Found ${existingGroups.length} existing groups`);

    const scrapedPages = await this.scrapedPageRepo.find();

    if (scrapedPages.length === 0) {
      console.log('No scraped pages found, cannot create groups');
      return [];
    }

    // Load all page-keyword associations to ensure groups use the same keywords
    const pageKeywords = await this.pageKeywordRepo.find({
      relations: ['keyword'],
    });
    const pageKeywordMap = new Map(
      pageKeywords.map(pk => [pk.scrapedPageId, pk]),
    );

    // Map page types to SEO focus areas
    const pageTypeTemplates = {
      HOMEPAGE: {
        actions: [
          'SEO Optimization',
          'Content Strategy',
          'Visibility Enhancement',
        ],
        priority: 'high',
        targetKeywords: [
          'real estate agent',
          'homes for sale',
          'property listings',
        ],
        descriptionTemplate:
          'Comprehensive {action} for {company} homepage to improve organic search visibility and user engagement',
      },
      HOME_VALUATION: {
        actions: [
          'Conversion Optimization',
          'Lead Generation Strategy',
          'SEO Enhancement',
        ],
        priority: 'high',
        targetKeywords: ['home value', 'property valuation', 'house worth'],
        descriptionTemplate:
          'Targeted {action} for {company} home valuation tool to increase qualified leads and search rankings',
      },
      MORTGAGE_CALCULATOR: {
        actions: [
          'Tool Optimization',
          'User Experience Enhancement',
          'Search Visibility',
        ],
        priority: 'medium',
        targetKeywords: ['mortgage calculator', 'home loan', 'mortgage rates'],
        descriptionTemplate:
          'Strategic {action} for {company} mortgage calculator to improve user engagement and conversion rates',
      },
      AGENT_BIO: {
        actions: [
          'Profile Optimization',
          'Local SEO Strategy',
          'Content Enhancement',
        ],
        priority: 'medium',
        targetKeywords: [
          'real estate agent',
          'local realtor',
          'property expert',
        ],
        descriptionTemplate:
          'Professional {action} for {company} agent profiles to build trust and improve local search presence',
      },
      BUYERS_GUIDE: {
        actions: [
          'Content Strategy',
          'Educational SEO',
          'Resource Optimization',
        ],
        priority: 'medium',
        targetKeywords: [
          'home buying guide',
          'first time buyer',
          'property purchase',
        ],
        descriptionTemplate:
          'Educational {action} for {company} buyers guide to establish authority and attract qualified buyers',
      },
      SELLERS_GUIDE: {
        actions: [
          'Seller Resource Optimization',
          'Lead Capture Strategy',
          'Market Positioning',
        ],
        priority: 'medium',
        targetKeywords: [
          'home selling guide',
          'sell my house',
          'property selling tips',
        ],
        descriptionTemplate:
          'Strategic {action} for {company} sellers guide to attract home sellers and showcase expertise',
      },
      NEIGHBORHOOD_GUIDE: {
        actions: [
          'Local SEO Enhancement',
          'Community Content Strategy',
          'Geographic Targeting',
        ],
        priority: 'high',
        targetKeywords: [
          'neighborhood guide',
          'local area information',
          'community real estate',
        ],
        descriptionTemplate:
          'Localized {action} for {company} neighborhood guides to dominate local search and attract area-specific buyers',
      },
      BLOG: {
        actions: [
          'Content Marketing Strategy',
          'Blog SEO Optimization',
          'Thought Leadership',
        ],
        priority: 'low',
        targetKeywords: [
          'real estate blog',
          'property market insights',
          'home buying tips',
        ],
        descriptionTemplate:
          'Content {action} for {company} blog to build authority and drive organic traffic through valuable insights',
      },
    };

    const allGroups: any[] = [];
    const usedTitles = new Set<string>(existingGroupTitles);

    // Create groups for each scraped page
    for (const scrapedPage of scrapedPages) {
      const company = companyData.find(c => c.id === scrapedPage.companyId);
      if (!company) {
        console.log(`No company found for scraped page ${scrapedPage.id}`);
        continue;
      }

      const template = pageTypeTemplates[scrapedPage.pageType];
      if (!template) {
        console.log(`No template found for page type ${scrapedPage.pageType}`);
        continue;
      }

      // Create 1-2 groups per page with different actions
      const numGroupsPerPage = Math.min(2, template.actions.length);

      for (let i = 0; i < numGroupsPerPage; i++) {
        const action = template.actions[i % template.actions.length];

        // Create unique title
        const baseTitle = `${company.name} ${scrapedPage.pageName} ${action}`;
        let groupTitle = baseTitle;
        let counter = 1;

        // Ensure uniqueness with incrementing counter
        while (usedTitles.has(groupTitle)) {
          groupTitle = `${baseTitle} - ${counter}`;
          counter++;
        }

        // Get the keyword that's already associated with this scraped page
        const pageKeyword = pageKeywordMap.get(scrapedPage.id);
        if (!pageKeyword) {
          console.log(
            `No page-keyword association found for scraped page ${scrapedPage.id}. Skipping group creation.`,
          );
          continue;
        }

        const selectedKeyword = pageKeyword.keyword;
        usedTitles.add(groupTitle);

        const randomGroup = DataFactory.createForClass(Group).generate(1)[0];

        // Create description
        const description = template.descriptionTemplate
          .replace('{action}', action.toLowerCase())
          .replace('{company}', company.name);

        allGroups.push({
          ...randomGroup,
          title: groupTitle,
          description: description,
          companyId: company.id,
          keywordId: pageKeyword.keywordId, // Use the same keyword ID as page_keyword
          scrapedPageId: scrapedPage.id, // Critical: Link group to scraped page
          metadata: {
            keyword: selectedKeyword.keyword,
            ranking: pageKeyword.currentRank
              ? `Position ${pageKeyword.currentRank}`
              : `Position ${Math.floor(Math.random() * 50) + 1}`,
            pageType: scrapedPage.pageType,
            pageName: scrapedPage.pageName,
          },
        });
      }
    }

    if (allGroups.length === 0) {
      console.log('No new groups to add');
      return existingGroups;
    }

    console.log(
      `Creating ${allGroups.length} new groups with proper scraped page associations...`,
    );
    const result = await this.repo.save(allGroups);
    console.log(`Added ${result.length} new groups`);

    // Verify the relationships
    const verificationCount = await this.repo.count({
      where: result.map(g => ({ id: g.id })),
    });
    console.log(
      `Verified ${verificationCount} groups were created with scraped page associations`,
    );
    console.log('Group seeding complete!');
    return result;
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
