import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Seeder } from 'nestjs-seeder';
import { GroupScheduledAction } from 'src/group-scheduled-action/entities/group-scheduled-action.entity';
import { PageKeyword } from 'src/page-keyword/entities/page-keyword.entity';
import { Recommendation } from 'src/recommendation/entities/recommendation.entity';
import {
  ScheduledAction,
  Status,
} from 'src/scheduled-action/entities/scheduled-action.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ScheduledActionContentPayloadSeeder implements Seeder {
  constructor(
    @InjectRepository(ScheduledAction)
    private readonly repo: Repository<ScheduledAction>,
    @InjectRepository(GroupScheduledAction)
    private readonly groupScheduledActionRepo: Repository<GroupScheduledAction>,
    @InjectRepository(PageKeyword)
    private readonly pageKeywordRepo: Repository<PageKeyword>,
  ) {}

  async seed(): Promise<any> {
    console.log('Starting scheduled-action content payload seeding...');
    await this.updateContentPayloadWithRealEntities();
    console.log('Scheduled-action content payload seeding complete!');
    return [];
  }

  /**
   * Updates contentPayload for SURFACING_PENDING+ actions with real entity data
   * instead of fake factory-generated data
   */
  private async updateContentPayloadWithRealEntities(): Promise<void> {
    console.log(
      'Updating contentPayload for SURFACING_PENDING+ actions with real entity data...',
    );

    // Find all scheduled actions that should have StoreSEODraftResponse contentPayload
    const actionsToUpdate = await this.repo.find({
      where: [
        { status: Status.SURFACING_PENDING },
        { status: Status.SURFACED },
        { status: Status.PUBLISHED },
      ],
    });

    if (actionsToUpdate.length === 0) {
      console.log('No SURFACING_PENDING+ actions found to update');
      return;
    }

    console.log(
      `Updating ${actionsToUpdate.length} actions with real entity data`,
    );

    for (const action of actionsToUpdate) {
      try {
        // Get the group associated with this scheduled action
        const groupScheduledAction =
          await this.groupScheduledActionRepo.findOne({
            where: { scheduledActionId: action.id },
            relations: ['group', 'group.keyword'],
          });

        if (!groupScheduledAction?.group) {
          console.log(
            `No group found for action ${action.id}, skipping contentPayload update`,
          );
          continue;
        }

        const group = groupScheduledAction.group;

        // Get the scraped page
        const scrapedPage = (await this.repo.manager.findOne('ScrapedPage', {
          where: { id: group.scrapedPageId },
        })) as any;

        if (!scrapedPage) {
          console.log(
            `No scraped page found for group ${group.id}, skipping contentPayload update`,
          );
          continue;
        }

        // Get all recommendations for this group
        const recommendations = await this.repo.manager
          .createQueryBuilder(Recommendation, 'r')
          .where('r.groupId = :groupId', { groupId: group.id })
          .getMany();

        // Get the scrape that the recommendations are using
        let scrape: any = null;
        if (recommendations.length > 0 && recommendations[0].scrapeId) {
          scrape = await this.repo.manager.findOne('Scrape', {
            where: { id: recommendations[0].scrapeId },
          });
        }

        // Get the page-keyword relationship
        const pageKeyword = await this.pageKeywordRepo.findOne({
          where: {
            scrapedPageId: scrapedPage.id,
            keywordId: group.keywordId,
          },
        });

        // Create realistic StoreSEODraftResponse using real entities
        const contentPayload = {
          savedKeyword: {
            id: group.keyword.id,
            keyword: group.keyword.keyword,
            metadata: group.keyword.metadata,
            createdAt: group.keyword.createdAt,
            updatedAt: group.keyword.updatedAt,
          },
          savedPage: {
            id: scrapedPage.id,
            companyId: scrapedPage.companyId,
            url: scrapedPage.url,
            pageName: scrapedPage.pageName,
            pageType: scrapedPage.pageType,
            metadata: scrapedPage.metadata,
            createdAt: scrapedPage.createdAt,
            updatedAt: scrapedPage.updatedAt,
            deletedAt: scrapedPage.deletedAt,
          },
          savedScrape: scrape
            ? {
                id: scrape.id,
                companyId: scrape.companyId,
                scrapedPageId: scrape.scrapedPageId,
                rawHtml: scrape.rawHtml,
                markdown: scrape.markdown,
                currentScrapedValues: scrape.currentScrapedValues,
                mediaId: scrape.mediaId,
                scrapedAt: scrape.scrapedAt,
                createdAt: scrape.createdAt,
                updatedAt: scrape.updatedAt,
              }
            : null,
          savedPageKeyword: pageKeyword
            ? {
                id: pageKeyword.id,
                scrapedPageId: pageKeyword.scrapedPageId,
                keywordId: pageKeyword.keywordId,
                originalRank: pageKeyword.originalRank,
                currentRank: pageKeyword.currentRank,
                rankCheckedAt: pageKeyword.rankCheckedAt,
                createdAt: pageKeyword.createdAt,
                updatedAt: pageKeyword.updatedAt,
              }
            : null,
          savedGroup: {
            id: group.id,
            companyId: group.companyId,
            keywordId: group.keywordId,
            title: group.title,
            description: group.description,
            metadata: group.metadata,
            createdAt: group.createdAt,
            updatedAt: group.updatedAt,
            deletedAt: group.deletedAt,
          },
          savedRecommendations: recommendations.map(rec => ({
            id: rec.id,
            scrapeId: rec.scrapeId,
            groupId: rec.groupId,
            type: rec.type,
            currentValue: rec.currentValue,
            recommendationValue: rec.recommendationValue,
            reasoning: rec.reasoning,
            status: 'PENDING',
            rejectionReason: null,
            metadata: rec.metadata,
            createdAt: rec.createdAt,
            updatedAt: rec.updatedAt,
          })),
        };

        // Update the action's contentPayload
        await this.repo.update(action.id, {
          contentPayload: contentPayload as any,
        });

        console.log(
          `Updated action ${action.id} with ${recommendations.length} recommendations`,
        );
      } catch (error) {
        console.error(
          `Error updating contentPayload for action ${action.id}:`,
          error,
        );
      }
    }

    console.log('Finished updating contentPayload with real entity data');
  }

  drop(): Promise<any> {
    // Skip individual drops - handled by DatabaseResetSeeder
    return Promise.resolve([]);
  }
}
