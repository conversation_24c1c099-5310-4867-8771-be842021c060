import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { Parent, ResolveField, Resolver } from '@nestjs/graphql';
import DataLoader from 'dataloader';

import { AppPolicyRegistry } from '../auth.module';
import { Campaign } from '../campaign/entities/campaign.entity';
import { AuthContext, DataLoaders } from '../graphql.decorator';
import { Property, PropertyCampaign } from './entities/property.entity';

@Resolver(() => Property)
export class PropertyResolver {
  @ResolveField(() => PropertyCampaign)
  async campaign(
    @Parent() property: Property,
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @DataLoaders()
    dataloaders: {
      campaign: DataLoader<string, Campaign | null>;
    },
  ): Promise<PropertyCampaign> {
    const canViewCampaign = await authContext.can(
      'property',
      'viewCampaign',
      property.companyId,
    );
    if (!canViewCampaign) {
      return { eligible: false };
    }

    // Check if property has FOR_SALE status
    if (property.status !== 'FOR_SALE') {
      return { eligible: false };
    }

    // Check for an existing active campaign
    const existingCampaign = await dataloaders.campaign.load(property.id);
    if (existingCampaign) {
      return { eligible: false };
    }

    return { eligible: true };
  }
}
