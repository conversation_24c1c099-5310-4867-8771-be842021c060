import { Injectable, Logger } from '@nestjs/common';

import { Media, Agent } from './entities/property.entity';
import { GraphQLClientService } from '../common/graphql/apollo-client.service';
import { GET_PROPERTY_WITH_MEDIA_AND_AGENTS } from '../common/graphql/queries/properties';

interface PropertyCMSData {
  media: Media[];
  agents: Agent[];
}

@Injectable()
export class PropertyCMSService {
  private readonly logger = new Logger(PropertyCMSService.name);

  constructor(private readonly graphqlClient: GraphQLClientService) {}

  async getPropertyData(
    propertyId: string,
    forCMS?: boolean,
  ): Promise<PropertyCMSData> {
    try {
      const response = await this.graphqlClient.request<{
        property: {
          id: string;
          media: Media[];
          agents: Agent[];
        };
      }>(GET_PROPERTY_WITH_MEDIA_AND_AGENTS, { id: propertyId, forCMS });

      if (!response?.property) {
        this.logger.warn(`No property data found for ID: ${propertyId}`);
        return { media: [], agents: [] };
      }

      return {
        media: response.property.media || [],
        agents: response.property.agents || [],
      };
    } catch (error) {
      this.logger.error('Error fetching property data from CMS', {
        propertyId,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
