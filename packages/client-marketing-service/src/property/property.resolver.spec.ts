import { Test, TestingModule } from '@nestjs/testing';

import { PropertyResolver } from './property.resolver';

describe('PropertyResolver', () => {
  let resolver: PropertyResolver;
  let mockDataLoaders: any;

  beforeEach(async () => {
    mockDataLoaders = {
      campaign: {
        load: jest.fn(),
        loadMany: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [PropertyResolver],
    }).compile();

    resolver = module.get<PropertyResolver>(PropertyResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });

  describe('campaign', () => {
    let mockAuthContext: any;

    beforeEach(() => {
      mockAuthContext = {
        can: jest.fn(),
      };
    });

    it('should return eligible false when campaign exists', async () => {
      const mockProperty = {
        id: 'property-123',
        status: 'FOR_SALE',
        companyId: 'user-123',
      };
      mockDataLoaders.campaign.load.mockResolvedValue({
        id: 'campaign-123',
      });
      mockAuthContext.can.mockResolvedValue(true);

      const result = await resolver.campaign(
        mockProperty,
        mockAuthContext,
        mockDataLoaders,
      );

      expect(result.eligible).toBe(false);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'property',
        'viewCampaign',
        'user-123',
      );
      expect(mockDataLoaders.campaign.load).toHaveBeenCalledWith(
        'property-123',
      );
      expect(mockDataLoaders.campaign.loadMany).not.toHaveBeenCalled();
    });

    it('should return eligible false when property status is not FOR_SALE', async () => {
      const mockProperty = {
        id: 'property-123',
        status: 'SOLD',
        companyId: 'user-123',
      };
      mockDataLoaders.campaign.load.mockResolvedValue(null);
      mockAuthContext.can.mockResolvedValue(true);

      const result = await resolver.campaign(
        mockProperty,
        mockAuthContext,
        mockDataLoaders,
      );

      expect(result.eligible).toBe(false);
    });

    it('should return eligible false when user cannot view campaign for property', async () => {
      const mockProperty = {
        id: 'property-123',
        status: 'FOR_SALE',
        companyId: 'company-123',
      };
      mockDataLoaders.campaign.load.mockResolvedValue(null);
      mockAuthContext.can.mockResolvedValue(false);

      const result = await resolver.campaign(
        mockProperty,
        mockAuthContext,
        mockDataLoaders,
      );

      expect(mockDataLoaders.campaign.load).not.toHaveBeenCalled();
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'property',
        'viewCampaign',
        'company-123',
      );
      expect(result.eligible).toBe(false);
    });

    it('should return eligible true when all conditions are met', async () => {
      const mockProperty = {
        id: 'property-123',
        status: 'FOR_SALE',
        companyId: 'user-123',
      };
      mockDataLoaders.campaign.load.mockResolvedValue(null);
      mockAuthContext.can.mockResolvedValue(true);

      const result = await resolver.campaign(
        mockProperty,
        mockAuthContext,
        mockDataLoaders,
      );
      expect(result.eligible).toBe(true);
      expect(mockAuthContext.can).toHaveBeenCalledWith(
        'property',
        'viewCampaign',
        'user-123',
      );
    });

    it('should use DataLoader for batching campaign queries', async () => {
      const mockProperty = {
        id: 'property-123',
        status: 'FOR_SALE',
        companyId: 'user-123',
      };
      mockDataLoaders.campaign.load.mockResolvedValue(null);
      mockAuthContext.can.mockResolvedValue(true);

      await resolver.campaign(mockProperty, mockAuthContext, mockDataLoaders);

      expect(mockDataLoaders.campaign.load).toHaveBeenCalledWith(
        'property-123',
      );
    });
  });
});
