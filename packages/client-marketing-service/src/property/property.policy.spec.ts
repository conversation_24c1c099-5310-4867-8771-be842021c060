import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { AppPolicyRegistry } from 'src/auth.module';

import { PropertyPolicy } from './property.policy';

describe('PropertyPolicy', () => {
  let policy: PropertyPolicy;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  beforeEach(() => {
    mockAuthContext = {
      isSuper: jest.fn<boolean, []>(),
      belongsToCompany: jest.fn<any, [string]>(),
      getUserId: jest.fn<string, []>(),
    } as unknown as jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

    policy = new PropertyPolicy(mockAuthContext);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('viewCampaign', () => {
    it('should return false when no property company ID is provided', () => {
      const result = policy.viewCampaign(undefined);
      expect(result).toBe(false);
    });

    it('should return true for super users', () => {
      mockAuthContext.isSuper.mockReturnValue(true);

      const result = policy.viewCampaign('company-123');

      expect(result).toBe(true);
      expect(mockAuthContext.isSuper).toHaveBeenCalled();
      expect(mockAuthContext.belongsToCompany).not.toHaveBeenCalled();
    });

    it('should return true when user belongs to the same company as the property', () => {
      mockAuthContext.isSuper.mockReturnValue(false);
      mockAuthContext.belongsToCompany.mockReturnValue({
        type: 'company',
      } as any);

      const result = policy.viewCampaign('company-123');

      expect(result).toBe(true);
      expect(mockAuthContext.belongsToCompany).toHaveBeenCalledWith(
        'company-123',
      );
    });

    it('should return false when user does not belong to the same company', () => {
      mockAuthContext.isSuper.mockReturnValue(false);
      mockAuthContext.belongsToCompany.mockReturnValue(null);

      const result = policy.viewCampaign('company-123');

      expect(result).toBe(false);
      expect(mockAuthContext.belongsToCompany).toHaveBeenCalledWith(
        'company-123',
      );
    });
  });

  describe('createCampaign', () => {
    it('should return false when no property company ID is provided', () => {
      const result = policy.createCampaign(undefined);
      expect(result).toBe(false);
    });

    it('should return true for super users', () => {
      mockAuthContext.isSuper.mockReturnValue(true);

      const result = policy.createCampaign('company-123');

      expect(result).toBe(true);
      expect(mockAuthContext.isSuper).toHaveBeenCalled();
    });

    it('should return true when user belongs to the same company as the property', () => {
      mockAuthContext.isSuper.mockReturnValue(false);
      mockAuthContext.belongsToCompany.mockReturnValue({
        type: 'company',
      } as any);

      const result = policy.createCampaign('company-123');

      expect(result).toBe(true);
      expect(mockAuthContext.belongsToCompany).toHaveBeenCalledWith(
        'company-123',
      );
    });

    it('should return false when user does not belong to the same company', () => {
      mockAuthContext.isSuper.mockReturnValue(false);
      mockAuthContext.belongsToCompany.mockReturnValue(null);

      const result = policy.createCampaign('company-123');

      expect(result).toBe(false);
      expect(mockAuthContext.belongsToCompany).toHaveBeenCalledWith(
        'company-123',
      );
    });
  });
});
