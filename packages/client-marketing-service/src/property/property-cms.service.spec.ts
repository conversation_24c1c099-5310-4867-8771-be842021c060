import { Test, TestingModule } from '@nestjs/testing';

import { Media, Agent } from './entities/property.entity';
import { PropertyCMSService } from './property-cms.service';
import { GraphQLClientService } from '../common/graphql/apollo-client.service';

describe('PropertyCMSService', () => {
  let service: PropertyCMSService;
  let graphqlClientService: jest.Mocked<GraphQLClientService>;

  beforeEach(async () => {
    const mockGraphQLClientService = {
      request: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PropertyCMSService,
        {
          provide: GraphQLClientService,
          useValue: mockGraphQLClientService,
        },
      ],
    }).compile();

    service = module.get<PropertyCMSService>(PropertyCMSService);
    graphqlClientService = module.get(GraphQLClientService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPropertyData', () => {
    it('should return empty arrays when no property data found', async () => {
      graphqlClientService.request.mockResolvedValue({ property: null });

      const result = await service.getPropertyData('test-id');

      expect(result).toEqual({ media: [], agents: [] });
    });

    it('should return media in original order', async () => {
      const mockMedia: Media[] = [
        {
          id: '3',
          mediaId: '3',
          resourceType: 'image',
          companyId: 'company1',
          leadAgent: false,
          createdAt: new Date('2023-01-03'),
          order: 2,
        } as Media,
        {
          id: '1',
          mediaId: '1',
          resourceType: 'image',
          companyId: 'company1',
          leadAgent: false,
          createdAt: new Date('2023-01-01'),
          order: 1,
        } as Media,
        {
          id: '2',
          mediaId: '2',
          resourceType: 'image',
          companyId: 'company1',
          leadAgent: false,
          createdAt: new Date('2023-01-02'),
        } as Media,
      ];

      graphqlClientService.request.mockResolvedValue({
        property: {
          id: 'test-id',
          media: mockMedia,
          agents: [],
        },
      });

      const result = await service.getPropertyData('test-id');

      // Should return media in original order
      expect(result.media.map(m => m.id)).toEqual(['3', '1', '2']);
    });

    it('should return agents in original order', async () => {
      const mockAgents: Agent[] = [
        {
          id: '3',
          agentId: '3',
          companyId: 'company1',
          leadAgent: false,
          createdAt: new Date('2023-01-03'),
        } as Agent,
        {
          id: '1',
          agentId: '1',
          companyId: 'company1',
          leadAgent: true,
          createdAt: new Date('2023-01-02'),
        } as Agent,
        {
          id: '2',
          agentId: '2',
          companyId: 'company1',
          leadAgent: false,
          createdAt: new Date('2023-01-01'),
        } as Agent,
      ];

      graphqlClientService.request.mockResolvedValue({
        property: {
          id: 'test-id',
          media: [],
          agents: mockAgents,
        },
      });

      const result = await service.getPropertyData('test-id');

      // Should return agents in original order
      expect(result.agents.map(a => a.id)).toEqual(['3', '1', '2']);
    });

    it('should handle GraphQL errors gracefully', async () => {
      const error = new Error('GraphQL request failed');
      graphqlClientService.request.mockRejectedValue(error);

      await expect(service.getPropertyData('test-id')).rejects.toThrow(error);
    });

    it('should pass forCMS parameter to GraphQL query', async () => {
      graphqlClientService.request.mockResolvedValue({
        property: { id: 'test-id', media: [], agents: [] },
      });

      await service.getPropertyData('test-id', true);

      expect(graphqlClientService.request).toHaveBeenCalledWith(
        expect.any(String),
        { id: 'test-id', forCMS: true },
      );
    });
  });
});
