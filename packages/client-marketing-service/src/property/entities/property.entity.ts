import { Directive, Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class PropertyCampaign {
  @Field(() => Boolean)
  eligible: boolean;
}

@ObjectType()
@Directive('@key(fields: "id")')
@Directive('@extends')
export class Media {
  @Field(() => ID)
  id: string;
}

@ObjectType()
@Directive('@key(fields: "id")')
@Directive('@extends')
export class Agent {
  @Field(() => ID)
  id: string;
}

@ObjectType()
@Directive('@key(fields: "id")')
@Directive('@extends')
export class Property {
  @Field(() => ID)
  id: string;

  @Field(() => String, { nullable: true })
  @Directive('@external')
  status?: string;

  @Field(() => String)
  @Directive('@external')
  companyId: string;

  @Field(() => PropertyCampaign, { nullable: true })
  @Directive('@requires(fields: "status companyId")')
  campaign?: PropertyCampaign;
}
