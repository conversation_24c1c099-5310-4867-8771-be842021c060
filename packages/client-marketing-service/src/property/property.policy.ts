import {
  BasePolicy,
  UnifiedAuthContext,
} from '@luxury-presence/authorization-middleware';
import { Injectable } from '@nestjs/common';
import { AppPolicyRegistry } from 'src/auth.module';

@Injectable()
export class PropertyPolicy extends BasePolicy {
  constructor(auth: UnifiedAuthContext<AppPolicyRegistry>) {
    super(auth);
  }

  /**
   * Checks if the user has permission to view a property campaign eligibility
   * @param propertyCompanyId The company ID that owns the property
   * @returns boolean - true if user has permission to view the campaign
   */
  viewCampaign = (propertyCompanyId?: string): boolean => {
    if (!propertyCompanyId) {
      return false; // No property company ID means we can't verify ownership
    }

    // Super users can view all campaigns
    if (this.auth.isSuper()) {
      return true;
    }

    // Regular users can only view campaigns for properties in their company
    return !!this.auth.belongsToCompany(propertyCompanyId);
  };

  /**
   * Checks if the user has permission to create a campaign for a property
   * @param propertyCompanyId The company ID that owns the property
   * @returns boolean - true if user has permission to create a campaign
   */
  createCampaign = (propertyCompanyId?: string): boolean => {
    if (!propertyCompanyId) {
      return false; // No property company ID means we can't verify ownership
    }

    // Super users can create campaigns for any property
    if (this.auth.isSuper()) {
      return true;
    }

    // Regular users can only create campaigns for properties in their company
    return !!this.auth.belongsToCompany(propertyCompanyId);
  };
}
