import {
  createUnionType,
  Field,
  ObjectType,
  registerEnumType,
} from '@nestjs/graphql';
import { FeedBlogPost } from 'src/cms/dto/feed-blog-post.dto';
import { FeedLeads } from 'src/common/services/api-gateway/dto/feed-leads.dto';
import { FeedRecommendationGroup } from 'src/group/dto/feed-recommendation-group.dto';
import { FeedAdPerformance } from 'src/snowflake/dto/feed-ad-performance.dto';

export enum ItemType {
  BLOG_POST = 'BLOG_POST',
  SEO_RECOMMENDATION = 'SEO_RECOMMENDATION',
  LEAD_HANDOFF = 'LEAD_HANDOFF',
  AD_PERFORMANCE = 'AD_PERFORMANCE',
}

registerEnumType(ItemType, { name: 'ItemType' });

const Content = createUnionType({
  name: 'Content',
  types: () =>
    [
      FeedBlogPost,
      FeedRecommendationGroup,
      FeedLeads,
      FeedAdPerformance,
    ] as const,
  resolveType: value => {
    if (Object.hasOwn(value, 'scheduledToBePublishedAt'))
      return FeedRecommendationGroup;

    if (Object.hasOwn(value, 'postId')) return FeedBlogPost;

    if (Object.hasOwn(value, 'leads')) return FeedLeads;

    if (Object.hasOwn(value, 'impressionsCount')) return FeedAdPerformance;

    return null;
  },
});

@ObjectType()
export class FeedItem {
  @Field(() => Date, {
    nullable: true,
    description:
      'Timestamp of the feed item. If null or a date in the future, consider it to be an "upcoming" item.',
  })
  timestamp: Date | null;
  @Field(() => ItemType, {
    description:
      'The card type used to the determine the UI element to render.',
  })
  itemType: ItemType;
  @Field(() => Content)
  content: typeof Content;
}

@ObjectType()
export default class HomepageFeed {
  @Field(() => [FeedItem])
  items: FeedItem[];
}
