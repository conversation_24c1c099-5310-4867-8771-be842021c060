import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AppPolicyRegistry } from 'src/auth.module';

import { HomepageFeedResolver } from './homepage-feed.resolver';
import { HomepageFeedService } from './homepage-feed.service';

describe('HomepageFeedResolver', () => {
  const companyId = 'test-company-id';
  const timestampStart = '2025-06-01T00:00:00Z';
  const filters = { timestampStart };

  let resolver: HomepageFeedResolver;
  let mockAuthContext: jest.Mocked<UnifiedAuthContext<AppPolicyRegistry>>;

  const mockHomepageFeedService = {
    getHomepageFeed: jest.fn(),
  } as unknown as HomepageFeedService;

  const mockCan = (value: boolean) => {
    mockAuthContext.can.mockImplementation(() => Promise.resolve(value));
  };

  beforeEach(async () => {
    mockAuthContext = { can: jest.fn() } as unknown as jest.Mocked<
      UnifiedAuthContext<AppPolicyRegistry>
    >;

    const module = await Test.createTestingModule({
      providers: [
        HomepageFeedResolver,
        { provide: HomepageFeedService, useValue: mockHomepageFeedService },
      ],
    }).compile();

    resolver = module.get<HomepageFeedResolver>(HomepageFeedResolver);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should check permissions', async () => {
    mockCan(false);

    await expect(
      resolver.homepageFeed(mockAuthContext, companyId, filters),
    ).rejects.toThrow(ForbiddenException);

    expect(mockAuthContext.can).toHaveBeenCalledWith(
      'homepageFeed',
      'read',
      companyId,
    );
  });

  it('should fetch data from service', async () => {
    mockCan(true);

    await resolver.homepageFeed(mockAuthContext, companyId, filters);

    expect(mockHomepageFeedService.getHomepageFeed).toHaveBeenCalledWith(
      companyId,
      filters,
    );
  });
});
