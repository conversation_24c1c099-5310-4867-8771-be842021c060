import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { CMSService } from 'src/cms/cms.service';
import { ApiGatewayService } from 'src/common/services/api-gateway/api-gateway.service';
import { TenantModule } from 'src/common/services/tenant/tenant.module';
import { WebsiteModule } from 'src/common/services/website/website.module';
import { GroupModule } from 'src/group/group.module';
import { SnowflakeService } from 'src/snowflake/snowflake.service';

import { HomepageFeedResolver } from './homepage-feed.resolver';
import { HomepageFeedService } from './homepage-feed.service';

@Module({
  imports: [GroupModule, HttpModule, TenantModule, WebsiteModule],
  providers: [
    HomepageFeedResolver,
    HomepageFeedService,
    CMSService,
    ApiGatewayService,
    SnowflakeService,
  ],
})
export class HomepageFeedModule {}
