import { Test, TestingModule } from '@nestjs/testing';
import { CMSService } from 'src/cms/cms.service';
import { FeedBlogPost } from 'src/cms/dto/feed-blog-post.dto';
import { ApiGatewayService } from 'src/common/services/api-gateway/api-gateway.service';
import { TenantService } from 'src/common/services/tenant/tenant.service';
import { WebsiteService } from 'src/common/services/website/website.service';
import { GroupService } from 'src/group/group.service';
import { SnowflakeService } from 'src/snowflake/snowflake.service';

import { FeedItem, ItemType } from './entities/homepage-feed.entity';
import {
  HomepageFeedService,
  RelevantEntitlement,
} from './homepage-feed.service';
import * as mappers from './utils';

jest.useFakeTimers();
jest.setSystemTime(new Date('2025-07-08T00:00:00Z'));

describe('HomepageFeedService', () => {
  const companyId = 'test-company-id';

  let service: HomepageFeedService;

  const mockTenantService: TenantService = {
    getEntitlements: jest.fn().mockResolvedValue([]),
    find: jest.fn().mockResolvedValue([{ website: 'test-website.com' }]),
  } as unknown as TenantService;

  const mockWebsiteService: WebsiteService = {
    getLiveBrandWebsites: jest.fn().mockResolvedValue([]),
  } as unknown as WebsiteService;

  const mockCMSService: CMSService = {
    findAIGeneratedBlogPosts: jest.fn().mockResolvedValue([]),
  } as unknown as CMSService;

  const mockGroupService: GroupService = {
    findSurfacedGroupsByCompany: jest.fn().mockResolvedValue([]),
  } as unknown as GroupService;

  const apiGatewayService: ApiGatewayService = {
    findHandedOffLeads: jest.fn().mockResolvedValue([]),
    findMedia: jest.fn().mockResolvedValue([]),
  } as unknown as ApiGatewayService;

  const mockSnowflakeService: SnowflakeService = {
    findDailyAdPerformance: jest.fn().mockResolvedValue([]),
    findPaidAdLeads: jest.fn().mockResolvedValue([]),
  } as unknown as SnowflakeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HomepageFeedService,
        { provide: CMSService, useValue: mockCMSService },
        { provide: GroupService, useValue: mockGroupService },
        { provide: ApiGatewayService, useValue: apiGatewayService },
        { provide: SnowflakeService, useValue: mockSnowflakeService },
        { provide: TenantService, useValue: mockTenantService },
        { provide: WebsiteService, useValue: mockWebsiteService },
      ],
    }).compile();

    service = module.get<HomepageFeedService>(HomepageFeedService);
  });

  describe('#homepageFeed', () => {
    it('should check entitlements', async () => {
      await service.getHomepageFeed(companyId);

      expect(mockTenantService.getEntitlements).toHaveBeenCalledWith(companyId);
    });

    it('should fetch company data', async () => {
      await service.getHomepageFeed(companyId);

      expect(mockTenantService.find).toHaveBeenCalledWith({
        displayId: companyId,
      });
    });

    it('should fetch live brand websites', async () => {
      await service.getHomepageFeed(companyId);

      expect(mockWebsiteService.getLiveBrandWebsites).toHaveBeenCalledWith(
        companyId,
      );
    });

    describe(`when not entitled to ${RelevantEntitlement.BLOG}`, () => {
      it('should not fetch blog posts', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: false,
            product: { name: RelevantEntitlement.BLOG },
          },
        ]);

        await service.getHomepageFeed(companyId);

        expect(mockCMSService.findAIGeneratedBlogPosts).not.toHaveBeenCalled();
      });
    });

    describe(`when entitled to ${RelevantEntitlement.BLOG}`, () => {
      it('should fetch blog posts', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.BLOG },
          },
        ]);

        const mapperSpy = jest.spyOn(mappers, 'mapBlogPosts');

        await service.getHomepageFeed(companyId);

        expect(mockCMSService.findAIGeneratedBlogPosts).toHaveBeenCalled();
        expect(mapperSpy).toHaveBeenCalled();
      });

      it('should call mapper with live brand website hostname', async () => {
        const hostname = 'test-hostname.com';
        const liveBrandWebsites = [
          {
            hostname,
          },
          {
            hostname: 'another-hostname.com',
          },
        ];

        (mockTenantService.find as jest.Mock).mockResolvedValueOnce([
          { website: hostname },
        ]);

        (
          mockWebsiteService.getLiveBrandWebsites as jest.Mock
        ).mockResolvedValueOnce(liveBrandWebsites);

        const mapperSpy = jest.spyOn(mappers, 'mapBlogPosts');

        await service.getHomepageFeed(companyId);

        expect(mapperSpy).toHaveBeenCalledWith(
          expect.anything(),
          hostname,
          expect.anything(),
        );
      });
    });

    describe(`when not entitled to ${RelevantEntitlement.SEO}`, () => {
      it('should not fetch SEO recommendations', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: false,
            product: { name: RelevantEntitlement.SEO },
          },
        ]);

        await service.getHomepageFeed(companyId);

        expect(
          mockGroupService.findSurfacedGroupsByCompany,
        ).not.toHaveBeenCalled();
      });
    });

    describe(`when entitled to ${RelevantEntitlement.SEO}`, () => {
      it('should fetch SEO recommendations', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.SEO },
          },
        ]);

        const mapperSpy = jest.spyOn(mappers, 'mapRecommendationGroups');

        await service.getHomepageFeed(companyId);

        expect(
          mockGroupService.findSurfacedGroupsByCompany,
        ).toHaveBeenCalledWith(companyId);
        expect(mapperSpy).toHaveBeenCalled();
      });
    });

    describe(`when not entitled to ${RelevantEntitlement.AILN}`, () => {
      it('should not fetch AILN leads', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: false,
            product: { name: RelevantEntitlement.AILN },
          },
        ]);

        await service.getHomepageFeed(companyId);

        expect(apiGatewayService.findHandedOffLeads).not.toHaveBeenCalled();
      });
    });

    describe(`when entitled to ${RelevantEntitlement.AILN}`, () => {
      it('should fetch AILN leads', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.AILN },
          },
        ]);

        const mapperSpy = jest.spyOn(mappers, 'mapLeads');

        await service.getHomepageFeed(companyId);

        expect(apiGatewayService.findHandedOffLeads).toHaveBeenCalled();
        expect(mapperSpy).toHaveBeenCalled();
      });
    });

    describe(`when not entitled to ${RelevantEntitlement.PAID}`, () => {
      it('should not fetch ad performance reports', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: false,
            product: { name: RelevantEntitlement.PAID },
          },
        ]);

        await service.getHomepageFeed(companyId);

        expect(
          mockSnowflakeService.findDailyAdPerformance,
        ).not.toHaveBeenCalled();
      });
    });

    describe(`when entitled to ${RelevantEntitlement.PAID}`, () => {
      it('should fetch ad performance reports', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.PAID },
          },
        ]);

        const mapperSpy = jest.spyOn(mappers, 'mapAdPerformance');

        await service.getHomepageFeed(companyId);

        expect(mockSnowflakeService.findDailyAdPerformance).toHaveBeenCalled();
        expect(mapperSpy).toHaveBeenCalled();
      });

      it('should fetch paid ad leads', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.PAID },
          },
        ]);

        const mapperSpy = jest.spyOn(mappers, 'mapAdPerformance');

        await service.getHomepageFeed(companyId);

        expect(mockSnowflakeService.findPaidAdLeads).toHaveBeenCalled();
        expect(mapperSpy).toHaveBeenCalled();
      });

      it('should fail when findDailyAdPerformance request fails', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.PAID },
          },
        ]);

        // Mock Snowflake service to throw an error
        (
          mockSnowflakeService.findDailyAdPerformance as jest.Mock
        ).mockRejectedValueOnce(new Error('Snowflake connection failed'));

        // Verify the promise rejects when Snowflake fails
        await expect(service.getHomepageFeed(companyId)).rejects.toThrow(
          'Snowflake connection failed',
        );

        // Verify Snowflake was called
        expect(mockSnowflakeService.findDailyAdPerformance).toHaveBeenCalled();
      });

      it('should fail when findPaidAdLeads request fails', async () => {
        (mockTenantService.getEntitlements as jest.Mock).mockResolvedValueOnce([
          {
            entitled: true,
            product: { name: RelevantEntitlement.PAID },
          },
        ]);

        // Mock Snowflake service to throw an error
        (
          mockSnowflakeService.findPaidAdLeads as jest.Mock
        ).mockRejectedValueOnce(new Error('Snowflake connection failed'));

        // Verify the promise rejects when Snowflake fails
        await expect(service.getHomepageFeed(companyId)).rejects.toThrow(
          'Snowflake connection failed',
        );

        // Verify Snowflake was called
        expect(mockSnowflakeService.findPaidAdLeads).toHaveBeenCalled();
      });
    });

    it('should order content by timestamp (descending)', async () => {
      const first = {
        timestamp: null,
        itemType: ItemType.BLOG_POST,
      } as FeedItem;
      const second = {
        timestamp: new Date('2025-10-01T00:00:00Z'),
        itemType: ItemType.SEO_RECOMMENDATION,
      } as FeedItem;
      const third = {
        timestamp: new Date('2024-10-01T00:00:00Z'),
        itemType: ItemType.BLOG_POST,
      } as FeedItem;
      const fourth = {
        timestamp: new Date('2023-10-01T00:00:00Z'),
        itemType: ItemType.SEO_RECOMMENDATION,
      } as FeedItem;

      jest.spyOn(mappers, 'mapBlogPosts').mockReturnValueOnce([third, first]);
      jest
        .spyOn(mappers, 'mapRecommendationGroups')
        .mockReturnValueOnce([second, fourth]);

      const result = await service.getHomepageFeed(companyId);

      expect(result).toEqual({ items: [first, second, third, fourth] });
    });

    it('should filter by timestamp start', async () => {
      const timestampStart = '2025-06-01T00:00:00Z';

      const tooOldItem: FeedItem = {
        timestamp: new Date('2025-05-01T00:00:00Z'),
        itemType: ItemType.BLOG_POST,
        content: {} as FeedBlogPost,
      };
      const visibleItem: FeedItem = {
        timestamp: new Date('2025-06-10T00:00:00Z'),
        itemType: ItemType.BLOG_POST,
        content: {} as FeedBlogPost,
      };

      jest
        .spyOn(mappers, 'mapBlogPosts')
        .mockReturnValueOnce([tooOldItem, visibleItem]);

      const result = await service.getHomepageFeed(companyId, {
        timestampStart,
      });

      expect(result.items).not.toContain(tooOldItem);
      expect(result.items).toContain(visibleItem);
    });

    it('should filter by timestamp end', async () => {
      const timestampEnd = '2025-06-01T00:00:00Z';

      const tooRecentItem: FeedItem = {
        timestamp: new Date('2025-07-01T00:00:00Z'),
        itemType: ItemType.BLOG_POST,
        content: {} as FeedBlogPost,
      };
      const visibleItem: FeedItem = {
        timestamp: new Date('2025-06-01T00:00:00Z'),
        itemType: ItemType.BLOG_POST,
        content: {} as FeedBlogPost,
      };

      jest
        .spyOn(mappers, 'mapBlogPosts')
        .mockReturnValueOnce([tooRecentItem, visibleItem]);

      const result = await service.getHomepageFeed(companyId, {
        timestampEnd,
      });

      expect(result.items).not.toContain(tooRecentItem);
      expect(result.items).toContain(visibleItem);
    });
  });

  describe('mappers', () => {
    it.todo('check that mappers are called with the correct data');
  });
});
