import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';
import { ForbiddenException } from '@nestjs/common';
import { Args, ID, Query, Resolver } from '@nestjs/graphql';
import { AppPolicyRegistry } from 'src/auth.module';
import { AuthContext } from 'src/graphql.decorator';

import HomepageFeed from './entities/homepage-feed.entity';
import { FeedFilters } from './homepage-feed-filters';
import { HomepageFeedService } from './homepage-feed.service';

@Resolver()
export class HomepageFeedResolver {
  constructor(private readonly homepageFeedService: HomepageFeedService) {}

  @Query(() => HomepageFeed, { description: 'Fetches the homepage feed.' })
  async homepageFeed(
    @AuthContext() authContext: UnifiedAuthContext<AppPolicyRegistry>,
    @Args('companyId', { type: () => ID }) companyId: string,
    @Args('filters', { type: () => FeedFilters, nullable: true })
    filters?: FeedFilters,
  ) {
    const canRead = await authContext.can('homepageFeed', 'read', companyId);

    if (!canRead) throw new ForbiddenException();

    const data = this.homepageFeedService.getHomepageFeed(companyId, filters);

    return data;
  }
}
