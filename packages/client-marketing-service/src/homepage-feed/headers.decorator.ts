import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const RequestHeaders = createParamDecorator(
  (headerName: string | undefined, ctx: ExecutionContext) => {
    const request = GqlExecutionContext.create(ctx).getContext().req;

    if (!request) {
      throw new Error('Request not available in GraphQL context');
    }

    return headerName ? request.headers[headerName] : request.headers;
  },
);
