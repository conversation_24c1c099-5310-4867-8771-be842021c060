import { BlogPost } from 'src/cms/dto/blog-post.dto';
import { LeadDto } from 'src/common/services/api-gateway/dto/lead.dto';
import { RecommendationType } from 'src/common/types/scraped-element.type';
import { Group } from 'src/group/entities/group.entity';
import { ScrapedPageType } from 'src/scraped-page/entities/scraped-page.entity';

import { ItemType } from './entities/homepage-feed.entity';
import {
  mapAdPerformance,
  mapBlogPosts,
  mapLeads,
  mapRecommendationGroups,
} from './utils';

describe('Feed utils', () => {
  describe('mapBlogPosts', () => {
    it('should map blog posts to feed items', () => {
      const input: BlogPost[] = [
        {
          postId: '123',
          postStatus: 'DRAFT',
          title: 'lorem',
          publishedAt: null,
          scheduledAt: null,
          slug: 'lorem-ipsum',
          media: [
            { mediaId: 'media123', displayOrder: 1 },
            { mediaId: 'media456', displayOrder: 2 },
          ],
        },
        {
          postId: '456',
          postStatus: 'PUBLISHED',
          title: 'lorem',
          slug: 'lorem-dolor',
          publishedAt: '2025-05-28T17:45:08.805Z',
          scheduledAt: null,
          media: [],
        },
        {
          postId: '789',
          postStatus: 'SCHEDULED',
          title: 'lorem',
          slug: 'lorem-sit',
          publishedAt: null,
          scheduledAt: '2025-07-28T17:45:08.805Z',
        },
      ];

      const expectedResult = [
        {
          timestamp: null,
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '123',
            postStatus: 'DRAFT',
            title: 'lorem',
            publishedAt: null,
            scheduledAt: null,
            mediaId: 'media123',
            media: null,
            liveUrl: null,
          },
        },
        {
          timestamp: new Date('2025-05-28T17:45:08.805Z'),
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '456',
            postStatus: 'PUBLISHED',
            title: 'lorem',
            publishedAt: new Date('2025-05-28T17:45:08.805Z'),
            scheduledAt: null,
            mediaId: null,
            media: null,
            liveUrl: null,
          },
        },
        {
          timestamp: new Date('2025-07-28T17:45:08.805Z'),
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '789',
            postStatus: 'SCHEDULED',
            title: 'lorem',
            publishedAt: null,
            scheduledAt: new Date('2025-07-28T17:45:08.805Z'),
            mediaId: null,
            media: null,
            liveUrl: null,
          },
        },
      ];

      const result = mapBlogPosts(input);

      expect(result).toEqual(expectedResult);
    });

    it('should add "liveUrl" field if "hostname" is available and status is "PUBLISHED"', () => {
      const hostname = 'example.com';

      const input: BlogPost[] = [
        {
          postId: '123',
          postStatus: 'DRAFT',
          title: 'lorem',
          publishedAt: null,
          scheduledAt: null,
          slug: 'lorem-ipsum',
          media: [
            { mediaId: 'media123', displayOrder: 1 },
            { mediaId: 'media456', displayOrder: 2 },
          ],
        },
        {
          postId: '456',
          postStatus: 'PUBLISHED',
          title: 'lorem',
          slug: 'lorem-dolor',
          publishedAt: '2025-05-28T17:45:08.805Z',
          scheduledAt: null,
          media: [],
        },
        {
          postId: '789',
          postStatus: 'SCHEDULED',
          title: 'lorem',
          slug: 'lorem-sit',
          publishedAt: null,
          scheduledAt: '2025-07-28T17:45:08.805Z',
        },
      ];

      const expectedResult = [
        {
          timestamp: null,
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '123',
            postStatus: 'DRAFT',
            title: 'lorem',
            publishedAt: null,
            scheduledAt: null,
            mediaId: 'media123',
            media: null,
            liveUrl: null,
          },
        },
        {
          timestamp: new Date('2025-05-28T17:45:08.805Z'),
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '456',
            postStatus: 'PUBLISHED',
            title: 'lorem',
            publishedAt: new Date('2025-05-28T17:45:08.805Z'),
            scheduledAt: null,
            mediaId: null,
            media: null,
            liveUrl: 'https://example.com/blog/lorem-dolor',
          },
        },
        {
          timestamp: new Date('2025-07-28T17:45:08.805Z'),
          itemType: ItemType.BLOG_POST,
          content: {
            postId: '789',
            postStatus: 'SCHEDULED',
            title: 'lorem',
            publishedAt: null,
            scheduledAt: new Date('2025-07-28T17:45:08.805Z'),
            mediaId: null,
            media: null,
            liveUrl: null,
          },
        },
      ];

      const result = mapBlogPosts(input, hostname);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('mapRecommendationGroups', () => {
    it('should filter out groups without any recommendations', () => {
      const input = [
        {
          id: '123',
          metadata: { ranking: '5' },
          recommendations: undefined,
        },
        {
          id: '456',
          metadata: { ranking: '5' },
          recommendations: [],
        },
      ] as unknown as Group[];

      const expectedResult = [];

      const result = mapRecommendationGroups(input);

      expect(result).toEqual(expectedResult);
    });

    it('should map recommendation groups to feed items with ranking as string', () => {
      const now = new Date('2023-06-15T10:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(now);

      const input = [
        {
          id: '123',
          createdAt: new Date('2025-06-01T10:00:00.000Z'),
          metadata: { ranking: null }, // metadata ranking is null, will use PageKeyword
          keyword: {
            keyword: 'example keyword',
            pageKeywords: [
              {
                currentRank: 5,
                originalRank: 25,
              },
            ],
          },
          groupScheduledActions: [
            {
              scheduledAction: {
                publishedAt: null,
                scheduledToBePublishedAt: null,
              },
            },
          ],
          recommendations: [
            {
              type: RecommendationType.META_TITLE,
            },
          ],
          scrapedPage: {
            pageName: 'page name',
            pageType: ScrapedPageType.HOMEPAGE,
            url: '',
          },
        },
        {
          id: '456',
          createdAt: new Date('2025-05-01T10:00:00.000Z'),
          metadata: { ranking: null }, // metadata ranking is null, will use PageKeyword
          keyword: {
            keyword: 'another keyword',
            pageKeywords: [
              {
                currentRank: 8,
                originalRank: 12,
              },
            ],
          },
          groupScheduledActions: [
            {
              scheduledAction: {
                publishedAt: new Date('2025-05-28T17:45:08.805Z'),
                scheduledToBePublishedAt: null,
              },
            },
          ],
          recommendations: [
            {
              type: RecommendationType.META_DESCRIPTION,
            },
          ],
          scrapedPage: {
            pageName: 'page name',
            pageType: ScrapedPageType.HOMEPAGE,
            url: '',
          },
        },
        {
          id: '789',
          createdAt: new Date('2025-06-01T10:00:00.000Z'),
          metadata: { ranking: null }, // metadata ranking is null, will use PageKeyword
          keyword: {
            keyword: 'yet another keyword',
            pageKeywords: [
              {
                currentRank: 22,
                originalRank: 30,
              },
            ],
          },
          groupScheduledActions: [
            {
              scheduledAction: {
                publishedAt: null,
                scheduledToBePublishedAt: new Date('2025-07-28T17:45:08.805Z'),
              },
            },
          ],
          recommendations: [
            {
              type: RecommendationType.META_TITLE,
            },
          ],
          scrapedPage: {
            pageName: 'page name',
            pageType: ScrapedPageType.HOMEPAGE,
            url: '',
          },
        },
      ] as unknown as Group[];

      const expectedResult = [
        {
          timestamp: new Date('2025-06-01T10:00:00.000Z'),
          itemType: ItemType.SEO_RECOMMENDATION,
          content: {
            id: '123',
            publishedAt: null,
            scheduledToBePublishedAt: now,
            keyword: 'example keyword',
            metadata: {
              ranking: null, // from metadata.ranking (null in test)
              rank: {
                current: 5,
                original: 25,
              },
            },
            recommendations: [{ type: RecommendationType.META_TITLE }],
            page: {
              name: 'page name',
              type: ScrapedPageType.HOMEPAGE,
              url: '',
            },
            mediaId: null,
            media: null,
          },
        },
        {
          timestamp: new Date('2025-05-28T17:45:08.805Z'),
          itemType: ItemType.SEO_RECOMMENDATION,
          content: {
            id: '456',
            publishedAt: new Date('2025-05-28T17:45:08.805Z'),
            scheduledToBePublishedAt: now,
            keyword: 'another keyword',
            metadata: {
              ranking: null, // from metadata.ranking (null in test)
              rank: {
                current: 8,
                original: 12,
              },
            },
            recommendations: [{ type: RecommendationType.META_DESCRIPTION }],
            page: {
              name: 'page name',
              type: ScrapedPageType.HOMEPAGE,
              url: '',
            },
            mediaId: null,
            media: null,
          },
        },
        {
          timestamp: new Date('2025-07-28T17:45:08.805Z'),
          itemType: ItemType.SEO_RECOMMENDATION,
          content: {
            id: '789',
            publishedAt: null,
            scheduledToBePublishedAt: new Date('2025-07-28T17:45:08.805Z'),
            keyword: 'yet another keyword',
            metadata: {
              ranking: null, // from metadata.ranking (null in test)
              rank: {
                current: 22,
                original: 30,
              },
            },
            recommendations: [{ type: RecommendationType.META_TITLE }],
            page: {
              name: 'page name',
              type: ScrapedPageType.HOMEPAGE,
              url: '',
            },
            mediaId: null,
            media: null,
          },
        },
      ];

      const result = mapRecommendationGroups(input);

      expect(result).toEqual(expectedResult);
    });

    it('should handle groups with metadata.ranking as string', () => {
      const now = new Date('2023-06-15T10:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(now);

      const input = [
        {
          id: '123',
          createdAt: new Date('2025-06-01T10:00:00.000Z'),
          metadata: { ranking: '15' }, // metadata ranking as string
          keyword: {
            pageKeywords: [
              {
                currentRank: 5,
                originalRank: 25,
              },
            ],
          },
          groupScheduledActions: [
            {
              scheduledAction: {
                publishedAt: null,
                scheduledToBePublishedAt: null,
              },
            },
          ],
          recommendations: [
            {
              type: RecommendationType.META_TITLE,
            },
          ],
          scrapedPage: {
            pageName: 'page name',
            pageType: ScrapedPageType.HOMEPAGE,
            url: '',
          },
        },
      ] as unknown as Group[];

      const expectedResult = [
        {
          timestamp: new Date('2025-06-01T10:00:00.000Z'),
          itemType: ItemType.SEO_RECOMMENDATION,
          content: {
            id: '123',
            publishedAt: null,
            scheduledToBePublishedAt: now,
            keyword: '',
            metadata: {
              ranking: '15', // from metadata.ranking string
              rank: {
                current: 5,
                original: 25,
              },
            },
            recommendations: [{ type: RecommendationType.META_TITLE }],
            page: {
              name: 'page name',
              type: ScrapedPageType.HOMEPAGE,
              url: '',
            },
            mediaId: null,
            media: null,
          },
        },
      ];

      const result = mapRecommendationGroups(input);

      expect(result).toEqual(expectedResult);
    });

    it('should handle groups with null rank values', () => {
      const now = new Date('2023-06-15T10:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(now);

      const input = [
        {
          id: '123',
          createdAt: new Date('2025-06-01T10:00:00.000Z'),
          metadata: { ranking: null },
          keyword: {
            pageKeywords: [
              {
                currentRank: null,
                originalRank: null,
              },
            ],
          },
          groupScheduledActions: [
            {
              scheduledAction: {
                publishedAt: null,
                scheduledToBePublishedAt: null,
              },
            },
          ],
          recommendations: [
            {
              type: RecommendationType.META_TITLE,
            },
          ],
          scrapedPage: {
            pageName: 'page name',
            pageType: ScrapedPageType.HOMEPAGE,
            url: '',
          },
        },
      ] as unknown as Group[];

      const expectedResult = [
        {
          timestamp: new Date('2025-06-01T10:00:00.000Z'),
          itemType: ItemType.SEO_RECOMMENDATION,
          content: {
            id: '123',
            publishedAt: null,
            scheduledToBePublishedAt: now,
            keyword: '',
            metadata: {
              ranking: null,
              rank: {
                current: null,
                original: null,
              },
            },
            recommendations: [{ type: RecommendationType.META_TITLE }],
            page: {
              name: 'page name',
              type: ScrapedPageType.HOMEPAGE,
              url: '',
            },
            mediaId: null,
            media: null,
          },
        },
      ];

      const result = mapRecommendationGroups(input);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('mapLeads', () => {
    it('should map leads to feed items', () => {
      const input: LeadDto[] = [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          handedOffAt: '2025-07-28T17:45:08.805Z',
        },
        {
          id: '2',
          firstName: 'Jane',
          lastName: 'Doe',
          handedOffAt: '2025-07-28T19:45:08.805Z',
        },
        {
          id: '3',
          firstName: 'Alice',
          lastName: 'Smith',
          handedOffAt: '2025-07-29T17:45:08.805Z',
        },
        {
          id: '4',
          firstName: 'Bob',
          lastName: 'Johnson',
          handedOffAt: '2025-07-30T17:45:08.805Z',
        },
      ];

      const expectedResult = [
        {
          timestamp: new Date('2025-07-28T00:00:00.000Z'),
          itemType: ItemType.LEAD_HANDOFF,
          content: {
            leads: [
              {
                id: '1',
                firstName: 'John',
                lastName: 'Doe',
                handedOffAt: '2025-07-28T17:45:08.805Z',
              },
              {
                id: '2',
                firstName: 'Jane',
                lastName: 'Doe',
                handedOffAt: '2025-07-28T19:45:08.805Z',
              },
            ],
          },
        },
        {
          timestamp: new Date('2025-07-29T00:00:00.000Z'),
          itemType: ItemType.LEAD_HANDOFF,
          content: {
            leads: [
              {
                id: '3',
                firstName: 'Alice',
                lastName: 'Smith',
                handedOffAt: '2025-07-29T17:45:08.805Z',
              },
            ],
          },
        },
        {
          timestamp: new Date('2025-07-30T00:00:00.000Z'),
          itemType: ItemType.LEAD_HANDOFF,
          content: {
            leads: [
              {
                id: '4',
                firstName: 'Bob',
                lastName: 'Johnson',
                handedOffAt: '2025-07-30T17:45:08.805Z',
              },
            ],
          },
        },
      ];

      const result = mapLeads(input);

      expect(result).toEqual(expectedResult);
    });
  });

  describe('mapAdPerformance', () => {
    it('should map ad reports to feed items', () => {
      const input = [
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-23'),
          IMPRESSIONS: 9999,
          CLICKS: 999,
          LEADS: 99,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-24'),
          IMPRESSIONS: 100,
          CLICKS: 10,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-25'),
          IMPRESSIONS: 139,
          CLICKS: 11,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-26'),
          IMPRESSIONS: 122,
          CLICKS: 10,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-27'),
          IMPRESSIONS: 152,
          CLICKS: 4,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-28'),
          IMPRESSIONS: 181,
          CLICKS: 15,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-29'),
          IMPRESSIONS: 276,
          CLICKS: 14,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-30'),
          IMPRESSIONS: 186,
          CLICKS: 14,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-01'),
          IMPRESSIONS: 133,
          CLICKS: 14,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-02'),
          IMPRESSIONS: 162,
          CLICKS: 14,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-03'),
          IMPRESSIONS: 153,
          CLICKS: 7,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-04'),
          IMPRESSIONS: 203,
          CLICKS: 14,
          LEADS: 2,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-05'),
          IMPRESSIONS: 192,
          CLICKS: 13,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-06'),
          IMPRESSIONS: 255,
          CLICKS: 15,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-07'),
          IMPRESSIONS: 177,
          CLICKS: 11,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-08'),
          IMPRESSIONS: 79,
          CLICKS: 12,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-09'),
          IMPRESSIONS: 9999,
          CLICKS: 999,
          LEADS: 99,
        },
      ];

      const expectedResult = [
        {
          timestamp: new Date('2025-07-01T00:00:00.000Z'),
          itemType: ItemType.AD_PERFORMANCE,
          content: {
            clicksCount: 78,
            clicksCountFormatted: '78',
            impressionsCount: 1156,
            impressionsCountFormatted: '1,156',
            leadsCount: 6,
            leadsCountFormatted: '6',
            paidLeads: [],
            paidLeadsHref: '/contacts/leads',
          },
        },
        {
          timestamp: new Date('2025-07-08T00:00:00.000Z'),
          itemType: ItemType.AD_PERFORMANCE,
          content: {
            clicksCount: 88,
            clicksCountFormatted: '88',
            impressionsCount: 1275,
            impressionsCountFormatted: '1,275',
            leadsCount: 15,
            leadsCountFormatted: '15',
            paidLeads: [],
            paidLeadsHref: '/contacts/leads',
          },
        },
      ];

      const result = mapAdPerformance(input, []);

      expect(result).toEqual(expectedResult);
    });

    it('should filter out feed items whose all counts equal zero', () => {
      const input = [
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-30'), // Monday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-01'), // Tuesday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-02'), // Wednesday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-03'), // Thursday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-04'), // Friday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-05'), // Saturday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-06'), // Sunday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-07'), // Monday
          IMPRESSIONS: 0,
          CLICKS: 0,
          LEADS: 0,
        },
      ];

      const expectedResult = [];

      const result = mapAdPerformance(input, []);

      expect(result).toEqual(expectedResult);
    });

    it("should return last week's data if monday is the last day of data", () => {
      const input = [
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-30'), // Monday
          IMPRESSIONS: 186,
          CLICKS: 14,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-01'), // Tuesday
          IMPRESSIONS: 133,
          CLICKS: 14,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-02'), // Wednesday
          IMPRESSIONS: 162,
          CLICKS: 14,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-03'), // Thursday
          IMPRESSIONS: 153,
          CLICKS: 7,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-04'), // Friday
          IMPRESSIONS: 203,
          CLICKS: 14,
          LEADS: 2,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-05'), // Saturday
          IMPRESSIONS: 192,
          CLICKS: 13,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-06'), // Sunday
          IMPRESSIONS: 255,
          CLICKS: 15,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-07'), // Monday
          IMPRESSIONS: 177,
          CLICKS: 11,
          LEADS: 3,
        },
      ];

      const expectedResult = [
        {
          timestamp: new Date('2025-07-08T00:00:00.000Z'),
          itemType: ItemType.AD_PERFORMANCE,
          content: {
            clicksCount: 88,
            clicksCountFormatted: '88',
            impressionsCount: 1275,
            impressionsCountFormatted: '1,275',
            leadsCount: 15,
            leadsCountFormatted: '15',
            paidLeads: [],
            paidLeadsHref: '/contacts/leads',
          },
        },
      ];

      const result = mapAdPerformance(input, []);

      expect(result).toEqual(expectedResult);
    });

    it('should add leads to feed items', () => {
      const performanceReports = [
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-23'),
          IMPRESSIONS: 9999,
          CLICKS: 999,
          LEADS: 99,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-24'),
          IMPRESSIONS: 100,
          CLICKS: 10,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-25'),
          IMPRESSIONS: 139,
          CLICKS: 11,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-26'),
          IMPRESSIONS: 122,
          CLICKS: 10,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-27'),
          IMPRESSIONS: 152,
          CLICKS: 4,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-28'),
          IMPRESSIONS: 181,
          CLICKS: 15,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-29'),
          IMPRESSIONS: 276,
          CLICKS: 14,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-06-30'),
          IMPRESSIONS: 186,
          CLICKS: 14,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-01'),
          IMPRESSIONS: 133,
          CLICKS: 14,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-02'),
          IMPRESSIONS: 162,
          CLICKS: 14,
          LEADS: 1,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-03'),
          IMPRESSIONS: 153,
          CLICKS: 7,
          LEADS: 0,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-04'),
          IMPRESSIONS: 203,
          CLICKS: 14,
          LEADS: 2,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-05'),
          IMPRESSIONS: 192,
          CLICKS: 13,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-06'),
          IMPRESSIONS: 255,
          CLICKS: 15,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-07'),
          IMPRESSIONS: 177,
          CLICKS: 11,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-08'),
          IMPRESSIONS: 79,
          CLICKS: 12,
          LEADS: 3,
        },
        {
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          REPORT_DATE: new Date('2025-07-09'),
          IMPRESSIONS: 9999,
          CLICKS: 999,
          LEADS: 99,
        },
      ];

      const paidLeads = [
        {
          LEAD_CREATED_DATE: new Date('2025-05-30T00:00:00.000Z'),
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          LP_LEAD_ID: 'lead-1',
        },
        {
          LEAD_CREATED_DATE: new Date('2025-06-30T00:00:00.000Z'),
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          LP_LEAD_ID: 'lead-2',
        },
        {
          LEAD_CREATED_DATE: new Date('2025-07-06T00:00:00.000Z'),
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          LP_LEAD_ID: 'lead-3',
        },
        {
          LEAD_CREATED_DATE: new Date('2025-07-07T00:00:00.000Z'),
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          LP_LEAD_ID: 'lead-4',
        },
        {
          LEAD_CREATED_DATE: new Date('2025-08-07T00:00:00.000Z'),
          LP_COMPANY_ID: '92460ac2-8f13-4f24-87e5-f0ada0d35af0',
          LP_LEAD_ID: 'lead-5',
        },
      ];

      const expectedResult = [
        {
          timestamp: new Date('2025-07-01T00:00:00.000Z'),
          itemType: ItemType.AD_PERFORMANCE,
          content: {
            clicksCount: 78,
            clicksCountFormatted: '78',
            impressionsCount: 1156,
            impressionsCountFormatted: '1,156',
            leadsCount: 6,
            leadsCountFormatted: '6',
            paidLeads: [
              {
                id: 'lead-2',
              },
            ],
            paidLeadsHref:
              '/contacts/leads?filters=%7B%22ids%22%3A%5B%22lead-2%22%5D%7D',
          },
        },
        {
          timestamp: new Date('2025-07-08T00:00:00.000Z'),
          itemType: ItemType.AD_PERFORMANCE,
          content: {
            clicksCount: 88,
            clicksCountFormatted: '88',
            impressionsCount: 1275,
            impressionsCountFormatted: '1,275',
            leadsCount: 15,
            leadsCountFormatted: '15',
            paidLeads: [
              {
                id: 'lead-3',
              },
              {
                id: 'lead-4',
              },
            ],
            paidLeadsHref:
              '/contacts/leads?filters=%7B%22ids%22%3A%5B%22lead-3%22%2C%22lead-4%22%5D%7D',
          },
        },
      ];

      const result = mapAdPerformance(performanceReports, paidLeads);

      expect(result).toEqual(expectedResult);
    });
  });
});
