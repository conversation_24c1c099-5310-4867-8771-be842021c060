import { Injectable } from '@nestjs/common';
import { CMSService } from 'src/cms/cms.service';
import { ApiGatewayService } from 'src/common/services/api-gateway/api-gateway.service';
import { MediaDto } from 'src/common/services/api-gateway/dto/media.dto';
import { TenantService } from 'src/common/services/tenant/tenant.service';
import { Entitlement } from 'src/common/services/tenant/tentant.types';
import { WebsiteService } from 'src/common/services/website/website.service';
import { GroupService } from 'src/group/group.service';
import { SnowflakeService } from 'src/snowflake/snowflake.service';

import HomepageFeed, { FeedItem } from './entities/homepage-feed.entity';
import { FeedFilters } from './homepage-feed-filters';
import {
  mapAdPerformance,
  mapBlogPosts,
  mapLeads,
  mapRecommendationGroups,
} from './utils';

export enum RelevantEntitlement {
  AILN = 'AI Lead Nurturing (AILN)',
  BLOG = 'AI Blog Specialist',
  SEO = 'AI SEO Specialist',
  PAID = 'AI Advertising Specialist',
}

@Injectable()
export class HomepageFeedService {
  constructor(
    private readonly cmsService: CMSService,
    private readonly groupService: GroupService,
    private readonly apiGatewayService: ApiGatewayService,
    private readonly snowflakeService: SnowflakeService,
    private readonly tenantService: TenantService,
    private readonly websiteService: WebsiteService,
  ) {}

  private getValidEntitlements(
    entitlements: Entitlement[],
  ): RelevantEntitlement[] {
    const validEntitlements: RelevantEntitlement[] = [];

    entitlements.forEach(entitlement => {
      const productName = entitlement.product.name;
      const nameMatches = Object.values(RelevantEntitlement).includes(
        productName as RelevantEntitlement,
      );
      const isValid = nameMatches && entitlement.entitled;

      if (isValid) {
        validEntitlements.push(productName as RelevantEntitlement);
      }
    });

    return validEntitlements;
  }

  // TODO: add error handling and logging
  async getHomepageFeed(
    companyId: string,
    filters?: FeedFilters,
  ): Promise<HomepageFeed> {
    const entitlements = await this.tenantService.getEntitlements(companyId);
    const validEntitlements = this.getValidEntitlements(entitlements);

    let items: FeedItem[] = [];

    const [
      blogPosts,
      recommendationGroups,
      leads,
      adPerformanceReports,
      paidLeads,
    ] = await Promise.all([
      validEntitlements.includes(RelevantEntitlement.BLOG)
        ? this.cmsService.findAIGeneratedBlogPosts(companyId)
        : [],
      validEntitlements.includes(RelevantEntitlement.SEO)
        ? this.groupService.findSurfacedGroupsByCompany(companyId)
        : [],
      validEntitlements.includes(RelevantEntitlement.AILN)
        ? this.apiGatewayService.findHandedOffLeads(companyId)
        : [],
      validEntitlements.includes(RelevantEntitlement.PAID)
        ? this.snowflakeService.findDailyAdPerformance(companyId)
        : [],
      validEntitlements.includes(RelevantEntitlement.PAID)
        ? this.snowflakeService.findPaidAdLeads(companyId)
        : [],
    ]);

    const mediaIds = blogPosts
      .map(
        post =>
          post.media?.find(({ displayOrder }) => displayOrder === 1)?.mediaId,
      )
      .filter(Boolean) as string[];

    let mediaData: MediaDto[] = [];
    try {
      mediaData = await this.apiGatewayService.findMedia(mediaIds);
    } catch (error) {
      console.error('Error fetching media data for homepage feed:', error);
      mediaData = [];
    }
    const mediaMap = new Map(mediaData.map(media => [media.id, media]));

    const [company] = await this.tenantService.find({ displayId: companyId });
    const liveBrandWebsites =
      await this.websiteService.getLiveBrandWebsites(companyId);
    const hostname =
      liveBrandWebsites.find(
        website => company.website && company.website === website.hostname,
      )?.hostname || company.website;

    items = items.concat(mapBlogPosts(blogPosts, hostname, mediaMap));
    items = items.concat(
      mapRecommendationGroups(
        recommendationGroups,
        this.buildMediaUrls.bind(this),
      ),
    );
    items = items.concat(mapLeads(leads));
    items = items.concat(mapAdPerformance(adPerformanceReports, paidLeads));

    // apply filters
    items = items.filter(feedItem => {
      if (feedItem.timestamp) {
        if (filters?.timestampStart) {
          if (new Date(feedItem.timestamp) < new Date(filters.timestampStart)) {
            return false;
          }
        }

        if (filters?.timestampEnd) {
          if (new Date(feedItem.timestamp) > new Date(filters.timestampEnd)) {
            return false;
          }
        }
      }

      return true;
    });

    // TODO: filter by timestamp parameters
    items.sort((a, b) => {
      if (a.timestamp === null && b.timestamp === null) return 0;

      if (a.timestamp === null) return -1;

      if (b.timestamp === null) return 1;

      return b.timestamp.getTime() - a.timestamp.getTime();
    });

    return { items };
  }

  private buildMediaUrls(mediaId: string | null): MediaDto | null {
    if (!mediaId) return null;

    const environment = process.env.NODE_ENV || 'staging';
    const sizeMap = {
      thumbnailUrl: 320,
      smallUrl: 960,
      mediumUrl: 1280,
      largeUrl: 1920,
    };

    return Object.entries(sizeMap).reduce(
      (acc, [name, size]) => {
        acc[name] =
          `https://lp-seo-page-screenshots-${environment}.lp-cdn.com/cdn-cgi/image/w=${size},h=${Math.round(
            size * 0.56,
          )},fit=crop,g=top,f=auto,q=75/full/${mediaId}.png`;
        return acc;
      },
      { id: mediaId } as MediaDto,
    );
  }
}
