import { BlogPost } from 'src/cms/dto/blog-post.dto';
import { LeadDto } from 'src/common/services/api-gateway/dto/lead.dto';
import { MediaDto } from 'src/common/services/api-gateway/dto/media.dto';
import { Group } from 'src/group/entities/group.entity';
import { ScrapedPageType } from 'src/scraped-page/entities/scraped-page.entity';
import { DailyAdPerformanceDto } from 'src/snowflake/dto/daily-ad-performance.dto';
import { FeedAdPerformance } from 'src/snowflake/dto/feed-ad-performance.dto';
import { PaidAdLeadDto } from 'src/snowflake/dto/paid-ad-lead.dto';

import { FeedItem, ItemType } from './entities/homepage-feed.entity';

const START_OF_WEEK = 2;

export const mapBlogPosts = (
  blogPosts: BlogPost[],
  hostname?: string,
  mediaMap?: Map<string, MediaDto>,
): FeedItem[] => {
  return blogPosts.map(blogPost => {
    const { postId, postStatus, title, publishedAt, scheduledAt, media, slug } =
      blogPost;
    const blogPostTimestamp = publishedAt || scheduledAt;
    const mediaId =
      media?.find(({ displayOrder }) => displayOrder === 1)?.mediaId || null;
    const mediaObject =
      mediaId && mediaMap ? mediaMap.get(mediaId) || null : null;

    const hostnameWithProtocol = hostname?.startsWith('http')
      ? hostname
      : `https://${hostname}`;
    const liveUrl =
      postStatus === 'PUBLISHED' && hostname
        ? `${hostnameWithProtocol}/blog/${slug}`
        : null;

    return {
      timestamp: blogPostTimestamp ? new Date(blogPostTimestamp) : null,
      itemType: ItemType.BLOG_POST,
      content: {
        postId,
        postStatus,
        title,
        publishedAt: publishedAt ? new Date(publishedAt) : null,
        scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
        mediaId,
        media: mediaObject,
        liveUrl,
      },
    };
  });
};

export const mapRecommendationGroups = (
  recommendationGroups: Group[],
  buildMediaUrls?: (mediaId: string | null) => MediaDto | null,
): FeedItem[] => {
  return recommendationGroups
    .filter(group => !!group.recommendations?.length) // TODO: consider moving to GroupService to apply globally
    .filter(group => !!group.groupScheduledActions?.[0]?.scheduledAction)
    .map(group => {
      const { id, metadata, recommendations, scrapedPage } = group;
      const scheduledAction = group.groupScheduledActions![0].scheduledAction;
      const groupTimestamp =
        scheduledAction.publishedAt ||
        scheduledAction.scheduledToBePublishedAt ||
        group.createdAt;
      // TODO: update these to use groupScheduledAction (singular)
      // TODO: remove fallback date once we're sure all groups have an action
      const publishedAt = scheduledAction.publishedAt;
      const scheduledToBePublishedAt =
        scheduledAction.scheduledToBePublishedAt! || new Date();

      const mediaId = recommendations![0]?.scrape?.mediaId || null;
      const mediaObject =
        mediaId && buildMediaUrls ? buildMediaUrls(mediaId) : null;

      // Source ranking from PageKeyword
      const pageKeyword = group.keyword?.pageKeywords?.[0];
      const currentRank = pageKeyword?.currentRank || null;
      const originalRank = pageKeyword?.originalRank || null;

      // Not really nullable. A recommendation can only exist if it has a keyword.
      const keyword = group.keyword?.keyword || '';

      const feedItem: FeedItem = {
        timestamp: groupTimestamp ? new Date(groupTimestamp) : null,
        itemType: ItemType.SEO_RECOMMENDATION,
        content: {
          id,
          publishedAt,
          scheduledToBePublishedAt,
          keyword,
          metadata: {
            ranking: metadata.ranking, // deprecated
            rank: {
              current: currentRank,
              original: originalRank,
            },
          },
          // FIXME: types seem to be incorrect here
          // is it possible for a group to have no recommendations?
          // is it possible for a recommendation to have no page?
          page: {
            name: scrapedPage?.pageName || '',
            type: scrapedPage?.pageType || ScrapedPageType.HOMEPAGE,
            url: scrapedPage?.url || '',
          },
          recommendations: recommendations!.map(({ type }) => ({ type })),
          mediaId,
          media: mediaObject,
        },
      };

      return feedItem;
    });
};

export const mapLeads = (leads: LeadDto[]): FeedItem[] => {
  const groupedLeads: Record<string, LeadDto[]> = leads.reduce((acc, lead) => {
    const timestamp = new Date(lead.handedOffAt);
    timestamp.setHours(0, 0, 0, 0);
    const key = timestamp.toISOString();

    if (!acc[key]) {
      acc[key] = [lead];
    } else {
      acc[key] = [...acc[key], lead];
    }

    return acc;
  }, {});

  return Object.entries(groupedLeads).map(([date, leads]) => {
    return {
      timestamp: new Date(date),
      itemType: ItemType.LEAD_HANDOFF,
      content: { leads },
    };
  });
};

const getNextStartOfWeek = (date: Date): Date => {
  const dayOfWeek = date.getDay();
  const daysToAdd = (START_OF_WEEK - dayOfWeek + 7) % 7 || 7;

  const nextStartOfWeek = new Date(date);
  nextStartOfWeek.setDate(date.getDate() + daysToAdd);
  nextStartOfWeek.setUTCHours(0, 0, 0, 0);

  return nextStartOfWeek;
};

const getPreviousStartOfWeek = (date: Date): Date => {
  const dayOfWeek = date.getDay();
  const daysToSubtract = (dayOfWeek + 7 - START_OF_WEEK) % 7;

  const previousStartOfWeek = new Date(date);
  previousStartOfWeek.setDate(date.getDate() - daysToSubtract);
  previousStartOfWeek.setUTCHours(0, 0, 0, 0);

  return previousStartOfWeek;
};

export const mapAdPerformance = (
  reports: DailyAdPerformanceDto[],
  paidLeads: PaidAdLeadDto[],
): FeedItem[] => {
  if (reports.length === 0) return [];

  // add fake row for today
  const today = new Date(reports[reports.length - 1].REPORT_DATE);
  today.setDate(today.getDate() + 1);
  reports.push({
    REPORT_DATE: today,
    IMPRESSIONS: 0,
    CLICKS: 0,
    LEADS: 0,
  });

  const firstStartOfWeek = getNextStartOfWeek(reports[0].REPORT_DATE);
  const lastStartOfWeek = getPreviousStartOfWeek(
    reports[reports.length - 1].REPORT_DATE,
  );

  const groupedReports = reports
    .filter(report => {
      return (
        report.REPORT_DATE >= firstStartOfWeek &&
        report.REPORT_DATE < lastStartOfWeek
      );
    })
    .reduce((groups, report) => {
      const nextStartOfWeek = getNextStartOfWeek(
        report.REPORT_DATE,
      ).toISOString();

      if (groups[nextStartOfWeek]) {
        groups[nextStartOfWeek] = {
          impressionsCount:
            groups[nextStartOfWeek].impressionsCount + report.IMPRESSIONS,
          clicksCount: groups[nextStartOfWeek].clicksCount + report.CLICKS,
          leadsCount: groups[nextStartOfWeek].leadsCount + report.LEADS,
        };
      } else {
        groups[nextStartOfWeek] = {
          impressionsCount: report.IMPRESSIONS,
          clicksCount: report.CLICKS,
          leadsCount: report.LEADS,
        };
      }

      return groups;
    }, {});

  paidLeads.forEach(lead => {
    const nextStartOfWeek = getNextStartOfWeek(
      lead.LEAD_CREATED_DATE,
    ).toISOString();

    if (groupedReports[nextStartOfWeek]) {
      if (groupedReports[nextStartOfWeek].paidLeads) {
        groupedReports[nextStartOfWeek].paidLeads.push({
          id: lead.LP_LEAD_ID,
        });
      } else {
        groupedReports[nextStartOfWeek].paidLeads = [{ id: lead.LP_LEAD_ID }];
      }
    }
  });

  const feedItems: FeedItem[] = Object.entries(groupedReports)
    .map(([timestamp, report]: [string, FeedAdPerformance]) => {
      let paidLeadsHref = '/contacts/leads';

      const leadIds = report.paidLeads?.map(lead => lead.id);

      if (leadIds?.length) {
        const filters = { ids: leadIds };
        const filterParam = encodeURIComponent(JSON.stringify(filters));

        paidLeadsHref += `?filters=${filterParam}`;
      }

      return {
        timestamp: new Date(timestamp),
        itemType: ItemType.AD_PERFORMANCE,
        content: {
          impressionsCount: report.impressionsCount,
          clicksCount: report.clicksCount,
          leadsCount: report.leadsCount,
          impressionsCountFormatted: report.impressionsCount.toLocaleString(),
          clicksCountFormatted: report.clicksCount.toLocaleString(),
          leadsCountFormatted: report.leadsCount.toLocaleString(),
          paidLeads: report.paidLeads || [],
          paidLeadsHref,
        },
      };
    })
    .filter(feedItem => {
      const { impressionsCount, clicksCount, leadsCount } = feedItem.content;

      return impressionsCount || clicksCount || leadsCount;
    });

  return feedItems;
};
