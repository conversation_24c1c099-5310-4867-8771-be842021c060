import request from 'supertest';

import {
  app,
  dataSource,
  createTestGroupData,
  createTestKeywordData,
  createTestScrapedPageData,
  createTestRecommendationData,
  createTestScrapeData,
  generateTestCompanyId,
} from './setup';
import { Group } from '../src/group/entities/group.entity';
import { Keyword } from '../src/keyword/entities/keyword.entity';
import { Recommendation } from '../src/recommendation/entities/recommendation.entity';
import { Scrape } from '../src/scrape/entities/scrape.entity';
import { ScrapedPage } from '../src/scraped-page/entities/scraped-page.entity';

describe('Group (e2e)', () => {
  let groupRepository: any;
  let keywordRepository: any;
  let scrapedPageRepository: any;
  let recommendationRepository: any;
  let scrapeRepository: any;

  beforeAll(() => {
    groupRepository = dataSource.getRepository(Group);
    keywordRepository = dataSource.getRepository(Keyword);
    scrapedPageRepository = dataSource.getRepository(ScrapedPage);
    recommendationRepository = dataSource.getRepository(Recommendation);
    scrapeRepository = dataSource.getRepository(Scrape);
  });

  describe('GET /group', () => {
    it('should return empty array when no groups exist', async () => {
      const companyId = generateTestCompanyId();
      const response = await request(app.getHttpServer())
        .get('/group')
        .query({ companyId })
        .expect(200);

      expect(response.body).toEqual([]);
    });

    it('should return empty array when no surfaced groups exist', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);

      const response = await request(app.getHttpServer())
        .get('/group')
        .query({ companyId })
        .expect(200);

      // Groups without scheduled actions are not surfaced, so should return empty array
      expect(response.body).toHaveLength(0);
    });

    it('should return empty array for companies with no surfaced groups', async () => {
      const companyId1 = generateTestCompanyId();
      const companyId2 = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage1 = createTestScrapedPageData(undefined, companyId1);
      const scrapedPage2 = createTestScrapedPageData(undefined, companyId2);
      const group1 = createTestGroupData(
        undefined,
        companyId1,
        keyword.id,
        scrapedPage1.id,
      );
      const group2 = createTestGroupData(
        undefined,
        companyId2,
        keyword.id,
        scrapedPage2.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save([scrapedPage1, scrapedPage2]);
      await groupRepository.save([group1, group2]);

      const response = await request(app.getHttpServer())
        .get('/group')
        .query({ companyId: companyId1 })
        .expect(200);

      // Groups without scheduled actions are not surfaced, so should return empty array
      expect(response.body).toHaveLength(0);
    });
  });

  describe('GET /group/:id', () => {
    it('should return a specific group by id', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);

      const response = await request(app.getHttpServer())
        .get(`/group/${group.id}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: group.id,
        companyId: group.companyId,
        keywordId: group.keywordId,
        scrapedPageId: group.scrapedPageId,
        title: group.title,
        description: group.description,
      });
    });

    it('should return 404 for non-existent group', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';

      await request(app.getHttpServer())
        .get(`/group/${nonExistentId}`)
        .expect(404);
    });
  });

  describe('POST /group', () => {
    it('should create a new group', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);

      const createGroupDto = {
        companyId,
        scrapedPageId: scrapedPage.id,
        title: 'Test Group Title',
        description: 'Test Group Description',
        metadata: {
          ranking: 50,
          searchVolume: 5000,
        },
      };

      const response = await request(app.getHttpServer())
        .post('/group')
        .send(createGroupDto)
        .expect(201);

      expect(response.body).toMatchObject({
        companyId: createGroupDto.companyId,
        scrapedPageId: createGroupDto.scrapedPageId,
        title: createGroupDto.title,
        description: createGroupDto.description,
        metadata: createGroupDto.metadata,
      });
      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();
      expect(response.body.updatedAt).toBeDefined();
    });

    it('should return 400 for invalid data', async () => {
      const invalidDto = {
        companyId: generateTestCompanyId().slice(0, 8), // Use a shorter, invalid UUID
        title: '', // Empty title
      };

      await request(app.getHttpServer())
        .post('/group')
        .send(invalidDto)
        .expect(400);
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteDto = {
        title: 'Test Group',
        description: 'Test Description',
      };

      await request(app.getHttpServer())
        .post('/group')
        .send(incompleteDto)
        .expect(400);
    });
  });

  describe('PATCH /group/:id', () => {
    it('should update an existing group', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);

      const updateDto = {
        title: 'Updated Group Title',
        description: 'Updated Group Description',
        metadata: {
          ranking: 25,
          searchVolume: 7500,
        },
      };

      const response = await request(app.getHttpServer())
        .patch(`/group/${group.id}`)
        .send(updateDto)
        .expect(200);

      expect(response.body).toMatchObject({
        id: group.id,
        companyId: group.companyId,
        keywordId: group.keywordId,
        scrapedPageId: group.scrapedPageId,
        title: updateDto.title,
        description: updateDto.description,
        metadata: updateDto.metadata,
      });
    });

    it('should return 404 for non-existent group', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';
      const updateDto = {
        title: 'Updated Title',
      };

      await request(app.getHttpServer())
        .patch(`/group/${nonExistentId}`)
        .send(updateDto)
        .expect(404);
    });

    it('should return 400 for invalid update data', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);

      const invalidUpdateDto = {
        title: '', // Empty title
      };

      await request(app.getHttpServer())
        .patch(`/group/${group.id}`)
        .send(invalidUpdateDto)
        .expect(400); // API returns 400 for validation errors
    });
  });

  describe('DELETE /group/:id', () => {
    it('should delete an existing group', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);

      await request(app.getHttpServer())
        .delete(`/group/${group.id}`)
        .expect(200);

      // Verify group is deleted
      const deletedGroup = await groupRepository.findOne({
        where: { id: group.id },
      });
      expect(deletedGroup).toBeNull();
    });

    it('should return 404 for non-existent group', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';

      await request(app.getHttpServer())
        .delete(`/group/${nonExistentId}`)
        .expect(404);
    });

    it('should return 500 for invalid uuid', async () => {
      await request(app.getHttpServer())
        .delete(`/group/invalid-uuid-format`)
        .expect(500); // Currently returns 500 due to database error, should be 400 in future
    });
  });

  describe('Group with recommendations', () => {
    it('should create group with recommendations', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const response = await request(app.getHttpServer())
        .get(`/group/${group.id}`)
        .expect(200);

      expect(response.body.recommendations).toBeDefined();
      expect(response.body.recommendations).toHaveLength(1);
      expect(response.body.recommendations[0].id).toBe(recommendation.id);
    });
  });

  describe('Group metadata handling', () => {
    it('should handle complex metadata', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);

      const complexMetadata = {
        ranking: 15,
        searchVolume: 12000,
        difficulty: 45,
        cpc: 2.5,
        competition: 'high',
        opportunities: ['featured_snippets', 'voice_search'],
        lastUpdated: new Date().toISOString(),
      };

      const createGroupDto = {
        companyId,
        scrapedPageId: scrapedPage.id,
        title: 'Complex Metadata Group',
        description: 'Group with complex metadata',
        metadata: complexMetadata,
      };

      const response = await request(app.getHttpServer())
        .post('/group')
        .send(createGroupDto)
        .expect(201);

      expect(response.body.metadata).toEqual(complexMetadata);
    });
  });
});
