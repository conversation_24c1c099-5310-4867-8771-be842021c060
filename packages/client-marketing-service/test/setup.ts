import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { useContainer } from 'class-validator';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { TestModule } from './test.module';

export let app: INestApplication;
export let dataSource: DataSource;

// Test data storage for cleanup
export const testData = {
  companies: new Set<string>(),
  keywords: new Set<string>(),
  groups: new Set<string>(),
  recommendations: new Set<string>(),
  scrapedPages: new Set<string>(),
  scrapes: new Set<string>(),
  pageKeywords: new Set<string>(),
};

export async function resetDatabase() {
  if (!dataSource) return;

  const entities = dataSource.entityMetadatas;
  for (const entity of entities) {
    if (entity.tableType === 'view') continue;
    const repository = dataSource.getRepository(entity.name);
    await repository.query(
      `TRUNCATE ${entity.schema}.${entity.tableName} RESTART IDENTITY CASCADE;`,
    );
  }

  // Clear test data sets
  Object.values(testData).forEach(set => set.clear());
}

export function generateTestCompanyId(): string {
  const companyId = uuidv4();
  testData.companies.add(companyId);
  return companyId;
}

export function generateTestKeywordId(): string {
  const keywordId = uuidv4();
  testData.keywords.add(keywordId);
  return keywordId;
}

export function generateTestGroupId(): string {
  const groupId = uuidv4();
  testData.groups.add(groupId);
  return groupId;
}

export function generateTestRecommendationId(): string {
  const recommendationId = uuidv4();
  testData.recommendations.add(recommendationId);
  return recommendationId;
}

export function generateTestScrapedPageId(): string {
  const scrapedPageId = uuidv4();
  testData.scrapedPages.add(scrapedPageId);
  return scrapedPageId;
}

export function generateTestScrapeId(): string {
  const scrapeId = uuidv4();
  testData.scrapes.add(scrapeId);
  return scrapeId;
}

export function generateTestPageKeywordId(): string {
  const pageKeywordId = uuidv4();
  testData.pageKeywords.add(pageKeywordId);
  return pageKeywordId;
}

export function createTestCompanyData(companyId?: string) {
  return {
    id: companyId || generateTestCompanyId(),
    name: `Test Company ${uuidv4().slice(0, 8)}`,
    domain: `test-${uuidv4().slice(0, 8)}.com`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function createTestKeywordData(
  keywordId?: string,
  keyword?: string,
  metadata?: { searchVolume: number; difficulty: number },
) {
  return {
    id: keywordId || generateTestKeywordId(),
    keyword: keyword || `test keyword ${uuidv4().slice(0, 8)}`,
    metadata: metadata || {
      searchVolume: Math.floor(Math.random() * 10000) + 100,
      difficulty: Math.floor(Math.random() * 100) + 1,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function createTestGroupData(
  groupId?: string,
  companyId?: string,
  keywordId?: string,
  scrapedPageId?: string,
) {
  return {
    id: groupId || generateTestGroupId(),
    companyId: companyId || generateTestCompanyId(),
    keywordId: keywordId || generateTestKeywordId(),
    scrapedPageId: scrapedPageId || generateTestScrapedPageId(),
    title: `Test Group ${uuidv4().slice(0, 8)}`,
    description: `Test description for group ${uuidv4().slice(0, 8)}`,
    metadata: {
      keyword: 'test-keyword',
      ranking: Math.floor(Math.random() * 100) + 1,
      searchVolume: Math.floor(Math.random() * 10000) + 100,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function createTestRecommendationData(
  recommendationId?: string,
  groupId?: string,
  scrapeId?: string,
) {
  return {
    id: recommendationId || generateTestRecommendationId(),
    groupId: groupId || generateTestGroupId(),
    scrapeId: scrapeId || generateTestScrapeId(),
    type: 'META_TITLE',
    currentValue: `Current title ${uuidv4().slice(0, 8)}`,
    recommendationValue: `Recommended title ${uuidv4().slice(0, 8)}`,
    reasoning: `Test reasoning for recommendation ${uuidv4().slice(0, 8)}`,
    status: 'PENDING',
    metadata: {
      priority: Math.floor(Math.random() * 5) + 1,
      impact: Math.floor(Math.random() * 100) + 1,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    statusUpdatedAt: new Date(),
  };
}

export function createTestScrapedPageData(
  scrapedPageId?: string,
  companyId?: string,
) {
  return {
    id: scrapedPageId || generateTestScrapedPageId(),
    companyId: companyId || generateTestCompanyId(),
    url: `https://test-${uuidv4().slice(0, 8)}.com/page`,
    pageName: `Test Page ${uuidv4().slice(0, 8)}`,
    pageType: 'HOMEPAGE',
    metadata: {
      wordCount: Math.floor(Math.random() * 2000) + 100,
      headings: Math.floor(Math.random() * 10) + 1,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function createTestScrapeData(
  scrapeId?: string,
  scrapedPageId?: string,
  companyId?: string,
) {
  return {
    id: scrapeId || generateTestScrapeId(),
    companyId: companyId || generateTestCompanyId(),
    scrapedPageId: scrapedPageId || generateTestScrapedPageId(),
    rawHtml: '<html><body><h1>Test Content</h1></body></html>',
    markdown: '# Test Content\n\nThis is test content.',
    currentScrapedValues: {
      title: 'Test Page Title',
      description: 'Test page description',
      headings: ['Test Heading'],
    },
    scrapedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export function createTestPageKeywordData(
  pageKeywordId?: string,
  keywordId?: string,
  scrapedPageId?: string,
) {
  return {
    id: pageKeywordId || generateTestPageKeywordId(),
    keywordId: keywordId || generateTestKeywordId(),
    scrapedPageId: scrapedPageId || generateTestScrapedPageId(),
    rank: Math.floor(Math.random() * 100) + 1,
    searchVolume: Math.floor(Math.random() * 10000) + 100,
    metadata: {
      difficulty: Math.floor(Math.random() * 100) + 1,
      cpc: Math.random() * 10 + 0.1,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

async function initServer() {
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [TestModule],
  }).compile();

  app = moduleFixture.createNestApplication();

  useContainer(app.select(TestModule), { fallbackOnErrors: true });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  app.enableShutdownHooks();

  await app.init();

  // Get the data source for database operations
  dataSource = app.get<DataSource>(DataSource);
}

global.beforeAll(async () => {
  await initServer();
});

global.afterAll(async () => {
  await resetDatabase();
  await app.close();
});

global.beforeEach(async () => {
  await resetDatabase();
});
