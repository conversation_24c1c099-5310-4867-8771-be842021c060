{"rootDir": "../", "modulePaths": ["<rootDir>"], "moduleFileExtensions": ["js", "json", "ts"], "testEnvironment": "node", "testRegex": "\\.e2e-spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "setupFilesAfterEnv": ["<rootDir>/test/jest.setup.ts", "<rootDir>/test/setup.ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}, "detectOpenHandles": true, "extensionsToTreatAsEsm": [".ts"], "globals": {"ts-jest": {"useESM": true}}, "testTimeout": 30000, "maxWorkers": 1}