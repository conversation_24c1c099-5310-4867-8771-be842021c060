// Mock <PERSON> module before any imports
jest.mock('langfuse', () => ({
  Langfuse: jest.fn().mockImplementation(() => ({
    trace: jest.fn(),
    span: jest.fn(),
    generation: jest.fn(),
    score: jest.fn(),
    event: jest.fn(),
    flush: jest.fn(),
    getPrompt: jest.fn().mockResolvedValue({
      getLangchainPrompt: () => 'Test prompt template',
      name: 'test-prompt',
      type: 'text',
    }),
  })),
  TextPromptClient: jest.fn().mockImplementation(() => ({
    score: jest.fn(),
    getLangchainPrompt: () => 'Test prompt template',
  })),
  ChatPromptClient: jest.fn().mockImplementation(() => ({
    score: jest.fn(),
    prompt: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: 'Hello' },
    ],
  })),
}));

// Mock langfuse-langchain module
jest.mock('langfuse-langchain', () => ({
  CallbackHandler: jest.fn().mockImplementation(() => ({
    handleLLMStart: jest.fn(),
    handleLLMNewToken: jest.fn(),
    handleLLMEnd: jest.fn(),
    handleLLMError: jest.fn(),
    handleChainStart: jest.fn(),
    handleChainEnd: jest.fn(),
    handleChainError: jest.fn(),
    handleToolStart: jest.fn(),
    handleToolEnd: jest.fn(),
    handleToolError: jest.fn(),
    handleAgentAction: jest.fn(),
    handleAgentEnd: jest.fn(),
    handleText: jest.fn(),
    handleRetrieverStart: jest.fn(),
    handleRetrieverEnd: jest.fn(),
    handleRetrieverError: jest.fn(),
    handleRetrieverChunk: jest.fn(),
    handleEmbeddingStart: jest.fn(),
    handleEmbeddingEnd: jest.fn(),
    handleEmbeddingError: jest.fn(),
  })),
}));

// Mock @langchain/core/prompts module
jest.mock('@langchain/core/prompts', () => ({
  ChatPromptTemplate: {
    fromMessages: jest.fn().mockImplementation(messages => ({
      messages,
      template: 'Mock chat prompt template',
    })),
  },
  PromptTemplate: {
    fromTemplate: jest.fn().mockImplementation(template => ({
      template,
      type: 'prompt',
    })),
  },
}));
