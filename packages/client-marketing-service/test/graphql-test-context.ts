import { ExecutionContext } from '@nestjs/common';

// Mock GraphQL context for tests that bypasses authorization
export function createTestGraphQLContext() {
  return {
    req: {
      authContext: {
        can: jest.fn().mockResolvedValue(true),
        getCompanyId: jest.fn().mockResolvedValue('test-company-id'),
        getUserId: jest.fn().mockResolvedValue('test-user-id'),
        getPermissions: jest
          .fn()
          .mockResolvedValue(['read', 'write', 'create', 'update', 'delete']),
        isSuperUser: jest.fn().mockResolvedValue(true),
      },
    },
  };
}

// Helper function to create a test GraphQL execution context
export function createTestExecutionContext(): ExecutionContext {
  const mockContext = createTestGraphQLContext();

  return {
    getType: () => 'graphql',
    getHandler: () => ({}) as any,
    getClass: () => ({}) as any,
    getArgs: () => [],
    getArgByIndex: () => ({}) as any,
    switchToHttp: () => ({
      getRequest: () => mockContext.req,
      getResponse: () => ({}) as any,
      getNext: () => ({}) as any,
    }),
    switchToRpc: () => ({
      getData: () => ({}) as any,
      getContext: () => ({}) as any,
    }),
    switchToWs: () => ({
      getData: () => ({}) as any,
      getClient: () => ({}) as any,
      getPattern: () => '',
    }),
  } as ExecutionContext;
}
