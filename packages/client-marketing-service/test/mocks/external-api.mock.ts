import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class MockExternalApiService {
  private readonly logger = new Logger(MockExternalApiService.name);

  /**
   * Mock website service API calls
   */
  public async getCompanyIdByHostname(hostname: string): Promise<string> {
    this.logger.debug(`Mock: Getting company ID for hostname: ${hostname}`);
    return Promise.resolve('test-company-id');
  }

  /**
   * Mock any external HTTP calls
   */
  public async makeHttpRequest(url: string): Promise<any> {
    this.logger.debug(`Mock: Making HTTP request to: ${url}`);
    return Promise.resolve({
      data: { success: true, message: 'Mock response' },
      status: 200,
      statusText: 'OK',
    });
  }

  /**
   * Mock GraphQL context creation
   */
  public createMockGraphQLContext() {
    return {
      req: {
        authContext: {
          can: jest.fn().mockResolvedValue(true),
          getCompanyId: jest.fn().mockResolvedValue('test-company-id'),
          getUserId: jest.fn().mockResolvedValue('test-user-id'),
          getPermissions: jest
            .fn()
            .mockResolvedValue(['read', 'write', 'create', 'update', 'delete']),
          isSuperUser: jest.fn().mockResolvedValue(true),
        },
        headers: {
          authorization: 'Bearer test-token',
          'content-type': 'application/json',
        },
        body: {},
        query: {},
      },
    };
  }
}
