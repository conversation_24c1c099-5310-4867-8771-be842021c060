import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class MockLangfuseService {
  private readonly logger = new Logger(MockLangfuseService.name);

  /**
   * Sanitize prompt content by replacing {{ with {
   */
  public sanitizePromptContent(content: string): string {
    this.logger.debug('Sanitizing prompt content');
    return content.replace(/{{(.*?)}}/g, '{$1}');
  }

  /**
   * Convert LangFuse chat prompt into LangChain ChatPromptTemplate
   */
  public convertLangfuseChatPromptToTemplate(messages: any[]): any {
    this.logger.debug(
      'Converting LangFuse prompt to LangChain template',
      messages,
    );
    return {
      messages: messages.map(msg => {
        const role =
          msg.role === 'user'
            ? 'human'
            : msg.role === 'assistant'
              ? 'ai'
              : 'system';

        const content = this.sanitizePromptContent(msg.content);
        return [role, content];
      }),
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public convertLangfuseTextPromptToTemplate(_prompt: any): any {
    this.logger.debug('Converting LangFuse text prompt to LangChain template');
    return { template: 'Test prompt template' };
  }

  /**
   * Get a text prompt from LangFuse by name
   */
  public getTextPrompt(name: string): Promise<any> {
    this.logger.debug(`Mock: Fetching text prompt from LangFuse: ${name}`);
    return Promise.resolve({
      getLangchainPrompt: () => 'Test prompt template',
      name,
      type: 'text',
    });
  }

  /**
   * Get a chat prompt from LangFuse by name
   */
  public getChatPrompt(name: string): Promise<any> {
    this.logger.debug(`Mock: Fetching chat prompt from LangFuse: ${name}`);
    return Promise.resolve({
      prompt: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Hello' },
      ],
      name,
      type: 'chat',
    });
  }

  public invoke(
    chain: any,
    inputs: Record<string, unknown>,
    promptName: string,
    metadata?: Record<string, unknown>,
  ): Promise<any> {
    this.logger.debug(`Mock: Invoking chain with prompt: ${promptName}`);
    return Promise.resolve({
      result: 'Mock response',
      metadata,
      promptName,
    });
  }
}

// Mock the Langfuse constructor
export const Langfuse = jest.fn().mockImplementation(() => ({
  trace: jest.fn(),
  span: jest.fn(),
  generation: jest.fn(),
  score: jest.fn(),
  event: jest.fn(),
  flush: jest.fn(),
  getPrompt: jest.fn().mockResolvedValue({
    getLangchainPrompt: () => 'Test prompt template',
    name: 'test-prompt',
    type: 'text',
  }),
}));

// Mock the TextPromptClient constructor
export const TextPromptClient = jest.fn().mockImplementation(() => ({
  score: jest.fn(),
  getLangchainPrompt: () => 'Test prompt template',
}));

// Mock the ChatPromptClient constructor
export const ChatPromptClient = jest.fn().mockImplementation(() => ({
  score: jest.fn(),
  prompt: [
    { role: 'system', content: 'You are a helpful assistant.' },
    { role: 'user', content: 'Hello' },
  ],
}));

// Mock the CallbackHandler constructor
export const CallbackHandler = jest.fn().mockImplementation(() => ({
  handleLLMStart: jest.fn(),
  handleLLMNewToken: jest.fn(),
  handleLLMEnd: jest.fn(),
  handleLLMError: jest.fn(),
  handleChainStart: jest.fn(),
  handleChainEnd: jest.fn(),
  handleChainError: jest.fn(),
  handleToolStart: jest.fn(),
  handleToolEnd: jest.fn(),
  handleToolError: jest.fn(),
  handleAgentAction: jest.fn(),
  handleAgentEnd: jest.fn(),
  handleText: jest.fn(),
  handleRetrieverStart: jest.fn(),
  handleRetrieverEnd: jest.fn(),
  handleRetrieverError: jest.fn(),
  handleRetrieverChunk: jest.fn(),
  handleEmbeddingStart: jest.fn(),
  handleEmbeddingEnd: jest.fn(),
  handleEmbeddingError: jest.fn(),
}));
