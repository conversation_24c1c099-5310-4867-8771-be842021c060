import { UnifiedAuthContext } from '@luxury-presence/authorization-middleware';

import { AppPolicyRegistry } from '../../src/auth.module';

export class MockAuthContext {
  can(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    resource: keyof AppPolicyRegistry,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    action: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    companyId?: string,
  ): Promise<boolean> {
    // In tests, always allow access
    return Promise.resolve(true);
  }

  getCompanyId(): Promise<string | undefined> {
    return Promise.resolve('test-company-id');
  }

  getUserId(): string | undefined {
    return 'test-user-id';
  }

  getPermissions(): Promise<string[]> {
    return Promise.resolve(['read', 'write', 'create', 'update', 'delete']);
  }

  isSuper(): boolean {
    return true;
  }
}

export const mockAuthContext =
  new MockAuthContext() as unknown as UnifiedAuthContext<AppPolicyRegistry>;
