# E2E Tests for Client Marketing Service

This directory contains comprehensive end-to-end tests for the client-marketing-service application. The tests are designed to validate the complete functionality of the API endpoints, database operations, and application behavior.

## Test Structure

### Setup (`setup.ts`)
The main setup file that configures the test environment:
- Initializes the NestJS application
- Sets up database connections
- Provides test data generation utilities
- Handles database cleanup between tests
- Configures global test lifecycle hooks

### Test Files

#### `health.e2e-spec.ts`
Tests for basic application functionality:
- Health endpoint validation
- GraphQL endpoint functionality
- Application startup and middleware
- Error handling and logging
- Database connectivity

#### `group.e2e-spec.ts`
Comprehensive tests for the Group module:
- CRUD operations (Create, Read, Update, Delete)
- Company-specific filtering
- Relationship testing with recommendations
- Metadata handling
- Validation and error cases

#### `recommendation.e2e-spec.ts`
Tests for the Recommendation module:
- Pagination functionality
- Status transitions (PENDING, APPLIED, REJECTED)
- Filtering and sorting
- Complex metadata handling
- Relationship testing with groups and scrapes

#### `keyword.e2e-spec.ts`
Tests for the Keyword module:
- CRUD operations
- Uniqueness validation
- Metadata handling
- Relationship testing with groups and page keywords
- Search and filtering functionality

#### `scraped-page.e2e-spec.ts`
Tests for the ScrapedPage module:
- CRUD operations
- URL validation
- Content handling (large content, special characters)
- Metadata analysis
- Relationship testing with groups and page keywords

## Running Tests

### Prerequisites
1. Ensure the database is running and accessible
2. Set up proper environment variables for database connection
3. Install dependencies: `pnpm install`

### Running All E2E Tests
```bash
pnpm test:e2e
```

### Running Specific Test Files
```bash
# Run only group tests
pnpm test:e2e --testNamePattern="Group"

# Run only recommendation tests
pnpm test:e2e --testNamePattern="Recommendation"
```

## Test Configuration

### Jest Configuration (`jest-e2e.json`)
- Root directory: `../` (points to project root)
- Test pattern: `\.e2e-spec\.ts$`
- Setup file: `test/setup.ts`
- Module mapping for `src/` imports
- TypeScript transformation with `ts-jest`

## Test Patterns

### Database Operations
Each test follows this pattern:
1. Create test data using utility functions
2. Save data to database using repositories
3. Make HTTP requests to test endpoints
4. Verify responses and database state
5. Clean up automatically via `beforeEach` hook

### Error Testing
Tests include validation of:
- Invalid UUIDs
- Missing required fields
- Invalid data formats
- Duplicate entries
- Non-existent resources

## Best Practices

### Test Isolation
- Each test is independent
- Database is reset between tests
- No shared state between tests

### Data Cleanup
- Automatic cleanup via `beforeEach` hook
- Test data tracking for proper cleanup
- No leftover data between test runs 