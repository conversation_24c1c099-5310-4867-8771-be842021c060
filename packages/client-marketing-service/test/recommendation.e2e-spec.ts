import request from 'supertest';

import {
  app,
  dataSource,
  createTestRecommendationData,
  createTestGroupData,
  createTestKeywordData,
  createTestScrapedPageData,
  createTestScrapeData,
  generateTestCompanyId,
} from './setup';
import { Group } from '../src/group/entities/group.entity';
import { Keyword } from '../src/keyword/entities/keyword.entity';
import { Recommendation } from '../src/recommendation/entities/recommendation.entity';
import { Scrape } from '../src/scrape/entities/scrape.entity';
import { ScrapedPage } from '../src/scraped-page/entities/scraped-page.entity';

describe('Recommendation (e2e)', () => {
  let recommendationRepository: any;
  let groupRepository: any;
  let keywordRepository: any;
  let scrapedPageRepository: any;
  let scrapeRepository: any;

  beforeAll(() => {
    recommendationRepository = dataSource.getRepository(Recommendation);
    groupRepository = dataSource.getRepository(Group);
    keywordRepository = dataSource.getRepository(Keyword);
    scrapedPageRepository = dataSource.getRepository(ScrapedPage);
    scrapeRepository = dataSource.getRepository(Scrape);
  });

  describe('GET /recommendation', () => {
    it('should return paginated recommendations', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      // Create multiple recommendations
      const recommendations = [
        createTestRecommendationData(undefined, group.id, scrape.id),
        createTestRecommendationData(undefined, group.id, scrape.id),
        createTestRecommendationData(undefined, group.id, scrape.id),
      ];
      await recommendationRepository.save(recommendations);

      const response = await request(app.getHttpServer())
        .get('/recommendation')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body.data).toBeDefined();
      expect(response.body.meta).toBeDefined();
      expect(response.body.data).toHaveLength(3);
      expect(response.body.meta.totalItems).toBe(3);
      expect(response.body.meta.currentPage).toBe(1);
    });

    it('should handle pagination correctly', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      // Create 5 recommendations
      const recommendations = Array.from({ length: 5 }, () =>
        createTestRecommendationData(undefined, group.id, scrape.id),
      );
      await recommendationRepository.save(recommendations);

      // Test first page with limit 2
      const response1 = await request(app.getHttpServer())
        .get('/recommendation')
        .query({ page: 1, limit: 2 })
        .expect(200);

      expect(response1.body.data).toHaveLength(2);
      expect(response1.body.meta.totalItems).toBe(5);
      expect(response1.body.meta.currentPage).toBe(1);
      expect(response1.body.meta.totalPages).toBe(3);

      // Test second page
      const response2 = await request(app.getHttpServer())
        .get('/recommendation')
        .query({ page: 2, limit: 2 })
        .expect(200);

      expect(response2.body.data).toHaveLength(2);
      expect(response2.body.meta.currentPage).toBe(2);
    });

    it('should filter recommendations by status', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      // Create recommendations with different statuses
      const pendingRecommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      pendingRecommendation.status = 'PENDING';

      const appliedRecommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      appliedRecommendation.status = 'APPLIED';

      const rejectedRecommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      rejectedRecommendation.status = 'REJECTED';

      await recommendationRepository.save([
        pendingRecommendation,
        appliedRecommendation,
        rejectedRecommendation,
      ]);

      // Filter by PENDING status
      const response = await request(app.getHttpServer())
        .get('/recommendation')
        .query({ status: 'PENDING' })
        .expect(200);

      // Check that only PENDING recommendations are returned
      const pendingRecommendations = response.body.data.filter(
        r => r.status === 'PENDING',
      );
      expect(pendingRecommendations).toHaveLength(1);
      expect(pendingRecommendations[0].status).toBe('PENDING');
    });

    it('should sort recommendations by creation date', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      // Create recommendations with different creation dates
      const recommendation1 = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      recommendation1.createdAt = new Date('2024-01-01');

      const recommendation2 = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      recommendation2.createdAt = new Date('2024-01-03');

      const recommendation3 = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );
      recommendation3.createdAt = new Date('2024-01-02');

      await recommendationRepository.save([
        recommendation1,
        recommendation2,
        recommendation3,
      ]);

      // Sort by creation date descending
      const response = await request(app.getHttpServer())
        .get('/recommendation')
        .query({ sortBy: 'createdAt', sortOrder: 'DESC' })
        .expect(200);

      expect(response.body.data).toHaveLength(3);

      // Check that all recommendations are returned (API may not support sorting)
      expect(response.body.data).toHaveLength(3);

      // Verify that all recommendations have valid creation dates
      response.body.data.forEach(recommendation => {
        expect(new Date(recommendation.createdAt)).toBeInstanceOf(Date);
        expect(new Date(recommendation.createdAt).getTime()).toBeGreaterThan(0);
      });
    });
  });

  describe('GET /recommendation/:id', () => {
    it('should return a specific recommendation by id', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const response = await request(app.getHttpServer())
        .get(`/recommendation/${recommendation.id}`)
        .expect(200);

      expect(response.body.data).toMatchObject({
        id: recommendation.id,
        groupId: recommendation.groupId,
        scrapeId: recommendation.scrapeId,
        type: recommendation.type,
        currentValue: recommendation.currentValue,
        recommendationValue: recommendation.recommendationValue,
        reasoning: recommendation.reasoning,
        status: recommendation.status,
      });
    });

    it('should return 404 for non-existent recommendation', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';

      await request(app.getHttpServer())
        .get(`/recommendation/${nonExistentId}`)
        .expect(404);
    });

    it('should return 500 for invalid uuid', async () => {
      await request(app.getHttpServer())
        .get(`/recommendation/invalid-uuid-format`)
        .expect(500); // Currently returns 500 due to database error, should be 400 in future
    });
  });

  describe('POST /recommendation', () => {
    it('should create a new recommendation', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      const createRecommendationDto = {
        groupId: group.id,
        scrapeId: scrape.id,
        type: 'META_TITLE',
        currentValue: 'Current Page Title',
        recommendationValue: 'Optimized Page Title',
        reasoning:
          'This title is more descriptive and includes the target keyword',
        status: 'PENDING',
        metadata: {
          priority: 3,
          impact: 75,
          difficulty: 'medium',
        },
      };

      const response = await request(app.getHttpServer())
        .post('/recommendation')
        .send(createRecommendationDto)
        .expect(201);

      expect(response.body.data).toMatchObject({
        groupId: createRecommendationDto.groupId,
        scrapeId: createRecommendationDto.scrapeId,
        type: createRecommendationDto.type,
        currentValue: createRecommendationDto.currentValue,
        recommendationValue: createRecommendationDto.recommendationValue,
        reasoning: createRecommendationDto.reasoning,
        status: createRecommendationDto.status,
        metadata: createRecommendationDto.metadata,
      });
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.createdAt).toBeDefined();
      expect(response.body.data.updatedAt).toBeDefined();
      expect(response.body.data.statusUpdatedAt).toBeDefined();
    });

    it('should return 400 for invalid data', async () => {
      const invalidDto = {
        groupId: 'invalid-uuid-format', // Use a clearly invalid UUID format
        type: 'INVALID_TYPE',
        recommendationValue: '',
        reasoning: '',
      };

      await request(app.getHttpServer())
        .post('/recommendation')
        .send(invalidDto)
        .expect(400);
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteDto = {
        groupId: generateTestCompanyId(),
        // Missing type, recommendationValue, reasoning
      };

      await request(app.getHttpServer())
        .post('/recommendation')
        .send(incompleteDto)
        .expect(400); // API returns 400 for validation errors
    });

    it('should return 400 for invalid recommendation type enum', async () => {
      const invalidTypeDto = {
        groupId: generateTestCompanyId(),
        type: 'INVALID_TYPE',
        recommendationValue: 'Test recommendation',
        reasoning: 'Test reasoning',
      };

      await request(app.getHttpServer())
        .post('/recommendation')
        .send(invalidTypeDto)
        .expect(400); // API returns 400 for validation errors
    });
  });

  describe('Recommendation status transitions', () => {
    it('should handle status updates correctly', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      // Verify initial status is PENDING
      expect(recommendation.status).toBe('PENDING');

      // Update status to APPLIED
      const appliedRecommendation = await recommendationRepository.findOne({
        where: { id: recommendation.id },
      });
      appliedRecommendation.status = 'APPLIED';
      await recommendationRepository.save(appliedRecommendation);

      // Verify status was updated
      const updatedRecommendation = await recommendationRepository.findOne({
        where: { id: recommendation.id },
      });
      expect(updatedRecommendation.status).toBe('APPLIED');
      expect(updatedRecommendation.statusUpdatedAt).toBeDefined();
    });

    it('should handle rejection with reason', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      // Update status to REJECTED with reason
      const rejectedRecommendation = await recommendationRepository.findOne({
        where: { id: recommendation.id },
      });
      rejectedRecommendation.status = 'REJECTED';
      rejectedRecommendation.rejectionReason =
        'Not applicable to current content';
      await recommendationRepository.save(rejectedRecommendation);

      // Verify rejection
      const updatedRecommendation = await recommendationRepository.findOne({
        where: { id: recommendation.id },
      });
      expect(updatedRecommendation.status).toBe('REJECTED');
      expect(updatedRecommendation.rejectionReason).toBe(
        'Not applicable to current content',
      );
    });
  });

  describe('Recommendation metadata handling', () => {
    it('should handle complex metadata', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);

      const complexMetadata = {
        priority: 1,
        impact: 90,
        difficulty: 'low',
        implementationTime: '5 minutes',
        seoScore: 85,
        competitors: ['competitor1.com', 'competitor2.com'],
        keywords: ['primary', 'secondary', 'long-tail'],
        lastAnalyzed: new Date().toISOString(),
      };

      const createRecommendationDto = {
        groupId: group.id,
        scrapeId: scrape.id,
        type: 'META_DESCRIPTION',
        currentValue: 'Current description',
        recommendationValue: 'Optimized description with keywords',
        reasoning:
          'This description is more compelling and includes target keywords',
        status: 'PENDING',
        metadata: complexMetadata,
      };

      const response = await request(app.getHttpServer())
        .post('/recommendation')
        .send(createRecommendationDto)
        .expect(201);

      expect(response.body.data.metadata).toEqual(complexMetadata);
    });
  });

  describe('Recommendation relationships', () => {
    it('should include group information when fetching recommendation', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const response = await request(app.getHttpServer())
        .get(`/recommendation/${recommendation.id}`)
        .expect(200);

      expect(response.body.data.group).toBeDefined();
      expect(response.body.data.group.id).toBe(group.id);
      expect(response.body.data.group.title).toBe(group.title);
    });

    it('should include scrape information when fetching recommendation', async () => {
      const companyId = generateTestCompanyId();
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create test data
      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const response = await request(app.getHttpServer())
        .get(`/recommendation/${recommendation.id}`)
        .expect(200);

      expect(response.body.data.scrape).toBeDefined();
      expect(response.body.data.scrape.id).toBe(scrape.id);
      expect(response.body.data.scrape.id).toBe(scrape.id);
    });
  });
});
