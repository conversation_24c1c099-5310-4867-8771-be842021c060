import request from 'supertest';

import {
  app,
  dataSource,
  createTestGroupData,
  createTestKeywordData,
  createTestScrapedPageData,
  createTestRecommendationData,
  createTestScrapeData,
  generateTestCompanyId,
  generateTestGroupId,
} from './setup';
import { Group } from '../src/group/entities/group.entity';
import { Keyword } from '../src/keyword/entities/keyword.entity';
import { Recommendation } from '../src/recommendation/entities/recommendation.entity';
import { ScheduledAction } from '../src/scheduled-action/entities/scheduled-action.entity';
import { Scrape } from '../src/scrape/entities/scrape.entity';
import { ScrapedPage } from '../src/scraped-page/entities/scraped-page.entity';

describe('SEO Optimizations GraphQL (e2e)', () => {
  let groupRepository: any;
  let keywordRepository: any;
  let scrapedPageRepository: any;
  let recommendationRepository: any;
  let scrapeRepository: any;
  let scheduledActionRepository: any;

  beforeAll(() => {
    groupRepository = dataSource.getRepository(Group);
    keywordRepository = dataSource.getRepository(Keyword);
    scrapedPageRepository = dataSource.getRepository(ScrapedPage);
    recommendationRepository = dataSource.getRepository(Recommendation);
    scrapeRepository = dataSource.getRepository(Scrape);
    scheduledActionRepository = dataSource.getRepository(ScheduledAction);
  });

  describe('seoOptimizationGroups query', () => {
    it('should return empty array when no surfaced groups exist', async () => {
      const companyId = generateTestCompanyId();

      const query = `
        query SeoOptimizationGroups($companyId: ID!) {
          seoOptimizationGroups(companyId: $companyId) {
            id
            companyId
            keyword
            pageName
            url
            scrapedPage {
              id
              companyId
              url
              pageName
              pageType
              metadata
            }
            mediaId
            rank {
              original
              current
            }
            ranking
            appliedAt
            scheduledToBeAppliedAt
            optimizations {
              id
              type
              status
              reasoning
              currentValue
              recommendationValue
              rejectionReason
              appliedAt
              scheduledToBeAppliedAt
            }
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query,
          variables: { companyId },
        })
        .expect(200);

      expect(response.body.data.seoOptimizationGroups).toEqual([]);
    });

    it('should return SEO optimization groups for company with surfaced groups', async () => {
      const companyId = generateTestCompanyId();

      // Create test data
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      // Create scheduled action to make group surfaced
      const scheduledAction = {
        id: generateTestCompanyId(), // Use proper UUID
        companyId,
        workflowType: 'SEO',
        status: 'SURFACED',
        contentPayload: {},
        generationPayload: {},
        failureReason: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        scheduledToBePublishedAt: new Date(Date.now() + 86400000), // Tomorrow
      };

      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);
      await scheduledActionRepository.save(scheduledAction);

      const query = `
        query SeoOptimizationGroups($companyId: ID!) {
          seoOptimizationGroups(companyId: $companyId) {
            id
            companyId
            keyword
            pageName
            url
            scrapedPage {
              id
              companyId
              url
              pageName
              pageType
              metadata
            }
            mediaId
            rank {
              original
              current
            }
            ranking
            appliedAt
            scheduledToBeAppliedAt
            optimizations {
              id
              type
              status
              reasoning
              currentValue
              recommendationValue
              rejectionReason
              appliedAt
              scheduledToBeAppliedAt
            }
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query,
          variables: { companyId },
        })
        .expect(200);

      expect(response.body.data.seoOptimizationGroups).toBeDefined();
      expect(Array.isArray(response.body.data.seoOptimizationGroups)).toBe(
        true,
      );
    });

    it('should handle invalid company ID', async () => {
      const query = `
        query SeoOptimizationGroups($companyId: ID!) {
          seoOptimizationGroups(companyId: $companyId) {
            id
            keyword
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query,
          variables: { companyId: 'invalid-uuid-format' }, // Use a clearly invalid UUID format
        })
        .expect(200);

      // Should return an error for invalid company ID
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('seoOptimizationGroup query', () => {
    it('should return specific SEO optimization group by ID', async () => {
      const companyId = generateTestCompanyId();

      // Create test data
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const query = `
        query SeoOptimizationGroup($groupId: ID!) {
          seoOptimizationGroup(groupId: $groupId) {
            id
            companyId
            keyword
            pageName
            url
            scrapedPage {
              id
              companyId
              url
              pageName
              pageType
              metadata
            }
            mediaId
            rank {
              original
              current
            }
            ranking
            appliedAt
            scheduledToBeAppliedAt
            optimizations {
              id
              type
              status
              reasoning
              currentValue
              recommendationValue
              rejectionReason
              appliedAt
              scheduledToBeAppliedAt
            }
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query,
          variables: { groupId: group.id },
        })
        .expect(200);

      expect(response.body.data.seoOptimizationGroup).toBeDefined();
      expect(response.body.data.seoOptimizationGroup.id).toBe(group.id);
    });

    it('should handle non-existent group ID', async () => {
      const query = `
        query SeoOptimizationGroup($groupId: ID!) {
          seoOptimizationGroup(groupId: $groupId) {
            id
            keyword
          }
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query,
          variables: { groupId: generateTestGroupId() }, // Use a valid UUID
        })
        .expect(200);

      // Should return an error for non-existent group
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('rejectSeoOptimization mutation', () => {
    it('should reject SEO optimization with reasoning', async () => {
      const companyId = generateTestCompanyId();

      // Create test data
      const keyword = createTestKeywordData();
      const scrapedPage = createTestScrapedPageData(undefined, companyId);
      const group = createTestGroupData(
        undefined,
        companyId,
        keyword.id,
        scrapedPage.id,
      );
      const scrape = createTestScrapeData(undefined, scrapedPage.id, companyId);
      const recommendation = createTestRecommendationData(
        undefined,
        group.id,
        scrape.id,
      );

      await keywordRepository.save(keyword);
      await scrapedPageRepository.save(scrapedPage);
      await groupRepository.save(group);
      await scrapeRepository.save(scrape);
      await recommendationRepository.save(recommendation);

      const mutation = `
        mutation RejectSeoOptimization($id: ID!, $reasoning: String!) {
          rejectSeoOptimization(id: $id, reasoning: $reasoning)
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query: mutation,
          variables: {
            id: recommendation.id,
            reasoning: 'Test rejection reasoning',
          },
        })
        .expect(200);

      expect(response.body.data.rejectSeoOptimization).toBeDefined();
      expect(response.body.data.rejectSeoOptimization).toBe(recommendation.id);
    });

    it('should handle invalid recommendation ID', async () => {
      const mutation = `
        mutation RejectSeoOptimization($id: ID!, $reasoning: String!) {
          rejectSeoOptimization(id: $id, reasoning: $reasoning)
        }
      `;

      const response = await request(app.getHttpServer())
        .post('/graphql')
        .send({
          query: mutation,
          variables: {
            id: generateTestCompanyId(), // Use a valid UUID that doesn't exist
            reasoning: 'Test rejection reasoning',
          },
        })
        .expect(200);

      // Should return an error for invalid recommendation ID
      expect(response.body.errors).toBeDefined();
    });
  });
});
