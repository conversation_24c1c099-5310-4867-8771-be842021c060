import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BrandProfileModule } from 'src/brand-profile/brand-profile.module';

import { CampaignModule } from '../src/campaign/campaign.module';
import { ApolloFederationDriver } from '../src/common/graphql/apollo-federation.driver';
import { GraphQLContextFactory } from '../src/common/graphql-context/graphql-context.factory';
import { GraphQLContextModule } from '../src/common/graphql-context/graphql-context.module';
import { CompanyModule } from '../src/company/company.module';
import { GroupModule } from '../src/group/group.module';
import { GroupScheduledActionModule } from '../src/group-scheduled-action/group-scheduled-action.module';
import { HomepageFeedModule } from '../src/homepage-feed/homepage-feed.module';
import { KeywordModule } from '../src/keyword/keyword.module';
import { NotificationModule } from '../src/notification/notification.module';
import { PageKeywordModule } from '../src/page-keyword/page-keyword.module';
import { RecommendationModule } from '../src/recommendation/recommendation.module';
import { ScheduledActionModule } from '../src/scheduled-action/scheduled-action.module';
import { ScrapeModule } from '../src/scrape/scrape.module';
import { ScrapedPageModule } from '../src/scraped-page/scraped-page.module';
import { SeoOptimizationsModule } from '../src/seo-optimizations/seo-optimizations.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env.test',
    }),
    GraphQLModule.forRootAsync({
      imports: [GraphQLContextModule],
      inject: [GraphQLContextFactory],
      useFactory: (contextFactory: GraphQLContextFactory) => ({
        driver: ApolloFederationDriver,
        autoSchemaFile: true,
        context: contextFactory.create.bind(contextFactory),
        playground: false,
        introspection: false,
        // Disable authorization for tests
        plugins: [],
      }),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: process.env.DB_DATABASE || 'test_db',
      entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
      synchronize: true,
      logging: false,
    }),
    // Import all modules except auth
    CompanyModule,
    GroupModule,
    RecommendationModule,
    NotificationModule,
    ScheduledActionModule,
    ScrapedPageModule,
    KeywordModule,
    ScrapeModule,
    PageKeywordModule,
    GroupScheduledActionModule,
    HomepageFeedModule,
    SeoOptimizationsModule,
    BrandProfileModule,
    CampaignModule,
  ],
  controllers: [],
  providers: [],
})
export class TestAppModule {}
