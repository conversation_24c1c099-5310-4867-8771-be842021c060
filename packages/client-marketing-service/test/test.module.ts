import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BrandProfileModule } from 'src/brand-profile/brand-profile.module';

import { MockExternalApiService } from './mocks/external-api.mock';
import { MockLangfuseService } from './mocks/langfuse.mock';
import { CampaignModule } from '../src/campaign/campaign.module';
import { CMSService } from '../src/cms/cms.service';
import { GraphQLContextFactory } from '../src/common/graphql-context/graphql-context.factory';
import { GraphQLContextModule } from '../src/common/graphql-context/graphql-context.module';
import { ApiGatewayService } from '../src/common/services/api-gateway/api-gateway.service';
import { TenantService } from '../src/common/services/tenant/tenant.service';
import { WebsiteService } from '../src/common/services/website/website.service';
import { CompanyModule } from '../src/company/company.module';
import { GroupModule } from '../src/group/group.module';
import { GroupScheduledActionModule } from '../src/group-scheduled-action/group-scheduled-action.module';
import { HealthController } from '../src/health.controller';
import { HomepageFeedModule } from '../src/homepage-feed/homepage-feed.module';
import { KeywordModule } from '../src/keyword/keyword.module';
import { NotificationModule } from '../src/notification/notification.module';
import { PageKeywordModule } from '../src/page-keyword/page-keyword.module';
import { RecommendationModule } from '../src/recommendation/recommendation.module';
import { ScheduledActionModule } from '../src/scheduled-action/scheduled-action.module';
import { ScrapeModule } from '../src/scrape/scrape.module';
import { ScrapedPageModule } from '../src/scraped-page/scraped-page.module';
import { SeoOptimizationsModule } from '../src/seo-optimizations/seo-optimizations.module';
import { SnowflakeService } from '../src/snowflake/snowflake.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env.test',
    }),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [GraphQLContextModule],
      inject: [GraphQLContextFactory],
      useFactory: (contextFactory: GraphQLContextFactory) => ({
        autoSchemaFile: true,
        context: ({ req }) => {
          // Create a mock context for tests
          const mockReq = {
            ...req,
            authContext: {
              can: jest.fn().mockResolvedValue(true),
              getCompanyId: jest.fn().mockResolvedValue('test-company-id'),
              getUserId: jest.fn().mockResolvedValue('test-user-id'),
              getPermissions: jest
                .fn()
                .mockResolvedValue([
                  'read',
                  'write',
                  'create',
                  'update',
                  'delete',
                ]),
              isSuperUser: jest.fn().mockResolvedValue(true),
            },
          };
          return contextFactory.create(mockReq);
        },
        playground: false,
        introspection: true,
      }),
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DATABASE_HOST || 'localhost',
      port: parseInt(process.env.DATABASE_PORT || '5432'),
      username: process.env.DATABASE_USER || 'postgres',
      password: process.env.DATABASE_PASSWORD || '123123',
      database: process.env.DATABASE_NAME || 'postgres',
      schema: process.env.DATABASE_SCHEMA || 'crew_ai_seo_automation',
      entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
      synchronize: true,
      logging: false,
    }),
    CompanyModule,
    GroupModule,
    RecommendationModule,
    NotificationModule,
    ScheduledActionModule,
    ScrapedPageModule,
    KeywordModule,
    ScrapeModule,
    PageKeywordModule,
    GroupScheduledActionModule,
    HomepageFeedModule,
    SeoOptimizationsModule,
    BrandProfileModule,
    CampaignModule,
  ],
  controllers: [HealthController],
  providers: [
    MockExternalApiService,
    MockLangfuseService,
    // Override the real LangfuseService with our mock
    {
      provide: 'LangfuseService',
      useClass: MockLangfuseService,
    },
    // Mock TenantService to prevent external service calls
    {
      provide: TenantService,
      useValue: {
        getEntitlements: jest.fn().mockResolvedValue([
          {
            id: 'test-entitlement-id',
            type: 'SEO',
            status: 'ACTIVE',
            startDate: new Date(),
            endDate: new Date(Date.now() + 86400000),
          },
        ]),
        find: jest.fn().mockResolvedValue([
          {
            id: 'test-company-id',
            displayId: 'test-company-id',
            name: 'Test Company',
            website: 'https://test.com',
          },
        ]),
      },
    },
    // Mock WebsiteService
    {
      provide: WebsiteService,
      useValue: {
        getLiveBrandWebsites: jest.fn().mockResolvedValue([
          {
            id: 'test-website-id',
            name: 'Test Website',
            hostname: 'test.com',
            url: 'https://test.com',
          },
        ]),
      },
    },
    // Mock ApiGatewayService
    {
      provide: ApiGatewayService,
      useValue: {
        findHandedOffLeads: jest.fn().mockResolvedValue([]),
        findMedia: jest.fn().mockResolvedValue([]),
      },
    },
    // Mock CMSService
    {
      provide: CMSService,
      useValue: {
        findAIGeneratedBlogPosts: jest.fn().mockResolvedValue([]),
      },
    },
    // Mock SnowflakeService
    {
      provide: SnowflakeService,
      useValue: {
        findDailyAdPerformance: jest.fn().mockResolvedValue([]),
      },
    },
  ],
  exports: [MockExternalApiService, MockLangfuseService],
})
export class TestModule {}
