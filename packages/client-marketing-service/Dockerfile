FROM node:22-alpine

ARG NPM_AUTH_TOKEN
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
ENV PORT=8080
ENV NODE_ENV production

# RUN corepack enable
# ENV PNPM_HOME="/pnpm"
# ENV PATH="$PNPM_HOME:$PATH"

RUN mkdir -p /usr/src/app
WORKDIR /usr/src/app

COPY . .

# RUN pnpm install -g husky
# RUN pnpm install

WORKDIR /usr/src/app/packages/client-marketing-service

RUN corepack enable
# Update corepack to the latest version to prevent signature issue.
# https://github.com/pnpm/pnpm/issues/9029#issuecomment-2629866277
RUN npm i -g corepack@latest
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV HUSKY=0

RUN pnpm install --production --frozen-lockfile

EXPOSE 8080
CMD ["npm", "run", "start:prod"]
