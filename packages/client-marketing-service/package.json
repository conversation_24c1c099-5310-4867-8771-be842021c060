{"name": "client-marketing-service", "version": "11.16.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "format:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "NODE_OPTIONS=--experimental-vm-modules jest --runInBand", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch", "test:cov": "NODE_OPTIONS=--experimental-vm-modules jest --coverage", "test:debug": "NODE_OPTIONS=--experimental-vm-modules node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "./scripts/test-e2e.sh", "test:e2e:shared": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./scripts/typeorm -d src/data-source.ts", "seed": "ts-node -r tsconfig-paths/register src/seeder.ts", "seed:refresh": "ts-node -r tsconfig-paths/register src/seeder.ts --refresh", "typeorm:create": "ts-node -r tsconfig-paths/register ./scripts/typeorm migration:create", "typeorm:generate": "pnpm run typeorm migration:generate", "typeorm:run": "pnpm run typeorm migration:run", "typeorm:revert": "pnpm run typeorm migration:revert", "changeset": "cd ../../ && changeset", "load-test-feed": "K6_WEB_DASHBOARD_EXPORT=report k6 run ./src/load-test.js"}, "dependencies": {"@apollo/client": "^4.0.4", "@apollo/server": "^4.12.0", "@apollo/subgraph": "^2.10.2", "@aws-sdk/client-s3": "^3.882.0", "@aws-sdk/client-sns": "^3.882.0", "@aws-sdk/client-sqs": "^3.699.0", "@langchain/anthropic": "^0.3.15", "@langchain/community": "^0.3.34", "@langchain/core": "0.3.42", "@langchain/openai": "^0.4.4", "@luxury-presence/authorization-middleware": "^1.1.0", "@luxury-presence/nestjs-logger": "^1.3.1", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.0.5", "@nestjs/typeorm": "^11.0.0", "@types/jsonwebtoken": "^9.0.7", "axios": "^1.8.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.2.0", "dataloader": "^2.2.3", "dd-trace": "^5.0.0", "dotenv": "^16.4.7", "graphql": "^16.9.0", "graphql-request": "^6.1.0", "graphql-type-json": "^0.3.2", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "k6": "^0.0.0", "langchain": "^0.3.19", "langfuse": "^3.37.0", "langfuse-langchain": "^3.37.0", "lodash-es": "^4.17.21", "nestjs-paginate": "^11.1.1", "nestjs-seeder": "^0.3.2", "pg": "^8.13.3", "playwright": "^1.51.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "snowflake-sdk": "^2.1.0", "typeorm": "^0.3.20", "uuid": "^10.0.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/axios": "^0.14.4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.5", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.16.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coveragePathIgnorePatterns": ["src/migration/*", "src/seeders/*", "\\.module\\.ts", "src/[^/]*\\.ts", "src/common/decorators/*", "src/common/utils/*", "src/config/*", "\\.entity\\.ts", "src/scraper/local-get-xpath/*", "\\.dto\\.ts", "\\.input\\.ts", "\\.policy\\.ts", "\\.paginate\\.ts", "src/common/types/*", "\\.openapi\\.ts", "src/middleware/auth.middleware.ts", ".*DEPRECATED.*", "src/common/graphql-context/*", "\\.schema\\.ts", "\\.decorator\\.ts", "src/test-utils/*"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}, "moduleDirectories": ["node_modules", "<rootDir>"], "globalSetup": "../global-setup.js"}}