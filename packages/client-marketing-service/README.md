# Description

Client Marketing Service provides an API for the resources required by the frontend and backend within the client marketing domain. This includes seo data (i.e. keywords and recommendations).

## Project setup

Ensure you are running the Node version found within [this repo's .nvmrc file](../../.nvmrc). Use [NVM](https://github.com/nvm-sh/nvm?tab=readme-ov-file#install--update-script) to install and switch Node versions.

Use your npmjs.org login to generate an authToken. Add a new file [.npmrc](./.npmrc) in this package. File contents: `//registry.npmjs.org/:_authToken=NPM_TOKEN`

```bash
pnpm install
```

## Compile and run the project

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

## Run tests

```bash
# unit tests
$ pnpm run test

# e2e tests (isolated database - default)
$ pnpm run test:e2e

# e2e tests with shared development database
$ pnpm run test:e2e:shared

# test coverage
$ pnpm run test:cov
```

## Load testing

Install k6 globally if you haven't already: https://grafana.com/docs/k6/latest/set-up/install-k6/#install-k6

Edit `load-test.js` to add a company id and your auth token.

The test can be further customized by modifying the `options` constant within the script. You can adjust the number of virtual users, duration, and other parameters to suit your testing needs.

Run the load test:

```bash
$ pnpm run load-test-feed
```

## Lint and format the code

```bash
# check
$ pnpm run lint

# fix
$ pnpm run lint:fix

# check
$ pnpm run format

# fix
$ pnpm run format:fix
```

## Local Development

### Running a local database

Edit or create a `.env` file. Example values are available in `.env.local`.

TL;DR:

```bash
docker compose up -d && pnpm run typeorm:run && pnpm run seed && pnpm run start:dev
```

#### Step-by-step

Make sure Docker is running. Then, run the following command:

```bash
docker compose up -d
```

Run migrations:

```bash
pnpm run typeorm:run
```

If you need seed data, run:

```bash
pnpm run seed
```

If you want to wipe the database and reseed with fresh data, run:

```bash
pnpm run seed:refresh
```

You can access pgAdmin by visiting `http://localhost:5050`.

Data is stored in the `seo-automation-api-gateway_pgdata` volume. You can wipe it by running:

### E2E Testing

The project supports two approaches for e2e testing:

1. **Isolated e2e tests** (`pnpm test:e2e`): Uses a separate test database (default)
2. **Shared e2e tests** (`pnpm test:e2e:shared`): Uses your development database

The isolated approach (default):
- Spins up a dedicated PostgreSQL container for testing
- Uses the same database configuration as CircleCI
- Automatically cleans up after tests complete
- Prevents any interference with your development data
- Runs on port 5433 to avoid conflicts with your dev database

**Default**: `pnpm test:e2e` now uses the isolated database for the safest testing experience.

```bash
docker volume rm seo-automation-api-gateway_pgdata
```

### Auth

#### REST endpoints

There is currently no auth for REST endpoints.

#### GraphQL

- Add the `x-lp-api-key` header with the M2M_SUPER_API_KEY `localkey`.
- Graphiql is available at `http://localhost:3000/graphql`.

**IMPORTANT**: If you make changes to the GraphQL schema, you will need to run the app locally to generate the updated schema (`pnpm start:dev`)

##### Local-Rover

- See the [Local-Rover](../local-rover/README.md) README for more information.

**IMPORTANT**: If you make changes to the GraphQL schema, you MUST make sure the federated graph does not break. Follow the directions in the local-rover README to test the subgraph's schema changes

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
