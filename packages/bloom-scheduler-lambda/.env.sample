LOG_LEVEL=DEBUG
REGION=us-east-1
AUTOMATED_BLOGS_PRODUCT_ID=5c9b746c-7327-42ce-b998-ce707e7cee44
# Get from vault
API_GATEWAY_KEY=
# Get from vault
API_GATEWAY_SUPER_USER_COMPANY_ID=
# workflow without human review step
AIR_OPS_WORKFLOW_ID=3f4ae1e0-0edb-4ba5-945a-9eabee2aab3a
# Blog Post Writer v2 specific app ID
BLOG_V2_AIR_OPS_WRITER_APP_ID=
AIR_OPS_API_URL=https://app.airops.com/public_api/airops_apps
# Get from vault
AIR_OPS_API_KEY=
# Blog Post Writer v2 Slack notifications
BLOG_V2_SLACK_NOTIFICATIONS_ENABLED=false
BLOG_V2_SLACK_WEBHOOK_URL=
POWERTOOLS_DEV=true
# You can point these locally running services to a local DB or staging DB
# This will only work if you are running the lambdas locally
# For AWS dev-sandbox, You will need to update the URLs output by the tunneling
TENANT_SERVICE_URL=http://local.luxurycoders.com:8011
CMS_SERVICE_URL=http://local.luxurycoders.com:8002
API_GATEWAY_URL=https://gw.luxurycoders.com
# Get from vault - Cosmo GraphQL endpoint
COSMO_GQL_URL=
# Get from vault - M2M Super API key for authentication
M2M_SUPER_API_KEY=
# This is the day of the week that the scheduler will generate posts on
GENERATE_POST_DAY=THURSDAY
# This is a mock date for a date the scheduler is "running" on
# Ex. If the GENERATE_POST_DAY is Tuesday, but the TESTING_TODAY_DATE is not a Tuesday,
# then the scheduler will not generate posts for anyone
# If the TESTING_TODAY_DATE is the first Tuesday of the month, then the scheduler will generate posts for everyone
# If the TESTING_TODAY_DATE is the second Tuesday of the month, then the scheduler will only generate posts for users with 4 units
TESTING_TODAY_DATE=2024-10-08
# The number of entitlements per page from cms-service
ENTITLEMENTS_LIMIT=10
LANGFUSE_SECRET_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_FLUSH_AT=1
LANGFUSE_BASE_URL=https://us.cloud.langfuse.com
LANGFUSE_ENVIRONMENT=development
OPENAI_API_KEY=
OPENAI_MODEL_NAME=gpt-4o-mini
OPENAI_TEMPERATURE=0.2
GEMINI_API_KEY=

# Additional Configuration
ENVIRONMENT=development
SUPER_BLOOM_LD_FLAG=enable-super-bloom
MOCK_DATA_MODE=false
ENTITLEMENTS_CHUNK_SIZE=5
GIT_SHA=latest
EXECUTION_BUCKET_NAME=lp-bloom-scheduler-execution-development
POST_PUBLISH_DAY_OF_WEEK=THURSDAY
ENABLE_ERROR_TESTING=true
SCHEDULER_STATE=DISABLED
LAUNCHDARKLY_KEY=

# Slack Notifications
SLACK_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=
