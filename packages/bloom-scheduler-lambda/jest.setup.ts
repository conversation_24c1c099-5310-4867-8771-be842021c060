// This file runs before each test file

// Mock the config module before any imports that might use it
jest.mock('src/config', () => ({
  getConfig: () => ({
    automatedBlogsProductId: 'test-product-id',
    ENVIRONMENT: 'test',
  }),
}));

// Mock graphql-request to avoid ESM issues
jest.mock('graphql-request', () => ({
  GraphQLClient: jest.fn().mockImplementation(() => ({
    request: jest.fn(),
  })),
}));
