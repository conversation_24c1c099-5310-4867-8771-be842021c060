import { JobDTO, JobPostDTO } from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

import { CMSServiceError } from './errors';

export class CMSClient {
  constructor(
    private readonly baseUrl: string,
    private readonly logger: ContextLogger,
  ) {}

  async createJobs(jobs: JobPostDTO[]): Promise<JobDTO[]> {
    const url = `${this.baseUrl}/api/v1/posts/generated/jobs/bulkCreate`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ jobs }),
    });

    if (!response.ok) {
      this.logger.error(
        `CMS service HTTP error adding jobs status: ${response.status}`,
        {
          jobs,
        },
      );
      throw new CMSServiceError(
        `CMS service HTTP error adding jobs status: ${response.status}`,
        response.status,
        {
          jobs,
        },
      );
    }
    this.logger.info('Successfully created jobs', {
      jobs,
    });

    return (await response.json()) as Promise<JobDTO[]>;
  }

  async getJob(jobId: string): Promise<JobDTO[]> {
    this.logger.info('Getting existing jobs', { jobId });

    const url = `${this.baseUrl}/api/v1/posts/generated/jobs/${jobId}`;
    const response = await fetch(url);

    if (!response.ok) {
      this.logger.error(
        `CMS service HTTP error getting jobs status: ${response.status}`,
      );
      throw new CMSServiceError(
        `Failed to fetch jobs status: ${response.status}`,
        response.status,
        {
          jobId,
        },
      );
    }

    const result = (await response.json()) as { data: JobDTO[] };
    return result.data;
  }

  private async getUploadTarget(companyId: string) {
    const endpoint = `${process.env.CMS_SERVICE_URL}/api/v1/media-upload-url?companyId=${companyId}`;
    const response = await fetch(endpoint);

    if (!response.ok) {
      throw new Error(`Failed to get upload URL for company: ${companyId}`);
    }

    const data = await response.json();
    const {
      result: { uploadUrl, url, objectId },
    } = data;

    return { uploadUrl, url, objectId };
  }

  private async uploadImage(buffer: Buffer, uploadUrl: string) {
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      body: buffer,
      headers: {
        'content-type': 'image/jpeg',
        'content-length': buffer.length.toString(),
      },
    });

    if (!response.ok) {
      throw new Error(
        `R2 upload failed: ${response.status} ${response.statusText}`,
      );
    }
  }

  private async createMediaRecord(
    companyId: string,
    url: string,
    objectId: string,
    buffer: Buffer,
  ) {
    const width = 1408;
    const height = 768;

    const response = await fetch(`${this.baseUrl}/api/v1/media`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        companyId,
        media: [
          {
            cloudinaryPublicId: objectId,
            resourceType: 'image',
            format: 'jpg',
            bytes: buffer.length,
            url,
            width,
            height,
            sourceName: 'r2',
          },
        ],
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to create media record for company: ${companyId}`,
      );
    }

    const data = await response.json();
    const [image] = data.result;

    return image;
  }

  async createMedia(companyId: string, buffer: Buffer) {
    try {
      const { uploadUrl, url, objectId } =
        await this.getUploadTarget(companyId);
      await this.uploadImage(buffer, uploadUrl);
      const media = await this.createMediaRecord(
        companyId,
        url,
        objectId,
        buffer,
      );

      this.logger.info(`Successfully created media: ${media.mediaId}`);

      return { ...media, objectId };
    } catch (error) {
      const errorMessage = `Failed to create media for company: ${companyId}`;

      this.logger.error(errorMessage, error);

      throw new CMSServiceError(errorMessage, 400, error);
    }
  }
}
