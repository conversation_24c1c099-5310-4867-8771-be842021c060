import { EntitlementDTO } from 'src/types/entitlement';
import { ContextLogger } from 'src/utils/contextLogger';

import { TenantServiceError } from './errors';
import { PaginatedResponse } from './types/tenantClient';

export class TenantClient {
  constructor(
    private readonly baseUrl: string,
    private readonly entitlementsLimit: number | undefined,
    private readonly logger: ContextLogger,
    private readonly productId: string,
  ) {}

  private getEntitlementsUrl(): string {
    const params = new URLSearchParams({
      'filter.productId': `$eq:${this.productId}`,
      'filter.endDate': '$null',
      select: 'company.website,company.name',
      ...(this.entitlementsLimit && {
        limit: `${this.entitlementsLimit}`,
      }),
    });
    return `${this.baseUrl}/api/v1/entitlements?${params.toString()}`;
  }

  private async getPageOfEntitlements(
    nextPageUrl?: string,
  ): Promise<PaginatedResponse<EntitlementDTO>> {
    this.logger.info('Fetching page of entitlements', { nextPageUrl });

    const url = nextPageUrl || this.getEntitlementsUrl();
    const response = await fetch(url);

    if (!response.ok) {
      this.logger.error(
        `Tenant service HTTP error fetching entitlements status: ${response.status}`,
      );

      throw new TenantServiceError(
        `Failed to fetch entitlements status: ${response.status}`,
        response.status,
        {
          url,
          productId: this.productId,
        },
      );
    }
    return response.json() as Promise<PaginatedResponse<EntitlementDTO>>;
  }
  async getAllEntitlements(): Promise<EntitlementDTO[]> {
    let allEntitlements: EntitlementDTO[] = [];
    let nextPageUrl: string | undefined;

    do {
      const paginatedEntitlements =
        await this.getPageOfEntitlements(nextPageUrl);
      allEntitlements = [...allEntitlements, ...paginatedEntitlements.data];
      nextPageUrl = paginatedEntitlements?.links?.next;
    } while (nextPageUrl);

    this.logger.info('Fetched all entitlements', {
      entitlements: allEntitlements,
      entitlementsCount: allEntitlements.length,
    });

    return allEntitlements;
  }
}
