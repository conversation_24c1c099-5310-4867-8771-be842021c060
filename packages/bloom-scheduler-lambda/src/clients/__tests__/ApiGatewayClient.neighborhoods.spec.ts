import { ApiGatewayClient } from '../ApiGatewayClient';
import { ApiGatewayError } from '../errors';

// Mock logger with proper typing
interface MockLogger {
  info: jest.Mock;
  error: jest.Mock;
  warn: jest.Mock;
  debug: jest.Mock;
}

const mockLogger: MockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

// Mock GraphQL client with proper typing
interface MockGraphQLClient {
  request: jest.Mock;
}

const mockGraphQLRequest = jest.fn();
const mockGraphQLClient: MockGraphQLClient = {
  request: mockGraphQLRequest,
};

describe('ApiGatewayClient - Neighborhoods', () => {
  let apiGatewayClient: ApiGatewayClient;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.debug.mockClear();

    // Type assertions for complex dependencies in tests
    const graphqlClientMock =
      mockGraphQLClient as unknown as ConstructorParameters<
        typeof ApiGatewayClient
      >[2];
    const loggerMock = mockLogger as unknown as ConstructorParameters<
      typeof ApiGatewayClient
    >[3];

    apiGatewayClient = new ApiGatewayClient(
      'test-super-user',
      'test-api-key',
      graphqlClientMock,
      loggerMock,
    );
  });

  describe('getNeighborhoodsWithGooglePlaces', () => {
    const validGooglePlaceData = {
      place_id: 'ChIJOwg_06VPwokRYv534QaPC8g',
      name: 'New York',
      formatted_address: 'New York, NY, USA',
      geometry: {
        location: { lat: 40.7127753, lng: -74.0059728 },
      },
    };

    const mockNeighborhoods = [
      {
        id: 'n1',
        name: 'Manhattan',
        googlePlaceData: validGooglePlaceData,
        isPrimary: true,
        slug: 'manhattan',
        description: 'The heart of NYC',
        coordinates: { lat: 40.7589, lng: -73.9851 },
      },
      {
        id: 'n2',
        name: 'Brooklyn',
        googlePlaceData: null, // Invalid - should be filtered out
        isPrimary: false,
        slug: 'brooklyn',
      },
      {
        id: 'n3',
        name: 'Queens',
        googlePlaceData: {}, // Invalid - empty object should be filtered out
        isPrimary: false,
        slug: 'queens',
      },
      {
        id: 'n4',
        name: 'Bronx',
        googlePlaceData: {
          place_id: 'ChIJaSZwFP9YwokRFz9-RrKQ-8I',
          name: 'Bronx',
        },
        isPrimary: false,
        slug: 'bronx',
      },
    ];

    it('should fetch and filter neighborhoods with valid Google Places data', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: mockNeighborhoods,
      });

      const result =
        await apiGatewayClient.getNeighborhoodsWithGooglePlaces('company-1');

      expect(result).toHaveLength(2); // Only n1 and n4 have valid Google Places data
      expect(result).toEqual([
        {
          id: 'n1',
          name: 'Manhattan',
          preferredName: 'Manhattan',
          googlePlacesName: 'New York', // From googlePlaceData.name
          googlePlaceData: validGooglePlaceData,
          isPrimary: true,
          slug: 'manhattan',
          description: 'The heart of NYC',
          coordinates: { lat: 40.7589, lng: -73.9851 },
        },
        {
          id: 'n4',
          name: 'Bronx',
          preferredName: 'Bronx',
          googlePlacesName: 'Bronx',
          googlePlaceData: {
            place_id: 'ChIJaSZwFP9YwokRFz9-RrKQ-8I',
            name: 'Bronx',
          },
          isPrimary: false,
          slug: 'bronx',
          description: undefined,
          coordinates: undefined,
        },
      ]);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Filtered neighborhoods with valid Google Places data',
        {
          totalNeighborhoods: 4,
          validNeighborhoods: 2,
          companyId: 'company-1',
        },
      );
    });

    it('should handle empty neighborhoods array', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: [],
      });

      const result =
        await apiGatewayClient.getNeighborhoodsWithGooglePlaces('company-1');

      expect(result).toEqual([]);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Filtered neighborhoods with valid Google Places data',
        {
          totalNeighborhoods: 0,
          validNeighborhoods: 0,
          companyId: 'company-1',
        },
      );
    });

    it('should throw ApiGatewayError on GraphQL failure', async () => {
      const error = new Error('GraphQL Error');
      mockGraphQLRequest.mockRejectedValueOnce(error);

      await expect(
        apiGatewayClient.getNeighborhoodsWithGooglePlaces('company-1'),
      ).rejects.toThrow(ApiGatewayError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch neighborhoods with Google Places data',
        {
          error: 'GraphQL Error',
          companyId: 'company-1',
        },
      );
    });

    it('should fallback to name when googlePlaceData.name is missing', async () => {
      const neighborhoodWithoutGooglePlaceName = {
        id: 'n1',
        name: 'Test Neighborhood',
        googlePlaceData: {
          place_id: 'test-place-id',
          // name is missing
        },
      };

      mockGraphQLRequest.mockResolvedValueOnce({
        neighborhoods: [neighborhoodWithoutGooglePlaceName],
      });

      const result =
        await apiGatewayClient.getNeighborhoodsWithGooglePlaces('company-1');

      expect(result[0].googlePlacesName).toBe('Test Neighborhood');
    });
  });

  describe('getBrandBlogPreferences', () => {
    it('should fetch brand blog preferences successfully', async () => {
      const mockBlogPreferences = {
        preferredLocations: ['manhattan', 'brooklyn', 'queens'],
      };

      mockGraphQLRequest.mockResolvedValueOnce({
        company: {
          blogPreferences: mockBlogPreferences,
        },
      });

      const result =
        await apiGatewayClient.getBrandBlogPreferences('company-1');

      expect(result).toEqual(mockBlogPreferences);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Fetched brand blog preferences',
        {
          companyId: 'company-1',
          hasPreferredLocations: true,
          preferredLocationsCount: 3,
        },
      );
    });

    it('should handle missing company', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        company: null,
      });

      const result =
        await apiGatewayClient.getBrandBlogPreferences('company-1');

      expect(result).toEqual({});
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Fetched brand blog preferences',
        {
          companyId: 'company-1',
          hasPreferredLocations: false,
          preferredLocationsCount: 0,
        },
      );
    });

    it('should handle missing blogPreferences', async () => {
      mockGraphQLRequest.mockResolvedValueOnce({
        company: {},
      });

      const result =
        await apiGatewayClient.getBrandBlogPreferences('company-1');

      expect(result).toEqual({});
    });

    it('should handle GraphQL errors gracefully', async () => {
      const error = new Error('GraphQL Error');
      mockGraphQLRequest.mockRejectedValueOnce(error);

      const result =
        await apiGatewayClient.getBrandBlogPreferences('company-1');

      expect(result).toEqual({});
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch brand blog preferences',
        {
          error: 'GraphQL Error',
          companyId: 'company-1',
        },
      );
    });
  });

  describe('getNeighborhoodsWithPreferences', () => {
    const mockNeighborhoodsWithGooglePlaces = [
      {
        id: 'n1',
        name: 'Manhattan',
        preferredName: 'Manhattan',
        googlePlacesName: 'Manhattan, NY',
        googlePlaceData: { place_id: 'manhattan-id', name: 'Manhattan, NY' },
        isPrimary: true,
        slug: 'manhattan',
      },
      {
        id: 'n2',
        name: 'Brooklyn',
        preferredName: 'Brooklyn',
        googlePlacesName: 'Brooklyn, NY',
        googlePlaceData: { place_id: 'brooklyn-id', name: 'Brooklyn, NY' },
        isPrimary: false,
        slug: 'brooklyn',
      },
      {
        id: 'n3',
        name: 'Queens',
        preferredName: 'Queens',
        googlePlacesName: 'Queens, NY',
        googlePlaceData: { place_id: 'queens-id', name: 'Queens, NY' },
        isPrimary: false,
        slug: 'queens',
      },
    ];

    const mockBlogPreferences = {
      preferredLocations: ['n1', 'brooklyn'], // Mix of ID and name matching
    };

    beforeEach(() => {
      // Mock the sub-methods
      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithGooglePlaces')
        .mockResolvedValue(mockNeighborhoodsWithGooglePlaces);
      jest
        .spyOn(apiGatewayClient, 'getBrandBlogPreferences')
        .mockResolvedValue(mockBlogPreferences);
    });

    it('should separate neighborhoods by preference correctly', async () => {
      const result =
        await apiGatewayClient.getNeighborhoodsWithPreferences('company-1');

      expect(result.preferredNeighborhoods).toHaveLength(2);
      expect(result.otherNeighborhoods).toHaveLength(1);
      expect(result.allNeighborhoods).toHaveLength(3);

      // Check preferred neighborhoods (n1 by ID, n2 by name)
      expect(result.preferredNeighborhoods[0].id).toBe('n1');
      expect(result.preferredNeighborhoods[0].isPreferred).toBe(true);
      expect(result.preferredNeighborhoods[1].id).toBe('n2');
      expect(result.preferredNeighborhoods[1].isPreferred).toBe(true);

      // Check other neighborhoods (n3)
      expect(result.otherNeighborhoods[0].id).toBe('n3');
      expect(result.otherNeighborhoods[0].isPreferred).toBe(false);

      // Check that preferred neighborhoods come first in allNeighborhoods
      expect(result.allNeighborhoods[0].id).toBe('n1');
      expect(result.allNeighborhoods[1].id).toBe('n2');
      expect(result.allNeighborhoods[2].id).toBe('n3');
    });

    it('should handle slug-based preference matching', async () => {
      const preferencesWithSlugs = {
        preferredLocations: ['manhattan', 'queens'], // Slug matching
      };

      jest
        .spyOn(apiGatewayClient, 'getBrandBlogPreferences')
        .mockResolvedValue(preferencesWithSlugs);

      const result =
        await apiGatewayClient.getNeighborhoodsWithPreferences('company-1');

      expect(result.preferredNeighborhoods).toHaveLength(2);
      expect(result.preferredNeighborhoods[0].id).toBe('n1'); // manhattan slug
      expect(result.preferredNeighborhoods[1].id).toBe('n3'); // queens slug
      expect(result.otherNeighborhoods).toHaveLength(1);
      expect(result.otherNeighborhoods[0].id).toBe('n2'); // brooklyn
    });

    it('should handle no preferred locations', async () => {
      jest
        .spyOn(apiGatewayClient, 'getBrandBlogPreferences')
        .mockResolvedValue({});

      const result =
        await apiGatewayClient.getNeighborhoodsWithPreferences('company-1');

      expect(result.preferredNeighborhoods).toHaveLength(0);
      expect(result.otherNeighborhoods).toHaveLength(3);
      expect(result.allNeighborhoods).toHaveLength(3);

      // All should be marked as not preferred
      result.allNeighborhoods.forEach(n => {
        expect(n.isPreferred).toBe(false);
      });
    });

    it('should handle empty neighborhoods array', async () => {
      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithGooglePlaces')
        .mockResolvedValue([]);

      const result =
        await apiGatewayClient.getNeighborhoodsWithPreferences('company-1');

      expect(result.allNeighborhoods).toEqual([]);
      expect(result.preferredNeighborhoods).toEqual([]);
      expect(result.otherNeighborhoods).toEqual([]);
    });

    it('should log preference separation details', async () => {
      await apiGatewayClient.getNeighborhoodsWithPreferences('company-1');

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Separated neighborhoods by preference',
        {
          companyId: 'company-1',
          totalNeighborhoods: 3,
          preferredCount: 2,
          otherCount: 1,
          preferredLocationIds: ['n1', 'brooklyn'],
        },
      );
    });

    it('should throw ApiGatewayError when neighborhoods fetch fails', async () => {
      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithGooglePlaces')
        .mockRejectedValue(new Error('Neighborhoods fetch failed'));

      await expect(
        apiGatewayClient.getNeighborhoodsWithPreferences('company-1'),
      ).rejects.toThrow(ApiGatewayError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch neighborhoods with preferences',
        {
          error: 'Neighborhoods fetch failed',
          companyId: 'company-1',
        },
      );
    });
  });

  describe('getNeighborhoodsWithDetails (Legacy)', () => {
    it('should use new method and apply legacy formatting', async () => {
      const mockPreferencesResult = {
        allNeighborhoods: [
          {
            id: 'n1',
            name: 'Manhattan',
            preferredName: 'Manhattan',
            googlePlacesName: 'Manhattan, NY',
            isPreferred: true,
          },
          {
            id: 'n2',
            name: 'Brooklyn',
            preferredName: 'Brooklyn',
            googlePlacesName: 'Brooklyn, NY',
            isPreferred: false,
          },
        ],
        preferredNeighborhoods: [],
        otherNeighborhoods: [],
      };

      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithPreferences')
        .mockResolvedValue(mockPreferencesResult);

      const result = await apiGatewayClient.getNeighborhoodsWithDetails(
        'company-1',
        10,
        0,
      );

      expect(result).toEqual([
        {
          id: 'n1',
          name: 'Manhattan',
          preferredName: 'Manhattan',
          googlePlacesName: 'Manhattan, NY',
          isPreferred: true,
        },
        {
          id: 'n2',
          name: 'Brooklyn',
          preferredName: 'Brooklyn',
          googlePlacesName: 'Brooklyn, NY',
          isPreferred: false,
        },
      ]);

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Using deprecated getNeighborhoodsWithDetails method',
        {
          companyId: 'company-1',
          limit: 10,
          offset: 0,
        },
      );
    });

    it('should apply limit and offset correctly', async () => {
      const mockPreferencesResult = {
        allNeighborhoods: [
          {
            id: 'n1',
            name: 'N1',
            preferredName: 'N1',
            googlePlacesName: 'N1',
            isPreferred: false,
          },
          {
            id: 'n2',
            name: 'N2',
            preferredName: 'N2',
            googlePlacesName: 'N2',
            isPreferred: false,
          },
          {
            id: 'n3',
            name: 'N3',
            preferredName: 'N3',
            googlePlacesName: 'N3',
            isPreferred: false,
          },
          {
            id: 'n4',
            name: 'N4',
            preferredName: 'N4',
            googlePlacesName: 'N4',
            isPreferred: false,
          },
        ],
        preferredNeighborhoods: [],
        otherNeighborhoods: [],
      };

      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithPreferences')
        .mockResolvedValue(mockPreferencesResult);

      const result = await apiGatewayClient.getNeighborhoodsWithDetails(
        'company-1',
        2, // limit
        1, // offset
      );

      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('n2');
      expect(result[1].id).toBe('n3');
    });

    it('should throw ApiGatewayError when underlying method fails', async () => {
      jest
        .spyOn(apiGatewayClient, 'getNeighborhoodsWithPreferences')
        .mockRejectedValue(new Error('Underlying method failed'));

      await expect(
        apiGatewayClient.getNeighborhoodsWithDetails('company-1', 10, 0),
      ).rejects.toThrow(ApiGatewayError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch neighborhoods with details (legacy)',
        {
          error: 'Underlying method failed',
          companyId: 'company-1',
        },
      );
    });
  });

  describe('Google Places Data Validation', () => {
    it('should validate Google Places data correctly', () => {
      // Access private method through double casting to avoid interface conflicts
      const validateMethod = (
        apiGatewayClient as unknown as {
          validateGooglePlacesData: (data: unknown) => boolean;
        }
      ).validateGooglePlacesData.bind(apiGatewayClient);

      // Valid data
      expect(
        validateMethod({
          place_id: 'ChIJOwg_06VPwokRYv534QaPC8g',
          name: 'New York',
        }),
      ).toBe(true);

      // Invalid data - null
      expect(validateMethod(null)).toBe(false);

      // Invalid data - undefined
      expect(validateMethod(undefined)).toBe(false);

      // Invalid data - empty object
      expect(validateMethod({})).toBe(false);

      // Invalid data - missing required fields
      expect(validateMethod({ formatted_address: 'Some address' })).toBe(false);

      // Invalid data - empty string fields
      expect(
        validateMethod({
          place_id: '',
          name: '',
        }),
      ).toBe(false);

      // Valid data - only name present
      expect(
        validateMethod({
          name: 'Valid Name',
        }),
      ).toBe(true);

      // Valid data - only place_id present
      expect(
        validateMethod({
          place_id: 'ChIJOwg_06VPwokRYv534QaPC8g',
        }),
      ).toBe(true);
    });
  });
});
