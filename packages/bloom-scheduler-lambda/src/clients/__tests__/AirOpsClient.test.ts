import { AirOpsClient } from 'src/clients/AirOpsClient';
import { ContextLogger } from 'src/utils/contextLogger';

describe('AirOpsClient', () => {
  let client: AirOpsClient;
  let mockLogger: { info: jest.Mock; error: jest.Mock };
  const originalFetch = global.fetch;

  const testInputs = [
    {
      inputs: {
        job_id: '1',
        business_name: 'Business 1',
        agent_s_website_url: 'test1.com',
        company_id: 'company1',
        environment: 'test',
      },
    },
    {
      inputs: {
        job_id: '2',
        business_name: 'Business 2',
        agent_s_website_url: 'test2.com',
        company_id: 'company2',
        environment: 'test',
      },
    },
  ];

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    client = new AirOpsClient(
      'baseUrl',
      'workflowId',
      'apiKey',
      mockLogger as unknown as ContextLogger,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
    global.fetch = originalFetch;
  });

  describe('createPosts', () => {
    it('successfully creates multiple posts', async () => {
      global.fetch = jest.fn().mockResolvedValue({ ok: true });
      const result = await client.createPosts(testInputs);
      expect(result.successfulPosts.length).toBe(2);
      expect(result.failedPosts.length).toBe(0);
    });

    it('handles mixed success and failure', async () => {
      global.fetch = jest
        .fn()
        .mockResolvedValueOnce({ ok: true })
        .mockResolvedValueOnce({ ok: false, status: 500 });

      const result = await client.createPosts(testInputs);
      expect(result.successfulPosts.length).toBe(1);
      expect(result.failedPosts.length).toBe(1);
    });
  });

  describe('getSuccessfulPosts', () => {
    it('filters successful posts correctly', () => {
      const results: PromiseSettledResult<void>[] = [
        { status: 'fulfilled', value: undefined },
        { status: 'rejected', reason: new Error() },
      ];

      const successfulPosts = client['getSuccessfulPosts'](testInputs, results);
      expect(successfulPosts).toHaveLength(1);
      expect(successfulPosts[0].inputs.job_id).toBe('1');
    });
  });

  describe('getFailedPosts', () => {
    it('filters failed posts with errors', () => {
      const error = new Error('Test error');
      const results: PromiseSettledResult<void>[] = [
        { status: 'fulfilled', value: undefined },
        { status: 'rejected', reason: error },
      ];

      const failedPosts = client['getFailedPosts'](testInputs, results);
      expect(failedPosts).toHaveLength(1);
      expect(failedPosts[0].post.inputs.job_id).toBe('2');
      expect(failedPosts[0].error).toBe(error);
    });
  });
});
