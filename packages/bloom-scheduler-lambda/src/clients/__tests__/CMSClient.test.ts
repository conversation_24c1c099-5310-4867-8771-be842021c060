import { CMS<PERSON>lient } from 'src/clients/CMSClient';
import { BaseJobStep, JobStatus } from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

import { CMSServiceError } from '../errors';

describe('CMSClient', () => {
  let client: CMSClient;
  let mockLogger: { info: jest.Mock; error: jest.Mock };
  const originalFetch = global.fetch;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    client = new CMSClient('baseUrl', mockLogger as unknown as ContextLogger);
  });

  afterEach(() => {
    jest.resetAllMocks();
    global.fetch = originalFetch;
  });

  const mockJobError = {
    message: 'test message',
    name: 'test name',
    stack: 'test stack',
  };

  const mockJobs = [
    {
      jobId: '1',
      companyId: 'company1',
      step: BaseJobStep.AIR_OPS_WEBHOOK_ERROR,
      status: JobStatus.ERROR,
      errorMessage: mockJobError,
    },
    {
      jobId: '1',
      companyId: 'company1',
      step: BaseJobStep.AIR_OPS_WEBHOOK_ERROR,
      status: JobStatus.ERROR,
      errorMessage: mockJobError,
    },
    {
      jobId: '2',
      companyId: 'company2',
      step: BaseJobStep.INITIATE_QUERY_ENTITLEMENTS,
      status: JobStatus.SUCCESS,
    },
  ];

  describe('createJobs', () => {
    it('successfully creates jobs in bulk', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockJobs),
      });

      const result = await client.createJobs(mockJobs);
      expect(result).toEqual(mockJobs);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Successfully created jobs',
        { jobs: mockJobs },
      );
    });

    it('throws CMSServiceError on failed request', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 500,
      });

      await expect(client.createJobs(mockJobs)).rejects.toBeInstanceOf(
        CMSServiceError,
      );
    });
  });

  describe('getJob', () => {
    it('retrieves job by id', async () => {
      const mockJob = {
        data: mockJobs[0],
      };
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockJob),
      });

      const result = await client.getJob('1');
      expect(result).toEqual(mockJob.data);
    });
  });

  describe('createMedia', () => {
    const companyId = 'mock-company-id';
    const buffer = Buffer.from('oijweoqiwej', 'base64');

    describe('on failure', () => {
      beforeEach(() => {
        global.fetch = jest.fn().mockResolvedValue({ ok: false });
      });

      it('should throw an error', async () => {
        await expect(
          client.createMedia(companyId, buffer),
        ).rejects.toBeInstanceOf(CMSServiceError);
      });

      it('should log the error', async () => {
        try {
          await client.createMedia(companyId, buffer);
        } catch {
          expect(mockLogger.error).toHaveBeenCalledWith(
            'Failed to create media for company: mock-company-id',
            expect.anything(),
          );
        }
      });
    });

    describe('on success', () => {
      const mediaId = 'mock-media-id-12345';
      const uploadUrl = 'mock-upload-url';
      const url = 'mock-url';
      const objectId = 'mock-object-id';

      beforeEach(() => {
        global.fetch = jest.fn().mockImplementation((fetchUrl: string) => {
          if (fetchUrl.includes('media-upload-url')) {
            return {
              ok: true,
              json: () =>
                Promise.resolve({ result: { uploadUrl, url, objectId } }),
            };
          }

          if (fetchUrl.includes(uploadUrl)) {
            return {
              ok: true,
            };
          }

          if (fetchUrl.includes('media')) {
            return {
              ok: true,
              json: () => Promise.resolve({ result: [{ mediaId }] }),
            };
          }
        });
      });

      it('should log the media id', async () => {
        await client.createMedia(companyId, buffer);

        expect(mockLogger.info).toHaveBeenCalledWith(
          `Successfully created media: ${mediaId}`,
        );
      });

      it('should return the media data', async () => {
        const result = await client.createMedia(companyId, buffer);

        expect(result).toMatchObject({ mediaId, objectId });
      });
    });
  });
});
