import { GraphQLClient } from 'graphql-request';
import { getGWAuthToken } from 'src/utils';
import { ContextLogger } from 'src/utils/contextLogger';

import { ApiGatewayClient } from '../ApiGatewayClient';
import { ApiGatewayError } from '../errors';

jest.mock('../../utils', () => ({
  getGWAuthToken: jest.fn().mockReturnValue('mock-token'),
}));

describe('ApiGatewayClient', () => {
  let client: ApiGatewayClient;
  let mockLogger: { info: jest.Mock; error: jest.Mock };
  let mockGraphQLClient: { request: jest.Mock };

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    mockGraphQLClient = {
      request: jest.fn(),
    };
    client = new ApiGatewayClient(
      'fake-super-user',
      'fake-api-key',
      mockGraphQLClient as unknown as GraphQLClient,
      mockLogger as unknown as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('initializes with correct auth token', () => {
    expect(getGWAuthToken).toHaveBeenCalledWith(
      'fake-super-user',
      'fake-api-key',
    );
  });

  describe('getCompanyDisplayNames', () => {
    it('successfully fetches company display names', async () => {
      const mockResponse = {
        brands: [{ displayName: 'Test Company', companyId: 'test-id' }],
      };
      mockGraphQLClient.request.mockResolvedValue(mockResponse);

      const result = await client.getCompanyBrands(['test-id']);
      expect(result).toEqual([
        {
          displayName: 'Test Company',
          companyId: 'test-id',
          blogPreferences: {},
        },
      ]);
      expect(mockLogger.info).toHaveBeenCalled();
      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(1);
    });

    it('throws ApiGatewayError on failed request', async () => {
      mockGraphQLClient.request.mockRejectedValue({
        response: { status: 500 },
      });

      await expect(client.getCompanyBrands(['test-id'])).rejects.toBeInstanceOf(
        ApiGatewayError,
      );
    });

    it('should paginate for more than 50 companies', async () => {
      const processChunkSpy = jest.spyOn(
        ApiGatewayClient.prototype as never,
        '_processChunk',
      );
      const bigCompanyIds = Array.from(
        { length: 100 },
        (_, i) => `test-id-${i}`,
      );
      mockGraphQLClient.request.mockResolvedValueOnce({
        brands: Array.from({ length: 50 }, (_, i) => ({
          displayName: `Test Company ${i}`,
          companyId: `test-id-${i}`,
        })),
      });
      mockGraphQLClient.request.mockResolvedValueOnce({
        brands: Array.from({ length: 50 }, (_, i) => ({
          displayName: `Test Company ${i + 50}`,
          companyId: `test-id-${i + 50}`,
        })),
      });

      const result = await client.getCompanyBrands(bigCompanyIds);

      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(2);
      expect(processChunkSpy).toHaveBeenCalledTimes(2);
      expect(result.length).toEqual(100);
      expect(result[0].displayName).toEqual('Test Company 0');
      expect(result[99].displayName).toEqual('Test Company 99');
    });
  });

  describe('getCompanyWebsites', () => {
    it('successfully fetches company websites', async () => {
      const mockResponse = {
        websites: [{ hostname: 'test.com' }],
      };
      mockGraphQLClient.request.mockResolvedValue(mockResponse);

      const result = await client.getCompanyWebsites('test-id');
      expect(result).toEqual([{ hostname: 'test.com' }]);
    });
  });

  describe('getNeighborhoods', () => {
    it('successfully fetches company neighborhoods', async () => {
      const mockResponse = {
        neighborhoods: [{ name: 'Test Neighborhood' }],
      };
      mockGraphQLClient.request.mockResolvedValue(mockResponse);

      const result = await client.getNeighborhoods('test-id', 5, []);
      expect(result).toEqual(['Test Neighborhood']);
    });

    it('should paginate results', async () => {
      const mockResponse = {
        neighborhoods: [
          { name: 'Test Neighborhood' },
          { name: 'Test Neighborhood 2' },
          { name: 'Test Neighborhood 3' },
        ],
      };
      const mockResponse2 = {
        neighborhoods: [{ name: 'Test Neighborhood 4' }],
      };
      mockGraphQLClient.request.mockResolvedValueOnce(mockResponse);
      mockGraphQLClient.request.mockResolvedValueOnce(mockResponse2);
      const result = await client.getNeighborhoods('test-id', 3, []);
      expect(result).toEqual([
        'Test Neighborhood',
        'Test Neighborhood 2',
        'Test Neighborhood 3',
        'Test Neighborhood 4',
      ]);
      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(2);
    });
  });
});
