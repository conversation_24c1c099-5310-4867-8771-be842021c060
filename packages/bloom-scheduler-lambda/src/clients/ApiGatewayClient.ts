import { GraphQLClient } from 'graphql-request';
import { BrandDTOFormatter } from 'src/formatters';
import {
  GooglePlaceData,
  Coordinates,
  BlogTopic,
} from 'src/types/blog-post-writer';
import { Brand } from 'src/types/brand';
import { Neighborhood } from 'src/types/neighborhood';
import { Website } from 'src/types/website';
import { ContextLogger, getGWAuthToken } from 'src/utils';

import { ApiGatewayError } from './errors';

export class ApiGatewayClient {
  private readonly token: string;
  private readonly gatewayChunkSize: number;

  constructor(
    private readonly apiGatewaySuperUser: string,
    private readonly apiGatewayKey: string,
    private readonly graphqlClient: GraphQLClient,
    private readonly logger: ContextLogger,
  ) {
    this.token = getGWAuthToken(this.apiGatewaySuperUser, this.apiGatewayKey);
    this.gatewayChunkSize = 50;
  }

  private async _processChunk(companyIds: string[]): Promise<Brand[]> {
    // First fetch brands for the chunk
    const brandsQuery = `
      query brands($companyId: [ID!]!) {
        brands(companyId: $companyId, disableAutomatedBlogs: false) {
          displayName
          companyId
          blogPreferences
        }
      }
    `;

    const brandsData = await this.graphqlClient.request<{
      brands: Brand[];
    }>(
      brandsQuery,
      { companyId: companyIds },
      {
        Authorization: this.token,
      },
    );

    return BrandDTOFormatter.formatGraphQLResponse(brandsData);
  }

  async getCompanyBrands(companyIds: string[]): Promise<Brand[]> {
    try {
      this.logger.info('Fetching brand attributes from companies', {
        companyIds,
      });

      // Split into chunks of 50
      const chunks = Array.from(
        {
          length: Math.ceil(companyIds.length / this.gatewayChunkSize),
        },
        (_, i) =>
          companyIds.slice(
            i * this.gatewayChunkSize,
            (i + 1) * this.gatewayChunkSize,
          ),
      );

      const allBrands: Brand[] = [];

      // Process each chunk and accumulate results
      for (const chunk of chunks) {
        const chunkResults = await this._processChunk(chunk);
        allBrands.push(...chunkResults);
      }

      this.logger.info('Fetched company brands', {
        allBrands,
      });
      return allBrands;
    } catch (error) {
      this.logger.error('Failed to fetch company brand names', {
        error: error instanceof Error ? error.message : String(error),
        companyIds,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch company brand names status: ${status}`,
        status,
        { companyIds },
      );
    }
  }

  async getCompanyWebsites(companyId: string): Promise<Website[]> {
    try {
      const websitesQuery = `
        query companyWebsite ($companyId: String) {
          websites(companyId: $companyId, status: LIVE, category: "BRAND") {
            name,
            hostname,
            category
          }
        }
      `;
      const websitesData = await this.graphqlClient.request<{
        websites: Website[];
      }>(websitesQuery, { companyId }, { Authorization: this.token });
      return websitesData.websites;
    } catch (error) {
      this.logger.error('Failed to fetch company websites', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch company websites status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  async getBrandProfile(companyId: string): Promise<{
    aboutTheBrand?: string;
    strategicFocus?: string;
    valueProposition?: string;
    idealCustomerProfiles?: string;
    missionAndCoreValues?: string;
    brandPointOfView?: string;
    toneOfVoice?: string;
    ctaText?: string;
    authorPersona?: string;
  } | null> {
    try {
      this.logger.info('Fetching brand profile', { companyId });

      const brandProfileQuery = `
        query getBrandProfile($companyId: String!) {
          brandProfile(companyId: $companyId) {
            aboutTheBrand
            strategicFocus
            valueProposition
            idealCustomerProfiles
            missionAndCoreValues
            brandPointOfView
            toneOfVoice
            ctaText
            authorPersona
          }
        }
      `;

      const brandProfileData = await this.graphqlClient.request<{
        brandProfile: {
          aboutTheBrand?: string;
          strategicFocus?: string;
          valueProposition?: string;
          idealCustomerProfiles?: string;
          missionAndCoreValues?: string;
          brandPointOfView?: string;
          toneOfVoice?: string;
          ctaText?: string;
          authorPersona?: string;
        } | null;
      }>(brandProfileQuery, { companyId }, { Authorization: this.token });

      return brandProfileData.brandProfile;
    } catch (error) {
      this.logger.error('Failed to fetch brand profile', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch brand profile status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  /**
   * Validates Google Places data to ensure it has essential fields
   */
  private validateGooglePlacesData(
    googlePlaceData: GooglePlaceData | null | undefined,
  ): boolean {
    if (!googlePlaceData || typeof googlePlaceData !== 'object') {
      return false;
    }

    // Check for essential Google Places fields
    const requiredFields = ['place_id', 'name'];
    const hasRequiredFields = requiredFields.some(field => {
      const value = googlePlaceData[field as keyof GooglePlaceData];
      return value && typeof value === 'string' && value.trim().length > 0;
    });

    return hasRequiredFields;
  }

  /**
   * Safely extracts HTTP status code from error objects
   * @param error - The error object to extract status from
   * @returns HTTP status code or 500 as default
   */
  private getErrorStatus(error: unknown): number {
    if (
      error &&
      typeof error === 'object' &&
      'response' in error &&
      error.response &&
      typeof error.response === 'object' &&
      'status' in error.response
    ) {
      const response = error.response as Record<string, unknown>;
      if (typeof response.status === 'number') {
        return response.status;
      }
    }
    return 500;
  }

  /**
   * Fetches neighborhoods with Google Places data validation
   * Only returns neighborhoods that have valid Google Places data
   */
  async getNeighborhoodsWithGooglePlaces(companyId: string): Promise<
    Array<{
      id: string;
      name: string;
      preferredName: string;
      googlePlacesName: string;
      googlePlaceData?: GooglePlaceData;
      isPrimary?: boolean;
      slug?: string;
      description?: string;
      coordinates?: Coordinates;
    }>
  > {
    try {
      this.logger.info('Fetching neighborhoods with Google Places data', {
        companyId,
      });

      const neighborhoodsQuery = `
        query GetNeighborhoodsWithGooglePlaces($companyId: String!) {
          neighborhoods(companyId: $companyId) {
            id
            name
            googlePlaceData
            isPrimary
            slug
            description
            coordinates
          }
        }
      `;

      const neighborhoodsData = await this.graphqlClient.request<{
        neighborhoods: Array<{
          id: string;
          name: string;
          googlePlaceData?: GooglePlaceData;
          isPrimary?: boolean;
          slug?: string;
          description?: string;
          coordinates?: Coordinates;
        }>;
      }>(neighborhoodsQuery, { companyId }, { Authorization: this.token });

      // Filter to only include neighborhoods with valid Google Places data
      const neighborhoodsWithGooglePlaces =
        neighborhoodsData.neighborhoods.filter(neighborhood =>
          this.validateGooglePlacesData(neighborhood.googlePlaceData),
        );

      this.logger.info('Filtered neighborhoods with valid Google Places data', {
        totalNeighborhoods: neighborhoodsData.neighborhoods.length,
        validNeighborhoods: neighborhoodsWithGooglePlaces.length,
        companyId,
      });

      return neighborhoodsWithGooglePlaces.map(n => ({
        id: n.id,
        name: n.name,
        preferredName: n.name,
        googlePlacesName: n.googlePlaceData?.name || n.name,
        googlePlaceData: n.googlePlaceData,
        isPrimary: n.isPrimary,
        slug: n.slug,
        description: n.description,
        coordinates: n.coordinates,
      }));
    } catch (error) {
      this.logger.error(
        'Failed to fetch neighborhoods with Google Places data',
        {
          error: error instanceof Error ? error.message : String(error),
          companyId,
        },
      );
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch neighborhoods with Google Places data status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  /**
   * Fetches brand blog preferences containing preferred locations
   */
  async getBrandBlogPreferences(
    companyId: string,
  ): Promise<{ preferredLocations?: string[] }> {
    try {
      this.logger.info('Fetching brand blog preferences', { companyId });

      const blogPreferencesQuery = `
        query GetBrandBlogPreferences($companyId: ID!) {
          company(id: $companyId) {
            blogPreferences
          }
        }
      `;

      const blogPreferencesData = await this.graphqlClient.request<{
        company: {
          blogPreferences?: { preferredLocations?: string[] };
        } | null;
      }>(blogPreferencesQuery, { companyId }, { Authorization: this.token });

      const blogPreferences =
        blogPreferencesData.company?.blogPreferences || {};

      this.logger.info('Fetched brand blog preferences', {
        companyId,
        hasPreferredLocations: Boolean(
          blogPreferences.preferredLocations?.length,
        ),
        preferredLocationsCount:
          blogPreferences.preferredLocations?.length || 0,
      });

      return blogPreferences;
    } catch (error) {
      this.logger.error('Failed to fetch brand blog preferences', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      // Don't throw error for blog preferences - treat as non-critical
      // Return empty preferences to allow neighborhoods fetching to continue
      return {};
    }
  }

  /**
   * Combined method that fetches neighborhoods with Google Places validation
   * and separates them by preference status
   */
  async getNeighborhoodsWithPreferences(companyId: string): Promise<{
    allNeighborhoods: Array<{
      id: string;
      name: string;
      preferredName: string;
      googlePlacesName: string;
      googlePlaceData?: GooglePlaceData;
      isPrimary?: boolean;
      slug?: string;
      description?: string;
      coordinates?: Coordinates;
      isPreferred?: boolean;
    }>;
    preferredNeighborhoods: Array<{
      id: string;
      name: string;
      preferredName: string;
      googlePlacesName: string;
      googlePlaceData?: GooglePlaceData;
      isPrimary?: boolean;
      slug?: string;
      description?: string;
      coordinates?: Coordinates;
      isPreferred?: boolean;
    }>;
    otherNeighborhoods: Array<{
      id: string;
      name: string;
      preferredName: string;
      googlePlacesName: string;
      googlePlaceData?: GooglePlaceData;
      isPrimary?: boolean;
      slug?: string;
      description?: string;
      coordinates?: Coordinates;
      isPreferred?: boolean;
    }>;
  }> {
    try {
      this.logger.info('Fetching neighborhoods with preferences', {
        companyId,
      });

      // Fetch both datasets in parallel
      const [neighborhoods, blogPreferences] = await Promise.all([
        this.getNeighborhoodsWithGooglePlaces(companyId),
        this.getBrandBlogPreferences(companyId),
      ]);

      const preferredLocationIds = blogPreferences.preferredLocations || [];

      // Separate preferred vs other neighborhoods
      // Check against id, name, and slug for flexibility
      const preferredNeighborhoods = neighborhoods
        .filter(
          n =>
            preferredLocationIds.includes(n.id) ||
            preferredLocationIds.includes(n.name) ||
            (n.slug && preferredLocationIds.includes(n.slug)),
        )
        .map(n => ({ ...n, isPreferred: true }));

      const otherNeighborhoods = neighborhoods
        .filter(
          n =>
            !preferredLocationIds.includes(n.id) &&
            !preferredLocationIds.includes(n.name) &&
            !(n.slug && preferredLocationIds.includes(n.slug)),
        )
        .map(n => ({ ...n, isPreferred: false }));

      const allNeighborhoods = [
        ...preferredNeighborhoods,
        ...otherNeighborhoods,
      ];

      this.logger.info('Separated neighborhoods by preference', {
        companyId,
        totalNeighborhoods: allNeighborhoods.length,
        preferredCount: preferredNeighborhoods.length,
        otherCount: otherNeighborhoods.length,
        preferredLocationIds,
      });

      return {
        allNeighborhoods,
        preferredNeighborhoods,
        otherNeighborhoods,
      };
    } catch (error) {
      this.logger.error('Failed to fetch neighborhoods with preferences', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch neighborhoods with preferences status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  /**
   * @deprecated Use getNeighborhoodsWithPreferences instead
   * Legacy method maintained for backward compatibility
   */
  async getNeighborhoodsWithDetails(
    companyId: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<
    Array<{
      id: string;
      preferredName: string;
      googlePlacesName: string;
      isPreferred?: boolean;
    }>
  > {
    this.logger.warn('Using deprecated getNeighborhoodsWithDetails method', {
      companyId,
      limit,
      offset,
    });

    try {
      const { allNeighborhoods } =
        await this.getNeighborhoodsWithPreferences(companyId);

      // Apply limit and offset for backward compatibility
      const slicedNeighborhoods = allNeighborhoods.slice(
        offset,
        offset + limit,
      );

      return slicedNeighborhoods.map(n => ({
        id: n.id,
        name: n.name,
        preferredName: n.preferredName,
        googlePlacesName: n.googlePlacesName,
        isPreferred: n.isPreferred,
      }));
    } catch (error) {
      this.logger.error('Failed to fetch neighborhoods with details (legacy)', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch neighborhoods with details status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  async getNeighborhoods(
    companyId: string,
    limit: number,
    neighborhoodIds: string[],
  ): Promise<string[]> {
    try {
      this.logger.info('Fetching neighborhoods', {
        companyId,
        limit,
        neighborhoodIds,
      });
      let offset = 0;
      let hasMore = true;
      const neighborhoods: string[] = [];
      const neighborhoodsQuery = `
        query neighborhoods($companyId: String, $limit: Int, $offset: Int, $neighborhoodIds: [ID!]) {
          neighborhoods(companyId: $companyId, limit: $limit, offset: $offset, neighborhoodIds: $neighborhoodIds) {
            name
          }
        }
      `;

      while (hasMore) {
        const neighborhoodsData = await this.graphqlClient.request<{
          neighborhoods: Neighborhood[];
        }>(
          neighborhoodsQuery,
          { companyId, limit, offset, neighborhoodIds },
          { Authorization: this.token },
        );
        neighborhoods.push(...neighborhoodsData.neighborhoods.map(n => n.name));
        offset += limit;
        hasMore = neighborhoodsData.neighborhoods.length === limit;
      }
      return neighborhoods;
    } catch (error) {
      const status = this.getErrorStatus(error);
      this.logger.error('Failed to fetch company neighborhoods', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      throw new ApiGatewayError(
        `Failed to fetch company neighborhoods status: ${status}`,
        status,
        { companyId },
      );
    }
  }

  /**
   * Fetches a blog topic using the blogTopicSelector GraphQL query
   */
  async selectBlogTopic(
    companyId: string,
    preferredNeighborhoods?: string[],
  ): Promise<BlogTopic | null> {
    try {
      this.logger.info('Selecting blog topic via GraphQL', {
        companyId,
        preferredNeighborhoodsCount: preferredNeighborhoods?.length || 0,
      });

      const blogTopicSelectorQuery = `
        query SelectBlogTopic($companyId: ID!, $preferredNeighborhoods: [ID!]) {
          blogTopicSelector(companyId: $companyId, preferredNeighborhoods: $preferredNeighborhoods) {
            id
            companyId
            topic
            blogTitle
            rationale
            type
            neighborhoodId
            parentTopic
            createdAt
            updatedAt
            cmsPostId
            blogPostJobId
            airopsExecutionId
          }
        }
      `;

      const blogTopicData = await this.graphqlClient.request<{
        blogTopicSelector: BlogTopic | null;
      }>(
        blogTopicSelectorQuery,
        { companyId, preferredNeighborhoods },
        { Authorization: this.token },
      );

      const selectedTopic = blogTopicData.blogTopicSelector;

      this.logger.info('Blog topic selected', {
        companyId,
        topicId: selectedTopic?.id,
        topicText: selectedTopic?.topic,
        neighborhoodId: selectedTopic?.neighborhoodId,
        hasPreferredNeighborhoods: Boolean(preferredNeighborhoods?.length),
      });

      return selectedTopic;
    } catch (error) {
      this.logger.error('Failed to select blog topic', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
        preferredNeighborhoodsCount: preferredNeighborhoods?.length || 0,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to select blog topic status: ${status}`,
        status,
        { companyId, preferredNeighborhoods },
      );
    }
  }

  /**
   * Locks a blog topic by setting its cmsPostId
   */
  async lockBlogTopic(
    blogTopicId: string,
    cmsPostId: string,
  ): Promise<BlogTopic> {
    try {
      this.logger.info('Locking blog topic', {
        blogTopicId,
        cmsPostId,
      });

      const updateBlogTopicMutation = `
        mutation UpdateBlogTopic($id: ID!, $input: UpdateBlogTopicInput!) {
          updateBlogTopic(id: $id, input: $input) {
            id
            companyId
            topic
            blogTitle
            rationale
            type
            neighborhoodId
            parentTopic
            createdAt
            updatedAt
            cmsPostId
            blogPostJobId
            airopsExecutionId
          }
        }
      `;

      const updateResult = await this.graphqlClient.request<{
        updateBlogTopic: BlogTopic;
      }>(
        updateBlogTopicMutation,
        {
          id: blogTopicId,
          input: { cmsPostId },
        },
        { Authorization: this.token },
      );

      this.logger.info('Blog topic locked successfully', {
        blogTopicId,
        cmsPostId,
      });

      return updateResult.updateBlogTopic;
    } catch (error) {
      this.logger.error('Failed to lock blog topic', {
        error: error instanceof Error ? error.message : String(error),
        blogTopicId,
        cmsPostId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to lock blog topic status: ${status}`,
        status,
        { blogTopicId, cmsPostId },
      );
    }
  }

  /**
   * Unlocks a blog topic by clearing its cmsPostId
   */
  async unlockBlogTopic(blogTopicId: string): Promise<BlogTopic> {
    try {
      this.logger.info('Unlocking blog topic', {
        blogTopicId,
      });

      const updateBlogTopicMutation = `
        mutation UpdateBlogTopic($id: ID!, $input: UpdateBlogTopicInput!) {
          updateBlogTopic(id: $id, input: $input) {
            id
            companyId
            topic
            blogTitle
            rationale
            type
            neighborhoodId
            parentTopic
            createdAt
            updatedAt
            cmsPostId
            blogPostJobId
            airopsExecutionId
          }
        }
      `;

      const updateResult = await this.graphqlClient.request<{
        updateBlogTopic: BlogTopic;
      }>(
        updateBlogTopicMutation,
        {
          id: blogTopicId,
          input: { cmsPostId: null },
        },
        { Authorization: this.token },
      );

      this.logger.info('Blog topic unlocked successfully', {
        blogTopicId,
      });

      return updateResult.updateBlogTopic;
    } catch (error) {
      this.logger.error('Failed to unlock blog topic', {
        error: error instanceof Error ? error.message : String(error),
        blogTopicId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to unlock blog topic status: ${status}`,
        status,
        { blogTopicId },
      );
    }
  }

  /**
   * Fetches a single neighborhood by ID
   */
  async getNeighborhoodById(
    companyId: string,
    neighborhoodId: string,
  ): Promise<{
    id: string;
    name: string;
    preferredName: string;
    googlePlacesName: string;
    googlePlaceData?: GooglePlaceData;
    isPrimary?: boolean;
    slug?: string;
    description?: string;
    coordinates?: Coordinates;
  } | null> {
    try {
      const requestPayload = { companyId, neighborhoodId, limit: 1, offset: 0 };
      const requestHeaders = { Authorization: this.token };

      this.logger.info('Fetching neighborhood by ID - Enhanced Debug Info', {
        companyId,
        neighborhoodId,
        graphqlClientUrl:
          (this.graphqlClient as unknown as { url?: string }).url ||
          'URL not accessible',
        requestPayload,
        requestHeaders: {
          Authorization: this.token ? 'Bearer [REDACTED]' : 'No token',
        },
        apiGatewaySuperUser: this.apiGatewaySuperUser,
        environmentApiGatewayUrl: process.env.API_GATEWAY_URL,
      });

      const neighborhoodQuery = `
        query GetNeighborhoodById($companyId: String!, $neighborhoodId: ID!, $limit: Int!, $offset: Int!) {
          neighborhoods(companyId: $companyId, limit: $limit, offset: $offset, neighborhoodIds: [$neighborhoodId]) {
            id
            name
            googlePlaceData
            isPrimary
            slug
            description
            coordinates
          }
        }
      `;

      this.logger.info('About to execute GraphQL request', {
        query: neighborhoodQuery.replace(/\s+/g, ' ').trim(),
        variables: requestPayload,
      });

      const neighborhoodData = await this.graphqlClient.request<{
        neighborhoods: Array<{
          id: string;
          name: string;
          googlePlaceData?: GooglePlaceData;
          isPrimary?: boolean;
          slug?: string;
          description?: string;
          coordinates?: Coordinates;
        }>;
      }>(neighborhoodQuery, requestPayload, requestHeaders);

      this.logger.info('GraphQL request completed successfully', {
        responseData: {
          neighborhoodsCount: neighborhoodData.neighborhoods?.length || 0,
          neighborhoods:
            neighborhoodData.neighborhoods?.map(n => ({
              id: n.id,
              name: n.name,
              hasGooglePlaceData: !!n.googlePlaceData,
            })) || [],
        },
      });

      const neighborhood = neighborhoodData.neighborhoods[0];

      if (!neighborhood) {
        this.logger.info('Neighborhood not found in response', {
          companyId,
          neighborhoodId,
          totalNeighborhoodsReturned:
            neighborhoodData.neighborhoods?.length || 0,
        });
        return null;
      }

      this.logger.info('Neighborhood fetched successfully', {
        companyId,
        neighborhoodId,
        neighborhoodName: neighborhood.name,
        hasGooglePlaceData: !!neighborhood.googlePlaceData,
      });

      return {
        id: neighborhood.id,
        name: neighborhood.name,
        preferredName: neighborhood.name,
        googlePlacesName:
          neighborhood.googlePlaceData?.name || neighborhood.name,
        googlePlaceData: neighborhood.googlePlaceData,
        isPrimary: neighborhood.isPrimary,
        slug: neighborhood.slug,
        description: neighborhood.description,
        coordinates: neighborhood.coordinates,
      };
    } catch (error) {
      this.logger.error(
        'Failed to fetch neighborhood by ID - Enhanced Error Info',
        {
          error: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
          companyId,
          neighborhoodId,
          graphqlClientUrl:
            (this.graphqlClient as unknown as { url?: string }).url ||
            'URL not accessible',
          environmentApiGatewayUrl: process.env.API_GATEWAY_URL,
          requestPayload: { companyId, neighborhoodId, limit: 1, offset: 0 },
          errorResponse: (
            error as {
              response?: {
                status?: number;
                statusText?: string;
                data?: unknown;
              };
            }
          )?.response
            ? {
                status: (error as { response: { status?: number } }).response
                  .status,
                statusText: (error as { response: { statusText?: string } })
                  .response.statusText,
                data: (error as { response: { data?: unknown } }).response.data,
              }
            : 'No response data',
        },
      );
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to fetch neighborhood by ID status: ${status}`,
        status,
        { companyId, neighborhoodId },
      );
    }
  }

  /**
   * Updates a blog topic's airops execution ID
   */
  async updateBlogTopicAiropsId(
    blogTopicId: string,
    airopsExecutionId: string,
  ): Promise<BlogTopic> {
    try {
      this.logger.info('Updating blog topic airops execution ID', {
        blogTopicId,
        airopsExecutionId,
      });

      const updateBlogTopicMutation = `
        mutation UpdateBlogTopic($id: ID!, $input: UpdateBlogTopicInput!) {
          updateBlogTopic(id: $id, input: $input) {
            id
            companyId
            topic
            blogTitle
            rationale
            type
            neighborhoodId
            parentTopic
            createdAt
            updatedAt
            cmsPostId
            blogPostJobId
            airopsExecutionId
          }
        }
      `;

      const updateResult = await this.graphqlClient.request<{
        updateBlogTopic: BlogTopic;
      }>(
        updateBlogTopicMutation,
        {
          id: blogTopicId,
          input: { airopsExecutionId },
        },
        { Authorization: this.token },
      );

      this.logger.info('Blog topic airops execution ID updated successfully', {
        blogTopicId,
        airopsExecutionId,
      });

      return updateResult.updateBlogTopic;
    } catch (error) {
      this.logger.error('Failed to update blog topic airops execution ID', {
        error: error instanceof Error ? error.message : String(error),
        blogTopicId,
        airopsExecutionId,
      });
      const status = this.getErrorStatus(error);
      throw new ApiGatewayError(
        `Failed to update blog topic airops execution ID status: ${status}`,
        status,
        { blogTopicId, airopsExecutionId },
      );
    }
  }
}
