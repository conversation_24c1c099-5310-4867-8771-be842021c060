import { GraphQLClient } from 'graphql-request';

import {
  Brand<PERSON>rofile,
  BlogTopic,
  Neighborhood,
} from '../types/blog-post-writer';
import { createContextLogger } from '../utils/contextLogger';

import { ApiGatewayClient } from './ApiGatewayClient';

export class CosmoClient {
  private readonly graphqlClient: GraphQLClient;
  private readonly apiGatewayClient: ApiGatewayClient;
  private readonly logger: ReturnType<typeof createContextLogger>;

  constructor(
    cosmoGqlUrl: string,
    m2mSuperApiKey: string,
    apiGatewayClient: ApiGatewayClient,
    logger: ReturnType<typeof createContextLogger>,
  ) {
    this.logger = logger;
    this.apiGatewayClient = apiGatewayClient;

    // Normalize URL to ensure it has /graphql path
    let graphqlUrl: string;
    if (cosmoGqlUrl.endsWith('/graphql')) {
      // Already has /graphql
      graphqlUrl = cosmoGqlUrl;
    } else if (cosmoGqlUrl.endsWith('/')) {
      // Has trailing slash, append graphql
      graphqlUrl = `${cosmoGqlUrl}graphql`;
    } else {
      // No trailing slash, append /graphql
      graphqlUrl = `${cosmoGqlUrl}/graphql`;
    }

    this.logger.info('Initializing CosmoClient', {
      originalUrl: cosmoGqlUrl,
      normalizedUrl: graphqlUrl,
      urlTransformed: cosmoGqlUrl !== graphqlUrl,
      hasApiKey: !!m2mSuperApiKey,
      apiKeyLength: m2mSuperApiKey?.length || 0,
    });

    this.graphqlClient = new GraphQLClient(graphqlUrl, {
      headers: {
        'x-lp-api-key': m2mSuperApiKey.split(',')[0],
      },
    });
  }

  async getBrandProfile(companyId: string): Promise<BrandProfile | null> {
    try {
      this.logger.info('Fetching brand profile via COSMO', { companyId });

      const query = `
        query GetBrandProfile($companyId: ID!) {
          brandProfile(companyId: $companyId) {
            companyId
            aboutTheBrand
            strategicFocus
            valueProposition
            idealCustomerProfiles
            missionAndCoreValues
            brandPointOfView
            toneOfVoice
            ctaText
            authorPersona
          }
        }
      `;

      const data = await this.graphqlClient.request<{
        brandProfile:
          | (Omit<BrandProfile, 'companyId'> & { companyId?: string })
          | null;
      }>(query, { companyId });

      if (data.brandProfile === null) {
        this.logger.info('Brand profile query returned null', { companyId });
        return null;
      }

      this.logger.info('Brand profile fetched successfully via COSMO', {
        companyId,
      });

      return {
        ...data.brandProfile,
        companyId: data.brandProfile.companyId || companyId,
      };
    } catch {
      this.logger.error(
        'Error fetching brand profile via COSMO, falling back to API Gateway',
        {
          companyId,
        },
      );
      const fallbackResult =
        await this.apiGatewayClient.getBrandProfile(companyId);
      if (fallbackResult === null) {
        return null;
      }
      return {
        ...fallbackResult,
        companyId,
      };
    }
  }

  async selectBlogTopic(
    companyId: string,
    preferredNeighborhoods?: string[],
  ): Promise<BlogTopic | null> {
    try {
      this.logger.info('Selecting blog topic via COSMO', {
        companyId,
        preferredNeighborhoodsCount: preferredNeighborhoods?.length || 0,
      });

      const query = `
        query SelectBlogTopic($companyId: ID!, $preferredNeighborhoods: [ID!]) {
          blogTopicSelector(companyId: $companyId, preferredNeighborhoods: $preferredNeighborhoods) {
            id
            companyId
            topic
            blogTitle
            neighborhoodId
            rationale
            airopsExecutionId
            cmsPostId
            blogPostJobId
            parentTopic
            type
            createdAt
            updatedAt
          }
        }
      `;

      const data = await this.graphqlClient.request<{
        blogTopicSelector: BlogTopic | null;
      }>(query, {
        companyId,
        preferredNeighborhoods,
      });

      this.logger.info('Blog topic selected via COSMO', {
        companyId,
        topicId: data.blogTopicSelector?.id,
      });

      return data.blogTopicSelector;
    } catch {
      this.logger.error(
        'Error selecting blog topic via COSMO, falling back to API Gateway',
        {
          companyId,
        },
      );
      return this.apiGatewayClient.selectBlogTopic(
        companyId,
        preferredNeighborhoods,
      );
    }
  }

  /**
   * Generic method to update a blog topic with any input fields
   */
  async updateBlogTopic(
    blogTopicId: string,
    input: {
      airopsExecutionId?: string;
      cmsPostId?: string | null;
      blogPostJobId?: string;
      [key: string]: unknown;
    },
  ): Promise<BlogTopic> {
    try {
      this.logger.info('Updating blog topic via COSMO', {
        blogTopicId,
        input,
      });

      const mutation = `
        mutation UpdateBlogTopic($id: ID!, $input: UpdateBlogTopicInput!) {
          updateBlogTopic(id: $id, input: $input) {
            id
            companyId
            topic
            blogTitle
            neighborhoodId
            rationale
            airopsExecutionId
            cmsPostId
            blogPostJobId
            parentTopic
            type
            createdAt
            updatedAt
          }
        }
      `;

      const data = await this.graphqlClient.request<{
        updateBlogTopic: BlogTopic;
      }>(mutation, {
        id: blogTopicId,
        input,
      });

      this.logger.info('Blog topic updated successfully via COSMO', {
        blogTopicId,
        input,
      });

      return data.updateBlogTopic;
    } catch (error) {
      this.logger.error(
        'Error updating blog topic via COSMO, falling back to API Gateway',
        {
          blogTopicId,
          input,
          error: error instanceof Error ? error.message : String(error),
        },
      );

      // For specific fallback methods, use the appropriate API Gateway method
      if (input.airopsExecutionId && Object.keys(input).length === 1) {
        return this.apiGatewayClient.updateBlogTopicAiropsId(
          blogTopicId,
          input.airopsExecutionId,
        );
      }

      // For lock/unlock operations, throw the error since we want to use COSMO
      throw new Error(
        `Failed to update blog topic: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  async updateBlogTopicAiropsId(
    blogTopicId: string,
    airopsExecutionId: string,
  ): Promise<BlogTopic> {
    return this.updateBlogTopic(blogTopicId, { airopsExecutionId });
  }

  async getNeighborhoodsWithPreferences(companyId: string) {
    this.logger.info(
      'Fetching neighborhoods with preferences via API Gateway (not available in COSMO)',
    );
    return this.apiGatewayClient.getNeighborhoodsWithPreferences(companyId);
  }

  async getNeighborhoodsWithDetails(companyId: string) {
    this.logger.info(
      'Fetching neighborhoods with details via API Gateway (not available in COSMO)',
    );
    return this.apiGatewayClient.getNeighborhoodsWithDetails(companyId);
  }

  async lockBlogTopic(
    blogTopicId: string,
    cmsPostId: string,
  ): Promise<BlogTopic> {
    this.logger.info('Locking blog topic via COSMO', {
      blogTopicId,
      cmsPostId,
    });
    return this.updateBlogTopic(blogTopicId, { cmsPostId });
  }

  async unlockBlogTopic(blogTopicId: string): Promise<BlogTopic> {
    this.logger.info('Unlocking blog topic via COSMO', {
      blogTopicId,
    });
    return this.updateBlogTopic(blogTopicId, { cmsPostId: null });
  }

  async getNeighborhoodById(
    companyId: string,
    neighborhoodId: string,
  ): Promise<Neighborhood | null> {
    this.logger.info(
      'Fetching neighborhood by ID via API Gateway (not available in COSMO)',
      {
        companyId,
        neighborhoodId,
      },
    );

    try {
      // Use the efficient single neighborhood fetch method
      const found = await this.apiGatewayClient.getNeighborhoodById(
        companyId,
        neighborhoodId,
      );

      if (found) {
        this.logger.info('Neighborhood found via API Gateway', {
          companyId,
          neighborhoodId,
          neighborhoodName: found.name,
        });

        // Convert to full Neighborhood object with all available data
        return {
          id: found.id,
          name: found.name,
          preferredName: found.preferredName,
          googlePlacesName: found.googlePlacesName,
          priority: undefined, // Priority not available from single fetch
          slug: found.slug,
          description: found.description,
          googlePlaceData: found.googlePlaceData,
          coordinates: found.coordinates,
          isPrimary: found.isPrimary,
        } as Neighborhood;
      } else {
        this.logger.warn('Neighborhood not found via API Gateway', {
          companyId,
          neighborhoodId,
        });
        return null;
      }
    } catch (error) {
      this.logger.error('Error fetching neighborhood by ID via API Gateway', {
        companyId,
        neighborhoodId,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }
}
