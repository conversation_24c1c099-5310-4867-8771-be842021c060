import { PostDTO, PostResult, FailedPostDTO } from 'src/types/post';
import { ContextLogger } from 'src/utils/contextLogger';

import { AirOpsError } from './errors';

export class AirOpsClient {
  constructor(
    private readonly baseUrl: string,
    private readonly workflowId: string,
    private readonly airOpsApiKey: string,
    private readonly logger: ContextLogger,
  ) {}

  private getSuccessfulPosts(
    posts: PostDTO[],
    results: PromiseSettledResult<void>[],
  ): PostDTO[] {
    return posts.filter((_, index) => results[index].status === 'fulfilled');
  }

  private getFailedPosts(
    posts: PostDTO[],
    results: PromiseSettledResult<void>[],
  ): FailedPostDTO[] {
    return posts
      .map((post, index) => {
        const result = results[index];
        if (result.status === 'rejected') {
          return {
            post: post,
            error: result.reason,
          };
        }
        return null;
      })
      .filter((item): item is FailedPostDTO => item !== null);
  }

  private async createPost(post: PostDTO): Promise<void> {
    const url = `${this.baseUrl}/${this.workflowId}/async_execute`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.airOpsApiKey}`,
      },
      body: JSON.stringify(post),
    });

    if (!response.ok) {
      this.logger.error(
        `AirOps service HTTP error sending generate post request status: ${response.status}`,
      );
      throw new AirOpsError(
        `AirOps service HTTP error sending create post request status: ${response.status}`,
        response.status,
        {
          post,
        },
      );
    }
  }

  async createPosts(posts: PostDTO[]): Promise<PostResult> {
    const results = await Promise.allSettled(
      posts.map(post => this.createPost(post)),
    );

    this.logger.info('AirOps service create posts responses', {
      results,
    });
    return {
      successfulPosts: this.getSuccessfulPosts(posts, results),
      failedPosts: this.getFailedPosts(posts, results),
    };
  }

  private async getExecution(executionId: string): Promise<PostDTO> {
    const url = `${this.baseUrl}/executions/${executionId}`;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.airOpsApiKey}`,
      },
    });

    if (!response.ok) {
      this.logger.error(
        `AirOps service HTTP error getting execution status: ${response.status}`,
      );
      throw new AirOpsError(
        `AirOps service HTTP error getting execution status: ${response.status}`,
        response.status,
        {
          executionId,
        },
      );
    }
    return (await response.json()) as PostDTO;
  }

  async getExecutionData(executionId: string): Promise<PostDTO> {
    const response = await this.getExecution(executionId);

    this.logger.info('Job execution data from AirOps', {
      inputs: response.inputs,
    });
    return { inputs: response.inputs };
  }
}
