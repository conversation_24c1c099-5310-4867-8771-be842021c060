import { Agent } from 'https';

import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { Context } from 'aws-lambda';
import { getConfig } from 'src/config';
import { createEntitlementService } from 'src/services/serviceFactories';
import { FeatureFlags } from 'src/types/featureFlags';
import {
  InitiateQueryEntitlementEvent,
  InitiateQueryEntitlementResponse,
} from 'src/types/handler';
import { BaseJobStep } from 'src/types/job';
import {
  LaunchDarkly,
  createHandlerError,
  createContextLogger,
} from 'src/utils';

// for source map support in logs
import 'source-map-support/register';

const STEP_NAME = BaseJobStep.INITIATE_QUERY_ENTITLEMENTS;
const config = getConfig();

const BUCKET_NAME = `lp-bloom-scheduler-execution-${config.ENVIRONMENT === 'development' ? 'development' : 'production'}`;

export const handler = async (
  event: InitiateQueryEntitlementEvent,
  context: Context,
): Promise<InitiateQueryEntitlementResponse> => {
  const timestamp = new Date().toISOString();
  const logger = createContextLogger('initiateQueryEntitlements', context, {
    lambdaName: 'initiateQueryEntitlements',
    eventType: 'InitiateQueryEntitlementEvent',
  });
  logger.info('Initiating query entitlements event:', { event });

  // Configure connection pooling
  const agent = new Agent({
    keepAlive: true,
    keepAliveMsecs: 1000,
    maxSockets: 25,
    maxFreeSockets: 5,
  });

  // Configure S3 client with simplified configuration
  const s3Client = new S3Client({
    region: config.REGION,
    maxAttempts: 3,
    requestHandler: new NodeHttpHandler({
      httpsAgent: agent,
      connectionTimeout: 5000,
    }),
    retryMode: 'adaptive',
  });

  // Set a shorter timeout to ensure we have time for cleanup
  context.callbackWaitsForEmptyEventLoop = false;

  try {
    // Check global feature flag
    const isEnabled = await LaunchDarkly.checkVariation(
      FeatureFlags.ENABLE_BLOOM,
    );
    if (!isEnabled) {
      logger.info('Bloom scheduler feature flag is not enabled');
      return { bucketName: '', key: '' };
    }

    const entitlementService = createEntitlementService(logger);

    // Process entitlements
    const entitlements =
      await entitlementService.processEntitlementsFromEvent(event);

    /**
     * There is no need to calculate a retry step for this lambda,
     * we only create jobs if it succeeds.
     * If it needs to retry, the jobs will not have been created
     * so we can just create all the jobs on the retry
     */
    await entitlementService.createEntitlementSuccessJobs(
      entitlements,
      STEP_NAME,
    );

    const entitlementChunks =
      entitlementService.chunkEntitlements(entitlements);

    logger.info('Generating posts for entitlements', {
      count: entitlements.length,
      chunks: `Splitting ${entitlements.length} entitlements into ${entitlementChunks.length} chunks`,
    });

    const s3Key = `entitlements-${timestamp}.json`;
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: JSON.stringify(entitlementChunks),
    });

    logger.info('Saving entitlements chunks to S3', {
      bucketName: BUCKET_NAME,
      key: s3Key,
      timestamp,
    });

    try {
      const result = await s3Client.send(command);
      logger.info('Entitlements saved to S3', { result });
    } catch (s3Error) {
      logger.error('Error uploading to S3', {
        error: s3Error.message,
        code: s3Error.code,
        bucket: BUCKET_NAME,
        key: s3Key,
        stack: s3Error.stack,
      });
      throw s3Error;
    }

    return {
      bucketName: BUCKET_NAME,
      key: s3Key,
    };
  } catch (error) {
    // Add specific handling for connection-related errors
    if (error instanceof Error && error.message.includes('ECONNRESET')) {
      logger.error('Connection reset error - likely due to timeout', {
        error: error.message,
        stack: error.stack,
      });
    }

    // Log the error with full context
    logger.error('Error in initiateQueryEntitlements', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      event,
      timestamp,
    });

    throw createHandlerError({
      error,
      context,
      logger,
      metadata: { step: STEP_NAME },
      event,
    });
  }
};
