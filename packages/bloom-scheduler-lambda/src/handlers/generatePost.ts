import { Context } from 'aws-lambda';
import { createPostService } from 'src/services/serviceFactories';
import { GeneratePostEvent } from 'src/types/handler';
import { GeneratePostResponse } from 'src/types/handler';
import { BaseJobStep } from 'src/types/job';
import { PostStateTracker } from 'src/types/post';
import { createHandlerError, createContextLogger } from 'src/utils';

import 'source-map-support/register';

const STEP_NAME = BaseJobStep.GENERATE_POST;

export const handler = async (
  entitlementChunk: GeneratePostEvent,
  context: Context,
): Promise<GeneratePostResponse> => {
  const logger = createContextLogger('generatePost', context, {
    lambdaName: 'generatePost',
    eventType: 'GeneratePostEvent',
  });
  logger.info('Generating posts event', { event: entitlementChunk });

  const retryStep = entitlementChunk[0].retryStep;

  /**
   * This handler can be called from the AirOps error webhook to retry
   * the generation of posts. If this a retry, a retryStep will be provider
   *  and we will use that step to create the job
   */
  const step = retryStep || STEP_NAME;

  /**
   * Use to track the state of the posts and is
   * passed through the lifecycle of this execution
   * to track the state of the posts for information included
   * in the return / error
   */
  const postStateTracker: PostStateTracker = {
    totalPosts: entitlementChunk.length,
    totalSuccessfulPosts: 0,
    successfulPosts: [],
    totalFailedPosts: 0,
    failedPosts: [],
  };

  try {
    const postService = createPostService(logger, step);
    const { successfulPosts, failedPosts, ...rest } =
      await postService.createPosts(entitlementChunk, postStateTracker);

    logger.info('Post generations', {
      successfulPosts,
      failedPosts,
      ...rest,
    });

    return {
      successfulPostJobIds: successfulPosts.map(post => post.jobId),
      failedPostJobIds: failedPosts.map(({ post }) => post.jobId),
      ...rest,
    };
  } catch (error) {
    const {
      totalSuccessfulPosts,
      failedPosts,
      successfulPosts,
      totalFailedPosts,
      totalPosts,
    } = postStateTracker;

    /**
     * Don't throw an error (which could trigger a lambda retry)
     * unless there were no successful posts
     * to ensure no posts get generated twice
     */
    //
    if (totalSuccessfulPosts === 0) {
      throw createHandlerError({
        error,
        context,
        event: entitlementChunk,
        logger,
        metadata: {
          failedPosts,
          totalFailedPosts,
        },
      });
    }

    logger.error(
      'There were successful posts but there was an error. No retry.',
    );

    return {
      totalSuccessfulPosts,
      successfulPostJobIds: successfulPosts.map(post => post.jobId),
      failedPostJobIds: failedPosts.map(({ post }) => post.jobId),
      totalFailedPosts,
      totalPosts,
    };
  }
};
