import { InvokeCommand, LambdaClient } from '@aws-sdk/client-lambda';
import {
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
  Context,
} from 'aws-lambda';
import { AirOpsClient } from 'src/clients';
import { getConfig } from 'src/config';
import { createJobService } from 'src/services/serviceFactories';
import { AirOpsWebhookPayload } from 'src/types/airOps';
import { createHandlerError, createContextLogger } from 'src/utils';

import 'source-map-support/register';

const config = getConfig();

export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  const logger = createContextLogger('airOpsWebhookError', context, {
    lambdaName: 'airOpsWebhookError',
    eventType: 'APIGatewayProxyEvent',
  });
  logger.info('Received AirOps webhook event', { event });

  try {
    if (!event.body) {
      throw new Error('Missing event body in AirOps webhook event');
    }

    const payload = JSON.parse(event.body) as AirOpsWebhookPayload;
    logger.info('AirOps webhook payload', { payload });

    const { id: executionId } = payload;

    if (!executionId) {
      throw new Error('Missing executionId  in AirOps webhook payload');
    }

    const jobService = createJobService(logger);

    const { AIR_OPS_API_KEY, AIR_OPS_API_URL, AIR_OPS_WORKFLOW_ID } = config;

    const airOpsClient = new AirOpsClient(
      AIR_OPS_API_URL,
      AIR_OPS_WORKFLOW_ID,
      AIR_OPS_API_KEY,
      logger,
    );

    const executionData = await airOpsClient.getExecutionData(executionId);

    const {
      job_id: jobId,
      business_name: businessName,
      agent_s_website_url: websiteUrl,
      environment,
    } = executionData.inputs;

    const jobInfo = await jobService.getJobInfo(jobId);

    if (!jobInfo) {
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'No more retries available' }),
      };
    }

    const { retryStep, companyId } = jobInfo;
    const client = new LambdaClient({ region: process.env.REGION });

    logger.info('Invoking generatePost lambda with retry step', {
      jobId,
      companyId,
      businessName,
      retryStep,
    });

    const command = new InvokeCommand({
      FunctionName: config.GENERATE_POST_FUNCTION_NAME,
      Payload: JSON.stringify([
        { jobId, businessName, websiteUrl, retryStep, environment, companyId },
      ]),
      InvocationType: 'Event',
    });

    try {
      // Necessary for sls invoke local to work
      if (config.ENVIRONMENT === 'local') {
        logger.info(
          'Skipping invocation of generatePost lambda in development environment',
        );
      } else {
        await client.send(command);
      }

      return {
        statusCode: 202,
        body: JSON.stringify({
          message: `Post retry ${retryStep} regeneration initiated`,
        }),
      };
    } catch (error) {
      logger.error(
        'Error calling generatePost lambda to re-generate post in AirOps webhook',
        {
          error,
        },
      );
      throw error;
    }
  } catch (error) {
    throw createHandlerError({ error, context, logger, event });
  }
};
