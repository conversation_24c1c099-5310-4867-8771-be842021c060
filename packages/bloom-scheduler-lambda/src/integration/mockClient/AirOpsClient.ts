import { asyncWrapper } from 'src/integration/helpers';
import { PostDTO, PostResult } from 'src/types/post';
import { v4 as uuidv4 } from 'uuid';

import { PostBehavior } from '.';

export class MockAirOpsClient {
  private posts: Map<string, PostDTO> = new Map();
  createPosts: (posts: PostDTO[]) => Promise<PostResult>;
  getExecutionData: (executionId: string) => Promise<PostDTO>;

  constructor() {
    this.createPosts = this.createPostsSuccess;
    this.getExecutionData = this.getExecutionDataSuccess;
  }

  async createPostsSuccess(posts: PostDTO[]): Promise<PostResult> {
    return asyncWrapper(() => {
      const executionId = uuidv4();
      posts.forEach(p => this.posts.set(executionId, p));
      return {
        successfulPosts: posts,
        failedPosts: [],
      };
    });
  }

  async createPostsFailure(posts: PostDTO[]): Promise<PostResult> {
    return asyncWrapper(() => {
      const executionId = uuidv4();
      posts.forEach(p => this.posts.set(executionId, p));
      return {
        successfulPosts: [],
        failedPosts: posts.map(p => ({ post: p, error: new Error('Failed') })),
      };
    });
  }

  async createPostsPartialSuccess(posts: PostDTO[]): Promise<PostResult> {
    return asyncWrapper(() => {
      const executionId = uuidv4();
      posts.forEach(p => this.posts.set(executionId, p));
      return {
        successfulPosts: posts.slice(0, 1),
        failedPosts: posts
          .slice(1)
          .map(p => ({ post: p, error: new Error('Failed') })),
      };
    });
  }

  async getExecutionDataSuccess(executionId: string): Promise<PostDTO> {
    return asyncWrapper(() => {
      return this.posts.get(executionId) as PostDTO;
    });
  }

  async getExecutionDataFailure(executionId: string): Promise<PostDTO> {
    return asyncWrapper(
      () => {
        return this.posts.get(executionId) as PostDTO;
      },
      { shouldError: true, message: 'Failed to get jobs' },
    );
  }

  setCreatePostsBehavior(behavior: PostBehavior) {
    switch (behavior) {
      case PostBehavior.SUCCESS:
        this.createPosts = this.createPostsSuccess;
        break;
      case PostBehavior.PARTIAL_SUCCESS:
        this.createPosts = this.createPostsPartialSuccess;
        break;
      case PostBehavior.FAILURE:
        this.createPosts = this.createPostsFailure;
        break;
    }
  }

  setGetExecutionDataBehavior(shouldFail: boolean) {
    this.getExecutionData = shouldFail
      ? this.getExecutionDataFailure
      : this.getExecutionDataSuccess;
  }
}
