import { asyncWrapper } from 'src/integration/helpers';
import { Brand } from 'src/types/brand';

export class MockApiGatewayClient {
  private brands: Map<string, Brand> = new Map();

  getCompanyDisplayNames: (companyIds: string[]) => Promise<Brand[]>;

  constructor() {
    this.getCompanyDisplayNames = this.getCompanyDisplayNamesSuccess;
  }

  async getCompanyDisplayNamesSuccess(companyIds: string[]): Promise<Brand[]> {
    return asyncWrapper(() => {
      return Array.from(this.brands.values()).filter(b =>
        companyIds.includes(b.companyId),
      );
    });
  }

  async getCompanyDisplayNamesFailure(companyIds: string[]): Promise<Brand[]> {
    return asyncWrapper(
      () => {
        return Array.from(this.brands.values()).filter(b =>
          companyIds.includes(b.companyId),
        );
      },
      { shouldError: true, message: 'Failed to get company display names' },
    );
  }

  setGetCompanyDisplayNamesBehavior(shouldFail: boolean) {
    this.getCompanyDisplayNames = shouldFail
      ? this.getCompanyDisplayNamesFailure
      : this.getCompanyDisplayNamesSuccess;
  }
}
