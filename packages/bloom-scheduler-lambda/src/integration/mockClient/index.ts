import { MockAirOpsClient } from './AirOpsClient';
import { MockApiGatewayClient } from './ApiGatewayClient';
import { MockCMSClient } from './CMSClient';
import { MockTenantClient } from './TenantClient';

export enum PostBehavior {
  SUCCESS = 'SUCCESS',
  PARTIAL_SUCCESS = 'PARTIAL_SUCCESS',
  FAILURE = 'FAILURE',
}

export class MockClient {
  public airOpsClient: MockAirOpsClient;
  public cmsClient: MockCMSClient;
  public tenantClient: MockTenantClient;
  public apiGatewayClient: MockApiGatewayClient;

  constructor() {
    this.airOpsClient = new MockAirOpsClient();
    this.cmsClient = new MockCMSClient();
    this.tenantClient = new MockTenantClient();
    this.apiGatewayClient = new MockApiGatewayClient();
  }

  setCreatePostsBehavior(behavior: PostBehavior) {
    this.airOpsClient.setCreatePostsBehavior(behavior);
  }

  setGetExecutionDataBehavior(shouldFail: boolean) {
    this.airOpsClient.setGetExecutionDataBehavior(shouldFail);
  }

  setGetJobBehavior(shouldFail: boolean) {
    this.cmsClient.setGetJobBehavior(shouldFail);
  }

  setGetAllEntitlementsBehavior(shouldFail: boolean) {
    this.tenantClient.setGetAllEntitlementsBehavior(shouldFail);
  }

  setGetCompanyDisplayNamesBehavior(shouldFail: boolean) {
    this.apiGatewayClient.setGetCompanyDisplayNamesBehavior(shouldFail);
  }
}
