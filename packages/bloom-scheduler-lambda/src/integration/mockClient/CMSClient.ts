import { asyncWrapper } from 'src/integration/helpers';
import { JobDTO, JobPostDTO } from 'src/types/job';

export class MockCMSClient {
  private jobs: Map<string, JobDTO> = new Map();
  getJob: (jobId: string) => Promise<JobDTO[]>;

  constructor() {
    // private readonly logger?: Logger, // private readonly baseUrl?: string,
    this.getJob = this.getJobSuccess;
  }

  async createJobs(jobs: JobPostDTO[]): Promise<JobDTO[]> {
    return await asyncWrapper(() => {
      jobs.forEach(j =>
        this.jobs.set(j.jobId, {
          ...j,
          createdAt: new Date().toISOString(),
        }),
      );
      return Array.from(this.jobs.values());
    });
  }

  async getJobSuccess(jobId: string): Promise<JobDTO[]> {
    return asyncWrapper(() => {
      return Array.from(this.jobs.values()).filter(job => job.jobId === jobId);
    });
  }

  async getJobFailure(jobId: string): Promise<JobDTO[]> {
    return asyncWrapper(
      () => {
        return Array.from(this.jobs.values()).filter(
          job => job.jobId === jobId,
        );
      },
      { shouldError: true, message: 'Failed to get jobs' },
    );
  }

  setGetJobBehavior(shouldFail: boolean) {
    this.getJob = shouldFail ? this.getJobFailure : this.getJobSuccess;
  }
}
