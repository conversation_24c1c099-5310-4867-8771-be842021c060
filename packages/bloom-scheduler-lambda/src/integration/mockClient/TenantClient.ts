import { asyncWrapper } from 'src/integration/helpers';
import { EntitlementDTO } from 'src/types/entitlement';

export class MockTenantClient {
  private entitlements: Map<string, EntitlementDTO> = new Map();
  getAllEntitlements: () => Promise<EntitlementDTO[]>;

  constructor() {
    this.getAllEntitlements = this.getAllEntitlementsSuccess;
  }

  async getAllEntitlementsSuccess(): Promise<EntitlementDTO[]> {
    return asyncWrapper(() => {
      return Array.from(this.entitlements.values());
    });
  }

  async getAllEntitlementsFailure(): Promise<EntitlementDTO[]> {
    return asyncWrapper(
      () => {
        return Array.from(this.entitlements.values());
      },
      { shouldError: true, message: 'Failed to get entitlements' },
    );
  }

  setGetAllEntitlementsBehavior(shouldFail: boolean) {
    this.getAllEntitlements = shouldFail
      ? this.getAllEntitlementsFailure
      : this.getAllEntitlementsSuccess;
  }
}
