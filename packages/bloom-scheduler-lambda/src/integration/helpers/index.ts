export interface ErrorOption {
  shouldError: boolean;
  message?: string;
}

export const asyncWrapper = <T>(
  operation: () => T | Promise<T>,
  { shouldError, message }: ErrorOption = { shouldError: false },
): Promise<T> => {
  return new Promise((resolve, reject) => {
    if (shouldError) {
      reject(new Error(message || 'Error occurred'));
      return;
    }
    try {
      const result = operation();
      Promise.resolve(result).then(resolve).catch(reject);
    } catch (error) {
      reject(error);
    }
  });
};
