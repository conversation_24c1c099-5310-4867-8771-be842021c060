import './instrumentation';
import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import {
  GoogleGenAI,
  PersonGeneration,
  SafetyFilterLevel,
} from '@google/genai';
import { LangfuseClient } from '@langfuse/client';
import { observeOpenAI } from '@langfuse/openai';
import { startActiveObservation, updateActiveTrace } from '@langfuse/tracing';
import { Handler } from 'aws-lambda';
import OpenAI from 'openai';
import { CMSClient } from 'src/clients';
import { createContextLogger } from 'src/utils';

import { BlogPostImageGeneratorError } from '../../errors/blog-post-writer';

export interface BlogPostImageGeneratorEvent {
  companyId: string;
  topicId: string;
  s3Location: string;
  dryRun: boolean;
  executionFolderName: string;
  executionId?: string;
}

export interface BlogPostImageGeneratorResult {
  companyId: string;
  topicId: string;
  s3Location: string;
  mediaId: string;
  imageUrl: string;
}

const imagenSettings = {
  model: 'imagen-4.0-generate-001',
  config: {
    numberOfImages: 1,
    personGeneration: PersonGeneration.DONT_ALLOW,
    aspectRatio: '16:9',
    sampleImageSize: '2K',
    safetyFilterLevel: SafetyFilterLevel.BLOCK_LOW_AND_ABOVE,
    outputMimeType: 'image/jpeg',
    outputCompressionQuality: 95,
  },
};

const s3Client = new S3Client({});

export const handler: Handler<
  BlogPostImageGeneratorEvent,
  BlogPostImageGeneratorResult
> = async event => {
  const logger = createContextLogger('blog-post-image-generator');
  const cmsClient = new CMSClient(process.env.CMS_SERVICE_URL!, logger);

  try {
    logger.info(
      'Generating blog post image:',
      event as unknown as Record<string, unknown>,
    );

    const { companyId, topicId, s3Location, dryRun } = event;

    // Parse S3 location to get bucket and key
    const s3Url = new URL(s3Location);
    const bucketName = s3Url.hostname;
    const s3Key = s3Url.pathname.substring(1); // Remove leading slash

    // Read blog content from S3
    logger.info('Reading blog content from S3...', {
      bucketName,
      s3Key,
      s3Location,
    });
    const getObjectResponse = await s3Client.send(
      new GetObjectCommand({
        Bucket: bucketName,
        Key: s3Key,
      }),
    );
    logger.info('Successfully read from S3', {
      contentLength: getObjectResponse.ContentLength || 0,
    });

    if (!getObjectResponse.Body) {
      throw new BlogPostImageGeneratorError(
        'Failed to read blog content from S3',
        undefined,
        { s3Location },
      );
    }

    const blogPostData = JSON.parse(
      await getObjectResponse.Body.transformToString(),
    );
    logger.info('Read blog content from S3', { title: blogPostData.title });

    if (dryRun) {
      logger.info('Dry run mode - skipping image generation');

      // Update S3 file with mock media data
      const updatedBlogPostData = {
        ...blogPostData,
        mediaId: 'mock-media-id-' + Date.now(),
        imageUrl: 'https://placeholder.com/800x600',
        imageGenerated: true,
        updatedAt: new Date().toISOString(),
      };

      await s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: s3Key,
          Body: JSON.stringify(updatedBlogPostData, null, 2),
          ContentType: 'application/json',
          Metadata: {
            companyId,
            topicId,
            executionFolderName: event.executionFolderName,
          },
        }),
      );

      return {
        companyId,
        topicId,
        s3Location,
        mediaId: updatedBlogPostData.mediaId,
        imageUrl: updatedBlogPostData.imageUrl,
      };
    }

    // Initialize Langfuse client with proper authentication
    logger.info('Initializing Langfuse client with authentication...');
    const langfuseClient = new LangfuseClient({
      secretKey: process.env.LANGFUSE_SECRET_KEY,
      publicKey: process.env.LANGFUSE_PUBLIC_KEY,
      baseUrl: process.env.LANGFUSE_BASE_URL,
    });

    const promptName = 'Image Gen Prompt Generator';

    logger.info('Fetching prompt from Langfuse', { promptName });
    const prompt = await langfuseClient.prompt.get(promptName, {});
    logger.info('Successfully fetched prompt from Langfuse', { promptName });
    const compiledPrompt = prompt.compile({
      title: blogPostData.title,
      article: blogPostData.content,
    });
    logger.info('Compiled prompt successfully', {
      titleLength: blogPostData.title?.length || 0,
      contentLength: blogPostData.content?.length || 0,
      promptLength: compiledPrompt?.length || 0,
    });

    return await startActiveObservation(
      'blog_post_image_generation',
      async () => {
        updateActiveTrace({
          userId: companyId,
          sessionId: event.executionId,
          metadata: {
            companyId,
            topicId,
            title: blogPostData.title,
          },
        });

        logger.info('Initializing OpenAI client...');
        const openai = observeOpenAI(
          new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
          }),
          {
            traceName: 'generate_image_prompt',
            generationMetadata: { companyId, topicId },
            langfusePrompt: prompt,
          },
        );
        logger.info('Calling OpenAI API to generate image prompt...', {
          model: process.env.OPENAI_MODEL_NAME || 'gpt-4o',
          temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0'),
        });
        const { output_text: imagePrompt } = await openai.responses.create({
          model: process.env.OPENAI_MODEL_NAME || 'gpt-4o',
          input: compiledPrompt,
          temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0'),
        });
        logger.info('Successfully generated image prompt', {
          promptLength: imagePrompt?.length || 0,
        });

        return await startActiveObservation('generate_image', async span => {
          span.update({
            input: { prompt: imagePrompt },
            metadata: {
              model: imagenSettings.model,
              aspectRatio: imagenSettings.config.aspectRatio,
            },
          });

          logger.info(
            'Initializing Google GenAI client for image generation...',
          );
          const geminiClient = new GoogleGenAI({
            apiKey: process.env.GEMINI_API_KEY,
          });

          logger.info('Calling Google GenAI API to generate image...', {
            model: imagenSettings.model,
            aspectRatio: imagenSettings.config.aspectRatio,
            promptLength: imagePrompt?.length || 0,
          });
          const response = await geminiClient.models.generateImages({
            ...imagenSettings,
            prompt: imagePrompt,
          });
          logger.info('Successfully generated image', {
            imagesCount: response.generatedImages?.length || 0,
          });

          const [generatedImage] = response.generatedImages!;
          const buffer = Buffer.from(
            generatedImage.image!.imageBytes!,
            'base64',
          );

          logger.info('Uploading image to CMS...', {
            companyId,
            bufferSize: buffer.length,
          });
          const { mediaId, url: imageUrl } = await cmsClient.createMedia(
            companyId,
            buffer,
          );
          logger.info('Successfully uploaded image to CMS', {
            mediaId,
            imageUrl,
          });

          span.update({
            output: 'Image generated successfully',
            metadata: { success: true, mediaId, imageUrl },
          });

          // Update S3 file with media data
          const updatedBlogPostData = {
            ...blogPostData,
            mediaId,
            imageUrl,
            imageGenerated: true,
            updatedAt: new Date().toISOString(),
          };

          logger.info('Updating S3 file with media data...', {
            bucketName,
            s3Key,
            mediaId,
            imageUrl,
          });
          await s3Client.send(
            new PutObjectCommand({
              Bucket: bucketName,
              Key: s3Key,
              Body: JSON.stringify(updatedBlogPostData, null, 2),
              ContentType: 'application/json',
              Metadata: {
                companyId,
                topicId,
                executionFolderName: event.executionFolderName,
              },
            }),
          );

          logger.info('Updated S3 file with media data', {
            s3Location,
            mediaId,
            imageUrl,
          });

          return {
            companyId,
            topicId,
            s3Location,
            mediaId,
            imageUrl,
          };
        });
      },
    );
  } catch (error) {
    if (error instanceof BlogPostImageGeneratorError) {
      throw error;
    }

    throw new BlogPostImageGeneratorError(
      'Failed to generate blog post image',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
