import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { Handler } from 'aws-lambda';

import { ResultsWriterError } from '../../errors/blog-post-writer';
import {
  CompanyProcessingResult,
  ExecutionReport,
  SlackMessage,
} from '../../types/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

const getBucketName = () =>
  process.env.EXECUTION_BUCKET_NAME ||
  `lp-bloom-scheduler-execution-${process.env.ENVIRONMENT || 'development'}`;

// Interface for Step Functions Map state manifest
interface MapStateManifest {
  DestinationBucket?: string;
  MapRunArn?: string;
  ResultFiles?: {
    SUCCEEDED?: Array<{
      Key: string;
      Size?: number;
    }>;
    FAILED?: Array<{
      Key?: string;
      Size?: number;
    }>;
    PENDING?: Array<{
      Key?: string;
      Size?: number;
    }>;
  };
}

export interface ResultWriterDetails {
  Bucket: string;
  Key: string;
}

export interface MapResultsWithWriter {
  ResultWriterDetails: ResultWriterDetails;
}

// Helper function to retry S3 operations with exponential backoff
async function retryS3Operation<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3,
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: unknown) {
      lastError = error;

      // Don't retry on certain error codes
      if (
        error &&
        typeof error === 'object' &&
        'Code' in error &&
        (error.Code === 'NoSuchKey' || error.Code === 'NoSuchBucket')
      ) {
        throw error;
      }

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
        const errorMessage =
          error instanceof Error
            ? error.message
            : typeof error === 'string'
              ? error
              : String(error);
        console.log(
          `${operationName} failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`,
          errorMessage,
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`${operationName} failed after ${maxRetries} attempts`);
  throw lastError;
}

async function readResultsFromS3(
  s3Client: S3Client,
  bucket: string,
  prefixOrKey: string,
): Promise<CompanyProcessingResult[]> {
  console.log(`Reading Map results from S3: s3://${bucket}/${prefixOrKey}`);

  try {
    // Check if we were given a direct path to manifest.json or a prefix
    if (prefixOrKey.includes('manifest.json')) {
      // We have a direct path to manifest.json, read it directly
      console.log(`Direct manifest path provided: ${prefixOrKey}`);

      // Retry reading the manifest in case of timing issues
      const manifestContent = await retryS3Operation(async () => {
        const manifestCommand = new GetObjectCommand({
          Bucket: bucket,
          Key: prefixOrKey,
        });
        const manifestResponse = await s3Client.send(manifestCommand);
        return await manifestResponse.Body?.transformToString();
      }, `Reading manifest from ${prefixOrKey}`);

      if (manifestContent) {
        const manifest: MapStateManifest = JSON.parse(manifestContent);
        console.log('Found manifest.json:', JSON.stringify(manifest, null, 2));

        return await processManifestResults(s3Client, manifest, bucket);
      }
    }

    // Original logic for when we have a prefix (folder path)
    const prefix = prefixOrKey;

    // Step Functions Map ResultWriter creates a UUID subfolder under the prefix
    // We need to list the contents to find the UUID folder, then read manifest.json from it
    const listCommand = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: prefix.endsWith('/') ? prefix : `${prefix}/`,
      Delimiter: '/',
    });

    console.log(
      'ListObjectsV2 command:',
      JSON.stringify(
        {
          Bucket: bucket,
          Prefix: prefix.endsWith('/') ? prefix : `${prefix}/`,
          Delimiter: '/',
        },
        null,
        2,
      ),
    );

    const listResponse = await retryS3Operation(
      () => s3Client.send(listCommand),
      'ListObjectsV2',
    );

    console.log(
      'ListObjectsV2 response:',
      JSON.stringify(
        {
          CommonPrefixes: listResponse.CommonPrefixes,
          Contents: listResponse.Contents?.map(c => ({
            Key: c.Key,
            Size: c.Size,
          })),
          KeyCount: listResponse.KeyCount,
          IsTruncated: listResponse.IsTruncated,
        },
        null,
        2,
      ),
    );

    // Find the UUID subfolder (it will be in CommonPrefixes)
    if (listResponse.CommonPrefixes && listResponse.CommonPrefixes.length > 0) {
      // Get the first subfolder (should be the UUID folder)
      const uuidPrefix = listResponse.CommonPrefixes[0].Prefix;
      console.log(`Found ResultWriter UUID folder: ${uuidPrefix}`);

      // Now read the manifest from the UUID folder
      const manifestKey = `${uuidPrefix}manifest.json`;
      console.log(`Reading manifest from: ${manifestKey}`);

      // Retry reading the manifest in case ResultWriter just finished
      const manifestContent = await retryS3Operation(async () => {
        const manifestCommand = new GetObjectCommand({
          Bucket: bucket,
          Key: manifestKey,
        });
        const manifestResponse = await s3Client.send(manifestCommand);
        return await manifestResponse.Body?.transformToString();
      }, `Reading manifest from ${manifestKey}`);

      if (manifestContent) {
        const manifest: MapStateManifest = JSON.parse(manifestContent);
        console.log('Found manifest.json:', JSON.stringify(manifest, null, 2));

        return await processManifestResults(s3Client, manifest, bucket);
      }
    } else {
      console.log(
        'No UUID folder found in ResultWriter output, falling back to direct listing',
      );
    }

    // Fallback: list all files with the prefix if no manifest found
    return await fallbackDirectFileRead(s3Client, bucket, prefix);
  } catch (error) {
    console.error('Error reading results from S3:', error);
    throw new ResultsWriterError(
      `Failed to read results from S3: s3://${bucket}/${prefixOrKey}`,
      error instanceof Error ? error : new Error(String(error)),
      { bucket, prefixOrKey },
    );
  }
}

async function processManifestResults(
  s3Client: S3Client,
  manifest: MapStateManifest,
  bucket: string,
): Promise<CompanyProcessingResult[]> {
  const results: CompanyProcessingResult[] = [];

  // Check if manifest has the expected structure
  if (manifest.ResultFiles) {
    console.log(
      'Manifest ResultFiles - SUCCEEDED:',
      manifest.ResultFiles.SUCCEEDED?.length || 0,
      'FAILED:',
      manifest.ResultFiles.FAILED?.length || 0,
      'PENDING:',
      manifest.ResultFiles.PENDING?.length || 0,
    );

    // Process succeeded results
    if (
      manifest.ResultFiles.SUCCEEDED &&
      Array.isArray(manifest.ResultFiles.SUCCEEDED)
    ) {
      console.log(
        `Processing ${manifest.ResultFiles.SUCCEEDED.length} succeeded results from manifest`,
      );

      for (const succeededItem of manifest.ResultFiles.SUCCEEDED) {
        if (succeededItem.Key) {
          try {
            console.log(
              `Reading succeeded result file: ${succeededItem.Key} (Size: ${succeededItem.Size})`,
            );

            // Use the destination bucket from manifest if available
            const resultBucket = manifest.DestinationBucket || bucket;

            // Retry reading the SUCCEEDED file
            const resultContent = await retryS3Operation(async () => {
              const resultCommand = new GetObjectCommand({
                Bucket: resultBucket,
                Key: succeededItem.Key,
              });
              const resultResponse = await s3Client.send(resultCommand);
              return await resultResponse.Body?.transformToString();
            }, `Reading SUCCEEDED file ${succeededItem.Key}`);

            if (resultContent) {
              const parsedResults = JSON.parse(resultContent);

              if (Array.isArray(parsedResults)) {
                console.log(
                  `Found ${parsedResults.length} results in ${succeededItem.Key}`,
                );

                parsedResults.forEach(executionDetail => {
                  // The actual output is in the "Output" field as a stringified JSON
                  if (executionDetail.Output) {
                    try {
                      const result = JSON.parse(executionDetail.Output);
                      console.log(
                        `  - companyId: ${result.companyId}, published: ${result.published}`,
                      );
                      results.push(result);
                    } catch (parseError) {
                      console.error(
                        `Error parsing Output field for execution ${executionDetail.ExecutionArn}:`,
                        parseError,
                      );
                    }
                  } else {
                    console.warn(
                      `No Output field found for execution ${executionDetail.ExecutionArn}`,
                    );
                  }
                });
              } else {
                // Single result - also needs Output field parsing
                if (parsedResults.Output) {
                  try {
                    const result = JSON.parse(parsedResults.Output);
                    console.log(
                      `Single result from ${succeededItem.Key}: companyId=${result.companyId}`,
                    );
                    results.push(result);
                  } catch (parseError) {
                    console.error(`Error parsing Output field:`, parseError);
                  }
                }
              }
            }
          } catch (error) {
            console.error(
              `Error reading succeeded result ${succeededItem.Key}:`,
              error,
            );
          }
        }
      }
    }

    // Process failed results
    if (
      manifest.ResultFiles.FAILED &&
      Array.isArray(manifest.ResultFiles.FAILED)
    ) {
      console.log(
        `Processing ${manifest.ResultFiles.FAILED.length} failed results from manifest`,
      );

      for (const failedItem of manifest.ResultFiles.FAILED) {
        if (failedItem.Key) {
          try {
            const resultBucket = manifest.DestinationBucket || bucket;
            const failedCommand = new GetObjectCommand({
              Bucket: resultBucket,
              Key: failedItem.Key,
            });

            const failedResponse = await s3Client.send(failedCommand);
            const failedContent =
              await failedResponse.Body?.transformToString();

            if (failedContent) {
              const failedData = JSON.parse(failedContent);
              console.log(
                `Found failed results in ${failedItem.Key}`,
                failedData,
              );

              const failedResults = Array.isArray(failedData)
                ? failedData
                : [failedData];

              const processedFailedResults = failedResults.map(item => ({
                companyId: item.companyId || item.Input?.companyId || 'unknown',
                topicId: item.topicId || item.Input?.topicId,
                failed: true,
                error: item.Error ||
                  item.error || {
                    message:
                      item.ErrorMessage ||
                      item.Cause ||
                      'Step Functions execution failed',
                  },
              }));

              results.push(...processedFailedResults);
              console.log(
                `Added ${processedFailedResults.length} failed results from ${failedItem.Key}`,
              );
            }
          } catch (error) {
            console.error(
              `Error reading failed result ${failedItem.Key}:`,
              error,
            );
          }
        }
      }
    }
  }

  console.log(
    `Successfully processed manifest, found ${results.length} total results`,
  );
  return results;
}

async function fallbackDirectFileRead(
  s3Client: S3Client,
  bucket: string,
  prefix: string,
): Promise<CompanyProcessingResult[]> {
  const fallbackListCommand = new ListObjectsV2Command({
    Bucket: bucket,
    Prefix: prefix,
    MaxKeys: 1000,
  });

  const fallbackListResponse = await s3Client.send(fallbackListCommand);
  const results: CompanyProcessingResult[] = [];

  if (
    !fallbackListResponse.Contents ||
    fallbackListResponse.Contents.length === 0
  ) {
    console.log('No result files found in S3 with prefix:', prefix);
    return results;
  }

  console.log(
    `Found ${fallbackListResponse.Contents.length} files in S3 with prefix ${prefix}`,
  );

  // Log all file names to debug
  fallbackListResponse.Contents.forEach(obj => {
    console.log(`  - ${obj.Key}`);
  });

  for (const object of fallbackListResponse.Contents) {
    // Skip directories, manifest files, and the prefix itself
    if (
      !object.Key ||
      object.Key === prefix ||
      object.Key.endsWith('/') ||
      object.Key.endsWith('manifest.json')
    ) {
      console.log(`Skipping: ${object.Key}`);
      continue;
    }

    try {
      console.log(`Reading result file: ${object.Key}`);
      const getCommand = new GetObjectCommand({
        Bucket: bucket,
        Key: object.Key,
      });

      const response = await s3Client.send(getCommand);
      const bodyContents = await response.Body?.transformToString();

      if (bodyContents) {
        const parsedResult = JSON.parse(bodyContents);

        // Check if this is a Step Functions execution result or direct output
        if (Array.isArray(parsedResult)) {
          console.log(`  Found array with ${parsedResult.length} results`);

          // Check if array contains execution details or direct results
          parsedResult.forEach(item => {
            if (item.Output && typeof item.Output === 'string') {
              // This is a Step Functions execution detail, parse the Output field
              try {
                const result = JSON.parse(item.Output);
                console.log(
                  `    - companyId: ${result.companyId}, published: ${result.published}`,
                );
                results.push(result);
              } catch (parseError) {
                console.error(`    Error parsing Output field:`, parseError);
              }
            } else if (item.companyId) {
              // This is already a parsed result
              console.log(
                `    - companyId: ${item.companyId}, published: ${item.published}`,
              );
              results.push(item);
            }
          });
        } else {
          // Single result - check if it's execution detail or direct result
          if (parsedResult.Output && typeof parsedResult.Output === 'string') {
            // Step Functions execution detail
            try {
              const result = JSON.parse(parsedResult.Output);
              console.log(
                `  Found single result: companyId=${result.companyId}, published=${result.published}`,
              );
              results.push(result);
            } catch (parseError) {
              console.error(`  Error parsing Output field:`, parseError);
            }
          } else if (parsedResult.companyId) {
            // Direct result
            console.log(
              `  Found single result: companyId=${parsedResult.companyId}, published=${parsedResult.published}`,
            );
            results.push(parsedResult);
          }
        }
      }
    } catch (error) {
      console.error(`Error reading file ${object.Key}:`, error);
    }
  }

  console.log(`Successfully read ${results.length} results from S3 files`);
  return results;
}

export interface ResultsWriterEvent {
  executionName?: string;
  executionStartTime?: string;
  executionArn?: string;
  dryRun: boolean;
  publishToCMS?: boolean;
  results: CompanyProcessingResult[] | MapResultsWithWriter;
}

export interface ResultsWriterResult {
  executionName?: string;
  executionStartTime?: string;
  summary: {
    totalCompanies: number;
    successfulPosts: number;
    failedPosts: number;
    skippedCompanies: number;
    cmsPublishedPosts: number; // Number of posts actually published to CMS
  };
  reportLocation: string;
  notificationSent: boolean;
}

export const handler: Handler<
  ResultsWriterEvent,
  ResultsWriterResult
> = async event => {
  const logger = createContextLogger('results-writer');

  try {
    // Create S3Client inside handler for better testability
    const s3Client = new S3Client({});
    logger.info('Results writer Lambda invoked', {
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
      dryRun: event.dryRun,
      resultsType: Array.isArray(event.results)
        ? 'array'
        : typeof event.results,
      resultsCount: Array.isArray(event.results) ? event.results.length : 'N/A',
      hasResultWriterDetails:
        event.results && 'ResultWriterDetails' in event.results,
    });

    const {
      executionName,
      executionStartTime,
      executionArn,
      dryRun,
      publishToCMS = false, // Default to false since state machine only publishes when publishToCMS === true
    } = event;

    const timestamp = executionStartTime || new Date().toISOString();
    const executionDate = timestamp.split('T')[0];
    const executionId = executionName || `manual-${Date.now()}`;
    const executionFolderName = `${executionId}-${timestamp.replace(/[:.]/g, '-')}`;

    // Handle both direct results array and S3 ResultWriter pattern
    let resultsArray: CompanyProcessingResult[] = [];

    if (Array.isArray(event.results)) {
      resultsArray = event.results;
      console.log(`Received ${resultsArray.length} direct results`);
    } else if (event.results && typeof event.results === 'object') {
      if ('ResultWriterDetails' in event.results) {
        const details = event.results.ResultWriterDetails;
        console.log(
          `Results written to S3: s3://${details.Bucket}/${details.Key}`,
        );

        if (details.Bucket && details.Key) {
          resultsArray = await readResultsFromS3(
            s3Client,
            details.Bucket,
            details.Key,
          );
        } else {
          console.log(
            'No S3 location provided in ResultWriterDetails, assuming empty results',
          );
          resultsArray = [];
        }
      } else {
        // Handle other object structures (fallback)
        console.log('Attempting to extract results from object structure');
        resultsArray = Object.values(event.results);
      }
    }

    console.log(`Total results to process: ${resultsArray.length}`);

    // Process results from the distributed map
    const processedResults = processResults(resultsArray);

    // Generate execution report
    const report: ExecutionReport = {
      executionName,
      executionStartTime: timestamp,
      executionArn,
      dryRun,
      publishToCMS,
      s3FolderPath: `blog-post-jobs/${executionDate}/${executionFolderName}`,
      summary: {
        totalCompanies: processedResults.totalCompanies,
        successfulPosts: processedResults.successfulPosts,
        failedPosts: processedResults.failedPosts,
        skippedCompanies: processedResults.skippedCompanies,
        cmsPublishedPosts: processedResults.cmsPublishedPosts,
        duration: calculateDuration(timestamp),
      },
      companies: processedResults.companies,
      errors: processedResults.errors,
      completedAt: new Date().toISOString(),
    };

    // Save report to S3
    const reportKey = `blog-post-jobs/${executionDate}/${executionFolderName}/execution-report.json`;

    const bucketName = getBucketName();
    await s3Client.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: reportKey,
        Body: JSON.stringify(report, null, 2),
        ContentType: 'application/json',
        Metadata: {
          executionName: executionName || '',
          executionArn: executionArn || '',
          dryRun: String(dryRun),
        },
      }),
    );

    console.log(`Saved execution report to S3: ${reportKey}`);

    const notificationSent = await sendSlackNotification(report);

    return {
      executionName,
      executionStartTime: timestamp,
      summary: {
        totalCompanies: processedResults.totalCompanies,
        successfulPosts: processedResults.successfulPosts,
        failedPosts: processedResults.failedPosts,
        skippedCompanies: processedResults.skippedCompanies,
        cmsPublishedPosts: processedResults.cmsPublishedPosts,
      },
      reportLocation: `s3://${bucketName}/${reportKey}`,
      notificationSent,
    };
  } catch (error) {
    if (error instanceof ResultsWriterError) {
      throw error;
    }

    throw new ResultsWriterError(
      'Failed to write execution results',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};

// Helper function to process results from distributed map
function processResults(results: CompanyProcessingResult[]) {
  const companies: CompanyProcessingResult[] = [];
  const errors: Array<{ companyId: string; error: unknown }> = [];
  let successfulPosts = 0;
  let cmsPublishedPosts = 0;
  let failedPosts = 0;
  let skippedCompanies = 0;

  for (const result of results) {
    if (result.error || result.failed) {
      errors.push({
        companyId: result.companyId,
        error: result.error || result.reason || 'Processing failed',
      });
      failedPosts++;
    } else if (result.skipped) {
      skippedCompanies++;
    } else if (result.published) {
      successfulPosts++;

      // If published=true, it means successfully published to CMS
      // (dry run always sets published=false)
      cmsPublishedPosts++;

      companies.push({
        companyId: result.companyId,
        topicId: result.topicId,
        createdAt: result.createdAt,
        s3Location: result.s3Location,
        cmsPostId: result.cmsPostId,
      });
    }
  }

  return {
    totalCompanies: results.length,
    successfulPosts,
    failedPosts,
    skippedCompanies,
    cmsPublishedPosts,
    companies,
    errors,
  };
}

// Helper function to calculate execution duration
function calculateDuration(startTime: string): string {
  const start = new Date(startTime).getTime();
  const end = Date.now();
  const durationMs = end - start;

  const hours = Math.floor(durationMs / (1000 * 60 * 60));
  const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);

  return `${hours}h ${minutes}m ${seconds}s`;
}

// Helper function to generate AWS Step Functions execution URL
function getExecutionUrl(executionArn?: string): string | null {
  if (!executionArn) return null;

  // Extract region from ARN
  // ARN format: arn:aws:states:REGION:ACCOUNT:execution:STATE_MACHINE:EXECUTION_ID
  const arnParts = executionArn.split(':');
  if (arnParts.length < 6) return null;

  const region = arnParts[3];

  return `https://${region}.console.aws.amazon.com/states/home?region=${region}#/executions/details/${encodeURIComponent(executionArn)}`;
}

// Helper function to generate S3 console URL for folder
function getS3FolderUrl(
  bucketName: string,
  folderPath: string,
  region: string = 'us-east-1',
): string {
  // Ensure folderPath ends with / for folder view
  const normalizedPath = folderPath.endsWith('/')
    ? folderPath
    : `${folderPath}/`;
  const encodedPath = encodeURIComponent(normalizedPath);
  return `https://s3.console.aws.amazon.com/s3/buckets/${bucketName}?region=${region}&prefix=${encodedPath}`;
}

// Helper function to send Slack notification
async function sendSlackNotification(
  report: ExecutionReport,
): Promise<boolean> {
  // Check if Slack notifications are enabled
  const slackEnabled =
    process.env.BLOG_V2_SLACK_NOTIFICATIONS_ENABLED === 'true';
  const slackWebhookUrl = process.env.BLOG_V2_SLACK_WEBHOOK_URL;

  if (!slackEnabled || !slackWebhookUrl) {
    console.log('Slack notifications disabled or webhook URL not configured');
    return false;
  }

  try {
    // Generate AWS execution link
    const executionUrl = getExecutionUrl(report.executionArn);
    const executionDisplay =
      executionUrl && report.executionName
        ? `<${executionUrl}|${report.executionName}>`
        : report.executionName || 'Unknown';

    // Generate S3 folder link
    const bucketName = getBucketName();
    const s3FolderUrl = report.s3FolderPath
      ? getS3FolderUrl(bucketName, report.s3FolderPath)
      : null;
    const s3Display = s3FolderUrl
      ? `<${s3FolderUrl}|View S3 Files>`
      : 'S3 files unavailable';

    // Determine publication mode for message
    const publishMode =
      report.publishToCMS === true
        ? report.dryRun
          ? ' (Dry Run)'
          : ''
        : ' (Review Mode)';

    // Create a clearer breakdown of results based on mode
    const getModeSpecificStats = () => {
      if (report.publishToCMS === true && !report.dryRun) {
        // Live publishing mode - show CMS publish count
        return (
          `Posts Generated: ${report.summary.successfulPosts}\n` +
          `❇️ Published to CMS: ${report.summary.cmsPublishedPosts}\n`
        );
      } else if (report.publishToCMS === true && report.dryRun) {
        // Dry run mode - posts generated but not published
        return `Posts Generated: ${report.summary.successfulPosts} (dry run)\n`;
      } else {
        // Review mode - posts generated but not published
        return `Posts Generated: ${report.summary.successfulPosts} (review only)\n`;
      }
    };

    const message: SlackMessage = {
      text: `Blog Post Generation${publishMode} Completed`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text:
              `*Blog Post Generation Report*\n` +
              `Execution: ${executionDisplay}\n` +
              `S3 Folder: ${s3Display}\n` +
              `Mode: ${report.publishToCMS === true ? (report.dryRun ? 'Dry Run' : 'Live') : 'Review Only'}\n` +
              `Total Companies: ${report.summary.totalCompanies}\n` +
              getModeSpecificStats() +
              `Failed: ${report.summary.failedPosts}\n` +
              `Skipped: ${report.summary.skippedCompanies}\n` +
              `Duration: ${report.summary.duration}`,
          },
        },
      ],
    };

    console.log('Slack message:', JSON.stringify(message, null, 2));

    // Send POST request to Slack webhook
    const response = await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      throw new Error(
        `Slack webhook failed: ${response.status} ${response.statusText}`,
      );
    }

    console.log('Slack notification sent successfully');
    return true;
  } catch (error) {
    console.error('Failed to send Slack notification:', error);
    return false;
  }
}
