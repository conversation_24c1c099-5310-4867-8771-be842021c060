import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { <PERSON><PERSON> } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';

import { ApiGatewayClient } from '../../clients/ApiGatewayClient';
import { TenantClient } from '../../clients/TenantClient';
import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  FetchEntitlementsError,
  TransientFetchEntitlementsError,
} from '../../errors/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';
import { LaunchDarkly } from '../../utils/LaunchDarkly';

const s3Client = new S3Client({});
const BUCKET_NAME =
  process.env.EXECUTION_BUCKET_NAME ||
  `lp-bloom-scheduler-execution-${process.env.ENVIRONMENT || 'development'}`;
const SUPER_BLOOM_FLAG =
  process.env.SUPER_BLOOM_LD_FLAG || 'enable-super-bloom';

// Helper function to generate S3 console URL for folder
function getS3FolderUrl(
  bucketName: string,
  folderPath: string,
  region: string = 'us-east-1',
): string {
  // Ensure folderPath ends with / for folder view
  const normalizedPath = folderPath.endsWith('/')
    ? folderPath
    : `${folderPath}/`;
  const encodedPath = encodeURIComponent(normalizedPath);
  return `https://s3.console.aws.amazon.com/s3/buckets/${bucketName}?region=${region}&prefix=${encodedPath}`;
}

// Helper function to get next available publish date (max 6 days out)
// Returns null if company is not entitled to post on the next occurrence
export const getNextPublishDate = (
  units: number,
  publishDayOfWeek: string = 'TUESDAY',
  currentDate?: Date,
): string | null => {
  const today = currentDate || new Date();
  const targetDay = getDayOfWeekNumber(publishDayOfWeek);

  // Find the next occurrence of the target day (within next 6 days)
  const nextOccurrence = getNextWeeklyOccurrence(today, targetDay);

  if (!nextOccurrence) {
    return null; // No occurrence within 6 days
  }

  // Check if the company is entitled to post on this specific occurrence based on units
  if (isCompanyEntitledForDate(units, nextOccurrence)) {
    return nextOccurrence.toISOString().split('T')[0];
  }

  return null; // Company not entitled for this week
};

// Helper function to find the next weekly occurrence
// If today is the POST_PUBLISH_DAY_OF_WEEK, look to next week (7 days out)
// Otherwise, look to the next occurrence (max 6 days out)
const getNextWeeklyOccurrence = (
  today: Date,
  targetDay: number,
): Date | null => {
  const todayDay = today.getDay();
  let daysUntilTarget = (targetDay - todayDay + 7) % 7;

  // If today is the target day, schedule for next week to allow for review time
  if (daysUntilTarget === 0) {
    daysUntilTarget = 7;
  }

  // Allow up to 7 days out (to handle running on POST_PUBLISH_DAY_OF_WEEK)
  if (daysUntilTarget > 7) {
    return null;
  }

  const nextDate = new Date(today);
  nextDate.setDate(today.getDate() + daysUntilTarget);
  return nextDate;
};

// Helper function to check if company is entitled for a specific date based on units
const isCompanyEntitledForDate = (
  units: number,
  publishDate: Date,
): boolean => {
  // Get all occurrences of the target day in the publish date's month
  const month = publishDate.getMonth();
  const year = publishDate.getFullYear();
  const targetDay = publishDate.getDay();

  const occurrences: Date[] = [];
  const firstDayOfMonth = new Date(year, month, 1);

  // Find the first occurrence of the target day in the month
  const currentDate = new Date(firstDayOfMonth);
  const daysUntilTarget = (targetDay - currentDate.getDay() + 7) % 7;
  currentDate.setDate(currentDate.getDate() + daysUntilTarget);

  // Collect all occurrences of the target day in the month (max 4)
  while (currentDate.getMonth() === month && occurrences.length < 4) {
    occurrences.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 7);
  }

  // Find which occurrence number the publish date is (1st, 2nd, 3rd, or 4th)
  const occurrenceIndex = occurrences.findIndex(
    date => date.getDate() === publishDate.getDate(),
  );

  if (occurrenceIndex === -1) {
    return false; // Should not happen
  }

  const occurrenceNumber = occurrenceIndex + 1;

  // Apply unit-based entitlement rules
  if (units === 2) {
    // 2 units: only 1st and 3rd occurrences
    return occurrenceNumber === 1 || occurrenceNumber === 3;
  } else if (units === 4) {
    // 4 units: 1st, 2nd, 3rd, and 4th occurrences (never 5th)
    return occurrenceNumber >= 1 && occurrenceNumber <= 4;
  } else {
    // Default behavior for other units: weekly (always entitled)
    return true;
  }
};

const getDayOfWeekNumber = (dayName: string): number => {
  const days = {
    SUNDAY: 0,
    MONDAY: 1,
    TUESDAY: 2,
    WEDNESDAY: 3,
    THURSDAY: 4,
    FRIDAY: 5,
    SATURDAY: 6,
  };
  return days[dayName as keyof typeof days] ?? 2; // Default to Tuesday
};

// GraphQL query to check for existing scheduled AI posts
const CHECK_SCHEDULED_AI_POSTS_QUERY = `
  query CheckScheduledAiPosts($companyId: String!, $publishDateGTE: String!, $publishDateLTE: String!) {
    posts(
      companyId: $companyId
      scheduledAtGTE: $publishDateGTE
      scheduledAtLTE: $publishDateLTE
      aiGenerated: true
      limit: 1
    ) {
      items {
        id
        scheduledAt
        aiGenerated
      }
      total
    }
  }
`;

interface CheckScheduledPostsResponse {
  posts: {
    items: Array<{
      id: string;
      scheduledAt: string;
      aiGenerated: boolean;
    }>;
    total: number;
  };
}

interface EntitledCompany {
  companyId: string;
  startDate?: string;
  endDate?: string;
  units: number;
}

// Helper function to validate entitlement dates
const isEntitlementActive = (
  entitlement: EntitledCompany,
  currentDate: Date = new Date(),
): boolean => {
  const currentISOString = currentDate.toISOString();

  // Check start date (if provided)
  if (entitlement.startDate && entitlement.startDate > currentISOString) {
    return false; // Not yet started
  }

  // Check end date (if provided)
  if (entitlement.endDate && entitlement.endDate <= currentISOString) {
    return false; // Already ended
  }

  // If no end date or end date is in future, and start date is in past (or not provided), it's active
  return true;
};

// Helper function to create TenantClient for fetching entitlements
const createTenantClient = (logger: ReturnType<typeof createContextLogger>) => {
  const tenantServiceUrl = process.env.TENANT_SERVICE_URL;
  const entitlementsLimit = process.env.ENTITLEMENTS_LIMIT
    ? parseInt(process.env.ENTITLEMENTS_LIMIT, 10)
    : undefined;
  const productId = process.env.AUTOMATED_BLOGS_PRODUCT_ID;

  if (!tenantServiceUrl) {
    throw new Error('TENANT_SERVICE_URL environment variable is required');
  }
  if (!productId) {
    throw new Error(
      'AUTOMATED_BLOGS_PRODUCT_ID environment variable is required',
    );
  }

  return new TenantClient(
    tenantServiceUrl,
    entitlementsLimit,
    logger,
    productId,
  );
};

// Helper function to fetch active entitled companies using TenantClient
const fetchActiveEntitledCompanies = async (
  logger: ReturnType<typeof createContextLogger>,
  currentDate: string,
): Promise<EntitledCompany[]> => {
  try {
    const tenantClient = createTenantClient(logger);
    const entitlements = await tenantClient.getAllEntitlements();

    // Convert EntitlementDTO[] to EntitledCompany[] and filter for active ones
    const activeEntitlements = entitlements
      .filter(entitlement =>
        isEntitlementActive(
          {
            companyId: entitlement.companyId,
            startDate: entitlement.startDate,
            endDate: entitlement.endDate,
            units: entitlement.units,
          },
          new Date(currentDate),
        ),
      )
      .map(entitlement => ({
        companyId: entitlement.companyId,
        startDate: entitlement.startDate,
        endDate: entitlement.endDate,
        units: entitlement.units,
      }));

    logger.info(
      `Fetched ${activeEntitlements.length}/${entitlements.length} active entitled companies using TenantClient`,
    );
    return activeEntitlements;
  } catch (error) {
    logger.error('Failed to fetch entitlements from tenant service:', error);
    throw error;
  }
};

// Helper function to check if company has existing scheduled AI post using GraphQL
const checkExistingScheduledPost = async (
  companyId: string,
  publishDate: string,
  graphqlClient?: GraphQLClient,
): Promise<boolean> => {
  try {
    console.log(
      `Checking for existing scheduled AI post for company ${companyId} on ${publishDate}`,
    );

    if (!graphqlClient) {
      console.log('No GraphQL client provided, skipping check');
      return false;
    }

    const variables = {
      companyId,
      publishDateGTE: `${publishDate}T00:00:00Z`,
      publishDateLTE: `${publishDate}T23:59:59Z`,
    };

    const response = await graphqlClient.request<CheckScheduledPostsResponse>(
      CHECK_SCHEDULED_AI_POSTS_QUERY,
      variables,
    );

    const hasScheduledPost = response.posts.total > 0;

    if (hasScheduledPost) {
      console.log(
        `Found ${response.posts.total} existing AI-generated post(s) for company ${companyId} on ${publishDate}`,
      );
    }

    return hasScheduledPost;
  } catch (error) {
    console.error(
      `Failed to check existing scheduled post for ${companyId}:`,
      error,
    );
    return false; // Assume no existing post on error
  }
};

export interface FetchEntitlementsEvent {
  companyIds: string[];
  dryRun: boolean;
  publishToCMS?: boolean;
  executionName?: string;
  executionStartTime?: string;
  executionArn?: string;
  skipEntitlementsValidation?: boolean;
  skipUnitsValidation?: boolean;
  skipExistingScheduledPostValidation?: boolean;
}

export interface FetchEntitlementsResult {
  companyIds: string[];
  dryRun: boolean;
  publishToCMS?: boolean;
  executionName?: string;
  executionStartTime?: string;
  executionFolderName: string;
  s3Key: string;
  eligibleCompanies: number;
  totalEntitlements: number;
  skippedCompanies: {
    noEntitlements: number;
    insufficientUnits: number;
    existingScheduledPost: number;
    launchDarklyDisabled: number;
    aiGeneratedPostAlreadyScheduled: number;
  };
}

interface Entitlement {
  companyId: string;
  blogPostLimit: number;
  units?: number;
  nextPublishDate?: string;
  placeholder?: boolean;
}

interface CompanyEntitlement {
  companyId: string;
  enabled: boolean;
  entitlements: Entitlement[];
  skipReason?:
    | 'no-entitlements'
    | 'insufficient-units'
    | 'existing-scheduled-post'
    | 'launch-darkly-disabled'
    | 'not-entitled-for-next-occurrence'
    | 'ai-generated-post-already-scheduled-for-next-occurrence';
}

export const handler: Handler<
  FetchEntitlementsEvent,
  FetchEntitlementsResult
> = async (event, _context) => {
  const logger = createContextLogger('fetch-entitlements');
  const startTime = Date.now();

  try {
    logger.info('Fetching entitlements for blog post writer', { event });

    const {
      companyIds = [],
      dryRun = true,
      publishToCMS = false,
      executionName,
      executionStartTime,
      executionArn,
      skipEntitlementsValidation = false,
      skipUnitsValidation = false,
      skipExistingScheduledPostValidation = false,
    } = event;

    // Factory function to create API clients lazily
    const createApiClients = () => {
      // Validate required environment variables only when creating clients
      const requiredEnvVars = {
        API_GATEWAY_URL: process.env.API_GATEWAY_URL,
        API_GATEWAY_SUPER_USER_COMPANY_ID:
          process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
        API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
        AUTOMATED_BLOGS_PRODUCT_ID: process.env.AUTOMATED_BLOGS_PRODUCT_ID,
      };

      const missingVars = Object.entries(requiredEnvVars)
        .filter(([, value]) => !value)
        .map(([key]) => key);

      if (missingVars.length > 0) {
        throw new FetchEntitlementsError(
          `Missing required environment variables: ${missingVars.join(', ')}`,
          undefined,
          { missingVars, providedVars: Object.keys(requiredEnvVars) },
        );
      }

      // Create clients with validated environment variables
      const graphqlClient = new GraphQLClient(
        `${process.env.API_GATEWAY_URL!}/graphql`,
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      const logger = createContextLogger('fetch-entitlements');
      const apiGatewayClient = new ApiGatewayClient(
        process.env.API_GATEWAY_SUPER_USER_COMPANY_ID!,
        process.env.API_GATEWAY_KEY!,
        graphqlClient,
        logger,
      );

      return { graphqlClient, apiGatewayClient };
    };

    // Initialize skip counters
    const skipCounters = {
      noEntitlements: 0,
      insufficientUnits: 0,
      existingScheduledPost: 0,
      launchDarklyDisabled: 0,
      aiGeneratedPostAlreadyScheduled: 0,
    };

    // API clients will be created lazily when needed

    // Generate execution folder name
    const timestamp = executionStartTime || new Date().toISOString();
    const executionDate = timestamp.split('T')[0];
    const executionId = executionName || `manual-${Date.now()}`;
    const executionFolderName = `${executionId}-${timestamp.replace(/[:.]/g, '-')}`;

    // Determine which companies to process
    let companiesToProcess: string[] = companyIds;
    let activeEntitlements: EntitledCompany[] = []; // Store entitlements for later lookup

    if (companyIds.length === 0) {
      if (skipEntitlementsValidation) {
        // If skipEntitlementsValidation is true but no companyIds provided,
        // we can't proceed because we don't know which companies to process
        logger.error(
          'No companyIds provided and skipEntitlementsValidation=true - cannot proceed without explicit company IDs',
        );
        throw new FetchEntitlementsError(
          'Cannot skip entitlements validation without providing specific companyIds',
          undefined,
          {
            skipEntitlementsValidation,
            providedCompanyIds: companyIds.length,
          },
        );
      } else {
        const mode = dryRun ? 'dry run - ' : '';
        logger.info(
          'No companyIds provided, fetching entitled companies from API Gateway',
          { mode },
        );
        // Fetch entitled companies from Tenant Service (active entitlements only)
        try {
          const currentDate = new Date().toISOString();
          activeEntitlements = await fetchActiveEntitledCompanies(
            logger,
            currentDate,
          );

          companiesToProcess = activeEntitlements.map(e => e.companyId);
          logger.info('Found entitled companies for automated blogs', {
            count: companiesToProcess.length,
          });
        } catch (error) {
          logger.error('Failed to fetch entitled companies', { error });
          throw new FetchEntitlementsError(
            'Failed to fetch entitled companies when no companyIds provided',
            error instanceof Error ? error : new Error(String(error)),
            { productId: process.env.AUTOMATED_BLOGS_PRODUCT_ID },
          );
        }
      }
    } else {
      logger.info('Processing provided company IDs', {
        count: companyIds.length,
      });

      // When companyIds are provided, validate they exist in entitled companies list (except when skipping validations)
      if (!skipEntitlementsValidation) {
        try {
          logger.info(
            'Validating provided companyIds against entitled companies list...',
          );

          const currentDate = new Date().toISOString();
          activeEntitlements = await fetchActiveEntitledCompanies(
            logger,
            currentDate,
          );

          const entitledCompanyIds = new Set(
            activeEntitlements.map(e => e.companyId),
          );

          // Filter provided companyIds to only those that are entitled
          const validCompanyIds = companyIds.filter(id =>
            entitledCompanyIds.has(id),
          );
          const invalidCompanyIds = companyIds.filter(
            id => !entitledCompanyIds.has(id),
          );

          if (invalidCompanyIds.length > 0) {
            logger.warn(
              'Some provided companyIds are not entitled for automated blogs',
              { invalidCount: invalidCompanyIds.length, invalidCompanyIds },
            );
          }

          if (validCompanyIds.length === 0) {
            throw new FetchEntitlementsError(
              'None of the provided companyIds are entitled for automated blogs',
              undefined,
              {
                providedCompanyIds: companyIds,
                invalidCompanyIds,
                productId: process.env.AUTOMATED_BLOGS_PRODUCT_ID,
              },
            );
          }

          companiesToProcess = validCompanyIds;
          logger.info('Validated provided company IDs against entitlements', {
            validCount: validCompanyIds.length,
            totalCount: companyIds.length,
          });
        } catch (error) {
          logger.error(
            'Failed to validate provided company IDs against entitlements',
            { error },
          );
          throw new FetchEntitlementsError(
            'Failed to validate provided company IDs against entitled companies',
            error instanceof Error ? error : new Error(String(error)),
            {
              providedCompanyIds: companyIds,
              productId: process.env.AUTOMATED_BLOGS_PRODUCT_ID,
            },
          );
        }
      }
    }

    // 1. Batch check LaunchDarkly feature flags for all companies
    logger.info('Step 1: Checking LaunchDarkly flags for all companies...');
    const launchDarklyResults = await LaunchDarkly.checkVariationBatch(
      SUPER_BLOOM_FLAG,
      false,
      companiesToProcess,
      dryRun, // Force LaunchDarkly evaluation in dryRun for testing
    );

    // Filter companies based on LaunchDarkly results
    const ldEnabledCompanies = companiesToProcess.filter(companyId => {
      const isEnabled = launchDarklyResults.get(companyId) || false;
      if (isEnabled) {
        if (dryRun) {
          logger.info('Company would be enabled for super bloom', {
            companyId,
            launchDarkly: true,
          });
        }
        return true;
      } else {
        skipCounters.launchDarklyDisabled++;
        const mode = dryRun ? ' (would be skipped in production)' : '';
        logger.info('Company not enabled for super bloom, skipping', {
          companyId,
          mode,
        });
        return false;
      }
    });

    logger.info('LaunchDarkly filtering complete', {
      enabledCount: ldEnabledCompanies.length,
      totalCount: companiesToProcess.length,
    });

    // 2. Process remaining validations for LaunchDarkly-enabled companies
    const companyEntitlements: CompanyEntitlement[] = [];
    const publishDayOfWeek = process.env.POST_PUBLISH_DAY_OF_WEEK || 'TUESDAY';

    // Create lookup map for entitlements by companyId to get actual units
    const entitlementsLookup = new Map(
      activeEntitlements.map(e => [e.companyId, e]),
    );

    // Add skipped companies (LaunchDarkly disabled) to results
    companiesToProcess.forEach(companyId => {
      if (!launchDarklyResults.get(companyId)) {
        companyEntitlements.push({
          companyId,
          enabled: false,
          entitlements: [],
          skipReason: 'launch-darkly-disabled',
        });
      }
    });

    // Process LaunchDarkly-enabled companies
    for (const companyId of ldEnabledCompanies) {
      try {
        let skipReason: CompanyEntitlement['skipReason'];
        let graphqlClient: GraphQLClient | undefined; // Declare GraphQL client for scheduled post checks
        const companyEntitlement: CompanyEntitlement = {
          companyId,
          enabled: false,
          entitlements: [],
        };

        // LaunchDarkly check already passed, continue with other validations

        // 2. Fetch company entitlements from API Gateway (unless validation skipped)
        let fetchedEntitlements: Entitlement[] = [];
        // No skipReason yet since LaunchDarkly passed
        if (dryRun || skipEntitlementsValidation) {
          // Use placeholder data in dry run or when skipping entitlements validation
          const entitlement = entitlementsLookup.get(companyId);
          fetchedEntitlements = [
            {
              companyId,
              blogPostLimit: 1,
              units: entitlement?.units || 4, // Use actual units from entitlement, fallback to 4
              placeholder: true,
            },
          ];
          if (skipEntitlementsValidation && !dryRun) {
            logger.warn('Company bypassing entitlements validation (risky)', {
              companyId,
            });
          }
        } else {
          // Create clients only when needed for actual API calls
          const { graphqlClient: gqlClient } = createApiClients();
          graphqlClient = gqlClient; // Make GraphQL client available for scheduled post checks

          // Company entitlements are already validated by TenantClient at the beginning
          // of this function, so if we reach this point, the company is entitled
          const entitlement = entitlementsLookup.get(companyId);
          fetchedEntitlements = [
            {
              companyId,
              blogPostLimit: 1,
              units: entitlement?.units || 4, // Use actual units from entitlement, fallback to 4
            },
          ];
        }

        // 3. Validate units and calculate next publish date (unless already skipped or validation skipped)
        if (
          !skipReason &&
          !skipUnitsValidation &&
          fetchedEntitlements.length > 0
        ) {
          for (const entitlement of fetchedEntitlements) {
            const units = entitlement.units || 4; // Default to 4 if not specified

            // Calculate next publish date based on units and weekly entitlement
            const executionDate = new Date(timestamp);
            const nextPublishDate = getNextPublishDate(
              units,
              publishDayOfWeek,
              executionDate,
            );

            if (nextPublishDate === null) {
              // Company not entitled for the next POST_PUBLISH_DAY_OF_WEEK occurrence
              skipReason = 'not-entitled-for-next-occurrence';
              skipCounters.insufficientUnits++; // Reuse counter for now
              logger.info(
                'Company not entitled for next publish day based on units, skipping',
                { companyId, publishDayOfWeek, units },
              );
              break;
            }

            entitlement.nextPublishDate = nextPublishDate;

            // Validate minimum units (2 units minimum for biweekly, 4 for monthly)
            if (units < 2) {
              skipReason = 'insufficient-units';
              skipCounters.insufficientUnits++;
              logger.info('Company has insufficient units, skipping', {
                companyId,
                units,
              });
              break;
            }
          }
        }

        // 4. Check for AI-generated posts already scheduled for next occurrence (unless validation skipped)
        if (
          !skipReason &&
          !skipExistingScheduledPostValidation &&
          fetchedEntitlements.length > 0
        ) {
          for (const entitlement of fetchedEntitlements) {
            if (entitlement.nextPublishDate && graphqlClient) {
              const hasAIGeneratedPost = await checkExistingScheduledPost(
                companyId,
                entitlement.nextPublishDate,
                graphqlClient,
              );

              if (hasAIGeneratedPost) {
                skipReason =
                  'ai-generated-post-already-scheduled-for-next-occurrence';
                skipCounters.aiGeneratedPostAlreadyScheduled++;
                logger.info(
                  'Company already has AI-generated post scheduled, skipping',
                  { companyId, nextPublishDate: entitlement.nextPublishDate },
                );
                break;
              }
            }
          }
        }

        // 5. Check for existing scheduled posts (unless already skipped or validation skipped)
        if (
          !skipReason &&
          !skipExistingScheduledPostValidation &&
          fetchedEntitlements.length > 0
        ) {
          for (const entitlement of fetchedEntitlements) {
            if (entitlement.nextPublishDate && graphqlClient) {
              const hasExistingPost = await checkExistingScheduledPost(
                companyId,
                entitlement.nextPublishDate,
                graphqlClient,
              );

              if (hasExistingPost) {
                skipReason = 'existing-scheduled-post';
                skipCounters.existingScheduledPost++;
                logger.info('Company already has scheduled post, skipping', {
                  companyId,
                  nextPublishDate: entitlement.nextPublishDate,
                });
                break;
              }
            }
          }
        }

        // Set final entitlement state
        companyEntitlement.enabled = !skipReason;
        companyEntitlement.entitlements = skipReason ? [] : fetchedEntitlements;
        companyEntitlement.skipReason = skipReason;

        companyEntitlements.push(companyEntitlement);

        if (!skipReason) {
          logger.info('Company is eligible', {
            companyId,
            entitlementCount: fetchedEntitlements.length,
          });
        }
      } catch (error) {
        logger.error('Failed to process company', { companyId, error });
        if (!dryRun) {
          throw new TransientFetchEntitlementsError(
            `Failed to process company ${companyId}`,
            error instanceof Error ? error : new Error(String(error)),
            { companyId },
          );
        }
        // Add failed company with error tracking
        skipCounters.noEntitlements++;
        companyEntitlements.push({
          companyId,
          enabled: false,
          entitlements: [],
          skipReason: 'no-entitlements',
        });
      }
    }

    // Filter to only enabled companies
    const eligibleCompanies = companyEntitlements.filter(
      c => c.enabled && c.entitlements.length > 0,
    );

    if (eligibleCompanies.length === 0) {
      // In dryRun mode with no company IDs, this is expected - return early with empty result
      if (dryRun && companyIds.length === 0) {
        logger.info('DryRun mode with no companies - returning empty result');

        const timestamp = executionStartTime || new Date().toISOString();
        const executionDate = timestamp.split('T')[0];
        const executionId = executionName || `manual-${Date.now()}`;
        const executionFolderName = `${executionId}-${timestamp.replace(/[:.]/g, '-')}`;
        const s3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/entitlements.json`;

        // Save empty entitlements to S3 for consistency - minimal structure
        const s3Data = {
          companies: [],
        };

        const metadataS3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/execution-metadata.json`;
        const metadataData = {
          executionName,
          executionStartTime: timestamp,
          executionFolderName,
          dryRun,
          skippedCompanies: {
            summary: skipCounters,
            details: [],
          },
          metadata: {
            totalRequested: 0,
            totalProcessed: 0,
            totalEligible: 0,
            totalSkipped: 0,
            totalEntitlements: 0,
            publishDayOfWeek: process.env.POST_PUBLISH_DAY_OF_WEEK || 'TUESDAY',
            skipValidationFlags: {
              skipEntitlementsValidation,
              skipUnitsValidation,
              skipExistingScheduledPostValidation,
            },
          },
        };

        await s3Client.send(
          new PutObjectCommand({
            Bucket: BUCKET_NAME,
            Key: s3Key,
            Body: JSON.stringify(s3Data, null, 2),
            ContentType: 'application/json',
          }),
        );

        await s3Client.send(
          new PutObjectCommand({
            Bucket: BUCKET_NAME,
            Key: metadataS3Key,
            Body: JSON.stringify(metadataData, null, 2),
            ContentType: 'application/json',
          }),
        );

        // Send Slack notification for processing start even when no eligible companies
        await sendProcessingStartNotification(
          0, // No eligible companies
          executionName,
          dryRun,
          publishToCMS,
          executionArn,
          `blog-post-jobs/${executionDate}/${executionFolderName}`,
        );

        return {
          companyIds: [],
          dryRun,
          executionName,
          executionStartTime: timestamp,
          executionFolderName,
          s3Key,
          eligibleCompanies: 0,
          totalEntitlements: 0,
          skippedCompanies: skipCounters,
        };
      }

      throw new FetchEntitlementsError(
        'No eligible companies found for blog post generation',
        undefined,
        {
          totalCompanies: companiesToProcess.length,
          checkedCompanies: companyEntitlements.length,
          originalCompanyIds: companyIds.length,
        },
      );
    }

    // Save entitlements to S3 - optimized for Distributed Map processing
    const s3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/entitlements.json`;

    // Create minimal structure for Map iteration - only what's needed
    const s3Data = {
      companies: eligibleCompanies || [],
    };

    // Save detailed metadata separately to avoid input size issues
    const metadataS3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/execution-metadata.json`;
    const metadataData = {
      executionName,
      executionStartTime: timestamp,
      executionFolderName,
      dryRun,
      skippedCompanies: {
        summary: skipCounters,
        details: companyEntitlements
          .filter(c => !c.enabled)
          .map(c => ({
            companyId: c.companyId,
            skipReason: c.skipReason,
          })),
      },
      metadata: {
        totalRequested:
          companyIds.length === 0
            ? companiesToProcess.length
            : companyIds.length,
        totalProcessed: companyEntitlements.length,
        totalEligible: eligibleCompanies.length,
        totalSkipped: companyEntitlements.length - eligibleCompanies.length,
        totalEntitlements: eligibleCompanies.reduce(
          (sum, c) => sum + c.entitlements.length,
          0,
        ),
        publishDayOfWeek,
        skipValidationFlags: {
          skipEntitlementsValidation,
          skipUnitsValidation,
          skipExistingScheduledPostValidation,
        },
      },
    };

    logger.info('Saving entitlements data to S3', {
      s3Key,
      metadataS3Key,
      companiesCount: eligibleCompanies.length,
      companiesStructure: eligibleCompanies.slice(0, 1), // Log first company structure for debugging
      s3DataStructure: {
        hasCompaniesField: 'companies' in s3Data,
        companiesIsArray: Array.isArray(s3Data.companies),
        companiesLength: s3Data.companies?.length,
      },
    });

    // Save the minimal companies data for Map iteration
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: s3Key,
        Body: JSON.stringify(s3Data, null, 2),
        ContentType: 'application/json',
      }),
    );

    // Save detailed metadata separately
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: metadataS3Key,
        Body: JSON.stringify(metadataData, null, 2),
        ContentType: 'application/json',
      }),
    );

    logger.info('Saved entitlements to S3', { s3Key });
    logger.info('Found eligible companies with total entitlements', {
      eligibleCompaniesCount: eligibleCompanies.length,
      totalEntitlements: eligibleCompanies.reduce(
        (sum, c) => sum + c.entitlements.length,
        0,
      ),
    });

    // Send Slack notification for processing start
    await sendProcessingStartNotification(
      eligibleCompanies.length,
      executionName,
      dryRun,
      publishToCMS,
      executionArn,
      `blog-post-jobs/${executionDate}/${executionFolderName}`,
    );

    // Send Datadog metrics (non-fatal - failures won't affect main Lambda flow)
    const executionTime = Date.now() - startTime;
    await Promise.all([
      datadogMetrics.sendTimingMetric(
        METRICS.FETCH_ENTITLEMENTS_TIME,
        executionTime,
        'fetch-entitlements',
      ),
      datadogMetrics.sendCountMetric(
        METRICS.FETCH_ENTITLEMENTS_COMPANIES_COUNT,
        companiesToProcess.length,
        'fetch-entitlements',
      ),
      datadogMetrics.sendCountMetric(
        METRICS.FETCH_ENTITLEMENTS_ELIGIBLE_COUNT,
        eligibleCompanies.length,
        'fetch-entitlements',
        ['status:eligible'],
      ),
    ]).catch(error => {
      logger.warn('Failed to send Datadog metrics', { error });
    });

    return {
      companyIds: eligibleCompanies.map(c => c.companyId),
      dryRun,
      publishToCMS,
      executionName,
      executionStartTime: timestamp,
      executionFolderName,
      s3Key,
      eligibleCompanies: eligibleCompanies.length,
      totalEntitlements: eligibleCompanies.reduce(
        (sum, c) => sum + c.entitlements.length,
        0,
      ),
      skippedCompanies: skipCounters,
    };
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(METRICS.LAMBDA_ERRORS, 'fetch-entitlements', errorType)
      .catch(() => {
        /* Ignore metric errors */
      });

    if (error instanceof FetchEntitlementsError) {
      throw error;
    }
    throw new FetchEntitlementsError(
      'Failed to fetch entitlements',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  } finally {
    // Cleanup handled by centralized LaunchDarkly utility
    LaunchDarkly.closeConnection();
  }
};

// Helper function to calculate estimated completion time
function calculateEstimatedTime(companyCount: number): {
  estimatedMinutes: number;
  estimatedCompletionTime: string;
} {
  const maxConcurrency = 5;
  const avgExecutionTimePerCompany = 5; // minutes (AirOps execution)
  const stateWaitTime = 1; // minute (60 seconds wait in WaitForAirOps)

  // Calculate batches needed with max concurrency
  const batches = Math.ceil(companyCount / maxConcurrency);

  // Estimate total time: each batch takes ~5 minutes (AirOps) + 1 minute (polling/wait) + processing time
  const estimatedMinutes =
    batches * (avgExecutionTimePerCompany + stateWaitTime);

  // Add current time to get completion estimate
  const completionTime = new Date();
  completionTime.setMinutes(completionTime.getMinutes() + estimatedMinutes);

  return {
    estimatedMinutes,
    estimatedCompletionTime: completionTime.toISOString(),
  };
}

// Helper function to generate AWS execution URL
function getExecutionUrl(executionArn?: string): string | null {
  if (!executionArn) return null;

  // Parse the ARN to extract region for AWS console URL
  const arnParts = executionArn.split(':');
  if (arnParts.length < 6) return null;

  const region = arnParts[3];
  return `https://${region}.console.aws.amazon.com/states/home?region=${region}#/executions/details/${encodeURIComponent(executionArn)}`;
}

// Helper function to send processing start notification
async function sendProcessingStartNotification(
  companyCount: number,
  executionName?: string,
  isDryRun?: boolean,
  publishToCMS?: boolean,
  executionArn?: string,
  s3FolderPath?: string,
): Promise<void> {
  // Check if Slack notifications are enabled
  const slackEnabled =
    process.env.BLOG_V2_SLACK_NOTIFICATIONS_ENABLED === 'true' ||
    process.env.SLACK_NOTIFICATIONS_ENABLED === 'true';
  const slackWebhookUrl =
    process.env.BLOG_V2_SLACK_WEBHOOK_URL || process.env.SLACK_WEBHOOK_URL;

  if (!slackEnabled || !slackWebhookUrl) {
    // Note: This function should be refactored to accept a logger parameter
    console.log('Slack notifications disabled or webhook URL not configured');
    return;
  }

  try {
    const { estimatedMinutes, estimatedCompletionTime } =
      calculateEstimatedTime(companyCount);
    const environment = process.env.ENVIRONMENT || 'dev';
    const envEmoji =
      environment === 'production'
        ? '🚀'
        : environment === 'staging'
          ? '🧪'
          : '🔧';
    const envName = environment.toUpperCase();
    const mode =
      publishToCMS === false ? ' [REVIEW MODE]' : isDryRun ? ' [DRY RUN]' : '';

    let message = `${envEmoji} *[${envName}]* 📝 *Blog Post Writer Starting*${mode}\n\n`;

    message += `*Processing Details:*\n`;
    const executionUrl = getExecutionUrl(executionArn);
    if (executionUrl) {
      message += `• Execution: <${executionUrl}|${executionName || 'View Execution'}>\n`;
    } else {
      message += `• Execution: ${executionName || 'Unknown'}\n`;
    }

    // Add S3 folder link if available
    if (s3FolderPath) {
      const s3FolderUrl = getS3FolderUrl(BUCKET_NAME, s3FolderPath);
      message += `• S3 Folder: <${s3FolderUrl}|View S3 Files>\n`;
    }

    message += `• Mode: ${publishToCMS === false ? 'Review Only' : isDryRun ? 'Dry Run' : 'Live Publication'}\n`;
    message += `• Companies to Process: ${companyCount}\n`;
    message += `• Max Concurrency: 5\n`;
    message += `• Estimated Time: ~${estimatedMinutes} minutes\n`;
    message += `• Estimated Completion: ${
      new Date(estimatedCompletionTime)
        .toISOString()
        .replace('T', ' ')
        .split('.')[0]
    } UTC\n`;

    if (companyCount > 0) {
      message += `\n*Process Flow:*\n`;
      message += `• Each company: ~5 min AirOps execution + 1 min polling\n`;
      message += `• Running ${Math.min(companyCount, 5)} companies in parallel\n`;
      message += `• ${Math.ceil(companyCount / 5)} batch${Math.ceil(companyCount / 5) > 1 ? 'es' : ''} expected\n`;
    }

    // Add footer with environment and timestamp
    const timestamp = new Date().toISOString().replace('T', ' ').split('.')[0];
    message += `\n───────────────────\n`;
    message += `_Environment: ${envName} | Started: ${timestamp} UTC_`;

    const response = await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text: message }),
    });

    if (!response.ok) {
      // Note: This function should be refactored to accept a logger parameter
      console.error('Failed to send Slack notification:', response.statusText);
    } else {
      // Note: This function should be refactored to accept a logger parameter
      console.log('Slack start notification sent successfully');
    }
  } catch (slackError) {
    // Note: This function should be refactored to accept a logger parameter
    console.error('Error sending Slack notification:', slackError);
  }
}
