import { Handler } from 'aws-lambda';

import { ApiGatewayClient } from '../../clients/ApiGatewayClient';
import { CosmoClient } from '../../clients/CosmoClient';
import { ApiGatewayError } from '../../clients/errors';
import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  GetBrandError,
  TransientGetBrandError,
  MissingBrandProfileError,
} from '../../errors/blog-post-writer';
import { BrandProfile, Neighborhood } from '../../types/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

export interface GetBrandEvent {
  companyId: string;
  dryRun: boolean;
  executionFolderName: string;
}

export interface GetBrandResult {
  companyId: string;
  brandProfile: BrandProfile;
  neighborhoods: Neighborhood[];
  preferredNeighborhoods: Neighborhood[];
  hasProfile: boolean;
}

export const handler: Handler<GetBrandEvent, GetBrandResult> = async event => {
  const logger = createContextLogger('get-brand');
  const startTime = Date.now();

  try {
    logger.info('Getting brand profile and neighborhoods', { event });

    const { companyId, dryRun } = event;

    // Validate required environment variables
    const requiredEnvVars = {
      API_GATEWAY_URL: process.env.API_GATEWAY_URL,
      API_GATEWAY_SUPER_USER_COMPANY_ID:
        process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
      API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
      COSMO_GQL_URL: process.env.COSMO_GQL_URL,
      M2M_SUPER_API_KEY: process.env.M2M_SUPER_API_KEY,
    };

    const missingEnvVars = Object.entries(requiredEnvVars)
      .filter(([, value]) => !value || value.trim() === '')
      .map(([key]) => key);

    if (missingEnvVars.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingEnvVars.join(', ')}`,
      );
    }

    // Initialize API Gateway client with validated environment variables
    const { GraphQLClient } = await import('graphql-request');
    const apiGatewayGraphqlClient = new GraphQLClient(
      `${requiredEnvVars.API_GATEWAY_URL!}/graphql`,
    );

    const apiGatewayClient = new ApiGatewayClient(
      requiredEnvVars.API_GATEWAY_SUPER_USER_COMPANY_ID!,
      requiredEnvVars.API_GATEWAY_KEY!,
      apiGatewayGraphqlClient,
      logger,
    );

    // Initialize hybrid CosmoClient
    const cosmoClient = new CosmoClient(
      requiredEnvVars.COSMO_GQL_URL!,
      requiredEnvVars.M2M_SUPER_API_KEY!,
      apiGatewayClient,
      logger,
    );

    // In dry run mode, return mock data
    if (dryRun) {
      logger.info('Dry run mode - returning mock brand data');
      return {
        companyId,
        brandProfile: {
          companyId,
          aboutTheBrand: `Test Company ${companyId} specializes in providing excellent services to our community.`,
          strategicFocus:
            'Delivering high-quality services with a focus on customer satisfaction and community engagement.',
          valueProposition:
            'We offer unique solutions that combine expertise, reliability, and local knowledge.',
          idealCustomerProfiles:
            'Local residents and businesses seeking trusted, professional services in their area.',
          missionAndCoreValues:
            'Our mission is to serve our community with integrity, excellence, and dedication to quality.',
          brandPointOfView:
            'We believe in building lasting relationships through exceptional service and community involvement.',
          toneOfVoice: 'Professional, friendly, and approachable',
          ctaText: 'Contact us today to learn more',
          authorPersona:
            'Local business expert with deep community knowledge and industry expertise',
        },
        neighborhoods: [
          {
            id: 'neighborhood-1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            priority: 1,
          },
          {
            id: 'neighborhood-2',
            name: 'Midtown',
            preferredName: 'Midtown',
            googlePlacesName: 'Midtown Area',
            priority: 2,
          },
        ],
        preferredNeighborhoods: [
          {
            id: 'neighborhood-1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            priority: 1,
          },
        ],
        hasProfile: true,
      };
    }

    logger.info('Fetching brand profile for company', { companyId });

    try {
      const brandProfile = await fetchBrandProfile(cosmoClient, companyId);

      if (!brandProfile) {
        throw new MissingBrandProfileError(companyId);
      }

      // Fetch neighborhoods with Google Places validation (uses API Gateway directly)
      const neighborhoods = await fetchNeighborhoods(
        apiGatewayClient,
        companyId,
        logger,
      );
      const preferredNeighborhoods = neighborhoods.filter(
        (n: Neighborhood) => n.isPreferred === true,
      );

      // Send timing metric
      const executionTime = Date.now() - startTime;
      await datadogMetrics
        .sendTimingMetric(METRICS.GET_BRAND_TIME, executionTime, 'get-brand')
        .catch(() => {
          /* Ignore metric errors */
        });

      return {
        companyId,
        brandProfile,
        neighborhoods,
        preferredNeighborhoods,
        hasProfile: true,
      };
    } catch (error) {
      if (error instanceof MissingBrandProfileError) {
        throw error;
      }
      throw new TransientGetBrandError(
        `Failed to fetch brand profile for company ${companyId}`,
        error instanceof Error ? error : new Error(String(error)),
        { companyId },
      );
    }
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(METRICS.LAMBDA_ERRORS, 'get-brand', errorType)
      .catch(() => {
        /* Ignore metric errors */
      });

    if (error instanceof GetBrandError) {
      throw error;
    }

    // Let environment validation errors pass through without wrapping
    if (
      error instanceof Error &&
      error.message.includes('Missing required environment variables')
    ) {
      throw error;
    }

    throw new GetBrandError(
      'Failed to get brand information',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};

// Helper function to fetch brand profile
async function fetchBrandProfile(
  client: CosmoClient,
  companyId: string,
): Promise<BrandProfile | null> {
  try {
    const brandProfileData = await client.getBrandProfile(companyId);

    if (!brandProfileData) {
      return null;
    }

    return {
      companyId,
      aboutTheBrand: brandProfileData.aboutTheBrand,
      strategicFocus: brandProfileData.strategicFocus,
      valueProposition: brandProfileData.valueProposition,
      idealCustomerProfiles: brandProfileData.idealCustomerProfiles,
      missionAndCoreValues: brandProfileData.missionAndCoreValues,
      brandPointOfView: brandProfileData.brandPointOfView,
      toneOfVoice: brandProfileData.toneOfVoice,
      ctaText: brandProfileData.ctaText,
      authorPersona: brandProfileData.authorPersona,
    };
  } catch (error) {
    // Return null only for schema/field-missing cases; otherwise rethrow so caller can treat as transient
    const errorResponse =
      error && typeof error === 'object' && 'response' in error
        ? (error as { response?: { errors?: Array<{ message: string }> } })
            .response
        : undefined;
    const maybeErrors = errorResponse?.errors;
    const isSchemaError =
      Array.isArray(maybeErrors) &&
      maybeErrors.some(e =>
        /Cannot query field|Unknown argument|Unknown type|Cannot return null for non-nullable field/i.test(
          e?.message || '',
        ),
      );
    if (isSchemaError) {
      return null;
    }
    throw error;
  }
}

// Helper function to fetch neighborhoods with Google Places validation
async function fetchNeighborhoods(
  client: ApiGatewayClient,
  companyId: string,
  logger: ReturnType<typeof createContextLogger>,
): Promise<Neighborhood[]> {
  try {
    const neighborhoodsData =
      await client.getNeighborhoodsWithGooglePlaces(companyId);

    // Check if the result is valid
    if (!neighborhoodsData || !Array.isArray(neighborhoodsData)) {
      throw new Error('Invalid neighborhoods data returned from API');
    }

    // Log for debugging and monitoring
    logger.info('Fetched neighborhoods with Google Places validation', {
      companyId,
      totalNeighborhoods: neighborhoodsData.length,
    });

    // Map to the expected Neighborhood interface, maintaining backward compatibility
    return neighborhoodsData.map(n => ({
      id: n.id,
      name: n.preferredName || n.name, // Use preferredName as the primary name
      preferredName: n.preferredName || n.name,
      googlePlacesName: n.googlePlacesName || n.name, // Guarantee fallback
      priority: n.isPrimary ? 1 : 2, // Primary neighborhoods get higher priority
      // Include new fields
      googlePlaceData: n.googlePlaceData,
      isPreferred: false, // Will be set by preference logic if needed
      isPrimary: n.isPrimary,
      slug: n.slug,
      description: n.description,
      coordinates: n.coordinates,
    }));
  } catch (error) {
    const isKnownRecoverableError =
      error instanceof ApiGatewayError ||
      error instanceof GetBrandError ||
      error instanceof MissingBrandProfileError ||
      (error instanceof Error &&
        (error.message.includes('Invalid neighborhoods data') ||
          error.message.includes('GraphQL') ||
          error.message.includes('validation') ||
          error.message.includes('schema') ||
          error.message.includes('network') ||
          error.message.includes('timeout') ||
          error.message.includes('fetch') ||
          error.message.includes('method failed')));

    if (isKnownRecoverableError) {
      logger.error(
        'Error fetching neighborhoods with Google Places validation',
        {
          error: error instanceof Error ? error.message : String(error),
          companyId,
        },
      );
      // Return empty array as fallback for known errors
      return [];
    }
    // Rethrow unexpected errors to avoid masking outages
    throw error;
  }
}
