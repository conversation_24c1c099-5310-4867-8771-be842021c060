import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Hand<PERSON> } from 'aws-lambda';

import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  GetAirOpsJobStatusError,
  TransientGetAirOpsJobStatusError,
  AirOpsJobFailedError,
} from '../../errors/blog-post-writer';
import {
  BlogContent,
  AirOpsStatusResponse,
} from '../../types/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

const s3Client = new S3Client({});
const BUCKET_NAME =
  process.env.EXECUTION_BUCKET_NAME ||
  `lp-bloom-scheduler-execution-${process.env.ENVIRONMENT || 'development'}`;

const AIROPS_API_URL =
  process.env.AIR_OPS_API_URL ||
  'https://api.airops.com/public_api/airops_apps';
const AIROPS_API_KEY = process.env.AIR_OPS_API_KEY || '';

export interface GetAirOpsJobStatusEvent {
  companyId: string;
  topicId: string;
  airopsExecutionId: string;
  dryRun: boolean;
  executionFolderName: string;
  executionStartTime?: string;
  pollingAttempts?: { count: number };
}

export interface GetAirOpsJobStatusResult {
  companyId: string;
  topicId: string;
  airopsExecutionId: string;
  status:
    | 'pending'
    | 'running'
    | 'completed'
    | 'failed'
    | 'cancelled'
    | 'expired';
  blogContent?: BlogContent;
  s3Location?: string;
  error?: unknown;
}

export const handler: Handler<
  GetAirOpsJobStatusEvent,
  GetAirOpsJobStatusResult
> = async event => {
  const logger = createContextLogger('get-airops-job-status');
  const startTime = Date.now();

  try {
    logger.info('Checking AirOps job status', { event });

    const {
      companyId,
      topicId,
      airopsExecutionId,
      dryRun,
      executionFolderName,
      executionStartTime,
      pollingAttempts,
    } = event;

    // Log polling attempt count if available and send metric
    if (pollingAttempts) {
      logger.info('Polling attempt', { count: pollingAttempts.count });
      await datadogMetrics
        .sendCountMetric(
          METRICS.GET_AIROPS_JOB_STATUS_POLLING_COUNT,
          pollingAttempts.count,
          'get-airops-job-status',
        )
        .catch(() => {
          /* Ignore metric errors */
        });
    }

    // In dry run mode, return mock status
    if (dryRun) {
      logger.info('Dry run mode - returning mock job status');

      // Simulate different statuses based on polling attempts
      const mockAttempts = pollingAttempts?.count || 0;

      if (mockAttempts < 2) {
        return {
          companyId,
          topicId,
          airopsExecutionId,
          status: 'running',
        };
      }

      const blogContent = {
        title: 'Mock Blog Post Title',
        content:
          '# Mock Blog Content\n\nThis is a mock blog post generated in dry run mode.',
        metaDescription: 'Mock meta description for SEO',
        keywords: ['mock', 'test', 'blog'],
        imagePrompt: 'A beautiful scenic view of downtown',
      };

      // Write mock blog content to S3 for consistency with real mode
      const timestamp = executionStartTime || new Date().toISOString();
      const executionDate = timestamp.split('T')[0];
      const s3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/posts/${companyId}-${topicId}.json`;

      const blogPostData = {
        companyId,
        topicId,
        title: blogContent.title,
        content: blogContent.content,
        metaDescription: blogContent.metaDescription,
        keywords: blogContent.keywords,
        imagePrompt: blogContent.imagePrompt,
        createdAt: new Date().toISOString(),
        dryRun,
        airopsExecutionId,
      };

      await s3Client.send(
        new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: s3Key,
          Body: JSON.stringify(blogPostData, null, 2),
          ContentType: 'application/json',
          Metadata: {
            companyId,
            topicId,
            executionFolderName,
          },
        }),
      );

      const s3Location = `s3://${BUCKET_NAME}/${s3Key}`;
      logger.info('Saved mock blog content to S3', { s3Location });

      return {
        companyId,
        topicId,
        airopsExecutionId,
        status: 'completed',
        blogContent,
        s3Location,
      };
    }

    // Call AirOps API to get execution status
    const url = `${AIROPS_API_URL}/executions/${airopsExecutionId}`;
    logger.info('Calling AirOps API', { url });

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${AIROPS_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();

      // Check if this is a transient error
      if (response.status >= 500 || response.status === 429) {
        throw new TransientGetAirOpsJobStatusError(
          `AirOps API returned error: ${response.status} ${response.statusText}`,
          undefined,
          { statusCode: response.status, response: errorText },
        );
      }

      throw new GetAirOpsJobStatusError(
        `AirOps API returned error: ${response.status} ${response.statusText}`,
        undefined,
        { statusCode: response.status, response: errorText },
      );
    }

    const responseData = await response.json();
    logger.info('AirOps response', { responseData });

    // Parse status from response
    const status = parseAirOpsStatus(responseData);

    // Check for failed statuses
    if (status === 'failed' || status === 'cancelled' || status === 'expired') {
      throw new AirOpsJobFailedError(
        airopsExecutionId,
        status,
        responseData.error,
      );
    }

    // Extract blog content if completed
    let blogContent;
    let s3Location;
    if (status === 'completed' && responseData.output) {
      blogContent = responseData.output;

      // Write blog content to S3 for review/publishing
      const timestamp = executionStartTime || new Date().toISOString();
      const executionDate = timestamp.split('T')[0];
      const s3Key = `blog-post-jobs/${executionDate}/${executionFolderName}/posts/${companyId}-${topicId}.json`;

      const blogPostData = {
        companyId,
        topicId,
        createdAt: new Date().toISOString(),
        subtitle: blogContent.subtitle,
        seoTitle: blogContent.seoTitle,
        seoDescription: blogContent.seoDescription,
        metaDescription: blogContent.metaDescription,
        content: blogContent.content,
        dryRun,
        airopsExecutionId,
      };

      await s3Client.send(
        new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: s3Key,
          Body: JSON.stringify(blogPostData, null, 2),
          ContentType: 'application/json',
          Metadata: {
            companyId,
            topicId,
            executionFolderName,
          },
        }),
      );

      s3Location = `s3://${BUCKET_NAME}/${s3Key}`;
      logger.info('Saved blog content to S3', { s3Location });
    }

    // Send timing metric and status
    const executionTime = Date.now() - startTime;
    await Promise.all([
      datadogMetrics.sendTimingMetric(
        METRICS.GET_AIROPS_JOB_STATUS_TIME,
        executionTime,
        'get-airops-job-status',
      ),
      datadogMetrics.sendIncrementMetric(
        METRICS.GET_AIROPS_JOB_STATUS_POLLING_COUNT,
        'get-airops-job-status',
        [`status:${status}`],
      ),
    ]).catch(() => {
      /* Ignore metric errors */
    });

    return {
      companyId,
      topicId,
      airopsExecutionId,
      status,
      blogContent,
      s3Location,
      error: responseData.error,
    };
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(
        METRICS.LAMBDA_ERRORS,
        'get-airops-job-status',
        errorType,
      )
      .catch(() => {
        /* Ignore metric errors */
      });

    if (error instanceof GetAirOpsJobStatusError) {
      throw error;
    }

    // Check if this is a network error
    if (
      error instanceof Error &&
      (error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('ENOTFOUND'))
    ) {
      throw new TransientGetAirOpsJobStatusError(
        'Network error calling AirOps API',
        error,
        { event },
      );
    }

    throw new GetAirOpsJobStatusError(
      'Failed to get AirOps job status',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};

// Helper function to parse AirOps status
function parseAirOpsStatus(
  response: AirOpsStatusResponse,
): GetAirOpsJobStatusResult['status'] {
  const status = response.status || response.state || 'pending';

  // Map AirOps statuses to our internal statuses
  switch (status.toLowerCase()) {
    case 'pending':
    case 'queued':
      return 'pending';
    case 'running':
    case 'processing':
    case 'in_progress':
      return 'running';
    case 'completed':
    case 'success':
    case 'succeeded':
      return 'completed';
    case 'failed':
    case 'error':
      return 'failed';
    case 'cancelled':
    case 'canceled':
      return 'cancelled';
    case 'expired':
    case 'timeout':
      return 'expired';
    default:
      // Note: This function should be refactored to accept a logger parameter
      console.warn(`Unknown AirOps status: ${status}, treating as pending`);
      return 'pending';
  }
}
