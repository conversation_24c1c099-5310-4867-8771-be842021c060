// Set up environment variables before importing the handler
process.env.API_GATEWAY_URL = 'https://test-api.example.com';
process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'test-user';
process.env.API_GATEWAY_KEY = 'test-key';

import { ApiGatewayClient } from '../../../clients/ApiGatewayClient';
import {
  handler,
  CatchFailureEvent,
  CatchFailureResult,
} from '../catch-failure';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

jest.mock('../../../utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

// Mock ApiGatewayClient
jest.mock('../../../clients/ApiGatewayClient');

// Mock fetch for Slack notifications
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

describe('CatchFailure Lambda', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockApiGatewayClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.debug.mockClear();

    // Mock ApiGatewayClient with unlockBlogTopic method
    mockApiGatewayClient = {
      unlockBlogTopic: jest.fn(),
    };

    (ApiGatewayClient as jest.Mock).mockImplementation(
      () => mockApiGatewayClient,
    );

    // Reset environment variables
    process.env.API_GATEWAY_URL = 'https://test-api.example.com';
    process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'test-user';
    process.env.API_GATEWAY_KEY = 'test-key';
    process.env.SLACK_NOTIFICATIONS_ENABLED = 'false';
    process.env.BLOG_V2_SLACK_NOTIFICATIONS_ENABLED = 'false';
    delete process.env.SLACK_WEBHOOK_URL;
    delete process.env.BLOG_V2_SLACK_WEBHOOK_URL;
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.API_GATEWAY_URL;
    delete process.env.API_GATEWAY_SUPER_USER_COMPANY_ID;
    delete process.env.API_GATEWAY_KEY;
  });

  describe('handler', () => {
    const createMockEvent = (overrides = {}): CatchFailureEvent => ({
      error: {
        Error: 'BlogPostWriterError',
        Cause: 'Failed to generate blog post',
        message: 'Failed to generate blog post',
      },
      executionName: 'test-execution-123',
      companyIds: ['company-1', 'company-2'],
      ...overrides,
    });

    it('should unlock blog topic when topic ID is available in context', async () => {
      const event = createMockEvent({
        context: {
          companyId: 'company-1',
          topicData: {
            selectedTopic: {
              id: 'topic-123',
              companyId: 'company-1',
            },
          },
        },
      });

      mockApiGatewayClient.unlockBlogTopic.mockResolvedValue({
        id: 'topic-123',
        cmsPostId: null,
      });

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockApiGatewayClient.unlockBlogTopic).toHaveBeenCalledWith(
        'topic-123',
      );
      expect(result.handled).toBe(true);
      expect(result.error.type).toBe('BlogPostWriterError');
    });

    it('should not attempt to unlock when no topic ID in context', async () => {
      const event = createMockEvent({
        context: {
          companyId: 'company-1',
          // No topicData
        },
      });

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockApiGatewayClient.unlockBlogTopic).not.toHaveBeenCalled();
      expect(result.handled).toBe(true);
    });

    it('should continue when unlock fails', async () => {
      const event = createMockEvent({
        context: {
          companyId: 'company-1',
          topicData: {
            selectedTopic: {
              id: 'topic-123',
              companyId: 'company-1',
            },
          },
        },
      });

      mockApiGatewayClient.unlockBlogTopic.mockRejectedValue(
        new Error('Unlock failed'),
      );

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockApiGatewayClient.unlockBlogTopic).toHaveBeenCalledWith(
        'topic-123',
      );
      expect(result.handled).toBe(true);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to unlock blog topic after failure',
        expect.objectContaining({
          error: 'Unlock failed',
          topicId: 'topic-123',
        }),
      );
    });

    it('should skip unlock when environment variables are missing', async () => {
      const event = createMockEvent({
        context: {
          companyId: 'company-1',
          topicData: {
            selectedTopic: {
              id: 'topic-123',
              companyId: 'company-1',
            },
          },
        },
      });

      // Remove environment variables
      delete process.env.API_GATEWAY_URL;
      delete process.env.API_GATEWAY_SUPER_USER_COMPANY_ID;
      delete process.env.API_GATEWAY_KEY;

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockApiGatewayClient.unlockBlogTopic).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Cannot unlock blog topic - missing environment variables',
        expect.objectContaining({
          topicId: 'topic-123',
          missingEnvVars: [
            'API_GATEWAY_URL',
            'API_GATEWAY_SUPER_USER_COMPANY_ID',
            'API_GATEWAY_KEY',
          ],
        }),
      );
      expect(result.handled).toBe(true);
    });

    it('should send Slack notification when enabled', async () => {
      process.env.SLACK_NOTIFICATIONS_ENABLED = 'true';
      process.env.SLACK_WEBHOOK_URL = 'https://hooks.slack.com/test';

      const event = createMockEvent({
        context: {
          companyId: 'company-1',
          topicData: {
            selectedTopic: {
              id: 'topic-123',
            },
          },
        },
      });

      mockFetch.mockResolvedValueOnce({
        ok: true,
      } as never);

      mockApiGatewayClient.unlockBlogTopic.mockResolvedValue({
        id: 'topic-123',
        cmsPostId: null,
      });

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockFetch).toHaveBeenCalledWith(
        'https://hooks.slack.com/test',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining(
            'Blog Post Writer State Machine Failed',
          ),
        }),
      );
      expect(result.handled).toBe(true);
    });

    it('should handle Slack notification failures gracefully', async () => {
      process.env.SLACK_NOTIFICATIONS_ENABLED = 'true';
      process.env.SLACK_WEBHOOK_URL = 'https://hooks.slack.com/test';

      const event = createMockEvent();

      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error sending Slack notification',
        expect.objectContaining({
          error: expect.any(Error),
        }),
      );
      expect(result.handled).toBe(true);
    });

    it('should log error details', async () => {
      const event = createMockEvent({
        error: {
          Error: 'CustomError',
          Cause: 'Custom error message',
        },
        executionName: 'execution-456',
        companyIds: ['company-3'],
        context: {
          companyId: 'company-3',
        },
      });

      await handler(event, {} as never, {} as never);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Blog post writer execution failed:',
        expect.objectContaining({
          event: expect.objectContaining({
            error: expect.objectContaining({
              Error: 'CustomError',
              Cause: 'Custom error message',
            }),
          }),
        }),
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error details:',
        expect.objectContaining({
          errorType: 'CustomError',
          errorMessage: 'Custom error message',
          executionName: 'execution-456',
          companyIds: ['company-3'],
          context: expect.objectContaining({
            companyId: 'company-3',
          }),
        }),
      );
    });

    it('should return proper result structure', async () => {
      const event = createMockEvent({
        error: {
          Error: 'TestError',
          Cause: 'Test error message',
        },
        executionName: 'test-execution',
      });

      const result = (await handler(
        event,
        {} as never,
        {} as never,
      )) as CatchFailureResult;

      expect(result).toEqual({
        error: {
          type: 'TestError',
          message: 'Test error message',
          raw: event.error,
        },
        executionName: 'test-execution',
        failedAt: expect.any(String),
        handled: true,
      });

      expect(new Date(result.failedAt)).toBeInstanceOf(Date);
    });
  });
});
