import { BlogPostPublisherEvent } from '../blog-post-publisher';

describe('BlogPostPublisher', () => {
  it('should have correct interface structure', () => {
    // Test that the interface is correctly defined
    const event: BlogPostPublisherEvent = {
      companyId: 'test-company-1',
      topicId: 'test-topic-123',
      s3Location: 's3://test-bucket/test-key.json',
      mediaId: 'test-media-id',
      dryRun: true,
      scheduledAt: '2024-01-01T10:00:00Z',
    };

    expect(event.companyId).toBe('test-company-1');
    expect(event.s3Location).toBe('s3://test-bucket/test-key.json');
    expect(event.dryRun).toBe(true);
    expect(event.scheduledAt).toBe('2024-01-01T10:00:00Z');
  });

  it('should handle optional fields correctly', () => {
    // Test minimal valid event
    const minimalEvent: BlogPostPublisherEvent = {
      companyId: 'test-company-1',
      topicId: 'test-topic-123',
      s3Location: 's3://test-bucket/test-key.json',
      dryRun: false,
    };

    expect(minimalEvent.mediaId).toBeUndefined();
    expect(minimalEvent.imageUrl).toBeUndefined();
    expect(minimalEvent.scheduledAt).toBeUndefined();
  });

  it('should handle S3 URL parsing correctly', () => {
    // Test the S3 URL parsing logic that the function uses
    const s3Location = 's3://test-bucket/path/to/blog-post.json';
    const url = new URL(s3Location);

    expect(url.hostname).toBe('test-bucket');
    expect(url.pathname.substring(1)).toBe('path/to/blog-post.json');
  });

  it('should handle different S3 URL formats', () => {
    const testCases = [
      {
        input: 's3://cms-bucket/post.json',
        expectedBucket: 'cms-bucket',
        expectedKey: 'post.json',
      },
      {
        input: 's3://production-cms/blog-posts/2024/post-123.json',
        expectedBucket: 'production-cms',
        expectedKey: 'blog-posts/2024/post-123.json',
      },
    ];

    testCases.forEach(({ input, expectedBucket, expectedKey }) => {
      const url = new URL(input);
      expect(url.hostname).toBe(expectedBucket);
      expect(url.pathname.substring(1)).toBe(expectedKey);
    });
  });

  describe('Date scheduling logic helpers', () => {
    const getDayOfWeekNumber = (dayName: string): number => {
      const days = {
        SUNDAY: 0,
        MONDAY: 1,
        TUESDAY: 2,
        WEDNESDAY: 3,
        THURSDAY: 4,
        FRIDAY: 5,
        SATURDAY: 6,
      };
      const day = days[dayName.toUpperCase() as keyof typeof days];
      return day !== undefined ? day : 2; // Default to TUESDAY
    };

    it('should return correct day numbers', () => {
      expect(getDayOfWeekNumber('SUNDAY')).toBe(0);
      expect(getDayOfWeekNumber('MONDAY')).toBe(1);
      expect(getDayOfWeekNumber('TUESDAY')).toBe(2);
      expect(getDayOfWeekNumber('WEDNESDAY')).toBe(3);
      expect(getDayOfWeekNumber('THURSDAY')).toBe(4);
      expect(getDayOfWeekNumber('FRIDAY')).toBe(5);
      expect(getDayOfWeekNumber('SATURDAY')).toBe(6);
    });

    it('should default to TUESDAY for invalid day names', () => {
      expect(getDayOfWeekNumber('INVALID')).toBe(2);
      expect(getDayOfWeekNumber('')).toBe(2);
    });

    it('should calculate next scheduled date correctly', () => {
      // Mock a specific date - Tuesday, January 2, 2024
      const mockDate = new Date('2024-01-02T08:00:00Z'); // Tuesday
      jest.useFakeTimers();
      jest.setSystemTime(mockDate);

      const getNextScheduledDate = (publishDayOfWeek = 'THURSDAY'): string => {
        const today = new Date();
        const targetDay = getDayOfWeekNumber(publishDayOfWeek);
        const todayDay = today.getDay();

        // Calculate days until target day
        let daysUntilTarget = (targetDay - todayDay + 7) % 7;

        // If today is the target day, schedule for next week
        if (daysUntilTarget === 0) {
          daysUntilTarget = 7;
        }

        // Create the scheduled date at 10am UTC
        const scheduledDate = new Date(today);
        scheduledDate.setDate(today.getDate() + daysUntilTarget);
        scheduledDate.setUTCHours(10, 0, 0, 0); // 10:00 AM UTC

        return scheduledDate.toISOString();
      };

      const scheduledDate = getNextScheduledDate('THURSDAY');
      const dateObj = new Date(scheduledDate);

      expect(dateObj.getDay()).toBe(4); // Thursday
      expect(dateObj.getUTCHours()).toBe(10); // 10 AM UTC
      expect(dateObj.getUTCMinutes()).toBe(0);

      jest.useRealTimers();
    });
  });

  describe('GraphQL mutation structure', () => {
    it('should have correct createPost mutation parameters', () => {
      // Test that all required parameters are included in the mutation
      const expectedParameters = [
        'companyId',
        'content',
        'mediaIds',
        'postId',
        'seoDescription',
        'seoTitle',
        'title',
        'neighborhoods',
        'scheduledAt',
      ];

      const createPostMutation = `
        mutation createPost(
          $companyId: ID!
          $content: String!
          $mediaIds: [ID!]!
          $postId: ID
          $seoDescription: String!
          $seoTitle: String!
          $title: String!
          $neighborhoods: [ID!] = []
          $scheduledAt: String
        ) {
          createPost(
            companyId: $companyId
            title: $title
            description: $content
            mediaIds: $mediaIds
            postId: $postId
            seoDescription: $seoDescription
            seoTitle: $seoTitle
            neighborhoods: $neighborhoods
            scheduledAt: $scheduledAt
            aiGenerated: true
          ) {
            id
            postId
            slug
          }
        }
      `;

      // Check that all expected parameters are present
      expectedParameters.forEach(param => {
        expect(createPostMutation).toContain(`$${param}`);
      });

      // Check response fields
      expect(createPostMutation).toContain('id');
      expect(createPostMutation).toContain('postId');
      expect(createPostMutation).toContain('slug');
    });
  });
});
