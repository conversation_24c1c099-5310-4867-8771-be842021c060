import { ApiGatewayClient } from '../../../clients/ApiGatewayClient';
import { CosmoClient } from '../../../clients/CosmoClient';
import {
  BrandProfile,
  Neighborhood,
  BlogTopic,
} from '../../../types/blog-post-writer';
import { handler } from '../blog-topic-selector';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

jest.mock('../../../utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

// Mock dependencies
jest.mock('../../../clients/ApiGatewayClient');
jest.mock('../../../clients/CosmoClient');

describe('BlogTopicSelector Lambda', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockApiGatewayClient: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockCosmoClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.debug.mockClear();

    // Mock environment variables
    process.env.API_GATEWAY_URL = 'https://test-api.example.com';
    process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'test-user';
    process.env.API_GATEWAY_KEY = 'test-key';
    process.env.COSMO_GQL_URL = 'https://test-cosmo-endpoint.com';
    process.env.M2M_SUPER_API_KEY = 'test-m2m-key';

    // Mock ApiGatewayClient with selectBlogTopic and lockBlogTopic methods
    mockApiGatewayClient = {
      selectBlogTopic: jest.fn(),
      lockBlogTopic: jest.fn(),
    };

    mockCosmoClient = {
      selectBlogTopic: jest.fn(),
      lockBlogTopic: jest.fn(),
      unlockBlogTopic: jest.fn(),
    };

    (ApiGatewayClient as jest.Mock).mockImplementation(
      () => mockApiGatewayClient,
    );
    (CosmoClient as jest.Mock).mockImplementation(() => mockCosmoClient);
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.API_GATEWAY_URL;
    delete process.env.API_GATEWAY_SUPER_USER_COMPANY_ID;
    delete process.env.API_GATEWAY_KEY;
    delete process.env.COSMO_GQL_URL;
    delete process.env.M2M_SUPER_API_KEY;
  });

  describe('handler', () => {
    const createMockEvent = (overrides = {}) => ({
      companyId: 'company-1',
      brandProfile: {
        companyId: 'company-1',
        aboutTheBrand: 'Test Company provides excellent business services',
        strategicFocus: 'Customer satisfaction',
        valueProposition: 'Quality and reliability',
        idealCustomerProfiles: 'Small businesses',
        missionAndCoreValues: 'Excellence and integrity',
        brandPointOfView: 'Business-focused approach',
        toneOfVoice: 'Professional and friendly',
        ctaText: 'Get started today',
        authorPersona: 'Business expert',
      } as BrandProfile,
      neighborhoods: [
        {
          id: 'n1',
          preferredName: 'Downtown',
          googlePlacesName: 'Downtown District',
          isPreferred: false,
          priority: 2,
        },
        {
          id: 'n2',
          preferredName: 'Uptown',
          googlePlacesName: 'Uptown Area',
          isPreferred: false,
          priority: 3,
        },
      ] as Neighborhood[],
      preferredNeighborhoods: [
        {
          id: 'n1',
          preferredName: 'Downtown',
          googlePlacesName: 'Downtown District',
          isPreferred: true,
          priority: 1,
        },
      ] as Neighborhood[],
      dryRun: false,
      executionFolderName: 'test-execution',
      ...overrides,
    });

    it('should return mock topic in dry run mode', async () => {
      const event = createMockEvent({ dryRun: true });

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyId: 'company-1',
        selectedTopic: expect.objectContaining({
          id: 'mock-topic-1',
          topic: 'Top 10 Things to Do in Downtown',
          title: 'Top 10 Things to Do in Downtown',
          keywords: ['Top', '10', 'Things', 'to', 'Do', 'in', 'Downtown'],
          neighborhood: expect.objectContaining({
            id: 'n1',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
          }),
          priority: 1,
          type: 'ARTICLE',
        }),
        selectionReason: 'Mock topic selected for dry run',
      });

      expect(mockCosmoClient.selectBlogTopic).not.toHaveBeenCalled();
    });

    it('should select topic from available topics', async () => {
      const event = createMockEvent();

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Best Restaurants',
        blogTitle: 'Best Restaurants in Downtown',
        rationale: 'Selected for preferred neighborhood',
        type: 'ARTICLE',
        neighborhoodId: 'n1',
        parentTopic: 'restaurants',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      // Mock selectBlogTopic to return a topic
      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.companyId).toBe('company-1');
      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectedTopic.id).toBe('topic-1');
      expect(result!.selectedTopic.topic).toBe('Best Restaurants');
      expect(result!.selectedTopic.title).toBe('Best Restaurants in Downtown');
      expect(result!.selectedTopic.neighborhood?.id).toBe('n1');
      expect(result!.selectionReason).toContain('preferred neighborhood');
      expect(mockCosmoClient.selectBlogTopic).toHaveBeenCalledWith(
        'company-1',
        ['n1'],
      );
    });

    it('should throw NoAvailableTopicsError when no topics available', async () => {
      const event = createMockEvent();

      // Mock selectBlogTopic to return null (no topics available)
      mockCosmoClient.selectBlogTopic.mockResolvedValue(null);

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'No available blog topics for company company-1',
      );

      expect(mockCosmoClient.selectBlogTopic).toHaveBeenCalledWith(
        'company-1',
        ['n1'],
      );
    });

    it('should prioritize topics for preferred neighborhoods', async () => {
      const event = createMockEvent();

      const mockTopic: BlogTopic = {
        id: 'topic-2',
        companyId: 'company-1',
        topic: 'Downtown Special Events',
        blogTitle: 'Best Events in Downtown',
        rationale: 'Selected for preferred neighborhood Downtown',
        type: 'ARTICLE',
        neighborhoodId: 'n1',
        parentTopic: 'events',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic.neighborhood?.id).toBe('n1');
      expect(result!.selectionReason).toContain('preferred neighborhood');
      expect(mockCosmoClient.selectBlogTopic).toHaveBeenCalledWith(
        'company-1',
        ['n1'],
      );
    });

    it('should select available topic based on GraphQL service logic', async () => {
      const event = createMockEvent();

      const mockTopic: BlogTopic = {
        id: 'topic-2',
        companyId: 'company-1',
        topic: 'Never Used Topic',
        blogTitle: 'Fresh Content Ideas',
        rationale: 'Selected as new topic never used before',
        type: 'ARTICLE',
        neighborhoodId: 'n2',
        parentTopic: 'activities',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic.id).toBe('topic-2');
      expect(result!.selectedTopic.topic).toBe('Never Used Topic');
      expect(result!.selectionReason).toContain('never used before');
    });

    it('should handle API errors gracefully', async () => {
      const event = createMockEvent();

      // Mock selectBlogTopic to throw an error
      mockCosmoClient.selectBlogTopic.mockRejectedValue(
        new Error('API connection failed'),
      );

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'Failed to select blog topic',
      );

      expect(mockCosmoClient.selectBlogTopic).toHaveBeenCalledWith(
        'company-1',
        ['n1'],
      );
    });

    it('should handle empty preferred neighborhoods', async () => {
      const event = createMockEvent({ preferredNeighborhoods: [] });

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'General Topic',
        blogTitle: 'General Content',
        type: 'ARTICLE',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectionReason).not.toContain('preferred neighborhood');
      expect(mockCosmoClient.selectBlogTopic).toHaveBeenCalledWith(
        'company-1',
        undefined,
      );
    });

    // This test is now covered above - removing duplicate

    it('should handle topic selection with rationale', async () => {
      const event = createMockEvent();

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Shopping Areas Guide',
        blogTitle: 'Best Shopping in the City',
        rationale:
          'Selected to avoid recent restaurant posts and diversify content',
        type: 'ARTICLE',
        neighborhoodId: 'n2',
        parentTopic: 'shopping',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectionReason).toContain(
        'avoid recent restaurant posts',
      );
    });

    it('should handle topics with multiple neighborhoods', async () => {
      const event = createMockEvent({
        neighborhoods: [
          {
            id: 'n1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown',
            isPreferred: false,
            priority: 1,
          },
          {
            id: 'n2',
            name: 'Uptown',
            preferredName: 'Uptown',
            googlePlacesName: 'Uptown',
            isPreferred: false,
            priority: 2,
          },
          {
            id: 'n3',
            name: 'Midtown',
            preferredName: 'Midtown',
            googlePlacesName: 'Midtown',
            isPreferred: false,
            priority: 3,
          },
        ],
      });

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'City Guide',
        blogTitle: 'Complete City Guide',
        type: 'ARTICLE',
        neighborhoodId: 'n2',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectedTopic.neighborhood?.id).toBe('n2');
    });

    it('should log appropriate messages during execution', async () => {
      const event = createMockEvent({ dryRun: true });

      await handler(event, {} as never, {} as never);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Selecting blog topic',
        expect.any(Object),
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Dry run mode - returning mock blog topic',
      );
    });

    it('should handle topics with minimal fields', async () => {
      const event = createMockEvent();

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Minimal Topic',
        type: 'ARTICLE',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        // No blogTitle, rationale, neighborhoodId, etc.
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectedTopic.title).toBe('Minimal Topic'); // Uses topic as title
      expect(result!.selectedTopic.neighborhood).toBeUndefined(); // No matching neighborhood
      expect(result!.selectionReason).toContain('random selection');
    });

    it('should handle dry run with no preferred neighborhoods', async () => {
      const event = createMockEvent({
        dryRun: true,
        preferredNeighborhoods: [],
        neighborhoods: [{ id: 'n1', name: 'Default', isPreferred: false }],
      });

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic.neighborhood).toEqual({
        id: 'n1',
        name: 'Default',
        isPreferred: false,
      });
    });

    it('should lock topic when publishToCMS is true and not dryRun', async () => {
      const event = createMockEvent({
        dryRun: false,
        publishToCMS: true,
      });

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Test Topic',
        blogTitle: 'Test Blog Title',
        type: 'ARTICLE',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      const mockLockedTopic: BlogTopic = {
        ...mockTopic,
        cmsPostId: 'topic-1',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);
      mockCosmoClient.lockBlogTopic.mockResolvedValue(mockLockedTopic);

      const result = await handler(event, {} as never, {} as never);

      expect(mockCosmoClient.lockBlogTopic).toHaveBeenCalledWith(
        'topic-1',
        'topic-1',
      );
      expect(result!.selectedTopic.cmsPostId).toBe('topic-1');
    });

    it('should not lock topic when dryRun is true', async () => {
      const event = createMockEvent({
        dryRun: true,
        publishToCMS: true,
      });

      await handler(event, {} as never, {} as never);

      expect(mockCosmoClient.lockBlogTopic).not.toHaveBeenCalled();
    });

    it('should not lock topic when publishToCMS is false', async () => {
      const event = createMockEvent({
        dryRun: false,
        publishToCMS: false,
      });

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Test Topic',
        type: 'ARTICLE',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);

      await handler(event, {} as never, {} as never);

      expect(mockCosmoClient.lockBlogTopic).not.toHaveBeenCalled();
    });

    it('should continue if locking fails', async () => {
      const event = createMockEvent({
        dryRun: false,
        publishToCMS: true,
      });

      const mockTopic: BlogTopic = {
        id: 'topic-1',
        companyId: 'company-1',
        topic: 'Test Topic',
        type: 'ARTICLE',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockCosmoClient.selectBlogTopic.mockResolvedValue(mockTopic);
      mockCosmoClient.lockBlogTopic.mockRejectedValue(new Error('Lock failed'));

      const result = await handler(event, {} as never, {} as never);

      expect(result!.selectedTopic).toBeDefined();
      expect(result!.selectedTopic.cmsPostId).toBeUndefined();
    });
  });
});
