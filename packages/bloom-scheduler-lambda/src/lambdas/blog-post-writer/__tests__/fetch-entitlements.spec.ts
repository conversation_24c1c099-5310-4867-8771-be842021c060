// Set up environment variables before importing
process.env.API_GATEWAY_URL = 'https://test-api-gateway.com';
process.env.ENVIRONMENT = 'test';
process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'test-super-user';
process.env.API_GATEWAY_KEY = 'test-api-key';
process.env.EXECUTION_BUCKET_NAME = 'test-bucket';
process.env.AUTOMATED_BLOGS_PRODUCT_ID = 'test-product-id';
process.env.POST_PUBLISH_DAY_OF_WEEK = 'TUESDAY';
process.env.TENANT_SERVICE_URL = 'https://test-tenant-service.com';

import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { GraphQLClient } from 'graphql-request';
import * as LaunchDarkly from 'launchdarkly-node-server-sdk';

import { ApiGatewayClient } from '../../../clients/ApiGatewayClient';
import { TenantClient } from '../../../clients/TenantClient';
import { FetchEntitlementsError } from '../../../errors/blog-post-writer';
import { LaunchDarkly as LaunchDarklyUtil } from '../../../utils/LaunchDarkly';
import { handler, getNextPublishDate } from '../fetch-entitlements';

// Mock dependencies
jest.mock('@aws-sdk/client-s3');
jest.mock('graphql-request');
jest.mock('launchdarkly-node-server-sdk');
jest.mock('../../../clients/ApiGatewayClient');
jest.mock('../../../clients/TenantClient');
jest.mock('../../../utils/LaunchDarkly');
jest.mock('../../../utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

const MockedS3Client = S3Client as jest.MockedClass<typeof S3Client>;
const MockedTenantClient = TenantClient as jest.MockedClass<
  typeof TenantClient
>;

describe('FetchEntitlements Lambda', () => {
  const mockSend = jest.fn();
  const mockGraphQLRequest = jest.fn();
  const mockGetAllEntitlements = jest.fn();
  let mockLDClient: {
    waitForInitialization: jest.Mock;
    variation: jest.Mock;
    close: jest.Mock;
  };
  let mockCheckVariationBatch: jest.Mock;
  let mockCloseConnection: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock S3 client
    MockedS3Client.prototype.send = mockSend;
    mockSend.mockResolvedValue({});

    // Mock LaunchDarkly client (legacy)
    mockLDClient = {
      waitForInitialization: jest.fn().mockResolvedValue(undefined),
      variation: jest.fn().mockResolvedValue(true),
      close: jest.fn().mockResolvedValue(undefined),
    };
    (LaunchDarkly.init as jest.Mock).mockReturnValue(mockLDClient);

    // Mock LaunchDarkly utility class
    mockCheckVariationBatch = jest.fn();
    mockCloseConnection = jest.fn();

    // Default: all companies enabled
    mockCheckVariationBatch.mockImplementation(
      (_flagName, _defaultValue, companyIds) => {
        const results = new Map();
        companyIds.forEach((companyId: string) => results.set(companyId, true));
        return Promise.resolve(results);
      },
    );

    (LaunchDarklyUtil.checkVariationBatch as jest.Mock) =
      mockCheckVariationBatch;
    (LaunchDarklyUtil.closeConnection as jest.Mock) = mockCloseConnection;

    // Mock TenantClient
    (MockedTenantClient as jest.Mock).mockImplementation(() => ({
      getAllEntitlements: mockGetAllEntitlements,
    }));

    // Default mock implementation for TenantClient
    const futureDate = new Date(
      Date.now() + 30 * 24 * 60 * 60 * 1000,
    ).toISOString(); // 30 days from now

    mockGetAllEntitlements.mockResolvedValue([
      {
        companyId: 'company-1',
        startDate: null,
        endDate: null,
        productId: 'test-product-id',
        units: 2, // 2 units for testing biweekly scheduling
      },
      {
        companyId: 'company-2',
        startDate: null,
        endDate: futureDate,
        productId: 'test-product-id',
        units: 4, // 4 units for testing monthly scheduling
      },
      {
        companyId: 'entitled-company-1',
        startDate: null,
        endDate: null,
        productId: 'test-product-id',
        units: 4,
      },
      {
        companyId: 'entitled-company-2',
        startDate: null,
        endDate: null,
        productId: 'test-product-id',
        units: 2,
      },
      {
        companyId: 'company-with-ai-post',
        startDate: null,
        endDate: null,
        productId: 'test-product-id',
        units: 4,
      },
    ]);

    // Mock GraphQL client for scheduled posts queries
    (GraphQLClient as jest.Mock).mockImplementation(() => ({
      request: mockGraphQLRequest,
    }));

    // Default mock for GraphQL (used for scheduled posts check)
    mockGraphQLRequest.mockImplementation(
      (query: string, _variables: unknown) => {
        if (query.includes('CheckScheduledAiPosts')) {
          // Default: no scheduled posts found
          return Promise.resolve({
            posts: {
              items: [],
              total: 0,
            },
          });
        }
        // Default response for other queries
        return Promise.resolve({});
      },
    );

    // Mock ApiGatewayClient
    (ApiGatewayClient as jest.Mock).mockImplementation(() => ({
      getCompanyBrands: jest.fn().mockResolvedValue([]),
    }));
  });

  describe('handler', () => {
    it('should fetch entitlements and save to S3 in dry run mode', async () => {
      const event = {
        companyIds: ['company-1', 'company-2'],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      try {
        const result = await handler(event, {} as never, {} as never);

        expect(result).toMatchObject({
          companyIds: expect.arrayContaining(['company-1', 'company-2']),
          dryRun: true,
          executionName: 'test-execution',
          executionStartTime: '2024-01-01T00:00:00Z',
          s3Key: expect.stringContaining('blog-post-jobs/2024-01-01/'),
          eligibleCompanies: 2,
          totalEntitlements: 2,
        });

        expect(mockSend).toHaveBeenCalledTimes(2); // Now saves both entitlements.json and execution-metadata.json
        expect(mockSend).toHaveBeenNthCalledWith(
          1,
          expect.any(PutObjectCommand),
        );
        expect(mockSend).toHaveBeenNthCalledWith(
          2,
          expect.any(PutObjectCommand),
        );
      } catch (error) {
        console.error('Test error:', error);
        throw error;
      }
    });

    it('should check LaunchDarkly feature flags when not in dry run', async () => {
      process.env.LAUNCHDARKLY_KEY = 'test-key';

      const event = {
        companyIds: ['company-1', 'company-2'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      // Mock batch result: company-1 enabled, company-2 disabled
      mockCheckVariationBatch.mockImplementationOnce(() => {
        const results = new Map();
        results.set('company-1', true);
        results.set('company-2', false);
        return Promise.resolve(results);
      });

      const result = await handler(event, {} as never, {} as never);

      expect(mockCheckVariationBatch).toHaveBeenCalledTimes(1);
      expect(mockCheckVariationBatch).toHaveBeenCalledWith(
        'enable-super-bloom',
        false,
        ['company-1', 'company-2'],
        false, // dryRun = false
      );

      // Only company-1 should be eligible
      expect(result!.eligibleCompanies).toBe(1);
      expect(result!.companyIds).toEqual(['company-1']);
    });

    it('should throw error when no eligible companies found', async () => {
      // Mock TenantClient to return no entitled companies
      mockGetAllEntitlements.mockResolvedValueOnce([]);

      const event = {
        companyIds: [],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        FetchEntitlementsError,
      );
    });

    it('should handle LaunchDarkly client errors gracefully in dry run', async () => {
      const event = {
        companyIds: ['company-1'],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      (LaunchDarkly.init as jest.Mock).mockImplementation(() => {
        throw new Error('LD init failed');
      });

      const result = await handler(event, {} as never, {} as never);

      expect(result!.eligibleCompanies).toBeGreaterThan(0);
    });

    it('should generate correct S3 key structure', async () => {
      const event = {
        companyIds: ['company-1'],
        dryRun: true,
        executionName: 'test-execution-123',
        executionStartTime: '2024-03-15T14:30:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result!.s3Key).toMatch(
        /^blog-post-jobs\/2024-03-15\/test-execution-123-2024-03-15T14-30-00Z\/entitlements\.json$/,
      );
    });

    it('should close LaunchDarkly client on completion', async () => {
      process.env.LAUNCHDARKLY_KEY = 'test-key';

      const event = {
        companyIds: ['company-1'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      await handler(event, {} as never, {} as never);

      expect(mockCloseConnection).toHaveBeenCalled();
    });

    it('should fetch entitled companies even in dry run mode when no companyIds provided', async () => {
      const event = {
        companyIds: [],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      // Should fetch entitled companies from TenantClient in dry run mode
      expect(mockGetAllEntitlements).toHaveBeenCalledTimes(1);

      // Should return entitled companies
      expect(result).toMatchObject({
        companyIds: expect.arrayContaining([
          'entitled-company-1',
          'entitled-company-2',
        ]),
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        eligibleCompanies: 5, // All 5 companies in mock response
        totalEntitlements: 5,
      });

      expect(mockSend).toHaveBeenCalledTimes(2); // Now saves both entitlements.json and execution-metadata.json
    });

    it('should validate provided companyIds against entitled companies list', async () => {
      // Mock TenantClient to return specific entitled companies
      mockGetAllEntitlements.mockResolvedValueOnce([
        {
          companyId: 'entitled-company-1',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 4,
        },
        {
          companyId: 'entitled-company-2',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 2,
        },
      ]);

      const event = {
        companyIds: [
          'entitled-company-1',
          'invalid-company-1',
          'entitled-company-2',
        ],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      // Should validate provided company IDs against entitlements using TenantClient
      expect(mockGetAllEntitlements).toHaveBeenCalledTimes(1);

      // Should only process valid entitled companies
      expect(result).toMatchObject({
        companyIds: expect.arrayContaining([
          'entitled-company-1',
          'entitled-company-2',
        ]),
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        eligibleCompanies: 2,
        totalEntitlements: 2,
      });

      // invalid-company-1 should be filtered out
      expect(result!.companyIds).not.toContain('invalid-company-1');
    });

    it('should throw error when no provided companyIds are entitled', async () => {
      // Mock TenantClient to return different companies than provided
      mockGetAllEntitlements.mockResolvedValueOnce([
        {
          companyId: 'other-company-1',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 4,
        },
        {
          companyId: 'other-company-2',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 2,
        },
      ]);

      const event = {
        companyIds: ['invalid-company-1', 'invalid-company-2'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        /Failed to validate provided company IDs against entitled companies|None of the provided companyIds are entitled for automated blogs/,
      );
    });

    it('should skip validation when skipEntitlementsValidation is true with provided companyIds', async () => {
      const event = {
        companyIds: ['any-company-1', 'any-company-2'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        skipEntitlementsValidation: true,
      };

      const result = await handler(event, {} as never, {} as never);

      // Should NOT call TenantClient to validate entitlements
      expect(mockGetAllEntitlements).not.toHaveBeenCalled();

      // Should process all provided companies
      expect(result).toMatchObject({
        companyIds: expect.arrayContaining(['any-company-1', 'any-company-2']),
        dryRun: false,
        eligibleCompanies: 2,
        totalEntitlements: 2,
      });
    });

    it('should skip companies when AI-generated post already scheduled for next occurrence', async () => {
      // Mock a company that has an AI-generated post scheduled for the next occurrence
      const event = {
        companyIds: ['company-with-ai-post'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      // Mock TenantClient to return the company in question
      mockGetAllEntitlements.mockResolvedValueOnce([
        {
          companyId: 'company-with-ai-post',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 4,
        },
      ]);

      // Mock GraphQL response to indicate AI-generated posts exist
      mockGraphQLRequest.mockImplementation(
        (query: string, variables: unknown) => {
          if (query.includes('CheckScheduledAiPosts')) {
            return Promise.resolve({
              posts: {
                items: [
                  {
                    id: 'existing-post-123',
                    scheduledAt: (variables as Record<string, unknown>)
                      .publishDateGTE as string,
                    aiGenerated: true,
                  },
                ],
                total: 1,
              },
            });
          }
          return Promise.resolve({});
        },
      );

      // Should throw error when all companies are skipped and none are eligible
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'No eligible companies found for blog post generation',
      );

      // Verify the GraphQL check was called for scheduled posts
      expect(mockGraphQLRequest).toHaveBeenCalledWith(
        expect.stringContaining('CheckScheduledAiPosts'),
        expect.objectContaining({
          companyId: 'company-with-ai-post',
          publishDateGTE: expect.any(String),
          publishDateLTE: expect.any(String),
        }),
      );
    });

    it('should respect skipExistingScheduledPostValidation flag for AI-generated post check', async () => {
      const event = {
        companyIds: ['company-with-ai-post'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        skipExistingScheduledPostValidation: true, // This should bypass AI post check
      };

      const result = await handler(event, {} as never, {} as never);

      // Should process the company (assuming other validations pass)
      expect(result).toBeDefined();
      expect(result!.eligibleCompanies).toBeGreaterThan(0);
      expect(result!.skippedCompanies.aiGeneratedPostAlreadyScheduled).toBe(0);

      // Verify that the GraphQL call for checking scheduled posts was NOT made
      expect(mockGraphQLRequest).not.toHaveBeenCalledWith(
        expect.stringContaining('CheckScheduledAiPosts'),
        expect.any(Object),
      );
    });

    it('should filter out expired entitlements using fallback validation', async () => {
      const event = {
        companyIds: [],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const pastDate = new Date(
        Date.now() - 30 * 24 * 60 * 60 * 1000,
      ).toISOString(); // 30 days ago
      const futureDate = new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000,
      ).toISOString(); // 30 days from now

      // Mock TenantClient to return mixed active/expired entitlements
      mockGetAllEntitlements.mockResolvedValueOnce([
        {
          companyId: 'active-company-1',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 2,
        }, // Active indefinitely
        {
          companyId: 'active-company-2',
          startDate: null,
          endDate: futureDate,
          productId: 'test-product-id',
          units: 4,
        }, // Active until future
        {
          companyId: 'expired-company-1',
          startDate: null,
          endDate: pastDate,
          productId: 'test-product-id',
          units: 4,
        }, // Expired
      ]);

      const result = await handler(event, {} as never, {} as never);

      expect(result).toBeDefined();
      // Should only include the 2 active companies, not the expired one
      expect(result!.eligibleCompanies).toBe(2);
      expect(result!.totalEntitlements).toBe(2);

      // Verify that fallback was used and filtering was applied correctly
      expect(result!.companyIds).toEqual(
        expect.arrayContaining(['active-company-1', 'active-company-2']),
      );
      expect(result!.companyIds).not.toContain('expired-company-1');
    });

    it('should use TenantClient to fetch entitled companies with date filtering', async () => {
      const event = {
        companyIds: [],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      // Mock TenantClient to return active companies
      mockGetAllEntitlements.mockResolvedValueOnce([
        {
          companyId: 'active-company-1',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 2,
        },
        {
          companyId: 'active-company-2',
          startDate: null,
          endDate: null,
          productId: 'test-product-id',
          units: 4,
        },
      ]);

      const result = await handler(event, {} as never, {} as never);

      expect(result).toBeDefined();
      expect(result!.eligibleCompanies).toBe(2);

      // Verify TenantClient was called to fetch entitlements
      expect(mockGetAllEntitlements).toHaveBeenCalledTimes(1);
    });

    it('should close LaunchDarkly client even on error', async () => {
      process.env.LAUNCHDARKLY_KEY = 'test-key';
      // Mock TenantClient to throw error to test error handling
      mockGetAllEntitlements.mockRejectedValueOnce(
        new Error('TenantClient error'),
      );

      const event = {
        companyIds: [],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      try {
        await handler(event, {} as never, {} as never);
      } catch {
        // Expected to throw
      }

      expect(mockCloseConnection).toHaveBeenCalled();
    });

    it('should handle skip validation flags correctly', async () => {
      const event = {
        companyIds: ['company-1', 'company-2'],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        skipEntitlementsValidation: true,
        skipUnitsValidation: true,
        skipExistingScheduledPostValidation: true,
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toMatchObject({
        companyIds: expect.arrayContaining(['company-1', 'company-2']),
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        eligibleCompanies: 2,
        totalEntitlements: 2,
        skippedCompanies: {
          noEntitlements: 0,
          insufficientUnits: 0,
          existingScheduledPost: 0,
          launchDarklyDisabled: 0,
        },
      });

      expect(mockSend).toHaveBeenCalledTimes(2); // Now saves both entitlements.json and execution-metadata.json
    });

    it('should include skip counters in results', async () => {
      const event = {
        companyIds: ['company-1'],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toBeDefined();
      expect(
        (result as typeof result & { skippedCompanies: object })
          .skippedCompanies,
      ).toEqual({
        noEntitlements: 0,
        insufficientUnits: 0,
        existingScheduledPost: 0,
        launchDarklyDisabled: 0,
        aiGeneratedPostAlreadyScheduled: 0,
      });
    });

    it('should calculate next publish date for entitled companies', async () => {
      const event = {
        companyIds: ['company-1'],
        dryRun: true,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toMatchObject({
        companyIds: ['company-1'],
        dryRun: true,
        eligibleCompanies: 1,
        totalEntitlements: 1,
      });

      // Verify S3 was called with the data
      expect(mockSend).toHaveBeenCalledWith(expect.any(PutObjectCommand));
    });

    it('should bypass entitlements validation but still check LaunchDarkly when skipEntitlementsValidation=true', async () => {
      process.env.LAUNCHDARKLY_KEY = 'test-key';

      const event = {
        companyIds: ['company-1'],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        skipEntitlementsValidation: true,
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toMatchObject({
        companyIds: ['company-1'],
        dryRun: false,
        eligibleCompanies: 1,
        totalEntitlements: 1,
      });

      // Should have checked LaunchDarkly batch method
      expect(mockCheckVariationBatch).toHaveBeenCalled();

      // Should have bypassed actual entitlements fetch and used placeholder
      expect(mockSend).toHaveBeenCalledWith(expect.any(PutObjectCommand));
    });

    it('should throw error when skipEntitlementsValidation=true but no companyIds provided', async () => {
      const event = {
        companyIds: [],
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        skipEntitlementsValidation: true,
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'Cannot skip entitlements validation without providing specific companyIds',
      );
    });

    it('should throw error when required environment variables are missing', async () => {
      // Save original env vars
      const originalEnvVars = {
        API_GATEWAY_URL: process.env.API_GATEWAY_URL,
        API_GATEWAY_SUPER_USER_COMPANY_ID:
          process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
        API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
        AUTOMATED_BLOGS_PRODUCT_ID: process.env.AUTOMATED_BLOGS_PRODUCT_ID,
        TENANT_SERVICE_URL: process.env.TENANT_SERVICE_URL,
      };

      try {
        // Remove one required env var (use TENANT_SERVICE_URL as it's now required)
        delete process.env.TENANT_SERVICE_URL;

        const event = {
          companyIds: ['company-1'],
          dryRun: false,
          executionName: 'test-execution',
          executionStartTime: '2024-01-01T00:00:00Z',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          FetchEntitlementsError,
        );

        try {
          await handler(event, {} as never, {} as never);
          fail('Should have thrown an error');
        } catch (error) {
          expect(error).toBeInstanceOf(FetchEntitlementsError);
          // The error now occurs when creating TenantClient with missing env var
          expect((error as FetchEntitlementsError).message).toContain(
            'Failed to validate provided company IDs against entitled companies',
          );
        }
      } finally {
        // Restore original env vars
        Object.assign(process.env, originalEnvVars);
      }
    });
  });

  describe('getNextPublishDate', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return next Tuesday for 2 units if next Tuesday is 1st or 3rd of month', () => {
      // Mock Monday, November 6, 2023 (day before 1st Tuesday of November)
      const mockDate = new Date('2023-11-06T12:00:00Z');

      // Next Tuesday (Nov 7) is 1st Tuesday of month - should be scheduled for 2 units
      const result = getNextPublishDate(2, 'TUESDAY', mockDate);
      expect(result).toBe('2023-11-07'); // 1st Tuesday of November 2023
    });

    it('should return null for 2 units if next Tuesday is 2nd or 4th of month', () => {
      // Mock Monday, November 13, 2023 (day before 2nd Tuesday of November)
      const mockDate = new Date('2023-11-13T12:00:00Z');

      // Next Tuesday (Nov 14) is 2nd Tuesday of month - should not be scheduled for 2 units
      const result = getNextPublishDate(2, 'TUESDAY', mockDate);
      expect(result).toBe(null); // Not entitled for 2nd Tuesday
    });

    it('should return next Tuesday for 4 units if next Tuesday is 1st, 2nd, 3rd, or 4th of month', () => {
      // Test 1st Tuesday of month
      const beforeFirst = new Date('2023-11-06T12:00:00Z');
      const firstResult = getNextPublishDate(4, 'TUESDAY', beforeFirst);
      expect(firstResult).toBe('2023-11-07'); // 1st Tuesday of November 2023

      // Test 2nd Tuesday of month
      const beforeSecond = new Date('2023-11-13T12:00:00Z');
      const secondResult = getNextPublishDate(4, 'TUESDAY', beforeSecond);
      expect(secondResult).toBe('2023-11-14'); // 2nd Tuesday of November 2023

      // Test 3rd Tuesday of month
      const beforeThird = new Date('2023-11-20T12:00:00Z');
      const thirdResult = getNextPublishDate(4, 'TUESDAY', beforeThird);
      expect(thirdResult).toBe('2023-11-21'); // 3rd Tuesday of November 2023

      // Test 4th Tuesday of month
      const beforeFourth = new Date('2023-11-27T12:00:00Z');
      const fourthResult = getNextPublishDate(4, 'TUESDAY', beforeFourth);
      expect(fourthResult).toBe('2023-11-28'); // 4th Tuesday of November 2023
    });

    it('should schedule for next week when run on the target day (Tuesday)', () => {
      // Mock Tuesday, November 7, 2023 (1st Tuesday of November)
      const mockDate = new Date('2023-11-07T12:00:00Z');

      // When run on Tuesday, should schedule for next Tuesday to allow review time
      const result = getNextPublishDate(4, 'TUESDAY', mockDate);
      expect(result).toBe('2023-11-14'); // Next Tuesday (2nd Tuesday of November)

      // Test with 2 units - next Tuesday (Nov 14) is 2nd Tuesday, so should be null
      const result2Units = getNextPublishDate(2, 'TUESDAY', mockDate);
      expect(result2Units).toBe(null); // 2 units not entitled for 2nd Tuesday
    });

    it('should handle different target days (Friday) correctly with weekly logic', () => {
      // Mock Wednesday, November 1, 2023
      const mockDate = new Date('2023-11-01T12:00:00Z');

      // Target Friday with 2 units - Nov 3 is 1st Friday, should be scheduled
      const result = getNextPublishDate(2, 'FRIDAY', mockDate);
      expect(result).toBe('2023-11-03'); // 1st Friday of November 2023

      // Target Friday with 4 units should also schedule for 1st Friday
      const fourUnitsResult = getNextPublishDate(4, 'FRIDAY', mockDate);
      expect(fourUnitsResult).toBe('2023-11-03'); // 1st Friday of November 2023

      // Test from Thursday Nov 9 - next Friday (Nov 10) is 2nd Friday, 2 units should be null
      const beforeSecondFriday = new Date('2023-11-09T12:00:00Z');
      const afterResult = getNextPublishDate(2, 'FRIDAY', beforeSecondFriday);
      expect(afterResult).toBe(null); // 2 units not entitled for 2nd Friday
    });

    it('should handle edge cases with 6-day and 7-day limits', () => {
      // Test Wednesday before Tuesday - 6 days until next Tuesday
      const wednesday = new Date('2023-11-01T12:00:00Z'); // Wednesday, Nov 1
      const result = getNextPublishDate(4, 'TUESDAY', wednesday);
      expect(result).toBe('2023-11-07'); // Next Tuesday (6 days away)

      // Test Saturday before Tuesday - 3 days until next Tuesday
      const saturday = new Date('2023-11-04T12:00:00Z'); // Saturday, Nov 4
      const satResult = getNextPublishDate(4, 'TUESDAY', saturday);
      expect(satResult).toBe('2023-11-07'); // Next Tuesday (3 days away)
    });

    it('should default to weekly scheduling for unknown units (always entitled)', () => {
      // Mock Monday before Tuesday
      const mockDate = new Date('2023-11-06T12:00:00Z');

      // Unknown units (e.g., 3) should default to weekly - always entitled
      const result = getNextPublishDate(3, 'TUESDAY', mockDate);
      expect(result).toBe('2023-11-07'); // Next Tuesday

      // Test for units = 1 (should also be weekly - always entitled)
      const weeklyResult = getNextPublishDate(1, 'TUESDAY', mockDate);
      expect(weeklyResult).toBe('2023-11-07'); // Next Tuesday

      // Test from Tuesday itself - should go to next week for review time
      const tuesdayDate = new Date('2023-11-07T12:00:00Z');
      const fromTuesday = getNextPublishDate(1, 'TUESDAY', tuesdayDate);
      expect(fromTuesday).toBe('2023-11-14'); // Next Tuesday (1 week out)
    });
  });
});
