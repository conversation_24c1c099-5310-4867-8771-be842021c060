import { ApiGatewayClient } from '../../../clients/ApiGatewayClient';
import { CosmoClient } from '../../../clients/CosmoClient';
import { handler } from '../get-brand';

// Mock logger
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
};

jest.mock('../../../utils/contextLogger', () => ({
  createContextLogger: jest.fn(() => mockLogger),
}));

// Mock dependencies
jest.mock('../../../clients/ApiGatewayClient');
jest.mock('../../../clients/CosmoClient');

describe('GetBrand Lambda', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockApiGatewayClient: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockCosmoClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.debug.mockClear();

    // Set up required environment variables for tests
    process.env.API_GATEWAY_URL = 'https://test-graphql-endpoint.com';
    process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'test-super-user';
    process.env.API_GATEWAY_KEY = 'test-api-key';
    process.env.COSMO_GQL_URL = 'https://test-cosmo-endpoint.com';
    process.env.M2M_SUPER_API_KEY = 'test-m2m-key';

    // Mock ApiGatewayClient with all neighborhood-related methods
    mockApiGatewayClient = {
      getBrandProfile: jest.fn(),
      getNeighborhoodsWithDetails: jest.fn(),
      getNeighborhoodsWithPreferences: jest.fn(),
      getNeighborhoodsWithGooglePlaces: jest.fn(),
      getBrandBlogPreferences: jest.fn(),
    };

    mockCosmoClient = {
      getBrandProfile: jest.fn(),
      selectBlogTopic: jest.fn(),
      updateBlogTopicAiropsId: jest.fn(),
      getNeighborhoodsWithPreferences: jest.fn(),
      getNeighborhoodsWithDetails: jest.fn(),
      lockBlogTopic: jest.fn(),
      unlockBlogTopic: jest.fn(),
    };

    (ApiGatewayClient as jest.Mock).mockImplementation(
      () => mockApiGatewayClient,
    );
    (CosmoClient as jest.Mock).mockImplementation(() => mockCosmoClient);
  });

  afterEach(() => {
    // Clean up environment variables to avoid test pollution
    delete process.env.API_GATEWAY_URL;
    delete process.env.API_GATEWAY_SUPER_USER_COMPANY_ID;
    delete process.env.API_GATEWAY_KEY;
    delete process.env.COSMO_GQL_URL;
    delete process.env.M2M_SUPER_API_KEY;
  });

  describe('handler', () => {
    it('should return mock brand profile in dry run mode', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: true,
        executionFolderName: 'test-execution',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyId: 'company-1',
        brandProfile: {
          companyId: 'company-1',
          aboutTheBrand:
            'Test Company company-1 specializes in providing excellent services to our community.',
          strategicFocus:
            'Delivering high-quality services with a focus on customer satisfaction and community engagement.',
          valueProposition:
            'We offer unique solutions that combine expertise, reliability, and local knowledge.',
          idealCustomerProfiles:
            'Local residents and businesses seeking trusted, professional services in their area.',
          missionAndCoreValues:
            'Our mission is to serve our community with integrity, excellence, and dedication to quality.',
          brandPointOfView:
            'We believe in building lasting relationships through exceptional service and community involvement.',
          toneOfVoice: 'Professional, friendly, and approachable',
          ctaText: 'Contact us today to learn more',
          authorPersona:
            'Local business expert with deep community knowledge and industry expertise',
        },
        neighborhoods: [
          {
            id: 'neighborhood-1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            priority: 1,
          },
          {
            id: 'neighborhood-2',
            name: 'Midtown',
            preferredName: 'Midtown',
            googlePlacesName: 'Midtown Area',
            priority: 2,
          },
        ],
        preferredNeighborhoods: [
          {
            id: 'neighborhood-1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            priority: 1,
          },
        ],
        hasProfile: true,
      });

      expect(mockCosmoClient.getBrandProfile).not.toHaveBeenCalled();
      expect(
        mockCosmoClient.getNeighborhoodsWithDetails,
      ).not.toHaveBeenCalled();
    });

    it('should fetch brand profile and neighborhoods from API', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Real Company provides excellent services',
        strategicFocus: 'Customer satisfaction',
        valueProposition: 'Quality services',
        idealCustomerProfiles: 'Small businesses',
        missionAndCoreValues: 'Excellence and integrity',
        brandPointOfView: 'Customer-focused approach',
        toneOfVoice: 'Professional',
        ctaText: 'Get started today',
        authorPersona: 'Industry expert',
      };

      const mockNeighborhoods = [
        {
          id: 'n1',
          name: 'Downtown',
          preferredName: 'Downtown',
          googlePlacesName: 'Downtown District',
          isPreferred: true,
        },
        {
          id: 'n2',
          name: 'Uptown',
          preferredName: 'Uptown',
          googlePlacesName: 'Uptown Area',
          isPreferred: false,
        },
      ];

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        mockNeighborhoods,
      );

      const result = await handler(event, {} as never, {} as never);

      // Expect the CosmoClient to be called for brand profile and ApiGatewayClient for neighborhoods
      expect(mockCosmoClient.getBrandProfile).toHaveBeenCalledWith('company-1');
      expect(
        mockApiGatewayClient.getNeighborhoodsWithGooglePlaces,
      ).toHaveBeenCalledWith('company-1');

      // Expect real API data structure
      expect(result).toEqual({
        companyId: 'company-1',
        brandProfile: {
          companyId: 'company-1',
          aboutTheBrand: 'Real Company provides excellent services',
          strategicFocus: 'Customer satisfaction',
          valueProposition: 'Quality services',
          idealCustomerProfiles: 'Small businesses',
          missionAndCoreValues: 'Excellence and integrity',
          brandPointOfView: 'Customer-focused approach',
          toneOfVoice: 'Professional',
          ctaText: 'Get started today',
          authorPersona: 'Industry expert',
        },
        neighborhoods: [
          {
            id: 'n1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            isPreferred: false,
            priority: 2,
            googlePlaceData: undefined,
            isPrimary: undefined,
            slug: undefined,
            description: undefined,
            coordinates: undefined,
          },
          {
            id: 'n2',
            name: 'Uptown',
            preferredName: 'Uptown',
            googlePlacesName: 'Uptown Area',
            isPreferred: false,
            priority: 2,
            googlePlaceData: undefined,
            isPrimary: undefined,
            slug: undefined,
            description: undefined,
            coordinates: undefined,
          },
        ],
        preferredNeighborhoods: [],
        hasProfile: true,
      });
    });

    it('should throw MissingBrandProfileError when brand profile not found', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      // Mock getBrandProfile to return null (no profile found)
      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(null);
      mockCosmoClient.getNeighborhoodsWithDetails.mockResolvedValueOnce([]);

      // Expect the handler to throw MissingBrandProfileError
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'Brand profile not found for company company-1',
      );

      expect(mockCosmoClient.getBrandProfile).toHaveBeenCalledWith('company-1');
    });

    it('should handle empty neighborhoods array', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company description',
        strategicFocus: 'Focus',
        valueProposition: 'Value',
        idealCustomerProfiles: 'Customers',
        missionAndCoreValues: 'Mission',
        brandPointOfView: 'Point of view',
        toneOfVoice: 'Voice',
        ctaText: 'CTA',
        authorPersona: 'Persona',
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        [],
      );

      const result = await handler(event, {} as never, {} as never);

      // Empty neighborhoods array should be returned
      expect(result!.neighborhoods).toEqual([]);
      expect(result!.preferredNeighborhoods).toEqual([]);
    });

    it('should filter preferred neighborhoods correctly', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company description',
        strategicFocus: 'Focus',
        valueProposition: 'Value',
        idealCustomerProfiles: 'Customers',
        missionAndCoreValues: 'Mission',
        brandPointOfView: 'Point of view',
        toneOfVoice: 'Voice',
        ctaText: 'CTA',
        authorPersona: 'Persona',
      };

      const mockNeighborhoods = [
        {
          id: 'n1',
          name: 'Area 1',
          preferredName: 'Area 1',
          googlePlacesName: 'Area 1 District',
          isPreferred: true,
          priority: 2,
        },
        {
          id: 'n2',
          name: 'Area 2',
          preferredName: 'Area 2',
          googlePlacesName: 'Area 2 District',
          isPreferred: false,
          priority: 3,
        },
        {
          id: 'n3',
          name: 'Area 3',
          preferredName: 'Area 3',
          googlePlacesName: 'Area 3 District',
          isPreferred: true,
          priority: 1,
        },
        {
          id: 'n4',
          name: 'Area 4',
          preferredName: 'Area 4',
          googlePlacesName: 'Area 4 District',
          isPreferred: true,
          priority: 4,
        },
      ];

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        mockNeighborhoods,
      );

      const result = await handler(event, {} as never, {} as never);

      // Should filter preferred neighborhoods correctly (now based on isPreferred=true)
      expect(result!.preferredNeighborhoods).toHaveLength(0);
      expect(result!.preferredNeighborhoods.map(n => n.id)).toEqual([]);
    });

    it('should handle API errors gracefully', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      // Mock API to throw errors
      mockCosmoClient.getBrandProfile.mockRejectedValueOnce(
        new Error('API Error'),
      );
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockRejectedValueOnce(
        new Error('API Error'),
      );

      // Should throw TransientGetBrandError when API fails
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'Failed to fetch brand profile for company company-1',
      );
    });

    it('should handle malformed API responses', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      // Mock API to return partial/malformed data
      const partialBrandProfile = {
        aboutTheBrand: 'Only partial data',
        // Missing other required fields
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(
        partialBrandProfile,
      );
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        [],
      );

      const result = await handler(event, {} as never, {} as never);

      // Should handle missing fields as undefined (since they're optional)
      expect(result!.brandProfile.aboutTheBrand).toBe('Only partial data');
      expect(result!.brandProfile.strategicFocus).toBeUndefined();
      expect(result!.brandProfile.valueProposition).toBeUndefined();
    });

    it('should log appropriate messages during execution', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: true,
        executionFolderName: 'test-execution',
      };

      await handler(event, {} as never, {} as never);

      // In dry run mode, it doesn't fetch, only logs "Dry run mode"
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Dry run mode - returning mock brand data',
      );
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Getting brand profile and neighborhoods',
        expect.any(Object),
      );
    });

    it('should use new neighborhoods method with preferences and Google Places validation', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company provides excellent services',
        strategicFocus: 'Customer satisfaction',
        valueProposition: 'Quality services',
        idealCustomerProfiles: 'Small businesses',
        missionAndCoreValues: 'Excellence and integrity',
        brandPointOfView: 'Customer-focused approach',
        toneOfVoice: 'Professional',
        ctaText: 'Get started today',
        authorPersona: 'Industry expert',
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        [
          {
            id: 'n1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            isPreferred: true,
            isPrimary: true,
            slug: 'downtown',
            description: 'City center',
            coordinates: { lat: 40.7589, lng: -73.9851 },
            googlePlaceData: {
              place_id: 'downtown-place-id',
              name: 'Downtown District',
            },
          },
          {
            id: 'n2',
            name: 'Uptown',
            preferredName: 'Uptown',
            googlePlacesName: 'Uptown Area',
            isPreferred: false,
            isPrimary: false,
            slug: 'uptown',
            googlePlaceData: {
              place_id: 'uptown-place-id',
              name: 'Uptown Area',
            },
          },
        ],
      );

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyId: 'company-1',
        brandProfile: {
          companyId: 'company-1',
          aboutTheBrand: 'Company provides excellent services',
          strategicFocus: 'Customer satisfaction',
          valueProposition: 'Quality services',
          idealCustomerProfiles: 'Small businesses',
          missionAndCoreValues: 'Excellence and integrity',
          brandPointOfView: 'Customer-focused approach',
          toneOfVoice: 'Professional',
          ctaText: 'Get started today',
          authorPersona: 'Industry expert',
        },
        neighborhoods: [
          {
            id: 'n1',
            name: 'Downtown',
            preferredName: 'Downtown',
            googlePlacesName: 'Downtown District',
            isPreferred: false,
            priority: 1,
            googlePlaceData: {
              place_id: 'downtown-place-id',
              name: 'Downtown District',
            },
            isPrimary: true,
            slug: 'downtown',
            description: 'City center',
            coordinates: { lat: 40.7589, lng: -73.9851 },
          },
          {
            id: 'n2',
            name: 'Uptown',
            preferredName: 'Uptown',
            googlePlacesName: 'Uptown Area',
            isPreferred: false,
            priority: 2,
            googlePlaceData: {
              place_id: 'uptown-place-id',
              name: 'Uptown Area',
            },
            isPrimary: false,
            slug: 'uptown',
            description: undefined,
            coordinates: undefined,
          },
        ],
        preferredNeighborhoods: [],
        hasProfile: true,
      });

      // Verify new method is called instead of legacy method
      expect(
        mockApiGatewayClient.getNeighborhoodsWithGooglePlaces,
      ).toHaveBeenCalledWith('company-1');
      expect(
        mockCosmoClient.getNeighborhoodsWithDetails,
      ).not.toHaveBeenCalled();
    });

    it('should return empty neighborhoods when Google Places method fails', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company description',
        strategicFocus: 'Focus',
        valueProposition: 'Value',
        idealCustomerProfiles: 'Customers',
        missionAndCoreValues: 'Mission',
        brandPointOfView: 'Point of view',
        toneOfVoice: 'Voice',
        ctaText: 'CTA',
        authorPersona: 'Persona',
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce({
        ...mockBrandProfile,
        companyId: 'company-1',
      });
      // Google Places method fails
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockRejectedValueOnce(
        new Error('Google Places method failed'),
      );

      const result = await handler(event, {} as never, {} as never);

      // Should return empty neighborhoods array when method fails
      expect(result!.neighborhoods).toEqual([]);
      expect(result!.preferredNeighborhoods).toEqual([]);

      // Google Places method should have been called
      expect(
        mockApiGatewayClient.getNeighborhoodsWithGooglePlaces,
      ).toHaveBeenCalledWith('company-1');
    });

    it('should handle Google Places method failures gracefully', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company description',
        strategicFocus: 'Focus',
        valueProposition: 'Value',
        idealCustomerProfiles: 'Customers',
        missionAndCoreValues: 'Mission',
        brandPointOfView: 'Point of view',
        toneOfVoice: 'Voice',
        ctaText: 'CTA',
        authorPersona: 'Persona',
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      // Google Places method fails
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockRejectedValueOnce(
        new Error('Google Places method failed'),
      );

      const result = await handler(event, {} as never, {} as never);

      // Should return empty neighborhoods array
      expect(result!.neighborhoods).toEqual([]);
      expect(result!.preferredNeighborhoods).toEqual([]);
    });

    it('should prioritize preferred neighborhoods correctly', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Company description',
        strategicFocus: 'Focus',
        valueProposition: 'Value',
        idealCustomerProfiles: 'Customers',
        missionAndCoreValues: 'Mission',
        brandPointOfView: 'Point of view',
        toneOfVoice: 'Voice',
        ctaText: 'CTA',
        authorPersona: 'Persona',
      };

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        [
          {
            id: 'preferred-1',
            name: 'Preferred Area 1',
            preferredName: 'Preferred Area 1',
            googlePlacesName: 'Preferred Area 1',
            isPreferred: true,
            isPrimary: true,
          },
          {
            id: 'other-1',
            name: 'Other Area 1',
            preferredName: 'Other Area 1',
            googlePlacesName: 'Other Area 1',
            isPreferred: false,
            isPrimary: false,
          },
          {
            id: 'preferred-2',
            name: 'Preferred Area 2',
            preferredName: 'Preferred Area 2',
            googlePlacesName: 'Preferred Area 2',
            isPreferred: true,
            isPrimary: true,
          },
        ],
      );

      const result = await handler(event, {} as never, {} as never);

      // Verify all neighborhoods are returned (no prioritization in new implementation)
      expect(result!.neighborhoods).toHaveLength(3);
      expect(result!.neighborhoods[0].id).toBe('preferred-1');
      expect(result!.neighborhoods[0].priority).toBe(1); // isPrimary=true -> priority=1
      expect(result!.neighborhoods[1].id).toBe('other-1');
      expect(result!.neighborhoods[1].priority).toBe(2); // isPrimary=false -> priority=2
      expect(result!.neighborhoods[2].id).toBe('preferred-2');
      expect(result!.neighborhoods[2].priority).toBe(1); // isPrimary=true -> priority=1

      // Verify preferred neighborhoods are empty (isPreferred is set to false in new implementation)
      expect(result!.preferredNeighborhoods).toHaveLength(0);
    });

    it('should handle undefined optional fields in brand profile', async () => {
      const event = {
        companyId: 'company-1',
        dryRun: false,
        executionFolderName: 'test-execution',
      };

      const mockBrandProfile = {
        aboutTheBrand: 'Minimal Company description',
        // Other fields undefined
      };

      const mockNeighborhoods = [
        {
          id: 'n1',
          name: 'Area',
          preferredName: 'Area',
          googlePlacesName: 'Area',
          isPreferred: false,
        },
        // Priority undefined
      ];

      mockCosmoClient.getBrandProfile.mockResolvedValueOnce(mockBrandProfile);
      mockApiGatewayClient.getNeighborhoodsWithGooglePlaces.mockResolvedValueOnce(
        mockNeighborhoods,
      );

      const result = await handler(event, {} as never, {} as never);

      // Should handle undefined fields as undefined (since they're optional)
      expect(result!.brandProfile.aboutTheBrand).toBe(
        'Minimal Company description',
      );
      expect(result!.brandProfile.strategicFocus).toBeUndefined();
      expect(result!.brandProfile.toneOfVoice).toBeUndefined();
      expect(result!.neighborhoods[0].isPreferred).toBe(false);
    });

    describe('environment variable validation', () => {
      it('should throw error when API_GATEWAY_URL is missing', async () => {
        delete process.env.API_GATEWAY_URL;

        const event = {
          companyId: 'company-1',
          dryRun: false,
          executionFolderName: 'test-execution',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          'Missing required environment variables: API_GATEWAY_URL',
        );
      });

      it('should throw error when API_GATEWAY_SUPER_USER_COMPANY_ID is missing', async () => {
        delete process.env.API_GATEWAY_SUPER_USER_COMPANY_ID;

        const event = {
          companyId: 'company-1',
          dryRun: false,
          executionFolderName: 'test-execution',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          'Missing required environment variables: API_GATEWAY_SUPER_USER_COMPANY_ID',
        );
      });

      it('should throw error when API_GATEWAY_KEY is missing', async () => {
        delete process.env.API_GATEWAY_KEY;

        const event = {
          companyId: 'company-1',
          dryRun: false,
          executionFolderName: 'test-execution',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          'Missing required environment variables: API_GATEWAY_KEY',
        );
      });

      it('should throw error with multiple missing environment variables', async () => {
        delete process.env.API_GATEWAY_URL;
        delete process.env.API_GATEWAY_KEY;

        const event = {
          companyId: 'company-1',
          dryRun: false,
          executionFolderName: 'test-execution',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          'Missing required environment variables: API_GATEWAY_URL, API_GATEWAY_KEY',
        );
      });

      it('should throw error when environment variables are empty strings', async () => {
        process.env.API_GATEWAY_URL = '';
        process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = '   '; // whitespace only

        const event = {
          companyId: 'company-1',
          dryRun: false,
          executionFolderName: 'test-execution',
        };

        await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
          'Missing required environment variables: API_GATEWAY_URL, API_GATEWAY_SUPER_USER_COMPANY_ID',
        );
      });
    });
  });
});
