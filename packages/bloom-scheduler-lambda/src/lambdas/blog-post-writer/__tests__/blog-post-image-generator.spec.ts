// Set up environment variables before importing the handler
process.env.CMS_SERVICE_URL = 'https://test-cms.example.com';
process.env.EXECUTION_BUCKET_NAME = 'test-execution-bucket';

import { S3Client } from '@aws-sdk/client-s3';
import { GoogleGenAI } from '@google/genai';
import { LangfuseClient } from '@langfuse/client';

import { CMSClient } from '../../../clients';
import { BlogPostImageGeneratorEvent } from '../blog-post-image-generator';

// Mock S3Client
jest.mock('@aws-sdk/client-s3');
const mockS3Client = {
  send: jest.fn().mockResolvedValue({}),
};

// Mock LangfuseClient
jest.mock('@langfuse/client');
const mockLangfuseClient = {
  prompt: {
    get: jest.fn(),
  },
};

// Mock GoogleGenAI
jest.mock('@google/genai');
const mockGoogleGenAI = {
  models: {
    generateImages: jest.fn(),
  },
};

// Mock CMSClient
jest.mock('../../../clients');
const mockCMSClient = {
  createMedia: jest.fn(),
};

// Mock OpenAI
jest.mock('openai');

// Mock Langfuse tracing
jest.mock('@langfuse/tracing', () => ({
  startActiveObservation: jest.fn((_name, fn) => fn({ update: jest.fn() })),
  updateActiveTrace: jest.fn(),
}));

// Mock observeOpenAI
jest.mock('@langfuse/openai', () => ({
  observeOpenAI: jest.fn(() => ({
    responses: {
      parse: jest.fn().mockResolvedValue({
        output_text: 'Generated image prompt',
      }),
    },
  })),
}));

describe('BlogPostImageGenerator Lambda', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (S3Client as jest.Mock).mockImplementation(() => mockS3Client);
    (LangfuseClient as jest.Mock).mockImplementation(() => mockLangfuseClient);
    (GoogleGenAI as jest.Mock).mockImplementation(() => mockGoogleGenAI);
    (CMSClient as jest.Mock).mockImplementation(() => mockCMSClient);

    // Setup default mocks
    mockLangfuseClient.prompt.get.mockResolvedValue({
      compile: jest.fn().mockReturnValue('Compiled prompt'),
    });

    // Setup S3 mock to return blog content for GetObjectCommand
    mockS3Client.send.mockImplementation(command => {
      if (command.constructor.name === 'GetObjectCommand') {
        return Promise.resolve({
          Body: {
            transformToString: () =>
              Promise.resolve(
                JSON.stringify({
                  companyId: 'company-1',
                  topicId: 'topic-1',
                  title: 'Test Blog Title',
                  content: 'Test blog content...',
                  metaDescription: 'Test description',
                  keywords: ['test', 'blog'],
                  imagePrompt: 'A beautiful image prompt',
                  createdAt: '2023-12-01T10:00:00.000Z',
                  dryRun: false,
                  airopsExecutionId: 'exec-123',
                }),
              ),
          },
        });
      }
      return Promise.resolve({});
    });
  });

  describe('handler', () => {
    it('should handle interface correctly', () => {
      // Test that the interface is correctly defined
      const event: BlogPostImageGeneratorEvent = {
        companyId: 'company-1',
        topicId: 'topic-1',
        s3Location: 's3://test-bucket/test.json',
        dryRun: true,
        executionFolderName: 'test-execution',
      };

      expect(event.s3Location).toBe('s3://test-bucket/test.json');
      expect(event.companyId).toBe('company-1');
      expect(event.dryRun).toBe(true);
    });

    it('should parse S3 URLs correctly', () => {
      // Test the S3 URL parsing logic
      const s3Location = 's3://test-bucket/path/to/file.json';
      const url = new URL(s3Location);

      expect(url.hostname).toBe('test-bucket');
      expect(url.pathname.substring(1)).toBe('path/to/file.json');
    });

    it('should handle different S3 URL formats', () => {
      const testCases = [
        {
          input: 's3://bucket/file.json',
          expectedBucket: 'bucket',
          expectedKey: 'file.json',
        },
        {
          input: 's3://my-bucket/nested/path/file.json',
          expectedBucket: 'my-bucket',
          expectedKey: 'nested/path/file.json',
        },
        {
          input:
            's3://test-bucket/blog-post-jobs/2023-12-01/posts/company-topic.json',
          expectedBucket: 'test-bucket',
          expectedKey: 'blog-post-jobs/2023-12-01/posts/company-topic.json',
        },
      ];

      testCases.forEach(({ input, expectedBucket, expectedKey }) => {
        const url = new URL(input);
        expect(url.hostname).toBe(expectedBucket);
        expect(url.pathname.substring(1)).toBe(expectedKey);
      });
    });
  });
});
