import { ProcessBlogInputsError } from '../../../errors/blog-post-writer';
import { handler, ProcessBlogInputsEvent } from '../process-blog-inputs';

describe('ProcessBlogInputs Lambda', () => {
  describe('handler', () => {
    it('should process valid input with defaults', async () => {
      const event = {
        companyIds: ['company-1', 'company-2'],
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: ['company-1', 'company-2'],
        dryRun: true,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      });
    });

    it('should handle nested input structure', async () => {
      const event = {
        input: {
          companyIds: ['company-1'],
          dryRun: false,
        },
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: ['company-1'],
        dryRun: false,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      });
    });

    it('should apply default values when not provided', async () => {
      const event = {};

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: [],
        dryRun: true,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should handle empty array companyIds', async () => {
      const event = {
        companyIds: [],
        dryRun: false,
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: [],
        dryRun: false,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should handle null values with defaults', async () => {
      const event = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        companyIds: null as any,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        dryRun: null as any,
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: [],
        dryRun: true,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should handle deeply nested input', async () => {
      const event = {
        input: {
          input: {
            companyIds: ['company-1'],
            dryRun: false,
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any,
        executionName: 'test-execution',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        companyIds: ['company-1'],
        dryRun: false,
        skipEntitlementsValidation: false,
        skipUnitsValidation: false,
        skipExistingScheduledPostValidation: false,
        publishToCMS: false,
        executionName: 'test-execution',
        executionStartTime: undefined,
      });
    });

    it('should throw error for invalid companyIds type', async () => {
      const event = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        companyIds: 'not-an-array' as any,
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        ProcessBlogInputsError,
      );
    });

    it('should throw error for invalid dryRun type', async () => {
      const event = {
        companyIds: [],
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        dryRun: 'not-a-boolean' as any,
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        ProcessBlogInputsError,
      );
    });

    it('should throw error for companyIds with non-string values', async () => {
      const event = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        companyIds: ['valid', 123, true] as any,
      };

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        ProcessBlogInputsError,
      );
    });

    it('should preserve execution metadata', async () => {
      const event = {
        companyIds: ['company-1'],
        executionName: 'execution-123',
        executionStartTime: '2024-01-01T12:00:00Z',
      };

      const result = await handler(event, {} as never, {} as never);

      expect(result!.executionName).toBe('execution-123');
      expect(result!.executionStartTime).toBe('2024-01-01T12:00:00Z');
    });

    it('should handle general errors', async () => {
      const event = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        get companyIds(): any {
          throw new Error('Unexpected error');
        },
      } as ProcessBlogInputsEvent;

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        ProcessBlogInputsError,
      );
    });
  });
});
