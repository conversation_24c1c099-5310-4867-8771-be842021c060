import {
  S3Client,
  GetObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';

import { ResultsWriterError } from '../../../errors/blog-post-writer';
import { CompanyProcessingResult } from '../../../types/blog-post-writer';
import {
  handler,
  ResultsWriterEvent,
  MapResultsWithWriter,
} from '../results-writer';

// Mock dependencies
jest.mock('@aws-sdk/client-s3');
jest.mock('node-fetch', () => jest.fn());

const MockedS3Client = S3Client as jest.MockedClass<typeof S3Client>;

describe('ResultsWriter Lambda - New Brand-Profile-Generator Approach', () => {
  const mockSend = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.ENVIRONMENT = 'test';
    process.env.EXECUTION_BUCKET_NAME = 'test-bucket';
    process.env.BLOG_V2_SLACK_NOTIFICATIONS_ENABLED = 'false';

    // Mock S3 client
    MockedS3Client.prototype.send = mockSend;

    // Default mock - empty results from S3
    mockSend.mockImplementation(command => {
      if (command instanceof GetObjectCommand) {
        return Promise.resolve({
          Body: {
            transformToString: () => Promise.resolve('[]'),
          },
        });
      }
      return Promise.resolve({}); // For PutObjectCommand
    });
  });

  describe('Direct Results Array Processing', () => {
    const createDirectResultsEvent = (
      results: CompanyProcessingResult[],
      overrides = {},
    ): ResultsWriterEvent => ({
      executionName: 'test-execution',
      executionStartTime: '2024-01-01T00:00:00Z',
      executionArn:
        'arn:aws:states:us-east-1:123456:execution:blog-post-writer:test-execution',
      dryRun: false,
      results: results,
      ...overrides,
    });

    it('should handle direct results array', async () => {
      const mockResults: CompanyProcessingResult[] = [
        {
          companyId: 'company-1',
          topicId: 'topic-1',
          published: true,
          createdAt: '2024-01-01T00:05:00Z',
          s3Location: 's3://bucket/post1.json',
        },
        {
          companyId: 'company-2',
          skipped: true,
          reason: 'No topics available',
        },
        {
          companyId: 'company-3',
          failed: true,
          error: 'API error',
        },
      ];

      const event = createDirectResultsEvent(mockResults);
      const result = await handler(event, {} as never, {} as never);

      expect(result).toEqual({
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
        summary: {
          totalCompanies: 3,
          successfulPosts: 1,
          failedPosts: 1,
          skippedCompanies: 1,
          cmsPublishedPosts: 1, // company-1 has published=true, so counts as CMS published
        },
        reportLocation: expect.stringMatching(
          /^s3:\/\/test-bucket\/blog-post-jobs\//,
        ),
        notificationSent: false,
      });

      // Only one S3 call for saving the report
      expect(mockSend).toHaveBeenCalledTimes(1);
    });

    it('should handle empty direct results array', async () => {
      const event = createDirectResultsEvent([]);
      const result = await handler(event, {} as never, {} as never);

      expect(result!.summary).toEqual({
        totalCompanies: 0,
        successfulPosts: 0,
        failedPosts: 0,
        skippedCompanies: 0,
        cmsPublishedPosts: 0,
      });
    });

    it('should correctly count CMS published posts vs dry run posts', async () => {
      const mockResults: CompanyProcessingResult[] = [
        {
          companyId: 'company-1',
          topicId: 'topic-1',
          published: true, // Published to CMS
          createdAt: '2024-01-01T00:05:00Z',
          s3Location: 's3://bucket/post1.json',
          cmsPostId: 'topic-1', // CMS returns the topicId we sent
        },
        {
          companyId: 'company-2',
          topicId: 'topic-2',
          published: true, // Published to CMS
          createdAt: '2024-01-01T00:06:00Z',
          s3Location: 's3://bucket/post2.json',
          cmsPostId: 'topic-2', // CMS returns the topicId we sent
        },
        {
          companyId: 'company-3',
          failed: true,
          error: 'CMS publishing failed',
        },
        {
          companyId: 'company-4',
          skipped: true,
          reason: 'No topics available',
        },
      ];

      const event = createDirectResultsEvent(mockResults);
      const result = await handler(event, {} as never, {} as never);

      expect(result!.summary).toEqual({
        totalCompanies: 4,
        successfulPosts: 2, // company-1 and company-2 (published=true)
        cmsPublishedPosts: 2, // Same as successful posts (published=true means CMS published)
        failedPosts: 1, // company-3 failed
        skippedCompanies: 1, // company-4 skipped
      });
    });
  });

  describe('S3 ResultWriter Processing', () => {
    const createS3ResultsEvent = (overrides = {}): ResultsWriterEvent => ({
      executionName: 'test-execution',
      executionStartTime: '2024-01-01T00:00:00Z',
      executionArn:
        'arn:aws:states:us-east-1:123456:execution:blog-post-writer:test-execution',
      dryRun: false,
      results: {
        ResultWriterDetails: {
          Bucket: 'test-execution-bucket',
          Key: 'blog-post-jobs/2024-01-01/test-execution/',
        },
      } as MapResultsWithWriter,
      ...overrides,
    });

    it('should handle S3 ResultWriter with manifest processing', async () => {
      const mockResults: CompanyProcessingResult[] = [
        {
          companyId: 'company-1',
          topicId: 'topic-1',
          published: true,
          createdAt: '2024-01-01T00:05:00Z',
          s3Location: 's3://bucket/post1.json',
        },
      ];

      // Mock the S3 calls for manifest processing
      const manifest = {
        DestinationBucket: 'test-execution-bucket',
        ResultFiles: {
          SUCCEEDED: [{ Key: 'succeeded-results.json', Size: 1000 }],
          FAILED: [],
          PENDING: [],
        },
      };

      const succeededResults = mockResults.map(result => ({
        Output: JSON.stringify(result),
        ExecutionArn: 'arn:aws:states:us-east-1:123456789012:execution:test',
      }));

      mockSend
        .mockImplementationOnce(command => {
          // ListObjectsV2Command for UUID folder discovery
          if (command instanceof ListObjectsV2Command) {
            return Promise.resolve({
              CommonPrefixes: [
                {
                  Prefix:
                    'blog-post-jobs/2024-01-01/test-execution/uuid-folder/',
                },
              ],
            });
          }
          return Promise.resolve({});
        })
        .mockImplementationOnce(command => {
          // GetObjectCommand for manifest.json
          if (command instanceof GetObjectCommand) {
            return Promise.resolve({
              Body: {
                transformToString: () =>
                  Promise.resolve(JSON.stringify(manifest)),
              },
            });
          }
          return Promise.resolve({});
        })
        .mockImplementationOnce(command => {
          // GetObjectCommand for succeeded results
          if (command instanceof GetObjectCommand) {
            return Promise.resolve({
              Body: {
                transformToString: () =>
                  Promise.resolve(JSON.stringify(succeededResults)),
              },
            });
          }
          return Promise.resolve({});
        })
        .mockImplementationOnce(() => Promise.resolve({})); // PutObjectCommand for report

      const event = createS3ResultsEvent();
      const result = await handler(event, {} as never, {} as never);

      expect(result!.summary.totalCompanies).toBe(1);
      expect(result!.summary.successfulPosts).toBe(1);
      expect(mockSend).toHaveBeenCalledTimes(4); // List + Manifest + Results + PutReport
    });

    it('should handle empty S3 results', async () => {
      // Mock empty S3 response
      mockSend
        .mockImplementationOnce(command => {
          // ListObjectsV2Command returns empty
          if (command instanceof ListObjectsV2Command) {
            return Promise.resolve({ Contents: [] });
          }
          return Promise.resolve({});
        })
        .mockImplementationOnce(() => Promise.resolve({})); // PutObjectCommand

      const event = createS3ResultsEvent();
      const result = await handler(event, {} as never, {} as never);

      expect(result!.summary.totalCompanies).toBe(0);
    });

    it('should handle S3 errors with retry logic', async () => {
      // Clear existing mocks and set up error sequence
      jest.clearAllMocks();
      mockSend
        .mockRejectedValueOnce(new Error('S3 temporary error'))
        .mockRejectedValueOnce(new Error('S3 temporary error'))
        .mockRejectedValueOnce(new Error('S3 permanent error'));

      const event = createS3ResultsEvent();

      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        ResultsWriterError,
      );

      // Clean up: reset mock to default behavior for subsequent tests
      jest.clearAllMocks();
      mockSend.mockImplementation(command => {
        if (command instanceof GetObjectCommand) {
          return Promise.resolve({
            Body: {
              transformToString: () => Promise.resolve('[]'),
            },
          });
        }
        return Promise.resolve({}); // For PutObjectCommand
      });
    });
  });

  // Note: Error handling is already covered by the other tests above
});
