// Set up environment variables before importing the handler
process.env.AIR_OPS_API_KEY = 'test-api-key';
process.env.AIR_OPS_API_URL = 'https://api.airops.com/public_api/airops_apps';
import { S3Client } from '@aws-sdk/client-s3';

import { AirOpsJobFailedError } from '../../../errors/blog-post-writer';
import { handler } from '../get-airops-job-status';

// Mock global fetch
const mockFetch = jest.fn() as jest.MockedFunction<typeof fetch>;
global.fetch = mockFetch;

// Mock S3Client
jest.mock('@aws-sdk/client-s3');
const mockS3Client = {
  send: jest.fn().mockResolvedValue({}),
};
describe('GetAirOpsJobStatus Lambda', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (S3Client as jest.Mock).mockImplementation(() => mockS3Client);
  });
  describe('handler', () => {
    it('should return running status in dry run mode initially', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: true,
        executionFolderName: 'test-execution',
        pollingAttempts: { count: 0 },
      };
      const result = await handler(event, {} as never, {} as never);
      expect(result).toEqual({
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        status: 'running',
      });
    });
    it('should return completed status with content in dry run after polling', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: true,
        executionFolderName: 'test-execution',
        pollingAttempts: { count: 2 },
      };
      const result = await handler(event, {} as never, {} as never);
      expect(result).toEqual({
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        status: 'completed',
        blogContent: expect.objectContaining({
          title: 'Mock Blog Post Title',
          content: expect.stringContaining('Mock Blog Content'),
          metaDescription: 'Mock meta description for SEO',
          keywords: ['mock', 'test', 'blog'],
        }),
        s3Location: expect.stringContaining(
          's3://lp-bloom-scheduler-execution-development/blog-post-jobs',
        ),
      });
    });
    it('should call AirOps API to check status', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: false,
        executionFolderName: 'test-execution',
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          status: 'running',
        }),
      } as never);
      const result = await handler(event, {} as never, {} as never);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.airops.com/public_api/airops_apps/executions/exec-123',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            Authorization: 'Bearer test-api-key',
            'Content-Type': 'application/json',
          }),
        }),
      );
      expect(result!.status).toBe('running');
    });
    it('should parse completed status with blog content and save to S3', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: false,
        executionFolderName: 'test-execution',
        executionStartTime: '2023-12-01T10:00:00.000Z',
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          status: 'completed',
          output: {
            content: 'Test blog content...',
            metaDescription: 'Test description',
          },
        }),
      } as never);
      const result = await handler(event, {} as never, {} as never);

      // Verify the result includes both blogContent and s3Location
      expect(result).toEqual({
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        status: 'completed',
        blogContent: {
          content: 'Test blog content...',
          metaDescription: 'Test description',
        },
        s3Location: expect.stringMatching(
          /^s3:\/\/.*\/blog-post-jobs\/2023-12-01\/test-execution\/posts\/company-1-topic-1\.json$/,
        ),
        error: undefined,
      });

      // Verify s3Location is properly formatted
      expect(result!.s3Location).toBeDefined();
      expect(result!.s3Location).toContain('s3://');
      expect(result!.s3Location).toContain(
        'blog-post-jobs/2023-12-01/test-execution/posts/company-1-topic-1.json',
      );
    });
    it('should throw AirOpsJobFailedError for failed status', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: false,
        executionFolderName: 'test-execution',
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          status: 'failed',
          error: 'Job failed due to timeout',
        }),
      } as never);
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        AirOpsJobFailedError,
      );
    });
    it('should handle different AirOps status formats', async () => {
      const statusMappings = [
        { input: 'pending', expected: 'pending' },
        { input: 'queued', expected: 'pending' },
        { input: 'processing', expected: 'running' },
        { input: 'in_progress', expected: 'running' },
        { input: 'success', expected: 'completed' },
        { input: 'succeeded', expected: 'completed' },
        { input: 'error', expected: 'failed' },
        { input: 'canceled', expected: 'cancelled' },
        { input: 'timeout', expected: 'expired' },
      ];
      for (const { input, expected } of statusMappings) {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({ status: input }),
        } as never);
        const event = {
          companyId: 'company-1',
          topicId: 'topic-1',
          airopsExecutionId: 'exec-123',
          dryRun: false,
          executionFolderName: 'test-execution',
        };
        if (
          expected === 'failed' ||
          expected === 'cancelled' ||
          expected === 'expired'
        ) {
          await expect(
            handler(event, {} as never, {} as never),
          ).rejects.toThrow();
        } else {
          const result = await handler(event, {} as never, {} as never);
          expect(result!.status).toBe(expected);
        }
      }
    });
    it('should handle transient network errors', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: false,
        executionFolderName: 'test-execution',
      };
      mockFetch.mockRejectedValueOnce(new Error('ECONNREFUSED'));
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'Network error calling AirOps API',
      );
    });
    it('should handle 5xx errors as transient', async () => {
      const event = {
        companyId: 'company-1',
        topicId: 'topic-1',
        airopsExecutionId: 'exec-123',
        dryRun: false,
        executionFolderName: 'test-execution',
      };
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        text: async () => 'Service temporarily unavailable',
      } as never);
      await expect(handler(event, {} as never, {} as never)).rejects.toThrow(
        'AirOps API returned error: 503 Service Unavailable',
      );
    });
  });
});
