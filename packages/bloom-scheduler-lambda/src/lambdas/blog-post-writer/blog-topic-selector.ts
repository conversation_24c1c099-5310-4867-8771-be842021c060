import { Handler } from 'aws-lambda';

import { ApiGatewayClient } from '../../clients/ApiGatewayClient';
import { CosmoClient } from '../../clients/CosmoClient';
import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  BlogTopicSelectorError,
  NoAvailableTopicsError,
} from '../../errors/blog-post-writer';
import {
  BrandProfile,
  Neighborhood,
  BlogTopic,
} from '../../types/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

export interface BlogTopicSelectorEvent {
  companyId: string;
  brandProfile: BrandProfile;
  neighborhoods: Neighborhood[];
  preferredNeighborhoods: Neighborhood[];
  dryRun: boolean;
  publishToCMS?: boolean;
  executionFolderName: string;
}

export interface BlogTopicSelectorResult {
  companyId: string;
  selectedTopic: BlogTopic;
  selectionReason: string;
}

export const handler: Handler<
  BlogTopicSelectorEvent,
  BlogTopicSelectorResult
> = async event => {
  const logger = createContextLogger('blog-topic-selector');
  const startTime = Date.now();

  try {
    logger.info('Selecting blog topic', { event });

    const {
      companyId,
      neighborhoods,
      preferredNeighborhoods,
      dryRun,
      publishToCMS,
    } = event;

    // In dry run mode, return mock topic
    if (dryRun) {
      logger.info('Dry run mode - returning mock blog topic');
      return {
        companyId,
        selectedTopic: {
          id: 'mock-topic-1',
          companyId,
          topic: 'Top 10 Things to Do in Downtown',
          title: 'Top 10 Things to Do in Downtown',
          keywords: ['Top', '10', 'Things', 'to', 'Do', 'in', 'Downtown'],
          neighborhood: preferredNeighborhoods[0] || neighborhoods[0],
          priority: 1,
          type: 'ARTICLE',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        selectionReason: 'Mock topic selected for dry run',
      };
    }

    // Validate required environment variables
    const requiredEnvVars = {
      API_GATEWAY_URL: process.env.API_GATEWAY_URL,
      API_GATEWAY_SUPER_USER_COMPANY_ID:
        process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
      API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
      COSMO_GQL_URL: process.env.COSMO_GQL_URL,
      M2M_SUPER_API_KEY: process.env.M2M_SUPER_API_KEY,
    };

    const missingEnvVars = Object.entries(requiredEnvVars)
      .filter(([, value]) => !value || value.trim() === '')
      .map(([key]) => key);

    if (missingEnvVars.length > 0) {
      throw new Error(
        `Missing required environment variables: ${missingEnvVars.join(', ')}`,
      );
    }

    // Initialize API Gateway client with validated environment variables
    const { GraphQLClient } = await import('graphql-request');
    const apiGatewayGraphqlClient = new GraphQLClient(
      `${requiredEnvVars.API_GATEWAY_URL!}/graphql`,
    );

    const apiGatewayClient = new ApiGatewayClient(
      requiredEnvVars.API_GATEWAY_SUPER_USER_COMPANY_ID!,
      requiredEnvVars.API_GATEWAY_KEY!,
      apiGatewayGraphqlClient,
      logger,
    );

    // Initialize hybrid CosmoClient
    const cosmoClient = new CosmoClient(
      requiredEnvVars.COSMO_GQL_URL!,
      requiredEnvVars.M2M_SUPER_API_KEY!,
      apiGatewayClient,
      logger,
    );

    // Extract preferred neighborhood IDs for GraphQL query
    const preferredNeighborhoodIds = preferredNeighborhoods
      .map(n => n.id)
      .filter(id => id && id.trim() !== '');

    // Use client-marketing-service GraphQL blogTopicSelector
    const selectedTopic = await cosmoClient.selectBlogTopic(
      companyId,
      preferredNeighborhoodIds.length > 0
        ? preferredNeighborhoodIds
        : undefined,
    );

    if (!selectedTopic) {
      throw new NoAvailableTopicsError(companyId);
    }

    // Map topic to expected result format for backward compatibility
    const blogTopic: BlogTopic = {
      ...selectedTopic,
      // Ensure legacy fields are populated
      title:
        selectedTopic.blogTitle || selectedTopic.topic || selectedTopic.title,
      keywords: selectedTopic.topic
        ? selectedTopic.topic.split(/[\s,]+/)
        : selectedTopic.keywords || [],
      priority: selectedTopic.priority || 1,
      type: selectedTopic.type || 'ARTICLE',
    };

    // Find neighborhood details for the selected topic
    if (selectedTopic.neighborhoodId) {
      logger.info('Attempting to match neighborhood for selected topic', {
        topicId: selectedTopic.id,
        neighborhoodId: selectedTopic.neighborhoodId,
        availableNeighborhoods: [
          ...preferredNeighborhoods,
          ...neighborhoods,
        ].map(n => ({ id: n.id, name: n.name })),
        preferredNeighborhoodsCount: preferredNeighborhoods.length,
        allNeighborhoodsCount: neighborhoods.length,
      });

      const matchingNeighborhood = [
        ...preferredNeighborhoods,
        ...neighborhoods,
      ].find(n => n.id === selectedTopic.neighborhoodId);

      if (matchingNeighborhood) {
        logger.info('Successfully matched neighborhood for topic', {
          topicId: selectedTopic.id,
          neighborhoodId: selectedTopic.neighborhoodId,
          neighborhoodName: matchingNeighborhood.name,
          neighborhoodPreferredName: matchingNeighborhood.preferredName,
          neighborhoodGooglePlacesName: matchingNeighborhood.googlePlacesName,
        });
        blogTopic.neighborhood = matchingNeighborhood;
      } else {
        logger.warn('No matching neighborhood found for selected topic', {
          topicId: selectedTopic.id,
          neighborhoodId: selectedTopic.neighborhoodId,
          searchedIn: [...preferredNeighborhoods, ...neighborhoods].map(
            n => n.id,
          ),
        });

        // Try to fetch neighborhood data directly using the ID
        try {
          logger.info('Attempting to fetch neighborhood by ID as fallback', {
            topicId: selectedTopic.id,
            neighborhoodId: selectedTopic.neighborhoodId,
            companyId,
          });

          const fetchedNeighborhood = await cosmoClient.getNeighborhoodById(
            companyId,
            selectedTopic.neighborhoodId,
          );

          if (fetchedNeighborhood) {
            logger.info('Successfully fetched neighborhood by ID', {
              topicId: selectedTopic.id,
              neighborhoodId: selectedTopic.neighborhoodId,
              neighborhoodName: fetchedNeighborhood.name,
              neighborhoodGooglePlacesName:
                fetchedNeighborhood.googlePlacesName,
            });
            blogTopic.neighborhood = fetchedNeighborhood;
          } else {
            logger.error(
              'Failed to fetch neighborhood by ID - neighborhood data unavailable',
              {
                topicId: selectedTopic.id,
                neighborhoodId: selectedTopic.neighborhoodId,
                companyId,
              },
            );
          }
        } catch (fetchError) {
          logger.error('Error fetching neighborhood by ID', {
            topicId: selectedTopic.id,
            neighborhoodId: selectedTopic.neighborhoodId,
            companyId,
            error:
              fetchError instanceof Error
                ? fetchError.message
                : String(fetchError),
          });
        }
      }
    } else {
      logger.info('Selected topic has no neighborhood ID', {
        topicId: selectedTopic.id,
      });
    }

    // Lock the topic if we're not in dry run mode and publishToCMS is true
    let lockedTopic = blogTopic;
    if (!dryRun && publishToCMS === true) {
      try {
        logger.info('Locking blog topic after selection', {
          blogTopicId: selectedTopic.id,
          companyId,
        });

        // Use the blog topic ID as the cmsPostId to lock it
        const updatedTopic = await cosmoClient.lockBlogTopic(
          selectedTopic.id,
          selectedTopic.id,
        );

        // Update our result with the locked topic
        lockedTopic = { ...blogTopic, cmsPostId: updatedTopic.cmsPostId };

        logger.info('Blog topic locked successfully', {
          blogTopicId: selectedTopic.id,
          cmsPostId: updatedTopic.cmsPostId,
        });
      } catch (error) {
        logger.error('Failed to lock blog topic, proceeding without lock', {
          error: error instanceof Error ? error.message : String(error),
          blogTopicId: selectedTopic.id,
          companyId,
        });
        // Continue execution without failing - topic locking is not critical
      }
    } else {
      logger.info('Skipping topic lock', {
        reason: dryRun ? 'dry run mode' : 'publishToCMS not enabled',
        dryRun,
        publishToCMS,
      });
    }

    // Determine selection reason based on GraphQL response
    let selectionReason = 'Selected via GraphQL blogTopicSelector: ';
    if (
      selectedTopic.neighborhoodId &&
      preferredNeighborhoodIds.includes(selectedTopic.neighborhoodId)
    ) {
      selectionReason += 'preferred neighborhood priority';
    } else if (selectedTopic.rationale) {
      selectionReason += selectedTopic.rationale;
    } else {
      selectionReason += 'random selection from available topics';
    }

    // Send timing metric
    const executionTime = Date.now() - startTime;
    await datadogMetrics
      .sendTimingMetric(
        METRICS.BLOG_TOPIC_SELECTOR_TIME,
        executionTime,
        'blog-topic-selector',
      )
      .catch(() => {
        /* Ignore metric errors */
      });

    return {
      companyId,
      selectedTopic: lockedTopic,
      selectionReason,
    };
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(METRICS.LAMBDA_ERRORS, 'blog-topic-selector', errorType)
      .catch(() => {
        /* Ignore metric errors */
      });

    if (error instanceof BlogTopicSelectorError) {
      throw error;
    }
    throw new BlogTopicSelectorError(
      'Failed to select blog topic',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
