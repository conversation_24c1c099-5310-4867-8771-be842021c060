import { Handler } from 'aws-lambda';

import { ApiGatewayClient } from '../../clients/ApiGatewayClient';
import { createContextLogger } from '../../utils/contextLogger';

// Helper function to generate AWS execution URL
function getExecutionUrl(executionArn?: string): string | null {
  if (!executionArn) return null;

  // Parse the ARN to extract region for AWS console URL
  const arnParts = executionArn.split(':');
  if (arnParts.length < 6) return null;

  const region = arnParts[3];
  return `https://${region}.console.aws.amazon.com/states/home?region=${region}#/executions/details/${encodeURIComponent(executionArn)}`;
}

export interface CatchFailureEvent {
  error: {
    Cause?: string;
    Error?: string;
    message?: string;
  };
  executionName?: string;
  companyIds?: string[];
  executionArn?: string;
  context?: {
    // Full step function execution state is passed as context
    companyId?: string;
    config?: {
      dryRun?: boolean;
      executionFolderName?: string;
    };
    topicData?: {
      selectedTopic?: {
        id?: string;
        companyId?: string;
      };
    };
    [key: string]: unknown;
  };
}

export interface CatchFailureResult {
  error: {
    type?: string;
    message?: string;
    raw: unknown;
  };
  executionName?: string;
  failedAt: string;
  handled: boolean;
}

export const handler: Handler<
  CatchFailureEvent,
  CatchFailureResult
> = async event => {
  const logger = createContextLogger('catch-blog-writer-failure');

  logger.error('Blog post writer execution failed:', { event });

  const { error, executionName, companyIds, executionArn, context } = event;

  // Log detailed error information
  logger.error('Error details:', {
    errorType: error.Error,
    errorMessage: error.Cause || error.message,
    executionName,
    companyIds,
    context,
  });

  // Try to unlock the blog topic if we have a topic ID and the topic was locked
  const topicId = context?.topicData?.selectedTopic?.id;
  if (topicId) {
    try {
      // Validate required environment variables
      const requiredEnvVars = {
        API_GATEWAY_URL: process.env.API_GATEWAY_URL,
        API_GATEWAY_SUPER_USER_COMPANY_ID:
          process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
        API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
      };

      const missingEnvVars = Object.entries(requiredEnvVars)
        .filter(([, value]) => !value || value.trim() === '')
        .map(([key]) => key);

      if (missingEnvVars.length === 0) {
        // Initialize API Gateway client
        const { GraphQLClient } = await import('graphql-request');
        const graphqlClient = new GraphQLClient(
          `${requiredEnvVars.API_GATEWAY_URL!}/graphql`,
        );

        const apiGatewayClient = new ApiGatewayClient(
          requiredEnvVars.API_GATEWAY_SUPER_USER_COMPANY_ID!,
          requiredEnvVars.API_GATEWAY_KEY!,
          graphqlClient,
          logger,
        );

        logger.info('Attempting to unlock blog topic after failure', {
          topicId,
        });

        await apiGatewayClient.unlockBlogTopic(topicId);

        logger.info('Successfully unlocked blog topic after failure', {
          topicId,
        });
      } else {
        logger.warn(
          'Cannot unlock blog topic - missing environment variables',
          {
            missingEnvVars,
            topicId,
          },
        );
      }
    } catch (unlockError) {
      logger.error('Failed to unlock blog topic after failure', {
        error:
          unlockError instanceof Error
            ? unlockError.message
            : String(unlockError),
        topicId,
      });
      // Continue execution - unlocking failure should not cause the error handler to fail
    }
  }

  // TODO: Send failure notification
  // This could include:
  // 1. Sending a Slack notification
  // 2. Creating an error report in S3
  // 3. Logging to a monitoring service

  // Check if Slack notifications are enabled
  const slackEnabled =
    process.env.BLOG_V2_SLACK_NOTIFICATIONS_ENABLED === 'true' ||
    process.env.SLACK_NOTIFICATIONS_ENABLED === 'true';
  const slackWebhookUrl =
    process.env.BLOG_V2_SLACK_WEBHOOK_URL || process.env.SLACK_WEBHOOK_URL;

  if (slackEnabled && slackWebhookUrl) {
    try {
      const environment = process.env.ENVIRONMENT || 'dev';
      const envEmoji =
        environment === 'production'
          ? '🚀'
          : environment === 'staging'
            ? '🧪'
            : '🔧';
      const envName = environment.toUpperCase();

      let message = `${envEmoji} *[${envName}]* ❌ *Blog Post Writer State Machine Failed*\n\n`;

      message += `*Execution Details:*\n`;
      const executionUrl = getExecutionUrl(executionArn);
      if (executionUrl) {
        message += `• Execution: <${executionUrl}|${executionName || 'View Execution'}>\n`;
      } else {
        message += `• Execution: ${executionName || 'Unknown'}\n`;
      }
      message += `• Failed At: ${new Date().toISOString()}\n`;
      message += `• Error Type: ${error.Error || 'Unknown'}\n`;

      if (companyIds && companyIds.length > 0) {
        message += `• Company IDs: ${companyIds.join(', ')}\n`;
      }

      if (context?.companyId) {
        message += `• Context Company ID: ${context.companyId}\n`;
      }

      if (topicId) {
        message += `• Topic ID: ${topicId}\n`;
      }

      message += `\n*Error Message:*\n`;
      message += `\`\`\`${error.Cause || error.message || 'No error message available'}\`\`\`\n`;

      // Add footer with environment and timestamp
      const timestamp = new Date()
        .toISOString()
        .replace('T', ' ')
        .split('.')[0];
      message += `\n───────────────────\n`;
      message += `_Environment: ${envName} | Failed: ${timestamp} UTC_`;

      const response = await fetch(slackWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: message }),
      });

      if (!response.ok) {
        logger.error('Failed to send Slack notification', {
          statusText: response.statusText,
        });
      } else {
        logger.info('Slack notification sent successfully');
      }
    } catch (slackError) {
      logger.error('Error sending Slack notification', { error: slackError });
    }
  }

  return {
    error: {
      type: error.Error,
      message: error.Cause || error.message,
      raw: error,
    },
    executionName,
    failedAt: new Date().toISOString(),
    handled: true,
  };
};
