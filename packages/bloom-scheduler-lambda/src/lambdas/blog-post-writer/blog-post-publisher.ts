import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { Hand<PERSON> } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';

import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  BlogPostPublisherError,
  TransientBlogPostPublisherError,
} from '../../errors/blog-post-writer';
import { getGWAuthToken } from '../../utils';
import { createContextLogger } from '../../utils/contextLogger';

const s3Client = new S3Client({});

const API_GATEWAY_URL = process.env.API_GATEWAY_URL || '';
const API_GATEWAY_SUPER_USER =
  process.env.API_GATEWAY_SUPER_USER_COMPANY_ID || '';
const API_GATEWAY_KEY = process.env.API_GATEWAY_KEY || '';

// Helper function to get day of week number
const getDayOfWeekNumber = (dayName: string): number => {
  const days = {
    SUNDAY: 0,
    MONDAY: 1,
    TUESDAY: 2,
    WEDNESDAY: 3,
    THURSDAY: 4,
    FRIDAY: 5,
    SATURDAY: 6,
  };
  return days[dayName as keyof typeof days] || 2; // Default to TUESDAY
};

// Helper function to calculate next POST_PUBLISH_DAY_OF_WEEK at 10am UTC (for AI Specialist)
const getNextScheduledDate = (): string => {
  const today = new Date();
  const publishDayOfWeek = process.env.POST_PUBLISH_DAY_OF_WEEK || 'THURSDAY';
  const targetDay = getDayOfWeekNumber(publishDayOfWeek);
  const todayDay = today.getDay();

  // Calculate days until target day
  let daysUntilTarget = (targetDay - todayDay + 7) % 7;

  // If today is the target day, schedule for next week
  if (daysUntilTarget === 0) {
    daysUntilTarget = 7;
  }

  // Create the scheduled date at 10am UTC
  const scheduledDate = new Date(today);
  scheduledDate.setDate(today.getDate() + daysUntilTarget);
  scheduledDate.setUTCHours(10, 0, 0, 0); // 10:00 AM UTC

  return scheduledDate.toISOString();
};

export interface BlogPostPublisherEvent {
  companyId: string;
  topicId: string;
  s3Location: string;
  mediaId?: string;
  imageUrl?: string;
  dryRun: boolean;
  scheduledAt?: string; // ISO string for when the post should be published
}

interface BlogPostData {
  companyId: string;
  topicId: string;
  title: string;
  content: string;
  metaDescription?: string;
  keywords?: string[];
  createdAt: string;
  dryRun: boolean;
  airopsExecutionId: string;
}

export interface BlogPostPublisherResult {
  companyId: string;
  topicId: string;
  published: boolean;
  publishedUrl?: string;
  s3Location?: string;
  cmsPostId?: string;
  createdAt?: string;
}

// GraphQL mutation for creating posts
const CREATE_POST_MUTATION = `
  mutation createPost(
    $companyId: ID!
    $content: String!
    $media: [ID!]!
    $postId: ID
    $seoDescription: String!
    $seoTitle: String!
    $title: String!
    $neighborhoods: [ID!] = []
    $scheduledAt: String
  ) {
    createPost(
      companyId: $companyId
      title: $title
      description: $content
      media: $media
      postId: $postId
      seoDescription: $seoDescription
      seoTitle: $seoTitle
      neighborhoods: $neighborhoods
      scheduledAt: $scheduledAt
      aiGenerated: true
    ) {
      id
      postId
      slug
    }
  }
`;

interface CreatePostResponse {
  createPost: {
    id: string;
    postId: string;
    slug: string;
  };
}

export const handler: Handler<
  BlogPostPublisherEvent,
  BlogPostPublisherResult
> = async event => {
  const logger = createContextLogger('blog-post-publisher');
  const startTime = Date.now();

  try {
    logger.info('Publishing blog post to CMS', { event });

    const {
      companyId,
      topicId,
      s3Location,
      mediaId,
      dryRun,
      scheduledAt: eventScheduledAt,
    } = event;

    // Extract bucket and key from S3 location
    const s3Url = new URL(s3Location);
    const bucket = s3Url.hostname;
    const key = s3Url.pathname.slice(1); // Remove leading slash

    // Read blog content from S3
    logger.info('Reading blog content from S3', { bucket, key });
    const s3Response = await s3Client.send(
      new GetObjectCommand({
        Bucket: bucket,
        Key: key,
      }),
    );

    if (!s3Response.Body) {
      throw new BlogPostPublisherError(
        'No content found in S3 object',
        new Error('S3 object body is empty'),
        { s3Location, bucket, key },
      );
    }

    const blogPostDataStr = await s3Response.Body.transformToString();
    const blogPostData: BlogPostData = JSON.parse(blogPostDataStr);

    logger.info('Retrieved blog content from S3', {
      title: blogPostData.title,
      contentLength: blogPostData.content.length,
    });

    // In dry run mode, return without publishing to CMS
    if (dryRun) {
      logger.info('Dry run mode - skipping CMS publication');
      return {
        companyId,
        topicId,
        published: false,
        s3Location,
        cmsPostId: topicId, // Use topic ID as placeholder in dry run
        createdAt: blogPostData.createdAt ?? new Date().toISOString(),
      };
    }

    // Validate required parameters for CMS publishing
    if (!API_GATEWAY_URL || !API_GATEWAY_SUPER_USER || !API_GATEWAY_KEY) {
      throw new BlogPostPublisherError(
        'Missing required environment variables for CMS publishing',
        new Error(
          'API_GATEWAY_URL, API_GATEWAY_SUPER_USER_COMPANY_ID, or API_GATEWAY_KEY not configured',
        ),
        { event },
      );
    }

    if (!mediaId) {
      throw new BlogPostPublisherError(
        'Media ID is required for CMS publishing',
        new Error('mediaId is missing from event'),
        { event },
      );
    }

    // Publish to CMS using GraphQL mutation
    logger.info('Publishing blog post to CMS', { companyId, topicId });

    const graphqlClient = new GraphQLClient(`${API_GATEWAY_URL}/graphql`);
    const token = getGWAuthToken(API_GATEWAY_SUPER_USER, API_GATEWAY_KEY);

    // Determine the scheduled date: use from event if provided, otherwise use next Thursday
    // Ensure we never pass null/undefined to the GraphQL mutation
    const scheduledAt =
      eventScheduledAt && eventScheduledAt.trim()
        ? eventScheduledAt.trim()
        : getNextScheduledDate();

    const variables = {
      companyId,
      content: blogPostData.content,
      media: [mediaId],
      postId: topicId,
      seoDescription: blogPostData.metaDescription || blogPostData.title,
      seoTitle: blogPostData.title,
      title: blogPostData.title,
      neighborhoods: [],
      scheduledAt,
    };

    logger.info('Creating post in CMS', {
      variables: { ...variables, content: '[REDACTED]' },
    });

    const response = await graphqlClient.request<CreatePostResponse>(
      CREATE_POST_MUTATION,
      variables,
      {
        Authorization: `Bearer ${token}`,
      },
    );

    logger.info('Successfully created post in CMS', { response });

    // Send timing and count metrics
    const executionTime = Date.now() - startTime;
    await Promise.all([
      datadogMetrics.sendTimingMetric(
        METRICS.BLOG_POST_PUBLISHER_TIME,
        executionTime,
        'blog-post-publisher',
      ),
      datadogMetrics.sendIncrementMetric(
        METRICS.BLOG_POST_PUBLISHER_POSTS_COUNT,
        'blog-post-publisher',
        ['status:published', `dry_run:${dryRun}`],
      ),
    ]).catch(() => {
      /* Ignore metric errors */
    });

    return {
      companyId,
      topicId,
      published: true,
      publishedUrl: response.createPost.slug
        ? `https://example.com/blog/${response.createPost.slug}`
        : undefined,
      s3Location,
      cmsPostId: response.createPost.postId ?? response.createPost.id,
      createdAt: blogPostData.createdAt ?? new Date().toISOString(),
    };
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(METRICS.LAMBDA_ERRORS, 'blog-post-publisher', errorType)
      .catch(() => {
        /* Ignore metric errors */
      });

    // Check if this is a transient error
    if (
      error instanceof Error &&
      (error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('AWS.SimpleQueueService.NonExistentQueue'))
    ) {
      throw new TransientBlogPostPublisherError(
        'Transient error publishing blog post',
        error,
        { event },
      );
    }

    if (error instanceof BlogPostPublisherError) {
      throw error;
    }

    throw new BlogPostPublisherError(
      'Failed to publish blog post',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
