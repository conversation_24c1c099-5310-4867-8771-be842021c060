import { Handler } from 'aws-lambda';

import { ApiGatewayClient } from '../../clients/ApiGatewayClient';
import { CosmoClient } from '../../clients/CosmoClient';
import { datadogMetrics, METRICS } from '../../config/datadog';
import {
  BlogPostWriterError,
  TransientBlogPostWriterError,
  AirOpsExecutionError,
} from '../../errors/blog-post-writer';
import {
  BrandProfile,
  BlogTopic,
  AirOpsResponse,
} from '../../types/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

const AIROPS_API_URL =
  process.env.AIR_OPS_API_URL ||
  'https://api.airops.com/public_api/airops_apps';
const AIROPS_API_KEY = process.env.AIR_OPS_API_KEY || '';
const AIROPS_APP_ID = process.env.BLOG_V2_AIR_OPS_WRITER_APP_ID || '';

export interface BlogPostWriterEvent {
  companyId: string;
  selectedTopic: BlogTopic;
  s3Location: string;
  brandProfile: BrandProfile;
  dryRun: boolean;
  executionFolderName: string;
}

export interface BlogPostWriterResult {
  companyId: string;
  topicId: string;
  airopsExecutionId: string;
  status: 'initiated' | 'failed';
  dryRun: boolean;
}

export const handler: Handler<
  BlogPostWriterEvent,
  BlogPostWriterResult
> = async event => {
  const logger = createContextLogger('blog-post-writer');
  const startTime = Date.now();

  try {
    logger.info('Initiating blog post generation', { event });

    const { companyId, selectedTopic, brandProfile, dryRun } = event;

    // Validate that selected topic has neighborhood data if not in dry run
    if (
      !dryRun &&
      selectedTopic.neighborhoodId &&
      !selectedTopic.neighborhood
    ) {
      logger.error('Selected blog topic missing neighborhood object', {
        topicId: selectedTopic.id,
        neighborhoodId: selectedTopic.neighborhoodId,
        companyId,
      });
      throw new BlogPostWriterError(
        `Blog topic ${selectedTopic.id} has neighborhoodId ${selectedTopic.neighborhoodId} but missing neighborhood object. This is required for AirOps API.`,
        undefined,
        {
          topicId: selectedTopic.id,
          neighborhoodId: selectedTopic.neighborhoodId,
          companyId,
        },
      );
    }

    // In dry run mode, return mock execution ID
    if (dryRun) {
      logger.info('Dry run mode - returning mock AirOps execution');
      return {
        companyId,
        topicId: selectedTopic.id,
        airopsExecutionId: `mock-execution-${Date.now()}`,
        status: 'initiated',
        dryRun,
      };
    }

    if (!AIROPS_APP_ID || !AIROPS_API_KEY) {
      throw new BlogPostWriterError('Missing AirOps configuration', undefined, {
        missing: {
          AIROPS_APP_ID: !AIROPS_APP_ID,
          AIROPS_API_KEY: !AIROPS_API_KEY,
        },
      });
    }

    // Prepare AirOps payload
    const airopsPayload = {
      inputs: {
        blog_title: selectedTopic.title,
        content_type: selectedTopic.type,
        brand_profile: brandProfile,
        ...(selectedTopic.neighborhood && {
          neighborhood: {
            preferredName: selectedTopic.neighborhood.preferredName,
            googlePlacesName: selectedTopic.neighborhood.googlePlacesName,
          },
        }),
      },
    };

    // Call AirOps async_execute API
    const url = `${AIROPS_API_URL}/${AIROPS_APP_ID}/async_execute`;
    logger.info('Prepared AirOps payload', { payload: airopsPayload });
    logger.info('Calling AirOps API', { url });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${AIROPS_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(airopsPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new AirOpsExecutionError(
        `AirOps API returned error: ${response.status} ${response.statusText}`,
        response.status,
        errorText,
      );
    }

    const responseData = (await response.json()) as AirOpsResponse;
    logger.info('AirOps response', { responseData });

    // Send Datadog metrics for AirOps request
    await datadogMetrics
      .sendIncrementMetric(
        METRICS.BLOG_POST_WRITER_AIROPS_REQUESTS,
        'blog-post-writer',
        ['status:initiated'],
      )
      .catch(() => {
        /* Ignore metric errors */
      });

    // Extract execution ID from response
    const executionId = responseData.airops_app_execution.id;

    if (!executionId) {
      throw new BlogPostWriterError(
        'No execution ID returned from AirOps',
        undefined,
        {
          response: responseData,
        },
      );
    }

    // Update blog topic with airops execution ID
    try {
      // Validate required environment variables
      const requiredEnvVars = {
        API_GATEWAY_URL: process.env.API_GATEWAY_URL,
        API_GATEWAY_SUPER_USER_COMPANY_ID:
          process.env.API_GATEWAY_SUPER_USER_COMPANY_ID,
        API_GATEWAY_KEY: process.env.API_GATEWAY_KEY,
        COSMO_GQL_URL: process.env.COSMO_GQL_URL,
        M2M_SUPER_API_KEY: process.env.M2M_SUPER_API_KEY,
      };

      const missingEnvVars = Object.entries(requiredEnvVars)
        .filter(([, value]) => !value || value.trim() === '')
        .map(([key]) => key);

      if (missingEnvVars.length === 0) {
        // Initialize API Gateway client
        const { GraphQLClient } = await import('graphql-request');
        const apiGatewayGraphqlClient = new GraphQLClient(
          `${requiredEnvVars.API_GATEWAY_URL!}/graphql`,
        );

        const apiGatewayClient = new ApiGatewayClient(
          requiredEnvVars.API_GATEWAY_SUPER_USER_COMPANY_ID!,
          requiredEnvVars.API_GATEWAY_KEY!,
          apiGatewayGraphqlClient,
          logger,
        );

        // Initialize hybrid CosmoClient
        const cosmoClient = new CosmoClient(
          requiredEnvVars.COSMO_GQL_URL!,
          requiredEnvVars.M2M_SUPER_API_KEY!,
          apiGatewayClient,
          logger,
        );

        logger.info('Updating blog topic with airops execution ID', {
          blogTopicId: selectedTopic.id,
          airopsExecutionId: String(executionId),
        });

        await cosmoClient.updateBlogTopicAiropsId(
          selectedTopic.id,
          String(executionId),
        );

        logger.info('Successfully updated blog topic with airops execution ID');
      } else {
        logger.warn(
          'Skipping blog topic update - missing environment variables',
          {
            missingEnvVars,
          },
        );
      }
    } catch (error) {
      logger.error('Failed to update blog topic with airops execution ID', {
        error: error instanceof Error ? error.message : String(error),
        blogTopicId: selectedTopic.id,
        airopsExecutionId: String(executionId),
      });
      // Continue execution without failing - this update is not critical for the main flow
    }

    // Send timing metric
    const executionTime = Date.now() - startTime;
    await datadogMetrics
      .sendTimingMetric(
        METRICS.BLOG_POST_WRITER_TIME,
        executionTime,
        'blog-post-writer',
      )
      .catch(() => {
        /* Ignore metric errors */
      });

    return {
      companyId,
      topicId: selectedTopic.id,
      airopsExecutionId: String(executionId),
      status: 'initiated',
      dryRun,
    };
  } catch (error) {
    // Send error metric
    const errorType =
      error instanceof Error ? error.constructor.name : 'UnknownError';
    await datadogMetrics
      .sendErrorMetric(METRICS.LAMBDA_ERRORS, 'blog-post-writer', errorType)
      .catch(() => {
        /* Ignore metric errors */
      });

    // Check if this is a transient error that should be retried
    if (
      error instanceof Error &&
      (error.message.includes('ECONNREFUSED') ||
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('ENOTFOUND'))
    ) {
      throw new TransientBlogPostWriterError(
        'Network error calling AirOps API',
        error,
        { event },
      );
    }

    if (error instanceof BlogPostWriterError) {
      throw error;
    }

    throw new BlogPostWriterError(
      'Failed to initiate blog post generation',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
