import { Handler } from 'aws-lambda';

import { ProcessBlogInputsError } from '../../errors/blog-post-writer';
import { createContextLogger } from '../../utils/contextLogger';

export interface ProcessBlogInputsEvent {
  input?: {
    companyIds?: string[];
    dryRun?: boolean;
    publishToCMS?: boolean;
    skipEntitlementsValidation?: boolean;
    skipUnitsValidation?: boolean;
    skipExistingScheduledPostValidation?: boolean;
    input?: {
      companyIds?: string[];
      dryRun?: boolean;
      publishToCMS?: boolean;
      skipEntitlementsValidation?: boolean;
      skipUnitsValidation?: boolean;
      skipExistingScheduledPostValidation?: boolean;
    };
  };
  companyIds?: string[];
  dryRun?: boolean;
  publishToCMS?: boolean;
  skipEntitlementsValidation?: boolean;
  skipUnitsValidation?: boolean;
  skipExistingScheduledPostValidation?: boolean;
  executionName?: string;
  executionStartTime?: string;
}

export interface ProcessBlogInputsResult {
  companyIds: string[];
  dryRun: boolean;
  publishToCMS?: boolean;
  skipEntitlementsValidation?: boolean;
  skipUnitsValidation?: boolean;
  skipExistingScheduledPostValidation?: boolean;
  executionName?: string;
  executionStartTime?: string;
}

export const handler: Handler<
  ProcessBlogInputsEvent,
  ProcessBlogInputsResult
> = async event => {
  const logger = createContextLogger('process-blog-inputs');

  try {
    logger.info('Processing blog inputs', { event });

    // Extract input from nested structure, handling deep nesting for backward compatibility
    let input = event;
    if (event.input) {
      input = event.input;
      // Handle deeply nested input (input.input.*)
      if (event.input.input) {
        input = event.input.input;
      }
    }

    // Apply defaults
    const processedInputs: ProcessBlogInputsResult = {
      companyIds: input.companyIds || [],
      dryRun: input.dryRun ?? true,
      publishToCMS: input.publishToCMS ?? false,
      skipEntitlementsValidation: input.skipEntitlementsValidation ?? false,
      skipUnitsValidation: input.skipUnitsValidation ?? false,
      skipExistingScheduledPostValidation:
        input.skipExistingScheduledPostValidation ?? false,
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
    };

    // Validate inputs
    if (!Array.isArray(processedInputs.companyIds)) {
      throw new ProcessBlogInputsError(
        'companyIds must be an array',
        undefined,
        {
          providedType: typeof processedInputs.companyIds,
          providedValue: processedInputs.companyIds,
        },
      );
    }

    // Validate that all companyIds are strings
    if (!processedInputs.companyIds.every(id => typeof id === 'string')) {
      throw new ProcessBlogInputsError(
        'All companyIds must be strings',
        undefined,
        {
          companyIds: processedInputs.companyIds,
          nonStringIds: processedInputs.companyIds.filter(
            id => typeof id !== 'string',
          ),
        },
      );
    }

    if (typeof processedInputs.dryRun !== 'boolean') {
      throw new ProcessBlogInputsError('dryRun must be a boolean', undefined, {
        providedType: typeof processedInputs.dryRun,
        providedValue: processedInputs.dryRun,
      });
    }

    if (typeof processedInputs.publishToCMS !== 'boolean') {
      throw new ProcessBlogInputsError(
        'publishToCMS must be a boolean',
        undefined,
        {
          providedType: typeof processedInputs.publishToCMS,
          providedValue: processedInputs.publishToCMS,
        },
      );
    }

    if (typeof processedInputs.skipEntitlementsValidation !== 'boolean') {
      throw new ProcessBlogInputsError(
        'skipEntitlementsValidation must be a boolean',
        undefined,
        {
          providedType: typeof processedInputs.skipEntitlementsValidation,
          providedValue: processedInputs.skipEntitlementsValidation,
        },
      );
    }

    if (typeof processedInputs.skipUnitsValidation !== 'boolean') {
      throw new ProcessBlogInputsError(
        'skipUnitsValidation must be a boolean',
        undefined,
        {
          providedType: typeof processedInputs.skipUnitsValidation,
          providedValue: processedInputs.skipUnitsValidation,
        },
      );
    }

    if (
      typeof processedInputs.skipExistingScheduledPostValidation !== 'boolean'
    ) {
      throw new ProcessBlogInputsError(
        'skipExistingScheduledPostValidation must be a boolean',
        undefined,
        {
          providedType:
            typeof processedInputs.skipExistingScheduledPostValidation,
          providedValue: processedInputs.skipExistingScheduledPostValidation,
        },
      );
    }

    // Log the processed inputs
    logger.info('Processed inputs', {
      processedInputs,
      companiesCount: processedInputs.companyIds.length,
      dryRun: processedInputs.dryRun,
    });

    return processedInputs;
  } catch (error) {
    if (error instanceof ProcessBlogInputsError) {
      throw error;
    }
    throw new ProcessBlogInputsError(
      'Failed to process blog inputs',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
