import { Context } from 'aws-lambda';

import {
  GetAirOpsJobStatusEvent,
  handler,
} from './blog-post-writer/get-airops-job-status';

const input: GetAirOpsJobStatusEvent = {
  companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
  topicId: '74fe5843-2443-4587-be94-60813adb18f1',
  airopsExecutionId: '29578296',
  executionFolderName: 'test-execution-folder',
  dryRun: false,
};

const context = {} as Context;

const callback = () => {};

(async () => {
  const result = await handler(input, context, callback);

  console.log({ result });
})();
