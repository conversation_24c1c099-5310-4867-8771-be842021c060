import { CreateJobParams, JobPostDTO, DataForJob } from 'src/types/job';

export class JobDTOFormatter {
  static format(params: CreateJobParams): JobPostDTO[] {
    const { jobs, step, status, error } = params;
    return jobs.map(j => ({
      jobId: j.jobId,
      companyId: j.companyId,
      step,
      status,
      ...(error && {
        errorMessage: {
          message: error.message,
          name: error.name,
          stack: error.stack,
        },
      }),
    }));
  }

  static toJobsArray(jobs: DataForJob | DataForJob[]): DataForJob[] {
    return Array.isArray(jobs) ? jobs : [jobs];
  }
}
