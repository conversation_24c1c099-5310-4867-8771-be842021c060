import { PostDTO, Post } from 'src/types/post';

export class PostDTOFormatter {
  static formatForRequest(posts: Post[], environment: string): PostDTO[] {
    return posts.map(
      ({ businessName, websiteUrl, companyId, jobId, neighborhoods }) => ({
        inputs: {
          job_id: jobId,
          business_name: businessName,
          agent_s_website_url: websiteUrl,
          company_id: companyId,
          environment,
          neighborhoods: JSON.stringify(neighborhoods || []),
        },
      }),
    );
  }

  static formatFromRequest(post: PostDTO): Post {
    const {
      job_id,
      business_name,
      agent_s_website_url,
      company_id,
      neighborhoods,
    } = post.inputs;
    return {
      jobId: job_id,
      businessName: business_name,
      websiteUrl: agent_s_website_url,
      companyId: company_id,
      neighborhoods: neighborhoods ? JSON.parse(neighborhoods) : [],
    };
  }
}
