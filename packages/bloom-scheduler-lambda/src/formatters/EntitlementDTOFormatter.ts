import { EnrichedEntitlement } from 'src/types/entitlement';
import { EntitlementDTO, Entitlement } from 'src/types/entitlement';
import { Post } from 'src/types/post';

export class EntitlementDTOFormatter {
  static formatEntitlementsForFetch(
    entitlements: EnrichedEntitlement[],
  ): Post[] {
    return entitlements.map(
      ({
        productId,
        companyId,
        websiteUrl,
        jobId,
        displayName,
        neighborhoods,
      }) => ({
        jobId,
        productId,
        companyId,
        businessName: displayName,
        websiteUrl: websiteUrl || '',
        neighborhoods,
      }),
    );
  }

  static formatEntitlementsFromFetch(
    entitlements: EntitlementDTO[],
  ): Entitlement[] {
    return entitlements.map(e => ({
      productId: e.productId,
      companyId: e.companyId,
      productName: e.product?.name,
      endDate: e.endDate,
      units: e.units,
      websiteUrl: e.company?.website || '',
      displayName: e.company?.name || '',
    }));
  }
}
