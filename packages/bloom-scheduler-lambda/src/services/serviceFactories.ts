import { GraphQLClient } from 'graphql-request';
import {
  AirOpsClient,
  ApiGatewayClient,
  CMSClient,
  TenantClient,
} from 'src/clients';
import { getConfig } from 'src/config';
import { Day } from 'src/types/date';
import { JobStep } from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

import { EntitlementFetchService } from './entitlement/EntitlementFetchService';
import { EntitlementValidationService } from './entitlement/EntitlementValidationService';
import { EntitlementService } from './EntitlementService';
import { JobFetchService } from './job/JobFetchService';
import { JobService } from './JobService';
import { PostFetchService } from './post/PostFetchService';
import { PostService } from './PostService';

// For services or clients that multiple services use, cache them here

let cmsClient: CMSClient;

function getCMSClient(logger: ContextLogger): CMSClient {
  if (!cmsClient) {
    const config = getConfig();
    cmsClient = new CMSClient(config.CMS_SERVICE_URL!, logger);
  }
  return cmsClient;
}

// Multiple services use the JobService so we cache it here to cache both
// it's dependencies and the service itself
function getJobFetchService(logger: ContextLogger): JobFetchService {
  return new JobFetchService(logger, getCMSClient(logger));
}

export function createJobService(logger: ContextLogger): JobService {
  return new JobService(logger, getJobFetchService(logger));
}

function createApiGatewayClient(logger: ContextLogger): ApiGatewayClient {
  const config = getConfig();

  const transformedUrl = config.API_GATEWAY_URL.replace(/staging\./g, '');

  logger.info('Creating ApiGatewayClient with URL transformation', {
    originalUrl: config.API_GATEWAY_URL,
    transformedUrl,
  });

  const graphqlClient = new GraphQLClient(`${transformedUrl}/graphql`);
  return new ApiGatewayClient(
    config.API_GATEWAY_SUPER_USER_COMPANY_ID,
    config.API_GATEWAY_KEY,
    graphqlClient,
    logger,
  );
}

export function createEntitlementService(
  logger: ContextLogger,
): EntitlementService {
  const config = getConfig();
  const tenantClient = new TenantClient(
    config.TENANT_SERVICE_URL,
    config.ENTITLEMENTS_LIMIT,
    logger,
    config.AUTOMATED_BLOGS_PRODUCT_ID,
  );

  const apiGatewayClient = createApiGatewayClient(logger);

  const fetchService = new EntitlementFetchService(
    tenantClient,
    apiGatewayClient,
    logger,
  );
  const validationService = new EntitlementValidationService(
    config.GENERATE_POST_DAY as keyof typeof Day,
    config.TESTING_TODAY_DATE,
    logger,
  );

  const jobService = createJobService(logger);

  return new EntitlementService(
    config.ENTITLEMENTS_CHUNK_SIZE,
    logger,
    fetchService,
    validationService,
    jobService,
  );
}

export function createPostService(
  logger: ContextLogger,
  step: JobStep,
): PostService {
  const config = getConfig();
  const airOpsClient = new AirOpsClient(
    config.AIR_OPS_API_URL,
    config.AIR_OPS_WORKFLOW_ID,
    config.AIR_OPS_API_KEY,
    logger,
  );

  const fetchService = new PostFetchService(airOpsClient, config.ENVIRONMENT);
  const jobService = createJobService(logger);

  return new PostService(fetchService, jobService, step);
}
