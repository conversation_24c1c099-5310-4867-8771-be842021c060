import { CMSClient } from 'src/clients';
import { JobDTOFormatter } from 'src/formatters/JobDTOFormatter';
import { JobDTO, JobPostDTO, CreateJobParams } from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

export class JobFetchService {
  constructor(
    private readonly logger: ContextLogger,
    private readonly cmsClient: CMSClient,
  ) {}

  async getJobs(jobId: string): Promise<JobDTO[]> {
    const jobs = await this.cmsClient.getJob(jobId);
    this.logger.info('Fetched jobs', {
      jobs: jobs.map(j => ({
        jobId: j.jobId,
        companyId: j.companyId,
        step: j.step,
      })),
      jobCount: jobs.length,
    });
    return jobs;
  }

  async createJobs(params: CreateJobParams): Promise<JobDTO[]> {
    const jobs: JobPostDTO[] = JobDTOFormatter.format(params);
    return await this.cmsClient.createJobs(jobs);
  }
}
