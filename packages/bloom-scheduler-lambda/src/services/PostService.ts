/**
 * PostService - Primary Orchestrator
 *
 * Central coordinator for all post-related operations in the Bloom Scheduler.
 * This service orchestrates the complete post lifecycle of creating the posts
 *
 * All post operations must flow through this orchestrator to maintain
 * consistency and proper coordination between sub-services.
 */

import { JobStep } from 'src/types/job';
import { PostStateTracker, Post } from 'src/types/post';

import { JobService } from './JobService';
import { PostFetchService } from './post/PostFetchService';

export class PostService {
  constructor(
    private readonly fetchService: PostFetchService,
    private readonly jobService: JobService,
    private readonly step: JobStep,
  ) {}

  private async createPostJobs(
    postStateTracker: PostStateTracker,
  ): Promise<void> {
    const { successfulPosts, failedPosts } = postStateTracker;
    if (successfulPosts.length > 0) {
      await this.jobService.createSuccessJobs(successfulPosts, this.step);
    }

    if (failedPosts.length > 0) {
      await Promise.allSettled(
        failedPosts.map(async ({ post, error }) => {
          await this.jobService.createErrorJob(post, this.step, error);
        }),
      );
      throw new Error('Failed to create posts');
    }
  }

  async createPosts(
    posts: Post[],
    postStateTracker: PostStateTracker,
  ): Promise<PostStateTracker> {
    const createPostsResponse = await this.fetchService.createPosts(
      posts,
      postStateTracker,
    );

    await this.createPostJobs(createPostsResponse);

    return createPostsResponse;
  }
}
