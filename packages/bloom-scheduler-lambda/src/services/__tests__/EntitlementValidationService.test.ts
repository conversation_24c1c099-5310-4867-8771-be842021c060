import { Day } from 'src/types/date';
import { ContextLogger } from 'src/utils/contextLogger';

import { EntitlementValidationService } from '../entitlement/EntitlementValidationService';

jest.mock('src/config', () => ({
  getConfig: () => ({
    automatedBlogsProductId: 'test-product-id',
    ENVIRONMENT: 'test',
  }),
}));

describe('EntitlementValidationService', () => {
  let mockLogger: { info: jest.Mock; error: jest.Mock; createChild: jest.Mock };

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      createChild: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
      }),
    };
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const mockEntitlements = [
    {
      productId: '1',
      companyId: '1',
      productName: 'AI Blogs',
      endDate: null,
      units: 2,
      websiteUrl: 'test1.com',
      displayName: 'Test Company 1',
    },
    {
      productId: '1',
      companyId: '2',
      productName: 'AI Blogs',
      endDate: null,
      units: 2,
      websiteUrl: 'test2.com',
      displayName: 'Test Company 2',
    },
    {
      productId: '1',
      companyId: '3',
      productName: 'AI Blogs',
      endDate: null,
      units: 4,
      websiteUrl: 'test3.com',
      displayName: 'Test Company 3',
    },
    {
      productId: '1',
      companyId: '4',
      productName: 'AI Blogs',
      endDate: null,
      units: 4,
      websiteUrl: 'test4.com',
      displayName: 'Test Company 4',
    },
  ];

  describe('validateEntitlementsByUnits', () => {
    it('validates entitlements based on units for the first week of the month', () => {
      const service = new EntitlementValidationService(
        Day[Day.TUESDAY] as unknown as keyof typeof Day,
        '2024-01-02',
        mockLogger as unknown as ContextLogger,
      );
      const validatedEntitlements =
        service.validateEntitlementsByUnits(mockEntitlements);
      expect(validatedEntitlements).toHaveLength(4);
    });

    it('validates entitlements based on units for the second week of the month', () => {
      const service = new EntitlementValidationService(
        Day[Day.TUESDAY] as unknown as keyof typeof Day,
        '2024-01-09',
        mockLogger as unknown as ContextLogger,
      );
      const validatedEntitlements =
        service.validateEntitlementsByUnits(mockEntitlements);
      expect(validatedEntitlements).toHaveLength(2);
      validatedEntitlements.forEach(entitlement => {
        expect(entitlement.units).toBe(4);
      });
    });
  });

  describe('validateByEndDate', () => {
    it('returns all entitlements if endDate is null', () => {
      const service = new EntitlementValidationService(
        Day[Day.TUESDAY] as unknown as keyof typeof Day,
        '2024-01-02',
        mockLogger as unknown as ContextLogger,
      );
      const validatedEntitlements = service.validateByEndDate(mockEntitlements);
      expect(validatedEntitlements).toHaveLength(4);
    });

    it('returns entitlements that have an endDate in the future', () => {
      const mockEntitlementsWithEndDate = [
        ...mockEntitlements,
        {
          ...mockEntitlements[0],
          endDate: '2030-01-03',
        },
      ];
      const service = new EntitlementValidationService(
        Day[Day.TUESDAY] as unknown as keyof typeof Day,
        '2024-01-02',
        mockLogger as unknown as ContextLogger,
      );
      const validatedEntitlements = service.validateByEndDate(
        mockEntitlementsWithEndDate,
      );
      expect(validatedEntitlements).toHaveLength(5);
    });

    it('should not return entitlements that have an endDate in the past', () => {
      const mockEntitlementsWithEndDate = [
        ...mockEntitlements,
        {
          ...mockEntitlements[0],
          endDate: '2023-01-01',
        },
      ];
      const service = new EntitlementValidationService(
        Day[Day.TUESDAY] as unknown as keyof typeof Day,
        '2024-01-02',
        mockLogger as unknown as ContextLogger,
      );
      const validatedEntitlements = service.validateByEndDate(
        mockEntitlementsWithEndDate,
      );
      expect(validatedEntitlements).toHaveLength(4);
      expect(validatedEntitlements.length).toBeLessThan(
        mockEntitlementsWithEndDate.length,
      );
    });
  });
});
