import { JobFetchService } from 'src/services/job/JobFetchService';
import { JobService } from 'src/services/JobService';
import { BaseJobStep, RetryStep } from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

describe('JobService', () => {
  let service: JobService;
  let mockLogger: { info: jest.Mock; error: jest.Mock; createChild: jest.Mock };
  let mockFetchService: {
    getJobs: jest.Mock;
    createJobs: jest.Mock;
  };

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      createChild: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
      }),
    };
    mockFetchService = {
      getJobs: jest.fn(),
      createJobs: jest.fn(),
    };
    service = new JobService(
      mockLogger as unknown as ContextLogger,
      mockFetchService as unknown as JobFetchService,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getJobInfo', () => {
    it('returns retry step and companyId for valid job', async () => {
      const mockJobs = [
        {
          companyId: 'test-company',
          step: BaseJobStep.GENERATE_POST,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ];
      mockFetchService.getJobs.mockResolvedValue(mockJobs);

      const result = await service.getJobInfo('test-job');
      expect(result).toEqual({
        retryStep: RetryStep.GENERATE_POST_RETRY_1,
        companyId: 'test-company',
      });
    });

    it('returns most recent job when multiple exist', async () => {
      const mockJobs = [
        {
          companyId: 'test-company',
          step: BaseJobStep.GENERATE_POST,
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          companyId: 'test-company',
          step: BaseJobStep.GENERATE_POST,
          createdAt: '2024-01-02T00:00:00Z',
        },
      ];
      mockFetchService.getJobs.mockResolvedValue(mockJobs);

      const result = await service.getJobInfo('test-job');
      expect(result?.companyId).toBe('test-company');
    });

    it('throws error when no companyId found', async () => {
      mockFetchService.getJobs.mockResolvedValue([
        {
          step: BaseJobStep.GENERATE_POST,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ]);

      await expect(service.getJobInfo('test-job')).rejects.toThrow();
    });

    it('returns undefined when max retries reached', async () => {
      const mockJobs = [
        {
          companyId: 'test-company',
          step: RetryStep.GENERATE_POST_RETRY_3,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ];
      mockFetchService.getJobs.mockResolvedValue(mockJobs);

      const result = await service.getJobInfo('test-job');
      expect(result).toBeUndefined();
    });
  });
});
