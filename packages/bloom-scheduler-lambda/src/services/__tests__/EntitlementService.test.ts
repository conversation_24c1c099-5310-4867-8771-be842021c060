import { ContextLogger } from 'src/utils/contextLogger';

import { EntitlementFetchService } from '../entitlement/EntitlementFetchService';
import { EntitlementValidationService } from '../entitlement/EntitlementValidationService';
import { EntitlementService } from '../EntitlementService';
import { JobService } from '../JobService';

describe('EntitlementService', () => {
  let service: EntitlementService;
  let mockLogger: {
    info: jest.Mock;
    error: jest.Mock;
    warn: jest.Mock;
    createChild: jest.Mock;
  };
  let mockFetchService: {
    fetchAllEntitlements: jest.Mock;
    fetchBrandAttributes: jest.Mock;
    fetchAllWebsites: jest.Mock;
    fetchAllCompanyNeighborhoods: jest.Mock;
  };
  let mockValidationService: {
    validateEntitlementsByFeatureFlags: jest.Mock;
    validateEntitlementsByUnits: jest.Mock;
    validateByEndDate: jest.Mock;
  };
  let mockJobService: { createSuccessJobs: jest.Mock };

  jest.mock('p-limit', () => {
    return () => (fn: () => Promise<unknown>) => fn(); // runs functions immediately
  });

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      createChild: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        createChild: jest.fn().mockReturnValue({
          info: jest.fn(),
          error: jest.fn(),
        }),
      }),
    };
    mockFetchService = {
      fetchAllEntitlements: jest.fn(),
      fetchBrandAttributes: jest.fn(),
      fetchAllWebsites: jest.fn(),
      fetchAllCompanyNeighborhoods: jest.fn(),
    };
    mockValidationService = {
      validateEntitlementsByFeatureFlags: jest.fn(),
      validateEntitlementsByUnits: jest.fn(),
      validateByEndDate: jest.fn(),
    };
    mockJobService = {
      createSuccessJobs: jest.fn(),
    };

    service = new EntitlementService(
      2,
      mockLogger as unknown as ContextLogger,
      mockFetchService as unknown as EntitlementFetchService,
      mockValidationService as unknown as EntitlementValidationService,
      mockJobService as unknown as JobService,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const mockEntitlements = [
    {
      companyId: 'test1',
      productId: '1',
      productName: 'AI Blogs',
      endDate: null,
      units: 2,
      websiteUrl: 'test1.com',
    },
    {
      companyId: 'test2',
      productId: '1',
      productName: 'AI Blogs',
      endDate: null,
      units: 4,
      websiteUrl: 'test2.com',
    },
  ];
  const mockDisplayNames = [
    { displayName: 'Test 1', companyId: 'test1' },
    { displayName: 'Test 2', companyId: 'test2' },
  ];

  const mockBrandWithPreferences = [
    {
      displayName: 'Test 1',
      companyId: 'test1',
      blogPreferences: {
        preferredLocations: ['neighborhood1', 'neighborhood2'],
      },
    },
  ];

  const mockBrandWithoutPreferences = [
    {
      displayName: 'Test 1',
      companyId: 'test1',
      blogPreferences: {},
    },
  ];

  const mockBrandWithEmptyPreferences = [
    {
      displayName: 'Test 1',
      companyId: 'test1',
      blogPreferences: { preferredLocations: [] },
    },
  ];

  describe('processEntitlementsFromEvent', () => {
    it('processes entitlements with validation', async () => {
      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlements);
      mockFetchService.fetchBrandAttributes.mockResolvedValue(mockDisplayNames);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlements);
      mockValidationService.validateByEndDate.mockReturnValue(mockEntitlements);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlements,
      );
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlements,
      );

      const result = await service.processEntitlementsFromEvent({});
      expect(result.length).toBe(2);
    });

    it('skips validation when specified', async () => {
      const mockEntitlements = [
        { companyId: 'test1', units: 1, websiteUrl: 'test.com' },
      ];
      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlements);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlements);
      mockFetchService.fetchBrandAttributes.mockResolvedValue([
        { companyId: 'test1', displayName: 'Test 1' },
      ]);

      const result = await service.processEntitlementsFromEvent({
        skipValidation: true,
        companyIds: ['test1'],
      });

      expect(
        mockValidationService.validateEntitlementsByFeatureFlags,
      ).not.toHaveBeenCalled();
      expect(result.length).toBe(1);
    });

    it('Should only return entitlements with websiteUrl', async () => {
      const mockEntitlementsWithoutWebsite = [
        ...mockEntitlements,
        {
          companyId: 'test3',
          productId: '1',
          productName: 'AI Blogs',
          endDate: null,
          units: 4,
        },
        {
          companyId: 'test4',
          productId: '1',
          productName: 'AI Blogs',
          endDate: null,
          units: 4,
        },
      ];
      mockFetchService.fetchAllEntitlements.mockResolvedValue(
        mockEntitlementsWithoutWebsite,
      );
      mockFetchService.fetchBrandAttributes.mockResolvedValue(mockDisplayNames);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlements,
      );
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlements);
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlements,
      );

      const result = await service.processEntitlementsFromEvent({
        skipValidation: true,
        companyIds: ['test1', 'test2', 'test3', 'test4'],
      });
      // 2 calls due to throttling
      expect(mockFetchService.fetchBrandAttributes).toHaveBeenCalledTimes(2);
      expect(mockFetchService.fetchBrandAttributes).toHaveBeenCalledWith([
        'test1',
      ]);
      expect(mockFetchService.fetchBrandAttributes).toHaveBeenCalledWith([
        'test2',
      ]);

      expect(result.length).toEqual(2);
    });

    it('uses brand preferred locations when available', async () => {
      const mockEntitlement = [
        {
          companyId: 'test1',
          units: 1,
          websiteUrl: 'test.com',
          neighborhoods: ['default1', 'default2'],
        },
      ];

      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchBrandAttributes.mockResolvedValue(
        mockBrandWithPreferences,
      );
      mockFetchService.fetchAllCompanyNeighborhoods.mockResolvedValue([
        'Preferred Neighborhood 1',
        'Preferred Neighborhood 2',
      ]);
      mockValidationService.validateByEndDate.mockReturnValue(mockEntitlement);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlement,
      );
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlement,
      );

      const result = await service.processEntitlementsFromEvent({});

      expect(
        mockFetchService.fetchAllCompanyNeighborhoods,
      ).toHaveBeenCalledWith('test1', ['neighborhood1', 'neighborhood2']);
      expect(result[0].neighborhoods).toEqual([
        'Preferred Neighborhood 1',
        'Preferred Neighborhood 2',
      ]);
    });

    it('falls back to entitlement neighborhoods when brand has no preferred locations', async () => {
      const mockEntitlement = [
        {
          companyId: 'test1',
          units: 1,
          websiteUrl: 'test.com',
          neighborhoods: ['default1', 'default2'],
        },
      ];

      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchBrandAttributes.mockResolvedValue(
        mockBrandWithoutPreferences,
      );
      mockFetchService.fetchAllCompanyNeighborhoods.mockResolvedValue([
        'Default Neighborhood 1',
        'Default Neighborhood 2',
      ]);
      mockValidationService.validateByEndDate.mockReturnValue(mockEntitlement);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlement,
      );
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlement,
      );

      const result = await service.processEntitlementsFromEvent({});

      expect(
        mockFetchService.fetchAllCompanyNeighborhoods,
      ).toHaveBeenCalledWith('test1', ['default1', 'default2']);
      expect(result[0].neighborhoods).toEqual([
        'Default Neighborhood 1',
        'Default Neighborhood 2',
      ]);
    });

    it('falls back to entitlement neighborhoods when brand has empty preferred locations', async () => {
      const mockEntitlement = [
        {
          companyId: 'test1',
          units: 1,
          websiteUrl: 'test.com',
          neighborhoods: ['default1', 'default2'],
        },
      ];

      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchBrandAttributes.mockResolvedValue(
        mockBrandWithEmptyPreferences,
      );
      mockFetchService.fetchAllCompanyNeighborhoods.mockResolvedValue([
        'Default Neighborhood 1',
        'Default Neighborhood 2',
      ]);
      mockValidationService.validateByEndDate.mockReturnValue(mockEntitlement);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlement,
      );
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlement,
      );

      const result = await service.processEntitlementsFromEvent({});

      expect(
        mockFetchService.fetchAllCompanyNeighborhoods,
      ).toHaveBeenCalledWith('test1', ['default1', 'default2']);
      expect(result[0].neighborhoods).toEqual([
        'Default Neighborhood 1',
        'Default Neighborhood 2',
      ]);
    });

    it('falls back to empty array when entitlement has no neighborhoods and brand has no preferences', async () => {
      const mockEntitlement = [
        { companyId: 'test1', units: 1, websiteUrl: 'test.com' },
      ];

      mockFetchService.fetchAllEntitlements.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchAllWebsites.mockResolvedValue(mockEntitlement);
      mockFetchService.fetchBrandAttributes.mockResolvedValue(
        mockBrandWithoutPreferences,
      );
      mockFetchService.fetchAllCompanyNeighborhoods.mockResolvedValue([]);
      mockValidationService.validateByEndDate.mockReturnValue(mockEntitlement);
      mockValidationService.validateEntitlementsByFeatureFlags.mockResolvedValue(
        mockEntitlement,
      );
      mockValidationService.validateEntitlementsByUnits.mockReturnValue(
        mockEntitlement,
      );

      const result = await service.processEntitlementsFromEvent({});

      expect(
        mockFetchService.fetchAllCompanyNeighborhoods,
      ).toHaveBeenCalledWith('test1', []);
      expect(result[0].neighborhoods).toEqual([]);
    });
  });

  describe('chunkEntitlements', () => {
    it('splits entitlements into correct chunk sizes', () => {
      const posts = [
        {
          companyId: '1',
          websiteUrl: 'test1.com',
          businessName: 'Test 1',
          jobId: '1',
        },
        {
          companyId: '2',
          websiteUrl: 'test2.com',
          businessName: 'Test 2',
          jobId: '2',
        },
        {
          companyId: '3',
          websiteUrl: 'test3.com',
          businessName: 'Test 3',
          jobId: '3',
        },
      ];

      const chunks = service.chunkEntitlements(posts);
      expect(chunks).toHaveLength(2);
      expect(chunks[0]).toHaveLength(2);
      expect(chunks[1]).toHaveLength(1);
    });
  });
});
