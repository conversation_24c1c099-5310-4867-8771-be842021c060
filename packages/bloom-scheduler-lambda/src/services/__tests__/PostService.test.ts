import { JobService } from 'src/services/JobService';
import { PostFetchService } from 'src/services/post/PostFetchService';
import { PostService } from 'src/services/PostService';
import { BaseJobStep } from 'src/types/job';

describe('PostService', () => {
  let service: PostService;
  let mockFetchService: {
    createPosts: jest.Mock;
  };
  let mockJobService: {
    createSuccessJobs: jest.Mock;
    createErrorJob: jest.Mock;
  };
  const step = BaseJobStep.GENERATE_POST;

  beforeEach(() => {
    mockFetchService = {
      createPosts: jest.fn(),
    };
    mockJobService = {
      createSuccessJobs: jest.fn(),
      createErrorJob: jest.fn(),
    };
    service = new PostService(
      mockFetchService as unknown as PostFetchService,
      mockJobService as unknown as JobService,
      step,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  const mockPosts = [
    {
      jobId: 'job1',
      companyId: 'company1',
      businessName: 'Company 1',
      websiteUrl: 'https://www.company1.com',
    },
    {
      jobId: 'job2',
      companyId: 'company2',
      businessName: 'Company 2',
      websiteUrl: 'https://www.company2.com',
    },
  ];

  const mockPostStateTracker = {
    totalPosts: 2,
    totalSuccessfulPosts: 0,
    successfulPosts: [],
    totalFailedPosts: 0,
    failedPosts: [],
  };

  describe('createPosts', () => {
    it('creates posts successfully', async () => {
      const successResponse = {
        ...mockPostStateTracker,
        totalSuccessfulPosts: 2,
        successfulPosts: mockPosts,
      };

      mockFetchService.createPosts.mockResolvedValue(successResponse);

      const result = await service.createPosts(mockPosts, mockPostStateTracker);
      expect(result.successfulPosts).toHaveLength(2);
      expect(mockJobService.createSuccessJobs).toHaveBeenCalledWith(
        mockPosts,
        step,
      );
    });

    it('handles failed posts', async () => {
      const error = new Error('Post creation failed');
      const failureResponse = {
        ...mockPostStateTracker,
        totalFailedPosts: 1,
        failedPosts: [{ post: mockPosts[0], error }],
      };

      mockFetchService.createPosts.mockResolvedValue(failureResponse);

      await expect(
        service.createPosts(mockPosts, mockPostStateTracker),
      ).rejects.toThrow('Failed to create posts');
      expect(mockJobService.createErrorJob).toHaveBeenCalledWith(
        mockPosts[0],
        step,
        error,
      );
    });

    it('handles partial success scenario', async () => {
      const error = new Error('Post creation failed');
      const partialSuccessResponse = {
        successfulPosts: [mockPosts[0]],
        failedPosts: [{ post: mockPosts[1], error }],
      };

      mockFetchService.createPosts.mockResolvedValue(partialSuccessResponse);

      try {
        await service.createPosts(mockPosts, mockPostStateTracker);
      } catch (e) {
        expect(e.message).toBe('Failed to create posts');
      }

      expect(mockJobService.createSuccessJobs).toHaveBeenCalledWith(
        [mockPosts[0]],
        step,
      );
      expect(mockJobService.createErrorJob).toHaveBeenCalledWith(
        mockPosts[1],
        step,
        error,
      );
    });
  });
});
