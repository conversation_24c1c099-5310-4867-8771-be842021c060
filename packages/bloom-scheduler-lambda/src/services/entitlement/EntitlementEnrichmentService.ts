import { Brand } from 'src/types/brand';
import { Entitlement, EnrichedEntitlement } from 'src/types/entitlement';
import { v4 as uuidv4 } from 'uuid';

export class EntitlementEnrichmentService {
  private static enrichEntitlementsWithJobIds(
    entitlements: Entitlement[],
  ): EnrichedEntitlement[] {
    if (!entitlements?.length) {
      return [];
    }
    return entitlements.map(e => ({ ...e, jobId: uuidv4() }));
  }

  private static enrichEntitlementsWithDisplayNames(
    entitlements: Entitlement[],
    displayNames: Brand[],
  ): Partial<EnrichedEntitlement>[] {
    if (!entitlements?.length) {
      return [];
    }
    if (!displayNames?.length) {
      return entitlements.map(e => ({ ...e, displayName: '' }));
    }

    const companyDisplayNameMap = new Map(
      displayNames.map(brand => [brand.companyId, brand.displayName]),
    );
    return entitlements.map(entitlement => ({
      ...entitlement,
      displayName: companyDisplayNameMap.get(entitlement.companyId) || '',
    }));
  }

  static enrichEntitlements(
    entitlements: Entitlement[],
    displayNames: Brand[],
  ): EnrichedEntitlement[] {
    const withJobIds =
      EntitlementEnrichmentService.enrichEntitlementsWithJobIds(
        entitlements,
      ) as EnrichedEntitlement[];
    return EntitlementEnrichmentService.enrichEntitlementsWithDisplayNames(
      withJobIds,
      displayNames,
    ) as EnrichedEntitlement[];
  }
}
