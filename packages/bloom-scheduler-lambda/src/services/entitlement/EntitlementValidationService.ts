import { Day } from 'src/types/date';
import { Entitlement } from 'src/types/entitlement';
import { FeatureFlags } from 'src/types/featureFlags';
import { LaunchDarkly, DateUtils, ContextLogger } from 'src/utils';

class EntitlementValidationError extends Error {
  constructor(
    message: string,
    public readonly details: {
      entitlements?: Entitlement[];
      error?: unknown;
    },
  ) {
    super(`EntitlementValidation: ${message}`);
    this.name = this.constructor.name;
  }
}

export class EntitlementValidationService {
  constructor(
    private readonly generatePostDay: keyof typeof Day,
    private readonly testingTodayDate: string | undefined,
    private readonly logger: ContextLogger,
  ) {}

  private static isValidForDate(
    units: number,
    todayDate: Date,
    validGeneratePostDates: Date[],
  ): boolean {
    if (!units) return false;

    const utcDate = DateUtils.toUTCMidnight(todayDate);

    // For 4 units, any matching date is valid
    if (units === 4) {
      return validGeneratePostDates.some(postDate =>
        DateUtils.isSameUTCDay(postDate, utcDate),
      );
    }

    // For 2 units, only 1st and 3rd weeks are valid (index 0 and 2)
    if (units === 2) {
      const validWeeks = [validGeneratePostDates[0], validGeneratePostDates[2]];

      return validWeeks.some(postDate =>
        DateUtils.isSameUTCDay(postDate, utcDate),
      );
    }

    return false;
  }

  private static validate({
    entitlements,
    todayDate,
    generatePostDay,
    logger,
  }: {
    entitlements: Entitlement[];
    todayDate: Date;
    generatePostDay: Day;
    logger: ContextLogger;
  }): Entitlement[] {
    const validGeneratePostDates = DateUtils.getWeekdaysInMonth(
      todayDate,
      generatePostDay,
    );

    logger.info('Validating entitlements with these parameters', {
      todayDate: todayDate.toISOString(),
      generatePostDay: Day[generatePostDay],
      validGeneratePostDates: validGeneratePostDates.map(d => d.toISOString()),
      entitlementCount: entitlements.length,
    });

    return entitlements
      .map(entitlement => ({
        ...entitlement,
        valid: EntitlementValidationService.isValidForDate(
          entitlement.units,
          todayDate,
          validGeneratePostDates,
        ),
      }))
      .filter(entitlement => entitlement.valid)
      .map(entitlement => {
        const { valid, ...rest } = entitlement;
        return rest;
      });
  }

  validateEntitlementsByUnits(entitlements: Entitlement[]): Entitlement[] {
    this.logger.info('Validating entitlements by units', {
      entitlementsLength: entitlements.length,
    });

    try {
      const generatePostDay = Day[this.generatePostDay];

      // If a testing date is provided, use it instead of the current date
      const today: Date = this.testingTodayDate
        ? new Date(this.testingTodayDate)
        : new Date();

      const utcToday = DateUtils.toUTCMidnight(today);

      const validatedEntitlements = EntitlementValidationService.validate({
        entitlements,
        todayDate: utcToday,
        generatePostDay,
        logger: this.logger,
      });

      this.logger.info('Validated entitlements', {
        validatedEntitlements,
        validatedEntitlementsLength: validatedEntitlements.length,
      });

      return validatedEntitlements;
    } catch (error) {
      this.logger.error('Error validating entitlements by unit', { error });
      throw new EntitlementValidationError(
        'Error validating entitlements by unit',
        {
          entitlements,
          error,
        },
      );
    }
  }

  private async checkFeatureFlags(companyId: string): Promise<boolean> {
    return LaunchDarkly.checkVariation(
      FeatureFlags.ENABLE_BLOOM_PER_COMPANY,
      false,
      companyId,
    );
  }

  async validateEntitlementsByFeatureFlags(
    entitlements: Entitlement[],
  ): Promise<Entitlement[]> {
    try {
      const featureFlagEntitlements = await Promise.all(
        entitlements.map(async entitlement => {
          const enabled = await this.checkFeatureFlags(entitlement.companyId);
          return { entitlement, enabled };
        }),
      );

      return featureFlagEntitlements
        .filter(({ enabled, entitlement }) => {
          if (!enabled) {
            this.logger.info('Feature flag disabled for company', {
              companyId: entitlement.companyId,
            });
          }
          return enabled;
        })
        .map(({ entitlement }) => entitlement);
    } catch (error) {
      this.logger.error('Error validating entitlements by feature flags', {
        error,
      });
      throw new EntitlementValidationError(
        'Error validating entitlements by feature flags',
        {
          entitlements,
          error,
        },
      );
    }
  }

  validateByEndDate(entitlements: Entitlement[]): Entitlement[] {
    return entitlements.filter(entitlement => {
      if (!entitlement.endDate) {
        return true;
      }
      const endDate = DateUtils.toUTCMidnight(new Date(entitlement.endDate));
      const today = DateUtils.toUTCMidnight(new Date());
      return endDate.getTime() > today.getTime();
    });
  }
}
