import pLimit from 'p-limit';
import { ApiGatewayClient, TenantClient } from 'src/clients';
import { EntitlementDTOFormatter } from 'src/formatters';
import { Brand } from 'src/types/brand';
import { Entitlement } from 'src/types/entitlement';
import { Website } from 'src/types/website';
import { ContextLogger } from 'src/utils/contextLogger';

export class EntitlementFetchService {
  constructor(
    private readonly tenantClient: TenantClient,
    private readonly apiGatewayClient: ApiGatewayClient,
    private readonly logger: ContextLogger,
  ) {}

  async fetchAllEntitlements(): Promise<Entitlement[]> {
    const entitlements = await this.tenantClient.getAllEntitlements();
    return EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlements);
  }

  async fetchBrandAttributes(companyIds: string[]): Promise<Brand[]> {
    return await this.apiGatewayClient.getCompanyBrands(companyIds);
  }
  /**
   * For a list of entitlements, sets websiteUrl with a valid LIVE website hostname if existent
   * @param entitlements {Entitlement[]}
   * @returns entitlements with websiteUrl set
   */
  async fetchAllWebsites(entitlements: Entitlement[]): Promise<Entitlement[]> {
    const limit = pLimit(5);

    // Create a context logger for this operation
    const operationLogger = this.logger.createChild({
      operation: 'fetchAllWebsites',
    });

    const withWebsites = await Promise.all(
      entitlements.map((entitlement, index) => {
        const wrappedFn = async () => {
          // Create a child logger for each concurrent operation to preserve context
          const itemLogger = operationLogger.createChild({
            companyId: entitlement.companyId,
            itemIndex: index,
            totalItems: entitlements.length,
          });

          try {
            const websites: Website[] =
              await this.apiGatewayClient.getCompanyWebsites(
                entitlement.companyId,
              );

            itemLogger.info(
              `Fetched websites from company ${entitlement.companyId}`,
              { websites },
            );

            if (!websites.length) {
              entitlement.websiteUrl = '';
              return null;
            }

            const website =
              (entitlement.websiteUrl &&
                websites.find(w =>
                  entitlement.websiteUrl!.includes(w.hostname),
                )) ||
              websites[0];
            entitlement.websiteUrl = website.hostname;

            itemLogger.info('Successfully set website URL', {
              companyId: entitlement.companyId,
              websiteUrl: entitlement.websiteUrl,
            });

            return entitlement;
          } catch (error) {
            itemLogger.error('Error fetching websites', {
              error: error instanceof Error ? error.message : String(error),
              companyId: entitlement.companyId,
            });
            entitlement.websiteUrl = '';
            return null;
          }
        };
        return limit(wrappedFn);
      }),
    );

    return withWebsites.filter((e): e is Entitlement => !!e);
  }

  async fetchAllCompanyNeighborhoods(
    companyId: string,
    neighborhoodIds: string[],
  ): Promise<string[]> {
    try {
      return await this.apiGatewayClient.getNeighborhoods(
        companyId,
        100,
        neighborhoodIds,
      );
    } catch {
      return [];
    }
  }
}
