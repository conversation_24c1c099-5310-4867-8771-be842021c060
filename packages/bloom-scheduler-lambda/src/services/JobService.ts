/**
 * JobService - Primary Orchestrator
 *
 * Central coordinator for all job-related operations in the Bloom Scheduler.
 * This service orchestrates the complete job lifecycle including:
 * - Creating jobs
 * - Determining job retry steps
 *
 * All job operations must flow through this orchestrator to maintain
 * consistency and proper coordination between sub-services.
 */

import { JobDTOFormatter } from 'src/formatters';
import { JobFetchService } from 'src/services/job/JobFetchService';
import {
  JobDTO,
  JobStep,
  DataForJob,
  JobStatus,
  BaseJobStep,
  RetryStep,
} from 'src/types/job';
import { ContextLogger } from 'src/utils/contextLogger';

export class JobService {
  constructor(
    private readonly logger: ContextLogger,
    private readonly fetchService: JobFetchService,
  ) {}

  private getMostRecentJob(jobs: JobDTO[]): JobDTO {
    return jobs.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    })[0];
  }

  async getJobInfo(
    jobId: string,
  ): Promise<{ retryStep: RetryStep; companyId: string } | undefined> {
    this.logger.info('Getting job info', { jobId });
    const existingJobs = await this.fetchService.getJobs(jobId);

    if (existingJobs.length === 0) {
      throw new Error(`No jobs found for jobId ${jobId}`);
    }

    const mostRecentJob = this.getMostRecentJob(existingJobs);

    if (!mostRecentJob?.companyId) {
      throw new Error(`No companyId found for jobId ${jobId}`);
    }

    const nextRetryStep = this.getNextRetryStep(mostRecentJob.step);

    if (!nextRetryStep) {
      return;
    }

    return {
      retryStep: nextRetryStep,
      companyId: mostRecentJob.companyId,
    };
  }

  private getNextRetryStep(currentStep: JobStep): RetryStep | null {
    const retryMap: { [key: string]: RetryStep } = {
      [BaseJobStep.GENERATE_POST]: RetryStep.GENERATE_POST_RETRY_1,
      [RetryStep.GENERATE_POST_RETRY_1]: RetryStep.GENERATE_POST_RETRY_2,
      [RetryStep.GENERATE_POST_RETRY_2]: RetryStep.GENERATE_POST_RETRY_3,
    };

    const nextStep = retryMap[currentStep] || null;

    if (nextStep === RetryStep.GENERATE_POST_RETRY_1) {
      this.logger.info('Starting retry 1 of 3');
    } else if (nextStep === RetryStep.GENERATE_POST_RETRY_2) {
      this.logger.info('Starting retry 2 of 3');
    } else if (nextStep === RetryStep.GENERATE_POST_RETRY_3) {
      this.logger.info('Starting retry 3 of 3');
    } else {
      this.logger.info('No more retries available');
    }

    return nextStep;
  }

  // Success jobs can be created in bulk because they all have the same status and step
  async createSuccessJobs(jobs: DataForJob | DataForJob[], step: JobStep) {
    this.logger.info('Creating success job', { jobs, step });
    return await this.fetchService.createJobs({
      jobs: JobDTOFormatter.toJobsArray(jobs),
      status: JobStatus.SUCCESS,
      step,
    });
  }

  // Error jobs need to be created individually because each job will have it's own error message
  async createErrorJob(job: DataForJob, step: JobStep, error: Error) {
    this.logger.info('Creating error job', { job, step, error });
    return await this.fetchService.createJobs({
      jobs: JobDTOFormatter.toJobsArray(job),
      status: JobStatus.ERROR,
      step,
      error,
    });
  }
}
