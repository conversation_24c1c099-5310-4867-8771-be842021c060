import { AirOpsClient } from 'src/clients';
import { PostDTOFormatter } from 'src/formatters';
import { PostStateTracker, Post, FailedPost } from 'src/types/post';
export class PostFetchService {
  constructor(
    private readonly airOpsClient: AirOpsClient,
    private readonly environment: string,
  ) {}

  private updatePostStateTracker(
    successfulPosts: Post[],
    failedPosts: FailedPost[],
    postStateTracker: PostStateTracker,
  ): PostStateTracker {
    postStateTracker.totalSuccessfulPosts += successfulPosts.length;
    postStateTracker.totalFailedPosts += failedPosts.length;
    postStateTracker.successfulPosts = successfulPosts;
    postStateTracker.failedPosts = failedPosts;
    return postStateTracker;
  }

  async createPosts(
    posts: Post[],
    postStateTracker: PostStateTracker,
  ): Promise<PostStateTracker> {
    const formattedPosts = PostDTOFormatter.formatForRequest(
      posts,
      this.environment,
    );
    const createdPosts = await this.airOpsClient.createPosts(formattedPosts);

    const { successfulPosts, failedPosts } = createdPosts;

    return this.updatePostStateTracker(
      successfulPosts.map(p => PostDTOFormatter.formatFromRequest(p)),
      failedPosts.map(({ post, error }) => ({
        post: PostDTOFormatter.formatFromRequest(post),
        error,
      })),
      postStateTracker,
    );
  }
}
