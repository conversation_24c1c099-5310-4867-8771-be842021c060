/**
 * EntitlementService - Primary Orchestrator
 *
 * Central coordinator for all entitlement-related operations in the Bloom Scheduler.
 * This service orchestrates the complete entitlement lifecycle including:
 * - Fetching entitlements from external services
 * - Validating entitlement data
 * - Enriching entitlements with additional information
 * - Creating success jobs for entitlements
 *
 * All entitlement operations must flow through this orchestrator to maintain
 * consistency and proper coordination between sub-services.
 */

import pLimit from 'p-limit';
import { Entitlement } from 'src/types/entitlement';
import { InitiateQueryEntitlementEvent } from 'src/types/handler';
import { JobStep } from 'src/types/job';
import { Post } from 'src/types/post';
import { ContextLogger } from 'src/utils/contextLogger';

import { EntitlementEnrichmentService } from './entitlement/EntitlementEnrichmentService';
import { EntitlementFetchService } from './entitlement/EntitlementFetchService';
import { EntitlementValidationService } from './entitlement/EntitlementValidationService';
import { JobService } from './JobService';

export class EntitlementService {
  constructor(
    private readonly chunkSize: number,
    private readonly logger: ContextLogger,
    private readonly fetchService: EntitlementFetchService,
    private readonly validationService: EntitlementValidationService,
    private readonly jobService: JobService,
  ) {}

  private isSkipValidation(event: InitiateQueryEntitlementEvent): boolean {
    return event.skipValidation === true;
  }

  private processSkipValidation(
    companyIds: string[] | undefined,
    entitlements: Entitlement[],
  ): Entitlement[] {
    if (companyIds && companyIds.length > 0) {
      return entitlements.filter(e => companyIds.includes(e.companyId));
    }
    return entitlements;
  }

  async processEntitlementsFromEvent(
    event: InitiateQueryEntitlementEvent,
  ): Promise<Post[]> {
    const limit = pLimit(5);

    // Create a context logger for this operation
    const operationLogger = this.logger.createChild({
      operation: 'processEntitlementsFromEvent',
    });

    // Fetch entitlements
    const fetchedEntitlements: Entitlement[] =
      await this.fetchService.fetchAllEntitlements();

    // Validate entitlements or skip validation
    const entitlements = this.isSkipValidation(event)
      ? (operationLogger.info('Skipping validation for all entitlements'),
        this.processSkipValidation(event.companyIds, fetchedEntitlements))
      : await this.validateStandard(fetchedEntitlements);

    // Filters out entitlements without a LIVE brand website on our platform
    const withWebsites = await this.fetchService.fetchAllWebsites(entitlements);

    // Fetch brand attributes with proper context logging
    const EntitlementBrands = await Promise.all(
      withWebsites.map((e, index) => {
        const wrappedFn = async () => {
          // Create a child logger for each concurrent operation to preserve context
          const itemLogger = operationLogger.createChild({
            companyId: e.companyId,
            itemIndex: index,
            totalItems: withWebsites.length,
            operation: 'fetchBrandAttributes',
          });

          try {
            const result = await this.fetchService.fetchBrandAttributes([
              e.companyId,
            ]);

            itemLogger.info('Fetched brand attributes', {
              companyId: e.companyId,
              result,
            });
            return result;
          } catch (error) {
            itemLogger.error('Error fetching brand attributes', {
              error: error instanceof Error ? error.message : String(error),
              companyId: e.companyId,
            });
            throw error;
          }
        };
        return limit(wrappedFn);
      }),
    ).then(results => results.flat());

    // Filter out entitlements without a brand website on our platform (disableAutomatedBlogs filter on query)
    const withBrands = withWebsites.filter(e => {
      const hasBrand = EntitlementBrands.find(b => b.companyId === e.companyId);
      if (!hasBrand) {
        operationLogger.warn('Entitlement without associated brand', {
          companyId: e.companyId,
          displayName: e.displayName,
          units: e.units,
        });
      }
      return hasBrand;
    });

    // Adds neighborhoods to entitlements, using the brand's preferred locations if set on brand
    const withNeighborhoods = await Promise.all(
      withBrands.map(async e => {
        const brandData = EntitlementBrands.find(
          b => b.companyId === e.companyId,
        );

        const neighborhoodIds = brandData?.blogPreferences?.preferredLocations
          ?.length
          ? brandData.blogPreferences.preferredLocations
          : e.neighborhoods || [];

        operationLogger.info('Using neighborhood IDs for company', {
          companyId: e.companyId,
          neighborhoodIds,
          source: brandData?.blogPreferences?.preferredLocations?.length
            ? 'brand_preferences'
            : 'entitlement_default',
          preferredLocationsCount:
            brandData?.blogPreferences?.preferredLocations?.length || 0,
        });

        const neighborhoods =
          await this.fetchService.fetchAllCompanyNeighborhoods(
            e.companyId,
            neighborhoodIds,
          );
        e.neighborhoods = neighborhoods;
        return e;
      }),
    );

    // Enrich entitlements with job IDs and display names
    const enrichedEntitlements =
      EntitlementEnrichmentService.enrichEntitlements(
        withNeighborhoods,
        EntitlementBrands,
      );

    // Convert to posts
    const posts = enrichedEntitlements.map(e => ({
      businessName: e.displayName,
      jobId: e.jobId,
      companyId: e.companyId,
      websiteUrl: e.websiteUrl || '',
      neighborhoods: e.neighborhoods,
    }));

    return posts;
  }

  async validateStandard(entitlements: Entitlement[]): Promise<Entitlement[]> {
    // Use the available validation methods
    const endDateFiltered =
      this.validationService.validateByEndDate(entitlements);
    const featureFlagFiltered =
      await this.validationService.validateEntitlementsByFeatureFlags(
        endDateFiltered,
      );
    return this.validationService.validateEntitlementsByUnits(
      featureFlagFiltered,
    );
  }

  async createEntitlementSuccessJobs(
    entitlements: Post[],
    step: JobStep,
  ): Promise<void> {
    await this.jobService.createSuccessJobs(entitlements, step);
  }

  chunkEntitlements(
    entitlements: Entitlement[] | Post[],
  ): (Entitlement[] | Post[])[] {
    const chunks: (Entitlement[] | Post[])[] = [];
    for (let i = 0; i < entitlements.length; i += this.chunkSize) {
      chunks.push(entitlements.slice(i, i + this.chunkSize));
    }
    return chunks;
  }
}
