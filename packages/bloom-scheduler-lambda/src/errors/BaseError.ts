/**
 * Abstract base error class that extends the standard Error class.
 * Provides additional metadata support for enhanced error handling.
 */
export abstract class BaseError extends Error {
  /**
   * Creates a new BaseError instance
   * @param message - The error message
   * @param metadata - Optional object containing additional error metadata
   * @example
   * class ValidationError extends BaseError {
   *   constructor(field: string, value: unknown) {
   *     super(`Invalid ${field}`, { field, value, });
   *   }
   * }
   *
   * @example Error handling with metadata
   * try {
   *   throw new ValidationError('email', 'invalid@email');
   * } catch (error) {
   *   if (error instanceof ValidationError) {
   *     console.log(error.message); // "Invalid email"
   *     console.log(error.metadata); // { field: 'email', value: 'invalid@email' }
   *   }
   * }
   */ constructor(
    message: string,
    public readonly metadata?: Record<string, unknown>,
  ) {
    super(message, metadata);
    this.name = this.constructor.name;
  }
}
