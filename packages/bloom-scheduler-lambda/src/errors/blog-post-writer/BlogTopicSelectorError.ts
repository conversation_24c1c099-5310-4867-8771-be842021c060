import { BaseError } from '../BaseError';

export class BlogTopicSelectorError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'BlogTopicSelectorError';
  }
}

export class TransientBlogTopicSelectorError extends BlogTopicSelectorError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientBlogTopicSelectorError';
  }
}

export class NoAvailableTopicsError extends BlogTopicSelectorError {
  constructor(companyId: string) {
    super(`No available blog topics for company ${companyId}`, undefined, {
      companyId,
      skipProcessing: true,
    });
    this.name = 'NoAvailableTopicsError';
  }
}
