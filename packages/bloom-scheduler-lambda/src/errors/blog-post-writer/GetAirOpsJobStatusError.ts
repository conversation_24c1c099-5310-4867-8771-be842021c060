import { BaseError } from '../BaseError';

export class GetAirOpsJobStatusError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'GetAirOpsJobStatusError';
  }
}

export class TransientGetAirOpsJobStatusError extends GetAirOpsJobStatusError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientGetAirOpsJobStatusError';
  }
}

export class AirOpsJobFailedError extends GetAirOpsJobStatusError {
  constructor(executionId: string, status: string, error?: unknown) {
    super(
      `AirOps job ${executionId} failed with status: ${status}`,
      undefined,
      {
        executionId,
        status,
        error,
      },
    );
    this.name = 'AirOpsJobFailedError';
  }
}
