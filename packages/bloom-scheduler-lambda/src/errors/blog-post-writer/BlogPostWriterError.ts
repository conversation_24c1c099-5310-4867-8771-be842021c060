import { BaseError } from '../BaseError';

export class BlogPostWriterError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'BlogPostWriterError';
  }
}

export class TransientBlogPostWriterError extends BlogPostWriterError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientBlogPostWriterError';
  }
}

export class AirOpsExecutionError extends BlogPostWriterError {
  constructor(message: string, statusCode?: number, response?: unknown) {
    super(message, undefined, {
      statusCode,
      response,
    });
    this.name = 'AirOpsExecutionError';
  }
}
