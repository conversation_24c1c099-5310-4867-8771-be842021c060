import { BaseError } from '../BaseError';

export class BlogPostImageGeneratorError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'BlogPostImageGeneratorError';
  }
}

export class TransientBlogPostImageGeneratorError extends BlogPostImageGeneratorError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientBlogPostImageGeneratorError';
  }
}
