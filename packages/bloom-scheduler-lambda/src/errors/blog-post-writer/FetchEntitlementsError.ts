import { BaseError } from '../BaseError';

export class FetchEntitlementsError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'FetchEntitlementsError';
  }
}

export class TransientFetchEntitlementsError extends FetchEntitlementsError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientFetchEntitlementsError';
  }
}
