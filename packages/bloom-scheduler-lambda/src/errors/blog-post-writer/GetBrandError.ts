import { BaseError } from '../BaseError';

export class GetBrandError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'GetBrandError';
  }
}

export class TransientGetBrandError extends GetBrandError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientGetBrandError';
  }
}

export class MissingBrandProfileError extends GetBrandError {
  constructor(companyId: string) {
    super(`Brand profile not found for company ${companyId}`, undefined, {
      companyId,
      skipProcessing: true,
    });
    this.name = 'MissingBrandProfileError';
  }
}
