import { BaseError } from '../BaseError';

export class BlogPostPublisherError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'BlogPostPublisherError';
  }
}

export class TransientBlogPostPublisherError extends BlogPostPublisherError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientBlogPostPublisherError';
  }
}
