import { BaseError } from '../BaseError';

export class ResultsWriterError extends BaseError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...metadata,
      cause: cause?.message,
      stack: cause?.stack,
    });
    this.name = 'ResultsWriterError';
  }
}

export class TransientResultsWriterError extends ResultsWriterError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'TransientResultsWriterError';
  }
}
