import {
  LDClient,
  LDContext,
  LDFlagValue,
  init,
} from 'launchdarkly-node-server-sdk';
import pLimit from 'p-limit';
import { getConfig } from 'src/config';

export class LaunchDarkly {
  private static readonly config = getConfig();
  private static readonly KEY = this.config.LAUNCHDARKLY_KEY || '';
  private static readonly API_GATEWAY_SUPER_USER_COMPANY_ID =
    this.config.API_GATEWAY_SUPER_USER_COMPANY_ID || '';
  private static client: LDClient;

  // Batch processing configuration
  private static readonly BATCH_SIZE = 50;
  private static readonly CONCURRENT_LIMIT = 20;

  public static generateLDContext(companyId: string): LDContext {
    return {
      kind: 'company',
      name: 'company',
      key: companyId,
      companyId,
    };
  }

  /**
   * Check Variation and return the value
   * Does multi-key for company and user
   * @param flagName Name related to the FF created via Launch Darkly
   * @param defaultValue This will be automatically set to false when empty
   * @param companyId Company ID
   * @param forceLaunchDarkly If true, always use LaunchDarkly even in dev/local (for testing production behavior)
   */
  public static async checkVariation(
    flagName: string,
    defaultValue = false,
    companyId: string = this.API_GATEWAY_SUPER_USER_COMPANY_ID,
    forceLaunchDarkly = false,
  ): Promise<LDFlagValue> {
    // Development/local environments: bypass LaunchDarkly for easier testing (unless forced)
    if (
      ['development', 'local'].includes(LaunchDarkly.config.ENVIRONMENT) &&
      !forceLaunchDarkly
    ) {
      return true;
    }

    // Always use LaunchDarkly flag evaluation for staging/production or when forced
    const context = LaunchDarkly.generateLDContext(companyId);
    const client = await LaunchDarkly.getClient();
    return client.variation(flagName, context, defaultValue);
  }

  /**
   * Check variation for multiple companies concurrently with rate limiting
   * @param flagName Name of the LaunchDarkly flag
   * @param defaultValue Default value if flag evaluation fails
   * @param companyIds Array of company IDs to check
   * @param forceLaunchDarkly If true, always use LaunchDarkly even in dev/local
   * @returns Map of companyId to flag value
   */
  public static async checkVariationBatch(
    flagName: string,
    defaultValue = false,
    companyIds: string[],
    forceLaunchDarkly = false,
  ): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    if (companyIds.length === 0) {
      return results;
    }

    console.log(
      `LaunchDarkly batch check: ${companyIds.length} companies for flag "${flagName}"`,
    );

    // Create rate limiter for concurrent requests
    const limit = pLimit(LaunchDarkly.CONCURRENT_LIMIT);

    // Process companies in batches to avoid memory issues with large datasets
    const batches: string[][] = [];
    for (let i = 0; i < companyIds.length; i += LaunchDarkly.BATCH_SIZE) {
      batches.push(companyIds.slice(i, i + LaunchDarkly.BATCH_SIZE));
    }

    try {
      // Process all batches concurrently
      const batchPromises = batches.map((batch, batchIndex) =>
        limit(async () => {
          console.log(
            `Processing LaunchDarkly batch ${batchIndex + 1}/${batches.length} (${batch.length} companies)`,
          );

          // Check each company in the batch concurrently
          const batchResults = await Promise.allSettled(
            batch.map(async companyId => {
              const value = await LaunchDarkly.checkVariation(
                flagName,
                defaultValue,
                companyId,
                forceLaunchDarkly,
              );
              return { companyId, value: Boolean(value) };
            }),
          );

          // Process batch results
          const batchMap = new Map<string, boolean>();
          batchResults.forEach((settledResult, index) => {
            const companyId = batch[index];
            if (settledResult.status === 'fulfilled') {
              const { value } = settledResult.value;
              batchMap.set(companyId, value);
            } else {
              console.error(
                `LaunchDarkly check failed for company ${companyId}:`,
                settledResult.reason,
              );
              batchMap.set(companyId, defaultValue);
            }
          });

          return batchMap;
        }),
      );

      // Wait for all batches to complete
      const batchMapResults = await Promise.allSettled(batchPromises);

      // Merge all batch results
      batchMapResults.forEach((settledResult, batchIndex) => {
        if (settledResult.status === 'fulfilled') {
          settledResult.value.forEach((value, companyId) => {
            results.set(companyId, value);
          });
        } else {
          console.error(
            `LaunchDarkly batch ${batchIndex + 1} failed:`,
            settledResult.reason,
          );
          // Set default values for failed batch
          batches[batchIndex].forEach(companyId => {
            results.set(companyId, defaultValue);
          });
        }
      });

      const enabledCount = Array.from(results.values()).filter(Boolean).length;
      console.log(
        `LaunchDarkly batch complete: ${enabledCount}/${companyIds.length} companies enabled for "${flagName}"`,
      );

      return results;
    } catch (error) {
      console.error('LaunchDarkly batch check failed:', error);

      // Return default values for all companies on failure
      companyIds.forEach(companyId => {
        results.set(companyId, defaultValue);
      });

      return results;
    }
  }

  /**
   * LaunchDarkly Initialize Client
   * @return {Promise<LaunchDarkly.LDClient>} Client
   */
  public static getClient(): Promise<LDClient> {
    if (!this.client) this.client = init(this.KEY);

    return this.client.waitForInitialization();
  }

  public static closeConnection(): void {
    if (this.client) return this.client.close();
  }
}
