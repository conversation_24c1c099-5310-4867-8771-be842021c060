import { Day } from 'src/types/date';

import { DateUtils } from '../date';

describe('DateUtils', () => {
  describe('toUTCMidnight', () => {
    it('converts date to UTC midnight', () => {
      const date = new Date('2024-01-01T15:30:45Z');
      const result = DateUtils.toUTCMidnight(date);
      expect(result.toISOString()).toBe('2024-01-01T00:00:00.000Z');
    });
  });

  describe('getWeekdaysInMonth', () => {
    it('returns all specified weekdays in a month', () => {
      const date = new Date('2024-01-03T00:00:00Z');
      const weekdays = DateUtils.getWeekdaysInMonth(date, Day.THURSDAY);

      expect(weekdays).toHaveLength(4);
      expect(weekdays[0].toISOString()).toBe('2024-01-04T00:00:00.000Z');
      expect(weekdays[1].toISOString()).toBe('2024-01-11T00:00:00.000Z');
      expect(weekdays[2].toISOString()).toBe('2024-01-18T00:00:00.000Z');
      expect(weekdays[3].toISOString()).toBe('2024-01-25T00:00:00.000Z');
      weekdays.forEach(day => {
        expect(day.getUTCDay()).toBe(Day.THURSDAY);
      });
    });

    it('limits to maximum 4 weekdays per month even if there are 5 weekdays in the month', () => {
      const date = new Date('2024-07-05T00:00:00Z');
      const weekdays = DateUtils.getWeekdaysInMonth(date, Day.MONDAY);
      expect(weekdays).toHaveLength(4);
    });

    it('returns correct weekdays for February', () => {
      const date = new Date('2024-02-06T00:00:00Z');
      const weekdays = DateUtils.getWeekdaysInMonth(date, Day.MONDAY);

      expect(weekdays).toHaveLength(4);
      weekdays.forEach(day => {
        expect(day.getUTCDay()).toBe(Day.MONDAY);
      });
      expect(weekdays[0].toISOString()).toBe('2024-02-05T00:00:00.000Z');
      expect(weekdays[1].toISOString()).toBe('2024-02-12T00:00:00.000Z');
      expect(weekdays[2].toISOString()).toBe('2024-02-19T00:00:00.000Z');
      expect(weekdays[3].toISOString()).toBe('2024-02-26T00:00:00.000Z');
    });

    it('returns correct weekdays when today date is in the same week as the previous months last day', () => {
      const date = new Date('2024-04-01T00:00:00Z');
      const weekdays = DateUtils.getWeekdaysInMonth(date, Day.TUESDAY);

      expect(weekdays).toHaveLength(4);
      weekdays.forEach(day => {
        expect(day.getUTCDay()).toBe(Day.TUESDAY);
      });
      expect(weekdays[0].toISOString()).toBe('2024-04-02T00:00:00.000Z');
      expect(weekdays[1].toISOString()).toBe('2024-04-09T00:00:00.000Z');
      expect(weekdays[2].toISOString()).toBe('2024-04-16T00:00:00.000Z');
      expect(weekdays[3].toISOString()).toBe('2024-04-23T00:00:00.000Z');
    });

    it('returns correct weekdays when today date is in the same week as the next months first day', () => {
      const date = new Date('2024-10-31T00:00:00Z');
      const weekdays = DateUtils.getWeekdaysInMonth(date, Day.FRIDAY);

      expect(weekdays).toHaveLength(4);
      weekdays.forEach(day => {
        expect(day.getUTCDay()).toBe(Day.FRIDAY);
      });
      expect(weekdays[0].toISOString()).toBe('2024-10-04T00:00:00.000Z');
      expect(weekdays[1].toISOString()).toBe('2024-10-11T00:00:00.000Z');
      expect(weekdays[2].toISOString()).toBe('2024-10-18T00:00:00.000Z');
      expect(weekdays[3].toISOString()).toBe('2024-10-25T00:00:00.000Z');
    });
  });
  describe('isSameUTCDay', () => {
    it('returns true for same UTC day', () => {
      const date1 = new Date('2024-01-01T00:00:00Z');
      const date2 = new Date('2024-01-01T23:59:59Z');
      expect(DateUtils.isSameUTCDay(date1, date2)).toBe(true);
    });

    it('returns false for different UTC days', () => {
      const date1 = new Date('2024-01-01T00:00:00Z');
      const date2 = new Date('2024-01-02T00:00:00Z');
      expect(DateUtils.isSameUTCDay(date1, date2)).toBe(false);
    });
  });
});
