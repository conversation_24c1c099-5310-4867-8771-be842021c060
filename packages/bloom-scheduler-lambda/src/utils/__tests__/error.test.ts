import { BaseError } from 'src/errors/BaseError';
import { ContextLogger } from 'src/utils/contextLogger';
import { createHandlerError } from 'src/utils/error';

describe('createHandlerError', () => {
  const mockContext = {
    awsRequestId: 'test-request-id',
    functionName: 'test-function',
    callbackWaitsForEmptyEventLoop: false,
    functionVersion: '1.0',
    invokedFunctionArn: 'test-arn',
    memoryLimitInMB: '128',
    logGroupName: '/aws/lambda/test-function',
    logStreamName: '2023/05/14/[$LATEST]abcdef1234567890',
    getRemainingTimeInMillis: () => 1000,
    done: () => {},
    fail: () => {},
    succeed: () => {},
  };

  const mockEvent = {
    skipValidation: true,
  };

  const mockLogger = {
    error: jest.fn(),
    info: jest.fn(),
  } as unknown as ContextLogger;

  class TestError extends BaseError {
    constructor(
      message: string,
      public readonly metadata?: Record<string, unknown>,
      public readonly cause?: Record<string, unknown>,
      public readonly stack?: string,
    ) {
      super(message, metadata);
    }
  }

  it('returns original error if it extends BaseError', () => {
    const error = new TestError('Test Error', {
      companyIds: ['1', '2', '3'],
    });

    const result = createHandlerError({
      error,
      context: mockContext,
      event: mockEvent,
      logger: mockLogger as unknown as ContextLogger,
    });

    expect(result).toBeInstanceOf(Error);
    expect(result.message).toBe(
      'TestError occurred in test-function: Test Error',
    );
  });

  it('wraps non-BaseError in HandlerError', () => {
    const error = new Error('standard error');

    const result = createHandlerError({
      error,
      context: mockContext,
      event: mockEvent,
      logger: mockLogger as unknown as ContextLogger,
    });

    expect(result.name).toBe('Error');
  });

  it('logs enriched error details', () => {
    const error = new TestError('standard error', {
      companyIds: ['1', '2', '3'],
    });

    createHandlerError({
      error,
      context: mockContext,
      event: mockEvent,
      logger: mockLogger,
    });

    expect(mockLogger.error).toHaveBeenCalledWith(
      'TestError occurred in test-function: standard error',
      {
        context: mockContext,
        name: 'TestError',
        message: 'TestError occurred in test-function: standard error',
        stack: error.stack,
        cause: error.cause,
        timestamp: expect.any(String),
        metadata: {
          companyIds: ['1', '2', '3'],
        },
        originalEvent: mockEvent,
      },
    );
  });
});
