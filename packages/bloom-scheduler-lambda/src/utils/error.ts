import { Context } from 'aws-lambda';
import { BaseError } from 'src/errors';
import { HandlerEvent } from 'src/types/handler';

import { ContextLogger } from './contextLogger';

// Useful for logging errors with detailed context in lambda handlers
export function createHandlerError<T extends HandlerEvent>(params: {
  error: BaseError | Error | unknown;
  context: Context;
  logger: ContextLogger;
  metadata?: Record<string, unknown>;
  event?: T;
}): Error {
  const { error, context, metadata = {}, event, logger } = params;

  // Extract metadata from BaseError if available
  const baseErrorMetadata = error instanceof BaseError ? error.metadata : {};

  // Convert unknown errors to Error objects
  const normalizedError =
    error instanceof Error || error instanceof BaseError
      ? error
      : new Error(String(error));

  // Enrich error with context
  const enrichedError = {
    context,
    name: normalizedError.name,
    message: `${normalizedError.name} occurred in ${context.functionName}: ${normalizedError.message}`,
    stack: normalizedError.stack,
    cause: normalizedError.cause,
    timestamp: new Date().toISOString(),
    metadata: {
      ...baseErrorMetadata,
      ...metadata,
    },
    ...(event && { originalEvent: event }),
  };

  // enrichedError.message serves as the logger message
  // passing in another message just get's overwritten by
  // enrichedError.message
  logger.error(enrichedError.message, enrichedError);

  const handlerError = new Error(
    `${normalizedError.name} occurred in ${context.functionName}: ${normalizedError.message}`,
  );
  handlerError.cause = normalizedError.cause;
  handlerError.stack = normalizedError.stack;
  return handlerError;
}
