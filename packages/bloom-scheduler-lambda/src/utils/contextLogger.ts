import { Logger } from '@aws-lambda-powertools/logger';
import { Context } from 'aws-lambda';

export interface LoggerContext {
  [key: string]: unknown;
}

export interface ComponentContext {
  lambdaName?: string;
  serviceName?: string;
  clientName?: string;
  componentName?: string;
  layer?: string;
}

export class ContextLogger {
  private logger: Logger;
  private context: LoggerContext;

  constructor(logger: Logger, initialContext?: LoggerContext) {
    this.logger = logger;
    this.context = initialContext || {};
  }

  /**
   * Creates a child logger with additional context
   * Use this for business context or operational data
   */
  createChild(additionalContext?: Partial<LoggerContext>): ContextLogger {
    return new ContextLogger(this.logger, {
      ...this.context,
      ...additionalContext,
    });
  }

  /**
   * Creates a child logger with ARCHITECTURAL COMPONENT identification
   *
   * Use this method to identify WHERE in your system architecture the log originated:
   * - lambdaName: Which Lambda function (e.g., 'publisher', 'processor')
   * - serviceName: Which service class (e.g., 'TenantService', 'ScheduledActionService')
   * - clientName: Which client/API wrapper (e.g., 'SeoApiGatewayClient', 'S3Client')
   * - componentName: Other architectural components (e.g., 'middleware', 'validator')
   * - layer: Architectural layer (e.g., 'api', 'business', 'data')
   *
   * This enables filtering logs in CloudWatch by architectural components:
   * - "Show me all logs from the TenantService"
   * - "Show me all logs from the publisher Lambda"
   * - "Track a request through lambda → service → client"
   */
  createComponentLogger(
    component: Partial<ComponentContext>,
    additionalContext?: Partial<LoggerContext>,
  ): ContextLogger {
    const componentContext: LoggerContext = {};

    // Only add defined component fields to avoid cluttering logs
    if (component.lambdaName)
      componentContext.lambdaName = component.lambdaName;
    if (component.serviceName)
      componentContext.serviceName = component.serviceName;
    if (component.clientName)
      componentContext.clientName = component.clientName;
    if (component.componentName)
      componentContext.componentName = component.componentName;
    if (component.layer) componentContext.layer = component.layer;

    return new ContextLogger(this.logger, {
      ...this.context,
      ...componentContext,
      ...additionalContext,
    });
  }

  /**
   * Gets the underlying AWS Lambda Powertools logger
   * Use this for advanced scenarios or when you need direct access
   */
  getBaseLogger(): Logger {
    return this.logger;
  }

  // Proxy methods to the underlying logger with context
  info(message: string, meta?: Record<string, unknown>): void {
    this.logger.info(message, { ...this.context, ...meta });
  }

  error(message: string, meta?: Record<string, unknown>): void {
    this.logger.error(message, { ...this.context, ...meta });
  }

  warn(message: string, meta?: Record<string, unknown>): void {
    this.logger.warn(message, { ...this.context, ...meta });
  }

  debug(message: string, meta?: Record<string, unknown>): void {
    this.logger.debug(message, { ...this.context, ...meta });
  }
}

/**
 * Creates a context-aware logger
 * @param functionName The name of the Lambda function
 * @param context Optional Lambda context
 * @param initialLogContext Optional initial logger context
 */
export function createContextLogger(
  functionName: string,
  context?: Context,
  initialLogContext?: LoggerContext,
): ContextLogger {
  const baseLogger = createLogger(functionName, context);
  return new ContextLogger(baseLogger, initialLogContext);
}

/**
 * Creates a base logger instance with Datadog correlation attributes
 */
function createLogger(functionName: string, context?: Context): Logger {
  const config = getConfig();
  const logLevel = process.env.LOG_LEVEL || 'INFO';
  const environment = config.ENVIRONMENT || 'development';
  const version = process.env.GIT_SHA || 'latest';

  return new Logger({
    logLevel: logLevel as 'INFO' | 'DEBUG' | 'WARN' | 'ERROR',
    serviceName: 'bloom-scheduler',
    persistentLogAttributes: {
      environment,
      awsRequestId: context?.awsRequestId,
      functionName: context?.functionName,
      lambdaName: functionName,
      // Datadog correlation attributes for log-to-trace correlation
      'dd.env': environment,
      'dd.service': 'bloom-scheduler',
      'dd.version': version,
      'dd.custom.function_name': functionName,
      'dd.custom.platform': 'aws_lambda',
      'dd.custom.team': 'client-marketing',
      'dd.custom.project': 'seo-automation',
    },
  });
}

// Import getConfig to avoid circular dependency
function getConfig() {
  // This will be imported at runtime
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  return require('../config').getConfig();
}
