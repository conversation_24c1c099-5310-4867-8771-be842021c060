import { Day } from 'src/types/date';

export class DateUtils {
  static toUTCMidnight(date: Date): Date {
    return new Date(
      Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()),
    );
  }

  /**
   * Determines the month from the date
   * Determines what weekday to get all days for
   * Ex: If the date is 2023-01-01 and the weekday is Day.MONDAY,
   * it will return an array of dates for every first Monday
   * in January 2023
   */
  static getWeekdaysInMonth(date: Date, weekday: Day): Date[] {
    // get current year and month from date
    const year = date.getUTCFullYear();
    const month = date.getUTCMonth();
    const firstDayOfMonth = new Date(Date.UTC(year, month, 1));

    // Calculate first occurrence of specified weekday
    const firstWeekday = new Date(firstDayOfMonth);
    const daysUntilFirst = (weekday + 7 - firstWeekday.getUTCDay()) % 7;
    firstWeekday.setUTCDate(firstWeekday.getUTCDate() + daysUntilFirst);

    // Get all occurrences in month
    const dates: Date[] = [];
    const currentDate = new Date(firstWeekday);
    while (currentDate.getUTCMonth() === month) {
      dates.push(new Date(currentDate));
      currentDate.setUTCDate(currentDate.getUTCDate() + 7);
    }

    // Return max 4 weeks (we skip blog generation on the 5th week)
    return dates.slice(0, 4);
  }

  static isSameUTCDay(date1: Date, date2: Date): boolean {
    return (
      date1.getUTCFullYear() === date2.getUTCFullYear() &&
      date1.getUTCMonth() === date2.getUTCMonth() &&
      date1.getUTCDate() === date2.getUTCDate()
    );
  }
}
