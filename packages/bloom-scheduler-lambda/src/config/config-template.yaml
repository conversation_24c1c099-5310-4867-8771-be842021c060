base:
  ENVIRONMENT: ${<PERSON>NVIRONMENT}
  REGION: us-east-1
  API_GATEWAY_SUPER_USER_COMPANY_ID: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_SUPER_USER_COMPANY_ID
  API_GATEWAY_KEY: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_KEY
  LAUNCHDARKLY_KEY: vault:secret/data/${VAULT_ENV}/standard#LAUNCHDARKLY_KEY
  AIR_OPS_API_URL: https://app.airops.com/public_api/airops_apps
  AIR_OPS_API_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#AIR_OPS_API_KEY
  TENANT_SERVICE_URL: https://tenant-service.${BASE_DOMAIN}
  CMS_SERVICE_URL: https://cms-service.${BASE_DOMAIN}
  API_GATEWAY_URL: https://gw.${BASE_DOMAIN}
  COSMO_GQL_URL: https://graphql.${BASE_DOMAIN}
  M2M_SUPER_API_KEY: vault:secret/data/${VAULT_ENV}/standard#DOMAIN_M2M_KEY
  GENERATE_POST_DAY: THURSDAY
  POST_PUBLISH_DAY_OF_WEEK: THURSDAY
  AUTOMATED_BLOGS_PRODUCT_ID: 5c9b746c-7327-42ce-b998-ce707e7cee44
  BLOG_V2_AIR_OPS_WRITER_APP_ID: 4bab0083-de37-40c0-9b3c-2c2ada6ca615
  BLOG_V2_SLACK_NOTIFICATIONS_ENABLED: true
  BLOG_V2_SLACK_WEBHOOK_URL: *******************************************************************************
  MOCK_DATA_MODE: false
  SUPER_BLOOM_LD_FLAG: enable-super-bloom
  EXECUTION_BUCKET_NAME: lp-bloom-scheduler-execution-${ENVIRONMENT}
  GIT_SHA: latest
  ENTITLEMENTS_CHUNK_SIZE: 5
  LANGFUSE_SECRET_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#LANGFUSE_SECRET_KEY
  LANGFUSE_PUBLIC_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#LANGFUSE_PUBLIC_KEY
  LANGFUSE_ENVIRONMENT: staging
  LANGFUSE_FLUSH_AT: 1
  LANGFUSE_BASE_URL: https://us.cloud.langfuse.com
  OPENAI_MODEL_NAME: gpt-4o
  OPENAI_TEMPERATURE: 0
  SECURITY_GROUP_IDS: ${SECURITY_GROUP_IDS}
  SUBNET_IDS: ${SUBNET_IDS}
  DEPLOY_BUCKET: ${DEPLOY_BUCKET}
  OPENAI_API_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#OPENAI_API_KEY
  GEMINI_API_KEY: vault:secret/data/${VAULT_ENV}/bloom-scheduler-lambda#GEMINI_API_KEY
  SLACK_NOTIFICATIONS_ENABLED: true
  SLACK_WEBHOOK_URL: *******************************************************************************
development:
  LOG_LEVEL: INFO
  POWERTOOLS_DEV: true
  ENTITLEMENTS_LIMIT: 3
  TESTING_TODAY_DATE: 2024-10-01
  ENABLE_ERROR_TESTING: true
  SCHEDULER_STATE: DISABLED
  AIR_OPS_WORKFLOW_ID: 3f4ae1e0-0edb-4ba5-945a-9eabee2aab3a
staging:
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
  LOG_LEVEL: INFO
  POWERTOOLS_DEV: true
  ENTITLEMENTS_LIMIT: 3
  TESTING_TODAY_DATE: 2024-10-01
  ENABLE_ERROR_TESTING: true
  SCHEDULER_STATE: DISABLED
  AIR_OPS_WORKFLOW_ID: 3f4ae1e0-0edb-4ba5-945a-9eabee2aab3a
production:
  LANGFUSE_ENVIRONMENT: production
  LANGFUSE_FLUSH_AT: 1
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
  LOG_LEVEL: ERROR
  POWERTOOLS_DEV: false
  ENTITLEMENTS_LIMIT: 100
  ENABLE_ERROR_TESTING: false
  SCHEDULER_STATE: ENABLED
  # workflow with human review step
  AIR_OPS_WORKFLOW_ID: 4e685cd9-927c-42be-9da2-8e8b3e8e51d0
  ENTITLEMENTS_CHUNK_SIZE: 5
