/**
 * Datadog configuration and utilities for Bloom Scheduler Lambda
 */
import { sendDistributionMetric } from 'datadog-lambda-js';

export interface DatadogConfig {
  service: string;
  environment: string;
  version: string;
  defaultTags: string[];
}

export const getDatadogConfig = (): DatadogConfig => {
  const environment = process.env.ENVIRONMENT || 'development';

  return {
    service: 'bloom-scheduler',
    environment,
    version: process.env.GIT_SHA || 'latest',
    defaultTags: [
      `environment:${environment}`,
      'team:client-marketing',
      'platform_version:v2',
      'project:seo-automation',
      'service:bloom-scheduler',
    ],
  };
};

// Metrics namespace constants for v2 state machine
export const METRICS = {
  // State machine execution metrics
  STATE_MACHINE_EXECUTION_TIME: 'bloom_scheduler.state_machine.execution_time',
  STATE_MACHINE_SUCCESS: 'bloom_scheduler.state_machine.success',
  STATE_MACHINE_FAILURE: 'bloom_scheduler.state_machine.failure',

  // Individual lambda metrics
  FETCH_ENTITLEMENTS_TIME: 'bloom_scheduler.fetch_entitlements.execution_time',
  FETCH_ENTITLEMENTS_COMPANIES_COUNT:
    'bloom_scheduler.fetch_entitlements.companies_count',
  FETCH_ENTITLEMENTS_ELIGIBLE_COUNT:
    'bloom_scheduler.fetch_entitlements.eligible_count',

  GET_BRAND_TIME: 'bloom_scheduler.get_brand.execution_time',

  BLOG_TOPIC_SELECTOR_TIME:
    'bloom_scheduler.blog_topic_selector.execution_time',

  BLOG_POST_WRITER_TIME: 'bloom_scheduler.blog_post_writer.execution_time',
  BLOG_POST_WRITER_AIROPS_REQUESTS:
    'bloom_scheduler.blog_post_writer.airops_requests',

  GET_AIROPS_JOB_STATUS_TIME:
    'bloom_scheduler.get_airops_job_status.execution_time',
  GET_AIROPS_JOB_STATUS_POLLING_COUNT:
    'bloom_scheduler.get_airops_job_status.polling_count',

  BLOG_POST_PUBLISHER_TIME:
    'bloom_scheduler.blog_post_publisher.execution_time',
  BLOG_POST_PUBLISHER_POSTS_COUNT:
    'bloom_scheduler.blog_post_publisher.posts_published',

  RESULTS_WRITER_TIME: 'bloom_scheduler.results_writer.execution_time',
  RESULTS_WRITER_RESULTS_COUNT:
    'bloom_scheduler.results_writer.results_processed',

  // Business intelligence metrics
  COMPANIES_PROCESSED: 'bloom_scheduler.companies.processed',
  COMPANIES_SKIPPED: 'bloom_scheduler.companies.skipped',
  COMPANIES_FAILED: 'bloom_scheduler.companies.failed',
  POSTS_GENERATED: 'bloom_scheduler.posts.generated',
  POSTS_FAILED: 'bloom_scheduler.posts.failed',
  AIROPS_API_ERRORS: 'bloom_scheduler.airops.api_errors',

  // Error tracking
  LAMBDA_ERRORS: 'bloom_scheduler.lambda.errors',
  API_ERRORS: 'bloom_scheduler.api.errors',
} as const;

// Centralized metrics class with error handling
export class DatadogMetrics {
  private config: DatadogConfig;

  constructor() {
    this.config = getDatadogConfig();
  }

  /**
   * Send timing metric in milliseconds
   */
  async sendTimingMetric(
    metricName: string,
    value: number,
    lambdaName: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      ...additionalTags,
      'unit:millisecond',
    ];

    try {
      await sendDistributionMetric(metricName, value, ...tags);
    } catch (error) {
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send timing metric:', error);
      }
    }
  }

  /**
   * Send count metric
   */
  async sendCountMetric(
    metricName: string,
    value: number,
    lambdaName: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      ...additionalTags,
      'unit:count',
    ];

    try {
      await sendDistributionMetric(metricName, value, ...tags);
    } catch (error) {
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send count metric:', error);
      }
    }
  }

  /**
   * Send increment metric (value = 1)
   */
  async sendIncrementMetric(
    metricName: string,
    lambdaName: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    return this.sendCountMetric(metricName, 1, lambdaName, additionalTags);
  }

  /**
   * Send error metric with error type classification
   */
  async sendErrorMetric(
    metricName: string,
    lambdaName: string,
    errorType: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [...additionalTags, `error_type:${errorType}`];

    return this.sendIncrementMetric(metricName, lambdaName, tags);
  }

  /**
   * Send business metric for companies processed
   */
  async sendCompaniesProcessedMetric(
    count: number,
    lambdaName: string,
    status: 'processed' | 'skipped' | 'failed',
    reason?: string,
  ): Promise<void> {
    const tags = [`status:${status}`];
    if (reason) {
      tags.push(`reason:${reason}`);
    }

    const metricName =
      status === 'processed'
        ? METRICS.COMPANIES_PROCESSED
        : status === 'skipped'
          ? METRICS.COMPANIES_SKIPPED
          : METRICS.COMPANIES_FAILED;

    return this.sendCountMetric(metricName, count, lambdaName, tags);
  }

  /**
   * Send posts generation metric
   */
  async sendPostsMetric(
    count: number,
    lambdaName: string,
    status: 'generated' | 'failed',
    additionalTags: string[] = [],
  ): Promise<void> {
    const metricName =
      status === 'generated' ? METRICS.POSTS_GENERATED : METRICS.POSTS_FAILED;

    return this.sendCountMetric(metricName, count, lambdaName, [
      `status:${status}`,
      ...additionalTags,
    ]);
  }
}

// Export singleton instance
export const datadogMetrics = new DatadogMetrics();
