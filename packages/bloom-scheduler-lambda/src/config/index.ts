import { Day } from 'src/types/date';

export interface EnvironmentConfig {
  AUTOMATED_BLOGS_PRODUCT_ID: string;
  TENANT_SERVICE_URL: string;
  CMS_SERVICE_URL: string;
  API_GATEWAY_URL: string;
  AIR_OPS_WORKFLOW_ID: string;
  AIR_OPS_API_URL: string;
  AIR_OPS_API_KEY: string;
  GENERATE_POST_DAY: string;
  API_GATEWAY_SUPER_USER_COMPANY_ID: string;
  API_GATEWAY_KEY: string;
  ENTITLEMENTS_CHUNK_SIZE: number;
  POWERTOOLS_DEV: boolean;
  LOG_LEVEL: string;
  REGION: string;
  TESTING_TODAY_DATE: string | undefined;
  ENTITLEMENTS_LIMIT: number | undefined;
  ENVIRONMENT: string;
  LAUNCHDARKLY_KEY: string;
  GENERATE_POST_FUNCTION_NAME: string;
  AWS_LAMBDA_LOG_STREAM_NAME: string;
  AWS_LAMBDA_FUNCTION_NAME: string;
}

let cachedConfig: EnvironmentConfig | null = null;

export function getConfig(): EnvironmentConfig {
  if (!cachedConfig) {
    cachedConfig = getEnvironmentConfig();
  }
  return cachedConfig;
}

function getEnvironmentConfig(): EnvironmentConfig {
  const requiredEnvVars = {
    automatedBlogsProductId: process.env.AUTOMATED_BLOGS_PRODUCT_ID,
    tenantServiceUrl: process.env.TENANT_SERVICE_URL,
    cmsServiceUrl: process.env.CMS_SERVICE_URL,
    apiGatewayUrl: process.env.API_GATEWAY_URL,
    apiGatewayKey: process.env.API_GATEWAY_KEY,
    airOpsWorkflowId: process.env.AIR_OPS_WORKFLOW_ID,
    airOpsApiUrl: process.env.AIR_OPS_API_URL,
    airOpsApiKey: process.env.AIR_OPS_API_KEY,
    generatePostDay: process.env.GENERATE_POST_DAY,
  };

  // Validate required env vars
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    if (!value) {
      throw new Error(`Missing required environment variable: ${key}`);
    }
  });

  // Validate GENERATE_POST_DAY maps to Day enum
  if (
    requiredEnvVars.generatePostDay &&
    !(requiredEnvVars.generatePostDay in Day)
  ) {
    throw new Error(
      `Invalid GENERATE_POST_DAY value: ${
        requiredEnvVars.generatePostDay
      }. Must be one of: ${Object.keys(Day).join(', ')}`,
    );
  }

  return {
    // Required env vars
    AUTOMATED_BLOGS_PRODUCT_ID: requiredEnvVars.automatedBlogsProductId!,
    TENANT_SERVICE_URL: requiredEnvVars.tenantServiceUrl!,
    CMS_SERVICE_URL: requiredEnvVars.cmsServiceUrl!,
    API_GATEWAY_URL: requiredEnvVars.apiGatewayUrl!,
    API_GATEWAY_KEY: requiredEnvVars.apiGatewayKey!,
    AIR_OPS_WORKFLOW_ID: requiredEnvVars.airOpsWorkflowId!,
    AIR_OPS_API_URL: requiredEnvVars.airOpsApiUrl!,
    AIR_OPS_API_KEY: requiredEnvVars.airOpsApiKey!,
    GENERATE_POST_DAY: requiredEnvVars.generatePostDay as keyof typeof Day,
    // Non-required env vars
    API_GATEWAY_SUPER_USER_COMPANY_ID:
      process.env.API_GATEWAY_SUPER_USER_COMPANY_ID || '',
    ENTITLEMENTS_CHUNK_SIZE: Number(process.env.ENTITLEMENTS_CHUNK_SIZE) || 10,
    POWERTOOLS_DEV: process.env.POWERTOOLS_DEV === 'true',
    LOG_LEVEL: process.env.LOG_LEVEL || 'ERROR',
    REGION: process.env.REGION || 'us-east-1',
    TESTING_TODAY_DATE: process.env.TESTING_TODAY_DATE || undefined,
    ENTITLEMENTS_LIMIT: process.env.ENTITLEMENTS_LIMIT
      ? Number(process.env.ENTITLEMENTS_LIMIT)
      : undefined,
    ENVIRONMENT: process.env.ENVIRONMENT || 'development',
    LAUNCHDARKLY_KEY: process.env.LAUNCHDARKLY_KEY || '',
    GENERATE_POST_FUNCTION_NAME: process.env.GENERATE_POST_FUNCTION_NAME || '',
    AWS_LAMBDA_LOG_STREAM_NAME: process.env.AWS_LAMBDA_LOG_STREAM_NAME || '',
    AWS_LAMBDA_FUNCTION_NAME: process.env.AWS_LAMBDA_FUNCTION_NAME || '',
  };
}
