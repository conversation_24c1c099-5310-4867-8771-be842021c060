import { APIGatewayProxyEvent } from 'aws-lambda';

import { Post } from './post';

export interface InitiateQueryEntitlementEvent {
  skipValidation?: boolean;
  companyIds?: string[];
}

export type GeneratePostEvent = Array<Post>;

export interface GeneratePostPayload {
  entitlementChunks: GeneratePostEvent[];
}

export interface InitiateQueryEntitlementResponse {
  bucketName: string;
  key: string;
}

export interface GeneratePostResponse {
  totalPosts: number;
  totalSuccessfulPosts: number;
  successfulPostJobIds: string[];
  totalFailedPosts: number;
  failedPostJobIds: string[];
}

export type HandlerEvent =
  | InitiateQueryEntitlementEvent
  | GeneratePostEvent
  | APIGatewayProxyEvent;
