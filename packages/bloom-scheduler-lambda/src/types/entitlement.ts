export interface Entitlement {
  productId: string;
  companyId: string;
  productName: string;
  endDate: string | null;
  units: number;
  websiteUrl: string | undefined;
  neighborhoods?: string[];
  displayName: string;
}

// Entitlement object returned from the tenant-service
export interface EntitlementDTO {
  displayId: string;
  createdAt: string;
  updatedAt: string;
  productId: string;
  companyId: string;
  startDate: string;
  endDate: string;
  units: number;
  salesforceServiceId: string;
  product: {
    id: string;
    name: string;
  };
  company?: {
    website?: string;
    name?: string;
  };
}

export interface EnrichedEntitlement extends Entitlement {
  jobId: string;
}
