export enum BaseJobStep {
  INITIATE_QUERY_ENTITLEMENTS = 'INITIATE_QUERY_ENTITLEMENTS',
  GENERATE_POST = 'GENERATE_POST',
  AIR_OPS_WEBHOOK_ERROR = 'AIR_OPS_WEBHOOK_ERROR',
}

// Possible values for any generate post retries from AirOps error webhook
export enum RetryStep {
  GENERATE_POST = BaseJobStep.GENERATE_POST,
  GENERATE_POST_RETRY_1 = 'GENERATE_POST_RETRY_1',
  GENERATE_POST_RETRY_2 = 'GENERATE_POST_RETRY_2',
  GENERATE_POST_RETRY_3 = 'GENERATE_POST_RETRY_3',
}

export type JobStep = BaseJobStep | RetryStep;

export enum JobStatus {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}

export enum JobEventType {
  SUCCESS = 'job.success',
  ERROR = 'job.error',
}

export interface JobPostDTO {
  jobId: string;
  companyId: string;
  step: JobStep;
  status: JobStatus;
  errorMessage?: Record<string, unknown>;
}
export interface JobDTO extends JobPostDTO {
  createdAt: string;
}

export interface DataForJob {
  jobId: string;
  companyId: string;
}

export interface CreateJobParams {
  jobs: Array<DataForJob>;
  step: JobStep;
  status: JobStatus;
  error?: Error;
}
