// Type definitions for blog-post-writer state machine

export interface BrandProfile {
  companyId: string;
  aboutTheBrand?: string;
  strategicFocus?: string;
  valueProposition?: string;
  idealCustomerProfiles?: string;
  missionAndCoreValues?: string;
  brandPointOfView?: string;
  toneOfVoice?: string;
  ctaText?: string;
  authorPersona?: string;
}

export interface GooglePlaceData {
  place_id?: string;
  name?: string;
  formatted_address?: string;
  geometry?: {
    location?: {
      lat?: number;
      lng?: number;
    };
  };
  types?: string[];
  [key: string]: unknown; // Allow additional Google Places fields
}

export interface Coordinates {
  lat?: number;
  lng?: number;
  [key: string]: unknown; // Allow additional coordinate fields
}

export interface Neighborhood {
  id: string;
  name: string;
  preferredName: string;
  googlePlacesName: string;
  priority?: number;
  // New fields from dashboard GraphQL
  googlePlaceData?: GooglePlaceData;
  isPreferred?: boolean; // New field indicating preference status
  isPrimary?: boolean;
  slug?: string;
  description?: string;
  coordinates?: Coordinates;
}

export interface BlogTopic {
  id: string;
  // New required fields from GraphQL
  companyId?: string;
  topic?: string;
  // GraphQL optional fields
  blogTitle?: string;
  rationale?: string;
  type: 'ARTICLE' | 'LISTICLE' | 'article' | 'listicle';
  neighborhoodId?: string;
  parentTopic?: string;
  createdAt?: string;
  updatedAt?: string;
  cmsPostId?: string;
  blogPostJobId?: string;
  airopsExecutionId?: string;
  neighborhood?: Neighborhood;
  // Legacy fields for backward compatibility
  title?: string;
  keywords?: string[];
  priority?: number;
  lastUsed?: string;
}

export interface BlogContent {
  title: string;
  content: string;
  metaDescription?: string;
  keywords?: string[];
  imagePrompt?: string;
}

export interface AirOpsResponse {
  airops_app_execution: {
    id: number;
    // TODO: improve and use enum
    status: string;
  };
}

export interface AirOpsStatusResponse {
  status?: string;
  state?: string;
  output?: AirOpsOutput;
  error?: unknown;
}

export interface AirOpsOutput {
  blogPost?: BlogContent;
  result?: BlogContent;
  data?: BlogContent;
  title?: string;
  headline?: string;
  content?: string;
  body?: string;
  text?: string;
  metaDescription?: string;
  meta_description?: string;
  description?: string;
  keywords?: string[];
  tags?: string[];
  imagePrompt?: string;
  image_prompt?: string;
  featuredImagePrompt?: string;
}

export interface CompanyProcessingResult {
  companyId: string;
  topicId?: string;
  published?: boolean;
  createdAt?: string;
  s3Location?: string;
  cmsPostId?: string;
  skipped?: boolean;
  failed?: boolean;
  reason?: string;
  error?: unknown;
}

export interface ExecutionReport {
  executionName?: string;
  executionStartTime: string;
  executionArn?: string;
  dryRun: boolean;
  publishToCMS?: boolean;
  s3FolderPath?: string; // Added for S3 folder links in Slack notifications
  summary: {
    totalCompanies: number;
    successfulPosts: number;
    failedPosts: number;
    skippedCompanies: number;
    cmsPublishedPosts: number; // Number of posts actually published to CMS
    duration?: string;
  };
  companies: CompanyProcessingResult[];
  errors: Array<{
    companyId: string;
    error: unknown;
  }>;
  completedAt: string;
}

export interface SlackMessage {
  text: string;
  blocks: Array<{
    type: string;
    text?: {
      type: string;
      text: string;
    };
  }>;
}
