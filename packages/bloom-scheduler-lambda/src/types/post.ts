import { RetryStep } from './job';

export interface Post {
  businessName: string;
  websiteUrl: string;
  companyId: string;
  jobId: string;
  neighborhoods?: string[];
  retryStep?: RetryStep;
}
export interface FailedPost {
  post: Post;
  error: Error;
}

export interface PostDTO {
  inputs: {
    job_id: string;
    business_name: string;
    agent_s_website_url: string;
    company_id: string;
    environment: string;
    neighborhoods?: string;
  };
}

export interface FailedPostDTO {
  post: PostDTO;
  error: Error;
}

export interface PostResult {
  successfulPosts: PostDTO[];
  failedPosts: FailedPostDTO[];
}

export interface PostStateTracker {
  totalPosts: number;
  successfulPosts: Post[];
  totalSuccessfulPosts: number;
  failedPosts: FailedPost[];
  totalFailedPosts: number;
}
