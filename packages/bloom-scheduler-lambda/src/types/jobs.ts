export const BaseJobSteps = {
  INITIATE_QUERY_ENTITLEMENTS: 'INITIATE_QUERY_ENTITLEMENTS',
  GENERATE_POST: 'GENERATE_POST',
  AIR_OPS_WEBHOOK_ERROR: 'AIR_OPS_WEBHOOK_ERROR',
} as const;

export const RetryJobSteps = {
  GENERATE_POST_RETRY_1: 'GENERATE_POST_RETRY_1',
  GENERATE_POST_RETRY_2: 'GENERATE_POST_RETRY_2',
  GENERATE_POST_RETRY_3: 'GENERATE_POST_RETRY_3',
} as const;

export const JobStep = {
  ...BaseJobSteps,
  ...RetryJobSteps,
} as const;

export type BaseJobStep = (typeof BaseJobSteps)[keyof typeof BaseJobSteps];
export type RetryJobStep = (typeof RetryJobSteps)[keyof typeof RetryJobSteps];
export type JobStep = BaseJobStep | RetryJobStep;

export enum JobStatus {
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}

export interface JobDTO {
  jobId: string;
  companyId: string;
  step: JobStep;
  status: JobStatus;
  errorMessage?: Record<string, unknown>;
}
