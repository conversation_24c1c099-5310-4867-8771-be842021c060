# Blog Post Writer State Machine

## Sequence Diagram

```mermaid
sequenceDiagram
    participant StateMachine
    participant FetchEntitlements
    participant GetBrand
    participant BlogTopicSelector
    participant BlogPostWriter
    participant AirOps
    participant GetAirOpsJobStatus
    participant BlogPostImageGenerator
    participant BlogPostPublisher
    participant CMS
    
    StateMachine->>FetchEntitlements: input { }
    FetchEntitlements->>GetBrand: entitlements map
    GetBrand->>BlogTopicSelector: brand profile + neighborhoods
    BlogTopicSelector->>BlogPostWriter: blog topic
    BlogPostWriter->>AirOps: async_execute
    BlogPostWriter->>GetAirOpsJobStatus: airops_execution_id
    GetAirOpsJobStatus->>AirOps: poll for status
    GetAirOpsJobStatus->>BlogPostImageGenerator: blog content
    BlogPostImageGenerator->>BlogPostImageGenerator: generate image
    BlogPostImageGenerator->>BlogPostPublisher: blog content + media id
    BlogPostPublisher->>CMS: publish blog
    BlogPostPublisher-->>StateMachine: execution complete
```