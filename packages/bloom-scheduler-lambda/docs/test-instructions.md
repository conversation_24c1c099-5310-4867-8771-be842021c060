# LaunchDarkly Batch Processing Test Instructions

## Overview
The bloom-scheduler-lambda blogPostWriterStateMachineV2 and associated Lambdas including fetch-entitlements includes optimized LaunchDarkly batch processing that can handle thousands of companies efficiently.

## Key Features Implemented
- **Concurrent processing:** 50 companies per batch, 20 concurrent requests max
- **Rate limiting:** Uses p-limit to prevent overwhelming LaunchDarkly API
- **Error resilience:** Promise.allSettled ensures failed requests don't break entire batches
- **Environment targeting:** <PERSON><PERSON><PERSON> handles staging/production LaunchDarkly evaluation

## Testing Options

### Option 1: Unit Tests (Recommended for Development)
```bash
# Run the comprehensive test suite
pnpm test src/lambdas/blog-post-writer/__tests__/fetch-entitlements.spec.ts

# This tests:
# - Batch LaunchDarkly processing
# - Concurrent request handling
# - Error handling and resilience
# - Environment-specific behavior
```

### Option 2: AWS Lambda Test (Staging/Production)
```bash
# First deploy the lambda
AWS_PROFILE=your-profile pnpm deploy

# Test with a small set of companies using AWS CLI
aws lambda invoke \
  --function-name bloom-scheduler-fetch-entitlements \
  --payload '{
    "companyIds": ["company-id-1", "company-id-2"],
    "dryRun": true,
    "executionName": "test-batch-processing",
    "executionStartTime": "2024-01-15T12:00:00Z"
  }' \
  --cli-binary-format raw-in-base64-out \
  response.json

# Check the response
cat response.json
```

### Option 3: AWS Step Functions Test (Full Workflow)
```bash
# Start the complete blog-post-writer workflow
aws stepfunctions start-execution \
  --state-machine-arn "arn:aws:states:region:account:stateMachine:blog-post-writer" \
  --input '{
    "companyIds": [],
    "dryRun": true,
    "executionName": "test-full-workflow"
  }'
```

## Environment Variables Required

For testing in staging/production environments:

```bash
# Required for LaunchDarkly
LAUNCHDARKLY_KEY=your-launchdarkly-sdk-key
ENVIRONMENT=staging  # or "production"

# Required for API Gateway integration  
API_GATEWAY_URL=https://your-api-gateway-url
API_GATEWAY_KEY=your-api-key
API_GATEWAY_SUPER_USER_COMPANY_ID=your-super-user-company-id

# Required for S3 storage
EXECUTION_BUCKET_NAME=your-s3-bucket-name

# Required for entitlements
AUTOMATED_BLOGS_PRODUCT_ID=your-product-id
```

## Expected Behavior

### In Development (Local)
- LaunchDarkly calls are bypassed (returns `true` for all companies)
- Demonstrates batch processing structure and logging
- Uses mock data for testing

### In Staging/Production  
- LaunchDarkly batch processing actively filters companies
- Only companies enabled in LaunchDarkly flags will be processed
- Console logs show:
  ```
  LaunchDarkly batch check: 1000 companies for flag "enable-super-bloom"
  Processing LaunchDarkly batch 1/20 (50 companies)
  Processing LaunchDarkly batch 2/20 (50 companies)
  ...
  LaunchDarkly batch complete: 15/1000 companies enabled for "enable-super-bloom"
  LaunchDarkly filtering: 15/1000 companies enabled
  ```

## Performance Expectations

### Before Optimization
- **1000 companies:** ~200 seconds (sequential LaunchDarkly calls)
- **High API load:** 1000+ requests to tenant-service

### After Optimization  
- **1000 companies:** ~10 seconds (concurrent batch processing)
- **Reduced API load:** Only LaunchDarkly-enabled companies checked with tenant-service
- **50-80% fewer** tenant-service API calls

## Monitoring in AWS

Check CloudWatch logs for these key indicators:

1. **Batch Processing Logs:**
   ```
   LaunchDarkly batch check: X companies for flag "enable-super-bloom"
   Processing LaunchDarkly batch N/M (X companies)
   LaunchDarkly batch complete: X/Y companies enabled
   ```

2. **Performance Metrics:**
   - Lambda duration should be significantly reduced
   - Memory usage should remain stable
   - Error rates should be minimal

3. **Business Metrics:**
   - Number of eligible companies found
   - LaunchDarkly filtering effectiveness
   - S3 storage of results for downstream processing

## Troubleshooting

### No Companies Enabled
- Verify LaunchDarkly flag configuration in your environment
- Check that company IDs are correctly targeted in LaunchDarkly
- Ensure LAUNCHDARKLY_KEY is properly set

### Performance Issues
- Monitor CloudWatch for timeout errors
- Adjust BATCH_SIZE (currently 50) and CONCURRENT_LIMIT (currently 20) if needed
- Check LaunchDarkly rate limiting on your account

### Module Import Errors (Local Testing)
The compiled JavaScript uses CommonJS but some dependencies are ES modules. This is expected for Lambda deployment but can cause issues in local testing. Use the unit tests instead of direct script execution for local development.