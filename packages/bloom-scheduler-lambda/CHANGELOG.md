# bloom-scheduler

## 2.21.0

### Minor Changes

- [#895](https://github.com/luxurypresence/seo-automation/pull/895) [`996b6cf`](https://github.com/luxurypresence/seo-automation/commit/996b6cffdf0488d04509070d7b866bb9da6923c4) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add environment separation for LangfuseSpanProcessor to enable trace filtering by deployment environment

## 2.20.2

### Patch Changes

- [#894](https://github.com/luxurypresence/seo-automation/pull/894) [`471acc4`](https://github.com/luxurypresence/seo-automation/commit/471acc4ef370b7668b5b41b2f54c04e6b8925d1c) Thanks [@jaycholland](https://github.com/jaycholland)! - createPost mutation media

## 2.20.1

### Patch Changes

- [#892](https://github.com/luxurypresence/seo-automation/pull/892) [`818db25`](https://github.com/luxurypresence/seo-automation/commit/818db25d5d06e98251f5ce1d243bdd4b78493d97) Thanks [@jaycholland](https://github.com/jaycholland)! - vault location for bloom-scheduler-lambda permission

## 2.20.0

### Minor Changes

- [#890](https://github.com/luxurypresence/seo-automation/pull/890) [`a5a4114`](https://github.com/luxurypresence/seo-automation/commit/a5a41145629d3ef1b7d65ac149045616755a649b) Thanks [@jaycholland](https://github.com/jaycholland)! - Fixed image generator auth and missing env vars

## 2.19.0

### Minor Changes

- [#887](https://github.com/luxurypresence/seo-automation/pull/887) [`b45fba3`](https://github.com/luxurypresence/seo-automation/commit/b45fba3e96019c3eb97151ccb80099f9c0bd3c61) Thanks [@jaycholland](https://github.com/jaycholland)! - use updateBlogTopic cosmo graphql through client-marketing-service

## 2.18.2

### Patch Changes

- [#884](https://github.com/luxurypresence/seo-automation/pull/884) [`8040f6a`](https://github.com/luxurypresence/seo-automation/commit/8040f6a673b8ac2a4d7f407e78a5406a40248806) Thanks [@jaycholland](https://github.com/jaycholland)! - trimmed neighborhood object in airops payload

## 2.18.1

### Patch Changes

- [#882](https://github.com/luxurypresence/seo-automation/pull/882) [`e54a115`](https://github.com/luxurypresence/seo-automation/commit/e54a1152ef8a9ca60990e4d8860d8cd23d9cae58) Thanks [@jaycholland](https://github.com/jaycholland)! - graphql append again to the request urls

## 2.18.0

### Minor Changes

- [#879](https://github.com/luxurypresence/seo-automation/pull/879) [`e074612`](https://github.com/luxurypresence/seo-automation/commit/e07461205a7915b0c0f4db720d3e51998533035d) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix staging environment URL resolution issue by transforming API_GATEWAY_URL to remove 'staging.' subdomain and add enhanced logging for getNeighborhoodById debugging

## 2.17.4

### Patch Changes

- [#876](https://github.com/luxurypresence/seo-automation/pull/876) [`ba46dc1`](https://github.com/luxurypresence/seo-automation/commit/ba46dc15e69c2336f084c2700dc8599edb1cb04a) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix getNeighborhoodById GraphQL query pagination parameters

## 2.17.3

### Patch Changes

- [#874](https://github.com/luxurypresence/seo-automation/pull/874) [`e0f4db8`](https://github.com/luxurypresence/seo-automation/commit/e0f4db8aa4a6b694d1f81e346031907c11f912c6) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix neighborhood fetching to include Google Places data validation

## 2.17.2

### Patch Changes

- [#869](https://github.com/luxurypresence/seo-automation/pull/869) [`68bf5ff`](https://github.com/luxurypresence/seo-automation/commit/68bf5ff4b64a90c666ebd30bc168873fac4a52bf) Thanks [@jaycholland](https://github.com/jaycholland)! - use api gateway for non-cosmo neighborhoods query

## 2.17.1

### Patch Changes

- [#868](https://github.com/luxurypresence/seo-automation/pull/868) [`c4d9a7f`](https://github.com/luxurypresence/seo-automation/commit/c4d9a7f533fc3f3f2592851199ce8ad17d36402f) Thanks [@jaycholland](https://github.com/jaycholland)! - query name neighborhood vs neighborhoods

## 2.17.0

### Minor Changes

- [#867](https://github.com/luxurypresence/seo-automation/pull/867) [`db03079`](https://github.com/luxurypresence/seo-automation/commit/db03079d9219a214c263fcbf5150663a95bd8bef) Thanks [@jaycholland](https://github.com/jaycholland)! - neighborhood for airops request object format and additional logging

## 2.16.3

### Patch Changes

- [#862](https://github.com/luxurypresence/seo-automation/pull/862) [`52dfb69`](https://github.com/luxurypresence/seo-automation/commit/52dfb697851d66ebc3dcf143443f68c622013d48) Thanks [@jaycholland](https://github.com/jaycholland)! - added real airops app id to config env var

## 2.16.2

### Patch Changes

- [#858](https://github.com/luxurypresence/seo-automation/pull/858) [`93b2063`](https://github.com/luxurypresence/seo-automation/commit/93b206396b9c0eac799201607205dd246cc2a34e) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed cosmo nova requests graphql path

## 2.16.1

### Patch Changes

- [#857](https://github.com/luxurypresence/seo-automation/pull/857) [`ddf8a31`](https://github.com/luxurypresence/seo-automation/commit/ddf8a317aa85aa35dc2fc39ffd52d908cd1c5c9e) Thanks [@jaycholland](https://github.com/jaycholland)! - added missing cosmo env vars

## 2.16.0

### Minor Changes

- [#855](https://github.com/luxurypresence/seo-automation/pull/855) [`8513643`](https://github.com/luxurypresence/seo-automation/commit/85136436d8f307e3dcc2364e2eefa0ec23f1c5b6) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - SEO-953: Migrate blog post writer lambdas to use federated GraphQL (NOVA/COSMO) with API Gateway fallback

## 2.15.1

### Patch Changes

- [#854](https://github.com/luxurypresence/seo-automation/pull/854) [`b2df762`](https://github.com/luxurypresence/seo-automation/commit/b2df76249a2bb882c4b00e1486b2ddd2367a5154) Thanks [@jaycholland](https://github.com/jaycholland)! - graphql route fixed

## 2.15.0

### Minor Changes

- [#852](https://github.com/luxurypresence/seo-automation/pull/852) [`d818aa2`](https://github.com/luxurypresence/seo-automation/commit/d818aa27dc966dc575188099e938154007ebbeaa) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed env var and slack message issues

## 2.14.0

### Minor Changes

- [#849](https://github.com/luxurypresence/seo-automation/pull/849) [`67a7b10`](https://github.com/luxurypresence/seo-automation/commit/67a7b10c4533307df63c3211aa60ed94253eb7ce) Thanks [@jaycholland](https://github.com/jaycholland)! - implemented blog topic selector

## 2.13.0

### Minor Changes

- [#842](https://github.com/luxurypresence/seo-automation/pull/842) [`1ae8262`](https://github.com/luxurypresence/seo-automation/commit/1ae8262460fb14390c017046a4df2afa04d9cfd8) Thanks [@jaycholland](https://github.com/jaycholland)! - get brand with proper brand profile structure and neighborhoods

## 2.12.0

### Minor Changes

- [#846](https://github.com/luxurypresence/seo-automation/pull/846) [`7dc6a7f`](https://github.com/luxurypresence/seo-automation/commit/7dc6a7f8eb4de0cbe6a4dab63d99429f2f127250) Thanks [@fbionsc](https://github.com/fbionsc)! - Update blog post data retrieved from AirOps

## 2.11.0

### Minor Changes

- [#840](https://github.com/luxurypresence/seo-automation/pull/840) [`5466dc6`](https://github.com/luxurypresence/seo-automation/commit/5466dc643bf3484fcf2a435247e8c0c7ce74ce88) Thanks [@fbionsc](https://github.com/fbionsc)! - Add AirOps call

## 2.10.0

### Minor Changes

- [#841](https://github.com/luxurypresence/seo-automation/pull/841) [`79f4640`](https://github.com/luxurypresence/seo-automation/commit/79f4640b538c1b138ecd3ad55089fff07823f037) Thanks [@jaycholland](https://github.com/jaycholland)! - Implemented post publisher

## 2.9.3

### Patch Changes

- [#838](https://github.com/luxurypresence/seo-automation/pull/838) [`1452ae0`](https://github.com/luxurypresence/seo-automation/commit/1452ae0f22fe9f4885066d37f731a185507093a7) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix image size

## 2.9.2

### Patch Changes

- [#837](https://github.com/luxurypresence/seo-automation/pull/837) [`eeb75b5`](https://github.com/luxurypresence/seo-automation/commit/eeb75b5a348fe14eb6c3a80d0e6276b241020cdd) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed missing cmsPostId by populating it with blog topicId

## 2.9.1

### Patch Changes

- [#836](https://github.com/luxurypresence/seo-automation/pull/836) [`4c309fa`](https://github.com/luxurypresence/seo-automation/commit/4c309fac52e5fd4678454b7f9ffd69ad4a42edc9) Thanks [@jaycholland](https://github.com/jaycholland)! - nested array index reference for map

## 2.9.0

### Minor Changes

- [#835](https://github.com/luxurypresence/seo-automation/pull/835) [`60d84c4`](https://github.com/luxurypresence/seo-automation/commit/60d84c4fa74d001ea56db76c2d667817ecc052e4) Thanks [@jaycholland](https://github.com/jaycholland)! - map reader s3 file structure

## 2.8.0

### Minor Changes

- [#833](https://github.com/luxurypresence/seo-automation/pull/833) [`3c95ceb`](https://github.com/luxurypresence/seo-automation/commit/3c95ceb28a47ad4daa842340faa141ad8edb0764) Thanks [@jaycholland](https://github.com/jaycholland)! - fetch entitlements from tenant service instead of gql

## 2.7.2

### Patch Changes

- [#832](https://github.com/luxurypresence/seo-automation/pull/832) [`8681cf5`](https://github.com/luxurypresence/seo-automation/commit/8681cf57caf1f7b0216d751300484bb62deb01df) Thanks [@jaycholland](https://github.com/jaycholland)! - datadog addLayers true now that permissions were added

## 2.7.1

### Patch Changes

- [#831](https://github.com/luxurypresence/seo-automation/pull/831) [`f4c257a`](https://github.com/luxurypresence/seo-automation/commit/f4c257a8824232b5d4dab5577f6f594e0be48a70) Thanks [@jaycholland](https://github.com/jaycholland)! - datadog layers

## 2.7.0

### Minor Changes

- [#830](https://github.com/luxurypresence/seo-automation/pull/830) [`d4aa531`](https://github.com/luxurypresence/seo-automation/commit/d4aa53196a9cf06c14c0db67b991ce322f5dafbf) Thanks [@jaycholland](https://github.com/jaycholland)! - upgrade to Node 22

## 2.6.2

### Patch Changes

- [#828](https://github.com/luxurypresence/seo-automation/pull/828) [`a410510`](https://github.com/luxurypresence/seo-automation/commit/a410510b4c41054c29171ebcdf32394ef76bff13) Thanks [@jaycholland](https://github.com/jaycholland)! - build fix 2

## 2.6.1

### Patch Changes

- [#827](https://github.com/luxurypresence/seo-automation/pull/827) [`4138b95`](https://github.com/luxurypresence/seo-automation/commit/4138b95d8e35e66b3432cf5bef554840511e95d7) Thanks [@jaycholland](https://github.com/jaycholland)! - added missing env vars and fallbacks to empty strings

## 2.6.0

### Minor Changes

- [#826](https://github.com/luxurypresence/seo-automation/pull/826) [`37c0099`](https://github.com/luxurypresence/seo-automation/commit/37c0099857a6db56c87f6740e9ceb4d42388c93d) Thanks [@jaycholland](https://github.com/jaycholland)! - debugged and fixed the state machine with some features added

## 2.5.0

### Minor Changes

- [#815](https://github.com/luxurypresence/seo-automation/pull/815) [`0e35355`](https://github.com/luxurypresence/seo-automation/commit/0e353553ff6937724c47c77d806c5d39c9a8071a) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - feat: add LangFuse tracing to blog post image generator

## 2.4.0

### Minor Changes

- [#802](https://github.com/luxurypresence/seo-automation/pull/802) [`49f11b4`](https://github.com/luxurypresence/seo-automation/commit/49f11b47a1fed8d081a7348fd39b32675bfbe90b) Thanks [@fbionsc](https://github.com/fbionsc)! - Send prompt to gpt 5 before imagen

## 2.3.0

### Minor Changes

- [#793](https://github.com/luxurypresence/seo-automation/pull/793) [`dca4eb4`](https://github.com/luxurypresence/seo-automation/commit/dca4eb4aebca3e85c9e26517c918d4cddd72c38d) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix bug where brand preferred locations were ignored during neighborhood fetching. EntitlementService now uses blogPreferences.preferredLocations when available, with fallback to existing behavior.

## 2.2.0

### Minor Changes

- [#791](https://github.com/luxurypresence/seo-automation/pull/791) [`53c9725`](https://github.com/luxurypresence/seo-automation/commit/53c9725ba1e7f59d3b3cc648ff155dc53d51ebdb) Thanks [@jaycholland](https://github.com/jaycholland)! - Added LaunchDarkly check for enable-super-bloom

## 2.1.0

### Minor Changes

- [#789](https://github.com/luxurypresence/seo-automation/pull/789) [`4d14ca8`](https://github.com/luxurypresence/seo-automation/commit/4d14ca8c02099c9f0b8c4f34bac764ac20c41f81) Thanks [@fbionsc](https://github.com/fbionsc)! - Add image generation

## 2.0.2

### Patch Changes

- [#771](https://github.com/luxurypresence/seo-automation/pull/771) [`11067af`](https://github.com/luxurypresence/seo-automation/commit/11067af9bdc87a929b5d36e3db71cdea2809cd78) Thanks [@jaycholland](https://github.com/jaycholland)! - fix if no companies to process in map

## 2.0.1

### Patch Changes

- [#770](https://github.com/luxurypresence/seo-automation/pull/770) [`adf50fb`](https://github.com/luxurypresence/seo-automation/commit/adf50fba3ec9ae7df781f80d28c7ac77e4271cc0) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed handling null input

## 2.0.0

### Major Changes

- [#758](https://github.com/luxurypresence/seo-automation/pull/758) [`a60d127`](https://github.com/luxurypresence/seo-automation/commit/a60d127e15ecdf67d1001201d5dc9dd587073287) Thanks [@jaycholland](https://github.com/jaycholland)! - Bloom v2 (SuperBloom) initial state machine configuration

## 1.15.1

### Patch Changes

- [#737](https://github.com/luxurypresence/seo-automation/pull/737) [`dd67b0f`](https://github.com/luxurypresence/seo-automation/commit/dd67b0f7cebb35d1ef3c474033f094217f4b89c6) Thanks [@jaycholland](https://github.com/jaycholland)! - Testing reducing esbuild concurrency, package individually

## 1.15.0

### Minor Changes

- [#655](https://github.com/luxurypresence/seo-automation/pull/655) [`4a174f9`](https://github.com/luxurypresence/seo-automation/commit/4a174f9f676b12d2be246f38a0d99c0d661449b4) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-815: Adds ContextLogger for fixing CW logs

  ### Added

  - **ContextLogger Pattern**: Implemented custom logging wrapper to preserve context across concurrent operations
  - **Context Preservation**: Added `createChild()` and `createComponentLogger()` methods for business and architectural context
  - **Concurrent Operation Logging**: Fixed CloudWatch log loss when using `pLimit` for parallel processing

  ### Changed

  - **Service Logger Types**: Simplified from `Logger | ContextLogger` union to explicit `ContextLogger` type
  - **Handler Logging**: Updated Lambda handlers to use `createContextLogger()` with architectural context
  - **Service Factories**: Standardized logger parameter types across all service creation functions

  ### Fixed

  - **Logging Context Loss**: Resolved issue where concurrent operations lost logging context in CloudWatch
  - **Type Safety**: Improved TypeScript type definitions and removed redundant instanceof checks
  - **Test Coverage**: Updated all service tests to use ContextLogger mocks

  ### Technical Details

  - Replaced `LoggerFactory.createLogger()` with `createContextLogger()` in handlers
  - Added context propagation through `pLimit` concurrent operations
  - Maintained back

## 1.14.4

### Patch Changes

- [#653](https://github.com/luxurypresence/seo-automation/pull/653) [`986505c`](https://github.com/luxurypresence/seo-automation/commit/986505cf2a48ac7f7d33d27defafd9834bb4777e) Thanks [@jaycholland](https://github.com/jaycholland)! - reverted hotfix schedule back to thursday

## 1.14.3

### Patch Changes

- [`00edcc8`](https://github.com/luxurypresence/seo-automation/commit/00edcc853e05642444f2a787b5ac4431b17bfa41) Thanks [@jaycholland](https://github.com/jaycholland)! - hotfix bloom scheduler run on monday for validation rerun from last week

## 1.14.2

### Patch Changes

- [#646](https://github.com/luxurypresence/seo-automation/pull/646) [`3b242b7`](https://github.com/luxurypresence/seo-automation/commit/3b242b770d4c35772a54565b91904c802bbdfd6b) Thanks [@jaycholland](https://github.com/jaycholland)! - updated bloom scheduler to run on thursday

## 1.14.1

### Patch Changes

- [#613](https://github.com/luxurypresence/seo-automation/pull/613) [`48a5eeb`](https://github.com/luxurypresence/seo-automation/commit/48a5eebd8683b092a1ff7375cf59dde73ce45cf3) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates owner of DD services to client-marketing

## 1.14.0

### Minor Changes

- [#598](https://github.com/luxurypresence/seo-automation/pull/598) [`14ea974`](https://github.com/luxurypresence/seo-automation/commit/14ea974d3ee3ee9200956545aedd2ba2a0b5db6f) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-664: Sets Bloom scheduler to run on Wednesdays

## 1.13.0

### Minor Changes

- [#578](https://github.com/luxurypresence/seo-automation/pull/578) [`7711185`](https://github.com/luxurypresence/seo-automation/commit/7711185e77c0778a421431b4c30104d19c314bd7) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-569: Sets Bloom to run on Fridays

## 1.12.2

### Patch Changes

- [#541](https://github.com/luxurypresence/seo-automation/pull/541) [`a969b36`](https://github.com/luxurypresence/seo-automation/commit/a969b3675f816a84d82258cbaeae32f812b23176) Thanks [@diegosr90](https://github.com/diegosr90)! - Makes Bloom run at 12 utc

## 1.12.1

### Patch Changes

- [#332](https://github.com/luxurypresence/seo-automation/pull/332) [`9664532`](https://github.com/luxurypresence/seo-automation/commit/9664532d8e701dae502ec276c52aec63e6983df9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes deprecated packages

## 1.12.0

### Minor Changes

- [#319](https://github.com/luxurypresence/seo-automation/pull/319) [`a750957`](https://github.com/luxurypresence/seo-automation/commit/a750957436bb23d3b1ce0747c8963adc9e576a72) Thanks [@diegosr90](https://github.com/diegosr90)! - Reduce concurrency values to prevent rate-limit violations

## 1.11.2

### Patch Changes

- 84fe83f: Make sure this package can build after CircleCI changes

## 1.11.1

### Patch Changes

- 810e117: Fixes bloom-scheduler-lambda tests

## 1.11.0

### Minor Changes

- d6b9d00: SEO-312: Tune step-machine to avoid rate limit on ideogram.
  - Fix logging within pLimit calls

## 1.10.1

### Patch Changes

- 4858c67: Fixes brand validation (query will return only brands with disableAutomatedBlogs: false) so no brand = disabled blogs, and fixes endDate validation'

## 1.10.0

### Minor Changes

- f65802d: Adds pLimit for throttling requests and increase memory size for step 1 lambda

## 1.9.3

### Patch Changes

- 4c62946: Fixes s3 client defintion

## 1.9.2

### Patch Changes

- 5f837e0: Use env var for lambda

## 1.9.1

### Patch Changes

- bb312ed: Fix credential lookup from s3 client

## 1.9.0

### Minor Changes

- 7d316cf: Fix s3 client connection limit issues.

## 1.8.0

### Minor Changes

- 6092923: SEO-211: Adds support for blogPreferences. Sets machine maxConcurrency to 5.

## 1.7.13

### Patch Changes

- e63e97e: Move start execution perms in order to allow IAM console trigger

## 1.7.12

### Patch Changes

- 4e2eb8b: Removes log config due to permissions issues

## 1.7.11

### Patch Changes

- be7fee0: Adding perms for logs

## 1.7.10

### Patch Changes

- c6d48d6: Fixes value for type on machine definition.

## 1.7.9

### Patch Changes

- e1596b4: Parses serverless file using AI

## 1.7.8

### Patch Changes

- c3f460e: Changes getAtt form.

## 1.7.7

### Patch Changes

- de86a26: Adds explicit role for step func execution

## 1.7.6

### Patch Changes

- 4b6444d: Adds iamRoleStatements for state machine.

## 1.7.5

### Patch Changes

- e8c6702: Adds perm policies for child state execution

## 1.7.4

### Patch Changes

- 06ef365: Adds permissions for execution.

## 1.7.3

### Patch Changes

- 350f927: Adds permissions policies for state machine

## 1.7.2

### Patch Changes

- d64c859: Adds putObject s3 permission on serverless config.

## 1.7.1

### Patch Changes

- d4ed755: Fix for schema violation regarding Step machine

## 1.7.0

### Minor Changes

- 462bddb: SEO-140: Scales step 1 output with S3. (File containes the same output format)

## 1.6.9

### Patch Changes

- 367a60b: Trying to fix circleci

## 1.6.8

### Patch Changes

- 17c839f: Removes CrewAI package
- 2e2a04f: Fixes circleci config to remove old python package steps

## 1.6.7

### Patch Changes

- 9b15537: Adds yaml config file

## 1.6.6

### Patch Changes

- bb9db57: SEO-117: Fix for accounts with no website on company table, but have live brand websites

## 1.6.5

### Patch Changes

- 94cd25a: Updates pnpm-workspace to not include packages recursively

## 1.6.4

### Patch Changes

- 8ad960a: Adds neighborhoods from initiateQueryEntitlement to generatePost in order to send them to airops.

## 1.6.3

### Patch Changes

- 47b0dc0: GLO-793: Add neighborhoods list to Airops input.

## 1.6.2

### Patch Changes

- cd97410: Add update on corepak for image build

## 1.6.1

### Patch Changes

- b4a1c41: Fixed pnpm version to avoid deployment issues.

## 1.6.0

### Minor Changes

- 3dafb7c: GLOW-833: Adds website validation against our platform for each entitlement.

## 1.5.0

### Minor Changes

- f06fb93: Fixes bad changeset

### Patch Changes

- c9c7f63: GLOW-832: Only process entitlements with websiteUrl defined, paginates api-gateway method.'

## 1.4.8

### Patch Changes

- 4e14c1e: GLOW-813: Fixes typo in companies query, improves logging of results for getCompanyDisplayNames.

## 1.4.7

### Patch Changes

- 82f01a0: GLOW-813: Adds company name to missing brands.

## 1.4.6

### Patch Changes

- fa4d952: Move lp_deploy/continue step and remove unneeded context from release-seo-automation

## 1.4.5

### Patch Changes

- 8f2af6d: Adds slack context to release-seo-automation

## 1.4.4

### Patch Changes

- 1b4ac96: Updates circleci config for proper docker image naming

## 1.4.3

### Patch Changes

- f121bd1: Removes bad changesets

## 1.4.2

### Patch Changes

- a2a11a6: Adds build script

## 1.4.1

### Patch Changes

- d204311: Updates run commands for newer turbo version

## 1.4.0

### Minor Changes

- ef3b2a4: @elisabethgross Adds clients, formatters, services, config updates, unit tests

## 1.3.2

### Patch Changes

- 82a5c98: @spencer-melop-lp - lc-1337 - Update graphql inspector and patch all packages to verify deployment

## 1.3.1

### Patch Changes

- a5ac264: Fixes bad import and re-declared variable

## 1.3.0

### Minor Changes

- 22912b9: - Better logging and error handling
  - serverless-offline
  - Local development of lambdas pnpm scripts
  - Fixes GENERATE_POST_RETRY steps when AirOps hits our error webhook which wasn't actually working
  - Adds ability to pass in an event to initiateQueryEntitlements to skip validation and generate for only certain companies

## 1.2.2

### Patch Changes

- f32a198: Ups scheduler lambda memory

## 1.2.0

### Minor Changes

- c552911: Updates Bloom ENV vars

## 1.1.0

### Minor Changes

- 1ab5a7b: Adds bloom

## 1.0.0

### Major Changes

- 65e86dd: Adds Bloom package, fixes lint error in api-gateway
