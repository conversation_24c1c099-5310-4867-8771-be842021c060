# Bloom Scheduler

AI Generated Posts Scheduler service managing automated blog post generation workflow using AWS Step Functions and Lambda.

## Overview

This service orchestrates the process of:

1. Querying entitlements for automated blog posts
2. Distributing post generation jobs to AirOps
3. Handling AirOps webhook callbacks for successes and failures

## Architecture

[ARM](https://luxurypresence.atlassian.net/wiki/spaces/ET/pages/3483795457/2024-10-22+-+Lifecycle+-+BLOOM+Blog+Optimization+Output+Machine)

[Architecture Diagram](https://lucid.app/lucidchart/cea43df2-8387-4ca1-8bf8-18ccb032cc00/edit?viewport_loc=710%2C-1434%2C4436%2C2312%2C0_0&invitationId=inv_35e8907c-52d6-47a7-9ead-4e4dd17b765f)

### Core Components

- Step Functions for workflow orchestration
- Lambda functions for serverless execution
- DLQ pattern for error handling
- Webhook handlers for asynchronous callbacks

### Step Function:

#### Main orchestration workflow

```mermaid
stateDiagram-v2
    direction TB
    Start --> InitiateQueryEntitlements
    InitiateQueryEntitlements --> DistributePostJobs: Success
    InitiateQueryEntitlements --> InitiateQueryEntitlementsDLQ: Error

    state DistributePostJobs {
        StartMap --> generatePost
        generatePost --> generatePost: Retry (3x)
        generatePost --> GeneratePostDLQ: Max Retries Exceeded
    }

    state AirOpsWebhook {
        [*] --> airOpsWebhookError
        airOpsWebhookError --> airOpsWebhookError: Retry (3x)
        airOpsWebhookError --> AirOpsWebhookDLQ: Max Retries Exceeded
    }

    airOpsWebhookError --> generatePost: Invoke

    note right of AirOpsWebhook
        Triggered by AirOps
        error callback
    end note

    %% Add spacing with empty lines in diagram

    DistributePostJobs --> AllPostsProcessed
    AllPostsProcessed --> DoneState: Success
    DoneState --> End
```

### Lambda Functions

- `initiateQueryEntitlements`: Queries and validates entitlements
  - Gets all entitlements with the `AUTOMATED_BLOGS_PRODUCT_ID` and an `endDate` in the future
  - Validates the entitlements based on the current date and the entitlement units
    - Based on the current date and the configured `GENERATE_POST_DAY` i.e. the day to generate posts on, all the weekdays that match the `GENERATE_POST_DAY` in the current month are generated and compared against the entitlement units
    - If a client has 4 units, they get a post every `GENERATE_POST_DAY` for the first 4 weeks of the month
    - If a client has 2 units, they get a post every `GENERATE_POST_DAY` on the first and third week of the month
  - **Note:** It is possible to force blog post generation without validation for all entitlements or specific companies by manually modifying the event body -
    ```json
    {
      "skipValidation": true,
      "companyIds": ["company-123", "company-456"] // Optional, if not provided, all entitlements are generated
    }
    ```
- `generatePost`: Handles post generation via AirOps
  - Posts to AirOps asynchronously to generate posts
- `airOpsWebhookError`: Processes error callbacks from AirOps
  - AirOps sends a post request to the `/webhook/airops/error` endpoint when there is an error
  - The webhook error handler queries AirOps for the execution data
  - Calls the `generatePost` lambda with the execution data

### Error Handling / Debugging

- Retry mechanisms for transient failures
- Dead Letter Queues for persistent failures with a 14-day message retention
- Error monitoring
- Error job creation for tracking
  - Jobs are tracked in the `property.blog_post_job` table. The PK of this table is a composite of `jobId`, `step`, and `status`. With each step in the orchestration workflow, a job is created. This way, you can see the lifecycle of a job in the orchestration.
    - If there are lambda retries, there will be an initial error job. If it exhausts all retries and never succeeds, there will only be the one failure job. If it succeeds on a retry, there will be the initial error job and then a success job. We don't care what retry succeeded.

## Development

Local development is done using the `serverless` framework. Look at their documentation to learn more about the commands and configuration.

To test lambdas locally, see `package.json` for test commands.

You will need an `.env.local` file to run the lambdas locally.
You will need an `.env.development` file to run the lambdas locally.
See the [.env.sample](./.env.sample) for more information.

There are two ways to test this package locally:

1. You can test each lambda individually by using the `pnpm` commands in the `package.json` file to invoke the lambdas locally
2. You can deploy to the dev-sandbox AWS environment and test there. Some gotchas:
   1. Comment out the `vpc` and the `deploymentBucket` section in the `serverless.yml` file
   2. In dev-sandbox, you will not be able to send requests to the LP services such as `cms-service`, etc.
      1. You can run [cms-service](https://github.com/luxurypresence/dashboard/tree/staging/packages/cms-service) and [tenant-service](https://github.com/luxurypresence/tenants/tree/master/packages/tenant-service) locally for the state machine in AWS to hit. API gateway has a public endpoint so as long as you add the `API_GATEWAY_KEY` and `API_GATEWAY_SUPER_USER_COMPANY_ID` from vault to the environment variables, you should be able to hit it. To create a public endpoint that tunnels to your locally running `cms-service` and `identity-service`, you can use [ngrok](https://ngrok.com/) or Cloudflare Tunnel client.
      2. `brew install cloudflared`
      3. `cloudflared tunnel --url http://localhost:8011` (identity-service)
      4. `cloudflared tunnel --url http://localhost:8002` (cms-service)
      5. Add the domains that log as the ENV vars for `TENANT_SERVICE_URL` and `IDENTITY_SERVICE_URL`
      6. **Note:** If you quit the tunneling and start it again, there will be new URL's. You must update these in the `.env.development` file or change them manually in the AWS console for all the lambdas that need those URLs.

### Other Local Development Notes

- For some test events to truly work
  - You will need to pass in data that actually exists in the database such as `jobId`, AirOps `executionId`, etc.
  - If you are testing `generatePost`, no post will succeed in creation if there is no `INITIATE_QUERY_ENTITLEMENTS` job already.
- There are test events in the [testEvents](./testEvents) folder that you can use to test the lambda functions.
- To test error handling and DLQ's you must run the state machine from the console. The individual lambdas do not have DLQ's configured.

### Software Patterns and Practices

1. Factory Pattern

   - Service factories for dependency injection
   - Client factories for external service connections

2. Repository Pattern

   - Abstracted data access through service layers
   - Clean separation between business logic and data sources
   - All business logic should be done in a service
   - All data access should be done through a repository / client

3. Builder Pattern

   - DTO formatters for standardizing data structures
   - Request/Response transformations

4. Strategy Pattern

   - Configurable validation strategies
   - Environment-based configuration handling
   - Do NOT use `process.env.` anywhere in the code, get it from the config

5. Error Handling Pattern
   - Centralized error handling
     - All lambda handlers should ultimately throw an error using `createHandlerError` which adds useful information to the error
   - Custom error types with metadata
     - All clients should get their own error type
   - DLQ implementation

In general:

- All files can make use of formatters however objects passed to a client should be formatted for the client as a DTO in a **fetch service**. Use your judgment to determine.
- The order of dependencies between files should be (everything to the left can import everything to its right, but cannot import anything to its left):
  - handlers → services → repositories / sub-services → clients → utils / types / config / error / formatters

### Prerequisites

- Node.js 18+
- pnpm
- AWS CLI configured
- Serverless Framework

### Installation

```bash
pnpm install
```
