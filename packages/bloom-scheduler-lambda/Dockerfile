FROM node:18-alpine

ARG NODE_ENV
ENV NODE_ENV ${NODE_ENV}
RUN corepack enable
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"

ARG NPM_AUTH_TOKEN
ENV NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN

WORKDIR /usr/src/app

COPY .npmrc .npmrc
COPY . .

# Update corepack to the latest version to prevent signature issue.
# https://github.com/pnpm/pnpm/issues/9029#issuecomment-2629866277
RUN npm i -g corepack@latest

RUN pnpm install --global serverless@3
RUN pnpm install

# Package all production dependencies to be included in the artifact
RUN pnpm --filter bloom-scheduler-lambda deploy output --prod

# Switch to deploy directory
WORKDIR /usr/src/app/output

# Installs package level dev dependencies
RUN pnpm install

# Deploy to lambda
ENTRYPOINT pnpm run deploy
