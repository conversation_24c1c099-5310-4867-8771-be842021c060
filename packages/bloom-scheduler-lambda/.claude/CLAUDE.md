# Bloom Scheduler Lambda

## Package Purpose

Manages blog post generation workflow with Dead Letter Queue (DLQ) pattern for error handling.

## Architecture Patterns

### DLQ Pattern for Error Handling

- Dead Letter Queue implementation for failed message processing
- Robust error handling and retry mechanisms

### Webhook Integration

- Webhook handlers for async callbacks
- Integration with AirOps for blog post generation

## Development Commands

```bash
# Deploy (requires AWS credentials)
AWS_PROFILE=your-profile pnpm deploy

# Test locally
pnpm test
```

## Key Patterns

- Webhook handlers for async callbacks
- Integration with external services (AirOps)
- Error handling with DLQ pattern
- Serverless Lambda architecture

## Environment Configuration

- AWS Lambda and DLQ configurations
- AirOps integration endpoints
- Authentication keys for external services
