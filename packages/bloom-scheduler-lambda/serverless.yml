app: bloom
service: scheduler

useDotenv: true
configValidationMode: warn

plugins:
  - serverless-dotenv-plugin
  - serverless-offline
  - serverless-esbuild
  - serverless-step-functions
  - serverless-plugin-datadog

custom:
  datadog:
    site: datadoghq.com
    apiKey: ${env:DD_API_KEY, ''}
    enableDDTracing: true
    enableDDLogs: true
    enableXrayTracing: false # Datadog-only tracing (consistent with other packages)
    enableSourceCodeIntegration: true
    captureLambdaPayload: true
    propagateUpstreamTrace: true
    addLayers: true
    enableColdStartTracing: true
    enableDDMonitoring: true
    env: ${self:provider.stage}
    service: bloom-scheduler
    version: ${env:GIT_SHA, 'latest'}
    tags: "team:client-marketing"

  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    exclude: ['aws-sdk', '@aws-sdk/client-secrets-manager']
    target: 'node22'
    define: { 'require.resolve': undefined }
    platform: 'node'
    concurrency: 1
    zipConcurrency: 1
    tsconfig: 'tsconfig.json'
    format: 'cjs'
    mainFields: ['module', 'main']

package:
  individually: true

provider:
  name: aws
  runtime: nodejs22.x
  stage: ${env:ENVIRONMENT, 'development'}
  region: ${env:AWS_REGION, 'us-east-1'}
  versionFunctions: false
  deploymentBucket:
    name: ${env:DEPLOY_BUCKET}
  tracing:
    lambda: false # Disabled - using Datadog-only tracing for consistency with other packages
  tags:
    Project: bloom
    Service: scheduler
    dd_trace_enabled: true
    dd_service: bloom-scheduler
    dd_env: ${env:ENVIRONMENT, 'development'}
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT, 'development'}
    BLOG_V2_SLACK_NOTIFICATIONS_ENABLED: ${env:BLOG_V2_SLACK_NOTIFICATIONS_ENABLED, 'false'}
    BLOG_V2_SLACK_WEBHOOK_URL: ${env:BLOG_V2_SLACK_WEBHOOK_URL, ''}
    SLACK_NOTIFICATIONS_ENABLED: ${env:SLACK_NOTIFICATIONS_ENABLED, 'false'}
    SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL, ''}
    API_GATEWAY_SUPER_USER_COMPANY_ID: ${env:API_GATEWAY_SUPER_USER_COMPANY_ID, ''}
    API_GATEWAY_KEY: ${env:API_GATEWAY_KEY, ''}
    API_GATEWAY_URL: ${env:API_GATEWAY_URL, ''}
    COSMO_GQL_URL: ${env:COSMO_GQL_URL, ''}
    M2M_SUPER_API_KEY: ${env:M2M_SUPER_API_KEY, ''}
    LAUNCHDARKLY_KEY: ${env:LAUNCHDARKLY_KEY, ''}
    BLOG_V2_AIR_OPS_WRITER_APP_ID: ${env:BLOG_V2_AIR_OPS_WRITER_APP_ID, ''}
    AIR_OPS_API_URL: ${env:AIR_OPS_API_URL, 'https://app.airops.com/public_api/airops_apps'}
    AIR_OPS_API_KEY: ${env:AIR_OPS_API_KEY, ''}
    EXECUTION_BUCKET_NAME: lp-${self:app}-scheduler-execution-${self:provider.stage}
    SUPER_BLOOM_LD_FLAG: ${env:SUPER_BLOOM_LD_FLAG, 'enable-super-bloom'}
    AUTOMATED_BLOGS_PRODUCT_ID: ${env:AUTOMATED_BLOGS_PRODUCT_ID, '5c9b746c-7327-42ce-b998-ce707e7cee44'}
    POST_PUBLISH_DAY_OF_WEEK: ${env:POST_PUBLISH_DAY_OF_WEEK, 'THURSDAY'}
    TENANT_SERVICE_URL: ${env:TENANT_SERVICE_URL, ''}
    CMS_SERVICE_URL: ${env:CMS_SERVICE_URL, ''}
    LANGFUSE_SECRET_KEY: ${env:LANGFUSE_SECRET_KEY, ''}
    LANGFUSE_PUBLIC_KEY: ${env:LANGFUSE_PUBLIC_KEY, ''}
    LANGFUSE_BASE_URL: ${env:LANGFUSE_BASE_URL, 'https://us.cloud.langfuse.com'}
    LANGFUSE_ENVIRONMENT: ${env:LANGFUSE_ENVIRONMENT, 'staging'}
    LANGFUSE_FLUSH_AT: ${env:LANGFUSE_FLUSH_AT, 1}
    OPENAI_API_KEY: ${env:OPENAI_API_KEY, ''}
    OPENAI_MODEL_NAME: ${env:OPENAI_MODEL_NAME, 'gpt-4o'}
    OPENAI_TEMPERATURE: ${env:OPENAI_TEMPERATURE, '0'}
    GEMINI_API_KEY: ${env:GEMINI_API_KEY, ''}
    GENERATE_POST_DAY: ${env:GENERATE_POST_DAY, 'THURSDAY'}
    GIT_SHA: ${env:GIT_SHA, 'latest'}
    ENTITLEMENTS_CHUNK_SIZE: ${env:ENTITLEMENTS_CHUNK_SIZE, '5'}
    MOCK_DATA_MODE: ${env:MOCK_DATA_MODE, 'false'}
    AIR_OPS_WORKFLOW_ID: ${env:AIR_OPS_WORKFLOW_ID, ''}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - lambda:GetLayerVersion
        - sqs:SendMessage
        - states:CreateStateMachine
        - states:UpdateStateMachine
        - states:DeleteStateMachine
        - states:DescribeStateMachine
        - states:StartExecution
        - states:StartSyncExecution
        - states:StopExecution
        - states:ListExecutions
        - states:GetExecutionHistory
      Resource:
        - '*'
    - Effect: Allow
      Action:
        - sqs:SendMessage
        - s3:GetObject
        - s3:PutObject
        - s3:ListBucket
      Resource:
        - !GetAtt InitiateQueryEntitlementsDLQ.Arn
        - !GetAtt GeneratePostDLQ.Arn
        - !GetAtt AirOpsWebhookErrorDLQ.Arn
        - !GetAtt ExecutionBucket.Arn
        - !Sub "${ExecutionBucket.Arn}/*"
  vpc:
    securityGroupIds:
      'Fn::Split':
        - ','
        - ${env:SECURITY_GROUP_IDS}
    subnetIds:
      'Fn::Split':
        - ','
        - ${env:SUBNET_IDS}

functions:
  initiateQueryEntitlements:
    name: ${self:app}-initiate-query-entitlements-${self:provider.stage}
    handler: src/handlers/initiateQueryEntitlements.handler
    memorySize: 1024
    timeout: 180
    environment:
      AWS_SDK_LOAD_CONFIG: "false"

  generatePost:
    name: ${self:app}-generate-post-${self:provider.stage}
    handler: src/handlers/generatePost.handler
    memorySize: 512
    timeout: 180
    reservedConcurrency: 5

  airOpsWebhookError:
    name: ${self:app}-airops-webhook-error-${self:provider.stage}
    handler: src/handlers/airOpsWebhookError.handler
    events:
      - http:
          path: /webhook/airops/error
          method: post
          cors: true
    environment:
      GENERATE_POST_FUNCTION_NAME: ${self:functions.generatePost.name}
    onError: !GetAtt AirOpsWebhookErrorDLQ.Arn
    memorySize: 512

  # Blog Post Writer v2 Lambda Functions
  processBlogInputs:
    name: ${self:app}-process-blog-inputs-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/process-blog-inputs.handler
    timeout: 60
    memorySize: 256
    description: Process and validate blog writer input parameters

  fetchEntitlements:
    name: ${self:app}-fetch-entitlements-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/fetch-entitlements.handler
    timeout: 600
    memorySize: 2048
    description: Fetch entitlements and check feature flags for eligible companies
    environment:
      EXECUTION_BUCKET_NAME: lp-${self:app}-scheduler-execution-${self:provider.stage}
      LAUNCHDARKLY_KEY: ${env:LAUNCHDARKLY_KEY, ''}

  getBrand:
    name: ${self:app}-get-brand-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/get-brand.handler
    timeout: 120
    memorySize: 512
    description: Get brand profile and neighborhoods for company

  blogTopicSelector:
    name: ${self:app}-blog-topic-selector-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/blog-topic-selector.handler
    timeout: 120
    memorySize: 512
    description: Select next blog topic based on preferences and history

  blogPostWriter:
    name: ${self:app}-blog-post-writer-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/blog-post-writer.handler
    timeout: 180
    memorySize: 1024
    description: Initiate blog post generation via AirOps API
    environment:
      BLOG_V2_AIR_OPS_WRITER_APP_ID: ${env:BLOG_V2_AIR_OPS_WRITER_APP_ID, ''}

  getAirOpsJobStatus:
    name: ${self:app}-get-airops-job-status-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/get-airops-job-status.handler
    timeout: 60
    memorySize: 512
    description: Check AirOps job execution status

  blogPostImageGenerator:
    name: ${self:app}-blog-post-image-generator-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/blog-post-image-generator.handler
    timeout: 180
    memorySize: 1024
    description: Generate images for blog posts (pass-through for now)

  blogPostPublisher:
    name: ${self:app}-blog-post-publisher-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/blog-post-publisher.handler
    timeout: 300
    memorySize: 1024
    description: Publish blog post to CMS or S3
    environment:
      EXECUTION_BUCKET_NAME: lp-${self:app}-scheduler-execution-${self:provider.stage}

  resultsWriter:
    name: ${self:app}-results-writer-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/results-writer.handler
    timeout: 120
    memorySize: 512
    description: Generate execution report and send notifications
    environment:
      EXECUTION_BUCKET_NAME: lp-${self:app}-scheduler-execution-${self:provider.stage}
      SLACK_NOTIFICATIONS_ENABLED: ${env:SLACK_NOTIFICATIONS_ENABLED, 'false'}
      SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL, ''}

  catchBlogWriterFailure:
    name: ${self:app}-catch-blog-writer-failure-${self:provider.stage}
    handler: src/lambdas/blog-post-writer/catch-failure.handler
    timeout: 60
    memorySize: 256
    description: Handle blog writer step function failures

stepFunctions:
  validate: true
  stateMachines:
    blogPostJobStateMachine:
      name: ${self:app}-${self:provider.stage}-blogPostJobStateMachine
      role: !GetAtt StepFunctionsExecutionRole.Arn
      type: STANDARD
      definition:
        StartAt: InitiateQueryEntitlements
        States:
          InitiateQueryEntitlements:
            Type: Task
            Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-initiate-query-entitlements-${self:provider.stage}"
            Retry:
              - ErrorEquals:
                  - 'Lambda.ServiceException' # AWS Lambda service errors
                  - 'Lambda.TooManyRequestsException' # Rate limiting
                  - 'Lambda.TimeoutError' # Lambda timeout
                  - 'States.Timeout' # Step Functions timeout
                  - 'States.TaskFailed.StatusCode-429' # HTTP 429 Too Many Requests
                  - 'States.TaskFailed.StatusCode-500' # HTTP 500 Internal Server Error
                  - 'States.TaskFailed.StatusCode-502' # HTTP 502 Bad Gateway
                  - 'States.TaskFailed.StatusCode-503' # HTTP 503 Service Unavailable
                  - 'States.TaskFailed.StatusCode-504' # HTTP 504 Gateway Timeout
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                Next: SendToInitiateQueryEntitlementsDLQ
            Next: DistributePostJobs

          SendToInitiateQueryEntitlementsDLQ:
            Type: Task
            Resource: arn:aws:states:::sqs:sendMessage
            Parameters:
              QueueUrl: !GetAtt InitiateQueryEntitlementsDLQ.QueueUrl
              MessageBody.$: '$'
            End: true

          DistributePostJobs:
            Type: Map
            Next: DoneState
            Iterator:
              StartAt: generatePost
              States:
                generatePost:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-generate-post-${self:provider.stage}"
                  Next: Wait
                  Retry:
                    - ErrorEquals:
                        - 'Lambda.ServiceException' # AWS Lambda service errors
                        - 'Lambda.TooManyRequestsException' # Rate limiting
                        - 'Lambda.TimeoutError' # Lambda timeout
                        - 'States.Timeout' # Step Functions timeout
                        - 'States.TaskFailed.StatusCode-429' # HTTP 429 Too Many Requests
                        - 'States.TaskFailed.StatusCode-500' # HTTP 500 Internal Server Error
                        - 'States.TaskFailed.StatusCode-502' # HTTP 502 Bad Gateway
                        - 'States.TaskFailed.StatusCode-503' # HTTP 503 Service Unavailable
                        - 'States.TaskFailed.StatusCode-504' # HTTP 504 Gateway Timeout
                      IntervalSeconds: 5
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      Next: GeneratePostSendToGeneratePostDLQ
                Wait:
                  Type: Wait
                  Seconds: 150
                  End: true
                GeneratePostSendToGeneratePostDLQ:
                  Type: Task
                  Resource: arn:aws:states:::sqs:sendMessage
                  Parameters:
                    QueueUrl: !GetAtt GeneratePostDLQ.QueueUrl
                    MessageBody.$: '$'
                  Next: Wait
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
            Label: DistributePostJobs
            MaxConcurrency: 4
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket.$: $.bucketName
                Key.$: $.key
            ToleratedFailurePercentage: 5
            ToleratedFailureCount: 10
          DoneState:
            Type: Succeed

    blogPostWriterStateMachineV2:
      name: ${self:app}-${self:provider.stage}-blogPostWriterStateMachineV2
      role: !GetAtt BlogPostWriterStepFunctionsRole.Arn
      type: STANDARD
      definition:
        Comment: "Blog Post Writer State Machine v2"
        StartAt: ProcessBlogInputs
        States:
          ProcessBlogInputs:
            Type: Task
            Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-process-blog-inputs-${self:provider.stage}"
            Parameters:
              input.$: $
              executionName.$: $$.Execution.Name
              executionStartTime.$: $$.Execution.StartTime
            ResultPath: $.normalizedInput
            Retry:
              - ErrorEquals: 
                  - States.ALL
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: 
                  - States.ALL
                ResultPath: $.error
                Next: CatchFailure
            Next: FetchEntitlements
          FetchEntitlements:
            Type: Task
            Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-fetch-entitlements-${self:provider.stage}"
            Parameters:
              companyIds.$: $.normalizedInput.companyIds
              dryRun.$: $.normalizedInput.dryRun
              publishToCMS.$: $.normalizedInput.publishToCMS
              executionName.$: $.normalizedInput.executionName
              executionStartTime.$: $.normalizedInput.executionStartTime
              executionArn.$: $$.Execution.Id
              skipEntitlementsValidation.$: $.normalizedInput.skipEntitlementsValidation
              skipUnitsValidation.$: $.normalizedInput.skipUnitsValidation
              skipExistingScheduledPostValidation.$: $.normalizedInput.skipExistingScheduledPostValidation
            ResultPath: $.entitlementData
            Retry:
              - ErrorEquals:
                  - TransientFetchEntitlementsError
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
              - ErrorEquals:
                  - States.ALL
                IntervalSeconds: 5
                MaxAttempts: 2
                BackoffRate: 2
            Catch:
              - ErrorEquals:
                  - States.ALL
                ResultPath: $.error
                Next: CatchFailure
            Next: CheckHasCompanies
          CheckHasCompanies:
            Type: Choice
            Choices:
              - And:
                  - Variable: $.entitlementData.eligibleCompanies
                    NumericGreaterThan: 0
                  - Variable: $.entitlementData.s3Key
                    IsPresent: true
                Next: ProcessCompanies
            Default: SkipProcessing
          SkipProcessing:
            Type: Pass
            Parameters:
              results: []
            ResultPath: $.mapResults
            Next: ResultsWriter
          ProcessCompanies:
            Type: Map
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket: lp-${self:app}-scheduler-execution-${self:provider.stage}
                Key.$: $.entitlementData.s3Key
            ItemsPath: $.companies
            MaxConcurrency: 5
            ItemSelector:
              companyId.$: $$.Map.Item.Value[0].companyId
              entitlements.$: $$.Map.Item.Value[0].entitlements
              entitlementData.$: $.entitlementData
            ItemProcessor:
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
              StartAt: PrepareCompanyContext
              States:
                PrepareCompanyContext:
                  Type: Pass
                  Parameters:
                    companyId.$: $.companyId
                    config:
                      dryRun.$: $.entitlementData.dryRun
                      executionFolderName.$: $.entitlementData.executionFolderName
                  ResultPath: $.context
                  Next: GetBrand
                GetBrand:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-get-brand-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    dryRun.$: $.context.config.dryRun
                    executionFolderName.$: $.context.config.executionFolderName
                  ResultPath: $.brandData
                  Retry:
                    - ErrorEquals:
                        - TransientGetBrandError
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - MissingBrandProfileError
                      ResultPath: $.error
                      Next: SkipNoBrandProfile
                    - ErrorEquals:
                        - States.ALL
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: BlogTopicSelector
                BlogTopicSelector:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-topic-selector-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    brandProfile.$: $.brandData.brandProfile
                    neighborhoods.$: $.brandData.neighborhoods
                    preferredNeighborhoods.$: $.brandData.preferredNeighborhoods
                    dryRun.$: $.context.config.dryRun
                    publishToCMS.$: $.entitlementData.publishToCMS
                    executionFolderName.$: $.context.config.executionFolderName
                  ResultPath: $.topicData
                  Retry:
                    - ErrorEquals:
                        - TransientBlogTopicSelectorError
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - NoAvailableTopicsError
                      ResultPath: $.error
                      Next: SkipNoTopics
                    - ErrorEquals:
                        - States.ALL
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: BlogPostWriter
                BlogPostWriter:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-writer-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    selectedTopic.$: $.topicData.selectedTopic
                    brandProfile.$: $.brandData.brandProfile
                    dryRun.$: $.context.config.dryRun
                    executionFolderName.$: $.context.config.executionFolderName
                  ResultPath: $.writerResult
                  Retry:
                    - ErrorEquals:
                        - TransientBlogPostWriterError
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - States.ALL
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: InitializePollingCounter
                InitializePollingCounter:
                  Type: Pass
                  Parameters:
                    count: 0
                  ResultPath: $.pollingAttempts
                  Next: WaitForAirOps
                WaitForAirOps:
                  Type: Wait
                  Seconds: 60
                  Next: IncrementPollingCounter
                IncrementPollingCounter:
                  Type: Pass
                  Parameters:
                    count.$: States.MathAdd($.pollingAttempts.count, 1)
                  ResultPath: $.pollingAttempts
                  OutputPath: $
                  Next: GetAirOpsJobStatus
                GetAirOpsJobStatus:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-get-airops-job-status-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    topicId.$: $.topicData.selectedTopic.id
                    airopsExecutionId.$: $.writerResult.airopsExecutionId
                    dryRun.$: $.context.config.dryRun
                    executionFolderName.$: $.context.config.executionFolderName
                    executionStartTime.$: $.entitlementData.executionStartTime
                    pollingAttempts.$: $.pollingAttempts
                  ResultPath: $.jobStatus
                  Retry:
                    - ErrorEquals:
                        - TransientGetAirOpsJobStatusError
                      IntervalSeconds: 2
                      MaxAttempts: 5
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - States.ALL
                      Next: AirOpsFailed
                      ResultPath: $.error
                  Next: CheckJobStatus
                CheckJobStatus:
                  Type: Choice
                  Choices:
                    - Variable: $.jobStatus.status
                      StringEquals: completed
                      Next: BlogPostImageGenerator
                    - Variable: $.jobStatus.status
                      StringEquals: failed
                      Next: AirOpsFailed
                    - Variable: $.jobStatus.status
                      StringEquals: cancelled
                      Next: AirOpsFailed
                    - Variable: $.jobStatus.status
                      StringEquals: expired
                      Next: AirOpsFailed
                    - Variable: $.pollingAttempts.count
                      NumericGreaterThan: 60
                      Next: PollingTimeout
                  Default: WaitForAirOps
                PollingTimeout:
                  Type: Fail
                  Error: PollingTimeout
                  Cause: "AirOps job polling exceeded maximum attempts (60 attempts * 60 seconds = 60 minutes)"
                BlogPostImageGenerator:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-image-generator-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    topicId.$: $.topicData.selectedTopic.id
                    s3Location.$: $.jobStatus.s3Location
                    dryRun.$: $.context.config.dryRun
                    executionFolderName.$: $.context.config.executionFolderName
                  ResultPath: $.imageResult
                  Retry:
                    - ErrorEquals:
                        - TransientBlogPostImageGeneratorError
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - States.ALL
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: CheckPublishToCMS
                CheckPublishToCMS:
                  Type: Choice
                  Choices:
                    - Variable: $.entitlementData.publishToCMS
                      BooleanEquals: true
                      Next: BlogPostPublisher
                  Default: SkipPublishing
                SkipPublishing:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    topicId.$: $.topicData.selectedTopic.id
                    published: false
                    createdAt.$: $$.State.EnteredTime
                    s3Location.$: $.jobStatus.s3Location
                    cmsPostId.$: $.topicData.selectedTopic.id
                    reason: "publishToCMS disabled - content saved to S3 as artifact"
                  End: true
                BlogPostPublisher:
                  Type: Task
                  Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-publisher-${self:provider.stage}"
                  Parameters:
                    companyId.$: $.context.companyId
                    topicId.$: $.topicData.selectedTopic.id
                    s3Location.$: $.jobStatus.s3Location
                    mediaId.$: $.imageResult.mediaId
                    imageUrl.$: $.imageResult.imageUrl
                    dryRun.$: $.context.config.dryRun
                  ResultPath: $.publishResult
                  Retry:
                    - ErrorEquals:
                        - TransientBlogPostPublisherError
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - States.ALL
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals:
                        - States.ALL
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: NormalizeSuccessOutput
                NormalizeSuccessOutput:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    topicId.$: $.topicData.selectedTopic.id
                    published.$: $.publishResult.published
                    createdAt.$: $.publishResult.createdAt
                    s3Location.$: $.publishResult.s3Location
                    cmsPostId.$: $.publishResult.cmsPostId
                  End: true
                SkipNoBrandProfile:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    skipped: true
                    reason: "No brand profile found"
                  End: true
                SkipNoTopics:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    skipped: true
                    reason: "No available blog topics"
                  End: true
                AirOpsFailed:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    failed: true
                    error:
                      Error: AirOpsFailed
                      Cause: "AirOps job failed"
                  End: true
                CompanyProcessingFailed:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    failed: true
                    error.$: $.error
                  End: true
            ResultPath: $.mapResults
            Next: ResultsWriter
          ResultsWriter:
            Type: Task
            Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-results-writer-${self:provider.stage}"
            Parameters:
              executionName.$: $.normalizedInput.executionName
              executionStartTime.$: $.normalizedInput.executionStartTime
              executionArn.$: $$.Execution.Id
              dryRun.$: $.normalizedInput.dryRun
              publishToCMS.$: $.normalizedInput.publishToCMS
              results.$: $.mapResults
            ResultPath: $.executionReport
            Retry:
              - ErrorEquals:
                  - TransientResultsWriterError
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
              - ErrorEquals:
                  - States.ALL
                IntervalSeconds: 5
                MaxAttempts: 2
                BackoffRate: 2
            Catch:
              - ErrorEquals:
                  - States.ALL
                ResultPath: $.error
                Next: CatchFailure
            End: true
          CatchFailure:
            Type: Task
            Resource: !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-catch-blog-writer-failure-${self:provider.stage}"
            Parameters:
              error.$: $.error
              executionName.$: $.normalizedInput.executionName
              companyIds.$: $.normalizedInput.companyIds
              executionArn.$: $$.Execution.Id
              context.$: $
            End: true

  role:
    statements:
      - Effect: Allow
        Action:
          - lambda:InvokeFunction
          - sqs:SendMessage
          - s3:GetObject
          - s3:PutObject
        Resource:
          - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-initiate-query-entitlements-${self:provider.stage}"
          - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-generate-post-${self:provider.stage}"
          - !GetAtt InitiateQueryEntitlementsDLQ.Arn
          - !GetAtt GeneratePostDLQ.Arn
          - !GetAtt AirOpsWebhookErrorDLQ.Arn
          - !Sub "${ExecutionBucket.Arn}/*"
      - Effect: Allow
        Action:
          - states:StartExecution
          - states:StartSyncExecution
          - states:DescribeExecution
          - states:StopExecution
        Resource:
          - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:stateMachine:${self:app}-${self:provider.stage}-blogPostJobStateMachine"
          - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:execution:${self:app}-${self:provider.stage}-blogPostJobStateMachine:*"

resources:
  Resources:
    BlogPostJobSchedulerLogs:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/states/${self:app}-${self:provider.stage}-blogPostJobStateMachine
        RetentionInDays: 14

    BlogPostWriterStateMachineV2Logs:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/states/${self:app}-${self:provider.stage}-blogPostWriterStateMachineV2
        RetentionInDays: 14

    InitiateQueryEntitlementsDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:app}-initiate-query-entitlements-dlq-${self:provider.stage}
        MessageRetentionPeriod: 1209600 # 14 days

    GeneratePostDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:app}-generate-post-dlq-${self:provider.stage}
        MessageRetentionPeriod: 1209600 # 14 days

    AirOpsWebhookErrorDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:app}-airops-webhook-error-dlq-${self:provider.stage}
        MessageRetentionPeriod: 1209600 # 14 days

    ScheduleRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:app}-scheduler-rule-${self:provider.stage}
        ScheduleExpression: !Sub cron(0 12 ? * ${env:GENERATE_POST_DAY} *) # Runs at 07:00 EST (12:00 UTC) on specified day
        State: ${env:SCHEDULER_STATE, 'DISABLED'} # Default to DISABLED if not set
        Targets:
          - Arn: !Sub "arn:aws:states:${aws:region}:${aws:accountId}:stateMachine:${self:app}-${self:provider.stage}-blogPostJobStateMachine"
            Id: 'BlogPostJobStateMachineTarget'
            RoleArn: !GetAtt StepFunctionInvokeRole.Arn

    StepFunctionInvokeRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:app}-scheduler-${self:provider.stage}
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StepFunctionInvokePolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - states:StartExecution
                  Resource:
                    - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:stateMachine:${self:app}-${self:provider.stage}-blogPostJobStateMachine"

    ExecutionBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: lp-${self:app}-scheduler-execution-${self:provider.stage}
        AccessControl: 'Private'
        BucketEncryption:
          ServerSideEncryptionConfiguration:
            - ServerSideEncryptionByDefault:
                SSEAlgorithm: AES256
        VersioningConfiguration:
          Status: Enabled

    StepFunctionsExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:app}-${self:provider.stage}-stepfunctions-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StepFunctionsExecutionPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                    - states:StartExecution
                    - states:StartSyncExecution
                    - states:DescribeExecution
                    - states:StopExecution
                    - s3:GetObject
                    - s3:PutObject
                    - s3:ListBucket
                    - sqs:SendMessage
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource:
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-initiate-query-entitlements-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-generate-post-${self:provider.stage}"
                    - !GetAtt InitiateQueryEntitlementsDLQ.Arn
                    - !GetAtt GeneratePostDLQ.Arn
                    - !GetAtt ExecutionBucket.Arn
                    - !Sub "${ExecutionBucket.Arn}/*"
                    - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:stateMachine:${self:app}-${self:provider.stage}-blogPostJobStateMachine"
                    - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:execution:${self:app}-${self:provider.stage}-blogPostJobStateMachine:*"
                    - !GetAtt BlogPostJobSchedulerLogs.Arn

    BlogPostWriterStepFunctionsRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:app}-${self:provider.stage}-blog-writer-stepfunctions-role
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: BlogPostWriterStepFunctionsPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                  Resource:
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-process-blog-inputs-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-fetch-entitlements-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-get-brand-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-topic-selector-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-writer-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-get-airops-job-status-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-image-generator-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-blog-post-publisher-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-results-writer-${self:provider.stage}"
                    - !Sub "arn:aws:lambda:${aws:region}:${aws:accountId}:function:${self:app}-catch-blog-writer-failure-${self:provider.stage}"
                - Effect: Allow
                  Action:
                    - s3:GetObject
                    - s3:PutObject
                    - s3:PutObjectTagging
                    - s3:ListBucket
                  Resource:
                    - !GetAtt ExecutionBucket.Arn
                    - !Sub "${ExecutionBucket.Arn}/*"
                - Effect: Allow
                  Action:
                    - states:StartExecution
                    - states:DescribeExecution
                    - states:StopExecution
                  Resource:
                    - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:stateMachine:${self:app}-${self:provider.stage}-blogPostWriterStateMachineV2"
                    - !Sub "arn:aws:states:${aws:region}:${aws:accountId}:execution:${self:app}-${self:provider.stage}-blogPostWriterStateMachineV2:*"
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource:
                    - !Sub "arn:aws:logs:${aws:region}:${aws:accountId}:log-group:/aws/states/${self:app}-${self:provider.stage}-blogPostWriterStateMachineV2:*"
                - Effect: Allow
                  Action:
                    - xray:PutTraceSegments
                    - xray:PutTelemetryRecords
                  Resource: '*'
