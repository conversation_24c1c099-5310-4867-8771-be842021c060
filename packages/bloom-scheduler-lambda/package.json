{"name": "bloom-scheduler-lambda", "version": "2.21.0", "description": "AI Generated Posts Scheduler", "author": "<PERSON><PERSON>ury Presence Dev Team", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "**************:luxurypresence/dashboard.git", "directory": "bloom-scheduler-lambda"}, "scripts": {"build": "tsc", "start:dev": "ENVIRONMENT=local nodemon --watch src -e ts --exec \"ts-node\" --exec \"serverless offline start --stage local\"", "local:initiate": "ENVIRONMENT=local sls invoke local -f initiateQueryEntitlements -p testEvents/initiateQueryEntitlementsEvent.json", "local:initiateSkipValidation": "ENVIRONMENT=local sls invoke local -f initiateQueryEntitlements -p testEvents/initiateQueryEntitlementsEventSkipValidation.json", "local:generate": "ENVIRONMENT=local sls invoke local -f generatePost -p testEvents/generatePostEvent.json", "local:webhook": "ENVIRONMENT=local sls invoke local -f airOpsWebhookError -p testEvents/airOpsWebhookEvent.json", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --check \"src/**/*.ts\"", "format:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "cross-env LOAD_TEST_CONFIG='true' jest --runInBand --verbose=true --forceExit", "run:lambda": "ts-node -r dotenv/config -r tsconfig-paths/register src/lambdas/run.ts", "deploy": "serverless deploy --verbose", "changeset": "cd ../../ && changeset"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.10.0", "@aws-sdk/client-lambda": "^3.687.0", "@aws-sdk/client-s3": "^3.772.0", "datadog-lambda-js": "^12.127.0", "dd-trace": "^5.0.0", "@aws-sdk/client-sqs": "^3.687.0", "@google/genai": "^1.19.0", "@langfuse/client": "^4.0.0", "@langfuse/openai": "^4.0.0", "@langfuse/otel": "^4.0.0", "@langfuse/tracing": "^4.0.0", "@opentelemetry/sdk-node": "^0.204.0", "@smithy/node-http-handler": "^4.0.4", "cross-env": "^7.0.3", "graphql": "^16.9.0", "graphql-request": "^7.1.2", "jsonwebtoken": "^9.0.2", "launchdarkly-node-server-sdk": "^7.0.4", "openai": "^5.19.1", "p-limit": "^3.1.0", "serverless-esbuild": "^1.54.5", "serverless-step-functions": "^3.21.1", "source-map-support": "^0.5.21", "uuid": "^10.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "9.16.0", "@types/aws-lambda": "^8.10.145", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.9.0", "@types/uuid": "^10.0.0", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.2.5", "serverless": "^3.39.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-offline": "^14.3.4", "serverless-plugin-datadog": "^5.101.0", "serverless-plugin-typescript": "^2.1.4", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.3.3", "typescript-eslint": "^8.17.0"}, "jest": {"preset": "ts-jest", "setupFilesAfterEnv": ["<rootDir>/jest.setup.ts"], "moduleNameMapper": {"^src/(.*)": "<rootDir>/src/$1"}, "moduleDirectories": ["node_modules", "src"], "modulePathIgnorePatterns": ["node_modules", "build", ".history"], "testEnvironment": "node", "roots": ["<rootDir>/src"], "modulePaths": ["<rootDir>/src"], "transformIgnorePatterns": ["node_modules/(?!(graphql-request)/)"]}, "keywords": []}