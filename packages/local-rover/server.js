const https = require('https');
const fs = require('fs');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');
const express = require('express');

// Create Express app
const app = express();

// Configure proxy middleware
const roverProxy = createProxyMiddleware({
  target: 'http://127.0.0.1:4001',
  changeOrigin: true,
  ws: true, // Support WebSocket
  logLevel: 'debug',
});

// Use the proxy for all requests
app.use('/', roverProxy);

// SSL certificate options
const sslOptions = {
  key: fs.readFileSync(
    path.resolve(__dirname, 'local.luxurycoders.com-key.pem')
  ),
  cert: fs.readFileSync(
    path.resolve(__dirname, 'local.luxurycoders.com.pem')
  ),
};

// Create HTTPS server
const server = https.createServer(sslOptions, app);

// Start server
const PORT = 4000;
server.listen(PORT, () => {
  console.log(
    `HTTPS Proxy Server running on https://local.luxurycoders.com:${PORT}/graphql`
  );
  console.log(`Proxying requests to <PERSON> at http://127.0.0.1:4001`);
});
