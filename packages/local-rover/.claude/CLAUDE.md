# Local Rover

## Package Purpose

Local GraphQL federation development environment for testing schema changes and gateway functionality.

## Setup Requirements

- Requires mkcert for SSL certificates
- Used for local GraphQL federation testing

## Development Commands

```bash
# Start local federation gateway
pnpm start
```

## Key Patterns

- Local GraphQL federation development
- SSL certificate management with mkcert
- Gateway functionality testing
- Schema federation testing environment

## Environment Configuration

- Local SSL certificate paths
- GraphQL service endpoints for federation
- Development-specific gateway configurations
