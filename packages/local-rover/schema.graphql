schema
  @link(url: "https://specs.apollo.dev/link/v1.0")
  @link(url: "https://specs.apollo.dev/join/v0.3", for: EXECUTION)
{
  query: Query
  mutation: Mutation
}

directive @join__enumValue(graph: join__Graph!) repeatable on ENUM_VALUE

directive @join__field(graph: join__Graph, requires: join__FieldSet, provides: join__FieldSet, type: String, external: Boolean, override: String, usedOverridden: Boolean) repeatable on FIELD_DEFINITION | INPUT_FIELD_DEFINITION

directive @join__graph(name: String!, url: String!) on ENUM_VALUE

directive @join__implements(graph: join__Graph!, interface: String!) repeatable on OBJECT | INTERFACE

directive @join__type(graph: join__Graph!, key: join__FieldSet, extension: Boolean! = false, resolvable: Boolean! = true, isInterfaceObject: Boolean! = false) repeatable on OBJECT | INTERFACE | UNION | ENUM | INPUT_OBJECT | SCALAR

directive @join__unionMember(graph: join__Graph!, member: String!) repeatable on UNION

directive @link(url: String, as: String, for: link__Purpose, import: [link__Import]) repeatable on SCHEMA

type CompaniesPage
  @join__type(graph: TENANT_SERVICE)
{
  items: [Company!]!
  total: Int!
}

type Company
  @join__type(graph: CLIENT_MARKETING_SERVICE, key: "displayId")
  @join__type(graph: TENANT_SERVICE, key: "displayId")
{
  displayId: ID!
  name: String! @join__field(graph: TENANT_SERVICE)
  email: String @join__field(graph: TENANT_SERVICE)
  website: String @join__field(graph: TENANT_SERVICE)
  type: String @join__field(graph: TENANT_SERVICE)
  addressLine1: String @join__field(graph: TENANT_SERVICE)
  addressLine2: String @join__field(graph: TENANT_SERVICE)
  addressCity: String @join__field(graph: TENANT_SERVICE)
  addressState: String @join__field(graph: TENANT_SERVICE)
  addressCountry: String @join__field(graph: TENANT_SERVICE)
  addressPostalCode: String @join__field(graph: TENANT_SERVICE)
  phoneNumber: String @join__field(graph: TENANT_SERVICE)
  currentBusinessSize: String @join__field(graph: TENANT_SERVICE)
  businessAddressLine1: String @join__field(graph: TENANT_SERVICE)
  businessAddressLine2: String @join__field(graph: TENANT_SERVICE)
  currentTargetMarket: String @join__field(graph: TENANT_SERVICE)
  globalNetwork: Boolean! @join__field(graph: TENANT_SERVICE)
  accountManagerId: String @join__field(graph: TENANT_SERVICE)
  accountManager: User @join__field(graph: TENANT_SERVICE)
  mlsSearchUserId: String @join__field(graph: TENANT_SERVICE)
  hasMlsAddon: Boolean! @join__field(graph: TENANT_SERVICE)
  defaultMapBounds: JSON @join__field(graph: TENANT_SERVICE)
  onboardingComplete: Boolean! @join__field(graph: TENANT_SERVICE)
  onboardingState: JSON @join__field(graph: TENANT_SERVICE)
  onboardingHubState: JSON @join__field(graph: TENANT_SERVICE)
  notes: String @join__field(graph: TENANT_SERVICE)
  favicon: JSON @join__field(graph: TENANT_SERVICE)
  logoDark: JSON @join__field(graph: TENANT_SERVICE)
  logoLight: JSON @join__field(graph: TENANT_SERVICE)
  demoAccount: Boolean! @join__field(graph: TENANT_SERVICE)
  leadEmail: String @join__field(graph: TENANT_SERVICE)
  additionalLeadEmails: [String!]! @join__field(graph: TENANT_SERVICE)
  leadPhoneNumbers: [String!]! @join__field(graph: TENANT_SERVICE)
  accessibilityWidget: Boolean! @join__field(graph: TENANT_SERVICE)
  companySettings: JSON @join__field(graph: TENANT_SERVICE)
  googlePlaceData: JSON @join__field(graph: TENANT_SERVICE)
  googleAdsCustomerId: String @join__field(graph: TENANT_SERVICE)
  locationDisplayName: String @join__field(graph: TENANT_SERVICE)
  propertyTypes: [String!] @join__field(graph: TENANT_SERVICE)
  websiteAssets: [String!] @join__field(graph: TENANT_SERVICE)
  brandMood: String @join__field(graph: TENANT_SERVICE)
  deletedAt: DateTime @join__field(graph: TENANT_SERVICE)
  createdAt: DateTime! @join__field(graph: TENANT_SERVICE)
  updatedAt: DateTime! @join__field(graph: TENANT_SERVICE)
  salesforceTenantId: String @join__field(graph: TENANT_SERVICE)
  salesforceAccountId: String @join__field(graph: TENANT_SERVICE)
  salesforceData: JSON @join__field(graph: TENANT_SERVICE)
  feedFilters: JSON @join__field(graph: TENANT_SERVICE)
  feedFiltersUpdatedAt: DateTime @join__field(graph: TENANT_SERVICE)
  products: [String] @join__field(graph: TENANT_SERVICE)
  tierPlan: String @join__field(graph: TENANT_SERVICE)
  hstFilters: JSON @join__field(graph: TENANT_SERVICE)
  outboundSmsNumber: String @join__field(graph: TENANT_SERVICE)
  twilioMessagingServiceSid: String @join__field(graph: TENANT_SERVICE)
  twilioPhoneNumberSid: String @join__field(graph: TENANT_SERVICE)
  twilioBrandSid: String @join__field(graph: TENANT_SERVICE)
  twilioCampaignSid: String @join__field(graph: TENANT_SERVICE)
}

union Content
  @join__type(graph: CLIENT_MARKETING_SERVICE)
  @join__unionMember(graph: CLIENT_MARKETING_SERVICE, member: "FeedBlogPost")
  @join__unionMember(graph: CLIENT_MARKETING_SERVICE, member: "FeedRecommendationGroup")
  @join__unionMember(graph: CLIENT_MARKETING_SERVICE, member: "FeedLeads")
  @join__unionMember(graph: CLIENT_MARKETING_SERVICE, member: "FeedAdPerformance")
 = FeedBlogPost | FeedRecommendationGroup | FeedLeads | FeedAdPerformance

input CreateKeywordDto
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  keyword: String!
  metadata: JSONObject
}

input CreateRecommendationInput
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  groupId: ID!
  scrapeId: ID!
  type: RecommendationType!
  currentValue: String
  recommendationValue: String!
  reasoning: String!
  status: RecommendationStatus
  metadata: JSONObject
}

input CreateScheduledActionInput
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  companyId: String!
  workflowType: WorkflowType!
  status: ScheduledActionStatus
  scheduledToBePublishedAt: DateTime
  contentPayload: JSONObject
  generationPayload: JSONObject
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime
  @join__type(graph: CLIENT_MARKETING_SERVICE)
  @join__type(graph: TENANT_SERVICE)

type FeedAdPerformance
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  impressionsCount: Int!
  clicksCount: Int!
  leadsCount: Int!
}

"""The AI generated blog post."""
type FeedBlogPost
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  postId: ID!
  title: String!
  postStatus: String!
  publishedAt: DateTime
  scheduledAt: DateTime

  """The id of the image to be used as a thumbnail."""
  mediaId: String
}

input FeedFilters
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  timestampStart: String
  timestampEnd: String
}

type Rank 
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  original: Int
  current: Int
}

type FeedGroupMetadata
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  ranking: String @deprecated(reason: "Use \"rank\" field instead")
  rank: Rank
}

type FeedItem
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  """
  Timestamp of the feed item. If null or a date in the future, consider it to be an "upcoming" item.
  """
  timestamp: DateTime

  """The card type used to the determine the UI element to render."""
  itemType: ItemType!
  content: Content!
}

type FeedLeads
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  leads: [LeadDto!]!
}

type FeedPage
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  name: String!
}

type FeedRecommendation
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  type: RecommendationType!
}

"""A group of recommended SEO optimizations."""
type FeedRecommendationGroup
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  appliedAt: DateTime
  scheduledToBePublishedAt: DateTime
  metadata: FeedGroupMetadata!
  recommendations: [FeedRecommendation!]!
  page: FeedPage!
}

type Group
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  companyId: ID!
  keywordId: ID
  title: String!
  description: String!
  metadata: GroupMetadata!
  surfacedAt: DateTime
  scheduledToBeSurfacedAt: DateTime
  appliedAt: DateTime
  scheduledToBePublishedAt: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  deletedAt: DateTime
  recommendations: [Recommendation!]!
  keyword: Keyword
  groupScheduledActions: [GroupScheduledAction!]
  company: Company
}

type GroupMetadata
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  keyword: String!
  ranking: String @deprecated(reason: "Use \"rank\" field instead")
  rank: Rank
}

type GroupScheduledAction
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  groupId: ID!
  scheduledActionId: ID!
  createdAt: DateTime!
  updatedAt: DateTime!
  group: Group!
  scheduledAction: ScheduledAction!

  """Company ID from the related group"""
  companyId: ID!
}

type HomepageFeed
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  items: [FeedItem!]!
}

enum ItemType
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  BLOG_POST @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  SEO_RECOMMENDATION @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  LEAD_HANDOFF @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  AD_PERFORMANCE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}

scalar join__FieldSet

enum join__Graph {
  CLIENT_MARKETING_SERVICE @join__graph(name: "client_marketing_service", url: "http://local.luxurycoders.com:3000/graphql")
  TENANT_SERVICE @join__graph(name: "tenant_service", url: "https://tenant-service.luxurycoders.com/graphql")
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON
  @join__type(graph: TENANT_SERVICE)

"""
The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSONObject
  @join__type(graph: CLIENT_MARKETING_SERVICE)

type Keyword
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  keyword: String!
  metadata: JSONObject!
  createdAt: DateTime!
  updatedAt: DateTime!
  groups: [Group!]
  pageKeywords: [PageKeyword!]
  pageKeywordHistory: [PageKeywordHistory!]
}

type LeadDto
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  firstName: String!
  lastName: String!
  handedOffAt: String!
}

scalar link__Import

enum link__Purpose {
  """
  `SECURITY` features provide metadata necessary to securely resolve fields.
  """
  SECURITY

  """
  `EXECUTION` features provide metadata necessary for operation execution.
  """
  EXECUTION
}

type Mutation
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  """Create a new recommendation"""
  createRecommendation(input: CreateRecommendationInput!): Recommendation!

  """Create owed actions"""
  createOwedActions(companyId: ID!, workflowType: WorkflowType!, generationPayload: JSONObject): ScheduledAction!

  """Create a new scheduled action"""
  createScheduledAction(input: CreateScheduledActionInput!): ScheduledAction!

  """Update an existing scheduled action"""
  updateScheduledAction(id: String!, input: UpdateScheduledActionInput!): ScheduledAction!

  """Create a new keyword"""
  createKeyword(input: CreateKeywordDto!): Keyword!

  """Update an existing keyword"""
  updateKeyword(id: ID!, input: UpdateKeywordDto!): Keyword!

  """Create or update a page keyword record"""
  upsertPageKeyword(input: UpsertPageKeywordInput!): Keyword!
}

type PageKeyword
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  pageId: ID!
  keywordId: ID!
  originalRank: Float
  currentRank: Float
  rankCheckedAt: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  keyword: Keyword!
  page: ScrapedPage!

  """Company ID from the related page"""
  companyId: ID!
}

type PageKeywordHistory
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  pageId: ID!
  keywordId: ID!
  changeNote: String
  updatedBy: ID
  assignedAt: DateTime
  removedAt: DateTime
  createdAt: DateTime!
  page: ScrapedPage!
  keyword: Keyword!
}

type Query
  @join__type(graph: CLIENT_MARKETING_SERVICE)
  @join__type(graph: TENANT_SERVICE)
{
  surfacedGroupsByCompany(companyId: ID!): [Group!]! @join__field(graph: CLIENT_MARKETING_SERVICE)
  groups(companyId: ID): [Group!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get all recommendations"""
  recommendations(companyId: ID): [Recommendation!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a recommendation by ID"""
  recommendation(id: ID!): Recommendation! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get scheduled actions with DRAFT_PENDING status"""
  draftPendingActions: [ScheduledAction!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get upcoming scheduled actions"""
  upcomingActions(companyId: ID!, workflowType: WorkflowType!): [ScheduledAction!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a scheduled action by ID"""
  scheduledAction(id: ID!): ScheduledAction @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get scheduled actions with SURFACED status ready for publishing"""
  surfacedActions(scheduledToBePublishedAt: DateTime!): [ScheduledAction!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get all pages"""
  pages(companyId: ID): [ScrapedPage!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a page by ID"""
  page(id: ID!): ScrapedPage! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get all keywords"""
  keywords: [Keyword!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a keyword by ID"""
  keyword(id: ID!): Keyword! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get all page keywords"""
  pageKeywords(companyId: ID): [PageKeyword!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a page-keyword by ID"""
  pageKeyword(id: ID!): PageKeyword! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get all group scheduled actions"""
  groupScheduledActions(companyId: ID): [GroupScheduledAction!]! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Get a group scheduled action by ID"""
  groupScheduledAction(id: ID!): GroupScheduledAction! @join__field(graph: CLIENT_MARKETING_SERVICE)

  """Fetches the homepage feed."""
  homepageFeed(companyId: ID!, filters: FeedFilters): HomepageFeed! @join__field(graph: CLIENT_MARKETING_SERVICE)
  company(displayId: String!): Company! @join__field(graph: TENANT_SERVICE)
  companies(limit: Int, offset: Int, search: String, sortBy: [String!], sortDir: [SortDirection!], includeDeleted: Boolean, displayId: [ID!], name: String, email: String, leadEmail: String, type: String, currentBusinessSize: String, currentTargetMarket: String, globalNetwork: Boolean, accountManagerId: String, accountManagerIdNull: Boolean, mlsSearchUserId: String, hasMlsAddon: Boolean, onboardingComplete: Boolean, brokerageId: String, demoAccount: Boolean, accessibilityWidget: Boolean, salesforceTenantId: String, salesforceAccountId: String, outboundSmsNumber: String): CompaniesPage! @join__field(graph: TENANT_SERVICE)
}

type Recommendation
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  scrapeId: ID
  groupId: ID!
  type: RecommendationType!
  currentValue: String
  recommendationValue: String!
  reasoning: String!
  status: RecommendationStatus!
  rejectionReason: String
  metadata: JSONObject!
  createdAt: DateTime!
  statusUpdatedAt: DateTime!
  updatedAt: DateTime!
  page: ScrapedPage
  companyId: ID
}

enum RecommendationStatus
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  PENDING @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  APPLIED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  APPROVED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  REJECTED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}

enum RecommendationType
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  META_TITLE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  META_DESCRIPTION @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  MAIN_HEADING @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}

type Role
  @join__type(graph: TENANT_SERVICE)
{
  displayId: String!
  name: String!
}

type ScheduledAction
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  companyId: ID!
  workflowType: WorkflowType!
  status: ScheduledActionStatus!
  createdAt: DateTime!
  updatedAt: DateTime!
  scheduledToBeSurfacedAt: DateTime
  surfacedAt: DateTime
  scheduledToBePublishedAt: DateTime
  publishedAt: DateTime
  contentPayload: JSONObject!
  generationPayload: JSONObject!
  failureReason: JSONObject!
  lockedAt: DateTime
  executionName: String
  executionArn: String
  groupScheduledActions: [GroupScheduledAction!]
}

enum ScheduledActionStatus
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  DRAFT_PENDING @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  HUMAN_QA_PENDING @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  SURFACING_PENDING @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  SURFACED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  PUBLISHED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  FAILED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  INVALIDATED @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}

type Scrape
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  companyId: ID!
  pageId: ID!
  rawHtml: String!
  markdown: String!
  currentScrapedValues: JSONObject
  mediaId: ID
  scrapedAt: DateTime!
  createdAt: DateTime!
  updatedAt: DateTime!
  page: ScrapedPage!
  recommendations: [Recommendation!]
}

type ScrapedPage
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  id: ID!
  companyId: ID!
  url: String!
  pageName: String!
  pageType: ScrapedPageType!
  metadata: JSONObject!
  createdAt: DateTime!
  updatedAt: DateTime!
  deletedAt: DateTime
  recommendations: [Recommendation!]
  scrapes: [Scrape!]
  pageKeywordHistory: [PageKeywordHistory!]
  pageKeywords: [PageKeyword!]
}

enum ScrapedPageType
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  HOMEPAGE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  HOME_VALUATION @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  MORTGAGE_CALCULATOR @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  BUYERS_GUIDE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  SELLERS_GUIDE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  NEIGHBORHOOD_GUIDE @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  AGENT_BIO @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  BLOG @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}

enum SortDirection
  @join__type(graph: TENANT_SERVICE)
{
  ASC @join__enumValue(graph: TENANT_SERVICE)
  DESC @join__enumValue(graph: TENANT_SERVICE)
}

input UpdateKeywordDto
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  keyword: String
  metadata: JSONObject
}

input UpdateScheduledActionInput
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  status: ScheduledActionStatus
  scheduledToBePublishedAt: DateTime
  contentPayload: JSONObject
  generationPayload: JSONObject
  failureReason: JSONObject
  executionName: String
  executionArn: String
  scheduledToBeSurfacedAt: DateTime
}

input UpsertPageKeywordInput
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  pageId: String!
  keyword: String!
}

type User
  @join__type(graph: TENANT_SERVICE)
{
  displayId: String!
  firstName: String
  lastName: String
  email: String!
  avatar: String
  teamLead: Boolean!
  externalAuthId: String
  facebookUserId: String
  lastLogin: DateTime
  notificationsLastSeen: DateTime
  leadEmail: String
  phoneNumber: String
  type: String!
  companies: [UserCompany!]
  createdAt: DateTime!
  updatedAt: DateTime!
  authMethods: [String!]
}

type UserCompany
  @join__type(graph: TENANT_SERVICE)
{
  userId: String!
  companyId: String!
  roleId: String!
  agentId: String
  role: Role!
  acceptedInvitation: Boolean!
  defaultCompany: Boolean!
  company: Company
  lastViewedLeads: DateTime!
}

enum WorkflowType
  @join__type(graph: CLIENT_MARKETING_SERVICE)
{
  SEO @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
  BLOG @join__enumValue(graph: CLIENT_MARKETING_SERVICE)
}