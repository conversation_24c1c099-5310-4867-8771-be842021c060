{"name": "local-rover", "version": "1.2.2", "private": true, "scripts": {"start": "concurrently \"rover dev --elv2-license accept --supergraph-config supergraph.yaml --router-config router.yaml\" \"node server.js\"", "generate": "rover supergraph compose --elv2-license accept --config ./supergraph.yaml --output ./schema.graphql"}, "devDependencies": {"@apollo/rover": "^0.33.0", "concurrently": "^9.1.2", "express": "^4.18.2", "http-proxy-middleware": "^3.0.5"}}