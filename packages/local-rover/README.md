# Local Rover

This package is designed to facilitate local development with federated GraphQL temporarily. It provides a convenient way to work with Apollo Federation locally while developing and testing changes to individual subgraphs.

## Purpose

The local-rover package allows developers to:

- Run a local instance of Apollo Router for development
- Override staging services/subgraphs with local versions
- Test federated GraphQL queries locally before deploying

## Usage

To use this package for local development:

1. Set up SSL certificates for local development:

    1. Install [mkcert for your OS](https://github.com/FiloSottile/mkcert#installation)
    2. Run `mkcert -install` to create a new local certificate authority (not needed if previously installed)
    3. With this repo as your current working directory, run `mkcert local.luxurycoders.com` to issue/create a certificate for "local.luxurycoders.com".

2. Start the development server:

```
pnpm run start
```

This will start both the Apollo Router and the HTTPS proxy server.

3. Run whatever app your subgraph is in (e.g. for `client-marketing-service` run `pnpm start:dev`)

- Any changes made to the subgraph will be automatically reflected in the supergraph

4. Navigate to <http://local.luxurycoders.com:4001/graphql>

!! You must be on the VPN if you are accessing any staging service subgraphs in the supergraph.yaml file !!

### Authentication

To authenticate for all subgraphs, login to the staging app [https://app.luxurycoders.com](https://app.luxurycoders.com) and go to the network tab. Look for a graphql request to `https://graphql.luxurycoders.com/graphql` and NOT to api gateway aka `https://gw.luxurycoders.com/graphql`. Copy the bearer token and use it as a header in the apollo router. 

## Configuration

Similar to modifying a BFF's local.json, you must modify the `supergraph.yaml` to override staging services/subgraphs with a local version.

Example of overriding a subgraph in `supergraph.yaml`:

```yaml
subgraphs:
  your_service:
    routing_url: &service_url http://localhost:3000/graphql
    schema:
      subgraph_url: *service_url
```

If you every want to just generate the supergraph schema, you can run:

```
pnpm run generate
```

This will create a `schema.graphql` file based on your `supergraph.yaml` configuration.

Note: This is unnecessary for local development, but can be useful for debugging or when you need to verify the schema.

## Known Issues

Rover TLS doesn't work at this version, so we use a custom `server.js` to create an HTTPS proxy. This server.js file sets up an HTTPS server using local certificates (generated with mkcert as described in the Usage section) and proxies requests to Rover running on 127.0.0.1:4000.

The proxy server handles the TLS termination and forwards requests to the Rover instance, allowing secure connections to work properly during local development.
