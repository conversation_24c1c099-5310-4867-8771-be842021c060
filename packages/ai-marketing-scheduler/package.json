{"name": "ai-marketing-scheduler", "version": "0.69.2", "description": "AI Scheduler (Posts/SEO/Etc)", "author": "<PERSON><PERSON>ury Presence Dev Team", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "**************:luxurypresence/dashboard.git", "directory": "ai-marketing-scheduler"}, "scripts": {"build": "tsc", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --check \"src/**/*.ts\"", "format:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "cross-env LOAD_TEST_CONFIG='true' jest --runInBand --verbose=true --forceExit  --detectOpenHandles", "test:cov": "cross-env LOAD_TEST_CONFIG='true' jest --coverage --runInBand --forceExit --detectOpenHandles", "deploy": "serverless deploy --verbose --force && serverless doctor", "run:lambda": "ts-node -r dotenv/config -r tsconfig-paths/register src/lambdas/run.ts", "sync:datadog-monitors": "ts-node -r dotenv/config -r tsconfig-paths/register scripts/setup-datadog-monitors.ts"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.10.0", "@aws-sdk/client-lambda": "^3.687.0", "@aws-sdk/client-s3": "^3.481.0", "@aws-sdk/client-secrets-manager": "^3.859.0", "@aws-sdk/client-sfn": "^3.465.0", "@aws-sdk/client-ssm": "^3.859.0", "@langchain/core": "0.3.42", "@langchain/openai": "^0.4.4", "@launchdarkly/node-server-sdk": "^9.10.0", "@mendable/firecrawl-js": "^1.25.1", "aws-lambda": "^1.0.7", "cross-env": "^7.0.3", "datadog-lambda-js": "^12.127.0", "dd-trace": "^5.0.0", "dotenv": "^16.5.0", "graphql": "^16.8.1", "graphql-request": "6.1.0", "html-entities": "^2.6.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.19", "langfuse": "^3.37.0", "langfuse-langchain": "^3.37.0", "p-limit": "^3.1.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "snowflake-sdk": "^2.1.0", "source-map-support": "^0.5.21", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "9.16.0", "@types/aws-lambda": "^8.10.136", "@types/babel__core": "^7.20.5", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/mdast": "^4.0.4", "@types/node": "^22.9.0", "@types/unist": "^3.0.3", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "babel-jest": "30.0.0-beta.3", "esbuild": "^0.25.5", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "globals": "^15.14.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.2.5", "serverless": "^3.40.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.54.5", "serverless-offline": "^14.3.4", "serverless-plugin-datadog": "^5.101.0", "serverless-step-functions": "^3.21.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.3.3", "typescript-eslint": "^8.20.0"}, "jest": {"transform": {"^.+\\.[tj]sx?$": "babel-jest"}, "moduleNameMapper": {"^src/(.*)": "<rootDir>/src/$1"}, "modulePaths": ["<rootDir>/src"], "modulePathIgnorePatterns": ["node_modules", "dist", ".history"], "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["src/config/*", "mock", "testEvents", "factories", "run.ts"], "moduleDirectories": ["node_modules", "src"], "testEnvironment": "node", "roots": ["<rootDir>/src"], "transformIgnorePatterns": ["/node_modules/.pnpm/(?!remark-parse|remark-stringify|unified|unist-util-visit|mdast-util-.*|micromark.*|decode-named-character-reference|character-entities|@mendable|langchain|@langchain)@"]}, "keywords": []}