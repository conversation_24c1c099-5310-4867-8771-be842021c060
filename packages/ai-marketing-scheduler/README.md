# AI Marketing Scheduler

A serverless AWS Lambda-based service that orchestrates AI-powered marketing content generation and SEO optimization workflows using AWS Step Functions.

## Overview

The AI Marketing Scheduler is a critical component of the SEO automation platform that:

- **Orchestrates SEO content workflows** including keyword extraction, recommendation generation, and content publishing
- **Manages scheduled actions** through their entire lifecycle from draft creation to publication
- **Provides a unified service layer** with configurable mock/real implementations for testing
- **Handles bulk processing** of tenant entitlements and SEO actions at scale
- **Integrates with external services** including DataForSEO, Firecrawl, and STAR API for content optimization

## Architecture

### Lambda Functions

#### Core Workflow Lambdas

##### Draft Action Consumer Daemon

- **Purpose**: Processes scheduled actions ready for content generation
- **Trigger**: EventBridge schedule or manual invocation
- **Workflow**:
  1. Retrieves draft-pending scheduled actions
  2. Processes actions in configurable chunks
  3. Starts SEO Draft Step Function execution
  4. Updates action status based on results

##### Publisher

- **Purpose**: Publishes approved SEO recommendations to client websites
- **Trigger**: Step Function or direct invocation
- **Workflow**:
  1. Validates action is in SURFACED status
  2. Fetches pending recommendations from content payload
  3. Updates website content via STAR API
  4. Marks successful recommendations as APPLIED
  5. Updates action status to PUBLISHED or FAILED

##### UrlSelector

- **Purpose**: Selects optimal URL for SEO analysis from website mapping
- **Trigger**: SEO Draft Step Function
- **Workflow**:
  1. Maps website URLs using Firecrawl
  2. Filters URLs by recognized page types (homepage, blog, etc.)
  3. Enriches URLs with Snowflake analytics data (impressions, search rankings)
  4. Filters out URLs with rank 1 (already well-performing)
  5. Selects URL with highest impressions or random selection for unmeasured URLs
  6. Updates action with selected URL

##### UrlScraper

- **Purpose**: Scrapes the selected URL content for SEO analysis
- **Trigger**: SEO Draft Step Function (after UrlSelector)
- **Workflow**:
  1. Receives selected URL from previous step
  2. Scrapes page content and metadata using Firecrawl
  3. Extracts main heading using JavaScript execution
  4. Uploads screenshots to S3
  5. Updates action with scraper results

##### Keyword Extractor

- **Purpose**: Extracts relevant SEO keywords from page content
- **Trigger**: SEO Draft Step Function
- **Uses**: OpenAI GPT-4 for intelligent keyword extraction

##### Recommendation Generator

- **Purpose**: Generates SEO recommendations for content optimization
- **Trigger**: SEO Draft Step Function
- **Generates**: Meta title, meta description, and H1 recommendations

##### Store SEO Draft

- **Purpose**: Persists generated SEO content and recommendations
- **Trigger**: SEO Draft Step Function
- **Stores**: Scraper results, keywords, and recommendations in database

#### Supporting Lambdas

##### Surfacer Daemon

- **Purpose**: Surfaces completed actions for human review
- **Trigger**: EventBridge schedule (Tuesdays)
- **Updates**: Action status from HUMAN_QA_PENDING to SURFACED

##### Rank Queuer Daemon

- **Purpose**: Queues keyword ranking checks via DataForSEO
- **Trigger**: EventBridge schedule (Tuesdays)
- **Processes**: Published actions to track SEO performance

##### Emailer Daemon

- **Purpose**: Sends weekly digest emails to clients
- **Trigger**: EventBridge schedule (Wednesdays)
- **Includes**: SEO recommendations, performance metrics, and action summaries

### Step Functions

#### SEO Draft Workflow

Orchestrates the complete SEO content generation pipeline:

```
Start → UrlSelector → UrlScraper → Keyword Extractor → Recommendation Generator → Store SEO Draft → End
```

Each step passes enriched data to the next, building a complete SEO optimization package.

#### Entitlements Workflow

Processes tenant entitlements to create scheduled actions:

```
Start → Entitlement Fetcher → Draft Action Scheduler → End
```

### Service Layer Architecture

The application uses a **Factory Pattern** with **Dependency Injection**:

```typescript
// Unified Service Factory provides automatic mock/real switching
import { serviceFactory } from './factories';
import { createContextLogger } from './logger/contextLogger';

// Create a context logger with initial context
const logger = createContextLogger('my-lambda', context, {
  actionId: event.actionId,
  companyId: event.companyId
});

// Services automatically get component loggers via factory
const scheduledActionService = serviceFactory.createScheduledActionService(logger);
const starApiService = serviceFactory.createStarApiService(logger);
```

#### Context-Aware Logging

The new logging system provides architectural component tracking:

```typescript
// Component loggers identify WHERE in the architecture
const serviceLogger = logger.createComponentLogger({
  serviceName: 'ScheduledActionService'
});

// Child loggers add business context
const operationLogger = logger.createChild({
  operation: 'fetchPendingRecommendations',
  recommendationCount: 5
});
```

See [Context Logger Pattern Documentation](docs/context-logger-pattern.md) and [Context Logger Examples](docs/contextLogger.examples.md) for details.

#### Services

- **ScheduledActionService**: Manages scheduled action lifecycle and status transitions
- **TenantService**: Handles tenant entitlements and company data
- **StarApiService**: Orchestrates website content updates via STAR API
- **EntitlementService**: Processes and validates tenant entitlements
- **PromptService**: Manages AI prompts for content generation

#### Clients

- **SeoApiGatewayClient**: GraphQL client for SEO Automation API
- **TenantClient**: HTTP client for tenant service
- **StarApiClient**: REST client for STAR API content updates
- **DataForSeoClient**: Client for keyword ranking checks
- **FirecrawlClient**: Web scraping and content extraction
- **S3Client**: AWS S3 operations for screenshots and data storage

## Configuration

### Environment Variables

#### Core Configuration

| Variable | Description | Required |
|----------|-------------|----------|
| `ENVIRONMENT` | Deployment environment (development/staging/production) | Yes |
| `M2M_SUPER_API_KEY` | Machine-to-machine API key for service authentication | Yes |
| `API_GATEWAY_KEY` | API Gateway authentication key | Yes |
| `API_GATEWAY_URL` | Base URL for API Gateway | Yes |
| `COSMO_GQL_URL` | GraphQL endpoint URL | Yes |

#### Service URLs

| Variable | Description | Required |
|----------|-------------|----------|
| `TENANT_SERVICE_URL` | Base URL for tenant service API | Yes |
| `STAR_API_URL` | Base URL for STAR API (content updates) | Yes |
| `DATAFORSEO_BASE_URL` | DataForSEO API base URL | Yes |
| `DATAFORSEO_POSTBACK_URL` | Webhook URL for DataForSEO callbacks | Yes |

#### External API Keys

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API key for content generation | Yes |
| `FIRECRAWL_API_KEY` | Firecrawl API key for web scraping | Yes |
| `DATAFORSEO_LOGIN` | DataForSEO account login | Yes |
| `DATAFORSEO_PASSWORD` | DataForSEO account password | Yes |

#### AWS Resources

| Variable | Description | Required |
|----------|-------------|----------|
| `SEO_DRAFT_SFN_ARN` | Step Function ARN for SEO workflow | Yes |
| `SCREENSHOTS_R2_BUCKET_NAME` | S3 bucket for screenshots | Yes |
| `API_RESPONSE_S3_BUCKET_NAME` | S3 bucket for API responses | Yes |

#### Processing Configuration

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `ENTITLEMENTS_CHUNK_SIZE` | Batch size for entitlements | No | 10 |
| `ENTITLEMENTS_LIMIT` | Max entitlements to process | No | 100 |
| `PRODUCT_IDS` | Comma-separated product IDs | No | - |

### Mock Configuration

For testing and development, the service supports comprehensive mock implementations:

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MOCK_USE_MOCK_CLIENTS` | Enable mock clients | No | false |
| `USE_MOCK_STAR` | Enable mock STAR API Client | No | false |

```typescript
// In your config or environment
export const config = {
  mock: {
    useMockClients: true,      // Enable all mock clients
    useMockStar: true,          // Enable only STAR API mocking
    actionIdsToFail: ['fail-1'] // Configure specific failures
  }
};
```

Mock mode is useful for:

- Local development without external dependencies
- Testing in AWS sandbox environments behind VPCs
- Predictable test scenarios with controlled failures
- CI/CD pipeline testing

## Development

### Prerequisites

- **Node.js 22+**
- **pnpm** package manager
- **AWS CLI** configured with appropriate credentials
- **Serverless Framework** for deployment
- **Docker** (optional, for local AWS services)

### Installation

```bash
# Install dependencies
pnpm install

# Build the project
pnpm build
```

### Environment Setup

1. Copy the configuration template:

```bash
cp .env.example .env
```

2. Configure your `.env` file with required variables (see Environment Variables section)

### Development Commands

```bash
# Type checking
pnpm build

# Linting
pnpm lint
pnpm lint:fix

# Testing
pnpm test                  # Run all tests
pnpm test:cov             # Generate coverage report

# Local development
pnpm run:lambda           # Execute lambda locally (configure in run.ts)


```

### Testing

The project includes comprehensive test coverage with Jest and centralized test utilities:

```bash
# Run all tests
pnpm test

# Run tests with coverage
pnpm test:cov

```

#### Centralized Test Utilities

All tests use centralized mock utilities from `src/utils/testUtils.ts`:

```typescript
import {
  createMockContextLogger,
  createMockScheduledActions,
  createMockSeoApiGatewayClient,
  createMockStarApiService,
  // ... more factory functions
} from '../utils/testUtils';

// Example test setup
const mockLogger = createMockContextLogger();
const mockActions = createMockScheduledActions(2, {
  status: Status.DRAFT_PENDING,
  workflowType: WorkflowType.SEO
});
```

**Key Features:**

- **Context-Aware Mock Logger**: Includes `createChild` and `createComponentLogger` methods
- **Consistent Mock Data**: Realistic test data that matches production schemas
- **Type-Safe Mocks**: All mocks implement proper TypeScript interfaces
- **Easy Overrides**: Factory functions accept partial overrides for test scenarios

#### Mock Testing System

The centralized mock system provides:

- **MockSeoApiGatewayClient**: Simulates GraphQL API responses with realistic data
- **MockTenantClient**: Provides mock tenant entitlements and company data
- **MockSFNClient**: Simulates Step Function executions with configurable failures
- **MockScheduledActionService**: Complete service layer mocking
- **MockTenantService**: Tenant service operations mocking
- **MockConfig**: Consistent configuration object creation

#### Test Configuration Pattern

```typescript
describe('MyService', () => {
  let mockLogger: Logger;
  let mockService: jest.Mocked<IMyService>;

  beforeEach(() => {
    // Centralized test setup
    const { mockLogger: logger } = setupTestMocks();
    mockLogger = logger;

    // Mock configuration setup
    (getConfig as jest.Mock).mockReturnValue(createMockConfig());

    // Service mocking using centralized factories
    mockService = createMockMyService();
  });

  it('should handle test scenario', async () => {
    // Use centralized mock data factories
    const mockData = createMockScheduledActions(2, [
      { id: 'action-1', status: Status.DRAFT_PENDING },
      { id: 'action-2', workflowType: WorkflowType.SEO }
    ]);

    mockService.getData.mockResolvedValue(mockData);

    // Test execution...
  });
});
```

#### Benefits of Centralized Testing

- **Reduced Duplication**: No repeated mock setup across test files
- **Consistent Behavior**: All tests use the same mock implementations
- **Easy Maintenance**: Mock changes in one place affect all tests
- **Better Test Readability**: Focus on test logic, not mock setup
- **Type Safety**: Factory functions ensure correct mock interfaces

### Monitoring and Observability

#### Datadog Integration

The scheduler includes comprehensive monitoring through Datadog with:

##### Custom Metrics
- Distribution metrics using native `datadog-lambda-js` library
- Tracks entitlements, actions, processing times, and errors
- Controlled cardinality without company-specific tags
- Enable with `DD_METRICS_ENABLED=true` environment variable

##### Enhanced Logging
- Automatic trace correlation via X-Ray integration
- Service identification and versioning
- Custom attributes for debugging (prefixed with `dd.custom.*`)
- Structured context logging with component tracking

See [docs/datadog-logging.md](docs/datadog-logging.md) for detailed configuration and usage.

### Local Development

#### Running Lambda Functions Locally

```bash
# Execute lambda function locally
# import whatever lambda handler you want to run in run.ts
pnpm run:lambda
```

### Mock vs Real Services

The application supports seamless switching between mock and real implementations:

#### Mock Mode (Development/Testing)

If the application is deployed in the sandbox environment, it cannot access the real APIs because they are behind a VPC.
There is the option to use mock clients in the sandbox environment which will simulate the real APIs responses with mock data.

```typescript
// config.yaml
mock:
  useMockClients: true
  actionIdsToFail: ["action-3"]
```

- Uses in-memory mock data
- Predictable responses for testing
- Configurable failure scenarios
- No external API dependencies
