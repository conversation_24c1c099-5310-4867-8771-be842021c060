service: ai-marketing-scheduler

useDotenv: true

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-step-functions
  - serverless-plugin-datadog
  - serverless-offline

custom:
  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    exclude: ['aws-sdk']
    target: 'node22'
    define: { 'require.resolve': undefined }
    platform: 'node'
    concurrency: 10
    tsconfig: 'tsconfig.json'
    format: 'cjs'
    mainFields: ['module', 'main']
  serverless-offline:
    httpPort: 3001
    lambdaPort: 3002
  datadog:
    site: datadoghq.com
    apiKey: ${env:DD_API_KEY}
    enableDDTracing: true
    enableDDLogs: true
    enableXrayTracing: false
    enableSourceCodeIntegration: true
    captureLambdaPayload: true
    propagateUpstreamTrace: true
    addLayers: true
    enableColdStartTracing: true
    enableDDMonitoring: true
    env: ${self:provider.stage}
    service: ai-marketing-scheduler
    version: ${env:GIT_SHA, 'latest'}
    # Global tags applied to ALL Datadog metrics and traces
    tags: "team:client-marketing"

package:
  individually: true

provider:
  name: aws
  stage: ${env:ENVIRONMENT, 'development'}
  region: us-east-1
  runtime: nodejs22.x
  versionFunctions: false
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT, 'development'}
    SLACK_NOTIFICATIONS_ENABLED: ${env:SLACK_NOTIFICATIONS_ENABLED, 'false'}
    SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL, ''}
    API_GATEWAY_SUPER_USER_COMPANY_ID: ${env:API_GATEWAY_SUPER_USER_COMPANY_ID, ''}
    API_GATEWAY_KEY: ${env:API_GATEWAY_KEY, ''}
    LAUNCHDARKLY_KEY: ${env:LAUNCHDARKLY_KEY, ''}
    TENANT_SERVICE_URL: ${env:TENANT_SERVICE_URL, ''}
    CMS_SERVICE_URL: ${env:CMS_SERVICE_URL, ''}
    API_GATEWAY_URL: ${env:API_GATEWAY_URL, ''}
    COSMO_GQL_URL: ${env:COSMO_GQL_URL, ''}
    LANGFUSE_SECRET_KEY: ${env:LANGFUSE_SECRET_KEY, ''}
    LANGFUSE_PUBLIC_KEY: ${env:LANGFUSE_PUBLIC_KEY, ''}
    ENTITLEMENTS_CHUNK_SIZE: ${env:ENTITLEMENTS_CHUNK_SIZE, 10}
    ENTITLEMENTS_LIMIT: ${env:ENTITLEMENTS_LIMIT, 100}
    PRODUCT_IDS: ${env:PRODUCT_IDS, 'product-123'}
    FIRECRAWL_API_KEY: ${env:FIRECRAWL_API_KEY, ''}
    SCREENSHOTS_R2_ACCESS_KEY_ID: ${env:SCREENSHOTS_R2_ACCESS_KEY_ID, ''}
    SCREENSHOTS_R2_SECRET_ACCESS_KEY: ${env:SCREENSHOTS_R2_SECRET_ACCESS_KEY, ''}
    SCREENSHOTS_R2_ENDPOINT: ${env:SCREENSHOTS_R2_ENDPOINT, ''}
    SCREENSHOTS_R2_BUCKET_NAME: lp-seo-page-screenshots-${env:ENVIRONMENT, 'staging'}
    API_RESPONSE_S3_BUCKET_NAME: lp-client-marketing-api-responses-${env:ENVIRONMENT, 'staging'}
    M2M_SUPER_API_KEY: ${env:M2M_SUPER_API_KEY, ''}
    OPENAI_API_KEY: ${env:OPENAI_API_KEY, ''}
    DATAFORSEO_LOGIN: ${env:DATAFORSEO_LOGIN, ''}
    DATAFORSEO_PASSWORD: ${env:DATAFORSEO_PASSWORD, ''}
    DATAFORSEO_POSTBACK_URL: ${env:DATAFORSEO_POSTBACK_URL, ''}
    DATAFORSEO_BASE_URL: ${env:DATAFORSEO_BASE_URL, 'https://api.dataforseo.com'}
    STAR_API_URL: ${env:STAR_API_URL, ''}
  deploymentBucket: ${file(./serverless/conditionalDeployBucket.js)}
  stackTags:
    env: ${env:ENVIRONMENT, 'development'}
    app: ai-marketing-scheduler
    platform_version: v3
    team: client-marketing
  tags:
    env: ${env:ENVIRONMENT, 'development'}
    app: ai-marketing-scheduler
    platform_version: v3
    team: client-marketing
    Project: seo-automation
    Service: ai-marketing-scheduler
    dd_trace_enabled: true
    dd_service: ai-marketing-scheduler
    dd_env: ${env:ENVIRONMENT, 'development'}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - lambda:InvokeFunction
        - states:StartExecution
        - states:DescribeExecution
      Resource:
        - '*'
    - Effect: Allow
      Action:
        - s3:PutObject
        - s3:GetObject
        - s3:ListBucket
      Resource:
        - arn:aws:s3:::${self:provider.environment.API_RESPONSE_S3_BUCKET_NAME}/*
    - Effect: Allow
      Action:
        - ssm:GetParameter
      Resource:
        - arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/${self:provider.stage}/ai-marketing-scheduler/snowflake/account
        - arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/${self:provider.stage}/ai-marketing-scheduler/snowflake/user
        - arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/${self:provider.stage}/ai-marketing-scheduler/snowflake/database
        - arn:aws:ssm:${self:provider.region}:${aws:accountId}:parameter/${self:provider.stage}/ai-marketing-scheduler/snowflake/private-key
  vpc: ${file(./serverless/conditionalVPC.js)}

functions:
  draft-action-consumer-daemon:
    name: ${self:service}-draft-action-consumer-daemon
    handler: src/lambdas/DraftActionConsumerDaemon.handler
    memorySize: 512
    timeout: 600
    environment:
      SEO_DRAFT_SFN_ARN:
        Fn::Sub: arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:stepFunctions.stateMachines.seoKeywordsStateMachine.name}
      BLOG_DRAFT_SFN_ARN: ${env:BLOG_DRAFT_SFN_ARN, ''}
    events:
      - schedule:
          rate: cron(0 22 ? * MON *)
          enabled: ${env:SCHEDULER_STATE, 'false'}
  draft-action-scheduler:
    name: ${self:service}-draft-action-scheduler
    handler: src/lambdas/DraftActionSchedulerDaemon.handler
    memorySize: 2048
    timeout: 360
  entitlement-fetcher:
    name: ${self:service}-entitlement-fetcher
    handler: src/lambdas/EntitlementFetcher.handler
    memorySize: 512
    timeout: 360
  store-draft:
    name: ${self:service}-store-draft
    handler: src/lambdas/StoreSEODraft.handler
    memorySize: 512
    timeout: 360
  catch-failure:
    name: ${self:service}-catch-failure
    handler: src/lambdas/CatchFailure.handler
    memorySize: 128
    timeout: 30
  url-selector:
    name: ${self:service}-url-selector
    handler: src/lambdas/UrlSelector.handler
    memorySize: 2048
    timeout: 600
  url-scraper:
    name: ${self:service}-url-scraper
    handler: src/lambdas/UrlScraper.handler
    memorySize: 2048
    timeout: 600
  keyword-extractor:
    name: ${self:service}-keyword-extractor
    handler: src/lambdas/KeywordExtractor.handler
    memorySize: 1024
    timeout: 600
  recommendation-generator:
    name: ${self:service}-recommendation-generator
    handler: src/lambdas/RecommendationGenerator.handler
    memorySize: 1024
    timeout: 600
  get-surfaced-actions:
    name: ${self:service}-get-surfaced-actions
    handler: src/lambdas/GetSurfacedActions.handler
    memorySize: 512
    timeout: 180
    disableLogs: true
  publisher:
    name: ${self:service}-publisher
    handler: src/lambdas/Publisher.handler
    memorySize: 512
    timeout: 180
  surfacer-daemon:
    name: ${self:service}-surfacer-daemon
    handler: src/lambdas/SurfacerDaemon.handler
    memorySize: 2048
    timeout: 300
    events:
      - schedule:
          rate: cron(0 17 ? * TUE *)
          enabled: ${env:SURFACER_DAEMON_STATE, 'false'}
  emailer-daemon:
    name: ${self:service}-emailer-daemon
    handler: src/lambdas/EmailerDaemon.handler
    memorySize: 512
    timeout: 600
  rank-webhook:
    name: ${self:service}-rank-webhook
    handler: src/lambdas/RankWebhook.handler
    memorySize: 512
    timeout: 360
    events:
      - http:
          path: /webhooks/dataforseo/rank
          method: post
          cors: true
  rank-queuer-daemon:
    name: ${self:service}-rank-queuer-daemon
    handler: src/lambdas/RankQueuerDaemon.handler
    memorySize: 512
    timeout: 180
  rank-batch-splitter:
    name: ${self:service}-rank-batch-splitter
    handler: src/lambdas/RankBatchSplitter.handler
    memorySize: 512
    timeout: 180
resources:
  Resources:
    StepFunctionsExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: states.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: StepFunctionsExecutionPolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - lambda:InvokeFunction
                  Resource:
                    - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-*

    EntitlementsScheduleRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:service}-entitlements-schedule-rule-${self:provider.stage}
        ScheduleExpression: cron(30 21 ? * MON *)
        State: ${env:ENTITLEMENTS_STATE, 'DISABLED'}
        Targets:
          - Arn: !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-entitlementsStateMachine"
            Id: 'EntitlementsStateMachineTarget'
            RoleArn: !GetAtt EntitlementsStepFunctionInvokeRole.Arn

    EntitlementsStepFunctionInvokeRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-entitlements-scheduler-${self:provider.stage}
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: EntitlementsStepFunctionInvokePolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - states:StartExecution
                  Resource:
                    - !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-entitlementsStateMachine"

    PublisherScheduleRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:service}-publisher-schedule-rule-${self:provider.stage}
        ScheduleExpression: cron(0 17 ? * THU *)
        State: ${env:PUBLISHER_STATE, 'DISABLED'}
        Targets:
          - Arn: !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-publisherStateMachine"
            Id: 'PublisherStateMachineTarget'
            RoleArn: !GetAtt PublisherStepFunctionInvokeRole.Arn

    PublisherStepFunctionInvokeRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-publisher-scheduler-${self:provider.stage}
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: PublisherStepFunctionInvokePolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - states:StartExecution
                  Resource:
                    - !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-publisherStateMachine"

    RankQueueScheduleRule:
      Type: AWS::Events::Rule
      Properties:
        Name: ${self:service}-rank-queue-schedule-rule-${self:provider.stage}
        ScheduleExpression: cron(0 19 ? * TUE *)
        State: ${env:RANK_QUEUER_DAEMON_STATE, 'DISABLED'}
        Targets:
          - Arn: !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-rankQueueStateMachine"
            Id: 'RankQueueStateMachineTarget'
            RoleArn: !GetAtt RankQueueStepFunctionInvokeRole.Arn

    RankQueueStepFunctionInvokeRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-rank-queue-scheduler-${self:provider.stage}
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: RankQueueStepFunctionInvokePolicy
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - states:StartExecution
                  Resource:
                    - !Sub "arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:${self:service}-${self:provider.stage}-rankQueueStateMachine"

stepFunctions:
  validate: true
  stateMachines:
    entitlementsStateMachine:
      name: ${self:service}-${self:provider.stage}-entitlementsStateMachine
      role: !GetAtt StepFunctionsExecutionRole.Arn
      type: STANDARD
      definition:
        StartAt: FetchEntitlements
        States:
          FetchEntitlements:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-entitlement-fetcher'
            Next: ProcessEntitlements
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
          ProcessEntitlements:
            Type: Map
            ItemsPath: $.chunkedEntitlements
            MaxConcurrency: 10
            ResultPath: $.mapResults
            Iterator:
              StartAt: WrapInput
              States:
                WrapInput:
                  Type: Pass
                  Parameters:
                    entitlements.$: $
                  Next: ActionScheduler
                ActionScheduler:
                  Type: Task
                  Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-draft-action-scheduler'
                  Retry:
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: LogEntitlementFailure
                  Next: MapSuccess
                LogEntitlementFailure:
                  Type: Pass
                  Parameters:
                    status: "failed"
                    message: "Failed to process entitlement chunk after retries"
                    entitlements.$: $.entitlements
                    error.$: $.error
                  End: true
                MapSuccess:
                  Type: Pass
                  Result: "Action scheduled successfully"
                  End: true
            Next: Success
          Success:
            Type: Succeed
    seoKeywordsStateMachine:
      name: ${self:service}-${self:provider.stage}-seoKeywordsStateMachine
      role: !GetAtt StepFunctionsExecutionRole.Arn
      type: STANDARD
      definition:
        StartAt: SelectUrl
        States:
          SelectUrl:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-url-selector'
            Next: CheckUrlSelected
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 3
                MaxAttempts: 5
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
          CheckUrlSelected:
            Type: Choice
            Choices:
              - Variable: $.generationPayload.selectedUrl
                IsPresent: true
                Next: ScrapeSelectedUrl
            Default: NoUrlSelectedSuccess
          NoUrlSelectedSuccess:
            Type: Succeed
            Comment: "Successfully completed - no URL was selected due to all URLs having rank 1 or no ranking data"
          ScrapeSelectedUrl:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-url-scraper'
            Next: ExtractKeywords
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 3
                MaxAttempts: 5
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
          ExtractKeywords:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-keyword-extractor'
            Next: GenerateRecommendations
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
          GenerateRecommendations:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-recommendation-generator'
            Next: StoreSEODraft
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
          StoreSEODraft:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-store-draft'
            End: true
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
          CatchFailure:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-catch-failure'
            End: true
    publisherStateMachine:
      name: ${self:service}-${self:provider.stage}-publisherStateMachine
      role: !GetAtt StepFunctionsExecutionRole.Arn
      type: STANDARD
      definition:
        StartAt: GetSurfacedActions
        States:
          GetSurfacedActions:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-get-surfaced-actions'
            Next: ProcessActions
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 3
                MaxAttempts: 5
                BackoffRate: 2
          ProcessActions:
            Type: Map
            MaxConcurrency: 20
            ItemsPath: $.actions
            Iterator:
              StartAt: PublishAction
              States:
                PublishAction:
                  Type: Task
                  Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-publisher'
                  Retry:
                    - ErrorEquals: ['TransientPublisherError']
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2.0
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CatchFailure
                  End: true

                CatchFailure:
                  Type: Task
                  Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-catch-failure'
                  End: true
            End: true
    rankQueueStateMachine:
      name: ${self:service}-${self:provider.stage}-rankQueueStateMachine
      role: !GetAtt StepFunctionsExecutionRole.Arn
      type: STANDARD
      definition:
        StartAt: SplitKeywordBatches
        States:
          SplitKeywordBatches:
            Type: Task
            Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-rank-batch-splitter'
            Next: ProcessKeywordBatches
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            # No Catch - let workflow fail if we can't split batches
          ProcessKeywordBatches:
            Type: Map
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket.$: $.bucketName
                Key.$: States.Format('{}/keywords.json', $.keyPrefix)
            MaxConcurrency: 20
            ItemSelector:
              keyword.$: $$.Map.Item.Value.keyword
              pageId.$: $$.Map.Item.Value.pageId
            ItemProcessor:
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
              StartAt: RankQueuer
              States:
                RankQueuer:
                  Type: Task
                  Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-rank-queuer-daemon'
                  Retry:
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                  End: true
            ResultWriter:
              Resource: arn:aws:states:::s3:putObject
              Parameters:
                Bucket.$: $.bucketName
                Prefix.$: States.Format('{}/results/', $.keyPrefix)
            ToleratedFailurePercentage: 10
            Next: Success
          Success:
            Type: Succeed
