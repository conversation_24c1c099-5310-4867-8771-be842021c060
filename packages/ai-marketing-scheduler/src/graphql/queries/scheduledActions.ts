/**
 * GraphQL queries for scheduled actions
 */

import { SCHEDULED_ACTION_FIELDS } from '../fragments/scheduledAction';

export const GET_DRAFT_PENDING_ACTIONS = `
  ${SCHEDULED_ACTION_FIELDS}
  
  query draftPendingActions {
    draftPendingActions {
      ...ScheduledActionFields
      surfacedAt
      scheduledToBePublishedAt
      publishedAt
    }
  }
`;

export const SCHEDULED_ACTION = `
  ${SCHEDULED_ACTION_FIELDS}
  
  query scheduledAction($id: ID!) {
    scheduledAction(id: $id) {
      ...ScheduledActionFields
      groupScheduledActions {
        id
        groupId
      }
    }
  }
`;

export const GET_SURFACED_ACTIONS = `
  ${SCHEDULED_ACTION_FIELDS}
  
  query surfacedActions($scheduledToBePublishedAt: DateTime!) {
    surfacedActions(scheduledToBePublishedAt: $scheduledToBePublishedAt) {
      ...ScheduledActionFields
      surfacedAt
      scheduledToBePublishedAt
      publishedAt
      contentPayload
    }
  }
`;
