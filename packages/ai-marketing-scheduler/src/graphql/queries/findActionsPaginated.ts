export const FIND_ACTIONS_PAGINATED = `
  query ($page: Int, $limit: Int, $companyId: ID, $workflowType: WorkflowType  , $status: ScheduledActionStatus) {
    findScheduledActionsPaginated(page: $page, limit: $limit, companyId: $companyId, workflowType: $workflowType, status: $status) {
      data {
        id
        companyId
        workflowType
        status
        createdAt
        updatedAt
        scheduledToBeSurfacedAt
        surfacedAt
        scheduledToBePublishedAt
        publishedAt
        contentPayload
        generationPayload
        failureReason
        lockedAt
        executionName
        executionArn
      }
      totalItems
      currentPage
      totalPages
      itemsPerPage
      hasNextPage
      hasPreviousPage
    }
  }
`;
