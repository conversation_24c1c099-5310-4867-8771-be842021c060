import { gql } from 'graphql-request';

export const GET_SURFACED_AND_PUBLISHED_GROUPS_FOR_RANKING_PAGINATED = gql`
  query SurfacedAndPublishedGroupsForRankingPaginated(
    $page: Int!
    $limit: Int!
  ) {
    surfacedAndPublishedGroupsForRankingPaginated(page: $page, limit: $limit) {
      data {
        keyword {
          keyword
        }
        scrapedPage {
          id
          url
        }
      }
      hasMore
      totalCount
      currentPage
      pageSize
    }
  }
`;
