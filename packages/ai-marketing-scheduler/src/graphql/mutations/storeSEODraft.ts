export const STORE_SEO_DRAFT_MUTATION = `
  mutation ($companyId: ID!, $actionId: ID!, $scraperResult: ScrapedPageInput!, $keywords: String!, $recommendations: RecommendationsInput!) {
  storeSEODraft(
    companyId: $companyId
    actionId: $actionId
    scraperResult: $scraperResult
    keywords: $keywords
    recommendations: $recommendations
  ) {
    savedKeyword {
      id
      keyword
    }
    savedRecommendations {
      id
      scrapeId
      groupId
      type
    }
    savedScrape {
      id
      mediaId
      markdown
    }
    savedScrapedPage {
      id
      companyId
      url
    }
    savedPageKeyword {
      id
      keywordId
    }
    savedGroup {
      id
      companyId
      keywordId
    }
  }
}
`;
