/**
 * GraphQL mutations for scheduled actions
 */

export const UPDATE_SCHEDULED_ACTION = `
  mutation UpdateScheduledAction($id: ID!, $status: ScheduledActionStatus, $failureReason: JSONObject, $scheduledToBeSurfacedAt: DateTime, $scheduledToBePublishedAt: DateTime, $generationPayload: JSONObject, $contentPayload: JSONObject, $executionName: String, $executionArn: String, $surfacedAt: DateTime, $publishedAt: DateTime) {
    updateScheduledAction(id: $id, status: $status, failureReason: $failureReason, scheduledToBeSurfacedAt: $scheduledToBeSurfacedAt, scheduledToBePublishedAt: $scheduledToBePublishedAt, generationPayload: $generationPayload, contentPayload: $contentPayload, executionName: $executionName, executionArn: $executionArn, surfacedAt: $surfacedAt, publishedAt: $publishedAt) {
      id
      status
      failureReason
      updatedAt
      scheduledToBeSurfacedAt
      scheduledToBePublishedAt
      generationPayload
      contentPayload
      executionName
      executionArn
      surfacedAt
      publishedAt
    }
  }
`;
