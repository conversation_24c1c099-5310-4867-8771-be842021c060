export const HOMEPAGE_FEED_FIELDS = `fragment FeedBlogPostFields on FeedBlogPost {
  postId
  title
  postStatus
  scheduledAt
  publishedAt
  mediaId
  media {
    thumbnailUrl
    smallUrl
    mediumUrl
  }
}

fragment FeedRecommendationGroupFields on FeedRecommendationGroup {
  id
  publishedAt
  scheduledToBePublishedAt
  keyword
  metadata {
    rank {
      original
      current
    }
  }
  recommendations {
    type
  }
  page {
    name
    type
    url
  }
  media {
    thumbnailUrl
    smallUrl
    mediumUrl
  }
}

fragment FeedLeadsFields on FeedLeads {
  leads {
    id
    firstName
    lastName
    handedOffAt
  }
}

fragment FeedAdPerformanceFields on FeedAdPerformance {
  impressionsCount
  clicksCount
  leadsCount
  impressionsCountFormatted
  clicksCountFormatted
  leadsCountFormatted
  paidLeadsHref
}

fragment FeedItemFields on FeedItem {
  timestamp
  itemType
  content {
    ...FeedBlogPostFields
    ...FeedRecommendationGroupFields
    ...FeedLeadsFields
    ...FeedAdPerformanceFields
    # __typename is useful for unions
    __typename
  }
}

fragment HomepageFeedFields on HomepageFeed {
  items {
    ...FeedItemFields
  }
}`;
