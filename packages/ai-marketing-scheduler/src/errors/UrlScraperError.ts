import { BaseError } from './BaseError';

/**
 * <PERSON>rror thrown when the URL scraper lambda fails
 */
export class UrlScraperError extends BaseError {
  /**
   * Creates a new UrlScraperError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}
