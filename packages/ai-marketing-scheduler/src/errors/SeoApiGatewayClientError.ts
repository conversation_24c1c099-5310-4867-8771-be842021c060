/**
 * Error thrown when there's an issue with the SeoApiGateway client operations
 */
export class SeoApiGatewayClientError extends Error {
  /**
   * Metadata associated with the error
   */
  public readonly metadata?: Record<string, unknown>;

  /**
   * Creates a new SeoApiGatewayClientError
   * @param message Error message
   * @param cause Original error that caused this error
   * @param metadata Additional metadata about the error
   */
  constructor(
    message: string,
    public readonly cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message);
    this.name = 'SeoApiGatewayClientError';
    this.metadata = metadata;
  }
}
