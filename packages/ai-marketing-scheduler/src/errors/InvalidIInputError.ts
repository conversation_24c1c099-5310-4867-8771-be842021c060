import { BaseError } from './BaseError';

/**
 * Error thrown when the input is invalid
 */
export class InvalidInputError extends BaseError {
  /**
   * Creates a new InvalidInputError
   * @param lambdaName - The name of the lambda that received invalid input
   * @param metadata - Additional metadata about the error
   */
  constructor(lambdaName: string, metadata?: Record<string, unknown>) {
    super(`Invalid input provided to ${lambdaName} lambda`, metadata);
    this.name = 'InvalidInputError';
  }
}
