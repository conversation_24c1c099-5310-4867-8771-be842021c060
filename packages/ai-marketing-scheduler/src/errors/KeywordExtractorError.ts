import { BaseError } from './BaseError';

/**
 * Error thrown when the scraper lambda fails
 */
export class KeywordExtractorError extends BaseError {
  /**
   * Creates a new KeywordExtractorError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}
