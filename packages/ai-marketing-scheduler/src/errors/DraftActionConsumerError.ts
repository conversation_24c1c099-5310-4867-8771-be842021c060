import { BaseError } from './BaseError';

/**
 * Error thrown when the draft action consumer lambda fails
 */
export class DraftActionConsumerError extends BaseError {
  /**
   * Creates a new DraftActionConsumerError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}
