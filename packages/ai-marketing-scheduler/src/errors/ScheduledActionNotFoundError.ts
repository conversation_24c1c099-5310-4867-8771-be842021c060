import { DraftActionConsumerError } from './DraftActionConsumerError';

/**
 * Error thrown when a scheduled action is not found
 */
export class ScheduledActionNotFoundError extends DraftActionConsumerError {
  /**
   * Creates a new ScheduledActionNotFoundError
   * @param actionId The ID of the action that was not found
   * @param cause Original error that caused this error
   * @param metadata Additional metadata about the error
   */
  constructor(
    actionId: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(`Scheduled action not found: ${actionId}`, cause, metadata);
    this.name = 'ScheduledActionNotFoundError';
  }
}
