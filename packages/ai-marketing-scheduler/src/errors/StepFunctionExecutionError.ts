import { BaseError } from './BaseError';

/**
 * Error thrown when a Step Function execution fails
 */
export class StepFunctionExecutionError extends BaseError {
  /**
   * Creates a new StepFunctionExecutionError
   * @param message - The error message
   * @param actionId - The ID of the action that failed
   * @param originalError - The original error that caused the failure
   */
  constructor(message: string, actionId: string, originalError?: Error) {
    super(message, {
      actionId,
      originalError: originalError?.message,
      originalErrorStack: originalError?.stack,
    });
  }
}
