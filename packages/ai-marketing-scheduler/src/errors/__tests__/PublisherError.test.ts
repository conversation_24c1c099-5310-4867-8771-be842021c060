import { BaseError } from '../BaseError';
import { PublisherError } from '../PublisherError';

describe('PublisherError', () => {
  it('should create an instance with message only', () => {
    const message = 'Publisher lambda error';
    const error = new PublisherError(message);

    expect(error).toBeInstanceOf(PublisherError);
    expect(error).toBeInstanceOf(BaseError);
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe(message);
    expect(error.name).toBe('PublisherError');
    expect(error.metadata).toEqual({
      cause: undefined,
      causeStack: undefined,
      context: undefined,
    });
  });

  it('should create an instance with message and cause', () => {
    const message = 'Failed to publish updates';
    const cause = new Error('STAR API timeout');
    const error = new PublisherError(message, cause);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
    expect(error.metadata?.context).toBeUndefined();
  });

  it('should create an instance with message, cause, and context', () => {
    const message = 'Failed to update meta title';
    const cause = new Error('Element not found');
    const context = {
      actionId: 'action-123',
      companyId: 'company-456',
      url: 'https://example.com/page',
      elementType: 'meta',
      elementProperty: 'title',
    };

    const error = new PublisherError(message, cause, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
    expect(error.metadata?.context).toEqual(context);
  });

  it('should handle undefined cause', () => {
    const message = 'No scraper result found';
    const context = {
      actionId: 'action-789',
      companyId: 'company-101',
    };

    const error = new PublisherError(message, undefined, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBeUndefined();
    expect(error.metadata?.causeStack).toBeUndefined();
    expect(error.metadata?.context).toEqual(context);
  });

  it('should be throwable and catchable as PublisherError', () => {
    const testFunction = () => {
      throw new PublisherError('Publishing failed');
    };

    expect(testFunction).toThrow(PublisherError);
    expect(testFunction).toThrow('Publishing failed');
  });

  it('should be catchable as BaseError', () => {
    const testFunction = () => {
      throw new PublisherError('Publishing failed');
    };

    try {
      testFunction();
    } catch (error) {
      expect(error).toBeInstanceOf(BaseError);
    }
  });

  it('should preserve stack trace', () => {
    const error = new PublisherError('Test error');

    expect(error.stack).toBeDefined();
    expect(error.stack).toContain('PublisherError');
    expect(error.stack).toContain('Test error');
  });

  it('should handle publishing specific context', () => {
    const context = {
      actionId: 'action-456',
      companyId: 'company-789',
      workflowType: 'SEO_KEYWORDS',
      updates: [
        { type: 'meta', property: 'title', success: true },
        {
          type: 'meta',
          property: 'description',
          success: false,
          error: 'Failed',
        },
        { type: 'h1', success: true },
      ],
    };

    const error = new PublisherError(
      'Partial publishing failure',
      undefined,
      context,
    );

    expect(error.metadata?.context).toEqual(context);
  });
});
