import { BaseError } from '../BaseError';
import { RankQueuerError } from '../RankQueuerError';

describe('RankQueuerError', () => {
  it('should create an instance with message only', () => {
    const message = 'Test error message';
    const error = new RankQueuerError(message);

    expect(error).toBeInstanceOf(RankQueuerError);
    expect(error).toBeInstanceOf(BaseError);
    expect(error.message).toBe(message);
    expect(error.name).toBe('RankQueuerError');
  });

  it('should create an instance with message and cause', () => {
    const message = 'Test error message';
    const cause = new Error('Original error');
    const error = new RankQueuerError(message, cause);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
  });

  it('should create an instance with message, cause, and context', () => {
    const message = 'Test error message';
    const cause = new Error('Original error');
    const context = {
      keyword: 'test keyword',
      pageId: 'page-123',
      additionalInfo: {
        timestamp: new Date().toISOString(),
        attemptNumber: 3,
      },
    };

    const error = new RankQueuerError(message, cause, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
    expect(error.metadata?.context).toEqual(context);
  });

  it('should handle undefined cause', () => {
    const message = 'Test error message';
    const context = { key: 'value' };
    const error = new RankQueuerError(message, undefined, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBeUndefined();
    expect(error.metadata?.causeStack).toBeUndefined();
    expect(error.metadata?.context).toEqual(context);
  });

  it('should be throwable and catchable as RankQueuerError', () => {
    const testFunction = () => {
      throw new RankQueuerError('Test throw');
    };

    expect(testFunction).toThrow(RankQueuerError);
    expect(testFunction).toThrow('Test throw');
  });

  it('should be catchable as BaseError', () => {
    const testFunction = () => {
      throw new RankQueuerError('Test throw');
    };

    try {
      testFunction();
    } catch (error) {
      expect(error).toBeInstanceOf(BaseError);
    }
  });

  it('should preserve stack trace', () => {
    const error = new RankQueuerError('Test error');

    expect(error.stack).toBeDefined();
    expect(error.stack).toContain('RankQueuerError');
    expect(error.stack).toContain('Test error');
  });

  it('should handle complex context objects', () => {
    const context = {
      keywords: ['keyword1', 'keyword2', 'keyword3'],
      invalidKeywords: [
        { keyword: '', pageId: 'page-1' },
        { keyword: 'test', pageId: '' },
      ],
      metadata: {
        source: 'lambda',
        environment: 'test',
        nested: {
          deep: {
            value: 42,
          },
        },
      },
    };

    const error = new RankQueuerError(
      'Complex context error',
      undefined,
      context,
    );

    expect(error.metadata?.context).toEqual(context);
  });
});
