import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON>r thrown when the publisher lambda fails
 */
export class PublisherError extends BaseError {
  /**
   * Creates a new PublisherError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the publisher encounters transient errors that should be retried
 */
export class TransientPublisherError extends PublisherError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientPublisherError';
  }
}
