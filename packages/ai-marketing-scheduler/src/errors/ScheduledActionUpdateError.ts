import { DraftActionConsumerError } from './DraftActionConsumerError';

/**
 * Error thrown when there's an issue updating a scheduled action
 */
export class ScheduledActionUpdateError extends DraftActionConsumerError {
  /**
   * Creates a new ScheduledActionUpdateError
   * @param actionId The ID of the action that failed to update
   * @param status The status that was being set
   * @param cause Original error that caused this error
   * @param metadata Additional metadata about the error
   */
  constructor(
    actionId: string,
    status: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(
      `Failed to update scheduled action ${actionId} to status ${status}`,
      cause,
      metadata,
    );
    this.name = 'ScheduledActionUpdateError';
  }
}
