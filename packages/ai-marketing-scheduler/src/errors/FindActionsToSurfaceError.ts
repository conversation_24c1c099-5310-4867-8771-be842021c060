import { DraftActionConsumerError } from './DraftActionConsumerError';

/**
 * Error thrown when there's an issue finding actions to surface
 */
export class FindActionsToSurfaceError extends DraftActionConsumerError {
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, cause, metadata);
    this.name = 'FindActionsToSurfaceError';
  }
}
