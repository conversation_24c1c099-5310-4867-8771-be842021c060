import { ContextLogger } from '../logger/contextLogger';
import { EntitlementDTO, UserDTO } from '../types';
import { TenantServiceError } from './errors';
import { ITenantClient } from './interfaces/ITenantClient';
import { PaginatedResponse } from './types/tenantClient';

export class TenantClient implements ITenantClient {
  constructor(
    private readonly baseUrl: string,
    private readonly entitlementsLimit: number | undefined,
    private readonly logger: ContextLogger,
    private readonly productIds: string[],
  ) {}

  private getEntitlementsUrl(): string {
    const params = new URLSearchParams({
      'filter.productId': `$in:${this.productIds.join(',')}`,
      'filter.endDate': '$null',
      select: 'company.website,company.name',
      ...(this.entitlementsLimit && {
        limit: `${this.entitlementsLimit}`,
      }),
    });
    return `${this.baseUrl}/api/v1/entitlements?${params.toString()}`;
  }

  private async getPageOfEntitlements(
    nextPageUrl?: string,
  ): Promise<PaginatedResponse<EntitlementDTO>> {
    this.logger.debug('Fetching page of entitlements', { nextPageUrl });

    const url = nextPageUrl || this.getEntitlementsUrl();
    const response = await fetch(url);

    if (!response.ok) {
      this.logger.error(
        `Tenant service HTTP error fetching entitlements status: ${response.status}`,
      );

      throw new TenantServiceError(
        `Failed to fetch entitlements status: ${response.status}`,
        response.status,
        {
          url,
          productIds: this.productIds,
        },
      );
    }
    return response.json() as Promise<PaginatedResponse<EntitlementDTO>>;
  }
  async getAllEntitlements(): Promise<EntitlementDTO[]> {
    let allEntitlements: EntitlementDTO[] = [];
    let nextPageUrl: string | undefined;

    do {
      const paginatedEntitlements =
        await this.getPageOfEntitlements(nextPageUrl);
      allEntitlements = [...allEntitlements, ...paginatedEntitlements.data];
      nextPageUrl = paginatedEntitlements?.links?.next;
    } while (nextPageUrl);

    this.logger.debug('Fetched all entitlements', {
      entitlementsCount: allEntitlements.length,
    });

    return allEntitlements;
  }

  private getCompanyAdminsUrl(companyId: string): string {
    // Hardcoding the admin role id for now since its the only aimed for this use case.
    // Values are the same on staging and prod.
    const adminRole = '8998af93-443c-49e3-9237-634583a947af';
    const params = new URLSearchParams({
      'filter.companyId': `$eq:${companyId}`,
      'filter.role': `$eq:${adminRole}`,
      'filter.type': '$eq:CLIENT',
      limit: '100',
    });
    return `${this.baseUrl}/api/v1/users?${params.toString()}`;
  }

  private async getPageOfUsers(
    companyId: string,
    nextPageUrl?: string,
  ): Promise<PaginatedResponse<UserDTO>> {
    this.logger.debug('Fetching page of company admins', { nextPageUrl });
    const url = nextPageUrl || this.getCompanyAdminsUrl(companyId);
    const response = await fetch(url);
    if (!response.ok) {
      this.logger.error(
        `Tenant service HTTP error fetching company users. Status: ${response.status}`,
      );

      throw new TenantServiceError(
        `Failed to fetch company users: ${response.status}`,
        response.status,
        {
          url,
          companyId,
        },
      );
    }
    return response.json() as Promise<PaginatedResponse<UserDTO>>;
  }

  async getAllCompanyAdmins(companyId: string): Promise<UserDTO[]> {
    let allAdmins: UserDTO[] = [];
    let nextPageUrl: string | undefined;

    do {
      const paginatedAdmins = await this.getPageOfUsers(companyId, nextPageUrl);
      // Filter out the companies field from each admin to reduce payload size
      const filteredAdmins = paginatedAdmins.data.map(
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ({ companies, ...admin }) => admin,
      );
      allAdmins = [...allAdmins, ...filteredAdmins];
      nextPageUrl = paginatedAdmins?.links?.next;
    } while (nextPageUrl);

    this.logger.debug('Fetched all company admins', {
      companyId,
      adminsCount: allAdmins.length,
    });

    return allAdmins;
  }
}
