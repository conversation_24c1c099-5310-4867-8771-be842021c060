import { ClientResponse } from '../../types/clientResponse';

/**
 * Payload for STAR API element update requests
 */
export interface StarApiPayload {
  /**
   * The URL of the page to update
   */
  url: string;

  /**
   * The input parameters for the element update
   */
  input: {
    /**
     * The property of the element to update (e.g., 'title', 'description')
     * Used for meta tag updates
     */
    elementProperty?: string;

    /**
     * The HTML tag of the element to update (e.g., 'meta', 'h1')
     */
    elementTag: string;

    /**
     * The index of the element on the page
     * Used for elements like h1 where multiple instances may exist
     */
    elementIndex?: number;

    /**
     * The new content to set for the element
     */
    newContent: string;

    /**
     * The current/old content of the element
     * Used for verification and conflict resolution
     */
    oldContent?: string;
  };

  /**
   * Whether to run in debug mode
   */
  debug: boolean;
}

/**
 * Response from STAR API element update
 */
export interface StarApiResponse {
  /**
   * Success message when the update succeeds
   */
  message?: string;

  /**
   * Error message when the update fails
   */
  error?: string;

  /**
   * Unique request ID for tracking
   */
  requestId: string;

  /**
   * HTTP status text
   */
  statusText?: string;
}

/**
 * Interface for the STAR API client
 */
export interface IStarApiClient {
  /**
   * Updates an element on a webpage via the STAR API
   * @param payload The update payload containing URL and element details
   * @param companyId Optional company ID for context in error notifications
   * @returns A promise that resolves with the update response
   * @throws {StarApiError} When the API request fails
   */
  updateElement(
    payload: StarApiPayload,
    companyId?: string,
    actionId?: string,
  ): Promise<ClientResponse<StarApiResponse>>;
}
