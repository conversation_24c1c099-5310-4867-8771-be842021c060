import { EntitlementDTO, UserDTO } from 'src/types';

/**
 * Interface for the Tenant client
 */
export interface ITenantClient {
  /**
   * Fetches all entitlements
   * @returns A list of entitlements
   */
  getAllEntitlements(): Promise<EntitlementDTO[]>;

  /**
   * Fetches all company admins
   * @param companyId The ID of the company
   * @returns A list of company admins
   */
  getAllCompanyAdmins(companyId: string): Promise<UserDTO[]>;
}
