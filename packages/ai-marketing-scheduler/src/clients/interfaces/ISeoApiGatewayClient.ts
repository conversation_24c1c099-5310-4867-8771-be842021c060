import {
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import { ActionsByCompany } from '../types/actions';
import {
  FeedFilters,
  HomepageFeed,
  RecommendationsRaw,
  StoreSEODraftResponse,
  StoredRecommendation,
  StoredScrapedPage,
} from '../types/seoApiGateway';

/**
 * Interface for the API Gateway client
 */
export interface ISeoApiGatewayClient {
  /**
   * Gets all scheduled actions with DRAFT_PENDING status
   * @returns A list of scheduled actions with DRAFT_PENDING status
   */
  getDraftPendingActions(): Promise<ScheduledAction[]>;

  /**
   * Gets all scheduled actions with SURFACED status ready to be published
   * @param scheduledToBePublishedAt The date to filter actions by (YYYY-MM-DD format)
   * @returns A list of scheduled actions with SURFACED status ready for publishing
   */
  getSurfacedActions(
    scheduledToBePublishedAt: string,
  ): Promise<ScheduledAction[]>;

  /**
   * Gets upcoming actions
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @returns The upcoming actions
   */
  getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]>;

  /**
   * Gets a scheduled action by ID
   * @param actionId The ID of the action
   * @returns The scheduled action or null if not found
   */
  getAction(actionId: string): Promise<ScheduledAction | null>;

  /**
   * Creates owed actions
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @param generationPayload Optional generation payload for the action
   * @returns The created action
   */
  createOwedActions(
    companyId: string,
    workflowType: WorkflowType,
    generationPayload?: object,
  ): Promise<ScheduledAction>;

  /**
   * Updates a scheduled action
   * @param actionId The ID of the action to update
   * @param delta The delta to apply to the action
   * @returns The updated scheduled action
   */
  updateScheduledAction(
    actionId: string,
    delta: Partial<
      Omit<ScheduledAction, 'id' | 'companyId' | 'workflowType' | 'createdAt'>
    >,
  ): Promise<ScheduledAction>;

  /**
   * Gets all actions to surface (pagination is fetched in batches of 100)
   * @returns A list of actions to surface
   */
  getAllActionsToSurface(): Promise<ActionsByCompany[]>;

  /**
   * Process actions to surface in batches to avoid memory issues
   * @param callback Function to process each batch of actions
   * @returns Promise that resolves when all batches are processed
   */
  processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void>;

  /**
   * Fetches all paginated actions
   * @param filters The filters to apply to the actions
   * @returns A list of actions
   */
  fetchAllPaginatedActions(filters: {
    status?: Status;
    companyId?: string;
    workflowType?: WorkflowType;
  }): Promise<ScheduledAction[]>;

  /**
   * Gets all pages for a specific company
   * @param companyId The ID of the company
   * @returns A list of pages for the company
   */
  getPagesByCompanyId(companyId: string): Promise<StoredScrapedPage[]>;

  /**
   * Gets a page by ID
   * @param pageId The ID of the page
   * @returns The page or null if not found
   */
  getPageById(pageId: string): Promise<StoredScrapedPage | null>;

  /**
   * Stores a SEO draft
   * @param companyId The ID of the company
   * @param actionId The ID of the action
   * @param scraperResult The scraper result to store
   * @param keywords The keywords to store
   * @param recommendations The recommendations to store
   * @returns The stored entities from the generation payload
   */
  storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult: ScraperResult,
    keywords: string,
    recommendations: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse>;

  /**
   * Updates the current rank of a page-keyword
   * @param pageKeywordId The ID of the page-keyword to update
   * @param currentRank The new current rank value (null to clear)
   * @returns The updated page-keyword
   */
  updatePageKeywordRank(
    pageKeywordId: string,
    currentRank: number | null,
  ): Promise<void>;

  /**
   * Gets the email content for a company
   * @param companyId The ID of the company
   * @param filters The filters to apply to the content
   * @returns The email content
   */
  getEmailContent(
    companyId: string,
    filters?: FeedFilters,
  ): Promise<HomepageFeed | null>;

  /**
   * Sends an email to the admins
   * @param companyId The ID of the company
   * @param admins The admins to send the email to
   * @param dynamicTemplateData Data objet to be passed to Sendgrid
   * @returns The email results
   */
  sendEmail(
    templateId: string,
    companyId: string,
    admins: string[],
    dynamicTemplateData: Record<string, unknown>,
    subject: string,
    bcc?: string | string[],
    groupUnsubscribeId?: string,
  ): Promise<void>;

  /**
   * Gets all surfaced and published groups with their keywords and scraped pages
   * @returns A list of groups with keyword and scraped page information
   */
  getSurfacedAndPublishedGroups(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  >;

  /**
   * Gets surfaced and published groups with minimal data for ranking operations
   * Optimized query that only loads necessary fields to prevent memory issues
   * @returns A lightweight list of groups with only keyword and scraped page info
   */
  getSurfacedAndPublishedGroupsForRanking(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  >;

  /**
   * Gets paginated surfaced and published groups for ranking operations
   * Prevents memory issues by fetching data in manageable chunks
   * @param page Page number (1-based)
   * @param limit Number of items per page
   * @returns Paginated response with groups and pagination info
   */
  getSurfacedAndPublishedGroupsForRankingPaginated(
    page: number,
    limit: number,
  ): Promise<{
    data: {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[];
    hasMore: boolean;
    totalCount: number;
    currentPage: number;
    pageSize: number;
  }>;

  /**
   * Applies a recommendation by setting its status to APPLIED
   * @param recommendationId The ID of the recommendation to apply
   * @returns The updated recommendation
   */
  applyRecommendation(recommendationId: string): Promise<{
    id: string;
    status: string;
    type: string;
    recommendationValue: string;
    currentValue: string;
  }>;

  /**
   * Gets all recommendations for a specific group
   * @param groupId The ID of the group
   * @returns A list of recommendations for the group
   */
  getRecommendationsByGroupId(groupId: string): Promise<
    {
      id: string;
      type: string;
      status: string;
      recommendationValue: string;
      currentValue: string;
    }[]
  >;

  /**
   * Gets a single recommendation by ID
   * @param recommendationId The ID of the recommendation
   * @returns The recommendation details
   */
  getRecommendationById(
    recommendationId: string,
  ): Promise<StoredRecommendation>;
}
