// This file contains types copied from client-marketing-service.

import { RecommendationType } from '../../types/recommendation';
import { ScrapedPageType } from '../../types/scraperResult';

// TODO: Move these to a shared package.
/**
 * StoredKeyword represents a keyword entity persisted in the API.
 */
export interface StoredKeyword {
  id: string;
  keyword: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  groups?: StoredGroup[];
  pageKeywords?: StoredScrapedPageKeyword[];
  pageKeywordHistory?: StoredPageKeywordHistory[];
}

/**
 * StoredRecommendation represents a recommendation entity persisted in the API.
 * This is the entity returned from SEO API Gateway.
 */
export interface StoredRecommendation {
  id: string;
  scrapeId: string | null;
  groupId: string;
  type: RecommendationType;
  currentValue?: string;
  recommendationValue: string;
  reasoning: string;
  status: RecommendationStatus;
  rejectionReason: string | null;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  scrapedPage?: StoredScrapedPage;
  group?: StoredGroup;
  scrape?: StoredScrape;
}

/**
 * StoredScrape represents a scrape entity persisted in the API.
 */
export interface StoredScrape {
  id: string;
  companyId: string;
  scrapedPageId: string;
  rawHtml: string;
  markdown: string;
  currentScrapedValues: ScrapedValues;
  mediaId: string;
  scrapedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  scrapedPage: StoredScrapedPage;
  recommendations: StoredRecommendation[];
}

/**
 * StoredScrapedPage represents a scraped page entity persisted in the API.
 * This is the entity returned from SEO API Gateway.
 * @see ScraperResult in types/scraperResult.ts for the pre-persistence scraper output
 */
export interface StoredScrapedPage {
  id: string;
  companyId: string;
  url: string;
  pageName: string;
  pageType: ScrapedPageType;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  recommendations: StoredRecommendation[];
  scrapes: StoredScrape[];
  pageKeywordHistory: StoredPageKeywordHistory[];
  pageKeywords: StoredScrapedPageKeyword[];
}

/**
 * StoredScrapedPageKeyword represents the relationship between a scraped page and keyword in the API.
 */
export interface StoredScrapedPageKeyword {
  id: string;
  scrapedPageId: string;
  keywordId: string;
  originalRank: number | null;
  currentRank: number | null;
  rankCheckedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
  keyword: StoredKeyword;
  scrapedPage: StoredScrapedPage;
  companyId: string;
}

/**
 * StoredGroup represents a recommendation group entity persisted in the API.
 */
export interface StoredGroup {
  id: string;
  companyId: string;
  keywordId: string;
  title: string;
  description: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  recommendations?: StoredRecommendation[];
  keyword: StoredKeyword;
  groupScheduledActions?: StoredGroupScheduledAction[];
  company?: StoredCompany;
}

/**
 * StoredPageKeywordHistory represents the history of page-keyword relationships in the API.
 */
export interface StoredPageKeywordHistory {
  id: string;
  scrapedPageId: string;
  keywordId: string;
  changeNote: string;
  updatedBy: string;
  assignedAt: Date | null;
  removedAt: Date | null;
  createdAt: Date;
  scrapedPage: StoredScrapedPage;
  keyword: StoredKeyword;
}

/**
 * StoredGroupScheduledAction represents the relationship between a group and scheduled action in the API.
 */
export interface StoredGroupScheduledAction {
  id: string;
  groupId: string;
  scheduledActionId: string;
  createdAt: Date;
  updatedAt: Date;
  group: StoredGroup;
  scheduledAction: StoredScheduledAction;
  companyId: string;
}

/**
 * StoredCompany represents a company entity persisted in the API.
 */
export interface StoredCompany {
  id: string;
  // Add other company fields as needed
}

/**
 * StoredScheduledAction represents a scheduled action entity persisted in the API.
 * Note: This is different from the ScheduledAction in types/scheduledAction.ts
 */
export interface StoredScheduledAction {
  id: string;
  // Add other scheduled action fields as needed
}

export enum RecommendationStatus {
  PENDING = 'PENDING',
  APPLIED = 'APPLIED',
  REJECTED = 'REJECTED',
}

export interface ScrapedValues {
  [key: string]: any;
}

export interface StoreSEODraftResponse {
  savedKeyword: {
    id: string;
    keyword: string;
  } | null;
  savedRecommendations:
    | {
        id: string;
        type: RecommendationType;
        groupId: string;
        scrapeId: string;
      }[]
    | null;
  savedScrape: {
    id: string;
    mediaId: string;
    markdown: string;
  } | null;
  savedScrapedPage: {
    id: string;
    url: string;
    companyId: string;
  } | null;
  savedPageKeyword: {
    id: string;
    keywordId: string;
  } | null;
  savedGroup: {
    id: string;
    companyId: string;
    keywordId: string;
  } | null;
}

export type RecommendationsRaw = {
  [key in 'metaTitle' | 'metaDescription' | 'mainHeading']: {
    currentValue: string;
    recommendationValue: string;
    reasoning: string;
  };
};

export type FeedItem = {
  timestamp: string;
  itemType: string;
  // This will contain several object for different item types,
  // as we are only passing this to the email service without any modifications or parsing leaving as any for now.
  content: any;
};

export type HomepageFeed = {
  items: FeedItem[];
};

export interface FeedFilters {
  timestampStart: string;
  timestampEnd: string;
}
