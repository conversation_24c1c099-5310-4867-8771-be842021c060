interface PaginatedResponseLinks {
  current: string;
  first?: string;
  previous?: string;
  next?: string;
  last?: string;
}

interface PaginatedResponseMeta {
  itemsPerPage: number;
  totalItems: number;
  currentPage: number;
  totalPages: number;
  sortBy?: [string, 'ASC' | 'DESC'][];
  filter?: Record<string, string>;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: PaginatedResponseMeta;
  links: PaginatedResponseLinks;
}
