import { StarApiConfig } from 'src/config';
import { ContextLogger } from 'src/logger/contextLogger';
import { getMetricsClient } from 'src/metrics/MetricsClient';
import { notifyStarApiFailure } from 'src/utils/slack';

import { ClientResponse } from '../types/clientResponse';
import { StarApiError } from './errors/StarApiError';
import {
  IStarApiClient,
  StarApiPayload,
  StarApiResponse,
} from './interfaces/IStarApiClient';

/**
 * Client for interacting with the STAR API
 */
export class StarApiClient implements IStarApiClient {
  private readonly apiUrl: string;
  private readonly metricsClient = getMetricsClient();

  constructor(
    private readonly config: StarApiConfig,
    private readonly logger: ContextLogger,
  ) {
    this.apiUrl = `${this.config.url}/api/v1/star/editor`;
  }

  /**
   * Returns a specific error message based on the HTTP status code
   * @param statusCode The HTTP status code
   * @param errorDetails The error details from the response
   * @returns A descriptive error message
   */
  private getErrorMessageForStatusCode(
    statusCode: number,
    errorDetails: any,
  ): string {
    switch (statusCode) {
      case 400:
        return `Bad request: ${errorDetails.message || 'Invalid payload format (JOI error)'}`;
      case 404:
        return 'Not found: URL or component of the URL was not found';
      case 405:
        return 'Method not allowed: Feature flag is not enabled';
      case 500:
        return 'Server error: Unexpected error (edge case that was not mapped)';
      default:
        return `STAR API request failed: ${errorDetails.error || errorDetails.message || 'Unknown error'}`;
    }
  }

  /**
   * Makes a request to the STAR API
   * @param url The endpoint URL
   * @param options Request options
   * @returns The response from the API
   * @throws {StarApiError} When the request fails
   */
  private async fetch(
    url: string,
    options: RequestInit = {},
    companyId?: string,
    actionId?: string,
  ): Promise<Response> {
    const headers = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...options.headers,
    };

    // Parse payload from body if it exists
    let payload: StarApiPayload | undefined;
    if (options.body && typeof options.body === 'string') {
      try {
        payload = JSON.parse(options.body);
      } catch {
        // If parsing fails, payload remains undefined
      }
    }

    this.logger.debug('Making STAR API request', {
      url,
      method: options.method || 'GET',
      body: options.body,
    });

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorDetails: any = {};

        try {
          errorDetails = JSON.parse(errorText);
        } catch {
          errorDetails = { rawError: errorText };
        }

        // Log error response
        this.logger.error('STAR API error response', {
          url,
          method: options.method || 'GET',
          status: response.status,
          statusText: response.statusText,
          requestBody: options.body,
          responseBody: errorDetails,
          responseMessage: errorDetails.message,
          error: errorDetails.error,
          requestId: errorDetails.requestId,
        });

        const error = new StarApiError(
          this.getErrorMessageForStatusCode(response.status, errorDetails),
          response.status,
          {
            url,
            status: response.status,
            statusText: response.statusText,
            ...errorDetails,
          },
        );

        // Record error metric for success rate tracking
        this.metricsClient.recordStarApiError(
          'http_error',
          companyId,
          payload?.input.elementTag,
          actionId,
          response.status,
        );

        // Send Slack notification for error response
        await notifyStarApiFailure({
          error,
          payload,
          url,
          method: options.method || 'GET',
          status: response.status,
          requestId: errorDetails.requestId,
          companyId,
          responseBody: errorDetails,
          actionId,
        });

        throw error;
      }

      return response;
    } catch (error) {
      if (error instanceof StarApiError) {
        throw error;
      }

      // Log network errors
      this.logger.error('STAR API network/fetch error', {
        url,
        method: options.method || 'GET',
        requestBody: options.body,
        error: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      });

      const networkError = new StarApiError(
        `Failed to connect to STAR API: ${error instanceof Error ? error.message : 'Unknown error'}`,
        0,
        {
          url,
          originalError: error instanceof Error ? error.message : String(error),
          errorStack: error instanceof Error ? error.stack : undefined,
        },
      );

      // Record network error metric
      this.metricsClient.recordStarApiError(
        'network_error',
        companyId,
        payload?.input.elementTag,
        actionId,
      );

      // Send Slack notification for network error
      await notifyStarApiFailure({
        error: networkError,
        payload,
        url,
        method: options.method || 'GET',
        companyId,
        actionId,
      });

      throw networkError;
    }
  }

  /**
   * Updates an element on a webpage via the STAR API
   * @param payload The update payload
   * @returns The API response
   */
  async updateElement(
    payload: StarApiPayload,
    companyId?: string,
    actionId?: string,
  ): Promise<ClientResponse<StarApiResponse>> {
    this.logger.debug('Updating element via STAR API', { payload });

    const startTime = Date.now();
    let success = false;

    try {
      const response = await this.fetch(
        this.apiUrl,
        {
          method: 'PATCH',
          body: JSON.stringify(payload),
        },
        companyId,
        actionId,
      );

      const responseData = await response.json();

      // Log successful response
      this.logger.debug('STAR API request successful', {
        url: this.apiUrl,
        method: 'PATCH',
        status: response.status,
        statusText: response.statusText,
        requestBody: JSON.stringify(payload),
        responseBody: responseData,
        responseMessage: responseData.message,
        error: responseData.error,
        requestId: responseData.requestId,
      });

      // Check if response contains error
      if (responseData.error) {
        this.logger.error('STAR API returned error', {
          payload,
          response: responseData,
          responseMessage: responseData.message,
          error: responseData.error,
          requestId: responseData.requestId,
        });

        const error = new StarApiError(responseData.error, response.status, {
          url: payload.url,
          ...responseData,
        });

        // Record API error metric
        this.metricsClient.recordStarApiError(
          'api_error',
          companyId,
          payload.input.elementTag,
          actionId,
          response.status,
        );

        // Send Slack notification for API error
        this.logger.debug('Sending Slack notification for API error');
        await notifyStarApiFailure({
          error,
          payload,
          url: this.apiUrl,
          method: 'PATCH',
          status: response.status,
          requestId: responseData.requestId,
          companyId,
          responseBody: responseData,
          actionId,
        });

        throw error;
      }

      // Record successful operation
      success = true;
      this.metricsClient.recordStarApiSuccess(
        companyId,
        payload.input.elementTag,
        actionId,
      );

      this.logger.info('STAR API update successful', {
        payload,
        response: responseData,
        responseMessage: responseData.message,
        error: responseData.error,
        requestId: responseData.requestId,
      });

      return {
        data: responseData,
        context: responseData.requestId
          ? { starApiRequestId: responseData.requestId }
          : {},
      };
    } catch (error) {
      if (error instanceof StarApiError) {
        this.logger.error('STAR API update failed', {
          url: payload.url,
          elementTag: payload.input.elementTag,
          error: error.message,
          metadata: error.metadata,
        });
        throw error;
      }

      // Unexpected error
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error('Unexpected error updating element', {
        url: payload.url,
        error: errorMessage,
        elementTag: payload.input.elementTag,
      });

      const unexpectedError = new StarApiError(errorMessage, 0, {
        companyId,
        url: payload.url,
        originalError: errorMessage,
      });

      // Record unexpected error metric
      this.metricsClient.recordStarApiError(
        'unexpected_error',
        companyId,
        payload.input.elementTag,
        actionId,
      );

      // Send Slack notification for unexpected error
      this.logger.debug('Sending Slack notification for unexpected error', {
        companyId,
        url: payload.url,
        error: errorMessage,
      });
      await notifyStarApiFailure({
        error: unexpectedError,
        payload,
        url: this.apiUrl,
        method: 'PATCH',
        companyId,
        actionId,
      });

      throw unexpectedError;
    } finally {
      // Always record timing metrics
      const duration = Date.now() - startTime;
      this.metricsClient.recordStarApiDuration(
        duration,
        companyId,
        payload.input.elementTag,
        success,
      );
    }
  }
}
