import { GraphQLClient } from 'graphql-request';
import pLimit from 'p-limit';

import { ContextLogger } from '../logger/contextLogger';
import { Entitlement } from '../types';
import { getGWAuthToken } from '../utils/getGWAuthToken';
import { IApiGatewayClient } from './interfaces/IApiGatewayClient';

export interface Website {
  name: string;
  hostname: string;
  category: string;
}

export class ApiGatewayClient implements IApiGatewayClient {
  private readonly token: string;

  constructor(
    private readonly apiGatewaySuperUser: string,
    private readonly apiGatewayKey: string,
    private readonly graphqlClient: GraphQLClient,
    private readonly logger: ContextLogger,
  ) {
    this.token = getGWAuthToken(this.apiGatewaySuperUser, this.apiGatewayKey);
  }
  /**
   * For a list of entitlements, sets websiteUrl with a valid LIVE website hostname if existent
   * @param entitlements {Entitlement[]}
   * @returns entitlements with websiteUrl set
   */
  async fetchAllWebsites(entitlements: Entitlement[]): Promise<Entitlement[]> {
    const limit = pLimit(5);
    const withWebsites = await Promise.all(
      entitlements.map(entitlement => {
        const wrappedFn = async () => {
          try {
            const websites: Website[] = await this.getCompanyWebsites(
              entitlement.companyId,
            );
            if (!websites.length) {
              this.logger.info(
                `No websites found for company ${entitlement.companyId}`,
              );
              entitlement.websiteUrl = '';
              return null;
            }
            this.logger.info(
              `Fetched websites from company ${entitlement.companyId}`,
              { websites },
            );
            const website =
              (entitlement.websiteUrl &&
                websites.find(w =>
                  entitlement.websiteUrl!.includes(w.hostname),
                )) ||
              websites[0];
            entitlement.websiteUrl = website.hostname;
            return entitlement;
          } catch (error) {
            this.logger.error('Error fetching websites', {
              error: error instanceof Error ? error.message : String(error),
              companyId: entitlement.companyId,
            });
            entitlement.websiteUrl = '';
            return null;
          }
        };
        return limit(wrappedFn);
      }),
    );

    return withWebsites.filter((e): e is Entitlement => !!e);
  }

  private async getCompanyWebsites(companyId: string): Promise<Website[]> {
    try {
      const websitesQuery = `
        query companyWebsite ($companyId: String) {
          websites(companyId: $companyId, status: LIVE, category: "BRAND") {
            name,
            hostname,
            category
          }
        }
      `;
      const websitesData = await this.graphqlClient.request<{
        websites: Website[];
      }>(websitesQuery, { companyId }, { Authorization: this.token });
      return websitesData.websites;
    } catch (error) {
      this.logger.error('Failed to fetch company websites', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      throw error;
    }
  }
}
