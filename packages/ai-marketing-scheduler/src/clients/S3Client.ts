import { S3Client as AWSS3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getConfig } from 'config';
import { S3Config } from 'config/interfaces';

import { ContextLogger } from '../logger/contextLogger';
import { S3Error } from './errors/S3Error';

export class S3Client {
  private readonly s3Client: AWSS3Client;
  private bucketName: string;

  constructor(
    s3Config: S3Config,
    private readonly logger: ContextLogger,
  ) {
    this.s3Client = new AWSS3Client(s3Config);
    this.bucketName = s3Config.bucketName;
  }

  async uploadFromUrl(url: string, key: string) {
    this.logger.debug('Fetching data from URL', { url });
    const stream = await this.fetchStream(url);

    this.logger.debug('Uploading data to S3 from URL', { url, key });
    return this.uploadObject(
      this.bucketName,
      key,
      Buffer.from(await stream.arrayBuffer()),
    );
  }

  private async fetchStream(url: string) {
    try {
      const res = await fetch(url);
      if (!res.ok) {
        throw new S3Error(
          `Failed to fetch image: ${res.statusText} (status: ${res.status})`,
        );
      }
      return res;
    } catch (error) {
      if (error instanceof S3Error) throw error;
      throw new S3Error(
        `Error fetching image from ${url}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async uploadObject(
    bucketName: string,
    key: string,
    body: Buffer | Uint8Array | Blob | string,
  ) {
    if (['local', 'development'].includes(getConfig().environment)) {
      this.logger.info('Skipping upload to S3 in development environment');
      return;
    }
    try {
      const start = new Date();
      const result = await this.s3Client.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: key,
          Body: body,
        }),
      );
      this.logger.info('Successfully uploaded object to S3', {
        bucketName,
        key,
        duration: new Date().getTime() - start.getTime(),
      });
      return result;
    } catch (error) {
      throw new S3Error(
        `Error uploading object to S3 bucket ${bucketName} with key ${key}`,
        error instanceof Error ? error : undefined,
      );
    }
  }
}
