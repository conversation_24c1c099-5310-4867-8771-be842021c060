import { BaseError } from '../../errors/BaseError';

/**
 * Error thrown when there's an issue with S3 operations.
 */
export class S3Error extends BaseError {
  /**
   * Creates a new S3Error.
   * @param message - The error message.
   * @param cause - The original error that caused this error (optional).
   * @param metadata - Additional metadata about the error (optional).
   */
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...(cause && {
        originalError: cause.message,
        originalStack: cause.stack,
      }),
      ...metadata,
      errorType: 'S3Error',
    });
    this.name = 'S3Error';
  }
}
