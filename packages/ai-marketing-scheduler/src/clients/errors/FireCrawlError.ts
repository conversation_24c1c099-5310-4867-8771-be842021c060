import { BaseError } from '../../errors/BaseError';

/**
 * Error thrown when there's an issue with Firecrawl operations.
 */
export class FireCrawlError extends BaseError {
  /**
   * Creates a new FireCrawlError.
   * @param message - The error message.
   * @param cause - The original error that caused this error (optional).
   * @param metadata - Additional metadata about the error (optional).
   */
  constructor(
    message: string,
    cause?: Error,
    metadata?: Record<string, unknown>,
  ) {
    super(message, {
      ...(cause && {
        originalError: cause.message,
        originalStack: cause.stack,
      }),
      ...metadata,
      errorType: 'FireCrawlError',
    });
    this.name = 'FireCrawlError';
  }
}
