import { BaseError } from 'src/errors';

/**
 * Abstract API error class that extends BaseError.
 * Provides status code support for HTTP error handling.
 */
export abstract class ApiError extends BaseError {
  /**
   * Creates a new ApiError instance
   * @param message - The error message
   * @param statusCode - The HTTP status code
   * @param context - Optional object containing additional error context
   * @example
   * class TenantNotFoundError extends ApiError {
   *   constructor(tenantId: string) {
   *     super('Tenant not found', 404, { tenantId });
   *   }
   * }
   *
   * @example Error handling with status code
   * try {
   *   throw new TenantNotFoundError('tenant_123');
   * } catch (error) {
   *   if (error instanceof TenantNotFoundError) {
   *     console.log(error.message); // "Tenant not found"
   *     console.log(error.statusCode); // 404
   *     console.log(error.context); // { tenantId: 'tenant_123' }
   *   }
   * }
   */
  constructor(
    message: string,
    public readonly statusCode: number,
    metadata?: Record<string, unknown>,
  ) {
    super(message, metadata);
    this.name = this.constructor.name;
  }
}
