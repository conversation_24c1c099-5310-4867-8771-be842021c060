import { ApiError } from '../ApiError';
import { StarApiError } from '../StarApiError';

describe('StarApiError', () => {
  it('should create an instance with message and status code', () => {
    const message = 'STAR API error';
    const statusCode = 500;
    const error = new StarApiError(message, statusCode);

    expect(error).toBeInstanceOf(StarApiError);
    expect(error).toBeInstanceOf(ApiError);
    expect(error).toBeInstanceOf(Error);
    expect(error.message).toBe(message);
    expect(error.statusCode).toBe(statusCode);
    expect(error.name).toBe('StarApiError');
  });

  it('should create an instance with message, status code, and metadata', () => {
    const message = 'Failed to update element';
    const statusCode = 400;
    const metadata = {
      url: 'https://example.com/page',
      elementTag: 'meta',
      elementProperty: 'title',
      oldContent: 'Old Title',
      newContent: 'New Title',
    };

    const error = new StarApiError(message, statusCode, metadata);

    expect(error.message).toBe(message);
    expect(error.statusCode).toBe(statusCode);
    expect(error.metadata).toEqual(metadata);
  });

  it('should handle different status codes correctly', () => {
    const testCases = [
      { status: 400, message: 'Bad Request' },
      { status: 401, message: 'Unauthorized' },
      { status: 404, message: 'Not Found' },
      { status: 500, message: 'Internal Server Error' },
    ];

    testCases.forEach(({ status, message }) => {
      const error = new StarApiError(message, status);
      expect(error.statusCode).toBe(status);
      expect(error.message).toBe(message);
    });
  });

  it('should be throwable and catchable as StarApiError', () => {
    const testFunction = () => {
      throw new StarApiError('API error', 500);
    };

    expect(testFunction).toThrow(StarApiError);
    expect(testFunction).toThrow('API error');
  });

  it('should be catchable as ApiError', () => {
    const testFunction = () => {
      throw new StarApiError('API error', 500);
    };

    try {
      testFunction();
    } catch (error) {
      expect(error).toBeInstanceOf(ApiError);
      if (error instanceof ApiError) {
        expect(error.statusCode).toBe(500);
      }
    }
  });

  it('should preserve stack trace', () => {
    const error = new StarApiError('Test error', 500);

    expect(error.stack).toBeDefined();
    expect(error.stack).toContain('StarApiError');
    expect(error.stack).toContain('Test error');
  });

  it('should handle element update failure context', () => {
    const metadata = {
      url: 'https://example.com/blog/post-1',
      payload: {
        url: 'https://example.com/blog/post-1',
        input: {
          elementProperty: 'description',
          elementTag: 'meta',
          newContent: 'New meta description',
          oldContent: 'Old meta description',
        },
        debug: false,
      },
      response: {
        error: 'Element not found',
        details: 'No meta description tag found on the page',
      },
    };

    const error = new StarApiError(
      'Failed to update meta description',
      404,
      metadata,
    );

    expect(error.metadata).toEqual(metadata);
    expect(error.statusCode).toBe(404);
  });

  it('should handle H1 update failure context', () => {
    const metadata = {
      url: 'https://example.com/page',
      payload: {
        url: 'https://example.com/page',
        input: {
          elementIndex: 0,
          elementTag: 'h1',
          newContent: 'New H1 Content',
          oldContent: 'Old H1 Content',
        },
        debug: false,
      },
      response: {
        error: 'Content mismatch',
        details: 'The old content does not match the current page content',
      },
    };

    const error = new StarApiError(
      'Failed to update H1 - content mismatch',
      409,
      metadata,
    );

    expect(error.metadata).toEqual(metadata);
    expect(error.statusCode).toBe(409);
  });
});
