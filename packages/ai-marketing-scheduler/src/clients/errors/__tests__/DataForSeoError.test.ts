import { BaseError } from '../../../errors/BaseError';
import { DataForSeoError } from '../DataForSeoError';

describe('DataForSeoError', () => {
  it('should create an instance with message only', () => {
    const message = 'DataForSeo API error';
    const error = new DataForSeoError(message);

    expect(error).toBeInstanceOf(DataForSeoError);
    expect(error).toBeInstanceOf(BaseError);
    expect(error.message).toBe(message);
    expect(error.name).toBe('DataForSeoError');
  });

  it('should create an instance with message and cause', () => {
    const message = 'Failed to fetch data';
    const cause = new Error('Network timeout');
    const error = new DataForSeoError(message, cause);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
  });

  it('should create an instance with message, cause, and context', () => {
    const message = 'API request failed';
    const cause = new Error('HTTP 500');
    const context = {
      url: '/v3/serp/google/organic/task_post',
      status: 500,
      response: 'Internal Server Error',
      requestData: {
        keyword: 'test keyword',
        location: 'United States',
      },
    };

    const error = new DataForSeoError(message, cause, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBe(cause.message);
    expect(error.metadata?.causeStack).toBe(cause.stack);
    expect(error.metadata?.context).toEqual(context);
  });

  it('should handle undefined cause', () => {
    const message = 'Invalid response format';
    const context = {
      response: { tasks: null },
      expectedFormat: 'array',
    };
    const error = new DataForSeoError(message, undefined, context);

    expect(error.message).toBe(message);
    expect(error.metadata?.cause).toBeUndefined();
    expect(error.metadata?.causeStack).toBeUndefined();
    expect(error.metadata?.context).toEqual(context);
  });

  it('should be throwable and catchable as DataForSeoError', () => {
    const testFunction = () => {
      throw new DataForSeoError('API error');
    };

    expect(testFunction).toThrow(DataForSeoError);
    expect(testFunction).toThrow('API error');
  });

  it('should be catchable as BaseError', () => {
    const testFunction = () => {
      throw new DataForSeoError('API error');
    };

    try {
      testFunction();
    } catch (error) {
      expect(error).toBeInstanceOf(BaseError);
    }
  });

  it('should preserve stack trace', () => {
    const error = new DataForSeoError('Test error');

    expect(error.stack).toBeDefined();
    expect(error.stack).toContain('DataForSeoError');
    expect(error.stack).toContain('Test error');
  });

  it('should handle task count mismatch context', () => {
    const context = {
      sentCount: 5,
      receivedCount: 3,
      sentKeywords: [
        'keyword1',
        'keyword2',
        'keyword3',
        'keyword4',
        'keyword5',
      ],
      receivedTaskIds: ['task-1', 'task-2', 'task-3'],
      response: {
        status_code: 20000,
        tasks: [
          { id: 'task-1', status_code: 20000 },
          { id: 'task-2', status_code: 20000 },
          { id: 'task-3', status_code: 20000 },
        ],
      },
    };

    const error = new DataForSeoError(
      'Task count mismatch',
      undefined,
      context,
    );

    expect(error.metadata?.context).toEqual(context);
  });

  it('should handle HTTP error context', () => {
    const context = {
      url: 'task_post',
      status: 401,
      response: 'Unauthorized: Invalid credentials',
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Basic [REDACTED]',
      },
    };

    const error = new DataForSeoError(
      'HTTP error task_post 401: Unauthorized',
      undefined,
      context,
    );

    expect(error.message).toBe('HTTP error task_post 401: Unauthorized');
    expect(error.metadata?.context).toEqual(context);
  });
});
