import { GraphQLClient } from 'graphql-request';

import { SeoApiGatewayClientError } from '../../errors';
import { CREATE_OWED_ACTIONS } from '../../graphql/mutations/createOwedActions';
import { UPDATE_SCHEDULED_ACTION } from '../../graphql/mutations/scheduledActions';
import { UPDATE_PAGE_KEYWORD_RANK } from '../../graphql/mutations/updatePageKeywordRank';
import { GET_PAGES_BY_COMPANY } from '../../graphql/queries/pages';
import {
  GET_DRAFT_PENDING_ACTIONS,
  GET_SURFACED_ACTIONS,
  SCHEDULED_ACTION,
} from '../../graphql/queries/scheduledActions';
import { GET_SCRAPED_PAGE_BY_ID } from '../../graphql/queries/scrapedPage';
import { UPCOMING_ACTIONS } from '../../graphql/queries/upcomingActions';
import { ContextLogger } from '../../logger/contextLogger';
import {
  ScrapedPageType,
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import {
  createMockContextLogger,
  createMockGraphQLClient,
  createMockScheduledActions,
  createMockScrapedPages,
} from '../../utils/testUtils';
import { SeoApiGatewayClient } from '../SeoApiGatewayClient';

jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
}));

describe('SeoApiGatewayClient', () => {
  const mockM2mSuperApiKey = 'localkey';

  let mockGraphQLClient: jest.Mocked<GraphQLClient>;
  let mockLogger: jest.Mocked<ContextLogger>;
  let seoApiGatewayClient: SeoApiGatewayClient;

  beforeEach(() => {
    // Set up test mocks including logger
    mockLogger = createMockContextLogger();

    // Create mocks using centralized factory
    mockGraphQLClient = createMockGraphQLClient();

    // Create client instance
    seoApiGatewayClient = new SeoApiGatewayClient(
      mockGraphQLClient,
      mockLogger,
      mockM2mSuperApiKey,
      'https://mock-client-marketing-service-url.com',
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should throw an error if M2M super API key is not provided', () => {
      expect(() => {
        new SeoApiGatewayClient(mockGraphQLClient, mockLogger, '', '');
      }).toThrow('M2M_SUPER_API_KEY is not set');
    });
  });

  describe('getDraftPendingActions', () => {
    const mockActions = createMockScheduledActions();

    it('should fetch draft pending actions successfully', async () => {
      // Mock GraphQL response
      mockGraphQLClient.request.mockResolvedValueOnce({
        draftPendingActions: mockActions,
      });

      // Call the method
      const result = await seoApiGatewayClient.getDraftPendingActions();

      // Verify GraphQL request
      // Verify GraphQL request uses the auth headers
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        GET_DRAFT_PENDING_ACTIONS,
        {},
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle errors when fetching draft pending actions', async () => {
      // Mock GraphQL error
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        seoApiGatewayClient.getDraftPendingActions(),
      ).rejects.toThrow(SeoApiGatewayClientError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch draft pending actions',
        {
          error: mockError.message,
        },
      );
    });
  });

  describe('updateScheduledAction', () => {
    const mockActionId = 'action-123';
    const mockDelta = {
      status: Status.PUBLISHED,
      updatedAt: new Date().toISOString(),
    };
    const mockUpdatedAction = {
      id: mockActionId,
      ...mockDelta,
      createdAt: new Date().toISOString(),
      companyId: 'company-123',
      workflowType: WorkflowType.SEO,
    };

    it('should update scheduled action successfully', async () => {
      // Mock GraphQL response
      mockGraphQLClient.request.mockResolvedValueOnce({
        updateScheduledAction: mockUpdatedAction,
      });

      // Call the method
      const result = await seoApiGatewayClient.updateScheduledAction(
        mockActionId,
        mockDelta,
      );

      // Verify GraphQL request
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        UPDATE_SCHEDULED_ACTION,
        {
          id: mockActionId,
          ...mockDelta,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      // Verify result
      expect(result).toEqual(mockUpdatedAction);
    });

    it('should handle errors when updating scheduled action', async () => {
      // Mock GraphQL error
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        seoApiGatewayClient.updateScheduledAction(mockActionId, mockDelta),
      ).rejects.toThrow(SeoApiGatewayClientError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to update scheduled action',
        {
          id: mockActionId,
          error: mockError.message,
        },
      );
    });
  });

  describe('createOwedActions', () => {
    const mockCompanyId = 'company-123';
    const mockWorkflowType = WorkflowType.SEO;
    const mockActions = createMockScheduledActions(2);

    it('should create owed actions successfully', async () => {
      // Mock GraphQL response
      mockGraphQLClient.request.mockResolvedValueOnce({
        createOwedActions: mockActions,
      });

      // Call the method
      const result = await seoApiGatewayClient.createOwedActions(
        mockCompanyId,
        mockWorkflowType,
      );

      // Verify GraphQL request
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        CREATE_OWED_ACTIONS,
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          generationPayload: undefined,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle errors when creating owed actions', async () => {
      // Mock GraphQL error
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        seoApiGatewayClient.createOwedActions(mockCompanyId, mockWorkflowType),
      ).rejects.toThrow(SeoApiGatewayClientError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to create owed actions',
        {
          error: mockError.message,
        },
      );
    });
  });

  describe('getPagesByCompanyId', () => {
    const mockCompanyId = 'company-123';
    const mockPages = createMockScrapedPages(3);

    it('should fetch pages by company ID successfully', async () => {
      // Mock GraphQL response
      mockGraphQLClient.request.mockResolvedValueOnce({
        scrapedPages: mockPages,
      });

      // Call the method
      const result =
        await seoApiGatewayClient.getPagesByCompanyId(mockCompanyId);

      // Verify GraphQL request
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        GET_PAGES_BY_COMPANY,
        { companyId: mockCompanyId },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      // Verify result
      expect(result).toEqual(mockPages);
    });

    it('should handle errors when fetching pages by company ID', async () => {
      // Mock GraphQL error
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        seoApiGatewayClient.getPagesByCompanyId(mockCompanyId),
      ).rejects.toThrow(SeoApiGatewayClientError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch pages by company ID',
        {
          error: mockError.message,
          companyId: mockCompanyId,
        },
      );
    });
  });

  describe('getAction', () => {
    const mockActionId = 'action-123';
    const mockAction = createMockScheduledActions(1)[0];

    it('should fetch a scheduled action by ID', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        scheduledAction: mockAction,
      });

      const result = await seoApiGatewayClient.getAction(mockActionId);

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        SCHEDULED_ACTION,
        { id: mockActionId },
        { 'x-lp-api-key': mockM2mSuperApiKey },
      );
      expect(result).toEqual(mockAction);
    });

    it('should return null when action is not found', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        scheduledAction: null,
      });

      const result = await seoApiGatewayClient.getAction(mockActionId);

      expect(result).toBeNull();
    });

    it('should handle errors from the GraphQL client', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      await expect(seoApiGatewayClient.getAction(mockActionId)).rejects.toThrow(
        SeoApiGatewayClientError,
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch scheduled action',
        { error: mockError.message },
      );
    });
  });

  describe('getUpcomingActions', () => {
    const mockCompanyId = 'company-123';
    const mockWorkflowType = WorkflowType.BLOG;
    const mockActions = createMockScheduledActions(2);

    it('should fetch upcoming actions successfully', async () => {
      // Mock GraphQL response
      mockGraphQLClient.request.mockResolvedValueOnce({
        upcomingActions: mockActions,
      });

      // Call the method
      const result = await seoApiGatewayClient.getUpcomingActions(
        mockCompanyId,
        mockWorkflowType,
      );

      // Verify GraphQL request
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        UPCOMING_ACTIONS,
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle errors when fetching upcoming actions', async () => {
      // Mock GraphQL error
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      // Call the method and expect it to throw
      await expect(
        seoApiGatewayClient.getUpcomingActions(mockCompanyId, mockWorkflowType),
      ).rejects.toThrow(SeoApiGatewayClientError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch upcoming actions',
        {
          error: mockError.message,
        },
      );
    });
  });

  describe('getSurfacedActions', () => {
    const mockScheduledToPublishedAt = '2023-01-01T00:00:00Z';
    const mockActions = createMockScheduledActions(3);

    it('should fetch surfaced actions successfully', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        surfacedActions: mockActions,
      });

      const result = await seoApiGatewayClient.getSurfacedActions(
        mockScheduledToPublishedAt,
      );

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        GET_SURFACED_ACTIONS,
        { scheduledToBePublishedAt: mockScheduledToPublishedAt },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(result).toEqual(mockActions);
    });

    it('should handle errors when fetching surfaced actions', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);
      await expect(
        seoApiGatewayClient.getSurfacedActions(mockScheduledToPublishedAt),
      ).rejects.toThrow(SeoApiGatewayClientError);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch surfaced actions',
        {
          error: mockError.message,
          scheduledToBePublishedAt: mockScheduledToPublishedAt,
        },
      );
    });
  });

  describe('getAllActionsToSurface', () => {
    it('should fetch all actions to surface successfully', async () => {
      const mockActions = [
        {
          id: 'action-1',
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          surfacedAt: null,
          scheduledToBePublishedAt: null,
          scheduledToBeSurfacedAt: new Date().toISOString(),
          publishedAt: null,
          contentPayload: null,
          generationPayload: null,
          failureReason: null,
          executionName: null,
          executionArn: null,
        },
        {
          id: 'action-2',
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          surfacedAt: null,
          scheduledToBePublishedAt: null,
          scheduledToBeSurfacedAt: new Date().toISOString(),
          publishedAt: null,
          contentPayload: null,
          generationPayload: null,
          failureReason: null,
          executionName: null,
          executionArn: null,
        },
      ];

      const expectedGroupedActions = [
        {
          companyId: 'company-1',
          actions: mockActions,
        },
      ];

      // Mock the paginated response
      mockGraphQLClient.request.mockResolvedValueOnce({
        findScheduledActionsPaginated: {
          data: mockActions,
          hasNextPage: false,
          currentPage: 1,
          totalItems: 2,
        },
      });

      const result = await seoApiGatewayClient.getAllActionsToSurface();

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        expect.anything(),
        {
          status: Status.SURFACING_PENDING,
          page: 1,
          limit: 100,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(result).toEqual(expectedGroupedActions);
    });

    it('should handle pagination when there are multiple pages', async () => {
      const page1Actions = [
        {
          id: 'action-1',
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          surfacedAt: null,
          scheduledToBePublishedAt: null,
          scheduledToBeSurfacedAt: new Date().toISOString(),
          publishedAt: null,
          contentPayload: null,
          generationPayload: null,
          failureReason: null,
          executionName: null,
          executionArn: null,
        },
      ];

      const page2Actions = [
        {
          id: 'action-2',
          companyId: 'company-2',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          surfacedAt: null,
          scheduledToBePublishedAt: null,
          scheduledToBeSurfacedAt: new Date().toISOString(),
          publishedAt: null,
          contentPayload: null,
          generationPayload: null,
          failureReason: null,
          executionName: null,
          executionArn: null,
        },
      ];

      // Mock first page response
      mockGraphQLClient.request.mockResolvedValueOnce({
        findScheduledActionsPaginated: {
          data: page1Actions,
          hasNextPage: true,
          currentPage: 1,
          totalItems: 2,
        },
      });

      // Mock second page response
      mockGraphQLClient.request.mockResolvedValueOnce({
        findScheduledActionsPaginated: {
          data: page2Actions,
          hasNextPage: false,
          currentPage: 2,
          totalItems: 2,
        },
      });

      const result = await seoApiGatewayClient.getAllActionsToSurface();

      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(2);
      expect(mockGraphQLClient.request).toHaveBeenNthCalledWith(
        1,
        expect.anything(),
        {
          status: Status.SURFACING_PENDING,
          page: 1,
          limit: 100,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(mockGraphQLClient.request).toHaveBeenNthCalledWith(
        2,
        expect.anything(),
        {
          status: Status.SURFACING_PENDING,
          page: 2,
          limit: 100,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      const expectedGroupedActions = [
        {
          companyId: 'company-1',
          actions: page1Actions,
        },
        {
          companyId: 'company-2',
          actions: page2Actions,
        },
      ];

      expect(result).toEqual(expectedGroupedActions);
    });

    it('should handle empty response', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        findScheduledActionsPaginated: {
          data: [],
          hasNextPage: false,
          currentPage: 1,
          totalItems: 0,
        },
      });

      const result = await seoApiGatewayClient.getAllActionsToSurface();
      expect(result).toEqual([]);
    });

    it('should handle errors when fetching actions to surface', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      await expect(
        seoApiGatewayClient.getAllActionsToSurface(),
      ).rejects.toThrow(SeoApiGatewayClientError);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to fetch all actions to surface',
        {
          error: mockError.message,
        },
      );
    });
  });

  describe('fetchAllPaginatedActions', () => {
    it('should fetch all paginated actions successfully', async () => {
      const mockActions = [
        {
          id: 'action-1',
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          status: Status.SURFACING_PENDING,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          surfacedAt: null,
          scheduledToBePublishedAt: null,
          scheduledToBeSurfacedAt: new Date().toISOString(),
          publishedAt: null,
          contentPayload: null,
          generationPayload: null,
          failureReason: null,
          executionName: null,
          executionArn: null,
        },
      ];

      mockGraphQLClient.request.mockResolvedValueOnce({
        findScheduledActionsPaginated: {
          data: mockActions,
          hasNextPage: false,
          currentPage: 1,
          totalItems: 1,
        },
      });

      const result = await seoApiGatewayClient.fetchAllPaginatedActions({
        status: Status.SURFACING_PENDING,
      });

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        expect.anything(),
        {
          status: Status.SURFACING_PENDING,
          page: 1,
          limit: 100,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(result).toEqual(mockActions);
    });

    it('should handle multiple pages with different filters', async () => {
      const page1Actions = [
        { id: 'action-1', companyId: 'company-1' } as ScheduledAction,
      ];
      const page2Actions = [
        { id: 'action-2', companyId: 'company-2' } as ScheduledAction,
      ];

      mockGraphQLClient.request
        .mockResolvedValueOnce({
          findScheduledActionsPaginated: {
            data: page1Actions,
            hasNextPage: true,
            currentPage: 1,
            totalItems: 2,
          },
        })
        .mockResolvedValueOnce({
          findScheduledActionsPaginated: {
            data: page2Actions,
            hasNextPage: false,
            currentPage: 2,
            totalItems: 2,
          },
        });

      const result = await seoApiGatewayClient.fetchAllPaginatedActions({
        status: Status.SURFACING_PENDING,
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
      });

      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(2);
      expect(mockGraphQLClient.request).toHaveBeenNthCalledWith(
        1,
        expect.anything(),
        {
          status: Status.SURFACING_PENDING,
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          page: 1,
          limit: 100,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(result).toEqual([...page1Actions, ...page2Actions]);
    });
  });

  describe('storeSEODraft', () => {
    const mockCompanyId = 'company-123';
    const mockActionId = 'action-456';
    const mockScraperResult: ScraperResult = {
      firecrawlId: 'scrape-1',
      companyId: mockCompanyId,
      url: 'https://example.com',
      type: ScrapedPageType.HOMEPAGE,
      markdown: '# Test',
      metaTitle: 'Test Title',
      metaDescription: 'Test Description',
      mainHeading: {
        section_id: 'test-section',
        element_id: 'test-element-1',
        tag: 'h1',
        index: 0,
        content: 'Test Heading',
      },
      mediaId: 'media-1',
    };
    const mockKeywords = 'test,seo,keywords';
    const mockRecommendations = {
      metaTitle: {
        currentValue: 'Test Title',
        recommendationValue: 'Improved Test Title',
        reasoning: 'This is a test reasoning',
      },
      metaDescription: {
        currentValue: 'Test Description',
        recommendationValue: 'Improved Test Description',
        reasoning: 'This is a test reasoning',
      },
      mainHeading: {
        currentValue: 'Test Heading',
        recommendationValue: 'Improved Test Heading',
        reasoning: 'This is a test reasoning',
      },
    };

    it('should store SEO draft successfully', async () => {
      const mockResponse = {
        savedKeyword: {
          id: 'keyword-1',
          keyword: 'test',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        savedRecommendations: [],
        savedScrape: null,
        savedPage: null,
        savedPageKeyword: null,
        savedGroup: null,
      };
      mockGraphQLClient.request.mockResolvedValueOnce({
        storeSEODraft: mockResponse,
      });

      const result = await seoApiGatewayClient.storeSEODraft(
        mockCompanyId,
        mockActionId,
        mockScraperResult,
        mockKeywords,
        mockRecommendations,
      );
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        expect.anything(),
        {
          companyId: mockCompanyId,
          actionId: mockActionId,
          scraperResult: mockScraperResult,
          keywords: mockKeywords,
          recommendations: mockRecommendations,
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle errors when storing SEO draft', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      await expect(
        seoApiGatewayClient.storeSEODraft(
          mockCompanyId,
          mockActionId,
          mockScraperResult,
          mockKeywords,
          mockRecommendations,
        ),
      ).rejects.toThrow(SeoApiGatewayClientError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to store SEO Draft',
        {
          companyId: mockCompanyId,
          actionId: mockActionId,
          error: mockError.message,
        },
      );
    });
  });

  describe('getPageById', () => {
    const mockPageId = 'page-123';
    const mockPage = {
      id: mockPageId,
      url: 'https://example.com/page',
      companyId: 'company-123',
    };

    it('should fetch page by ID successfully', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        scrapedPage: mockPage,
      });

      const result = await seoApiGatewayClient.getPageById(mockPageId);

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        GET_SCRAPED_PAGE_BY_ID,
        { id: mockPageId },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );

      expect(result).toEqual(mockPage);
    });

    it('should return null when page is not found', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce({
        scrapedPage: null,
      });

      const result = await seoApiGatewayClient.getPageById(mockPageId);

      expect(result).toBeNull();
    });

    it('should handle errors when fetching page by ID', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      await expect(seoApiGatewayClient.getPageById(mockPageId)).rejects.toThrow(
        SeoApiGatewayClientError,
      );
    });
  });

  describe('updatePageKeywordRank', () => {
    const mockPageKeywordId = 'page-keyword-123';
    const mockCurrentRank = 5;
    const mockResponse = {
      updatePageKeywordRank: {
        id: mockPageKeywordId,
        currentRank: mockCurrentRank,
        rankCheckedAt: new Date().toISOString(),
      },
    };

    it('should update page keyword rank successfully', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce(mockResponse);

      await seoApiGatewayClient.updatePageKeywordRank(
        mockPageKeywordId,
        mockCurrentRank,
      );

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        UPDATE_PAGE_KEYWORD_RANK,
        {
          input: {
            id: mockPageKeywordId,
            currentRank: mockCurrentRank,
          },
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
    });

    it('should handle errors when updating page keyword rank', async () => {
      const mockError = new Error('GraphQL error');
      mockGraphQLClient.request.mockRejectedValueOnce(mockError);

      await expect(
        seoApiGatewayClient.updatePageKeywordRank(
          mockPageKeywordId,
          mockCurrentRank,
        ),
      ).rejects.toThrow(SeoApiGatewayClientError);
    });

    it('should handle null rank value', async () => {
      mockGraphQLClient.request.mockResolvedValueOnce(mockResponse);

      await seoApiGatewayClient.updatePageKeywordRank(mockPageKeywordId, null);

      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        UPDATE_PAGE_KEYWORD_RANK,
        {
          input: {
            id: mockPageKeywordId,
            currentRank: null,
          },
        },
        expect.objectContaining({ 'x-lp-api-key': mockM2mSuperApiKey }),
      );
    });
  });
});
