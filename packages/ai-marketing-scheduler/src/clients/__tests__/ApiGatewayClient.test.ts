import { GraphQLClient } from 'graphql-request';

import { ContextLogger } from '../../logger/contextLogger';
import {
  createMockContextLogger,
  createMockEntitlements,
  createMockGraphQLClient,
  createMockWebsites,
} from '../../utils/testUtils';
import { ApiGatewayClient } from '../ApiGatewayClient';

jest.mock('../../utils/getGWAuthToken', () => ({
  getGWAuthToken: jest.fn().mockReturnValue('mock-token'),
}));

describe('ApiGatewayClient', () => {
  let client: ApiGatewayClient;
  let mockLogger: jest.Mocked<ContextLogger>;
  let mockGraphQLClient: jest.Mocked<GraphQLClient>;

  const mockEntitlement = createMockEntitlements(1, [
    {
      companyId: 'test-company-id',
      websiteUrl: 'existing-website.com',
    },
  ])[0];

  beforeEach(() => {
    mockLogger = createMockContextLogger();
    mockGraphQLClient = createMockGraphQLClient();

    client = new ApiGatewayClient(
      'super-user',
      'api-key',
      mockGraphQLClient,
      mockLogger,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('fetchAllWebsites', () => {
    it('should fetch websites and update entitlement websiteUrl', async () => {
      const mockWebsites = createMockWebsites(2, [
        {
          name: 'Test Website',
          hostname: 'test-website.com',
        },
        {
          name: 'Another Website',
          hostname: 'another-website.com',
        },
      ]);

      mockGraphQLClient.request.mockResolvedValue({
        websites: mockWebsites,
      });

      const result = await client.fetchAllWebsites([mockEntitlement]);

      expect(result).toHaveLength(1);
      expect(result[0].websiteUrl).toBe('test-website.com');
      expect(mockGraphQLClient.request).toHaveBeenCalledWith(
        expect.stringContaining('query companyWebsite'),
        { companyId: 'test-company-id' },
        { Authorization: 'mock-token' },
      );
    });

    it('should use existing website if it matches one of the fetched websites', async () => {
      const mockWebsites = createMockWebsites(2, [
        {
          name: 'Existing Website',
          hostname: 'existing-website.com',
        },
        {
          name: 'Another Website',
          hostname: 'another-website.com',
        },
      ]);

      mockGraphQLClient.request.mockResolvedValue({
        websites: mockWebsites,
      });

      const result = await client.fetchAllWebsites([mockEntitlement]);

      expect(result).toHaveLength(1);
      expect(result[0].websiteUrl).toBe('existing-website.com');
    });

    it('should set websiteUrl to empty string when no websites are found', async () => {
      mockGraphQLClient.request.mockResolvedValue({
        websites: [],
      });

      const result = await client.fetchAllWebsites([mockEntitlement]);

      expect(result).toHaveLength(0);
    });

    it('should handle errors and filter out failed entitlements', async () => {
      mockGraphQLClient.request.mockRejectedValue(new Error('API Error'));

      const result = await client.fetchAllWebsites([mockEntitlement]);

      expect(result).toHaveLength(0);
    });

    it('should handle multiple entitlements with concurrent requests', async () => {
      const entitlements = createMockEntitlements(3).map((e, i) => ({
        ...e,
        companyId: `company-${i + 1}`,
        units: i + 1,
        websiteUrl: '',
      }));

      const mockWebsites = createMockWebsites(1, [
        {
          name: 'Test Website',
          hostname: 'test-website.com',
        },
      ]);

      mockGraphQLClient.request.mockResolvedValue({
        websites: mockWebsites,
      });

      const result = await client.fetchAllWebsites(entitlements);

      expect(result).toHaveLength(3);
      expect(mockGraphQLClient.request).toHaveBeenCalledTimes(3);
      result.forEach(entitlement => {
        expect(entitlement.websiteUrl).toBe('test-website.com');
      });
    });

    it('should filter out entitlements that failed to fetch websites', async () => {
      const entitlements = createMockEntitlements(2).map((e, i) => ({
        ...e,
        companyId: i === 0 ? 'company-success' : 'company-fail',
        units: i + 1,
        websiteUrl: '',
      }));

      const mockWebsites = createMockWebsites(1, [
        {
          name: 'Test Website',
          hostname: 'test-website.com',
        },
      ]);

      mockGraphQLClient.request
        .mockResolvedValueOnce({ websites: mockWebsites })
        .mockRejectedValueOnce(new Error('API Error'));

      const result = await client.fetchAllWebsites(entitlements);

      expect(result).toHaveLength(1);
      expect(result[0].companyId).toBe('company-success');
      expect(result[0].websiteUrl).toBe('test-website.com');
    });
  });
});
