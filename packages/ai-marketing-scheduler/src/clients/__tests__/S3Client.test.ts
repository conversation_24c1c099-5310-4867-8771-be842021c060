import { getConfig } from 'config';

import { ContextLogger } from '../../logger/contextLogger';
import {
  createMockConfig,
  createMockContextLogger,
} from '../../utils/testUtils';
import { S3Error } from '../errors/S3Error';
import { S3Client } from '../S3Client';

const originalFetch = global.fetch;

jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
}));

jest.mock('config', () => ({
  getConfig: jest.fn(),
}));

const mockAWSS3Client = {
  send: jest.fn().mockResolvedValue({}),
};

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(() => mockAWSS3Client),
  PutObjectCommand: jest.fn(),
}));

beforeEach(() => {
  mockAWSS3Client.send.mockClear();
});

describe('S3Client', () => {
  const mockConfig = createMockConfig();
  let mockLogger: jest.Mocked<ContextLogger>;
  let s3Client: S3Client;

  beforeEach(() => {
    mockLogger = createMockContextLogger();
    (getConfig as jest.Mock).mockReturnValue(mockConfig);
    s3Client = new S3Client(mockConfig.screenshotsS3, mockLogger);
    global.fetch = jest.fn(url => {
      if (url === 'https://example.com/image.jpg') {
        return Promise.resolve(
          new Response(Buffer.from('mocked-image-data'), { status: 200 }),
        );
      }
      return Promise.resolve(
        new Response(null, {
          status: 500,
          statusText: 'Internal Server Error',
        }),
      );
    });
  });

  afterEach(() => {
    global.fetch = originalFetch;
  });

  describe('uploadFromUrl', () => {
    it('should upload data from a URL to S3', async () => {
      const url = 'https://example.com/image.jpg';
      const key = 'test-key';

      const result = await s3Client.uploadFromUrl(url, key);

      expect(global.fetch).toHaveBeenCalledWith(url);
      expect(result).toBeDefined();
    });

    it('should throw an S3Error if fetching the URL fails', async () => {
      const url = 'https://invalid-url.com';
      const key = 'test-key';

      await expect(s3Client.uploadFromUrl(url, key)).rejects.toThrow(S3Error);
    });
  });

  describe('uploadObject', () => {
    it('should upload an object to S3', async () => {
      const bucketName = mockConfig.screenshotsS3.bucketName;
      const key = 'test-key';
      const body = Buffer.from('test-data');

      const result = await s3Client.uploadObject(bucketName, key, body);

      expect(mockAWSS3Client.send).toHaveBeenCalledTimes(1);
      expect(result).toBeDefined();
    });

    it('should throw an S3Error if uploading fails', async () => {
      const bucketName = mockConfig.screenshotsS3.bucketName;
      const key = 'test-key';
      const body = Buffer.from('test-data');

      mockAWSS3Client.send.mockRejectedValueOnce(new Error('Upload failed'));

      await expect(
        s3Client.uploadObject(bucketName, key, body),
      ).rejects.toThrow(S3Error);
    });
  });
});
