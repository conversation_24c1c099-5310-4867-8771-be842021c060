import { ContextLogger } from '../../logger/contextLogger';
import {
  createMockConfig,
  createMockContextLogger,
} from '../../utils/testUtils';
import { DataForSeoClient } from '../DataForSeoClient';

jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
}));

const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('DataForSeoClient', () => {
  let mockLogger: jest.Mocked<ContextLogger>;
  let dataForSeoClient: DataForSeoClient;
  const mockConfig = createMockConfig().dataForSeo;

  beforeEach(() => {
    mockLogger = createMockContextLogger();
    dataForSeoClient = new DataForSeoClient(mockConfig, mockLogger);
    jest.clearAllMocks();
  });

  afterEach(() => {});

  describe('enqueueTasks', () => {
    const mockKeywords = [
      { pageId: 'page-1', keyword: 'test keyword 1' },
      { pageId: 'page-2', keyword: 'test keyword 2' },
    ];

    it('should successfully enqueue tasks sequentially with delay', async () => {
      const mockResponse1 = {
        tasks: [{ id: 'task-1', status_code: 20000 }],
      };
      const mockResponse2 = {
        tasks: [{ id: 'task-2', status_code: 20000 }],
      };

      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse1),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResponse2),
        });

      const startTime = Date.now();
      const result = await dataForSeoClient.enqueueTasks(mockKeywords);
      const endTime = Date.now();

      // Should be called twice, once for each keyword
      expect(mockFetch).toHaveBeenCalledTimes(2);

      // Verify at least 100ms delay between tasks
      expect(endTime - startTime).toBeGreaterThanOrEqual(100);

      // First call
      expect(mockFetch).toHaveBeenNthCalledWith(
        1,
        `${mockConfig.baseUrl}/v3/serp/google/organic/task_post`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${btoa(`${mockConfig.username}:${mockConfig.password}`)}`,
          },
          body: JSON.stringify([
            {
              keyword: 'test keyword 1',
              location_name: 'United States',
              language_name: 'English',
              device: 'desktop',
              tag: 'page-1',
              postback_url: mockConfig.postbackUrl,
              postback_data: 'regular',
            },
          ]),
        },
      );

      // Second call
      expect(mockFetch).toHaveBeenNthCalledWith(
        2,
        `${mockConfig.baseUrl}/v3/serp/google/organic/task_post`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${btoa(`${mockConfig.username}:${mockConfig.password}`)}`,
          },
          body: JSON.stringify([
            {
              keyword: 'test keyword 2',
              location_name: 'United States',
              language_name: 'English',
              device: 'desktop',
              tag: 'page-2',
              postback_url: mockConfig.postbackUrl,
              postback_data: 'regular',
            },
          ]),
        },
      );

      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: 'task-1' },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: 'task-2' },
      ]);
    });

    it('should handle HTTP errors', async () => {
      // First request fails, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          text: () => Promise.resolve('Unauthorized'),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks: [{ id: 'task-2', status_code: 20000 }],
            }),
        });

      const result = await dataForSeoClient.enqueueTasks(mockKeywords);

      // Should return results with undefined taskId for failed request
      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: undefined },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: 'task-2' },
      ]);
    });

    it('should handle network errors', async () => {
      // First request fails with network error, second succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks: [{ id: 'task-2', status_code: 20000 }],
            }),
        });

      const result = await dataForSeoClient.enqueueTasks(mockKeywords);

      // Should return results with undefined taskId for failed request
      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: undefined },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: 'task-2' },
      ]);
    });

    it('should handle invalid response format', async () => {
      // First request returns invalid response, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({}),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks: [{ id: 'task-2', status_code: 20000 }],
            }),
        });

      const result = await dataForSeoClient.enqueueTasks(mockKeywords);

      // Should return results with undefined taskId for invalid response
      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: undefined },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: 'task-2' },
      ]);
    });

    it('should handle empty keywords array', async () => {
      const result = await dataForSeoClient.enqueueTasks([]);

      expect(mockFetch).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it('should handle tasks with errors in response', async () => {
      // First request returns error in tasks, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks_error: 1,
              tasks: [
                {
                  id: null,
                  status_code: 40000,
                  status_message: 'Invalid keyword',
                },
              ],
            }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks: [{ id: 'task-2', status_code: 20000 }],
            }),
        });

      const result = await dataForSeoClient.enqueueTasks(mockKeywords);

      // Should return results with undefined taskId for error response
      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: undefined },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: 'task-2' },
      ]);
    });

    it('should handle all tasks failing', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          text: () => Promise.resolve('Server error'),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          text: () => Promise.resolve('Server error'),
        });

      const result = await dataForSeoClient.enqueueTasks(mockKeywords);

      // Should return results with all undefined taskIds
      expect(result).toEqual([
        { pageId: 'page-1', keyword: 'test keyword 1', taskId: undefined },
        { pageId: 'page-2', keyword: 'test keyword 2', taskId: undefined },
      ]);
    });

    it('should verify delay between requests', async () => {
      // Create 3 keywords to test delay
      const threeKeywords = [
        { pageId: 'page-1', keyword: 'test keyword 1' },
        { pageId: 'page-2', keyword: 'test keyword 2' },
        { pageId: 'page-3', keyword: 'test keyword 3' },
      ];

      mockFetch.mockImplementation(() =>
        Promise.resolve({
          ok: true,
          json: () =>
            Promise.resolve({
              tasks: [{ id: 'task-id', status_code: 20000 }],
            }),
        }),
      );

      const startTime = Date.now();
      await dataForSeoClient.enqueueTasks(threeKeywords);
      const endTime = Date.now();

      // Should be called 3 times
      expect(mockFetch).toHaveBeenCalledTimes(3);

      // Should have at least 200ms delay (2 delays of 100ms each)
      expect(endTime - startTime).toBeGreaterThanOrEqual(200);
    });

    it('should not add delay after the last request', async () => {
      const singleKeyword = [{ pageId: 'page-1', keyword: 'test keyword' }];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () =>
          Promise.resolve({
            tasks: [{ id: 'task-1', status_code: 20000 }],
          }),
      });

      const startTime = Date.now();
      await dataForSeoClient.enqueueTasks(singleKeyword);
      const endTime = Date.now();

      // Should complete quickly without unnecessary delay
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
