import FirecrawlApp, {
  type ActionsResult,
  type FirecrawlAppConfig,
  type MapResponse,
  type ScrapeResponse,
} from '@mendable/firecrawl-js';

import { ContextLogger } from '../../logger/contextLogger';
import { createMockContextLogger } from '../../utils/testUtils';
import { FireCrawlError } from '../errors/FireCrawlError';
import { FirecrawlClient } from '../FirecrawlClient';

// Mock the LoggerFactory first
jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
}));

// Mock FirecrawlApp
jest.mock('@mendable/firecrawl-js');

const mockGetMainHeadingScript =
  'return document.querySelector("h1")?.textContent;';

describe('FirecrawlClient', () => {
  let mockFirecrawlAppInstance: jest.Mocked<FirecrawlApp>;
  let mockLogger: jest.Mocked<ContextLogger>;
  let firecrawlClient: FirecrawlClient;
  let firecrawlConfig: FirecrawlAppConfig;

  afterEach(() => {
    jest.clearAllMocks();
  });

  beforeEach(() => {
    // Set up test mocks including logger
    mockLogger = createMockContextLogger();

    // Set up mock FirecrawlApp instance with all required methods
    mockFirecrawlAppInstance = {
      mapUrl: jest.fn(),
      scrapeUrl: jest.fn(),
    } as unknown as jest.Mocked<FirecrawlApp>;

    (FirecrawlApp as jest.Mock).mockImplementation(
      () => mockFirecrawlAppInstance,
    );

    firecrawlConfig = {
      apiKey: 'test-api-key',
    };

    firecrawlClient = new FirecrawlClient(firecrawlConfig, mockLogger);
  });

  describe('mapUrl', () => {
    const testUrl = 'https://example.com';
    const mockLinks = ['/page1', '/page2'];

    it('should return links when mapping is successful', async () => {
      mockFirecrawlAppInstance.mapUrl.mockResolvedValueOnce({
        success: true,
        links: mockLinks,
      } as unknown as MapResponse);

      const result = await firecrawlClient.mapUrl(testUrl);

      expect(result).toEqual(mockLinks);
      expect(mockFirecrawlAppInstance.mapUrl).toHaveBeenCalledWith(testUrl, {
        sitemapOnly: true,
      });
    });

    it('should handle different error scenarios', async () => {
      // Test API failure
      mockFirecrawlAppInstance.mapUrl.mockResolvedValueOnce({
        success: false,
        error: 'API Error',
      } as unknown as MapResponse);
      await expect(firecrawlClient.mapUrl(testUrl)).rejects.toThrow(
        FireCrawlError,
      );

      // Test missing links
      mockFirecrawlAppInstance.mapUrl.mockResolvedValueOnce({
        success: true,
        links: undefined,
      } as unknown as MapResponse);
      await expect(firecrawlClient.mapUrl(testUrl)).rejects.toThrow(
        'No links found',
      );
    });
  });

  describe('scrapeUrl', () => {
    const testUrl = 'https://example.com/page1';
    const mockMarkdown = '## Test Content';
    const mockScreenshot = 'https://example.com/screenshot.png';

    it('should handle scrape errors', async () => {
      // Test API failure
      mockFirecrawlAppInstance.scrapeUrl.mockResolvedValueOnce({
        success: false,
        error: 'Scrape failed',
      } as unknown as ScrapeResponse<unknown, ActionsResult>);
      await expect(
        firecrawlClient.scrapeUrl(testUrl, mockGetMainHeadingScript),
      ).rejects.toThrow(FireCrawlError);

      // Test network error
      const networkError = new Error('Network issue');
      mockFirecrawlAppInstance.scrapeUrl.mockRejectedValueOnce(networkError);
      await expect(
        firecrawlClient.scrapeUrl(testUrl, mockGetMainHeadingScript),
      ).rejects.toThrow(FireCrawlError);
    });

    it('should throw FireCrawlError if no markdown content is found', async () => {
      mockFirecrawlAppInstance.scrapeUrl.mockResolvedValueOnce({
        success: true,
        markdown: undefined,
        screenshot: mockScreenshot,
        actions: {
          javascriptReturns: [],
        },
        metadata: { scrapeId: 'test-scrape-id' },
      } as unknown as ScrapeResponse<unknown, ActionsResult>);

      await expect(
        firecrawlClient.scrapeUrl(testUrl, mockGetMainHeadingScript),
      ).rejects.toThrow('No markdown content found');
    });

    it('should throw FireCrawlError if parsing javascriptReturns fails due to invalid JSON', async () => {
      mockFirecrawlAppInstance.scrapeUrl.mockResolvedValueOnce({
        success: true,
        markdown: mockMarkdown,
        screenshot: mockScreenshot,
        actions: {
          javascriptReturns: [
            { type: 'executeJavascript', value: 'invalid json' },
          ] as unknown as ActionsResult['javascriptReturns'],
          screenshots: [],
          scrapes: [],
        },
        metadata: { scrapeId: 'test-scrape-id' },
      } as unknown as ScrapeResponse<unknown, ActionsResult>);

      await expect(
        firecrawlClient.scrapeUrl(testUrl, mockGetMainHeadingScript),
      ).rejects.toThrow('Error parsing javascript returns');
    });
  });
});
