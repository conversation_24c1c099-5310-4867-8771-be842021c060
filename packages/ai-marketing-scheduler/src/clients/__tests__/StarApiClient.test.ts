import { StarApiConfig } from 'src/config';
import { notifyStarApiFailure } from 'src/utils/slack';

import { ContextLogger } from '../../logger/contextLogger';
import { createMockContextLogger } from '../../utils/testUtils';
import { StarApiError } from '../errors/StarApiError';
import { StarApiPayload } from '../interfaces/IStarApiClient';
import { StarApiClient } from '../StarApiClient';

// Mock fetch globally
global.fetch = jest.fn();

// Mock the slack notification function
jest.mock('src/utils/slack', () => ({
  notifyStarApiFailure: jest.fn(),
}));

describe('StarApiClient', () => {
  let client: StarApiClient;
  let mockLogger: jest.Mocked<ContextLogger>;
  let mockConfig: StarApiConfig;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockReset();
    (notifyStarApiFailure as jest.Mock).mockReset();

    // Create mock logger
    mockLogger = createMockContextLogger();

    // Create mock config
    mockConfig = {
      url: 'https://star-api.example.com',
    };

    // Create client
    client = new StarApiClient(mockConfig, mockLogger);
  });

  describe('updateElement', () => {
    const validPayload: StarApiPayload = {
      url: 'https://example.com/page',
      input: {
        elementProperty: 'title',
        elementTag: 'meta',
        newContent: 'New Title',
        oldContent: 'Old Title',
      },
      debug: false,
    };

    it('should successfully update an element', async () => {
      const mockResponse = {
        message: 'Website edit executed successfully',
        requestId: '1752165198695-e07e26f7-0a66-40fc-9dca-738d1c50645a',
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await client.updateElement(validPayload);

      expect(result.data).toEqual(mockResponse);
      expect(result.context).toEqual({
        starApiRequestId: mockResponse.requestId,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        'https://star-api.example.com/api/v1/star/editor',
        {
          method: 'PATCH',
          body: JSON.stringify(validPayload),
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        },
      );
    });

    it('should successfully update an element with companyId', async () => {
      const mockResponse = {
        message: 'Website edit executed successfully',
        requestId: '1752165198695-e07e26f7-0a66-40fc-9dca-738d1c50645a',
      };
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      });

      const result = await client.updateElement(validPayload, 'test-company');

      expect(result.data).toEqual(mockResponse);
      expect(result.context).toEqual({
        starApiRequestId: mockResponse.requestId,
      });

      expect(global.fetch).toHaveBeenCalledWith(
        'https://star-api.example.com/api/v1/star/editor',
        {
          method: 'PATCH',
          body: JSON.stringify(validPayload),
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        },
      );
    });

    it('should handle H1 update payload correctly', async () => {
      const h1Payload: StarApiPayload = {
        url: 'https://example.com/page',
        input: {
          elementIndex: 0,
          elementTag: 'h1',
          newContent: 'New H1',
          oldContent: 'Old H1',
        },
        debug: false,
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () =>
          Promise.resolve({
            message: 'Website edit executed successfully',
            requestId: '1752165198695-e07e26f7-0a66-40fc-9dca-738d1c50645a',
          }),
      });

      await client.updateElement(h1Payload);

      expect(global.fetch).toHaveBeenCalledWith(
        'https://star-api.example.com/api/v1/star/editor',
        expect.objectContaining({
          body: JSON.stringify(h1Payload),
        }),
      );
    });

    it('should handle API error responses', async () => {
      const errorResponse = {
        error: 'Element not found',
        details: 'No meta title tag found on the page',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        text: () => Promise.resolve(JSON.stringify(errorResponse)),
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        StarApiError,
      );
    });

    it('should send Slack notification on API error response', async () => {
      const errorResponse = {
        error: 'Element not found',
        details: 'No meta title tag found on the page',
        requestId: 'test-request-id',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        text: () => Promise.resolve(JSON.stringify(errorResponse)),
      });

      await expect(
        client.updateElement(validPayload, 'test-company'),
      ).rejects.toThrow(StarApiError);

      expect(notifyStarApiFailure).toHaveBeenCalledWith({
        error: expect.any(StarApiError),
        payload: validPayload,
        url: 'https://star-api.example.com/api/v1/star/editor',
        method: 'PATCH',
        status: 404,
        requestId: 'test-request-id',
        companyId: 'test-company',
        responseBody: errorResponse,
        actionId: undefined,
      });
    });

    it('should handle non-JSON error responses', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: () => Promise.resolve('Plain text error message'),
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        StarApiError,
      );
    });

    it('should handle network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network timeout'),
      );

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        'Failed to connect to STAR API: Network timeout',
      );
    });

    it('should send Slack notification on network error', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network timeout'),
      );

      await expect(
        client.updateElement(validPayload, 'test-company'),
      ).rejects.toThrow('Failed to connect to STAR API: Network timeout');

      expect(notifyStarApiFailure).toHaveBeenCalledWith({
        error: expect.any(StarApiError),
        payload: validPayload,
        url: 'https://star-api.example.com/api/v1/star/editor',
        method: 'PATCH',
        companyId: 'test-company',
        actionId: undefined,
      });
    });

    it('should handle unexpected errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => {
          throw new Error('JSON parse error');
        },
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        'JSON parse error',
      );
    });

    it('should handle all error types correctly', async () => {
      // Test StarApiError is preserved
      const starApiError = new StarApiError('Custom error', 400, {
        custom: 'data',
      });
      (global.fetch as jest.Mock).mockImplementationOnce(() => {
        throw starApiError;
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        starApiError,
      );
    });

    it('should handle error responses from successful API calls', async () => {
      const errorResponse = {
        error: 'Element change does not match live HTML',
        requestId: '1752164298125-d969a7b6-8626-42e2-9bcd-c3281878d76d',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(errorResponse),
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        'Element change does not match live HTML',
      );
    });

    it('should send Slack notification on successful API response with error', async () => {
      const errorResponse = {
        error: 'Element change does not match live HTML',
        requestId: '1752164298125-d969a7b6-8626-42e2-9bcd-c3281878d76d',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(errorResponse),
      });

      await expect(
        client.updateElement(validPayload, 'test-company'),
      ).rejects.toThrow('Element change does not match live HTML');

      expect(notifyStarApiFailure).toHaveBeenCalledWith({
        error: expect.any(StarApiError),
        payload: validPayload,
        url: 'https://star-api.example.com/api/v1/star/editor',
        method: 'PATCH',
        status: 200,
        requestId: '1752164298125-d969a7b6-8626-42e2-9bcd-c3281878d76d',
        companyId: 'test-company',
        responseBody: errorResponse,
        actionId: undefined,
      });
    });

    it('should include detailed error message from STAR API response', async () => {
      const errorResponse = {
        message: '"body.input.newContent" must be a string',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: () => Promise.resolve(JSON.stringify(errorResponse)),
      });

      await expect(client.updateElement(validPayload)).rejects.toThrow(
        'Bad request: "body.input.newContent" must be a string',
      );
    });

    it('should handle notification failures gracefully', async () => {
      const errorResponse = {
        error: 'Element not found',
        details: 'No meta title tag found on the page',
        requestId: 'test-request-id',
      };

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        text: () => Promise.resolve(JSON.stringify(errorResponse)),
      });

      // Mock notification failure
      (notifyStarApiFailure as jest.Mock).mockRejectedValueOnce(
        new Error('Slack webhook failed'),
      );

      await expect(
        client.updateElement(validPayload, 'test-company'),
      ).rejects.toThrow(StarApiError);

      // Verify notification was attempted
      expect(notifyStarApiFailure).toHaveBeenCalled();
    });
  });

  describe('constructor', () => {
    it('should construct the correct API URL', async () => {
      const customConfig: StarApiConfig = {
        url: 'https://custom-star-api.com',
      };

      const customClient = new StarApiClient(customConfig, mockLogger);

      const testPayload: StarApiPayload = {
        url: 'https://example.com/page',
        input: {
          elementProperty: 'title',
          elementTag: 'meta',
          newContent: 'New Title',
          oldContent: 'Old Title',
        },
        debug: false,
      };

      // We can't directly access private properties, but we can test
      // the behavior by mocking fetch and checking the URL used
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () =>
          Promise.resolve({
            message: 'Website edit executed successfully',
            requestId: '1752165198695-e07e26f7-0a66-40fc-9dca-738d1c50645a',
          }),
      });

      await customClient.updateElement(testPayload);

      expect(global.fetch).toHaveBeenCalledWith(
        'https://custom-star-api.com/api/v1/star/editor',
        expect.any(Object),
      );
    });
  });
});
