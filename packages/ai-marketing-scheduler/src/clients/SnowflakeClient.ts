import * as snowflake from 'snowflake-sdk';

import { SnowflakeConfig } from '../config/interfaces';
import { createContextLogger } from '../logger/contextLogger';

export class SnowflakeClient {
  private connectionPool!: snowflake.Pool<snowflake.Connection>;
  private initializingPromise: Promise<void> | null = null;
  private readonly maxRetries = 3;
  private readonly baseDelay = 1000; // 1 second

  constructor(
    private readonly config: SnowflakeConfig,
    private readonly logger: ReturnType<typeof createContextLogger>,
  ) {}

  async getConnectionPool(): Promise<snowflake.Pool<snowflake.Connection>> {
    if (!this.connectionPool) {
      this.logger.warn(
        'Connection pool not initialized, attempting to initialize...',
      );
      await this.createConnection();
    }

    this.logger.info(
      `[SnowflakeStatus] Total: ${this.connectionPool.size}, Idle: ${this.connectionPool.available}, In Use: ${this.connectionPool.borrowed}, Waiting: ${this.connectionPool.pending}`,
    );

    return this.connectionPool;
  }

  async createConnection() {
    try {
      // If initialization is already in progress, wait for it to complete to avoid multiple initialization
      if (this.initializingPromise) {
        return this.initializingPromise;
      }

      // Create a new initialization promise
      this.initializingPromise = (() => {
        return new Promise(resolve => {
          try {
            // Create pool with required parameters
            this.connectionPool = snowflake.createPool(
              {
                account: this.config.account,
                username: this.config.username,
                database: this.config.database,
                authenticator: 'SNOWFLAKE_JWT',
                privateKey: this.config.privateKey,
              },
              {
                min: 1,
                max: 10,
              },
            );
          } finally {
            // Clear the initializing promise regardless of success or failure
            this.initializingPromise = null;
            resolve();
          }
        });
      })();

      // Wait for initialization to complete
      return this.initializingPromise;
    } catch (error) {
      this.logger.error(
        'Failed to initialize Snowflake connection pool:',
        error,
      );
      throw error;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isTerminatedConnectionError(error: any): boolean {
    return error.message && error.message.includes('terminated connection');
  }

  async query(sql: string, params: string[] = []): Promise<any> {
    try {
      await this.getConnectionPool();

      return this.connectionPool.use(
        async (connection: snowflake.Connection) => {
          return new Promise((resolve, reject) => {
            connection.execute({
              sqlText: sql,
              binds: params,
              complete: (err: any, _: any, rows: any) => {
                if (err) {
                  return reject(
                    new Error(`Snowflake query error: ${err.message}`),
                  );
                } else {
                  return resolve(rows || []);
                }
              },
            });
          });
        },
      );
    } catch (error) {
      this.logger.error('Failed to query Snowflake:', error);
      throw error;
    }
  }

  async queryWithRetries(sql: string, params: string[] = []): Promise<any> {
    let lastError: Error | null = null;
    let hasRetried = false;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        await this.getConnectionPool();

        const result = await this.connectionPool.use(
          async (connection: snowflake.Connection) => {
            return new Promise((resolve, reject) => {
              connection.execute({
                sqlText: sql,
                binds: params,
                complete: (err: any, _: any, rows: any) => {
                  if (err) {
                    // Check if this is a terminated connection error
                    if (this.isTerminatedConnectionError(err)) {
                      return reject(new Error(err.message)); // Pass the original error to be handled by retry logic
                    }
                    return reject(
                      new Error(`Snowflake query error: ${err.message}`),
                    );
                  } else {
                    return resolve(rows || []);
                  }
                },
              });
            });
          },
        );

        // If we get here, the query succeeded
        if (hasRetried) {
          this.logger.info(
            `Snowflake query succeeded after retry (attempt ${attempt + 1}/${this.maxRetries + 1})`,
          );
        }

        return result;
      } catch (error) {
        lastError = error as Error;

        // Check if this is a terminated connection error and we haven't exceeded max retries
        if (
          this.isTerminatedConnectionError(lastError) &&
          attempt < this.maxRetries
        ) {
          hasRetried = true;
          const delayMs = this.baseDelay * Math.pow(2, attempt); // Exponential backoff
          this.logger.warn(
            `Snowflake query failed with terminated connection error (attempt ${attempt + 1}/${this.maxRetries + 1}). Retrying in ${delayMs}ms...`,
            { error: lastError.message },
          );
          await this.delay(delayMs);
          continue;
        }

        // If it's not a terminated connection error or we've exceeded retries, break
        break;
      }
    }

    // If we get here, all retries failed or it's not a retryable error
    this.logger.error('Failed to query Snowflake after all retries:', {
      error: lastError?.message || 'Unknown error',
    });
    throw new Error(
      `Snowflake query failed: ${lastError?.message || 'Unknown error'}`,
    );
  }

  async destroy() {
    try {
      if (this.connectionPool) {
        await this.connectionPool.drain();
      }
    } catch (error) {
      this.logger.error('Error draining connection pool:', error);
      throw error;
    }
  }
}
