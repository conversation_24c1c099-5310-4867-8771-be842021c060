import { DataForSeoConfig } from 'src/config';
import { DataForSeoResponse } from 'src/types/dataForSeo';

import { ContextLogger } from '../logger/contextLogger';
import { DataForSeoError } from './errors/DataForSeoError';

export class DataForSeoClient {
  private readonly baseUrl: string;

  constructor(
    private readonly config: DataForSeoConfig,
    private readonly logger: ContextLogger,
  ) {
    this.baseUrl = `${this.config.baseUrl}/v3/serp/google/organic`;
  }

  private async fetch(url: string, options: RequestInit = {}) {
    options.headers = {
      ...options.headers,
      'Content-Type': 'application/json',
      Authorization:
        'Basic ' + btoa(`${this.config.username}:${this.config.password}`),
    };
    this.logger.debug('Fetching DataForSeo');
    const res = await fetch(`${this.baseUrl}/${url}`, options);
    if (!res.ok) {
      const errorText = await res.text();
      throw new DataForSeoError(
        `HTTP error ${url} ${res.status}: ${errorText}`,
        undefined,
        {
          url,
          status: res.status,
          response: errorText,
        },
      );
    }
    return res;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async enqueueTasks(
    data: { pageId: string; keyword: string; taskId?: string }[],
  ) {
    this.logger.debug('Enqueuing tasks', { count: data.length });
    const startedAt = new Date();

    if (data.length === 0) {
      return [];
    }

    const results: Array<{
      pageId: string;
      keyword: string;
      taskId?: string;
    }> = [];

    // Process each task sequentially with a delay
    for (let i = 0; i < data.length; i++) {
      const item = data[i];

      try {
        const body = [
          {
            keyword: item.keyword,
            location_name: 'United States',
            language_name: 'English',
            device: 'desktop',
            tag: item.pageId,
            postback_url: this.config.postbackUrl,
            postback_data: 'regular',
          },
        ];

        this.logger.debug('Sending task to DataForSEO', {
          taskIndex: i + 1,
          totalTasks: data.length,
          keyword: item.keyword,
          pageId: item.pageId,
        });

        const res = await this.fetch(`task_post`, {
          method: 'POST',
          body: JSON.stringify(body),
        });

        const response = (await res.json()) as DataForSeoResponse<unknown>;

        if (
          !response.tasks ||
          response.tasks.length === 0 ||
          (response.tasks_error ?? 0) > 0
        ) {
          this.logger.error('Task enqueue failed', {
            taskIndex: i + 1,
            keyword: item.keyword,
            pageId: item.pageId,
            response,
            tasksError: response.tasks_error,
          });
          results.push({ ...item, taskId: undefined });
        } else {
          const taskId = response.tasks[0].id;
          this.logger.debug('Successfully enqueued task', {
            taskIndex: i + 1,
            keyword: item.keyword,
            pageId: item.pageId,
            taskId,
          });
          results.push({ ...item, taskId });
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        this.logger.error('Failed to enqueue task', {
          taskIndex: i + 1,
          keyword: item.keyword,
          pageId: item.pageId,
          error: errorMessage,
        });
        results.push({ ...item, taskId: undefined });
      }

      // Add delay between requests (except for the last one)
      if (i < data.length - 1) {
        await this.delay(100);
      }
    }

    const duration = new Date().getTime() - startedAt.getTime();
    const successful = results.filter(r => r.taskId !== undefined);
    const failed = results.filter(r => r.taskId === undefined);

    this.logger.debug('Completed enqueuing tasks', {
      total: data.length,
      successful: successful.length,
      failed: failed.length,
      duration,
      averageTimePerTask: duration / data.length,
    });

    if (failed.length > 0) {
      this.logger.warn('Some tasks failed to enqueue', {
        failedKeywords: failed.map(f => ({
          keyword: f.keyword,
          pageId: f.pageId,
        })),
      });
    }

    return results;
  }
}
