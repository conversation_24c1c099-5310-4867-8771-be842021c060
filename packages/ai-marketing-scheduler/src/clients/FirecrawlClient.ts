import FirecrawlApp, { type FirecrawlAppConfig } from '@mendable/firecrawl-js';

import { ContextLogger } from '../logger/contextLogger';
import { FireCrawlError } from './errors/FireCrawlError';

export class FirecrawlClient {
  private readonly firecrawlApp: FirecrawlApp;

  constructor(
    firecrawlConfig: FirecrawlAppConfig,
    private readonly logger: ContextLogger,
  ) {
    this.firecrawlApp = new FirecrawlApp(firecrawlConfig);
  }

  async mapUrl(url: string): Promise<string[]> {
    this.logger.info('Mapping URL', { url });
    const mapStartedAt = new Date();
    const mapResponse = await this.firecrawlApp.mapUrl(url, {
      sitemapOnly: true,
    });

    if (!mapResponse.success) {
      throw new FireCrawlError(mapResponse.error || 'Unknown Firecrawl error');
    }
    if (!mapResponse.links) {
      throw new FireCrawlError('No links found in map response');
    }
    this.logger.info('Successfully mapped URL', {
      url,
      linkCount: mapResponse.links.length,
      duration: new Date().getTime() - mapStartedAt.getTime(),
    });
    return mapResponse.links;
  }

  async scrapeUrl(url: string, jsString: string) {
    this.logger.info('Scraping URL', { url });
    const scrapeStartedAt = new Date();
    let scrapeResponse;
    try {
      scrapeResponse = await this.firecrawlApp.scrapeUrl(url, {
        formats: ['markdown', 'screenshot@fullPage'],
        onlyMainContent: true,
        actions: [
          {
            type: 'executeJavascript',
            script: jsString,
          },
          { type: 'scroll' },
          { type: 'wait', milliseconds: 3000 },
        ],
      });
    } catch (error) {
      throw new FireCrawlError(
        `Error during Firecrawl scrapeUrl for ${url}`,
        error instanceof Error ? error : undefined,
      );
    }

    if (!scrapeResponse.success) {
      throw new FireCrawlError(
        scrapeResponse.error || 'Unknown Firecrawl error',
      );
    }
    if (!scrapeResponse.markdown) {
      throw new FireCrawlError('No markdown content found');
    }
    this.logger.info('Successfully scraped URL', {
      url,
      firecrawlId: scrapeResponse.metadata?.scrapeId,
      duration: new Date().getTime() - scrapeStartedAt.getTime(),
    });
    if (
      !scrapeResponse.actions.javascriptReturns.length ||
      !scrapeResponse.actions.javascriptReturns[0].value ||
      scrapeResponse.actions.javascriptReturns[0].type !== 'object'
    ) {
      throw new FireCrawlError(`Error parsing javascript returns for ${url}`);
    }
    const javascriptReturn = scrapeResponse.actions.javascriptReturns[0]
      .value as object;
    if ('error' in javascriptReturn) {
      throw new FireCrawlError(javascriptReturn.error as string);
    }
    return {
      ...scrapeResponse,
      firecrawlId: scrapeResponse.metadata?.scrapeId,
    };
  }
}
