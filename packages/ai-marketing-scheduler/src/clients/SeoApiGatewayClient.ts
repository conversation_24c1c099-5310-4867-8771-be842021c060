import { GraphQLC<PERSON> } from 'graphql-request';

import { Seo<PERSON><PERSON>GatewayClientError } from '../errors';
import { APPLY_RECOMMENDATION } from '../graphql/mutations/applyRecommendation';
import { CREATE_OWED_ACTIONS } from '../graphql/mutations/createOwedActions';
import { UPDATE_SCHEDULED_ACTION } from '../graphql/mutations/scheduledActions';
import { STORE_SEO_DRAFT_MUTATION } from '../graphql/mutations/storeSEODraft';
import { UPDATE_PAGE_KEYWORD_RANK } from '../graphql/mutations/updatePageKeywordRank';
import { FIND_ACTIONS_PAGINATED } from '../graphql/queries/findActionsPaginated';
import { HOMEPAGE_FEED_QUERY } from '../graphql/queries/hompageFeed';
import { GET_PAGES_BY_COMPANY } from '../graphql/queries/pages';
import { GET_RECOMMENDATION_BY_ID } from '../graphql/queries/recommendationById';
import { GET_RECOMMENDATIONS_BY_GROUP_ID } from '../graphql/queries/recommendationsByGroupId';
import {
  GET_DRAFT_PENDING_ACTIONS,
  GET_SURFACED_ACTIONS,
  SCHEDULED_ACTION,
} from '../graphql/queries/scheduledActions';
import { GET_SCRAPED_PAGE_BY_ID } from '../graphql/queries/scrapedPage';
import { GET_SURFACED_AND_PUBLISHED_GROUPS } from '../graphql/queries/surfacedAndPublishedGroups';
import { GET_SURFACED_AND_PUBLISHED_GROUPS_FOR_RANKING } from '../graphql/queries/surfacedAndPublishedGroupsForRanking';
import { GET_SURFACED_AND_PUBLISHED_GROUPS_FOR_RANKING_PAGINATED } from '../graphql/queries/surfacedAndPublishedGroupsForRankingPaginated';
import { UPCOMING_ACTIONS } from '../graphql/queries/upcomingActions';
import { ScheduledAction, ScraperResult, Status, WorkflowType } from '../types';
import { ISeoApiGatewayClient } from './interfaces/ISeoApiGatewayClient';
import { ActionsByCompany } from './types/actions';
import {
  RecommendationsRaw,
  StoreSEODraftResponse,
  StoredRecommendation,
  StoredScrapedPage,
} from './types/seoApiGateway';
import { FeedFilters, HomepageFeed } from './types/seoApiGateway';
import { ContextLogger } from '../logger/contextLogger';

export class SeoApiGatewayClient implements ISeoApiGatewayClient {
  private readonly authHeaders: Record<string, string>;

  constructor(
    private readonly graphqlClient: GraphQLClient,
    private readonly logger: ContextLogger,
    private readonly m2mSuperApiKey: string,
    private readonly clientMarketingServiceUrl: string,
  ) {
    if (!m2mSuperApiKey) {
      throw new Error('M2M_SUPER_API_KEY is not set');
    }

    // Define auth headers once to be reused across all requests
    this.authHeaders = { 'x-lp-api-key': this.m2mSuperApiKey.split(',')[0] };
  }

  async getDraftPendingActions(): Promise<ScheduledAction[]> {
    try {
      const response = await this.graphqlClient.request<{
        draftPendingActions: ScheduledAction[];
      }>(GET_DRAFT_PENDING_ACTIONS, {}, this.authHeaders);

      this.logger.debug('Fetched draft pending actions', {
        count: response.draftPendingActions.length,
        actionIds: response.draftPendingActions.map(action => action.id),
      });

      return response.draftPendingActions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch draft pending actions', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch draft pending actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async createOwedActions(
    companyId: string,
    workflowType: WorkflowType,
    generationPayload?: object,
  ): Promise<ScheduledAction> {
    try {
      const variables = {
        companyId,
        workflowType,
        ...(generationPayload ? { generationPayload } : {}),
      };

      const response = await this.graphqlClient.request<{
        createOwedActions: ScheduledAction;
      }>(CREATE_OWED_ACTIONS, variables, this.authHeaders);

      this.logger.debug('Created owed actions', {
        actionId: response.createOwedActions.id,
      });

      return response.createOwedActions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to create owed actions', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to create owed actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getPagesByCompanyId(companyId: string): Promise<StoredScrapedPage[]> {
    try {
      const variables = { companyId };
      const response = await this.graphqlClient.request<{
        scrapedPages: StoredScrapedPage[];
      }>(GET_PAGES_BY_COMPANY, variables, this.authHeaders);

      this.logger.debug('Fetched pages by company ID', {
        companyId,
        count: response.scrapedPages.length,
        pageIds: response.scrapedPages.map(page => page.id),
      });

      return response.scrapedPages;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch pages by company ID', {
        error: errorMessage,
        companyId,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch pages by company ID: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }
  async getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]> {
    try {
      const variables = {
        companyId,
        workflowType,
      };

      const response = await this.graphqlClient.request<{
        upcomingActions: ScheduledAction[];
      }>(UPCOMING_ACTIONS, variables, this.authHeaders);

      this.logger.debug('Fetched upcoming actions', {
        count: response.upcomingActions.length,
        actionIds: response.upcomingActions.map(action => action.id),
      });

      return response.upcomingActions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch upcoming actions', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch upcoming actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getAction(actionId: string): Promise<ScheduledAction | null> {
    try {
      this.logger.debug('Fetching scheduled action', { actionId });

      const variables = { id: actionId };

      const response = await this.graphqlClient.request<{
        scheduledAction: ScheduledAction | null;
      }>(SCHEDULED_ACTION, variables, this.authHeaders);

      return response.scheduledAction;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch scheduled action', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch scheduled action: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getSurfacedActions(
    scheduledToBePublishedAt: string,
  ): Promise<ScheduledAction[]> {
    try {
      this.logger.debug(
        `Fetching surfaced actions ${scheduledToBePublishedAt}`,
      );

      const variables = {
        scheduledToBePublishedAt,
      };

      const response = await this.graphqlClient.request<{
        surfacedActions: ScheduledAction[];
      }>(GET_SURFACED_ACTIONS, variables, this.authHeaders);

      return response.surfacedActions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch surfaced actions', {
        error: errorMessage,
        scheduledToBePublishedAt,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch surfaced actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async updateScheduledAction(
    id: string,
    delta: Partial<ScheduledAction>,
  ): Promise<ScheduledAction> {
    try {
      const variables = {
        id,
        ...delta,
      };

      const response = await this.graphqlClient.request<{
        updateScheduledAction: ScheduledAction;
      }>(UPDATE_SCHEDULED_ACTION, variables, this.authHeaders);

      this.logger.debug('Updated scheduled action', {
        updatedActionId: id,
        updatedAt: response.updateScheduledAction.updatedAt,
        // Only log the delta keys that are not contentPayload or generationPayload to avoid very large logs
        delta: {
          ...Object.keys(delta).reduce(
            (acc, key) => {
              if (key === 'contentPayload' || key === 'generationPayload') {
                acc[key] = `${key[0].toUpperCase() + key.slice(1)} update`;
              } else {
                acc[key] = delta[key as keyof Partial<ScheduledAction>];
              }
              return acc;
            },
            {} as Record<string, unknown>,
          ),
        },
      });

      return response.updateScheduledAction;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to update scheduled action', {
        id,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to update scheduled action: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getAllActionsToSurface(): Promise<ActionsByCompany[]> {
    try {
      const allActions = await this.fetchAllPaginatedActions({
        status: Status.SURFACING_PENDING,
      });

      const groupedActions = this.groupActionsByCompany(allActions);

      this.logger.debug('Fetched all actions to surface', {
        totalActions: allActions.length,
        totalGroups: groupedActions.length,
      });

      return groupedActions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch all actions to surface', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch all actions to surface: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void> {
    try {
      let currentPage = 1;
      const pageSize = 50; // Smaller batch size to reduce memory usage
      let hasMoreItems = true;

      do {
        const response = await this.graphqlClient.request<{
          findScheduledActionsPaginated: {
            data: ScheduledAction[];
            hasNextPage: boolean;
            currentPage: number;
            totalItems: number;
          };
        }>(
          FIND_ACTIONS_PAGINATED,
          {
            status: Status.SURFACING_PENDING,
            page: currentPage,
            limit: pageSize,
          },
          this.authHeaders,
        );

        const { data, hasNextPage } = response.findScheduledActionsPaginated;

        if (data.length > 0) {
          const groupedActions = this.groupActionsByCompany(data);

          this.logger.debug('Processing batch', {
            page: currentPage,
            batchSize: data.length,
            totalGroups: groupedActions.length,
          });

          await callback(groupedActions);
        }

        hasMoreItems = hasNextPage;
        currentPage++;
      } while (hasMoreItems);

      this.logger.debug('Completed processing all batches', {
        totalPages: currentPage - 1,
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to process actions to surface in batches', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to process actions to surface in batches: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async fetchAllPaginatedActions(filters: {
    status?: Status;
    companyId?: string;
    workflowType?: WorkflowType;
  }): Promise<ScheduledAction[]> {
    const allActions: ScheduledAction[] = [];
    let currentPage = 1;
    const pageSize = 100;
    let hasMoreItems = true;

    do {
      const response = await this.graphqlClient.request<{
        findScheduledActionsPaginated: {
          data: ScheduledAction[];
          hasNextPage: boolean;
          currentPage: number;
          totalItems: number;
        };
      }>(
        FIND_ACTIONS_PAGINATED,
        {
          ...filters,
          page: currentPage,
          limit: pageSize,
        },
        this.authHeaders,
      );

      const { data, hasNextPage } = response.findScheduledActionsPaginated;
      allActions.push(...data);

      hasMoreItems = hasNextPage;
      currentPage++;
    } while (hasMoreItems);

    return allActions;
  }

  private groupActionsByCompany(
    actions: ScheduledAction[],
  ): ActionsByCompany[] {
    const groupMap = new Map<string, ScheduledAction[]>();

    for (const action of actions) {
      const existing = groupMap.get(action.companyId);
      if (existing) {
        existing.push(action);
      } else {
        groupMap.set(action.companyId, [action]);
      }
    }

    return Array.from(groupMap.entries()).map(([companyId, actions]) => ({
      companyId,
      actions,
    }));
  }

  async storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult: ScraperResult,
    keywords: string,
    recommendations: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse> {
    this.logger.debug('Storing SEO draft', { companyId, actionId });
    try {
      const response = await this.graphqlClient.request<{
        storeSEODraft: StoreSEODraftResponse;
      }>(
        STORE_SEO_DRAFT_MUTATION,
        { companyId, actionId, scraperResult, keywords, recommendations },
        this.authHeaders,
      );
      return response.storeSEODraft;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to store SEO Draft', {
        companyId,
        actionId,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to store SEO Draft: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getPageById(pageId: string): Promise<StoredScrapedPage | null> {
    try {
      const variables = { id: pageId };

      const response = await this.graphqlClient.request<{
        scrapedPage: StoredScrapedPage | null;
      }>(GET_SCRAPED_PAGE_BY_ID, variables, this.authHeaders);

      this.logger.debug('Fetched page by ID', {
        pageId,
        page: response.scrapedPage?.id,
      });

      return response.scrapedPage;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch page by ID', {
        pageId,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch page by ID: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async updatePageKeywordRank(
    pageKeywordId: string,
    currentRank: number | null,
  ): Promise<void> {
    try {
      await this.graphqlClient.request(
        UPDATE_PAGE_KEYWORD_RANK,
        {
          input: {
            id: pageKeywordId,
            currentRank,
          },
        },
        this.authHeaders,
      );

      this.logger.debug('Successfully updated page-keyword rank', {
        pageKeywordId,
        currentRank,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to update page-keyword rank', {
        pageKeywordId,
        currentRank,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to update page-keyword rank: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  // Fetch email content for a company. (POW Feed - for now - , scheduled actions, etc)
  // TODO: Add other content types (scheduled actions, etc)
  async getEmailContent(
    companyId: string,
    filters?: FeedFilters,
  ): Promise<HomepageFeed | null> {
    try {
      const response = await this.graphqlClient.request<{
        homepageFeed: HomepageFeed;
      }>(HOMEPAGE_FEED_QUERY, { companyId, filters }, this.authHeaders);
      this.logger.debug('Fetched email content', {
        companyId,
        filters,
        itemsCount: response.homepageFeed.items.length,
      });
      return response.homepageFeed;
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to get email content', {
        companyId,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to get email content: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async sendEmail(
    templateId: string,
    companyId: string,
    admins: string[],
    dynamicTemplateData: Record<string, unknown>,
    subject: string,
    bcc?: string | string[],
    groupUnsubscribeId?: string,
  ): Promise<void> {
    try {
      this.logger.debug('Sending email to admins', {
        companyId,
        adminCount: admins.length,
        bcc,
      });

      const emailPayload = {
        companyId,
        recipients: admins,
        dynamicTemplateData,
        templateId,
        bcc,
        groupUnsubscribeId,
        subject,
      };

      const response = await fetch(
        new URL(
          '/api/v1/notification/email',
          this.clientMarketingServiceUrl,
        ).toString(),
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.authHeaders,
          },
          body: JSON.stringify(emailPayload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      this.logger.debug('Successfully sent email to admins', {
        companyId,
        adminCount: admins.length,
        recipients: admins,
        bcc,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to send email to admins', {
        companyId,
        admins,
        error: message,
      });
      throw new SeoApiGatewayClientError(
        `Failed to send email to admins: ${message}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getSurfacedAndPublishedGroups(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  > {
    try {
      const response = await this.graphqlClient.request<{
        surfacedAndPublishedGroups: {
          keyword: {
            keyword: string;
          };
          scrapedPage: {
            url: string;
            id: string;
          };
        }[];
      }>(GET_SURFACED_AND_PUBLISHED_GROUPS, {}, this.authHeaders);

      this.logger.debug('Fetched surfaced and published groups', {
        count: response.surfacedAndPublishedGroups.length,
      });

      return response.surfacedAndPublishedGroups;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch surfaced and published groups', {
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch surfaced and published groups: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getSurfacedAndPublishedGroupsForRanking(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  > {
    try {
      const response = await this.graphqlClient.request<{
        surfacedAndPublishedGroupsForRanking: {
          keyword: {
            keyword: string;
          };
          scrapedPage: {
            url: string;
            id: string;
          };
        }[];
      }>(GET_SURFACED_AND_PUBLISHED_GROUPS_FOR_RANKING, {}, this.authHeaders);

      this.logger.debug('Fetched surfaced and published groups for ranking', {
        count: response.surfacedAndPublishedGroupsForRanking.length,
      });

      return response.surfacedAndPublishedGroupsForRanking;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        'Failed to fetch surfaced and published groups for ranking',
        {
          error: errorMessage,
        },
      );
      throw new SeoApiGatewayClientError(
        `Failed to fetch surfaced and published groups for ranking: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getSurfacedAndPublishedGroupsForRankingPaginated(
    page: number,
    limit: number,
  ): Promise<{
    data: {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[];
    hasMore: boolean;
    totalCount: number;
    currentPage: number;
    pageSize: number;
  }> {
    try {
      const response = await this.graphqlClient.request<{
        surfacedAndPublishedGroupsForRankingPaginated: {
          data: {
            keyword: {
              keyword: string;
            };
            scrapedPage: {
              url: string;
              id: string;
            };
          }[];
          hasMore: boolean;
          totalCount: number;
          currentPage: number;
          pageSize: number;
        };
      }>(
        GET_SURFACED_AND_PUBLISHED_GROUPS_FOR_RANKING_PAGINATED,
        { page, limit },
        this.authHeaders,
      );

      this.logger.debug(
        'Fetched paginated surfaced and published groups for ranking',
        {
          page,
          limit,
          count:
            response.surfacedAndPublishedGroupsForRankingPaginated.data.length,
          totalCount:
            response.surfacedAndPublishedGroupsForRankingPaginated.totalCount,
          hasMore:
            response.surfacedAndPublishedGroupsForRankingPaginated.hasMore,
        },
      );

      return response.surfacedAndPublishedGroupsForRankingPaginated;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(
        'Failed to fetch paginated surfaced and published groups for ranking',
        {
          error: errorMessage,
          page,
          limit,
        },
      );
      throw new SeoApiGatewayClientError(
        `Failed to fetch paginated surfaced and published groups for ranking: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async applyRecommendation(recommendationId: string): Promise<{
    id: string;
    status: string;
    type: string;
    recommendationValue: string;
    currentValue: string;
  }> {
    try {
      const response = await this.graphqlClient.request<{
        applyRecommendation: {
          id: string;
          status: string;
          type: string;
          recommendationValue: string;
          currentValue: string;
        };
      }>(APPLY_RECOMMENDATION, { id: recommendationId }, this.authHeaders);

      this.logger.debug('Applied recommendation successfully', {
        recommendationId,
        status: response.applyRecommendation.status,
      });

      return response.applyRecommendation;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to apply recommendation', {
        recommendationId,
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to apply recommendation: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getRecommendationsByGroupId(groupId: string): Promise<
    {
      id: string;
      type: string;
      status: string;
      recommendationValue: string;
      currentValue: string;
    }[]
  > {
    try {
      const response = await this.graphqlClient.request<{
        recommendations: {
          id: string;
          type: string;
          status: string;
          recommendationValue: string;
          currentValue: string;
        }[];
      }>(GET_RECOMMENDATIONS_BY_GROUP_ID, { groupId }, this.authHeaders);

      this.logger.debug('Fetched recommendations by group ID', {
        groupId,
        count: response.recommendations.length,
      });

      return response.recommendations;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch recommendations by group ID', {
        groupId,
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch recommendations by group ID: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getRecommendationById(
    recommendationId: string,
  ): Promise<StoredRecommendation> {
    try {
      const response = await this.graphqlClient.request<{
        recommendation: StoredRecommendation;
      }>(GET_RECOMMENDATION_BY_ID, { id: recommendationId }, this.authHeaders);

      this.logger.debug('Fetched recommendation by ID', {
        recommendationId,
        status: response.recommendation.status,
        type: response.recommendation.type,
      });

      return response.recommendation;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to fetch recommendation by ID', {
        recommendationId,
        error: errorMessage,
      });
      throw new SeoApiGatewayClientError(
        `Failed to fetch recommendation by ID: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }
}
