import { IStarApiService } from '../../services/interfaces/IStarApiService';
import { UpdateResult } from '../../utils/publisherUtils';

/**
 * Mock implementation of IStarApiService for testing
 */
export class MockStarApiService implements IStarApiService {
  private updateMetaTitleCallCount = 0;
  private updateMetaDescriptionCallCount = 0;
  private updateMainHeadingCallCount = 0;
  private customResponses: Map<string, UpdateResult> = new Map();
  private shouldFail = false;
  private failureError?: string;
  private failureMetadata?: any;

  /**
   * Tracks the last parameters passed to each method
   */
  public lastUpdateMetaTitleCall?: {
    url: string;
    oldContent: string;
    newContent: string;
    companyId?: string;
    actionId?: string;
  };

  public lastUpdateMetaDescriptionCall?: {
    url: string;
    oldContent: string;
    newContent: string;
    companyId?: string;
    actionId?: string;
  };

  public lastUpdateMainHeadingCall?: {
    url: string;
    elementType: string;
    elementIndex: number;
    oldContent: string;
    newContent: string;
    companyId?: string;
    actionId?: string;
  };

  /**
   * Updates the meta title of a webpage
   */
  updateMetaTitle(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    this.updateMetaTitleCallCount++;
    this.lastUpdateMetaTitleCall = {
      url,
      oldContent,
      newContent,
      companyId,
      actionId,
    };

    // Check for custom response
    const customKey = `metaTitle-${url}`;
    if (this.customResponses.has(customKey)) {
      return Promise.resolve(this.customResponses.get(customKey)!);
    }

    // Return failure if configured
    if (this.shouldFail) {
      return Promise.resolve({
        elementType: 'meta',
        elementProperty: 'title',
        success: false,
        error: this.failureError || 'Mock update failed',
        ...(this.failureMetadata && { metadata: this.failureMetadata }),
      });
    }

    // Default success response
    return Promise.resolve({
      elementType: 'meta',
      elementProperty: 'title',
      success: true,
    });
  }

  /**
   * Updates the meta description of a webpage
   */
  updateMetaDescription(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    this.updateMetaDescriptionCallCount++;
    this.lastUpdateMetaDescriptionCall = {
      url,
      oldContent,
      newContent,
      companyId,
      actionId,
    };

    // Check for custom response
    const customKey = `metaDescription-${url}`;
    if (this.customResponses.has(customKey)) {
      return Promise.resolve(this.customResponses.get(customKey)!);
    }

    // Return failure if configured
    if (this.shouldFail) {
      return Promise.resolve({
        elementType: 'meta',
        elementProperty: 'description',
        success: false,
        error: this.failureError || 'Mock update failed',
        ...(this.failureMetadata && { metadata: this.failureMetadata }),
      });
    }

    // Default success response
    return Promise.resolve({
      elementType: 'meta',
      elementProperty: 'description',
      success: true,
    });
  }

  /**
   * Updates the main heading on a webpage
   */
  updateMainHeading(
    url: string,
    elementType: string,
    elementIndex: number,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    this.updateMainHeadingCallCount++;
    this.lastUpdateMainHeadingCall = {
      url,
      elementType,
      elementIndex,
      oldContent,
      newContent,
      companyId,
      actionId,
    };

    // Check for custom response
    const customKey = `mainHeading-${url}-${elementIndex}`;
    if (this.customResponses.has(customKey)) {
      return Promise.resolve(this.customResponses.get(customKey)!);
    }

    // Return failure if configured
    if (this.shouldFail) {
      return Promise.resolve({
        elementType: elementType as 'h1' | 'h2',
        success: false,
        error: this.failureError || 'Mock update failed',
        ...(this.failureMetadata && { metadata: this.failureMetadata }),
      });
    }

    // Default success response
    return Promise.resolve({
      elementType: elementType as 'h1' | 'h2',
      success: true,
    });
  }

  /**
   * Test helper methods
   */

  /**
   * Sets a custom response for a specific method and URL
   */
  setCustomResponse(
    method: 'metaTitle' | 'metaDescription' | 'mainHeading',
    url: string,
    response: UpdateResult,
    elementIndex?: number,
  ): void {
    const key =
      method === 'mainHeading'
        ? `${method}-${url}-${elementIndex}`
        : `${method}-${url}`;
    this.customResponses.set(key, response);
  }

  /**
   * Configures the mock to fail all requests
   */
  setShouldFail(shouldFail: boolean, error?: string, metadata?: any): void {
    this.shouldFail = shouldFail;
    this.failureError = error;
    this.failureMetadata = metadata;
  }

  /**
   * Gets the call count for a specific method
   */
  getCallCount(
    method: 'updateMetaTitle' | 'updateMetaDescription' | 'updateMainHeading',
  ): number {
    switch (method) {
      case 'updateMetaTitle':
        return this.updateMetaTitleCallCount;
      case 'updateMetaDescription':
        return this.updateMetaDescriptionCallCount;
      case 'updateMainHeading':
        return this.updateMainHeadingCallCount;
    }
  }

  /**
   * Gets the total call count across all methods
   */
  getTotalCallCount(): number {
    return (
      this.updateMetaTitleCallCount +
      this.updateMetaDescriptionCallCount +
      this.updateMainHeadingCallCount
    );
  }

  /**
   * Resets all mock state
   */
  reset(): void {
    this.updateMetaTitleCallCount = 0;
    this.updateMetaDescriptionCallCount = 0;
    this.updateMainHeadingCallCount = 0;
    this.customResponses.clear();
    this.shouldFail = false;
    this.failureError = undefined;
    this.failureMetadata = undefined;
    this.lastUpdateMetaTitleCall = undefined;
    this.lastUpdateMetaDescriptionCall = undefined;
    this.lastUpdateMainHeadingCall = undefined;
  }
}
