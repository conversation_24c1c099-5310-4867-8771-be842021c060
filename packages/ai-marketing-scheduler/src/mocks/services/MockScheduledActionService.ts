import { ActionsByCompany } from '../../clients/types/actions';
import {
  RecommendationsRaw,
  StoreSEODraftResponse,
  StoredRecommendation,
  RecommendationStatus,
} from '../../clients/types/seoApiGateway';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { UpdateScheduledActionResult } from '../../services/ScheduledActionService';
import {
  ScheduledAction,
  CheckActionResult,
  Status,
  WorkflowType,
  ScraperResult,
  RecommendationType,
} from '../../types';

export class MockScheduledActionService implements IScheduledActionService {
  private actions: Map<string, ScheduledAction> = new Map();
  private actionIdCounter = 1;

  constructor(initialActions: ScheduledAction[] = []) {
    // Initialize with some mock data
    const baseMockActions = [
      this.createMockAction('action-1', 'company-1', WorkflowType.SEO),
      this.createMockAction('action-2', 'company-1', WorkflowType.BLOG),
      this.createMockAction('action-3', 'company-2', WorkflowType.SEO),
      this.createMockAction('action-4', 'company-3', WorkflowType.SEO),
      this.createMockAction(
        'action-5',
        'company-1',
        WorkflowType.SEO,
        Status.SURFACING_PENDING,
        new Date().toISOString(),
      ),
      this.createMockAction(
        'action-6',
        'company-2',
        WorkflowType.SEO,
        Status.SURFACED,
      ),
      this.createMockAction(
        'action-7',
        'company-2',
        WorkflowType.SEO,
        Status.SURFACED,
      ),
      this.createMockAction(
        'action-8',
        'company-2',
        WorkflowType.SEO,
        Status.PUBLISHED,
      ),
    ];
    const mockActions = [...initialActions, ...baseMockActions];
    mockActions.forEach(action => {
      this.actions.set(action.id, action);
    });
  }

  /**
   * Helper to create a mock scheduled action
   */
  private createMockAction(
    id: string,
    companyId: string,
    workflowType: WorkflowType,
    status: Status = Status.DRAFT_PENDING,
    scheduledToBeSurfacedAtOverride?: string,
  ): ScheduledAction {
    // Set dates based on status according to the rules
    let scheduledToBeSurfacedAt: string | null = null;
    let surfacedAt: string | null = null;
    let scheduledToBePublishedAt: string | null = null;
    let publishedAt: string | null = null;

    const currentDate = new Date();
    const currentTimestamp = currentDate.toISOString();

    // If status is SURFACING_PENDING or higher, set scheduledToBeSurfacedAt
    if (status !== Status.DRAFT_PENDING) {
      scheduledToBeSurfacedAt =
        scheduledToBeSurfacedAtOverride || currentTimestamp;
    }

    // If status is SURFACED or higher, set surfacedAt to same day as scheduledToBeSurfacedAt
    // and scheduledToBePublishedAt to +2 days after surfacedAt
    if (status === Status.SURFACED || status === Status.PUBLISHED) {
      surfacedAt = scheduledToBeSurfacedAt;

      const publishDate = new Date(surfacedAt!);
      publishDate.setDate(publishDate.getDate() + 2);
      scheduledToBePublishedAt = publishDate.toISOString();
    }

    // If status is PUBLISHED, set publishedAt to same as scheduledToBePublishedAt
    if (status === Status.PUBLISHED) {
      publishedAt = scheduledToBePublishedAt;
    }

    return {
      id,
      companyId,
      workflowType,
      status,
      createdAt: currentTimestamp,
      updatedAt: currentTimestamp,
      scheduledToBeSurfacedAt,
      surfacedAt,
      scheduledToBePublishedAt,
      publishedAt,
      contentPayload: {
        title: `Mock ${workflowType} Content for ${id}`,
        description: 'This is mock content for testing',
      },
      generationPayload: {
        prompt: 'Generate mock content',
        parameters: { temperature: 0.7 },
      },
      failureReason: {},
      executionName: null,
      executionArn: null,
    };
  }
  getDraftPendingActions(): Promise<ScheduledAction[]> {
    return Promise.resolve(
      Array.from(this.actions.values()).filter(
        action => action.status === Status.DRAFT_PENDING,
      ),
    );
  }

  getSurfacedActions(): Promise<ScheduledAction[]> {
    return Promise.resolve(
      Array.from(this.actions.values()).filter(
        action => action.status === Status.SURFACED,
      ),
    );
  }

  getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]> {
    return Promise.resolve(
      Array.from(this.actions.values()).filter(
        action =>
          action.companyId === companyId &&
          action.workflowType === workflowType &&
          [
            Status.DRAFT_PENDING,
            Status.HUMAN_QA_PENDING,
            Status.SURFACING_PENDING,
            Status.SURFACED,
          ].includes(action.status),
      ),
    );
  }

  checkAction(
    actionId: string,
    fieldsToCheck: string[],
  ): Promise<CheckActionResult> {
    const action = this.actions.get(actionId);
    if (!action) {
      return Promise.reject(new Error(`Action ${actionId} not found`));
    }

    const generationPayload = action.generationPayload || {};
    const skipStep = fieldsToCheck.every(field => field in generationPayload);

    return Promise.resolve({
      action,
      skipStep,
    });
  }

  getAction(actionId: string): Promise<ScheduledAction | null> {
    return Promise.resolve(this.actions.get(actionId) || null);
  }

  createScheduledActions(
    companyId: string,
    workflowType: WorkflowType,
    count: number,
    generationPayload?: object,
  ): Promise<void> {
    const now = new Date().toISOString();
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 7);

    for (let i = 0; i < count; i++) {
      const actionId = `mock-action-${this.actionIdCounter++}`;
      const action: ScheduledAction = {
        id: actionId,
        companyId,
        workflowType,
        status: Status.DRAFT_PENDING,
        createdAt: now,
        updatedAt: now,
        surfacedAt: null,
        scheduledToBePublishedAt: scheduledDate.toISOString().split('T')[0],
        scheduledToBeSurfacedAt: null,
        publishedAt: null,
        contentPayload: null,
        generationPayload:
          (generationPayload as Record<string, unknown>) || null,
        failureReason: {},
        executionName: null,
        executionArn: null,
      };
      this.actions.set(actionId, action);
    }

    return Promise.resolve();
  }

  updateWithFailure(
    actionId: string,
    failureReason: Record<string, unknown>,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult> {
    const action = this.actions.get(actionId);
    if (!action) {
      return Promise.resolve({
        id: actionId,
        success: false,
        error: `Action ${actionId} not found`,
        workflowType,
      });
    }

    action.status = Status.FAILED;
    action.failureReason = failureReason;
    action.updatedAt = new Date().toISOString();

    return Promise.resolve({
      id: actionId,
      success: true,
      workflowType: workflowType || action.workflowType,
    });
  }

  updateStatus(
    actionId: string,
    status: Status,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult> {
    const action = this.actions.get(actionId);
    if (!action) {
      return Promise.resolve({
        id: actionId,
        success: false,
        error: `Action ${actionId} not found`,
        workflowType,
      });
    }

    action.status = status;
    action.updatedAt = new Date().toISOString();

    return Promise.resolve({
      id: actionId,
      success: true,
      workflowType: workflowType || action.workflowType,
    });
  }

  update(
    actionId: string,
    delta: Partial<
      Omit<ScheduledAction, 'id' | 'companyId' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<ScheduledAction> {
    const action = this.actions.get(actionId);
    if (!action) {
      return Promise.reject(new Error(`Action ${actionId} not found`));
    }

    const updatedAction = {
      ...action,
      ...delta,
      updatedAt: new Date().toISOString(),
    };

    this.actions.set(actionId, updatedAction);
    return Promise.resolve(updatedAction);
  }

  storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult: ScraperResult,
    keywords: string,
    recommendations: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse> {
    const action = this.actions.get(actionId);
    if (!action) {
      return Promise.reject(new Error(`Action ${actionId} not found`));
    }

    const now = new Date().toISOString();
    const keywordId = `keyword-${Date.now()}`;
    const pageId = `page-${Date.now()}`;

    const savedRecommendations: StoredRecommendation[] = [];

    if (recommendations.metaTitle) {
      savedRecommendations.push({
        id: `rec-meta-title-${Date.now()}`,
        scrapeId: `scrape-${Date.now()}`,
        groupId: `group-${Date.now()}`,
        type: RecommendationType.META_TITLE,
        currentValue: recommendations.metaTitle.currentValue,
        recommendationValue: recommendations.metaTitle.recommendationValue,
        reasoning: recommendations.metaTitle.reasoning,
        status: RecommendationStatus.PENDING,
        rejectionReason: null,
        metadata: {},
        createdAt: new Date(now),
        updatedAt: new Date(now),
      });
    }

    if (recommendations.metaDescription) {
      savedRecommendations.push({
        id: `rec-meta-desc-${Date.now()}`,
        scrapeId: `scrape-${Date.now()}`,
        groupId: `group-${Date.now()}`,
        type: RecommendationType.META_DESCRIPTION,
        currentValue: recommendations.metaDescription.currentValue,
        recommendationValue:
          recommendations.metaDescription.recommendationValue,
        reasoning: recommendations.metaDescription.reasoning,
        status: RecommendationStatus.PENDING,
        rejectionReason: null,
        metadata: {},
        createdAt: new Date(now),
        updatedAt: new Date(now),
      });
    }

    if (recommendations.mainHeading) {
      savedRecommendations.push({
        id: `rec-main-heading-${Date.now()}`,
        scrapeId: `scrape-${Date.now()}`,
        groupId: `group-${Date.now()}`,
        type: RecommendationType.MAIN_HEADING,
        currentValue: recommendations.mainHeading.currentValue,
        recommendationValue: recommendations.mainHeading.recommendationValue,
        reasoning: recommendations.mainHeading.reasoning,
        status: RecommendationStatus.PENDING,
        rejectionReason: null,
        metadata: {},
        createdAt: new Date(now),
        updatedAt: new Date(now),
      });
    }

    const mockResponse: StoreSEODraftResponse = {
      savedKeyword: {
        id: keywordId,
        keyword: keywords,
      },
      savedRecommendations:
        savedRecommendations.length > 0
          ? savedRecommendations.map(rec => ({
              id: rec.id,
              type: rec.type,
              groupId: rec.groupId,
              scrapeId: rec.scrapeId!,
            }))
          : null,
      savedScrape: {
        id: `scrape-${Date.now()}`,
        markdown: scraperResult.markdown,
        mediaId: scraperResult.mediaId,
      },
      savedScrapedPage: {
        id: pageId,
        companyId,
        url: scraperResult.url,
      },
      savedPageKeyword: {
        id: `page-keyword-${Date.now()}`,
        keywordId,
      },
      savedGroup: null,
    };

    action.generationPayload = {
      ...action.generationPayload,
      storeSEODraftResponse: mockResponse,
    };
    action.updatedAt = now;

    return Promise.resolve(mockResponse);
  }

  findActionsToSurface(): Promise<ActionsByCompany[]> {
    const actionsByCompany = new Map<string, ScheduledAction[]>();

    Array.from(this.actions.values())
      .filter(action => action.status === Status.SURFACING_PENDING)
      .forEach(action => {
        const actions = actionsByCompany.get(action.companyId) || [];
        actions.push(action);
        actionsByCompany.set(action.companyId, actions);
      });

    return Promise.resolve(
      Array.from(actionsByCompany.entries()).map(([companyId, actions]) => ({
        companyId,
        actions,
      })),
    );
  }

  async processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void> {
    const allActions = await this.findActionsToSurface();
    if (allActions.length > 0) {
      await callback(allActions);
    }
  }

  addMockAction(action: ScheduledAction): void {
    this.actions.set(action.id, action);
  }

  getMockActions(): ScheduledAction[] {
    return Array.from(this.actions.values());
  }

  clearMockActions(): void {
    this.actions.clear();
    this.actionIdCounter = 1;
  }
}
