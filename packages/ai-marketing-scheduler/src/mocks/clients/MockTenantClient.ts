import { ITenantClient } from '../../clients/interfaces/ITenantClient';
import { ContextLogger } from '../../logger/contextLogger';
import { EntitlementDTO, UserDTO } from '../../types';

/**
 * Mock implementation of TenantClient for testing
 * Provides predefined responses without making actual API calls
 */
export class MockTenantClient implements ITenantClient {
  constructor(private readonly logger: ContextLogger) {}

  /**
   * Override to return mock entitlements without making API calls
   */
  async getAllEntitlements(): Promise<EntitlementDTO[]> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();
    const mockEntitlements: EntitlementDTO[] = [
      {
        displayId: 'ent-1',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        productId: 'seo-automation',
        companyId: 'company-1',
        startDate: new Date(
          Date.now() - 30 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        endDate: null,
        units: 1,
        salesforceServiceId: 'sf-service-1',
        product: {
          id: 'seo-automation',
          name: 'SEO Automation',
        },
        company: {
          website: 'https://example1.com',
          name: 'Example Company 1',
        },
      },
      {
        displayId: 'ent-2',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        productId: 'blog-automation',
        companyId: 'company-2',
        startDate: new Date(
          Date.now() - 60 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        endDate: null,
        units: 1,
        salesforceServiceId: 'sf-service-2',
        product: {
          id: 'blog-automation',
          name: 'Blog Automation',
        },
        company: {
          website: 'https://example2.com',
          name: 'Example Company 2',
        },
      },
      {
        displayId: 'ent-3',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        productId: 'seo-automation',
        companyId: 'company-3',
        startDate: new Date(
          Date.now() - 45 * 24 * 60 * 60 * 1000,
        ).toISOString(),
        endDate: null,
        units: 1,
        salesforceServiceId: 'sf-service-3',
        product: {
          id: 'seo-automation',
          name: 'SEO Automation',
        },
        company: {
          website: 'https://example3.com',
          name: 'Example Company 3',
        },
      },
    ];

    // Access logger through protected method or create a new one
    this.logger.info('Returning mock entitlements', {
      entitlementsCount: mockEntitlements.length,
    });

    return mockEntitlements;
  }

  async getAllCompanyAdmins(companyId: string): Promise<UserDTO[]> {
    return Promise.resolve([
      {
        displayId: companyId,
        firstName: 'Admin',
        lastName: '1',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar1.jpg',
        teamLead: false,
        externalAuthId: 'ext-auth-1',
        facebookUserId: 'fb-user-1',
        lastLogin: null,
        notificationsLastSeen: new Date().toISOString(),
        leadEmail: null,
        phoneNumber: null,
        type: 'CLIENT',
      },
    ]);
  }
}
