import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import { ActionsByCompany } from '../../clients/types/actions';
import {
  FeedFilters,
  HomepageFeed,
  RecommendationsRaw,
  StoreSEODraftResponse,
  StoredRecommendation,
  StoredScrapedPage,
} from '../../clients/types/seoApiGateway';
import { ContextLogger } from '../../logger/contextLogger';
import {
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import { ScrapedPageType } from '../../types/scraperResult';
import { createMockStoredRecommendations } from '../../utils/testUtils';

/**
 * Mock implementation of SeoApiGatewayClient for testing
 * Provides predefined responses without making actual GraphQL calls
 */
export class MockSeoApiGatewayClient implements ISeoApiGatewayClient {
  private mockActions: ScheduledAction[];

  constructor(private readonly logger: ContextLogger) {
    // Initialize with some mock data
    this.mockActions = [
      this.createMockAction('action-1', 'company-1', WorkflowType.SEO),
      this.createMockAction('action-2', 'company-1', WorkflowType.BLOG),
      this.createMockAction('action-3', 'company-2', WorkflowType.SEO),
      this.createMockAction('action-4', 'company-3', WorkflowType.SEO),
      this.createMockAction(
        'action-5',
        'company-1',
        WorkflowType.SEO,
        Status.SURFACING_PENDING,
      ),
      this.createMockAction(
        'action-6',
        'company-2',
        WorkflowType.SEO,
        Status.SURFACED,
      ),
      this.createMockAction(
        'action-7',
        'company-2',
        WorkflowType.SEO,
        Status.SURFACED,
      ),
      this.createMockAction(
        'action-8',
        'company-2',
        WorkflowType.SEO,
        Status.PUBLISHED,
      ),
    ];
  }

  /**
   * Helper to create a mock scheduled action
   */
  private createMockAction(
    id: string,
    companyId: string,
    workflowType: WorkflowType,
    status: Status = Status.DRAFT_PENDING,
  ): ScheduledAction {
    const now = new Date();
    return {
      id,
      companyId,
      workflowType,
      status,
      createdAt: new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: now.toISOString(),
      surfacedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      publishedAt: null,
      contentPayload: {
        title: `Mock ${workflowType} Content for ${id}`,
        description: 'This is mock content for testing',
      },
      generationPayload: {
        prompt: 'Generate mock content',
        parameters: { temperature: 0.7 },
      },
      failureReason: {},
      executionName: null,
      executionArn: null,
    };
  }

  /**
   * Gets mock scheduled actions with DRAFT_PENDING status
   */
  async getDraftPendingActions(): Promise<ScheduledAction[]> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Fetching DRAFT_PENDING scheduled actions');

    const pendingActions = this.mockActions.filter(
      action => action.status === Status.DRAFT_PENDING,
    );

    this.logger.debug('Mock: Fetched draft pending actions', {
      count: pendingActions.length,
    });

    return pendingActions;
  }

  async getSurfacedActions(
    scheduledToBePublishedAt: string,
  ): Promise<ScheduledAction[]> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Fetching surfaced actions', {
      scheduledToBePublishedAt,
    });

    const surfacedActions = this.mockActions.filter(
      action => action.status === Status.SURFACED,
    );

    this.logger.debug('Mock: Fetched surfaced actions', {
      count: surfacedActions.length,
    });

    return surfacedActions;
  }

  async getAction(actionId: string): Promise<ScheduledAction | null> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Fetching action by ID', { actionId });

    const action = this.mockActions.find(action => action.id === actionId);

    this.logger.debug('Mock: Fetched action', { actionId });

    return action || null;
  }

  /**
   * Gets mock upcoming scheduled actions filtered by company ID and workflow type
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @returns The upcoming actions for the specified company and workflow type
   */
  async getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Fetching upcoming scheduled actions', {
      companyId,
      workflowType,
    });

    const upcomingActions = this.mockActions.filter(
      action =>
        action.companyId === companyId && action.workflowType === workflowType,
    );

    this.logger.debug('Mock: Fetched upcoming actions', {
      companyId,
      workflowType,
      count: upcomingActions.length,
    });

    return upcomingActions;
  }

  /**
   * Creates mock owed actions for a company and workflow type
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @returns The created actions
   */
  async createOwedActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Creating owed actions', {
      companyId,
      workflowType,
    });

    // Generate new mock actions for this company and workflow type
    const newActionId = `owed-action-${companyId}-${workflowType}-1`;

    const newAction = this.createMockAction(
      newActionId,
      companyId,
      workflowType,
    );

    // Add the new actions to our mock actions list
    this.mockActions = [...this.mockActions, newAction];

    this.logger.debug('Mock: Created owed actions', {
      companyId,
      workflowType,
      actionId: newAction.id,
    });

    return newAction;
  }

  async updateScheduledAction(
    actionId: string,
    delta: Partial<ScheduledAction>,
  ): Promise<ScheduledAction> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Updating scheduled action', {
      actionId,
      delta,
    });

    const actionIndex = this.mockActions.findIndex(
      action => action.id === actionId,
    );

    if (actionIndex === -1) {
      this.logger.error('Mock: Action not found', { actionId });
      throw new Error(`Action with ID ${actionId} not found`);
    }

    const updatedAction = {
      ...this.mockActions[actionIndex],
      ...delta,
      updatedAt: new Date().toISOString(),
    };

    this.mockActions[actionIndex] = updatedAction;

    this.logger.debug('Mock: Updated scheduled action', {
      actionId,
      updatedAction,
    });

    return updatedAction;
  }

  /**
   * Gets mock pages for a specific company
   * @param companyId The ID of the company
   * @returns A list of pages for the company
   */
  async getPagesByCompanyId(companyId: string): Promise<StoredScrapedPage[]> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.logger.debug('Mock: Fetching pages by company ID', { companyId });

    // For now, return an empty array. This can be expanded if tests need specific mock pages.
    // TODO MOCK THIS
    const pages: StoredScrapedPage[] = [];

    this.logger.debug('Mock: Fetched pages for company', {
      companyId,
      count: pages.length,
    });

    return pages;
  }

  async getAllActionsToSurface(): Promise<ActionsByCompany[]> {
    // Using await with a resolved promise to satisfy the linter
    const actionsByCompany = this.mockActions
      .filter(a => a.status === Status.SURFACING_PENDING)
      .reduce<Record<string, ScheduledAction[]>>((acc, a) => {
        (acc[a.companyId] ||= []).push(a);
        return acc;
      }, {});
    const actions = await Promise.resolve(
      Object.entries(actionsByCompany).map(([companyId, actions]) => ({
        companyId,
        actions,
      })),
    );
    this.logger.debug('Mock: Fetching actions to surface');
    return actions;
  }

  async processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void> {
    this.logger.debug('Mock: Processing actions to surface in batches');
    const allActions = await this.getAllActionsToSurface();
    if (allActions.length > 0) {
      await callback(allActions);
    }
  }

  // Return empty array since this will be spied on with Jest on actual tests
  // TODO mock this
  fetchAllPaginatedActions(): Promise<ScheduledAction[]> {
    return Promise.resolve([]);
  }

  async storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult?: ScraperResult,
    keywords?: string,
    recommendations?: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse> {
    this.logger.debug('Mock: Storing SEO draft', {
      companyId,
      actionId,
      scraperResult,
      keywords,
      recommendations,
    });
    return Promise.resolve({
      savedKeyword: null,
      savedRecommendations: [],
      savedScrape: null,
      savedScrapedPage: null,
      savedPageKeyword: null,
      savedGroup: null,
    });
  }

  async getPageById(pageId: string): Promise<StoredScrapedPage | null> {
    // Using await with a resolved promise to satisfy the linter

    this.logger.debug('Mock: Fetching page by ID', { pageId });
    return Promise.resolve({
      id: pageId,
      companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
      url: 'https://ryansbeach.homes/developments',
      pageName: "Home Valuation - Ryan's Beach Homes",
      pageType: ScrapedPageType.HOME_VALUATION,
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      recommendations: [],
      scrapes: [],
      pageKeywordHistory: [],
      pageKeywords: [
        {
          id: 'mock-page-keyword-123',
          scrapedPageId: pageId,
          keywordId: 'mock-keyword-123',
          originalRank: 10,
          currentRank: 5,
          rankCheckedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          keyword: {
            id: 'mock-keyword-123',
            keyword: 'Homes for sale near Ryan Beach',
            metadata: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          scrapedPage: {} as any,
          companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
        },
      ],
    });
  }

  async updatePageKeywordRank(
    pageKeywordId: string,
    currentRank: number | null,
  ): Promise<void> {
    this.logger.debug('Mock: Updating page-keyword rank', {
      pageKeywordId,
      currentRank,
    });
    // Mock implementation - just log and resolve
    return Promise.resolve();
  }

  async getEmailContent(
    companyId: string,
    filters?: FeedFilters,
  ): Promise<HomepageFeed> {
    this.logger.debug('Mock: Fetching email content', { companyId, filters });
    return Promise.resolve({ items: [] });
  }

  async getSurfacedAndPublishedGroups(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  > {
    this.logger.debug('Mock: Fetching surfaced and published groups');

    // Return mock data
    return Promise.resolve([
      {
        keyword: {
          keyword: 'test keyword 1',
        },
        scrapedPage: {
          url: 'https://example.com/page1',
          id: 'page-1',
        },
      },
      {
        keyword: {
          keyword: 'test keyword 2',
        },
        scrapedPage: {
          url: 'https://example.com/page2',
          id: 'page-2',
        },
      },
    ]);
  }

  async getSurfacedAndPublishedGroupsForRanking(): Promise<
    {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[]
  > {
    this.logger.debug(
      'Mock: Fetching surfaced and published groups for ranking',
    );

    // Return the same mock data as the original method since it's the same structure
    return this.getSurfacedAndPublishedGroups();
  }

  async getSurfacedAndPublishedGroupsForRankingPaginated(
    page: number,
    limit: number,
  ): Promise<{
    data: {
      keyword: {
        keyword: string;
      };
      scrapedPage: {
        url: string;
        id: string;
      };
    }[];
    hasMore: boolean;
    totalCount: number;
    currentPage: number;
    pageSize: number;
  }> {
    this.logger.debug(
      'Mock: Fetching paginated surfaced and published groups for ranking',
      { page, limit },
    );

    // Generate mock data based on page and limit
    const allMockData = await this.getSurfacedAndPublishedGroups();
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const pageData = allMockData.slice(startIndex, endIndex);

    return {
      data: pageData,
      hasMore: endIndex < allMockData.length,
      totalCount: allMockData.length,
      currentPage: page,
      pageSize: limit,
    };
  }

  async sendEmail(
    templateId: string,
    companyId: string,
    admins: string[],
    dynamicTemplateData: Record<string, unknown>,
    bcc?: string | string[],
  ): Promise<void> {
    this.logger.debug('Mock: Sending email', {
      companyId,
      admins,
      dynamicTemplateData,
      templateId,
      bcc,
    });
    return Promise.resolve();
  }

  async applyRecommendation(recommendationId: string): Promise<{
    id: string;
    status: string;
    type: string;
    recommendationValue: string;
    currentValue: string;
  }> {
    this.logger.debug('Mock: Applying recommendation', { recommendationId });

    return Promise.resolve({
      id: recommendationId,
      status: 'APPLIED',
      type: 'META_TITLE',
      recommendationValue: 'New Title',
      currentValue: 'Old Title',
    });
  }

  async getRecommendationsByGroupId(groupId: string): Promise<
    {
      id: string;
      type: string;
      status: string;
      recommendationValue: string;
      currentValue: string;
    }[]
  > {
    this.logger.debug('Mock: Fetching recommendations by group ID', {
      groupId,
    });

    return Promise.resolve([
      {
        id: 'rec-1',
        type: 'META_TITLE',
        status: 'PENDING',
        recommendationValue: 'New Title',
        currentValue: 'Old Title',
      },
      {
        id: 'rec-2',
        type: 'META_DESCRIPTION',
        status: 'PENDING',
        recommendationValue: 'New Description',
        currentValue: 'Old Description',
      },
      {
        id: 'rec-3',
        type: 'MAIN_HEADING',
        status: 'PENDING',
        recommendationValue: 'New H1',
        currentValue: 'Old H1',
      },
    ]);
  }

  async getRecommendationById(
    recommendationId: string,
  ): Promise<StoredRecommendation> {
    this.logger.debug('Mock: Fetching recommendation by ID', {
      recommendationId,
    });

    const mockRecommendations = createMockStoredRecommendations();

    // Return different recommendation types based on ID pattern for more realistic testing
    let baseRecommendation;
    if (
      recommendationId.includes('rec-2') ||
      recommendationId.toLowerCase().includes('description')
    ) {
      baseRecommendation = mockRecommendations.metaDescription;
    } else if (
      recommendationId.includes('rec-3') ||
      recommendationId.toLowerCase().includes('heading') ||
      recommendationId.toLowerCase().includes('h1')
    ) {
      baseRecommendation = mockRecommendations.mainHeading;
    } else {
      // Default to metaTitle for rec-1 or any other pattern
      baseRecommendation = mockRecommendations.metaTitle;
    }

    return Promise.resolve({
      ...baseRecommendation,
      id: recommendationId,
    });
  }
}
