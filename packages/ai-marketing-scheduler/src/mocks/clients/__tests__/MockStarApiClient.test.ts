import { StarApiPayload } from '../../../clients/interfaces/IStarApiClient';
import { ContextLogger } from '../../../logger/contextLogger';
import { createMockContextLogger } from '../../../utils/testUtils';
import { MockStarApiClient } from '../MockStarApiClient';

describe('MockStarApiClient', () => {
  let mockClient: MockStarApiClient;
  let mockLogger: jest.Mocked<ContextLogger>;

  beforeEach(() => {
    mockLogger = createMockContextLogger();
    mockClient = new MockStarApiClient(mockLogger);
  });

  describe('updateElement', () => {
    const validPayload: StarApiPayload = {
      url: 'https://example.com/page',
      input: {
        elementProperty: 'title',
        elementTag: 'meta',
        newContent: 'New Title',
        oldContent: 'Old Title',
      },
      debug: false,
    };

    it('should return success response by default', async () => {
      const result = await mockClient.updateElement(validPayload);

      expect(result.data).toEqual({
        message: 'Successfully updated meta element',
        requestId: expect.stringMatching(/^mock-request-\d+$/),
        statusText: 'OK',
      });
      expect(result.context).toEqual({
        starApiRequestId: expect.stringMatching(/^mock-request-\d+$/),
      });

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Mock: Updating element via STAR API',
        expect.objectContaining({
          payload: validPayload,
          callCount: 1,
        }),
      );
    });

    it('should track call count and last payload', async () => {
      await mockClient.updateElement(validPayload);
      await mockClient.updateElement(validPayload);

      expect(mockClient.getCallCount()).toBe(2);
      expect(mockClient.getLastPayload()).toEqual(validPayload);
    });

    it('should return custom responses when configured', async () => {
      const customResponse = {
        message: 'Custom success message',
        requestId: 'custom-request-123',
        statusText: 'OK',
      };

      mockClient.addMockResponse(customResponse);

      const result = await mockClient.updateElement(validPayload);

      expect(result.data).toEqual(customResponse);
      expect(result.context).toEqual({
        starApiRequestId: 'custom-request-123',
      });
    });

    it('should simulate failures when configured', async () => {
      mockClient.setFailure('Custom error message');

      await expect(mockClient.updateElement(validPayload)).rejects.toThrow(
        'Custom error message',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Mock: STAR API update failed',
        expect.objectContaining({
          error: 'Custom error message',
        }),
      );
    });

    it('should reset state correctly', async () => {
      mockClient.setFailure('Error');
      mockClient.addMockResponse({ requestId: 'test', message: 'test' });
      await mockClient.updateElement(validPayload).catch(() => {});

      mockClient.reset();

      expect(mockClient.getCallCount()).toBe(0);
      expect(mockClient.getLastPayload()).toBeNull();

      // Should not fail after reset
      const result = await mockClient.updateElement(validPayload);
      expect(result.data.message).toContain('Successfully updated');
    });

    it('should handle H1 updates with elementIndex', async () => {
      const h1Payload: StarApiPayload = {
        url: 'https://example.com/page',
        input: {
          elementIndex: 0,
          elementTag: 'h1',
          newContent: 'New Heading',
          oldContent: 'Old Heading',
        },
        debug: false,
      };

      const result = await mockClient.updateElement(h1Payload);

      expect(result.data.message).toBe('Successfully updated h1 element');
    });

    it('should work with company ID', async () => {
      const result = await mockClient.updateElement(
        validPayload,
        'test-company-123',
      );

      expect(result).toBeDefined();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Mock: Updating element via STAR API',
        expect.objectContaining({
          companyId: 'test-company-123',
        }),
      );
    });
  });
});
