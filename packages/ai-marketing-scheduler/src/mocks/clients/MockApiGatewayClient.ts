import { Entitlement } from 'src/types';

import { IApiGatewayClient } from '../../clients/interfaces/IApiGatewayClient';
import { ContextLogger } from '../../logger/contextLogger';

/**
 * Mock implementation of ApiGatewayClient for testing
 * Provides predefined responses without making actual API calls
 */

export class MockApiGatewayClient implements IApiGatewayClient {
  constructor(private readonly logger: ContextLogger) {}

  /**
   * Override to return mock websites without making API calls
   */
  async fetchAllWebsites(entitlements: Entitlement[]): Promise<Entitlement[]> {
    await Promise.resolve();
    this.logger.info('Returning mock websites', {
      count: entitlements.length,
    });
    const mockWebsites: Entitlement[] = entitlements.map(e => ({
      ...e,
      websiteUrl: 'mock.com',
    }));
    return mockWebsites;
  }
}
