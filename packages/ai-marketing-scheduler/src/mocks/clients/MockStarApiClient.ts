import {
  IStarApiClient,
  StarApiPayload,
  StarApiResponse,
} from '../../clients/interfaces/IStarApiClient';
import { ContextLogger } from '../../logger/contextLogger';
import { ClientResponse } from '../../types/clientResponse';

/**
 * Mock implementation of StarApiClient for testing
 * Provides predefined responses without making actual API calls
 */
export class MockStarApiClient implements IStarApiClient {
  private updateElementCallCount = 0;
  private lastPayload: StarApiPayload | null = null;
  private mockResponses: StarApiResponse[] = [];
  private shouldFail = false;
  private failureError: string | null = null;

  public lastUpdateElementCall?: {
    payload: StarApiPayload;
    companyId?: string;
    actionId?: string;
  };

  constructor(private readonly logger: ContextLogger) {}

  /**
   * Mock implementation of updateElement
   * Returns predefined responses for testing different scenarios
   */
  async updateElement(
    payload: StarApiPayload,
    companyId?: string,
    actionId?: string,
  ): Promise<ClientResponse<StarApiResponse>> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    this.updateElementCallCount++;
    this.lastPayload = payload;
    this.lastUpdateElementCall = { payload, companyId, actionId };

    this.logger.debug('Mock: Updating element via STAR API', {
      payload,
      companyId,
      actionId,
      callCount: this.updateElementCallCount,
    });

    // Simulate failure if configured
    if (this.shouldFail) {
      const errorResponse: StarApiResponse = {
        error: this.failureError || 'Mock error: Update failed',
        requestId: `mock-request-${Date.now()}`,
        statusText: 'Internal Server Error',
      };

      this.logger.error('Mock: STAR API update failed', {
        payload,
        companyId,
        actionId,
        error: errorResponse.error,
        requestId: errorResponse.requestId,
      });

      throw new Error(errorResponse.error);
    }

    // Return custom response if configured, otherwise default success
    const response: StarApiResponse = this.mockResponses.shift() || {
      message: `Successfully updated ${payload.input.elementTag} element`,
      requestId: `mock-request-${Date.now()}`,
      statusText: 'OK',
    };

    this.logger.debug('Mock: STAR API update successful', {
      payload,
      companyId,
      actionId,
      response,
    });

    return {
      data: response,
      context: response.requestId
        ? { starApiRequestId: response.requestId }
        : {},
    };
  }

  /**
   * Configures the mock to fail on the next call
   * @param error Optional custom error message
   */
  setFailure(error?: string): void {
    this.shouldFail = true;
    this.failureError = error || null;
  }

  /**
   * Resets the failure state
   */
  resetFailure(): void {
    this.shouldFail = false;
    this.failureError = null;
  }

  /**
   * Adds a custom response to be returned by the next updateElement call
   * @param response The response to return
   */
  addMockResponse(response: StarApiResponse): void {
    this.mockResponses.push(response);
  }

  /**
   * Gets the number of times updateElement was called
   */
  getCallCount(): number {
    return this.updateElementCallCount;
  }

  /**
   * Gets the last payload passed to updateElement
   */
  getLastPayload(): StarApiPayload | null {
    return this.lastPayload;
  }

  /**
   * Resets all mock state
   */
  reset(): void {
    this.updateElementCallCount = 0;
    this.lastPayload = null;
    this.lastUpdateElementCall = undefined;
    this.mockResponses = [];
    this.shouldFail = false;
    this.failureError = null;
  }
}
