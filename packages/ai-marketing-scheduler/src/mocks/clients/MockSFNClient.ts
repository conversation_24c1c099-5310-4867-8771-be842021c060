import {
  <PERSON><PERSON>lient,
  StartExecutionCommand,
  StartExecutionCommandOutput,
} from '@aws-sdk/client-sfn';

import { ContextLogger } from '../../logger/contextLogger';

/**
 * Mock implementation of SFNClient for testing
 * Provides predefined responses without making actual AWS calls
 */
export class Mock<PERSON><PERSON>lient extends SFNClient {
  private readonly logger: ContextLogger;
  private simulateError: boolean;
  private actionIdsToFail: string[];

  constructor(
    logger: ContextLogger,
    options: { simulateError?: boolean; actionIdsToFail?: string[] } = {},
  ) {
    // Call parent constructor with empty config
    super({ region: 'us-east-1' });

    this.logger = logger;
    this.simulateError = options.simulateError ?? false;
    this.actionIdsToFail = options.actionIdsToFail ?? [];

    this.logger.info('Initialized MockSFNClient', {
      simulateError: this.simulateError,
      actionIdsToFail: this.actionIdsToFail,
    });
  }

  /**
   * Override send method to handle StartExecutionCommand
   */
  async send<TInput extends object, TOutput extends object>(
    command: TInput,
  ): Promise<TOutput> {
    // Wait a bit to simulate network latency
    await new Promise(resolve => setTimeout(resolve, 100));

    if (command instanceof StartExecutionCommand) {
      return this.handleStartExecution(command) as unknown as TOutput;
    }

    // Default behavior for other commands
    this.logger.warn('Unhandled command type in MockSFNClient', {
      commandType: command?.constructor?.name || 'Unknown',
    });

    return {} as TOutput;
  }

  /**
   * Handle StartExecutionCommand specifically
   */
  private async handleStartExecution(
    command: StartExecutionCommand,
  ): Promise<StartExecutionCommandOutput> {
    // Using await with a resolved promise to satisfy the linter
    await Promise.resolve();

    // Access the command input properly
    const commandInput = command.input;

    // Extract the input string from the command
    let inputStr = '{}';
    if (
      commandInput &&
      typeof commandInput === 'object' &&
      'input' in commandInput
    ) {
      inputStr = commandInput.input as string;
    }

    const payload = JSON.parse(inputStr) as Record<string, unknown>;

    const actionId = payload.actionId as string;

    this.logger.info('MockSFNClient received StartExecutionCommand', {
      stateMachineArn: commandInput.stateMachineArn,
      name: commandInput.name,
      actionId,
    });

    // Check if we should simulate an error for this action
    if (
      this.simulateError ||
      (actionId && this.actionIdsToFail.includes(actionId))
    ) {
      this.logger.info('MockSFNClient simulating error', { actionId });

      // Create an AccessDeniedException similar to the one in the error log
      const error = new Error(
        'User: arn:aws:iam::381475384502:user/egross is not authorized to access this resource',
      );
      error.name = 'AccessDeniedException';

      // Add a stack trace to make it look more realistic
      error.stack =
        'AccessDeniedException: User: arn:aws:iam::381475384502:user/egross is not authorized to access this resource\n' +
        '    at throwDefaultError (/node_modules/@smithy/smithy-client/dist-cjs/index.js:379:20)\n' +
        '    at Object.handleError (/node_modules/@smithy/smithy-client/dist-cjs/index.js:388:5)\n' +
        '    at de_CommandError (/node_modules/@aws-sdk/client-sfn/dist-cjs/index.js:2279:14)';

      throw error;
    }

    // Return a successful response
    const stateMachineName =
      commandInput.stateMachineArn?.split(':').pop() || 'mock-state-machine';
    const executionArn = `arn:aws:states:us-east-1:123456789012:execution:${stateMachineName}:${commandInput.name || 'mock-execution'}`;

    return {
      executionArn,
      startDate: new Date(),
      $metadata: {
        httpStatusCode: 200,
        requestId: `mock-request-${Date.now()}`,
      },
    };
  }
}
