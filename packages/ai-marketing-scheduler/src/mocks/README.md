# Mock Services for AWS Dev Sandbox Testing

This directory contains mock implementations of external services used by the AI Marketing Scheduler. These mocks allow Lambda functions to be tested in the AWS dev sandbox environment without requiring connectivity to external services outside the VPC.

## Overview

When Lambda functions are deployed in the AWS dev sandbox environment, they may not have access to external services due to VPC restrictions. These mock implementations provide a way to test the Lambda functions with realistic but simulated data.

## Available Mocks

- **MockTenantClient**: Provides mock implementations of tenant-related operations
- **MockSeoApiGatewayClient**: Provides mock implementations of API Gateway operations

## How to Use

### 1. Enable Mock Services

To use the mock services, set the `USE_MOCK_CLIENTS` environment variable to `true` in your .env file.

### 2. Testing with Mock Data

The mock implementations provide realistic but static data. You can modify the mock data in the respective mock client files to test different scenarios:

- `src/mocks/clients/MockTenantClient.ts` - Edit the `getAllEntitlements` method to modify mock entitlements
- `src/mocks/clients/MockSeoApiGatewayClient.ts` - Edit the `mockActions` array in the constructor to modify mock scheduled actions

## Implementation Details

The mock services are implemented as drop-in replacements for the real services. The service factory will automatically use the mock implementations when the `USE_MOCK_CLIENTS` environment variable is set to `true`.

### Example Lambda Handler

```typescript
import { serviceFactory } from '../factories';

export const handler = async (event, context) => {
  setAwsRequestId(context.awsRequestId);
  const logger = createLogger('lambda-handler');

  // This will automatically create a service instance with a mock client
  // if the USE_MOCK_CLIENTS environment variable is set to true
  const tenantService = serviceFactory.createTenantService(logger);
  
  // Continue with normal processing using the selected service
  // ...
};
```

## Testing with Mocks

For unit and integration testing, use the centralized test utilities from `src/utils/testUtils.ts`:

```typescript
import {
  setupTestMocks,
  createMockSeoApiGatewayClient,
  createMockTenantClient,
  createMockScheduledActions
} from '../utils/testUtils';

describe('MyTest', () => {
  beforeEach(() => {
    setupTestMocks(); // Sets up logger and base mocks
    
    // Use centralized mock factories
    const mockClient = createMockSeoApiGatewayClient();
    const mockData = createMockScheduledActions(5);
  });
});
```

This approach provides:

- **Consistency**: All tests use the same mock implementations
- **Maintainability**: Mock changes in one place affect all tests  
- **Type Safety**: Factory functions ensure correct interfaces
- **Realistic Data**: Mock factories create realistic test data

## Extending the Mocks

To add more mock implementations:

1. Create a new mock client in `src/mocks/clients/`
2. Update the `MockServiceFactory.ts` to include your new mock
3. Add a corresponding factory function in `src/utils/testUtils.ts` for testing
4. Update any Lambda handlers to use the mock service when appropriate
