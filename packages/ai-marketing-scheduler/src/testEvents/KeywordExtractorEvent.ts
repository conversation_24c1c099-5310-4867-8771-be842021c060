import { ScrapedPageType, ScraperResult } from 'src/types';

interface KeywordExtractorEvent {
  scraperResult: ScraperResult;
  actionId: string;
}
export const event: KeywordExtractorEvent = {
  scraperResult: {
    companyId: 'a7b4401f-a8be-440d-922f-7b133d4f2197',
    firecrawlId: 'firecrawl-3',
    mainHeading: {
      section_id: 'mortgage-calc',
      element_id: 'calc-element-1',
      content: 'Mortgage Payment Calculator',
      index: 0,
      tag: 'h1',
    },
    markdown:
      "# Mortgage Payment Calculator\n\nPlan your home purchase with our mortgage calculator. Get accurate payment estimates and explore different loan scenarios.\n\n## Contact Information\n- Phone: (*************\n- Email: info@ryan'sbeachhomes.com\n\n## Services\n- Residential Real Estate\n- Commercial Properties\n- Property Management\n- Market Analysis",
    mediaId: 'media-3',
    metaDescription:
      "Calculate your mortgage payments with Ryan's Beach Homes's easy-to-use calculator. Estimate rates, payments, and affordability.",
    metaTitle: "Mortgage Calculator - Ryan's Beach Homes",
    type: ScrapedPageType.MORTGAGE_CALCULATOR,
    url: 'https://ryansbeach.homes/calculator',
  },
  actionId: 'b020fbf2-a3f8-49c8-b417-f64a2dbfcd43',
};
