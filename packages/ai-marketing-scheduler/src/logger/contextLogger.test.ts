import { Logger } from '@aws-lambda-powertools/logger';

import { ContextLogger } from './contextLogger';

describe('ContextLogger', () => {
  let mockBaseLogger: jest.Mocked<Logger>;

  beforeEach(() => {
    mockBaseLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    } as any;
  });

  describe('Context Management', () => {
    test('should initialize with context', () => {
      const initialContext = {
        actionId: 'action-123',
        companyId: 'company-456',
      };

      const logger = new ContextLogger(mockBaseLogger, initialContext);

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
      });
    });

    test('should add context values', () => {
      const logger = new ContextLogger(mockBaseLogger);

      logger.addContextValue('actionId', 'action-123');
      logger.addContextValue('companyId', 'company-456');

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
      });
    });

    test('should set multiple context values', () => {
      const logger = new ContextLogger(mockBaseLogger);

      logger.setContext({
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
      });

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
      });
    });

    test('should clear specific context values', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
      });

      logger.clearContext('url', 'companyId');
      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
      });
    });

    test('should clear all context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      logger.clearAllContext();
      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {});
    });

    test('should filter undefined values', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: undefined,
        url: undefined,
        groupId: 'group-456',
        requestId: 'request-123',
      });

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        groupId: 'group-456',
        requestId: 'request-123',
      });
    });
  });

  describe('Logging Methods', () => {
    test('should support all log levels', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
      });

      logger.debug('Debug message', { extra: 'debug' });
      logger.info('Info message', { extra: 'info' });
      logger.warn('Warn message', { extra: 'warn' });
      logger.error('Error message', { extra: 'error' });

      expect(mockBaseLogger.debug).toHaveBeenCalledWith('Debug message', {
        actionId: 'action-123',
        extra: 'debug',
      });
      expect(mockBaseLogger.info).toHaveBeenCalledWith('Info message', {
        actionId: 'action-123',
        extra: 'info',
      });
      expect(mockBaseLogger.warn).toHaveBeenCalledWith('Warn message', {
        actionId: 'action-123',
        extra: 'warn',
      });
      expect(mockBaseLogger.error).toHaveBeenCalledWith('Error message', {
        actionId: 'action-123',
        extra: 'error',
      });
    });

    test('should merge context with log data', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      logger.info('Test message', {
        url: 'https://example.com',
        extra: 'data',
      });

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://example.com',
        extra: 'data',
      });
    });
  });

  describe('Child Loggers', () => {
    test('should create child with inherited context', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      const childLogger = parentLogger.createChild({
        url: 'https://child.com',
      });

      childLogger.info('Child message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Child message', {
        actionId: 'action-123',
        companyId: 'company-456',
        url: 'https://child.com',
      });
    });

    test('should create component logger with component context', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        lambdaName: 'publisher',
        actionId: 'action-123',
        companyId: 'company-456',
      });

      const serviceLogger = parentLogger.createComponentLogger({
        serviceName: 'TenantService',
      });

      serviceLogger.info('Service message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Service message', {
        lambdaName: 'publisher',
        actionId: 'action-123',
        companyId: 'company-456',
        serviceName: 'TenantService',
      });
    });

    test('should create nested component loggers', () => {
      const lambdaLogger = new ContextLogger(mockBaseLogger, {
        lambdaName: 'publisher',
        actionId: 'action-123',
      });

      const serviceLogger = lambdaLogger.createComponentLogger({
        serviceName: 'ScheduledActionService',
      });

      const clientLogger = serviceLogger.createComponentLogger({
        clientName: 'SeoApiGatewayClient',
      });

      clientLogger.info('Client message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Client message', {
        lambdaName: 'publisher',
        actionId: 'action-123',
        serviceName: 'ScheduledActionService',
        clientName: 'SeoApiGatewayClient',
      });
    });

    test('should only add defined component fields', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
      });

      const componentLogger = parentLogger.createComponentLogger({
        lambdaName: undefined,
        serviceName: 'MyService',
        clientName: undefined,
      });

      componentLogger.info('Component message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Component message', {
        actionId: 'action-123',
        serviceName: 'MyService',
      });
    });

    test('should not affect parent context', () => {
      const parentLogger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
      });

      const childLogger = parentLogger.createChild({
        url: 'https://child.com',
      });

      childLogger.addContextValue('groupId', 'group-789');

      parentLogger.info('Parent message');
      childLogger.info('Child message');

      expect(mockBaseLogger.info).toHaveBeenNthCalledWith(1, 'Parent message', {
        actionId: 'action-123',
      });
      expect(mockBaseLogger.info).toHaveBeenNthCalledWith(2, 'Child message', {
        actionId: 'action-123',
        url: 'https://child.com',
        groupId: 'group-789',
      });
    });
  });

  describe('Utility Methods', () => {
    test('should get current context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
        companyId: 'company-456',
      });

      const context = logger.getContext();

      expect(context).toEqual({
        actionId: 'action-123',
        companyId: 'company-456',
      });
    });

    test('should get base logger', () => {
      const logger = new ContextLogger(mockBaseLogger);

      expect(logger.getBaseLogger()).toBe(mockBaseLogger);
    });
  });

  describe('Datadog Custom Metrics', () => {
    test('should add single custom metric with dd.custom prefix', () => {
      const logger = new ContextLogger(mockBaseLogger);

      logger.addCustomMetric('processingTime', 145);
      logger.addCustomMetric('itemCount', 10);

      logger.info('Test message');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Test message', {
        'dd.custom.processingTime': 145,
        'dd.custom.itemCount': 10,
      });
    });

    test('should add multiple custom metrics', () => {
      const logger = new ContextLogger(mockBaseLogger);

      logger.addCustomMetrics({
        successCount: 8,
        failureCount: 2,
        totalDuration: 1250,
      });

      logger.info('Batch completed');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Batch completed', {
        'dd.custom.successCount': 8,
        'dd.custom.failureCount': 2,
        'dd.custom.totalDuration': 1250,
      });
    });

    test('should combine custom metrics with regular context', () => {
      const logger = new ContextLogger(mockBaseLogger, {
        actionId: 'action-123',
      });

      logger.addContextValue('companyId', 'company-456');
      logger.addCustomMetric('recommendationsProcessed', 5);

      logger.info('Processing complete');

      expect(mockBaseLogger.info).toHaveBeenCalledWith('Processing complete', {
        actionId: 'action-123',
        companyId: 'company-456',
        'dd.custom.recommendationsProcessed': 5,
      });
    });
  });
});
