import { Logger } from '@aws-lambda-powertools/logger';

import { createLogger } from './createLogger';

/**
 * Adds dd.custom prefix to a key for custom business metrics
 *
 * @param key The key to prefix
 * @returns The prefixed key
 */
function addCustomPrefix(key: string): string {
  return `dd.custom.${key}`;
}

export interface LoggerContext {
  [key: string]: unknown;
}

export interface ComponentContext {
  lambdaName?: string;
  serviceName?: string;
  clientName?: string;
  componentName?: string;
  layer?: string;
}

export class ContextLogger {
  private logger: Logger;
  private context: LoggerContext;

  constructor(logger: Logger, initialContext?: LoggerContext) {
    this.logger = logger;
    this.context = initialContext || {};
  }

  /**
   * Updates the logger context with new values
   * Existing values are preserved unless overwritten
   */
  setContext(newContext: Partial<LoggerContext>): void {
    this.context = {
      ...this.context,
      ...newContext,
    };
  }

  /**
   * Adds a single context value
   */
  addContextValue(key: keyof LoggerContext, value: string): void {
    this.context[key] = value;
  }

  /**
   * Clears specific context values
   */
  clearContext(...keys: (keyof LoggerContext)[]): void {
    keys.forEach(key => {
      delete this.context[key];
    });
  }

  /**
   * Clears all context
   */
  clearAllContext(): void {
    this.context = {};
  }

  /**
   * Adds a custom metric with dd.custom prefix
   * Use this for business metrics that should be indexed in Datadog
   *
   * @param key The metric key (will be prefixed with dd.custom.)
   * @param value The metric value
   */
  addCustomMetric(key: string, value: unknown): void {
    this.context[addCustomPrefix(key)] = value;
  }

  /**
   * Adds multiple custom metrics with dd.custom prefix
   *
   * @param metrics Object containing metric key-value pairs
   */
  addCustomMetrics(metrics: Record<string, unknown>): void {
    Object.entries(metrics).forEach(([key, value]) => {
      this.addCustomMetric(key, value);
    });
  }

  /**
   * Gets the current context
   */
  getContext(): LoggerContext {
    return { ...this.context };
  }

  /**
   * Merges the context with additional data for a single log entry
   */
  private mergeContext(
    data?: Record<string, unknown>,
  ): Record<string, unknown> {
    // Filter out undefined values from context
    const cleanContext = Object.entries(this.context).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, unknown>,
    );

    return {
      ...cleanContext,
      ...data,
    };
  }

  /**
   * Log methods that automatically include context
   */
  debug(message: string, data?: Record<string, unknown>): void {
    this.logger.debug(message, this.mergeContext(data));
  }

  info(message: string, data?: Record<string, unknown>): void {
    this.logger.info(message, this.mergeContext(data));
  }

  warn(message: string, data?: Record<string, unknown>): void {
    this.logger.warn(message, this.mergeContext(data));
  }

  error(message: string, data?: Record<string, unknown>): void {
    this.logger.error(message, this.mergeContext(data));
  }

  /**
   * Creates a child logger with inherited context for BUSINESS/OPERATIONAL data
   *
   * Use this method when you need to add:
   * - Business context (userId, tenantId, companyId, actionId)
   * - Request-specific data (correlationId, requestId, apiVersion)
   * - Processing context (batchId, itemId, processingStep)
   * - Any arbitrary key-value pairs for debugging
   *
   * Example:
   * ```typescript
   * const userLogger = logger.createChild({
   *   userId: '12345',
   *   tenantId: 'abc-corp',
   *   operation: 'updateProfile'
   * });
   * ```
   *
   * @param additionalContext Any key-value pairs to add to the logger context
   * @returns A new ContextLogger with combined context
   */
  createChild(additionalContext?: Partial<LoggerContext>): ContextLogger {
    return new ContextLogger(this.logger, {
      ...this.context,
      ...additionalContext,
    });
  }

  /**
   * Creates a child logger with ARCHITECTURAL COMPONENT identification
   *
   * Use this method to identify WHERE in your system architecture the log originated:
   * - lambdaName: Which Lambda function (e.g., 'publisher', 'processor')
   * - serviceName: Which service class (e.g., 'TenantService', 'ScheduledActionService')
   * - clientName: Which client/API wrapper (e.g., 'SeoApiGatewayClient', 'S3Client')
   * - componentName: Other architectural components (e.g., 'middleware', 'validator')
   * - layer: Architectural layer (e.g., 'api', 'business', 'data')
   *
   * This enables filtering logs in CloudWatch by architectural components:
   * - "Show me all logs from the TenantService"
   * - "Show me all logs from the publisher Lambda"
   * - "Track a request through lambda → service → client"
   *
   * Example:
   * ```typescript
   * const serviceLogger = logger.createComponentLogger({
   *   serviceName: 'TenantService'
   * });
   * ```
   *
   * @param component Architectural component identifiers
   * @param additionalContext Optional additional context to include
   * @returns A new ContextLogger with component identification
   */
  createComponentLogger(
    component: Partial<ComponentContext>,
    additionalContext?: Partial<LoggerContext>,
  ): ContextLogger {
    const componentContext: LoggerContext = {};

    // Only add defined component fields to avoid cluttering logs
    if (component.lambdaName)
      componentContext.lambdaName = component.lambdaName;
    if (component.serviceName)
      componentContext.serviceName = component.serviceName;
    if (component.clientName)
      componentContext.clientName = component.clientName;
    if (component.componentName)
      componentContext.componentName = component.componentName;
    if (component.layer) componentContext.layer = component.layer;

    return new ContextLogger(this.logger, {
      ...this.context,
      ...componentContext,
      ...additionalContext,
    });
  }

  /**
   * Gets the underlying AWS Lambda Powertools logger
   * Use this for advanced scenarios or when you need direct access
   */
  getBaseLogger(): Logger {
    return this.logger;
  }
}

/**
 * Creates a context-aware logger
 * @param functionName The name of the Lambda function (will be added as a tag, not the service name)
 * @param context Optional Lambda context
 * @param initialLogContext Optional initial logger context
 */
export function createContextLogger(
  functionName: string,
  context?: any,
  initialLogContext?: LoggerContext,
): ContextLogger {
  const baseLogger = createLogger(functionName, context);
  return new ContextLogger(baseLogger, initialLogContext);
}
