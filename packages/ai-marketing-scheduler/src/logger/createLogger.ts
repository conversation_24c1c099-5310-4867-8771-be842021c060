import { Logger } from '@aws-lambda-powertools/logger';
import { Context } from 'aws-lambda';

import * as packageJson from '../../package.json';
import { getConfig } from '../config';

// Module-level variable to store the AWS request ID
let awsRequestId: string | null = null;

/**
 * Sets the AWS request ID from the Lambda context
 */
export function setAwsRequestId(requestId: string): void {
  awsRequestId = requestId;
}

/**
 * Gets the current AWS request ID or returns a placeholder if not set
 */
function getAwsRequestId(): string {
  return awsRequestId || 'unknown-request-id';
}

/**
 * Creates a base logger instance
 * This is separated to avoid circular dependencies
 */
export function createLogger(functionName: string, context?: Context): Logger {
  // Automatically set the AWS request ID if context is provided
  if (context?.awsRequestId) {
    setAwsRequestId(context.awsRequestId);
  }
  const config = getConfig();
  const logLevel = process.env.LOG_LEVEL || 'INFO';

  return new Logger({
    logLevel: logLevel as 'INFO' | 'DEBUG' | 'WARN' | 'ERROR',
    // Always use the consistent service name for all lambdas
    serviceName: 'ai-marketing-scheduler',
    persistentLogAttributes: {
      environment: config.environment,
      awsRequestId: getAwsRequestId(),
      // Add Datadog service attributes
      'dd.env': config.environment,
      // Must be the same as the service name in service.datadog.yaml to get picked up by APM
      'dd.service': 'ai-marketing-scheduler',
      'dd.version': packageJson.version,
      // Add the individual function name as a tag for filtering
      'dd.custom.function_name': functionName,
    },
  });
}

/**
 * Resets the AWS request ID - useful for testing
 */
export function resetAwsRequestId(): void {
  awsRequestId = null;
}
