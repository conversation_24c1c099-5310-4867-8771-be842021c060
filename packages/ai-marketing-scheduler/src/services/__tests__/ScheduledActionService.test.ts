import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import { StoreSEODraftResponse } from '../../clients/types/seoApiGateway';
import {
  CheckActionError,
  DraftActionConsumerError,
  GetActionError,
  ScheduledActionNotFoundError,
  SeoApiGatewayClientError,
} from '../../errors';
import { StoreSEODraftError } from '../../errors/StoreSEODraftError';
import { ContextLogger } from '../../logger/contextLogger';
import {
  ScrapedPageType,
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import {
  createMockScheduledActions,
  createMockSeoApiGatewayClient,
  createMockContextLogger,
} from '../../utils/testUtils';
import { ScheduledActionService } from '../ScheduledActionService';

jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
}));

describe('ScheduledActionService', () => {
  let mockSeoApiGatewayClient: jest.Mocked<ISeoApiGatewayClient>;
  let mockLogger: jest.Mocked<ContextLogger>;
  let scheduledActionService: ScheduledActionService;

  beforeEach(() => {
    // Create mocks using centralized factory
    mockLogger = createMockContextLogger();
    mockSeoApiGatewayClient = createMockSeoApiGatewayClient();

    // Create service instance
    scheduledActionService = new ScheduledActionService(
      mockSeoApiGatewayClient,
      mockLogger,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDraftPendingActions', () => {
    const mockActions = createMockScheduledActions();

    it('should fetch draft pending actions successfully', async () => {
      // Mock API client response
      mockSeoApiGatewayClient.getDraftPendingActions.mockResolvedValueOnce(
        mockActions,
      );

      // Call the method
      const result = await scheduledActionService.getDraftPendingActions();

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.getDraftPendingActions,
      ).toHaveBeenCalledTimes(1);

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle SeoApiGatewayClientError when fetching draft pending actions', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.getDraftPendingActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.getDraftPendingActions(),
      ).rejects.toThrow(DraftActionConsumerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'API Gateway client error when fetching draft pending actions',
        {
          error: mockError.message,
          cause: 'Original error',
        },
      );
    });

    it('should handle unexpected errors when fetching draft pending actions', async () => {
      // Mock API client error
      const mockError = new Error('Unexpected error');
      mockSeoApiGatewayClient.getDraftPendingActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.getDraftPendingActions(),
      ).rejects.toThrow(DraftActionConsumerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Unexpected error when fetching draft pending actions',
        {
          error: 'Unexpected error',
        },
      );
    });
  });

  describe('getSurfacedActions', () => {
    const mockScheduledToBePublishedAt = '2023-10-15';
    const mockActions = createMockScheduledActions(3).map(action => ({
      ...action,
      status: Status.SURFACED,
    }));

    it('should fetch surfaced actions successfully', async () => {
      // Mock API client response
      mockSeoApiGatewayClient.getSurfacedActions.mockResolvedValueOnce(
        mockActions,
      );

      // Call the method
      const result = await scheduledActionService.getSurfacedActions(
        mockScheduledToBePublishedAt,
      );

      // Verify API client call
      expect(mockSeoApiGatewayClient.getSurfacedActions).toHaveBeenCalledWith(
        mockScheduledToBePublishedAt,
      );

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle SeoApiGatewayClientError when fetching surfaced actions', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.getSurfacedActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.getSurfacedActions(mockScheduledToBePublishedAt),
      ).rejects.toThrow(DraftActionConsumerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'API Gateway client error when fetching surfaced actions',
        {
          error: mockError.message,
          cause: 'Original error',
          scheduledToBePublishedAt: mockScheduledToBePublishedAt,
        },
      );
    });

    it('should handle unexpected errors when fetching surfaced actions', async () => {
      // Mock API client error
      const mockError = new Error('Unexpected error');
      mockSeoApiGatewayClient.getSurfacedActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.getSurfacedActions(mockScheduledToBePublishedAt),
      ).rejects.toThrow(DraftActionConsumerError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Unexpected error when fetching surfaced actions',
        {
          error: 'Unexpected error',
          scheduledToBePublishedAt: mockScheduledToBePublishedAt,
        },
      );
    });
  });

  describe('updateStatus', () => {
    const mockActionId = 'action-123';
    const mockStatus = Status.HUMAN_QA_PENDING;

    it('should update scheduled action status successfully', async () => {
      // Mock API client response
      const mockUpdatedAction = createMockScheduledActions(1, [
        {
          id: mockActionId,
          status: mockStatus,
          workflowType: WorkflowType.SEO,
          companyId: 'company-1',
        },
      ])[0];
      mockSeoApiGatewayClient.updateScheduledAction.mockResolvedValueOnce(
        mockUpdatedAction,
      );

      // Call the method
      const result = await scheduledActionService.updateStatus(
        mockActionId,
        mockStatus,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, { status: mockStatus });

      // Verify result
      expect(result).toEqual({
        id: mockActionId,
        success: true,
        workflowType: undefined,
      });
    });

    it('should handle SeoError when updating status', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.updateScheduledAction.mockRejectedValueOnce(
        mockError,
      );

      // Call the method
      const result = await scheduledActionService.updateStatus(
        mockActionId,
        mockStatus,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, { status: mockStatus });

      // Verify logger calls (from update only)
      expect(mockLogger.error).toHaveBeenCalledWith(
        'API Gateway client error when updating scheduled action',
        {
          actionId: mockActionId,
          delta: { status: mockStatus },
          error: 'API error',
          cause: 'Original error',
        },
      );

      // Verify result
      expect(result).toEqual({
        id: mockActionId,
        success: false,
        // Error message from ScheduledActionUpdateError
        error:
          'Failed to update scheduled action action-123 to status HUMAN_QA_PENDING',
        workflowType: undefined,
      });
    });

    it('should handle scheduled action not found error', async () => {
      // Mock API client error with "not found" message
      const mockError = new SeoApiGatewayClientError(
        'Scheduled action not found',
        new Error('Action does not exist'),
      );
      mockSeoApiGatewayClient.updateScheduledAction.mockRejectedValueOnce(
        mockError,
      );

      // Call the method - it will catch the ScheduledActionNotFoundError and return result
      const result = await scheduledActionService.updateStatus(
        mockActionId,
        mockStatus,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, { status: mockStatus });

      // Verify error logging - this verifies that ScheduledActionNotFoundError was thrown internally
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Scheduled action not found',
        {
          actionId: mockActionId,
          delta: { status: mockStatus },
          error: 'Scheduled action not found',
        },
      );

      // Verify result contains the ScheduledActionNotFoundError message
      expect(result).toEqual({
        id: mockActionId,
        success: false,
        error: 'Scheduled action not found: action-123',
        workflowType: undefined,
      });
    });

    it('should handle unexpected errors when updating status', async () => {
      // Mock API client error
      const mockError = new Error('Unexpected error');
      mockSeoApiGatewayClient.updateScheduledAction.mockRejectedValueOnce(
        mockError,
      );

      // Call the method
      const result = await scheduledActionService.updateStatus(
        mockActionId,
        mockStatus,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, { status: mockStatus });

      // Verify logger calls (from update only)
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Unexpected error when updating scheduled action',
        {
          actionId: mockActionId,
          delta: { status: mockStatus },
          error: 'Unexpected error',
        },
      );

      // Verify result
      expect(result).toEqual({
        id: mockActionId,
        success: false,
        // Error message from ScheduledActionUpdateError
        error:
          'Failed to update scheduled action action-123 to status HUMAN_QA_PENDING',
        workflowType: undefined,
      });
    });
  });

  describe('updateWithFailure', () => {
    const mockActionId = 'action-123';
    const mockFailureReason = { reason: 'Something went wrong' };

    it('should update scheduled action with failure successfully', async () => {
      // Mock API client response
      const mockFailedAction = createMockScheduledActions(1, [
        {
          id: mockActionId,
          status: Status.FAILED,
          workflowType: WorkflowType.SEO,
          companyId: 'company-1',
          failureReason: mockFailureReason,
        },
      ])[0];
      mockSeoApiGatewayClient.updateScheduledAction.mockResolvedValueOnce(
        mockFailedAction,
      );

      // Call the method
      const result = await scheduledActionService.updateWithFailure(
        mockActionId,
        mockFailureReason,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, {
        status: Status.FAILED,
        failureReason: mockFailureReason,
      });

      // Verify result
      expect(result).toEqual({
        id: mockActionId,
        success: false,
        error: JSON.stringify(mockFailureReason, null, 2),
        workflowType: undefined,
      });
    });

    it('should handle errors when updating with failure', async () => {
      // Mock API client error
      const mockError = new Error('Update error');
      mockSeoApiGatewayClient.updateScheduledAction.mockRejectedValueOnce(
        mockError,
      );

      // Call the method
      const result = await scheduledActionService.updateWithFailure(
        mockActionId,
        mockFailureReason,
      );

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.updateScheduledAction,
      ).toHaveBeenCalledWith(mockActionId, {
        status: Status.FAILED,
        failureReason: mockFailureReason,
      });

      // Verify logger calls (from update only)
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Unexpected error when updating scheduled action',
        {
          actionId: mockActionId,
          delta: {
            status: Status.FAILED,
            failureReason: mockFailureReason,
          },
          error: 'Update error',
        },
      );

      // Verify result
      expect(result).toEqual({
        id: mockActionId,
        success: false,
        // Error message from ScheduledActionUpdateError
        error: 'Failed to update scheduled action action-123 to status FAILED',
        workflowType: undefined,
      });
    });
  });

  describe('getUpcomingActions', () => {
    const mockCompanyId = 'company-123';
    const mockWorkflowType = WorkflowType.SEO;
    const mockActions: ScheduledAction[] = [
      {
        id: 'action-1',
        companyId: mockCompanyId,
        workflowType: mockWorkflowType,
        status: Status.DRAFT_PENDING,
        createdAt: '2025-01-01T00:00:00Z',
        updatedAt: '2025-01-01T00:00:00Z',
        surfacedAt: null,
        scheduledToBeSurfacedAt: null,
        scheduledToBePublishedAt: null,
        publishedAt: null,
        contentPayload: {},
        generationPayload: { key: 'value' },
        failureReason: {},
        executionName: 'action-1-timestamp',
        executionArn: 'an-actual-arn',
      },
    ];

    it('should fetch upcoming actions successfully', async () => {
      // Mock API client response
      mockSeoApiGatewayClient.getUpcomingActions.mockResolvedValueOnce(
        mockActions,
      );

      // Call the method
      const result = await scheduledActionService.getUpcomingActions(
        mockCompanyId,
        mockWorkflowType,
      );

      // Verify API client call
      expect(mockSeoApiGatewayClient.getUpcomingActions).toHaveBeenCalledWith(
        mockCompanyId,
        mockWorkflowType,
      );

      // Verify logger calls
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Fetching scheduled actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
        },
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Retrieved upcoming actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          total: mockActions.length,
        },
      );

      // Verify result
      expect(result).toEqual(mockActions);
    });

    it('should handle errors when fetching upcoming actions', async () => {
      // Mock API client error
      const mockError = new Error('Failed to fetch upcoming actions');
      mockSeoApiGatewayClient.getUpcomingActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.getUpcomingActions(
          mockCompanyId,
          mockWorkflowType,
        ),
      ).rejects.toThrow(mockError);

      // Verify logger calls
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Fetching scheduled actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
        },
      );
    });

    it('should handle SeoApiGatewayClientError when fetching upcoming actions', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.getUpcomingActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw DraftActionConsumerError
      await expect(
        scheduledActionService.getUpcomingActions(
          mockCompanyId,
          mockWorkflowType,
        ),
      ).rejects.toThrow(DraftActionConsumerError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when fetching upcoming actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          error: 'API error',
          cause: 'Original error',
        },
      );
    });
  });

  describe('getAction', () => {
    const mockActionId = 'action-123';
    const mockAction = createMockScheduledActions(1)[0];

    it('should fetch a scheduled action successfully', async () => {
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(mockAction);

      const result = await scheduledActionService.getAction(mockActionId);

      expect(mockSeoApiGatewayClient.getAction).toHaveBeenCalledWith(
        mockActionId,
      );
      expect(result).toEqual(mockAction);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Fetching scheduled action',
        {
          actionId: mockActionId,
        },
      );
    });

    it('should return null when action is not found', async () => {
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(null);

      const result = await scheduledActionService.getAction(mockActionId);

      expect(result).toBeNull();
      expect(mockSeoApiGatewayClient.getAction).toHaveBeenCalledWith(
        mockActionId,
      );
    });

    it('should handle SeoApiGatewayClientError and throw GetActionError', async () => {
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Network error'),
      );
      mockSeoApiGatewayClient.getAction.mockRejectedValueOnce(mockError);

      await expect(
        scheduledActionService.getAction(mockActionId),
      ).rejects.toThrow(GetActionError);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when fetching scheduled action',
        {
          actionId: mockActionId,
          error: mockError.message,
          cause: mockError.cause?.message,
        },
      );
    });
  });

  describe('checkAction', () => {
    const mockActionId = 'action-123';
    const mockFieldsToCheck = ['field1', 'field2'];
    const mockAction = {
      ...createMockScheduledActions(1)[0],
      generationPayload: {
        field1: 'value1',
        field2: 'value2',
      },
    };

    it('should check action successfully and return skipStep true when all fields exist', async () => {
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(mockAction);

      const result = await scheduledActionService.checkAction(
        mockActionId,
        mockFieldsToCheck,
      );

      expect(mockSeoApiGatewayClient.getAction).toHaveBeenCalledWith(
        mockActionId,
      );
      expect(result).toEqual({
        action: mockAction,
        skipStep: true,
      });
      expect(mockLogger.info).toHaveBeenCalledWith('Checking action', {
        actionId: mockActionId,
        fieldsToCheck: mockFieldsToCheck,
      });
    });

    it('should check action successfully and return skipStep false when some fields are missing', async () => {
      const actionWithMissingField = {
        ...mockAction,
        generationPayload: {
          field1: 'value1',
        },
      };
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(
        actionWithMissingField,
      );

      const result = await scheduledActionService.checkAction(
        mockActionId,
        mockFieldsToCheck,
      );

      expect(result).toEqual({
        action: actionWithMissingField,
        skipStep: false,
      });
    });

    it('should handle null generationPayload and return skipStep false', async () => {
      const actionWithoutPayload = {
        ...mockAction,
        generationPayload: null,
      };
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(
        actionWithoutPayload,
      );

      const result = await scheduledActionService.checkAction(
        mockActionId,
        mockFieldsToCheck,
      );

      expect(result).toEqual({
        action: actionWithoutPayload,
        skipStep: false,
      });
    });

    it('should check action successfully and return skipStep false when some fields are null', async () => {
      const actionWithFalsyFields = {
        ...mockAction,
        generationPayload: {
          field1: 'value1',
          field2: null,
        },
      };
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(
        actionWithFalsyFields,
      );

      const result = await scheduledActionService.checkAction(
        mockActionId,
        mockFieldsToCheck,
      );

      expect(result).toEqual({
        action: actionWithFalsyFields,
        skipStep: false,
      });
    });

    it('should check action successfully and return skipStep true when some fields are falsey', async () => {
      const actionWithFalsyFields = {
        ...mockAction,
        generationPayload: {
          field1: 'value1',
          field2: false,
          field3: 0,
          field4: '',
        },
      };
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(
        actionWithFalsyFields,
      );

      const result = await scheduledActionService.checkAction(mockActionId, [
        'field1',
        'field2',
        'field3',
        'field4',
      ]);

      expect(result).toEqual({
        action: actionWithFalsyFields,
        skipStep: true,
      });
    });

    it('should throw ScheduledActionNotFoundError when action is not found', async () => {
      mockSeoApiGatewayClient.getAction.mockResolvedValueOnce(null);

      await expect(
        scheduledActionService.checkAction(mockActionId, mockFieldsToCheck),
      ).rejects.toThrow(ScheduledActionNotFoundError);
    });

    it('should handle SeoApiGatewayClientError and throw CheckActionError', async () => {
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Network error'),
      );
      mockSeoApiGatewayClient.getAction.mockRejectedValueOnce(mockError);

      await expect(
        scheduledActionService.checkAction(mockActionId, mockFieldsToCheck),
      ).rejects.toThrow(CheckActionError);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when checking action',
        {
          actionId: mockActionId,
          fieldsToCheck: mockFieldsToCheck,
          error: mockError.message,
          cause: mockError.cause?.message,
        },
      );
    });
  });

  describe('createScheduledActions', () => {
    const mockCompanyId = 'company-123';
    const mockWorkflowType = WorkflowType.SEO;
    const mockCount = 3;

    it('should create scheduled actions successfully', async () => {
      // Mock API client response
      mockSeoApiGatewayClient.createOwedActions.mockResolvedValue(
        createMockScheduledActions(1)[0],
      );

      // Call the method
      await scheduledActionService.createScheduledActions(
        mockCompanyId,
        mockWorkflowType,
        mockCount,
        { websiteUrl: 'https://example.com' },
      );

      // Verify API client calls
      expect(mockSeoApiGatewayClient.createOwedActions).toHaveBeenCalledTimes(
        mockCount,
      );
      expect(mockSeoApiGatewayClient.createOwedActions).toHaveBeenCalledWith(
        mockCompanyId,
        mockWorkflowType,
        { websiteUrl: 'https://example.com' },
      );

      // Verify logger calls
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Creating owed draft actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          count: mockCount,
        },
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Created owed draft actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          count: mockCount,
        },
      );
    });

    it('should handle errors when creating scheduled actions', async () => {
      // Mock API client error
      const mockError = new Error('Failed to create action');
      mockSeoApiGatewayClient.createOwedActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.createScheduledActions(
          mockCompanyId,
          mockWorkflowType,
          mockCount,
          { websiteUrl: 'https://example.com' },
        ),
      ).rejects.toThrow(mockError);

      // Verify logger calls
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Creating owed draft actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          count: mockCount,
        },
      );
    });

    it('should handle SeoApiGatewayClientError when creating scheduled actions', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.createOwedActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw DraftActionConsumerError
      await expect(
        scheduledActionService.createScheduledActions(
          mockCompanyId,
          mockWorkflowType,
          mockCount,
          { websiteUrl: 'https://example.com' },
        ),
      ).rejects.toThrow(DraftActionConsumerError);

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when creating owed draft actions',
        {
          companyId: mockCompanyId,
          workflowType: mockWorkflowType,
          error: 'API error',
          cause: 'Original error',
        },
      );
    });
  });

  describe('findActionsToSurface', () => {
    it('should fetch actions to surface successfully', async () => {
      // Mock API client response
      const mockActionsByCompany = [
        {
          companyId: 'company-1',
          actions: createMockScheduledActions(2, [
            { companyId: 'company-1' },
            { companyId: 'company-1' },
          ]),
        },
        {
          companyId: 'company-2',
          actions: createMockScheduledActions(1, [{ companyId: 'company-2' }]),
        },
      ];
      mockSeoApiGatewayClient.processActionsToSurfaceInBatches.mockImplementation(
        async callback => {
          await callback(mockActionsByCompany);
        },
      );

      // Call the method
      const result = await scheduledActionService.findActionsToSurface();

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.processActionsToSurfaceInBatches,
      ).toHaveBeenCalledTimes(1);

      // Verify result
      expect(result).toEqual(mockActionsByCompany);
      expect(result).toHaveLength(2);
      expect(result[0].actions).toHaveLength(2);
      expect(result[1].actions).toHaveLength(1);
    });

    it('should handle empty response', async () => {
      // Mock API client response with empty callback (no batches)
      mockSeoApiGatewayClient.processActionsToSurfaceInBatches.mockImplementation(
        async () => {
          // No batches to process - callback never called
        },
      );

      // Call the method
      const result = await scheduledActionService.findActionsToSurface();

      // Verify API client call
      expect(
        mockSeoApiGatewayClient.processActionsToSurfaceInBatches,
      ).toHaveBeenCalledTimes(1);

      // Verify result
      expect(result).toEqual([]);
    });

    it('should handle SeoApiGatewayClientError when fetching actions to surface', async () => {
      // Mock API client error
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.processActionsToSurfaceInBatches.mockRejectedValueOnce(
        mockError,
      );

      // Call the method and expect it to throw
      await expect(
        scheduledActionService.findActionsToSurface(),
      ).rejects.toThrow('API error');

      // Verify error logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when fetching actions to surface',
        {
          error: 'API error',
          cause: 'Original error',
        },
      );
    });
  });
  describe('storeSEODraft', () => {
    const mockCompanyId = 'company-123';
    const mockActionId = 'action-456';
    const mockScraperResult: ScraperResult = {
      firecrawlId: 'some-id',
      url: 'some-url.com',
      type: ScrapedPageType.HOMEPAGE,
      markdown: 'some mrk',
      metaTitle: 'Test Title',
      metaDescription: 'Test Description',
      mainHeading: {
        section_id: 'test-section',
        element_id: 'test-element-1',
        tag: 'h1',
        index: 0,
        content: 'Test Heading',
      },
      mediaId: 'some-media-id',
      companyId: mockCompanyId,
    };
    const mockKeywords = 'test,seo,keywords';
    const mockRecommendations = {
      metaTitle: {
        currentValue: 'Test Title',
        recommendationValue: 'Improved Test Title',
        reasoning: 'This is a test reasoning',
      },
      metaDescription: {
        currentValue: 'Test Description',
        recommendationValue: 'Improved Test Description',
        reasoning: 'This is a test reasoning',
      },
      mainHeading: {
        currentValue: 'Test Heading',
        recommendationValue: 'Improved Test Heading',
        reasoning: 'This is a test reasoning',
      },
    };

    it('should store SEO draft successfully', async () => {
      const mockResponse = {
        savedKeyword: {
          id: 'keyword-1',
          keyword: 'test',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        savedRecommendations: [
          {
            id: 'rec-1',
            scrapeId: 'scrape-1',
            groupId: 'group-1',
            type: 'META_TITLE',
            currentValue: 'Test Title',
            recommendationValue: 'Improved Test Title',
            reasoning: 'This is a test reasoning',
            status: 'PENDING',
            rejectionReason: null,
            metadata: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ],
        savedScrape: {
          id: 'scrape-1',
          companyId: mockCompanyId,
          scrapedPageId: 'page-1',
          rawHtml: '<html>test</html>',
          markdown: '# Test',
          currentScrapedValues: {},
          mediaId: 'media-1',
          scrapedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          scrapedPage: mockScraperResult,
          recommendations: [],
        },
        savedPage: mockScraperResult,
        savedPageKeyword: {
          id: 'page-keyword-1',
          scrapedPageId: 'page-1',
          keywordId: 'keyword-1',
          originalRank: 1,
          currentRank: 1,
          rankCheckedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          companyId: mockCompanyId,
        },
        savedGroup: {
          id: 'group-1',
          companyId: mockCompanyId,
          keywordId: 'keyword-1',
          title: 'Test Group',
          description: 'Test Description',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
      } as unknown as StoreSEODraftResponse;

      mockSeoApiGatewayClient.storeSEODraft.mockResolvedValueOnce(mockResponse);

      const result = await scheduledActionService.storeSEODraft(
        mockCompanyId,
        mockActionId,
        mockScraperResult,
        mockKeywords,
        mockRecommendations,
      );

      expect(mockSeoApiGatewayClient.storeSEODraft).toHaveBeenCalledWith(
        mockCompanyId,
        mockActionId,
        mockScraperResult,
        mockKeywords,
        mockRecommendations,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle SeoApiGatewayClientError when storing SEO draft', async () => {
      const mockError = new SeoApiGatewayClientError(
        'API error',
        new Error('Original error'),
      );
      mockSeoApiGatewayClient.storeSEODraft.mockRejectedValueOnce(mockError);

      await expect(
        scheduledActionService.storeSEODraft(
          mockCompanyId,
          mockActionId,
          mockScraperResult,
          mockKeywords,
          mockRecommendations,
        ),
      ).rejects.toThrow(StoreSEODraftError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error when storing SEO draft',
        {
          companyId: mockCompanyId,
          actionId: mockActionId,
          error: 'API error',
          cause: 'Original error',
        },
      );
    });

    it('should handle unexpected errors when storing SEO draft', async () => {
      const mockError = new Error('Unexpected error');
      mockSeoApiGatewayClient.storeSEODraft.mockRejectedValueOnce(mockError);

      await expect(
        scheduledActionService.storeSEODraft(
          mockCompanyId,
          mockActionId,
          mockScraperResult,
          mockKeywords,
          mockRecommendations,
        ),
      ).rejects.toThrow(StoreSEODraftError);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Unexpected error when storing SEO draft',
        {
          companyId: mockCompanyId,
          actionId: mockActionId,
          error: 'Unexpected error',
        },
      );
    });
  });
});
