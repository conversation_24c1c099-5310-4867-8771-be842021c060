import { Entitlement, WorkflowType } from '../../types';
import { createMockContextLogger } from '../../utils/testUtils';
import {
  chunkEntitlements,
  chunkKeywords,
  getWorkflowType,
  getUnits,
} from '../EntitlementService';

jest.mock('../../config', () => ({
  getConfig: jest.fn().mockReturnValue({
    seoAutomationApi: {
      url: 'https://seo-automation-api.com',
    },
    tenantApi: {
      productIds: ['5c9b746c-7327-42ce-b998-ce707e7cee44'],
    },
  }),
}));

const mockLogger = createMockContextLogger();

describe('EntitlementService', () => {
  describe('getWorkflowType', () => {
    it('should return BLOG for automated blogs product', () => {
      const entitlement: Entitlement = {
        productId: '5c9b746c-7327-42ce-b998-ce707e7cee44',
        companyId: 'test-company',
        productName: 'Automated Blogs',
        units: 4,
      } as Entitlement;

      expect(getWorkflowType(entitlement, mockLogger)).toBe(WorkflowType.BLOG);
    });

    it('should return SEO for non-automated blogs product', () => {
      const entitlement: Entitlement = {
        productId: 'other-id',
        companyId: 'test-company',
        productName: 'SEO Package',
        units: 4,
      } as Entitlement;

      expect(getWorkflowType(entitlement, mockLogger)).toBe(WorkflowType.SEO);
    });
  });

  describe('chunkEntitlements', () => {
    const mockEntitlements: Entitlement[] = Array.from(
      { length: 25 },
      (_, i) =>
        ({
          productId: `product-${i}`,
          companyId: `company-${i}`,
          productName: `Product ${i}`,
          units: 4,
        }) as Entitlement,
    );

    it('should chunk entitlements into arrays of specified size', () => {
      const chunkSize = 10;
      const result = chunkEntitlements(mockEntitlements, chunkSize);

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveLength(10);
      expect(result[1]).toHaveLength(10);
      expect(result[2]).toHaveLength(5);
    });

    it('should use default chunk size of 20', () => {
      const result = chunkEntitlements(mockEntitlements);

      expect(result).toHaveLength(2);
      expect(result[0]).toHaveLength(20);
      expect(result[1]).toHaveLength(5);
    });

    it('should throw error for invalid chunk size', () => {
      expect(() => chunkEntitlements(mockEntitlements, 0)).toThrow(
        'chunkSize must be a positive integer',
      );
      expect(() => chunkEntitlements(mockEntitlements, -1)).toThrow(
        'chunkSize must be a positive integer',
      );
    });

    it('should handle empty array', () => {
      const result = chunkEntitlements([]);

      expect(result).toHaveLength(0);
    });

    it('should handle array smaller than chunk size', () => {
      const smallArray = mockEntitlements.slice(0, 5);
      const result = chunkEntitlements(smallArray, 10);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveLength(5);
    });
  });

  describe('chunkKeywords', () => {
    const mockKeywords: { keyword: string; pageId: string }[] = Array.from(
      { length: 250 },
      (_, i) => ({
        keyword: `keyword-${i}`,
        pageId: `page-${i}`,
      }),
    );

    it('should chunk keywords into arrays of specified size', () => {
      const chunkSize = 100;
      const result = chunkKeywords(mockKeywords, chunkSize);

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveLength(100);
      expect(result[1]).toHaveLength(100);
      expect(result[2]).toHaveLength(50);
    });

    it('should use default chunk size of 100', () => {
      const result = chunkKeywords(mockKeywords);

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveLength(100);
      expect(result[1]).toHaveLength(100);
      expect(result[2]).toHaveLength(50);
    });

    it('should throw error for invalid chunk size', () => {
      expect(() => chunkKeywords(mockKeywords, 0)).toThrow(
        'chunkSize must be a positive integer',
      );
      expect(() => chunkKeywords(mockKeywords, -1)).toThrow(
        'chunkSize must be a positive integer',
      );
    });

    it('should handle empty array', () => {
      const result = chunkKeywords([]);

      expect(result).toHaveLength(0);
    });

    it('should handle array smaller than chunk size', () => {
      const smallArray = mockKeywords.slice(0, 50);
      const result = chunkKeywords(smallArray, 100);

      expect(result).toHaveLength(1);
      expect(result[0]).toHaveLength(50);
    });
  });

  describe('getUnits', () => {
    it('should return 4 for SEO workflow type regardless of entitlement units', () => {
      expect(getUnits(10, WorkflowType.SEO)).toBe(4);
      expect(getUnits(null, WorkflowType.SEO)).toBe(4);
    });

    it('should return entitlement units for non-SEO workflow type when provided', () => {
      expect(getUnits(10, WorkflowType.BLOG)).toBe(10);
    });

    it('should return default value of 4 for non-SEO workflow type when units is null', () => {
      expect(getUnits(null, WorkflowType.BLOG)).toBe(4);
    });
  });
});
