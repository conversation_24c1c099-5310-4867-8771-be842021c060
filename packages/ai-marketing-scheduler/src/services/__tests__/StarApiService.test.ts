import { StarApiError } from '../../clients/errors/StarApiError';
import { IStarApiClient } from '../../clients/interfaces/IStarApiClient';
import { ContextLogger } from '../../logger/contextLogger';
import { StarApiService } from '../StarApiService';

describe('StarApiService', () => {
  let mockStarApiClient: jest.Mocked<IStarApiClient>;
  let mockLogger: jest.Mocked<ContextLogger>;
  let starApiService: StarApiService;

  beforeEach(() => {
    mockStarApiClient = {
      updateElement: jest.fn(),
    } as any;

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      setContext: jest.fn(),
    } as any;

    starApiService = new StarApiService(mockStarApiClient, mockLogger);
  });

  describe('updateMetaTitle', () => {
    it('should successfully update meta title', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id',
        },
        context: { starApiRequestId: 'test-request-id' },
      });

      const result = await starApiService.updateMetaTitle(
        'https://example.com',
        'Old Title',
        'New Title',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });

      expect(mockStarApiClient.updateElement).toHaveBeenCalledWith(
        {
          url: 'https://example.com',
          input: {
            elementTag: 'meta',
            elementProperty: 'title',
            newContent: 'New Title',
            oldContent: 'Old Title',
          },
          debug: false,
        },
        'company-123',
        undefined,
      );
    });

    it('should handle update failure', async () => {
      const error = new StarApiError('Update failed', 500, {
        details: 'Server error',
      });
      mockStarApiClient.updateElement.mockRejectedValue(error);

      const result = await starApiService.updateMetaTitle(
        'https://example.com',
        'Old Title',
        'New Title',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'meta',
        elementProperty: 'title',
        success: false,
        error: 'Update failed',
        metadata: { details: 'Server error' },
        statusCode: 500,
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to STAR API update meta title',
        expect.objectContaining({
          companyId: 'company-123',
          url: 'https://example.com',
          error: 'Update failed',
          metadata: { details: 'Server error' },
        }),
      );
    });

    it('should treat "No changes were made" error as success', async () => {
      const error = new StarApiError('No changes were made', 400);
      mockStarApiClient.updateElement.mockRejectedValue(error);

      const result = await starApiService.updateMetaTitle(
        'https://example.com',
        'Old Title',
        'New Title',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
    });
  });

  describe('updateMetaDescription', () => {
    it('should successfully update meta description', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id',
        },
        context: { starApiRequestId: 'test-request-id' },
      });

      const result = await starApiService.updateMetaDescription(
        'https://example.com',
        'Old Description',
        'New Description',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });

      expect(mockStarApiClient.updateElement).toHaveBeenCalledWith(
        {
          url: 'https://example.com',
          input: {
            elementTag: 'meta',
            elementProperty: 'description',
            newContent: 'New Description',
            oldContent: 'Old Description',
          },
          debug: false,
        },
        'company-123',
        undefined,
      );
    });
  });

  describe('updateMainHeading', () => {
    it('should successfully update main heading', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id',
        },
        context: { starApiRequestId: 'test-request-id' },
      });

      const result = await starApiService.updateMainHeading(
        'https://example.com',
        'h1',
        2,
        'Old Heading',
        'New Heading',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'h1',
        success: true,
      });

      expect(mockStarApiClient.updateElement).toHaveBeenCalledWith(
        {
          url: 'https://example.com',
          input: {
            elementTag: 'h1',
            elementIndex: 2,
            newContent: 'New Heading',
            oldContent: 'Old Heading',
          },
          debug: false,
        },
        'company-123',
        undefined,
      );
    });

    it('should handle main heading update failure', async () => {
      const error = new Error('Network error');
      mockStarApiClient.updateElement.mockRejectedValue(error);

      const result = await starApiService.updateMainHeading(
        'https://example.com',
        'h1',
        1,
        'Old Heading',
        'New Heading',
      );

      expect(result).toEqual({
        elementType: 'h1',
        success: false,
        error: 'Network error',
        statusCode: undefined,
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to STAR API update h1',
        expect.objectContaining({
          elementIndex: 1,
          error: 'Network error',
        }),
      );
    });

    it('should successfully update h2 heading', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id',
        },
        context: { starApiRequestId: 'test-request-id' },
      });

      const result = await starApiService.updateMainHeading(
        'https://example.com',
        'h2',
        0,
        'Old H2 Heading',
        'New H2 Heading',
        'company-123',
      );

      expect(result).toEqual({
        elementType: 'h2',
        success: true,
      });

      expect(mockStarApiClient.updateElement).toHaveBeenCalledWith(
        {
          url: 'https://example.com',
          input: {
            elementTag: 'h2',
            elementIndex: 0,
            newContent: 'New H2 Heading',
            oldContent: 'Old H2 Heading',
          },
          debug: false,
        },
        'company-123',
        undefined,
      );
    });

    it('should handle h2 heading update failure', async () => {
      const error = new Error('Element not found');
      mockStarApiClient.updateElement.mockRejectedValue(error);

      const result = await starApiService.updateMainHeading(
        'https://example.com',
        'h2',
        3,
        'Old H2 Content',
        'New H2 Content',
      );

      expect(result).toEqual({
        elementType: 'h2',
        success: false,
        error: 'Element not found',
        statusCode: undefined,
      });

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to STAR API update h2',
        expect.objectContaining({
          elementIndex: 3,
          error: 'Element not found',
        }),
      );
    });

    it('should handle different element types with same function', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id',
        },
        context: { starApiRequestId: 'test-request-id' },
      });

      // Test h1
      const h1Result = await starApiService.updateMainHeading(
        'https://example.com',
        'h1',
        1,
        'Old H1',
        'New H1',
      );

      expect(h1Result).toEqual({
        elementType: 'h1',
        success: true,
      });

      // Test h2
      const h2Result = await starApiService.updateMainHeading(
        'https://example.com',
        'h2',
        2,
        'Old H2',
        'New H2',
      );

      expect(h2Result).toEqual({
        elementType: 'h2',
        success: true,
      });

      // Verify both calls were made with correct parameters
      expect(mockStarApiClient.updateElement).toHaveBeenCalledTimes(2);
      expect(mockStarApiClient.updateElement).toHaveBeenNthCalledWith(
        1,
        {
          url: 'https://example.com',
          input: {
            elementTag: 'h1',
            elementIndex: 1,
            newContent: 'New H1',
            oldContent: 'Old H1',
          },
          debug: false,
        },
        undefined,
        undefined,
      );
      expect(mockStarApiClient.updateElement).toHaveBeenNthCalledWith(
        2,
        {
          url: 'https://example.com',
          input: {
            elementTag: 'h2',
            elementIndex: 2,
            newContent: 'New H2',
            oldContent: 'Old H2',
          },
          debug: false,
        },
        undefined,
        undefined,
      );
    });
  });

  describe('requestId context', () => {
    it('should set requestId context when available', async () => {
      mockStarApiClient.updateElement.mockResolvedValue({
        data: {
          message: 'Element updated successfully',
          requestId: 'test-request-id-123',
        },
        context: { starApiRequestId: 'test-request-id-123' },
      });

      await starApiService.updateMetaTitle(
        'https://example.com',
        'Old Title',
        'New Title',
      );

      expect(mockLogger.setContext).toHaveBeenCalledWith({
        starApiRequestId: 'test-request-id-123',
      });
    });
  });

  describe('case insensitive "no changes" handling', () => {
    it('should treat "no changes were made" error as success (case insensitive)', async () => {
      const error = new Error('NO CHANGES WERE MADE');
      mockStarApiClient.updateElement.mockRejectedValue(error);

      const result = await starApiService.updateMetaDescription(
        'https://example.com',
        'Old Description',
        'New Description',
      );

      expect(result).toEqual({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
    });
  });
});
