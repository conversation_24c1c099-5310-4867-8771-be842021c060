import { TenantClient } from '../../clients';
import { ContextLogger } from '../../logger/contextLogger';
import {
  createMockContextLogger,
  createMockTenantClient,
  createMockEntitlementDTOs,
} from '../../utils/testUtils';
import { TenantService } from '../TenantService';

// Mock the dependencies
jest.mock('../../clients/TenantClient');
jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
}));

describe('TenantService', () => {
  let tenantService: TenantService;
  let mockLogger: jest.Mocked<ContextLogger>;
  let mockTenantClient: jest.Mocked<TenantClient>;

  beforeEach(() => {
    // Create mocks using centralized factory
    mockLogger = createMockContextLogger();
    mockTenantClient = createMockTenantClient(mockLogger);

    // Create service instance
    tenantService = new TenantService(mockTenantClient, mockLogger);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getEntitlements', () => {
    it('should return formatted entitlements', async () => {
      // Mock data using centralized factory
      const mockEntitlements = createMockEntitlementDTOs(1);

      // Setup mock implementation
      mockTenantClient.getAllEntitlements.mockResolvedValue(mockEntitlements);

      // Execute
      const result = await tenantService.getEntitlements();

      // Assert
      expect(mockTenantClient.getAllEntitlements).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(result[0].units).toBe(1);
      expect(result[0].websiteUrl).toBe('test1.com');
      expect(result[0].displayName).toBe('Test Company 1');
    });

    it('should handle empty entitlements', async () => {
      // Setup mock implementation
      mockTenantClient.getAllEntitlements.mockResolvedValue([]);

      // Execute
      const result = await tenantService.getEntitlements();

      // Assert
      expect(result).toEqual([]);
      expect(mockLogger.info).toHaveBeenCalled();
    });
  });

  describe('validateEntitlementsByDates', () => {
    it('should filter out expired entitlements', () => {
      const currentDate = new Date();
      const pastDate = new Date(currentDate);
      pastDate.setFullYear(pastDate.getFullYear() - 1);
      const futureDate = new Date(currentDate);
      futureDate.setFullYear(futureDate.getFullYear() + 1);

      // Create test entitlements with specific date scenarios
      const entitlements = createMockEntitlementDTOs(3);
      // Override dates for test scenarios
      entitlements[0].company = { website: 'valid.com', name: 'Valid Company' };
      entitlements[1] = {
        ...entitlements[1],
        startDate: pastDate.toISOString(),
        endDate: pastDate.toISOString(), // Expired
        company: { website: 'expired.com', name: 'Expired Company' },
      };
      entitlements[2] = {
        ...entitlements[2],
        startDate: futureDate.toISOString(),
        endDate: futureDate.toISOString(), // Future
        company: { website: 'future.com', name: 'Future Company' },
      };

      const result = tenantService.validateEntitlementsByDates(entitlements);

      expect(result).toHaveLength(1);
      expect(result[0].company?.website).toBe('valid.com');
    });

    it('should handle entitlements with no end date', () => {
      const currentDate = new Date();
      const pastDate = new Date(currentDate);
      pastDate.setFullYear(pastDate.getFullYear() - 1);

      const entitlements = createMockEntitlementDTOs(1);
      // Override for no end date scenario
      entitlements[0] = {
        ...entitlements[0],
        startDate: pastDate.toISOString(),
        endDate: null,
        company: { website: 'no-end.com', name: 'No End Company' },
      };

      const result = tenantService.validateEntitlementsByDates(entitlements);

      expect(result).toHaveLength(1);
      expect(result[0].company?.website).toBe('no-end.com');
    });
  });
});
