import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import { ActionsByCompany } from '../clients/types/actions';
import {
  RecommendationsRaw,
  StoreSEODraftResponse,
} from '../clients/types/seoApiGateway';
import {
  CheckActionError,
  DraftActionConsumerError,
  GetActionError,
  ScheduledActionNotFoundError,
  ScheduledActionUpdateError,
  SeoApiGatewayClientError,
} from '../errors';
import { FindActionsToSurfaceError } from '../errors/FindActionsToSurfaceError';
import { ContextLogger } from '../logger/contextLogger';
import {
  CheckActionResult,
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../types';
import { IScheduledActionService } from './interfaces/IScheduledActionService';
import { StoreSEODraftError } from '../errors/StoreSEODraftError';

/**
 * Interface for the result of updating a scheduled action status
 */
export interface UpdateScheduledActionResult {
  id: string;
  success: boolean;
  error?: string;
  workflowType?: WorkflowType;
}

/**
 * Service for handling scheduled action operations
 */
export class ScheduledActionService implements IScheduledActionService {
  constructor(
    private readonly seoApiGatewayClient: ISeoApiGatewayClient,
    private readonly logger: ContextLogger,
  ) {}

  /**
   * Updates a scheduled action with a failure status and handles any errors that occur during the update
   * @param actionId The ID of the action to update
   * @param failureReason The reason for the failure
   * @param workflowType Optional workflow type for the action
   * @returns A result object with information about the update
   */
  async updateWithFailure(
    actionId: string,
    failureReason: Record<string, unknown>,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult> {
    try {
      await this.update(actionId, {
        status: Status.FAILED,
        failureReason,
      });
      const stringifiedFailureReason = JSON.stringify(failureReason, null, 2);

      return {
        id: actionId,
        workflowType,
        success: false,
        error: stringifiedFailureReason,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      return {
        id: actionId,
        workflowType,
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Updates a scheduled action with a success status and handles any errors that occur during the update
   * @param actionId The ID of the action to update
   * @param status The status to update to
   * @param workflowType Optional workflow type for the action
   * @returns A result object with information about the update
   */
  async updateStatus(
    actionId: string,
    status: Status,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult> {
    try {
      await this.update(actionId, { status });
      return {
        id: actionId,
        workflowType,
        success: true,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      return {
        id: actionId,
        workflowType,
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Gets all scheduled actions with DRAFT_PENDING status
   * @returns A list of scheduled actions with DRAFT_PENDING status
   */
  async getDraftPendingActions(): Promise<ScheduledAction[]> {
    try {
      const actions = await this.seoApiGatewayClient.getDraftPendingActions();

      return actions;
    } catch (error) {
      // If it's already a client error, translate it to a domain error
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error(
          'API Gateway client error when fetching draft pending actions',
          {
            error: error.message,
            cause: error.cause?.message,
          },
        );

        throw new DraftActionConsumerError(
          error.message,
          error.cause,
          error.metadata,
        );
      }

      // For any other type of error
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.logger.error(
        'Unexpected error when fetching draft pending actions',
        {
          error: errorMessage,
        },
      );

      throw new DraftActionConsumerError(
        `Failed to fetch draft pending actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getSurfacedActions(
    scheduledToBePublishedAt: string,
  ): Promise<ScheduledAction[]> {
    try {
      const actions = await this.seoApiGatewayClient.getSurfacedActions(
        scheduledToBePublishedAt,
      );

      return actions;
    } catch (error) {
      // If it's already a client error, translate it to a domain error
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error(
          'API Gateway client error when fetching surfaced actions',
          {
            error: error.message,
            cause: error.cause?.message,
            scheduledToBePublishedAt,
          },
        );

        throw new DraftActionConsumerError(
          error.message,
          error.cause,
          error.metadata,
        );
      }

      // For any other type of error
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.logger.error('Unexpected error when fetching surfaced actions', {
        error: errorMessage,
        scheduledToBePublishedAt,
      });

      throw new DraftActionConsumerError(
        `Failed to fetch surfaced actions: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }

  async getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]> {
    this.logger.info('Fetching scheduled actions', { companyId, workflowType });
    try {
      const data = await this.seoApiGatewayClient.getUpcomingActions(
        companyId,
        workflowType,
      );

      this.logger.info('Retrieved upcoming actions', {
        companyId,
        workflowType,
        total: data.length,
      });

      return data;
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when fetching upcoming actions', {
          companyId,
          workflowType,
          error: error.message,
          cause: error.cause?.message,
        });
        throw new DraftActionConsumerError(
          error.message,
          error.cause,
          error.metadata,
        );
      }
      throw error;
    }
  }

  async getAction(actionId: string): Promise<ScheduledAction | null> {
    this.logger.info('Fetching scheduled action', { actionId });
    try {
      const action = await this.seoApiGatewayClient.getAction(actionId);
      return action;
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when fetching scheduled action', {
          actionId,
          error: error.message,
          cause: error.cause?.message,
        });
        throw new GetActionError(error.message, error.cause, error.metadata);
      }
      throw error;
    }
  }

  async checkAction(
    actionId: string,
    fieldsToCheck: string[],
  ): Promise<CheckActionResult> {
    this.logger.info('Checking action', { actionId, fieldsToCheck });
    try {
      const action = await this.seoApiGatewayClient.getAction(actionId);
      if (!action) {
        throw new ScheduledActionNotFoundError(actionId);
      }

      const generationPayload = action.generationPayload || {};

      const skipStep = fieldsToCheck.every(
        field =>
          // Check if the field exists and is not undefined and not null; we accept 0 false, and empty string as results.
          field in generationPayload &&
          generationPayload[field] !== undefined &&
          generationPayload[field] !== null,
      );
      return { action, skipStep };
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when checking action', {
          actionId,
          fieldsToCheck,
          error: error.message,
          cause: error.cause?.message,
        });
        throw new CheckActionError(error.message, error.cause, error.metadata);
      }
      throw error;
    }
  }

  async createScheduledActions(
    companyId: string,
    workflowType: WorkflowType,
    count: number,
    generationPayload?: object,
  ): Promise<void> {
    this.logger.info('Creating owed draft actions', {
      companyId,
      workflowType,
      count,
    });

    try {
      // Create all actions in parallel for better performance
      const promises = Array.from({ length: count }, () =>
        this.seoApiGatewayClient.createOwedActions(
          companyId,
          workflowType,
          generationPayload,
        ),
      );

      await Promise.all(promises);

      this.logger.info('Created owed draft actions', {
        companyId,
        workflowType,
        count,
      });
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when creating owed draft actions', {
          companyId,
          workflowType,
          error: error.message,
          cause: error.cause?.message,
        });
        throw new DraftActionConsumerError(
          error.message,
          error.cause,
          error.metadata,
        );
      }
      throw error;
    }
  }

  async update(
    actionId: string,
    delta: Partial<ScheduledAction>,
  ): Promise<ScheduledAction> {
    try {
      const result = await this.seoApiGatewayClient.updateScheduledAction(
        actionId,
        delta,
      );

      return result;
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        // Check if this is a "not found" error based on the error message
        const isNotFoundError =
          error.message.toLowerCase().includes('not found') ||
          error.message.toLowerCase().includes('does not exist') ||
          error.message.toLowerCase().includes('invalid id');

        if (isNotFoundError) {
          this.logger.error('Scheduled action not found', {
            actionId,
            delta,
            error: error.message,
          });
          throw new ScheduledActionNotFoundError(actionId, error);
        }

        this.logger.error(
          'API Gateway client error when updating scheduled action',
          {
            actionId,
            delta,
            error: error.message,
            cause: error.cause?.message,
          },
        );
        throw new ScheduledActionUpdateError(actionId, delta.status!, error);
      }
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Unexpected error when updating scheduled action', {
        actionId,
        delta,
        error: errorMessage,
      });
      throw new ScheduledActionUpdateError(actionId, delta.status!, error);
    }
  }

  async findActionsToSurface(): Promise<ActionsByCompany[]> {
    try {
      const allActionsByCompany: ActionsByCompany[] = [];

      await this.seoApiGatewayClient.processActionsToSurfaceInBatches(
        async batchActionsByCompany => {
          await Promise.resolve(
            allActionsByCompany.push(...batchActionsByCompany),
          );
        },
      );

      return allActionsByCompany;
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when fetching actions to surface', {
          error: error.message,
          cause: error.cause?.message,
        });
        throw new FindActionsToSurfaceError(
          error.message,
          error.cause,
          error.metadata,
        );
      }
      throw error;
    }
  }

  async processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void> {
    try {
      return await this.seoApiGatewayClient.processActionsToSurfaceInBatches(
        callback,
      );
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error(
          'Error when processing actions to surface in batches',
          {
            error: error.message,
            cause: error.cause?.message,
          },
        );
        throw new FindActionsToSurfaceError(
          error.message,
          error.cause,
          error.metadata,
        );
      }
      throw error;
    }
  }

  async storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult: ScraperResult,
    keywords: string,
    recommendations: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse> {
    try {
      return await this.seoApiGatewayClient.storeSEODraft(
        companyId,
        actionId,
        scraperResult,
        keywords,
        recommendations,
      );
    } catch (error) {
      if (error instanceof SeoApiGatewayClientError) {
        this.logger.error('Error when storing SEO draft', {
          companyId,
          actionId,
          error: error.message,
          cause: error.cause?.message,
        });
        throw new StoreSEODraftError(
          error.message,
          error.cause,
          error.metadata,
        );
      }
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error('Unexpected error when storing SEO draft', {
        companyId,
        actionId,
        error: errorMessage,
      });
      throw new StoreSEODraftError(
        `Failed to store SEO draft: ${errorMessage}`,
        error instanceof Error ? error : undefined,
      );
    }
  }
}
