import { ITenantClient } from '../clients/interfaces/ITenantClient';
import { EntitlementDTOFormatter } from '../formatters/EntitlementDTOFormatter';
import { ContextLogger } from '../logger/contextLogger';
import { Entitlement, EntitlementDTO, UserDTO } from '../types';
import { ITenantService } from './interfaces/ITenantService';

/**
 * Service for handling tenant operations
 */
export class TenantService implements ITenantService {
  constructor(
    private readonly tenantClient: ITenantClient,
    private readonly logger: ContextLogger,
  ) {}

  async getEntitlements(): Promise<Entitlement[]> {
    const entitlements = await this.tenantClient.getAllEntitlements();
    const validEntitlements = this.validateEntitlementsByDates(entitlements);
    return EntitlementDTOFormatter.formatEntitlementsFromFetch(
      validEntitlements,
    );
  }

  validateEntitlementsByDates(
    entitlements: EntitlementDTO[],
  ): EntitlementDTO[] {
    const filteredEntitlements = entitlements.reduce(
      (
        acc: { valid: EntitlementDTO[]; invalid: EntitlementDTO[] },
        entitlement,
      ) => {
        const { startDate, endDate } = entitlement;
        const currentDate = new Date().getTime();
        if (
          (!startDate || new Date(startDate).getTime() < currentDate) &&
          (!endDate || new Date(endDate).getTime() > currentDate)
        ) {
          acc.valid.push(entitlement);
        } else {
          acc.invalid.push(entitlement);
        }
        return acc;
      },
      { valid: [], invalid: [] },
    );
    if (filteredEntitlements.invalid.length > 0) {
      this.logger.info(
        'Invalid entitlements with expired dates or not active yet.',
        {
          invalidEntitlements: filteredEntitlements.invalid.length,
          invalidEntitlementCompanyIds: filteredEntitlements.invalid.map(
            e => e.companyId,
          ),
        },
      );
    }
    this.logger.info('Valid entitlements', {
      validEntitlements: filteredEntitlements.valid.length,
      validEntitlementCompanyIds: filteredEntitlements.valid.map(
        e => e.companyId,
      ),
    });
    return filteredEntitlements.valid;
  }

  async getCompanyAdmins(companyId: string): Promise<UserDTO[]> {
    return this.tenantClient.getAllCompanyAdmins(companyId);
  }
}
