import { ChatPromptTemplate, PromptTemplate } from '@langchain/core/prompts';
import { ChatOpenAI } from '@langchain/openai';
import { Langfuse, TextPromptClient, ChatPromptClient } from 'langfuse';
import { CallbackHandler } from 'langfuse-langchain';
import { z } from 'zod';

import { getConfig } from '../config';
import { ContextLogger } from '../logger/contextLogger';
import { PromptResult } from '../types';

export class PromptService {
  private readonly langfuse: Langfuse;
  private readonly langfuseLangchainHandler: CallbackHandler;
  private readonly model: ChatOpenAI;

  constructor(
    private readonly logger: ContextLogger,
    private readonly config = getConfig(),
  ) {
    this.langfuse = new Langfuse({
      secretKey: this.config.prompt.langfuse.secretKey,
      publicKey: this.config.prompt.langfuse.publicKey,
      baseUrl: this.config.prompt.langfuse.baseUrl,
    });

    this.langfuseLangchainHandler = new CallbackHandler({
      secretKey: this.config.prompt.langfuse.secretKey,
      publicKey: this.config.prompt.langfuse.publicKey,
      baseUrl: this.config.prompt.langfuse.baseUrl,
      environment: this.config.prompt.langfuse.environment,
      flushAt: this.config.prompt.langfuse.flushAt,
    });

    // Initialize the default model
    this.model = new ChatOpenAI({
      modelName: this.config.prompt.openai.modelName,
      temperature: this.config.prompt.openai.temperature,
      apiKey: this.config.prompt.openai.apiKey,
    });
  }

  /**
   * Sanitize prompt content by replacing {{ with {
   */
  private sanitizePromptContent(content: string): string {
    this.logger.debug('Sanitizing prompt content');

    // Step 1: Replace single braces that are not followed by another brace with !{ or !}
    const step1Content = content.replace(/(?<!{){|}(?!})/g, match => {
      return match === '{' ? '!{' : '!}';
    });

    // Step 2: Convert {{var}} to {var}
    const step2Content = step1Content.replace(/{{(.*?)}}/g, '{$1}');

    // Step 3: Convert !{ and !} back to {{ and }}
    const finalContent = step2Content.replace(/!{/g, '{{').replace(/!}/g, '}}');

    return finalContent;
  }

  /**
   * Convert LangFuse chat prompt into LangChain ChatPromptTemplate
   */
  private convertLangfuseChatPromptToTemplate(
    messages: ChatPromptClient['prompt'],
  ): ChatPromptTemplate {
    this.logger.debug('Converting LangFuse prompt to LangChain template', {
      messages: JSON.stringify(messages),
    });
    return ChatPromptTemplate.fromMessages(
      messages.map(msg => {
        const role =
          msg.role === 'user'
            ? 'human'
            : msg.role === 'assistant'
              ? 'ai'
              : 'system';
        const content = this.sanitizePromptContent(msg.content);
        return [role, content];
      }),
    );
  }

  private convertLangfuseTextPromptToTemplate(
    prompt: TextPromptClient,
  ): PromptTemplate {
    this.logger.debug('Converting LangFuse text prompt to LangChain template');
    return PromptTemplate.fromTemplate(prompt.getLangchainPrompt());
  }

  /**
   * Execute a chat prompt from LangFuse
   */
  public async executeChatPrompt<T = any>(
    promptName: string,
    inputs: Record<string, unknown>,
    structuredOutputSchema?: z.ZodType<T>,
    metadata?: Record<string, unknown>,
  ): Promise<PromptResult<T | string>> {
    this.logger.info(`Executing chat prompt: ${promptName}`);
    try {
      const prompt = await this.langfuse.getPrompt(promptName);
      if (!this.isChatPrompt(prompt)) {
        throw new Error(
          `Expected chat prompt but got text prompt: ${promptName}`,
        );
      }
      const promptTemplate = this.convertLangfuseChatPromptToTemplate(
        prompt.prompt as unknown as ChatPromptClient['prompt'],
      );

      // Create a model with structured output if schema is provided
      let model;
      if (structuredOutputSchema) {
        model = new ChatOpenAI({
          modelName: this.config.prompt.openai.modelName,
          temperature: this.config.prompt.openai.temperature,
          apiKey: this.config.prompt.openai.apiKey,
        }).withStructuredOutput(structuredOutputSchema);
      } else {
        model = this.model;
      }

      const chain = promptTemplate.pipe(model);

      const result = await chain.invoke(inputs, {
        callbacks: [this.langfuseLangchainHandler],
        runName: promptName,
        ...(metadata && { metadata }),
      });

      // Return a PromptResult with content as the structured output if schema provided
      return {
        content: structuredOutputSchema
          ? (result as T)
          : (result.content as string),
        prompt: JSON.stringify(promptTemplate),
        prompt_id: (prompt as any).id,
        prompt_name: (prompt as any).name,
      };
    } catch (error) {
      this.logger.error(`Failed to execute chat prompt: ${error.message}`);
      throw error as Error;
    }
  }

  /**
   * Execute a text prompt from LangFuse
   */
  public async executeTextPrompt(
    promptName: string,
    inputs: Record<string, unknown>,
    metadata?: Record<string, unknown>,
  ): Promise<PromptResult> {
    this.logger.info(`Executing text prompt: ${promptName}`);
    try {
      const prompt = await this.langfuse.getPrompt(promptName);
      if (!this.isTextPrompt(prompt)) {
        throw new Error(
          `Expected text prompt but got chat prompt: ${promptName}`,
        );
      }

      const promptTemplate = this.convertLangfuseTextPromptToTemplate(prompt);
      const chain = promptTemplate.pipe(this.model);

      const result = await chain.invoke(inputs, {
        callbacks: [this.langfuseLangchainHandler],
        runName: promptName,
        ...(metadata && { metadata }),
      });

      return {
        content: result.content as string,
        prompt: JSON.stringify(promptTemplate),
        prompt_id: (prompt as any).id,
        prompt_name: (prompt as any).name,
      };
    } catch (error) {
      this.logger.error(`Failed to execute text prompt: ${error.message}`);
      throw error;
    }
  }

  private isTextPrompt(prompt: TextPromptClient): boolean {
    return !!(prompt && prompt.type === 'text');
  }

  private isChatPrompt(prompt: TextPromptClient): boolean {
    return !!(prompt && prompt.type === 'chat');
  }
}
