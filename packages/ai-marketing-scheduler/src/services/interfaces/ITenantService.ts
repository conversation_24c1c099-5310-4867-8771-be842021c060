import { Entitlement, UserDTO } from '../../types';

/**
 * Interface for the Tenant Service
 */
export interface ITenantService {
  /**
   * Gets all entitlements for tenants
   * @returns A list of entitlements
   */
  getEntitlements(): Promise<Entitlement[]>;

  /**
   * Gets all admins for a company
   * @param companyId - The ID of the company
   * @returns A list of admins
   */
  getCompanyAdmins(companyId: string): Promise<UserDTO[]>;
}
