import { SFNClient } from '@aws-sdk/client-sfn';

import { IScheduledActionService } from './IScheduledActionService';
import { IStarApiService } from './IStarApiService';
import { ITenantService } from './ITenantService';
import { IApiGatewayClient } from '../../clients/interfaces/IApiGatewayClient';
import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import { IStarApiClient } from '../../clients/interfaces/IStarApiClient';
import { ContextLogger } from '../../logger/contextLogger';
import { PromptService } from '../PromptService';

/**
 * Interface for the Service Factory
 * Abstracts the creation of services and clients
 */
export interface IServiceFactory {
  /**
   * Creates a service for scheduled action operations
   * @param logger Logger instance
   * @returns A scheduled action service implementation
   */
  createScheduledActionService(logger: ContextLogger): IScheduledActionService;

  /**
   * Creates a service for tenant operations
   * @param logger Logger instance
   * @returns A tenant service implementation
   */
  createTenantService(logger: ContextLogger): ITenantService;

  /**
   * Creates an SFN client based on configuration
   * @param logger Logger instance
   * @returns An SFN client implementation
   */
  createSfnClient(logger: ContextLogger): SFNClient;

  /**
   * Creates an API Gateway client based on configuration
   * @param logger Logger instance
   * @returns An API Gateway client implementation
   */
  createSeoApiGatewayClient(logger: ContextLogger): ISeoApiGatewayClient;

  /**
   * Creates a generic API Gateway client (not SEO-specific)
   * @param logger Logger instance
   * @returns An API Gateway client implementation
   */
  createApiGatewayClient(logger: ContextLogger): IApiGatewayClient;

  /**
   * Creates a prompt service
   * @param logger Logger instance
   * @returns A prompt service implementation
   */
  createPromptService(logger: ContextLogger): PromptService;

  /**
   * Creates a STAR API client
   * @param logger Logger instance
   * @returns A STAR API client implementation
   */
  createStarApiClient(logger: ContextLogger): IStarApiClient;

  /**
   * Creates a STAR API service
   * @param logger Logger instance
   * @returns A STAR API service implementation
   */
  createStarApiService(logger: ContextLogger): IStarApiService;
}
