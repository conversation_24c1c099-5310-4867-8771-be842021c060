import { UpdateResult } from '../../utils/publisherUtils';

/**
 * Interface for STAR API service operations
 */
export interface IStarApiService {
  /**
   * Updates the meta title of a webpage
   */
  updateMetaTitle(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult>;

  /**
   * Updates the meta description of a webpage
   */
  updateMetaDescription(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult>;

  /**
   * Updates the main heading on a webpage
   */
  updateMainHeading(
    url: string,
    elementType: string,
    elementIndex: number,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
    pageSectionId?: string,
  ): Promise<UpdateResult>;
}
