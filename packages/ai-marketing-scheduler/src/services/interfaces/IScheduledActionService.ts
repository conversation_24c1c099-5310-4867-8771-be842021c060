import { ActionsByCompany } from '../../clients/types/actions';
import {
  RecommendationsRaw,
  StoreSEODraftResponse,
} from '../../clients/types/seoApiGateway';
import {
  CheckActionResult,
  ScheduledAction,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import { UpdateScheduledActionResult } from '../ScheduledActionService';

/**
 * Interface for the Scheduled Action Service
 */
export interface IScheduledActionService {
  /**
   * Gets all scheduled actions with DRAFT_PENDING status
   * @returns A list of scheduled actions with DRAFT_PENDING status
   */
  getDraftPendingActions(): Promise<ScheduledAction[]>;

  /**
   * Gets all scheduled actions with SURFACED status ready to be published
   * @param scheduledToBePublishedAt The date to filter actions by (YYYY-MM-DD format)
   * @returns A list of scheduled actions with SURFACED status ready for publishing
   */
  getSurfacedActions(
    scheduledToBePublishedAt: string,
  ): Promise<ScheduledAction[]>;

  /**
   * Gets upcoming actions for a company and workflow type
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @returns A list of upcoming scheduled actions
   */
  getUpcomingActions(
    companyId: string,
    workflowType: WorkflowType,
  ): Promise<ScheduledAction[]>;

  /**
   * Gets record of action and checks if it has fieldsToCheck set in its generationPayload
   * @param actionId The ID of the action
   * @param fieldsToCheck The fields to check (if all set then skipStep is true)
   * @returns A checkActionResult
   */
  checkAction(
    actionId: string,
    fieldsToCheck: string[],
  ): Promise<CheckActionResult>;

  /**
   * Gets a scheduled action by ID
   * @param actionId The ID of the action
   * @returns The scheduled action or null if not found
   */
  getAction(actionId: string): Promise<ScheduledAction | null>;

  /**
   * Creates scheduled actions for a company
   * @param companyId The ID of the company
   * @param workflowType The type of workflow
   * @param count The number of actions to create
   * @param generationPayload Optional generation payload for the action
   */
  createScheduledActions(
    companyId: string,
    workflowType: WorkflowType,
    count: number,
    generationPayload?: object,
  ): Promise<void>;

  /**
   * Updates a scheduled action with a failure status and handles any errors that occur during the update
   * @param actionId The ID of the action to update
   * @param failureReason The reason for the failure
   * @param workflowType Optional workflow type for the action
   * @returns A result object with information about the update
   */
  updateWithFailure(
    actionId: string,
    failureReason: Record<string, unknown>,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult>;

  /**
   * Updates a scheduled action's status
   * @param actionId The ID of the action to update
   * @param status The new status to set
   * @param workflowType Optional workflow type for the action
   * @returns A result object with information about the update
   */
  updateStatus(
    actionId: string,
    status: Status,
    workflowType?: WorkflowType,
  ): Promise<UpdateScheduledActionResult>;

  /**
   * Updates a scheduled action
   * @param actionId The ID of the action to update
   * @param delta The delta to apply to the action
   * @returns The updated scheduled action
   */
  update(
    actionId: string,
    delta: Partial<
      Omit<ScheduledAction, 'id' | 'companyId' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<ScheduledAction>;

  /**
   * Stores a SEO draft
   * @param companyId The ID of the company
   * @param actionId The ID of the action
   * @param scraperResult The scraper result to store
   * @param keywords The keywords to store
   * @param recommendations The recommendations to store
   * @returns The stored entities from the generation payload
   */
  storeSEODraft(
    companyId: string,
    actionId: string,
    scraperResult: ScraperResult,
    keywords: string,
    recommendations: RecommendationsRaw,
  ): Promise<StoreSEODraftResponse>;

  /**
   * Finds all actions to surface
   * @returns A list of actions to surface
   */
  findActionsToSurface(): Promise<ActionsByCompany[]>;

  /**
   * Process actions to surface in batches to avoid memory issues
   * @param callback Function to process each batch of actions
   * @returns Promise that resolves when all batches are processed
   */
  processActionsToSurfaceInBatches(
    callback: (actionsByCompany: ActionsByCompany[]) => Promise<void>,
  ): Promise<void>;
}
