import { ContextLogger } from 'src/factories/LoggerFactory';
import { Entitlement, WorkflowType } from 'src/types';

import { getConfig } from '../config';

// Map of product IDs to their corresponding workflow types
const PRODUCT_WORKFLOW_MAP: Record<string, WorkflowType> = {
  'a2b3c4d5-f6e7-8a9b-0c1d-2e3f4a5b6c7d': WorkflowType.SEO, // automated-seo
  '5c9b746c-7327-42ce-b998-ce707e7cee44': WorkflowType.BLOG, // automated-blogs
  // Add more mappings as needed
};

export const getWorkflowType = (
  entitlement: Entitlement,
  logger: ContextLogger,
): WorkflowType => {
  const config = getConfig();

  // Check if the product ID exists in our mapping
  if (entitlement.productId in PRODUCT_WORKFLOW_MAP) {
    return PRODUCT_WORKFLOW_MAP[entitlement.productId];
  }

  // If the product ID is in the configured product IDs but not in our mapping,
  // we can add logging to help identify missing mappings
  if (config.tenantApi.productIds.includes(entitlement.productId)) {
    // Product ID is configured but has no workflow type mapping
    logger.warn('Product ID is configured but has no workflow type mapping', {
      productId: entitlement.productId,
      productName: entitlement.productName,
    });
  }

  // Default to SEO workflow type for unknown products
  return WorkflowType.SEO;
};

export const getUnits = (
  entitlementUnits: number | null,
  workflowType: WorkflowType,
): number => {
  if (workflowType === WorkflowType.SEO) {
    return 4;
  }

  return entitlementUnits || 4;
};

/**
 * Chunks an array of entitlements into smaller arrays of specified size
 */
export const chunkEntitlements = (
  entitlements: Entitlement[],
  chunkSize = 20,
): Entitlement[][] => {
  if (chunkSize <= 0) {
    throw new Error('chunkSize must be a positive integer');
  }
  return entitlements.reduce((acc, entitlement, index) => {
    const chunkIndex = Math.floor(index / chunkSize);
    if (!acc[chunkIndex]) {
      acc[chunkIndex] = [];
    }
    acc[chunkIndex].push(entitlement);
    return acc;
  }, [] as Entitlement[][]);
};

/**
 * Chunks an array of keywords into smaller arrays of specified size
 */
export const chunkKeywords = (
  keywords: { keyword: string; pageId: string }[],
  chunkSize = 100,
): { keyword: string; pageId: string }[][] => {
  if (chunkSize <= 0) {
    throw new Error('chunkSize must be a positive integer');
  }
  return keywords.reduce(
    (acc, keyword, index) => {
      const chunkIndex = Math.floor(index / chunkSize);
      if (!acc[chunkIndex]) {
        acc[chunkIndex] = [];
      }
      acc[chunkIndex].push(keyword);
      return acc;
    },
    [] as { keyword: string; pageId: string }[][],
  );
};
