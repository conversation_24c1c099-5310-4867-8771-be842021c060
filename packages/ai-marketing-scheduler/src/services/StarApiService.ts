import { StarApiError } from '../clients/errors/StarApiError';
import { IStarApiClient } from '../clients/interfaces/IStarApiClient';
import { ContextLogger } from '../logger/contextLogger';
import { UpdateResult } from '../utils/publisherUtils';
import { IStarApiService } from './interfaces/IStarApiService';

/**
 * Service for handling STAR API operations
 */
export class StarApiService implements IStarApiService {
  constructor(
    private readonly starApiClient: IStarApiClient,
    private readonly logger: ContextLogger,
  ) {}

  /**
   * Updates the meta title of a webpage
   */
  async updateMetaTitle(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    return this.updateElement(
      url,
      oldContent,
      newContent,
      {
        elementType: 'meta',
        elementProperty: 'title',
        logDescription: 'meta title',
      },
      companyId,
      actionId,
    );
  }

  /**
   * Updates the meta description of a webpage
   */
  async updateMetaDescription(
    url: string,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    return this.updateElement(
      url,
      oldContent,
      newContent,
      {
        elementType: 'meta',
        elementProperty: 'description',
        logDescription: 'meta description',
      },
      companyId,
      actionId,
    );
  }

  /**
   * Updates the main heading on a webpage
   */
  async updateMainHeading(
    url: string,
    elementType: string,
    elementIndex: number,
    oldContent: string,
    newContent: string,
    companyId?: string,
    actionId?: string,
    pageSectionId?: string,
  ): Promise<UpdateResult> {
    return this.updateElement(
      url,
      oldContent,
      newContent,
      {
        elementType: elementType as 'h1' | 'h2',
        elementIndex,
        pageSectionId,
        logDescription: elementType,
      },
      companyId,
      actionId,
    );
  }

  /**
   * Generic method to update website elements via STAR API
   */
  private async updateElement(
    url: string,
    oldContent: string,
    newContent: string,
    config: {
      elementType: 'meta' | 'h1' | 'h2';
      elementProperty?: string;
      elementIndex?: number;
      pageSectionId?: string;
      logDescription: string;
    },
    companyId?: string,
    actionId?: string,
  ): Promise<UpdateResult> {
    const {
      elementType,
      elementProperty,
      elementIndex,
      pageSectionId,
      logDescription,
    } = config;

    try {
      // Build input object dynamically based on element type
      const starApiInput: any = {
        elementTag: elementType,
        newContent,
        oldContent,
      };

      // Add property for meta elements
      if (elementProperty) {
        starApiInput.elementProperty = elementProperty;
      }

      // Add index for indexed elements like H1
      if (elementIndex !== undefined) {
        starApiInput.elementIndex = elementIndex;
      }

      // Add pageSectionId for elements that have it
      if (pageSectionId !== undefined) {
        starApiInput.pageSectionId = pageSectionId;
      }

      const response = await this.starApiClient.updateElement(
        {
          url,
          input: starApiInput,
          debug: false,
        },
        companyId,
        actionId,
      );

      // Add any context from the client response
      this.logger.setContext(response.context);

      this.logger.info(`Successfully STAR API updated ${logDescription}`, {
        companyId,
        url,
        ...(elementIndex !== undefined && { elementIndex }),
        responseMessage: response.data.message,
      });

      return {
        elementType,
        ...(elementProperty && { elementProperty }),
        success: true,
      };
    } catch (error) {
      const starApiErrorMessage =
        error instanceof Error ? error.message : String(error);
      const starApiErrorMetadata =
        error instanceof StarApiError ? error.metadata : undefined;

      // Check if the error message contains "No changes were made" (case insensitive)
      if (starApiErrorMessage.toLowerCase().includes('no changes were made')) {
        this.logger.info(
          `No changes needed for ${logDescription} - treating as success`,
          {
            ...(elementIndex !== undefined && { elementIndex }),
            responseMessage: starApiErrorMessage,
          },
        );

        return {
          elementType,
          ...(elementProperty && { elementProperty }),
          success: true,
        };
      }

      this.logger.error(`Failed to STAR API update ${logDescription}`, {
        companyId,
        url,
        ...(elementIndex !== undefined && { elementIndex }),
        error: starApiErrorMessage,
        metadata: starApiErrorMetadata,
      });

      return {
        elementType,
        ...(elementProperty && { elementProperty }),
        success: false,
        error: starApiErrorMessage,
        ...(starApiErrorMetadata && { metadata: starApiErrorMetadata }),
        statusCode:
          error instanceof StarApiError ? error.statusCode : undefined,
      };
    }
  }
}
