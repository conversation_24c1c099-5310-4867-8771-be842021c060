import { LDClient, LDContext, init } from '@launchdarkly/node-server-sdk';

import { getConfig } from '../config';
import { createLogger } from '../factories/LoggerFactory';

export class LaunchDarkly {
  private static instance: LaunchDarkly | null = null;
  private client: LDClient | null = null;
  private readonly logger = createLogger('LaunchDarkly');
  private readonly config: { key: string; superUserCompanyId: string };

  constructor() {
    const envConfig = getConfig();
    this.config = {
      key: envConfig.launchDarklyKey || '',
      superUserCompanyId: envConfig.apiGateway.superUser || '',
    };
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): LaunchDarkly {
    if (!LaunchDarkly.instance) {
      LaunchDarkly.instance = new LaunchDarkly();
    }
    return LaunchDarkly.instance;
  }

  /**
   * Check feature flag for a single company
   */
  public async checkVariation(
    flagName: string,
    defaultValue = false,
    companyId?: string,
  ): Promise<boolean> {
    const targetCompanyId = companyId || this.config.superUserCompanyId;

    try {
      const client = await this.getClient();
      const context: LDContext = {
        kind: 'company',
        name: 'company',
        key: targetCompanyId,
        companyId: targetCompanyId,
      };

      const value = await client.variation(flagName, context, defaultValue);
      return Boolean(value);
    } catch (error) {
      this.logger.error('LaunchDarkly check failed', {
        flagName,
        companyId: targetCompanyId,
        error: error instanceof Error ? error.message : String(error),
      });
      return defaultValue;
    }
  }

  /**
   * Check feature flag for multiple companies
   */
  public async checkVariationBatch(
    flagName: string,
    defaultValue = false,
    companyIds: string[],
  ): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    try {
      const promises = companyIds.map(async companyId => {
        const value = await this.checkVariation(
          flagName,
          defaultValue,
          companyId,
        );
        return { companyId, value };
      });

      const batchResults = await Promise.allSettled(promises);

      batchResults.forEach((settledResult, index) => {
        if (settledResult.status === 'fulfilled') {
          const { companyId, value } = settledResult.value;
          results.set(companyId, value);
        } else {
          // Use default value for failed checks - map back to the correct company ID using the index
          const companyId = companyIds[index];
          results.set(companyId, defaultValue);
        }
      });

      return results;
    } catch (error) {
      this.logger.error('LaunchDarkly batch check failed', {
        flagName,
        companyCount: companyIds.length,
        error: error instanceof Error ? error.message : String(error),
      });

      // Return default values for all companies on failure
      companyIds.forEach(companyId => {
        results.set(companyId, defaultValue);
      });

      return results;
    }
  }

  /**
   * Get LaunchDarkly client with initialization
   */
  private async getClient(): Promise<LDClient> {
    if (!this.config.key) {
      throw new Error('LaunchDarkly key is not configured');
    }

    if (!this.client) {
      this.client = init(this.config.key);
    }

    try {
      await this.client.waitForInitialization({ timeout: 30 });
    } catch (error) {
      this.logger.error('LaunchDarkly client initialization failed', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }

    return this.client;
  }

  /**
   * Close the LaunchDarkly client connection
   */
  public closeConnection(): void {
    if (this.client) {
      try {
        this.client.close();
      } catch (error) {
        this.logger.error('Failed to close LaunchDarkly client', {
          error: error instanceof Error ? error.message : String(error),
        });
      } finally {
        this.client = null;
      }
    }
  }
}
