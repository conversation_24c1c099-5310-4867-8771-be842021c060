import {
  createMultipleAdmins,
  createMultipleEntitlements,
  createTestEmailContent,
  createTestEntitlement,
} from './testFactories';
import { HomepageFeed } from '../clients/types/seoApiGateway';
import { Entitlement } from '../types';

export interface TestSetupOptions {
  adminCount?: number;
  emailContent?: HomepageFeed;
  customEmails?: string[];
  entitlements?: Entitlement[];
  companyCount?: number;
  featureFlagEnabled?: boolean;
}

export const setupSuccessfulCompanyTest = (
  mockTenantService: any,
  mockSeoApiGatewayClient: any,
  ldInstance: any,
  options: TestSetupOptions = {},
) => {
  const {
    adminCount = 1,
    emailContent,
    customEmails,
    entitlements = [createTestEntitlement()],
    companyCount,
    featureFlagEnabled = true,
  } = options;

  // Create entitlements if companyCount is specified
  const finalEntitlements = companyCount
    ? createMultipleEntitlements(companyCount)
    : entitlements;

  mockTenantService.getEntitlements.mockResolvedValue(finalEntitlements);

  // Setup feature flags
  if (featureFlagEnabled) {
    const companyIds = finalEntitlements.map(e => e.companyId);
    const flagResults = new Map(companyIds.map(id => [id, true]));
    ldInstance.checkVariationBatch.mockResolvedValue(flagResults);
  } else {
    const companyIds = finalEntitlements.map(e => e.companyId);
    const flagResults = new Map(companyIds.map(id => [id, false]));
    ldInstance.checkVariationBatch.mockResolvedValue(flagResults);
  }

  if (customEmails) {
    // Don't mock getCompanyAdmins for custom emails
  } else {
    mockTenantService.getCompanyAdmins.mockResolvedValue(
      createMultipleAdmins(adminCount),
    );
  }

  mockSeoApiGatewayClient.getEmailContent.mockResolvedValue(
    emailContent || createTestEmailContent(),
  );
  mockSeoApiGatewayClient.sendEmail.mockResolvedValue(undefined);
};

export const setupCompanyDataError = (mockTenantService: any, error: Error) => {
  mockTenantService.getCompanyAdmins.mockRejectedValue(error);
};

export const setupEmailContentError = (
  mockSeoApiGatewayClient: any,
  error: Error,
) => {
  mockSeoApiGatewayClient.getEmailContent.mockRejectedValue(error);
};

export const setupEmailSendingError = (
  mockSeoApiGatewayClient: any,
  error: Error,
) => {
  mockSeoApiGatewayClient.sendEmail.mockRejectedValue(error);
};

export const setupMixedSuccessScenario = (
  mockTenantService: any,
  mockSeoApiGatewayClient: any,
  ldInstance: any,
  companyCount: number,
) => {
  const entitlements = createMultipleEntitlements(companyCount);

  mockTenantService.getEntitlements.mockResolvedValue(entitlements);
  ldInstance.checkVariationBatch.mockResolvedValue(
    new Map(entitlements.map(e => [e.companyId, true])),
  );

  // First company fails (no admins), second succeeds, rest fail
  mockTenantService.getCompanyAdmins
    .mockResolvedValueOnce([]) // c1 fails
    .mockResolvedValueOnce(createMultipleAdmins(1)) // c2 succeeds
    .mockResolvedValue([]); // c3+ fail

  mockSeoApiGatewayClient.getEmailContent
    .mockResolvedValueOnce({ items: [] }) // c1 fails
    .mockResolvedValueOnce(createTestEmailContent()) // c2 succeeds
    .mockResolvedValue({ items: [] }); // c3+ fail

  mockSeoApiGatewayClient.sendEmail.mockResolvedValue();
};

export const setupLaunchDarklyError = (ldInstance: any, error: Error) => {
  ldInstance.checkVariationBatch.mockRejectedValue(error);
  ldInstance.closeConnection.mockImplementation(() => {});
};
