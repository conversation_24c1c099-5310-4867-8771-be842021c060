import { ScrapedPageType } from '../types';

export const pageTypeRules = [
  [ScrapedPageType.HOMEPAGE, /^\/$/],
  [ScrapedPageType.BLOG, /^\/blog\//i],
  [ScrapedPageType.NEIGHBORHOOD_GUIDE, /^\/neighborhoods?\//i],
  [ScrapedPageType.AGENT_BIO, /^\/agents?\//i],
  [ScrapedPageType.HOME_VALUATION, /^\/home-valuation/i],
  [ScrapedPageType.MORTGAGE_CALCULATOR, /^\/mortgage-calculator/i],
  [ScrapedPageType.BUYERS_GUIDE, /^\/buyers(-guide)?/i],
  [ScrapedPageType.SELLERS_GUIDE, /^\/sellers(-guide)?/i],
] as const;

export const getPageType = function (url: string): ScrapedPageType | undefined {
  let pathname = url;
  try {
    pathname = new URL(url).pathname;
  } catch {
    // assume url is already a pathname
  }
  for (const [type, regex] of pageTypeRules) {
    if (regex.test(pathname)) {
      return type;
    }
  }
  return undefined;
};

export const extractDomainFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    return urlObj.host;
  } catch {
    // If URL parsing fails, try to extract domain from the string
    const match = url.match(/^(?:https?:\/\/)?([^/]+)/);
    return match ? match[1] : url;
  }
};

export const normalizeUrlForComparison = (url: string): string => {
  if (!url || typeof url !== 'string') {
    return url;
  }
  return url.replace(/^http:\/\//, 'https://');
};
