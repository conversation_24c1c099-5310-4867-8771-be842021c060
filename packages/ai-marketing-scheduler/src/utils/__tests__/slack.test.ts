import { Logger } from '@aws-lambda-powertools/logger';
import { StarApiPayload } from 'src/clients/interfaces/IStarApiClient';
import { getEnvironmentConfig } from 'src/config/implementation';

import {
  notifyStarApiFailure,
  sendSlackMessage,
  setLogger,
  clearNotificationCache,
} from '../slack';

// Mock the fetch function
global.fetch = jest.fn();

// Mock the configuration
jest.mock('src/config/implementation', () => ({
  getEnvironmentConfig: jest.fn().mockReturnValue({
    slack: {
      webhookUrl: 'https://hooks.slack.com/webhook/url',
      enabled: true,
    },
  }),
}));

describe('Slack utilities', () => {
  let mockLogger: jest.Mocked<Logger>;
  let mockGetEnvironmentConfig: jest.MockedFunction<
    typeof getEnvironmentConfig
  >;

  beforeEach(() => {
    jest.clearAllMocks();
    clearNotificationCache(); // Clear deduplication cache between tests
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    } as any;
    setLogger(mockLogger);

    // Reset the mock configuration
    mockGetEnvironmentConfig = getEnvironmentConfig as jest.MockedFunction<
      typeof getEnvironmentConfig
    >;
    mockGetEnvironmentConfig.mockReturnValue({
      slack: {
        webhookUrl: 'https://hooks.slack.com/webhook/url',
        enabled: true,
      },
    } as any);
  });

  describe('sendSlackMessage', () => {
    it('should not send messages when notifications are disabled', async () => {
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      await sendSlackMessage('Test message');

      expect(global.fetch).not.toHaveBeenCalled();
      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slack notification blocked',
        expect.objectContaining({
          reason: 'notifications_disabled',
          text: 'Test message',
          slackEnabled: false,
          slackWebhookUrl: 'configured',
          nodeEnv: 'test',
          environment: undefined,
        }),
      );
    });

    it('should send messages when notifications are enabled', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
      });

      await sendSlackMessage('Test error');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://hooks.slack.com/webhook/url',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: 'Test error' }),
        },
      );
      expect(mockLogger.info).toHaveBeenCalledWith('Sent Slack notification');
    });

    it('should handle Slack API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      await sendSlackMessage('Test error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send Slack notification',
        { error: 'Slack API returned 500' },
      );
    });

    it('should handle network errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network error'),
      );

      await sendSlackMessage('Production error');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to send Slack notification',
        { error: 'Network error' },
      );
    });
  });

  describe('notifyStarApiFailure', () => {
    const mockPayload: StarApiPayload = {
      url: 'https://test-company.luxurypresence.com/page',
      input: {
        elementTag: 'meta',
        elementProperty: 'description',
        oldContent: 'Old description',
        newContent: 'New description',
      },
      debug: false,
    };

    it('should format the message correctly with payload', async () => {
      // Set up mock to disable notifications so we can test the structure
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      const error = new Error('Test error');
      error.stack =
        'Error: Test error\n    at line1\n    at line2\n    at line3\n    at line4';

      const mockResponseBody = {
        error: 'Validation failed',
        details: 'Invalid content',
        requestId: 'req-123',
      };

      await notifyStarApiFailure({
        error,
        payload: mockPayload,
        url: 'https://star-api.luxurypresence.com/api/v1/star/editor',
        method: 'PATCH',
        status: 400,
        requestId: 'req-123',
        responseBody: mockResponseBody,
      });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slack notification blocked',
        expect.objectContaining({
          reason: 'notifications_disabled',
          text: expect.stringContaining('STAR API failure'),
          slackEnabled: false,
          slackWebhookUrl: 'configured',
          nodeEnv: 'test',
          environment: undefined,
        }),
      );

      const loggedData = (mockLogger.warn as jest.Mock).mock.calls[0][1];
      const blocks = loggedData.blocks;

      // Check header block
      expect(blocks[0]).toEqual({
        type: 'header',
        text: {
          type: 'plain_text',
          text: '🚨 STAR API Failure',
          emoji: true,
        },
      });

      // Check error message
      expect(blocks[1].text.text).toBe('*Error:* Test error');

      // Check fields in the main section
      const mainFields = blocks[2].fields;
      expect(mainFields[0].text).toBe('*CompanyId:*\nUnknown');
      expect(mainFields[1].text).toContain('*Admin Account URL:*');
      expect(mainFields[1].text).toContain('Unknown');
      expect(mainFields[2].text).toBe(
        '*API Endpoint:*\n`PATCH https://star-api.luxurypresence.com/api/v1/star/editor`',
      );
      expect(mainFields[3].text).toBe('*Status:*\n400');
      expect(mainFields[4].text).toBe('*Request ID:*\nreq-123');

      // Check page update details
      const pageFields = blocks.find(
        (b: any) => b.fields && b.fields[0]?.text?.includes('*Page URL:*'),
      )?.fields;
      expect(pageFields[0].text).toBe(
        '*Page URL:*\nhttps://test-company.luxurypresence.com/page',
      );
      expect(pageFields[1].text).toContain('*Element:*');
      expect(pageFields[1].text).toContain('meta (description)');

      // Check content comparison - old and new content are in separate blocks
      const oldContentBlock = blocks.find((b: any) =>
        b.text?.text?.includes('*Old Content:*'),
      );
      expect(oldContentBlock.text.text).toBe(
        '*Old Content:*\n> Old description',
      );

      const newContentBlock = blocks.find((b: any) =>
        b.text?.text?.includes('*New Content:*'),
      );
      expect(newContentBlock.text.text).toBe(
        '*New Content:*\n> New description',
      );

      // Check stack trace
      const stackBlock = blocks.find((b: any) =>
        b.text?.text?.includes('*Stack Trace:*'),
      );
      expect(stackBlock.text.text).toContain('```');
      expect(stackBlock.text.text).toContain('Error: Test error');

      // Check response body
      const responseBlock = blocks.find((b: any) =>
        b.text?.text?.includes('*Response Body:*'),
      );
      expect(responseBlock.text.text).toContain('```');
      expect(responseBlock.text.text).toContain('"error": "Validation failed"');
      expect(responseBlock.text.text).toContain('"details": "Invalid content"');
    });

    it('should format the message correctly without payload', async () => {
      // Set up mock to disable notifications so we can test the structure
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      const error = new Error('Network error');

      await notifyStarApiFailure({
        error,
        url: 'https://star-api.luxurypresence.com/api/v1/star/editor',
        method: 'GET',
      });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slack notification blocked',
        expect.objectContaining({
          reason: 'notifications_disabled',
          text: expect.stringContaining('STAR API failure'),
          slackEnabled: false,
          slackWebhookUrl: 'configured',
          nodeEnv: 'test',
          environment: undefined,
        }),
      );

      const loggedData = (mockLogger.warn as jest.Mock).mock.calls[0][1];
      const blocks = loggedData.blocks;

      // Check error message
      expect(blocks[1].text.text).toBe('*Error:* Network error');

      // Check fields
      const mainFields = blocks[2].fields;
      expect(mainFields[0].text).toBe('*CompanyId:*\nUnknown');
      expect(mainFields[1].text).toContain('*Admin Account URL:*');
      expect(mainFields[2].text).toBe(
        '*API Endpoint:*\n`GET https://star-api.luxurypresence.com/api/v1/star/editor`',
      );
      expect(mainFields[3].text).toBe('*Status:*\nN/A');
      expect(mainFields[4].text).toBe('*Request ID:*\nN/A');

      // Should not have page details
      const pageDetailsBlock = blocks.find(
        (b: any) => b.text?.text === '*Page Update Details*',
      );
      expect(pageDetailsBlock).toBeUndefined();
    });

    it('should extract company ID from various URL formats', async () => {
      // Set up mock to disable notifications so we can test the structure
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      const testCases = [
        {
          url: 'https://company-123.luxurypresence.com/page',
          expectedCompanyId: 'company-123',
        },
        {
          url: 'https://test.luxurypresence.com/page',
          expectedCompanyId: 'test',
        },
        {
          url: 'https://nonluxury.com/page',
          expectedCompanyId: 'unknown',
        },
        {
          url: 'invalid-url',
          expectedCompanyId: 'unknown',
        },
      ];

      for (const testCase of testCases) {
        await notifyStarApiFailure({
          error: new Error('Test'),
          payload: { ...mockPayload, url: testCase.url },
          url: 'https://star-api.luxurypresence.com',
          method: 'PATCH',
        });

        expect(mockLogger.warn).toHaveBeenCalledWith(
          'Slack notification blocked',
          expect.objectContaining({
            reason: 'notifications_disabled',
            text: expect.stringContaining('STAR API failure'),
            slackEnabled: false,
            slackWebhookUrl: 'configured',
            nodeEnv: 'test',
            environment: undefined,
          }),
        );

        const loggedData = (mockLogger.warn as jest.Mock).mock.calls[
          (mockLogger.warn as jest.Mock).mock.calls.length - 1
        ][1];
        const blocks = loggedData.blocks;
        const mainFields = blocks[2].fields;
        expect(mainFields[0].text).toBe('*CompanyId:*\nUnknown');
        expect(mainFields[1].text).toContain('*Admin Account URL:*');
        expect(mainFields[1].text).toContain('undefined');
      }
    });

    it('should use provided companyId instead of extracting from URL', async () => {
      // Set up mock to disable notifications so we can test the structure
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      const error = new Error('Test error');

      await notifyStarApiFailure({
        error,
        payload: mockPayload,
        url: 'https://star-api.luxurypresence.com/api/v1/star/editor',
        method: 'PATCH',
        status: 400,
        requestId: 'req-123',
        companyId: 'provided-company-id',
      });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slack notification blocked',
        expect.objectContaining({
          reason: 'notifications_disabled',
          text: expect.stringContaining('STAR API failure'),
          slackEnabled: false,
          slackWebhookUrl: 'configured',
          nodeEnv: 'test',
          environment: undefined,
        }),
      );

      const loggedData = (mockLogger.warn as jest.Mock).mock.calls[0][1];
      const blocks = loggedData.blocks;
      const mainFields = blocks[2].fields;
      expect(mainFields[0].text).toBe('*CompanyId:*\nprovided-company-id');
      expect(mainFields[1].text).toBe(
        '*Admin Account URL:*\n<https://app.luxurypresence.com/admin/accounts/provided-company-id|provided-company-id>',
      );
    });

    it('should format element index when present', async () => {
      // Set up mock to disable notifications so we can test the structure
      mockGetEnvironmentConfig.mockReturnValue({
        slack: {
          webhookUrl: 'https://hooks.slack.com/webhook/url',
          enabled: false,
        },
      } as any);

      const h1Payload: StarApiPayload = {
        url: 'https://test-company.luxurypresence.com/page',
        input: {
          elementTag: 'h1',
          elementIndex: 2,
          oldContent: 'Old Heading',
          newContent: 'New Heading',
        },
        debug: false,
      };

      await notifyStarApiFailure({
        error: new Error('H1 update failed'),
        payload: h1Payload,
        url: 'https://star-api.luxurypresence.com/api/v1/star/editor',
        method: 'PATCH',
      });

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slack notification blocked',
        expect.objectContaining({
          reason: 'notifications_disabled',
          text: expect.stringContaining('STAR API failure'),
          slackEnabled: false,
          slackWebhookUrl: 'configured',
          nodeEnv: 'test',
          environment: undefined,
        }),
      );

      const loggedData = (mockLogger.warn as jest.Mock).mock.calls[0][1];
      const blocks = loggedData.blocks;
      const pageFields = blocks.find(
        (b: any) => b.fields && b.fields[0]?.text?.includes('*Page URL:*'),
      )?.fields;
      expect(pageFields[1].text).toContain('h1 [index: 2]');
    });
  });
});
