import { LDClient } from '@launchdarkly/node-server-sdk';

import { LaunchDarkly } from '../launchdarkly';
import { createMockConfig, setupTestMocks } from '../testUtils';

// Create a comprehensive mock client
const createMockLDClient = () => ({
  variation: jest.fn(),
  waitForInitialization: jest.fn(),
  close: jest.fn(),
  flush: jest.fn(),
  track: jest.fn(),
  identify: jest.fn(),
  allFlags: jest.fn(),
  secureModeHash: jest.fn(),
  // Add any additional methods that might be called by the new SDK
  on: jest.fn(),
  off: jest.fn(),
  once: jest.fn(),
});

// Mock the LaunchDarkly SDK with more comprehensive coverage
jest.mock('@launchdarkly/node-server-sdk', () => {
  const mockClient = createMockLDClient();

  // Ensure waitForInitialization returns a resolved promise by default
  mockClient.waitForInitialization.mockResolvedValue(mockClient);

  return {
    init: jest.fn(() => mockClient),
    LDClient: jest.fn().mockImplementation(() => mockClient),
    // Export the mock client for test access
    __mockClient: mockClient,
  };
});

// Mock the config module
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

// Mock the logger factory
jest.mock('../../factories/LoggerFactory', () => ({
  createLogger: jest.fn(),
}));

// Use fake timers to prevent open handles
beforeAll(() => {
  jest.useFakeTimers();
  // Suppress console errors from LaunchDarkly SDK during tests
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

// Global cleanup to prevent open handles
afterAll(() => {
  jest.useRealTimers();
  jest.clearAllTimers();
  jest.restoreAllMocks();
});

describe('LaunchDarkly', () => {
  let mockClient: jest.Mocked<LDClient>;
  let mockInit: jest.MockedFunction<
    typeof import('@launchdarkly/node-server-sdk').init
  >;
  let mockGetConfig: jest.Mock;
  let mockCreateLogger: jest.Mock;
  let mockLogger: any;

  beforeEach(() => {
    // Reset the singleton instance before each test
    (LaunchDarkly as any).instance = null;

    // Get the mock client from the mocked module
    const mockModule = jest.requireMock('@launchdarkly/node-server-sdk');
    mockClient = mockModule.__mockClient;

    // Reset all mock functions and set default behaviors
    Object.values(mockClient).forEach((fn: any) => {
      if (typeof fn === 'function' && fn.mockClear) {
        fn.mockClear();
        // Set default resolved behavior for waitForInitialization
        if (fn === mockClient.waitForInitialization) {
          fn.mockResolvedValue(mockClient);
        }
      }
    });

    mockInit = mockModule.init;
    mockInit.mockReturnValue(mockClient);

    mockGetConfig = jest.requireMock('../../config').getConfig;
    mockGetConfig.mockReturnValue(createMockConfig());

    const mocks = setupTestMocks('LaunchDarkly');
    mockLogger = mocks.mockLogger;
    mockCreateLogger = jest.requireMock(
      '../../factories/LoggerFactory',
    ).createLogger;
    mockCreateLogger.mockReturnValue(mockLogger);
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllTimers();
    mockLogger.restore();

    // Reset singleton instance
    (LaunchDarkly as any).instance = null;
  });

  describe('getInstance', () => {
    it('should create a singleton instance', () => {
      const instance1 = LaunchDarkly.getInstance();
      const instance2 = LaunchDarkly.getInstance();

      expect(instance1).toBe(instance2);
      expect(instance1).toBeInstanceOf(LaunchDarkly);
    });

    it('should initialize with config values', () => {
      const mockConfig = createMockConfig();
      mockConfig.launchDarklyKey = 'test-key';
      mockConfig.apiGateway.superUser = 'test-super-user';
      mockGetConfig.mockReturnValue(mockConfig);

      const instance = LaunchDarkly.getInstance();

      expect(mockGetConfig).toHaveBeenCalled();
      expect(instance).toBeInstanceOf(LaunchDarkly);
    });
  });

  describe('checkVariation', () => {
    let instance: LaunchDarkly;

    beforeEach(() => {
      instance = LaunchDarkly.getInstance();
      mockClient.waitForInitialization.mockResolvedValue(mockClient);
    });

    it('should check feature flag for a specific company', async () => {
      const flagName = 'test-flag';
      const companyId = 'test-company';
      const expectedValue = true;

      mockClient.variation.mockResolvedValue(expectedValue);

      const result = await instance.checkVariation(flagName, false, companyId);

      expect(mockClient.variation).toHaveBeenCalledWith(
        flagName,
        {
          kind: 'company',
          name: 'company',
          key: companyId,
          companyId: companyId,
        },
        false,
      );
      expect(result).toBe(expectedValue);
    });

    it('should use superUser companyId when no companyId is provided', async () => {
      const flagName = 'test-flag';
      const expectedValue = false;

      mockClient.variation.mockResolvedValue(expectedValue);

      const result = await instance.checkVariation(flagName, false);

      expect(mockClient.variation).toHaveBeenCalledWith(
        flagName,
        {
          kind: 'company',
          name: 'company',
          key: 'test-super-user',
          companyId: 'test-super-user',
        },
        false,
      );
      expect(result).toBe(expectedValue);
    });

    it('should return default value when LaunchDarkly check fails', async () => {
      const flagName = 'test-flag';
      const defaultValue = true;
      const error = new Error('LaunchDarkly error');

      mockClient.variation.mockRejectedValue(error);

      const result = await instance.checkVariation(flagName, defaultValue);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly check failed',
        {
          flagName,
          companyId: 'test-super-user',
          error: 'LaunchDarkly error',
        },
      );
      expect(result).toBe(defaultValue);
    });

    it('should handle non-Error exceptions', async () => {
      const flagName = 'test-flag';
      const defaultValue = false;

      mockClient.variation.mockRejectedValue('String error');

      const result = await instance.checkVariation(flagName, defaultValue);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly check failed',
        {
          flagName,
          companyId: 'test-super-user',
          error: 'String error',
        },
      );
      expect(result).toBe(defaultValue);
    });
  });

  describe('checkVariationBatch', () => {
    let instance: LaunchDarkly;

    beforeEach(() => {
      instance = LaunchDarkly.getInstance();
      mockClient.waitForInitialization.mockResolvedValue(mockClient);
    });

    it('should check feature flags for multiple companies', async () => {
      const flagName = 'test-flag';
      const companyIds = ['company1', 'company2', 'company3'];
      const expectedResults = new Map([
        ['company1', true],
        ['company2', false],
        ['company3', true],
      ]);

      // Mock different responses for each company
      mockClient.variation
        .mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false)
        .mockResolvedValueOnce(true);

      const result = await instance.checkVariationBatch(
        flagName,
        false,
        companyIds,
      );

      expect(mockClient.variation).toHaveBeenCalledTimes(3);
      expect(result).toEqual(expectedResults);
    });

    it('should handle partial failures in batch operations', async () => {
      const flagName = 'test-flag';
      const companyIds = ['company1', 'company2', 'company3'];
      const defaultValue = false;

      // Mock one success and two failures
      mockClient.variation
        .mockResolvedValueOnce(true)
        .mockRejectedValueOnce(new Error('Error 1'))
        .mockRejectedValueOnce(new Error('Error 2'));

      const result = await instance.checkVariationBatch(
        flagName,
        defaultValue,
        companyIds,
      );

      expect(result.get('company1')).toBe(true);
      expect(result.get('company2')).toBe(defaultValue);
      expect(result.get('company3')).toBe(defaultValue);
      expect(result.size).toBe(3);
    });

    it('should return default values for all companies when batch operation fails', async () => {
      const flagName = 'test-flag';
      const companyIds = ['company1', 'company2'];
      const defaultValue = true;

      // Mock all operations to fail
      mockClient.variation
        .mockRejectedValueOnce(new Error('Error 1'))
        .mockRejectedValueOnce(new Error('Error 2'));

      const result = await instance.checkVariationBatch(
        flagName,
        defaultValue,
        companyIds,
      );

      expect(result.get('company1')).toBe(defaultValue);
      expect(result.get('company2')).toBe(defaultValue);
      expect(result.size).toBe(2);
    });

    it('should log error when batch operation fails', async () => {
      const flagName = 'test-flag';
      const companyIds = ['company1', 'company2'];
      const defaultValue = false;

      mockClient.variation.mockRejectedValue(new Error('Batch error'));

      await instance.checkVariationBatch(flagName, defaultValue, companyIds);

      // The actual implementation logs individual errors for each company
      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly check failed',
        {
          flagName,
          companyId: 'company1',
          error: 'Batch error',
        },
      );
      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly check failed',
        {
          flagName,
          companyId: 'company2',
          error: 'Batch error',
        },
      );
    });
  });

  describe('getClient', () => {
    let instance: LaunchDarkly;

    beforeEach(() => {
      instance = LaunchDarkly.getInstance();
    });

    it('should initialize client with correct key', async () => {
      const mockConfig = createMockConfig();
      mockConfig.launchDarklyKey = 'test-launchdarkly-key';
      mockGetConfig.mockReturnValue(mockConfig);

      // Reset instance to use new config
      (LaunchDarkly as any).instance = null;
      instance = LaunchDarkly.getInstance();

      mockClient.waitForInitialization.mockResolvedValue(mockClient);

      await (instance as any).getClient();

      expect(mockInit).toHaveBeenCalledWith('test-launchdarkly-key');
    });

    it('should throw error when LaunchDarkly key is not configured', async () => {
      const mockConfig = createMockConfig();
      mockConfig.launchDarklyKey = '';
      mockGetConfig.mockReturnValue(mockConfig);

      // Reset instance to use new config
      (LaunchDarkly as any).instance = null;
      instance = LaunchDarkly.getInstance();

      await expect((instance as any).getClient()).rejects.toThrow(
        'LaunchDarkly key is not configured',
      );
    });

    it('should wait for client initialization', async () => {
      mockClient.waitForInitialization.mockResolvedValue(mockClient);

      await (instance as any).getClient();

      expect(mockClient.waitForInitialization).toHaveBeenCalled();
    });

    it('should handle initialization timeout', async () => {
      // Mock waitForInitialization to reject with a timeout error
      const timeoutError = new Error('Initialization timeout');
      mockClient.waitForInitialization.mockRejectedValue(timeoutError);

      await expect((instance as any).getClient()).rejects.toThrow(
        'Initialization timeout',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly client initialization failed',
        {
          error: 'Initialization timeout',
        },
      );
    });

    it('should handle initialization errors', async () => {
      const error = new Error('Initialization failed');
      mockClient.waitForInitialization.mockRejectedValue(error);

      await expect((instance as any).getClient()).rejects.toThrow(
        'Initialization failed',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'LaunchDarkly client initialization failed',
        {
          error: 'Initialization failed',
        },
      );
    });

    it('should reuse existing client instance', async () => {
      mockClient.waitForInitialization.mockResolvedValue(mockClient);

      // First call should initialize
      await (instance as any).getClient();
      expect(mockInit).toHaveBeenCalledTimes(1);

      // Second call should reuse existing client
      await (instance as any).getClient();
      expect(mockInit).toHaveBeenCalledTimes(1);
    });
  });

  describe('closeConnection', () => {
    let instance: LaunchDarkly;

    beforeEach(() => {
      instance = LaunchDarkly.getInstance();
      mockClient.waitForInitialization.mockResolvedValue(mockClient);
    });

    it('should close client connection successfully', async () => {
      // Initialize client first
      await (instance as any).getClient();

      instance.closeConnection();

      expect(mockClient.close).toHaveBeenCalled();
    });

    it('should handle close errors gracefully', async () => {
      // Initialize client first
      mockClient.waitForInitialization.mockResolvedValue(mockClient);
      await (instance as any).getClient();

      const error = new Error('Close failed');
      mockClient.close.mockImplementation(() => {
        throw error;
      });

      instance.closeConnection();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to close LaunchDarkly client',
        {
          error: 'Close failed',
        },
      );
    });

    it('should set client to null after closing', async () => {
      // Initialize client first
      await (instance as any).getClient();

      instance.closeConnection();

      expect((instance as any).client).toBeNull();
    });

    it('should not throw when client is null', () => {
      expect(() => instance.closeConnection()).not.toThrow();
    });
  });
});
