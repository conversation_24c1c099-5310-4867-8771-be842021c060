import JWT from 'jsonwebtoken';

import { getGWAuthToken } from '../getGWAuthToken';
import { createMockConfig } from '../testUtils';

jest.mock('../../config', () => ({
  getConfig: jest.fn(() => ({
    apiGateway: {
      superUser: 'config-super-user',
      apiGatewayKey: 'config-api-key',
    },
  })),
}));

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
}));

describe('getGWAuthToken', () => {
  const mockJWTSign = JWT.sign as jest.MockedFunction<typeof JWT.sign>;
  let mockGetConfig: jest.Mock;

  beforeEach(() => {
    mockJWTSign.mockReturnValue('mock-jwt-token' as any);
    mockGetConfig = jest.requireMock('../../config').getConfig as jest.Mock;
    mockGetConfig.mockReturnValue(createMockConfig());
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should generate token with provided parameters', () => {
    const superUser = 'test-user';
    const apiGatewayKey = 'test-key';

    const result = getGWAuthToken(superUser, apiGatewayKey);

    expect(mockJWTSign).toHaveBeenCalledWith({ id: superUser }, apiGatewayKey);
    expect(result).toBe('mock-jwt-token');
  });

  it('should fallback to config when parameters are not provided', () => {
    const result = getGWAuthToken();

    expect(mockJWTSign).toHaveBeenCalledWith(
      { id: 'test-super-user' },
      'test-key',
    );
    expect(result).toBe('mock-jwt-token');
  });

  it('should use provided superUser and fallback apiGatewayKey from config', () => {
    const superUser = 'custom-user';

    const result = getGWAuthToken(superUser);

    expect(mockJWTSign).toHaveBeenCalledWith({ id: superUser }, 'test-key');
    expect(result).toBe('mock-jwt-token');
  });

  it('should use provided apiGatewayKey and fallback superUser from config', () => {
    const apiGatewayKey = 'custom-key';

    const result = getGWAuthToken(undefined, apiGatewayKey);

    expect(mockJWTSign).toHaveBeenCalledWith(
      { id: 'test-super-user' },
      apiGatewayKey,
    );
    expect(result).toBe('mock-jwt-token');
  });

  it('should throw error when apiGatewayKey is not available', () => {
    // Mock config to return undefined apiGatewayKey
    const mockConfigWithoutKey = createMockConfig();
    mockConfigWithoutKey.apiGateway.apiGatewayKey = '';
    mockGetConfig.mockReturnValue(mockConfigWithoutKey);

    expect(() => {
      getGWAuthToken();
    }).toThrow('API_GATEWAY_KEY is not set');
  });

  it('should throw error when provided apiGatewayKey is empty', () => {
    // Mock config to also return empty key so the function actually fails
    const mockConfigWithEmptyKey = createMockConfig();
    mockConfigWithEmptyKey.apiGateway.apiGatewayKey = '';
    mockGetConfig.mockReturnValue(mockConfigWithEmptyKey);

    expect(() => {
      getGWAuthToken('test-user', '');
    }).toThrow('API_GATEWAY_KEY is not set');
  });
});
