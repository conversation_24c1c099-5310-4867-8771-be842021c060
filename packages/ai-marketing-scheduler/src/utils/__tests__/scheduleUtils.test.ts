import { ContextLogger } from '../../logger/contextLogger';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { WorkflowType } from '../../types';
import { calculateSchedulingDates } from '../scheduleUtils';
import {
  createMockContextLogger,
  createMockScheduledActionService,
  createMockScheduledActions,
} from '../testUtils';

describe('scheduleUtils', () => {
  let mockLogger: jest.Mocked<ContextLogger>;
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;

  beforeEach(() => {
    mockLogger = createMockContextLogger();
    mockScheduledActionService = createMockScheduledActionService();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('calculateSchedulingDates', () => {
    const companyId = 'test-company-id';
    const workflowType = WorkflowType.SEO;

    it('should calculate dates when no upcoming actions exist', async () => {
      mockScheduledActionService.getUpcomingActions.mockResolvedValue([]);

      const now = new Date('2023-06-15T10:00:00Z');
      jest.useFakeTimers();
      jest.setSystemTime(now);

      const result = await calculateSchedulingDates(
        companyId,
        workflowType,
        mockScheduledActionService,
        mockLogger,
      );

      expect(result.scheduledToBeSurfacedAt).toBe('2023-06-15T10:00:00.000Z');

      jest.useRealTimers();
    });

    it('should calculate dates based on latest upcoming action', async () => {
      const upcomingActions = createMockScheduledActions(2, [
        {
          companyId,
          workflowType,
          scheduledToBeSurfacedAt: '2023-06-20T10:00:00Z',
          scheduledToBePublishedAt: '2023-06-27T10:00:00Z',
          createdAt: '2023-06-13T10:00:00Z',
          updatedAt: '2023-06-13T10:00:00Z',
        },
        {
          companyId,
          workflowType,
          scheduledToBeSurfacedAt: '2023-06-25T10:00:00Z',
          scheduledToBePublishedAt: '2023-07-02T10:00:00Z',
          createdAt: '2023-06-15T10:00:00Z',
          updatedAt: '2023-06-15T10:00:00Z',
        },
      ]);

      mockScheduledActionService.getUpcomingActions.mockResolvedValue(
        upcomingActions,
      );

      const result = await calculateSchedulingDates(
        companyId,
        workflowType,
        mockScheduledActionService,
        mockLogger,
      );

      // Should use the latest surfaced date (2023-06-25) as base and add 7 days
      expect(result.scheduledToBeSurfacedAt).toBe('2023-07-02T10:00:00.000Z');
    });

    it('should fallback to createdAt when scheduledToBeSurfacedAt is not available', async () => {
      const upcomingActions = createMockScheduledActions(1, [
        {
          companyId,
          workflowType,
          scheduledToBePublishedAt: '2023-06-27T10:00:00Z',
          createdAt: '2023-06-20T10:00:00Z',
          updatedAt: '2023-06-20T10:00:00Z',
          scheduledToBeSurfacedAt: null,
        },
      ]);

      mockScheduledActionService.getUpcomingActions.mockResolvedValue(
        upcomingActions,
      );

      const result = await calculateSchedulingDates(
        companyId,
        workflowType,
        mockScheduledActionService,
        mockLogger,
      );

      // Should use createdAt (2023-06-20) as base and add 7 days
      expect(result.scheduledToBeSurfacedAt).toBe('2023-06-27T10:00:00.000Z');
    });

    it('should handle service errors and rethrow them', async () => {
      const error = new Error('Service unavailable');
      mockScheduledActionService.getUpcomingActions.mockRejectedValue(error);

      await expect(
        calculateSchedulingDates(
          companyId,
          workflowType,
          mockScheduledActionService,
          mockLogger,
        ),
      ).rejects.toThrow('Service unavailable');
    });

    it('should find the latest date among multiple actions', async () => {
      const upcomingActions = createMockScheduledActions(3, [
        {
          companyId,
          workflowType,
          scheduledToBeSurfacedAt: '2023-06-15T10:00:00Z',
          scheduledToBePublishedAt: '2023-06-22T10:00:00Z',
          createdAt: '2023-06-10T10:00:00Z',
          updatedAt: '2023-06-10T10:00:00Z',
        },
        {
          companyId,
          workflowType,
          scheduledToBeSurfacedAt: '2023-06-30T10:00:00Z',
          scheduledToBePublishedAt: '2023-07-07T10:00:00Z',
          createdAt: '2023-06-25T10:00:00Z',
          updatedAt: '2023-06-25T10:00:00Z',
        },
        {
          companyId,
          workflowType,
          scheduledToBeSurfacedAt: '2023-06-20T10:00:00Z',
          scheduledToBePublishedAt: '2023-06-27T10:00:00Z',
          createdAt: '2023-06-18T10:00:00Z',
          updatedAt: '2023-06-18T10:00:00Z',
        },
      ]);

      mockScheduledActionService.getUpcomingActions.mockResolvedValue(
        upcomingActions,
      );

      const result = await calculateSchedulingDates(
        companyId,
        workflowType,
        mockScheduledActionService,
        mockLogger,
      );

      // Should use the latest date (2023-06-30) as base
      expect(result.scheduledToBeSurfacedAt).toBe('2023-07-07T10:00:00.000Z');
    });
  });
});
