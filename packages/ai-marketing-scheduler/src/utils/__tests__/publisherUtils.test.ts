import {
  RecommendationStatus,
  StoredRecommendation,
} from '../../clients/types/seoApiGateway';
import { RecommendationType } from '../../types';
import {
  mapUpdatesToRecommendationTypes,
  buildFailureReasonFromUpdates,
  isPendingRecommendation,
  UpdateResult,
} from '../publisherUtils';

describe('publisherUtils', () => {
  describe('mapUpdatesToRecommendationTypes', () => {
    it('should only map successful updates to recommendation types', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'title',
          success: true,
        },
        {
          elementType: 'meta',
          elementProperty: 'description',
          success: false,
          error: 'Failed to update',
        },
        {
          elementType: 'h1',
          success: true,
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(2);
      expect(result.has(RecommendationType.META_TITLE)).toBe(true);
      expect(result.has(RecommendationType.META_DESCRIPTION)).toBe(false);
      expect(result.has(RecommendationType.MAIN_HEADING)).toBe(true);
    });

    it('should return empty set when all updates failed', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'title',
          success: false,
          error: 'Network error',
        },
        {
          elementType: 'meta',
          elementProperty: 'description',
          success: false,
          error: 'Element not found',
        },
        {
          elementType: 'h1',
          success: false,
          error: 'Invalid content',
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(0);
    });

    it('should handle empty updates array', () => {
      const updates: UpdateResult[] = [];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(0);
    });

    it('should handle all successful updates', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'title',
          success: true,
        },
        {
          elementType: 'meta',
          elementProperty: 'description',
          success: true,
        },
        {
          elementType: 'h1',
          success: true,
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(3);
      expect(result.has(RecommendationType.META_TITLE)).toBe(true);
      expect(result.has(RecommendationType.META_DESCRIPTION)).toBe(true);
      expect(result.has(RecommendationType.MAIN_HEADING)).toBe(true);
    });

    it('should map h2 updates to MAIN_HEADING recommendation type', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'h2',
          success: true,
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(1);
      expect(result.has(RecommendationType.MAIN_HEADING)).toBe(true);
    });

    it('should handle both h1 and h2 updates in same array', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'h1',
          success: true,
        },
        {
          elementType: 'h2',
          success: true,
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(1); // Both map to MAIN_HEADING
      expect(result.has(RecommendationType.MAIN_HEADING)).toBe(true);
    });

    it('should handle h2 update failures', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'h2',
          success: false,
          error: 'Element not found',
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(0); // Failed updates should not be mapped
    });

    it('should handle unknown element types gracefully', () => {
      const updates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'keywords',
          success: true,
        } as UpdateResult,
        {
          elementType: 'div' as any,
          success: true,
        },
      ];

      const result = mapUpdatesToRecommendationTypes(updates);

      expect(result.size).toBe(0);
    });
  });

  describe('buildFailureReasonFromUpdates', () => {
    it('should build failure reason from failed updates', () => {
      const failedUpdates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'title',
          success: false,
          error: 'Element not found',
          metadata: { statusCode: 404 },
        },
        {
          elementType: 'h1',
          success: false,
          error: 'Network timeout',
        },
        {
          elementType: 'h2',
          success: false,
          error: 'Invalid content',
        },
      ];

      const result = buildFailureReasonFromUpdates(failedUpdates);

      expect(result).toEqual({
        meta_title: {
          error: 'Element not found',
          metadata: { statusCode: 404 },
        },
        h1: {
          error: 'Network timeout',
        },
        h2: {
          error: 'Invalid content',
        },
      });
    });

    it('should handle empty failed updates array', () => {
      const result = buildFailureReasonFromUpdates([]);

      expect(result).toEqual({});
    });

    it('should use default error message when error is undefined', () => {
      const failedUpdates: UpdateResult[] = [
        {
          elementType: 'meta',
          elementProperty: 'description',
          success: false,
        },
      ];

      const result = buildFailureReasonFromUpdates(failedUpdates);

      expect(result).toEqual({
        meta_description: {
          error: 'Unknown error',
        },
      });
    });
  });

  describe('isPendingRecommendation', () => {
    it('should return true for PENDING recommendations', () => {
      const recommendation: StoredRecommendation = {
        id: '1',
        type: RecommendationType.META_TITLE,
        status: RecommendationStatus.PENDING,
        currentValue: 'Old',
        recommendationValue: 'New',
        reasoning: 'Better',
      } as StoredRecommendation;

      expect(isPendingRecommendation(recommendation)).toBe(true);
    });

    it('should return false for APPLIED recommendations', () => {
      const recommendation: StoredRecommendation = {
        id: '1',
        type: RecommendationType.META_TITLE,
        status: RecommendationStatus.APPLIED,
        currentValue: 'Old',
        recommendationValue: 'New',
        reasoning: 'Better',
      } as StoredRecommendation;

      expect(isPendingRecommendation(recommendation)).toBe(false);
    });

    it('should return false for REJECTED recommendations', () => {
      const recommendation: StoredRecommendation = {
        id: '1',
        type: RecommendationType.META_TITLE,
        status: RecommendationStatus.REJECTED,
        currentValue: 'Old',
        recommendationValue: 'New',
        reasoning: 'Better',
      } as StoredRecommendation;

      expect(isPendingRecommendation(recommendation)).toBe(false);
    });
  });
});
