import { ScrapedPageType } from '../../types';
import {
  getPageType,
  extractDomainFromUrl,
  normalizeUrlForComparison,
  pageTypeRules,
} from '../pageUtils';

describe('pageUtils', () => {
  describe('getPageType', () => {
    it('should identify homepage URLs', () => {
      expect(getPageType('https://example.com/')).toBe(
        ScrapedPageType.HOMEPAGE,
      );
      expect(getPageType('/')).toBe(ScrapedPageType.HOMEPAGE);
    });

    it('should identify blog URLs', () => {
      expect(getPageType('https://example.com/blog/post-title')).toBe(
        ScrapedPageType.BLOG,
      );
      expect(getPageType('/blog/another-post')).toBe(ScrapedPageType.BLOG);
    });

    it('should identify neighborhood guide URLs', () => {
      expect(getPageType('https://example.com/neighborhoods/downtown')).toBe(
        ScrapedPageType.NEIGHBORHOOD_GUIDE,
      );
      expect(getPageType('/neighborhood/uptown')).toBe(
        ScrapedPageType.NEIGHBORHOOD_GUIDE,
      );
    });

    it('should identify agent bio URLs', () => {
      expect(getPageType('https://example.com/agents/john-doe')).toBe(
        ScrapedPageType.AGENT_BIO,
      );
      expect(getPageType('/agent/jane-smith')).toBe(ScrapedPageType.AGENT_BIO);
    });

    it('should identify home valuation URLs', () => {
      expect(getPageType('https://example.com/home-valuation')).toBe(
        ScrapedPageType.HOME_VALUATION,
      );
      expect(getPageType('/home-valuation/123')).toBe(
        ScrapedPageType.HOME_VALUATION,
      );
    });

    it('should identify mortgage calculator URLs', () => {
      expect(getPageType('https://example.com/mortgage-calculator')).toBe(
        ScrapedPageType.MORTGAGE_CALCULATOR,
      );
    });

    it('should identify buyers guide URLs', () => {
      expect(getPageType('https://example.com/buyers')).toBe(
        ScrapedPageType.BUYERS_GUIDE,
      );
      expect(getPageType('/buyers-guide')).toBe(ScrapedPageType.BUYERS_GUIDE);
    });

    it('should identify sellers guide URLs', () => {
      expect(getPageType('https://example.com/sellers')).toBe(
        ScrapedPageType.SELLERS_GUIDE,
      );
      expect(getPageType('/sellers-guide')).toBe(ScrapedPageType.SELLERS_GUIDE);
    });

    it('should return undefined for unrecognized URLs', () => {
      expect(getPageType('https://example.com/random-page')).toBeUndefined();
      expect(getPageType('/contact')).toBeUndefined();
      expect(getPageType('/properties/123')).toBeUndefined();
    });

    it('should handle invalid URLs gracefully', () => {
      expect(getPageType('invalid-url')).toBeUndefined();
      expect(getPageType('')).toBeUndefined();
    });
  });

  describe('extractDomainFromUrl', () => {
    it('should extract domain from valid URLs', () => {
      expect(extractDomainFromUrl('https://example.com/path')).toBe(
        'example.com',
      );
      expect(extractDomainFromUrl('http://subdomain.example.com/path')).toBe(
        'subdomain.example.com',
      );
      expect(extractDomainFromUrl('https://www.example.com')).toBe(
        'www.example.com',
      );
    });

    it('should handle URLs without protocol', () => {
      expect(extractDomainFromUrl('example.com/path')).toBe('example.com');
      expect(extractDomainFromUrl('subdomain.example.com')).toBe(
        'subdomain.example.com',
      );
    });

    it('should handle invalid URLs by returning the input', () => {
      expect(extractDomainFromUrl('invalid-url')).toBe('invalid-url');
      expect(extractDomainFromUrl('')).toBe('');
    });

    it('should handle URLs with ports', () => {
      expect(extractDomainFromUrl('https://example.com:8080/path')).toBe(
        'example.com:8080',
      );
    });
  });

  describe('normalizeUrlForComparison', () => {
    it('should convert HTTP to HTTPS', () => {
      expect(normalizeUrlForComparison('http://example.com/path')).toBe(
        'https://example.com/path',
      );
    });

    it('should leave HTTPS URLs unchanged', () => {
      expect(normalizeUrlForComparison('https://example.com/path')).toBe(
        'https://example.com/path',
      );
    });

    it('should handle non-string inputs', () => {
      expect(normalizeUrlForComparison(null as any)).toBe(null);
      expect(normalizeUrlForComparison(undefined as any)).toBe(undefined);
      expect(normalizeUrlForComparison('')).toBe('');
    });

    it('should leave URLs without protocol unchanged', () => {
      expect(normalizeUrlForComparison('example.com/path')).toBe(
        'example.com/path',
      );
    });
  });

  describe('pageTypeRules', () => {
    it('should export page type rules array', () => {
      expect(pageTypeRules).toBeDefined();
      expect(Array.isArray(pageTypeRules)).toBe(true);
      expect(pageTypeRules.length).toBeGreaterThan(0);
    });

    it('should contain expected page types', () => {
      const pageTypes = pageTypeRules.map(rule => rule[0]);
      expect(pageTypes).toContain(ScrapedPageType.HOMEPAGE);
      expect(pageTypes).toContain(ScrapedPageType.BLOG);
      expect(pageTypes).toContain(ScrapedPageType.NEIGHBORHOOD_GUIDE);
      expect(pageTypes).toContain(ScrapedPageType.AGENT_BIO);
    });
  });
});
