import { Logger } from '@aws-lambda-powertools/logger';
import { StarApiPayload } from 'src/clients/interfaces/IStarApiClient';
import { getEnvironmentConfig } from 'src/config/implementation';

let logger = new Logger({ serviceName: 'SlackUtil' });

const notifiedErrors = new Set<string>();

// For testing purposes
export function setLogger(newLogger: Logger): void {
  logger = newLogger;
}

// For testing purposes - clear the deduplication cache
export function clearNotificationCache(): void {
  notifiedErrors.clear();
}

/**
 * Generates a unique deduplication key for element failures
 * @param payload The StarApiPayload containing element details
 * @param error The error that occurred
 * @returns A unique string key for this specific element failure
 */
function getDeduplicationKey(payload?: StarApiPayload, error?: Error): string {
  if (!payload) return '';

  const parts = [
    payload.url,
    payload.input.elementTag,
    payload.input.elementProperty || '',
    payload.input.elementIndex?.toString() || '',
    error?.message || '',
  ];

  return parts.join('|');
}

/**
 * Sends a notification to Slack when a STAR API request fails
 * This function handles errors internally and never throws
 */
export async function notifyStarApiFailure({
  error,
  payload,
  url,
  method,
  status,
  requestId,
  companyId,
  responseBody,
  actionId,
}: {
  error: Error;
  payload?: StarApiPayload;
  url: string;
  method: string;
  status?: number;
  requestId?: string;
  companyId?: string;
  responseBody?: any;
  actionId?: string;
}): Promise<void> {
  const dedupKey = getDeduplicationKey(payload, error);

  if (dedupKey && notifiedErrors.has(dedupKey)) {
    logger.info('Skipping duplicate notification for element', {
      dedupKey,
      errorMessage: error.message,
      url: payload?.url || url,
      companyId,
      method,
      status,
      requestId,
    });
    return;
  }

  if (dedupKey) {
    notifiedErrors.add(dedupKey);
  }

  logger.info('notifyStarApiFailure called', {
    errorMessage: error.message,
    companyId,
    url: payload?.url || url,
    method,
    status,
    requestId,
    hasPayload: !!payload,
    hasResponseBody: !!responseBody,
    dedupKey,
    actionId,
  });
  const presenceUrl = `https://app.luxurypresence.com/admin/accounts/${companyId}`;

  // Build blocks for a rich formatted message
  const blocks: any[] = [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: '🚨 STAR API Failure',
        emoji: true,
      },
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Error:* ${error.message}`,
      },
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*CompanyId:*\n${companyId || 'Unknown'}`,
        },
        {
          type: 'mrkdwn',
          text: `*Admin Account URL:*\n<${presenceUrl}|${companyId || 'Unknown'}>`,
        },
        {
          type: 'mrkdwn',
          text: `*API Endpoint:*\n\`${method} ${url}\``,
        },
        {
          type: 'mrkdwn',
          text: `*Status:*\n${status ?? 'N/A'}`,
        },
        {
          type: 'mrkdwn',
          text: `*Request ID:*\n${requestId ?? 'N/A'}`,
        },
        {
          type: 'mrkdwn',
          text: `*Scheduled Action ID:*\n${actionId ?? 'N/A'}`,
        },
      ],
    },
  ];

  // Add page update details if payload exists
  if (payload) {
    blocks.push(
      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*Page Update Details*',
        },
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Page URL:*\n${payload.url}`,
          },
          {
            type: 'mrkdwn',
            text: `*Element:*\n\`${payload.input.elementTag}${
              payload.input.elementProperty
                ? ` (${payload.input.elementProperty})`
                : ''
            }${payload.input.elementIndex !== undefined ? ` [index: ${payload.input.elementIndex}]` : ''}\``,
          },
        ],
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Old Content:*\n> ${payload.input.oldContent}`,
        },
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*New Content:*\n> ${payload.input.newContent}`,
        },
      },
    );

    if (payload.debug) {
      blocks.push({
        type: 'context',
        elements: [
          {
            type: 'mrkdwn',
            text: '⚠️ Debug mode enabled',
          },
        ],
      });
    }
  }

  // Add stack trace if available
  if (error.stack) {
    blocks.push(
      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Stack Trace:*\n\`\`\`${error.stack.split('\n').slice(0, 3).join('\n')}\`\`\``,
        },
      },
    );
  }

  // Add response body if available
  if (responseBody) {
    const responseText = JSON.stringify(responseBody, null, 2);
    // Slack has a 3000 character limit for text fields, so truncate if needed
    const truncatedResponse =
      responseText.length > 2900
        ? responseText.substring(0, 2900) + '\n... (truncated)'
        : responseText;

    blocks.push(
      {
        type: 'divider',
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Response Body:*\n\`\`\`${truncatedResponse}\`\`\``,
        },
      },
    );
  }

  // Add timestamp
  blocks.push({
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: `<!date^${Math.floor(Date.now() / 1000)}^{date_short_pretty} at {time}|${new Date().toISOString()}>`,
      },
    ],
  });

  // Fallback text for notifications
  const fallbackText = `STAR API failure: ${error.message} for ${companyId || 'Unknown company'}`;

  await sendSlackMessage(fallbackText, blocks);
}

/**
 * Generic function to send a message to Slack
 */
export async function sendSlackMessage(
  text: string,
  blocks?: any[],
): Promise<void> {
  const config = getEnvironmentConfig();

  // Check if notifications are enabled
  const shouldSendNotification = config.slack.enabled;

  // Enhanced logging to help debug notification issues
  logger.info('Slack notification decision', {
    shouldSendNotification,
    slackEnabled: config.slack.enabled,
    slackWebhookUrl: config.slack.webhookUrl ? 'configured' : 'not configured',
    containsExampleDotCom: text.includes('example.com'),
    nodeEnv: process.env.NODE_ENV,
    environment: process.env.ENVIRONMENT,
    textPreview: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
  });

  if (!shouldSendNotification) {
    logger.warn('Slack notification blocked', {
      reason: 'notifications_disabled',
      text,
      blocks,
      slackEnabled: config.slack.enabled,
      slackWebhookUrl: config.slack.webhookUrl
        ? 'configured'
        : 'not configured',
      nodeEnv: process.env.NODE_ENV,
      environment: process.env.ENVIRONMENT,
    });
    return;
  }

  try {
    const payload: any = { text };
    if (blocks) {
      payload.blocks = blocks;
    }

    const response = await fetch(config.slack.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Slack API returned ${response.status}`);
    }

    logger.info('Sent Slack notification');
  } catch (error) {
    logger.error('Failed to send Slack notification', {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

export async function sendSeoAlertMessage(text: string, blocks?: any[]) {
  const config = getEnvironmentConfig();

  if (!config.slack.enabled) {
    logger.warn('Slack notifications are disabled');
    return;
  }

  if (!config.slack.seoAlertsWebhookUrl) {
    logger.warn('Slack SEO alerts webhook URL not configured');
    return;
  }

  try {
    const response = await fetch(config.slack.seoAlertsWebhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text, blocks }),
    });

    if (!response.ok) {
      logger.error('Failed to send Slack SEO alert notification', {
        error: response.statusText,
      });
      return;
    }

    logger.info('Sent Slack SEO alert notification');
  } catch (error) {
    logger.error('Failed to send Slack SEO alert notification', {
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
