import {
  StoredRecommendation,
  RecommendationStatus,
} from '../clients/types/seoApiGateway';
import { RecommendationType } from '../types';

/**
 * Maps update results to recommendation types
 */
export interface UpdateResult {
  elementType: 'meta' | 'h1' | 'h2';
  elementProperty?: string;
  success: boolean;
  error?: string;
  metadata?: Record<string, unknown>;
  statusCode?: number;
}

/**
 * Maps successful updates to recommendation types
 */
export function mapUpdatesToRecommendationTypes(
  updates: UpdateResult[],
): Set<RecommendationType> {
  const successfulRecommendationTypes = new Set<RecommendationType>();

  updates.forEach(updateResult => {
    // Only process successful updates
    if (!updateResult.success) {
      return;
    }

    if (
      updateResult.elementType === 'meta' &&
      updateResult.elementProperty === 'title'
    ) {
      successfulRecommendationTypes.add(RecommendationType.META_TITLE);
    } else if (
      updateResult.elementType === 'meta' &&
      updateResult.elementProperty === 'description'
    ) {
      successfulRecommendationTypes.add(RecommendationType.META_DESCRIPTION);
    } else if (
      updateResult.elementType === 'h1' ||
      updateResult.elementType === 'h2'
    ) {
      successfulRecommendationTypes.add(RecommendationType.MAIN_HEADING);
    }
  });

  return successfulRecommendationTypes;
}

/**
 * Builds failure reason object from failed updates for error reporting
 */
export function buildFailureReasonFromUpdates(
  failedUpdates: UpdateResult[],
): Record<string, any> {
  const failureReasonDetails: Record<string, any> = {};

  failedUpdates.forEach(failedUpdate => {
    const errorKey = failedUpdate.elementProperty
      ? `${failedUpdate.elementType}_${failedUpdate.elementProperty}`
      : failedUpdate.elementType;

    failureReasonDetails[errorKey] = {
      error: failedUpdate.error || 'Unknown error',
      ...(failedUpdate.metadata && { metadata: failedUpdate.metadata }),
    };
  });

  return failureReasonDetails;
}

/**
 * Checks if a recommendation has PENDING status and should be published
 */
export function isPendingRecommendation(
  recommendation: StoredRecommendation,
): boolean {
  return recommendation.status === RecommendationStatus.PENDING;
}
