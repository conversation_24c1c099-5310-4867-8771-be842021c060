import { HomepageFeed } from '../clients/types/seoApiGateway';
import { Entitlement } from '../types';

export interface Admin {
  email: string;
  displayId: string;
  firstName: string;
  lastName: string;
  avatar: string;
  teamLead: boolean;
  externalAuthId: string;
  facebookUserId: string;
  lastLogin: string;
  notificationsLastSeen: string;
  leadEmail: string;
  phoneNumber: string;
  type: string;
}

export const createTestEntitlement = (
  overrides: Partial<Entitlement> = {},
): Entitlement => ({
  companyId: 'c1',
  websiteUrl: 'site1.com',
  displayName: 'Site 1',
  productId: 'product1',
  productName: 'Product 1',
  endDate: null,
  units: 1,
  ...overrides,
});

export const createTestAdmin = (overrides: Partial<Admin> = {}): Admin => ({
  email: '<EMAIL>',
  displayId: 'display1',
  firstName: 'First',
  lastName: 'Last',
  avatar: 'avatar1',
  teamLead: false,
  externalAuthId: 'external1',
  facebookUserId: 'facebook1',
  lastLogin: '2021-01-01',
  notificationsLastSeen: '2021-01-01',
  leadEmail: '<EMAIL>',
  phoneNumber: '1234567890',
  type: 'admin',
  ...overrides,
});

export const createTestEmailContent = (
  overrides: Partial<HomepageFeed> = {},
): HomepageFeed => ({
  items: [
    {
      timestamp: '2021-01-01T00:00:00Z',
      itemType: 'type',
      content: { publishedAt: '2021-01-01T00:00:00Z' },
    },
  ],
  ...overrides,
});

export const createMultipleEntitlements = (
  count: number,
  baseCompanyId = 'c',
): Entitlement[] => {
  return Array.from({ length: count }, (_, i) =>
    createTestEntitlement({
      companyId: `${baseCompanyId}${i + 1}`,
      websiteUrl: `site${i + 1}.com`,
      displayName: `Site ${i + 1}`,
      productId: `product${i + 1}`,
      productName: `Product ${i + 1}`,
      units: i + 1,
    }),
  );
};

export const createMultipleAdmins = (
  count: number,
  baseEmail = 'admin',
  domain = 'test.com',
): Admin[] => {
  return Array.from({ length: count }, (_, i) =>
    createTestAdmin({
      email: `${baseEmail}${i + 1}@${domain}`,
      displayId: `display${i + 1}`,
      firstName: `First${i + 1}`,
      lastName: `Last${i + 1}`,
    }),
  );
};
