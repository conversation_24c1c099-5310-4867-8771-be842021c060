import { Logger } from '@aws-lambda-powertools/logger';
import { Context } from 'aws-lambda';
import { GraphQLClient } from 'graphql-request';

import { Website } from '../clients/ApiGatewayClient';
import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import { TenantClient } from '../clients/TenantClient';
import {
  RecommendationStatus,
  StoredRecommendation,
  StoredScrapedPage,
} from '../clients/types/seoApiGateway';
import { EnvironmentConfig, PromptConfig } from '../config/interfaces';
import { createContextLogger } from '../factories/LoggerFactory';
import { ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { ITenantService } from '../services/interfaces/ITenantService';
import {
  ScheduledAction,
  Status,
  WorkflowType,
  EntitlementDTO,
  Entitlement,
  ScrapedPageType,
  RecommendationType,
} from '../types';

/**
 * Creates a configured Logger instance for testing with spies on info and error methods
 * that output to the console for easier debugging.
 *
 * The returned logger includes a restore() method that should be called in afterEach
 * to clean up spies and prevent state bleed between tests.
 *
 * @example
 * ```typescript
 * let logger: Logger & { restore: () => void };
 *
 * beforeEach(() => {
 *   logger = createTestLogger();
 * });
 *
 * afterEach(() => {
 *   logger.restore();
 * });
 * ```
 *
 * @param serviceName Optional service name for the logger (defaults to 'test-service')
 * @returns A configured Logger instance with spies and a restore method
 */
export const createTestLogger = (
  serviceName = 'test-service',
): Logger & { restore: () => void } => {
  // Create a real Logger instance for testing
  const logger = new Logger({
    logLevel: 'INFO',
    serviceName,
  }) as Logger & { restore: () => void };

  // Spy on the info method and ensure console output
  const infoSpy = jest
    .spyOn(logger, 'info')
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    .mockImplementation((_message, ..._args) => {
      // uncomment to see test logger logs
      // console.log(_message, ..._args);
      return logger;
    });

  // Spy on the error method and ensure console output
  const errorSpy = jest
    .spyOn(logger, 'error')
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    .mockImplementation((_message, ..._args) => {
      // uncomment to see test logger logs
      // console.error(_message, ..._args);
      return logger;
    });

  // Add ContextLogger methods
  (logger as any).setContext = jest.fn();
  (logger as any).addContextValue = jest.fn();
  (logger as any).clearContext = jest.fn();
  (logger as any).clearAllContext = jest.fn();
  (logger as any).getContext = jest.fn().mockReturnValue({});
  (logger as any).createChild = jest.fn().mockReturnValue(logger);
  (logger as any).createComponentLogger = jest.fn().mockReturnValue(logger);
  (logger as any).getBaseLogger = jest.fn().mockReturnValue(logger);

  // Add missing custom metrics methods
  (logger as any).addCustomMetric = jest.fn();
  (logger as any).addCustomMetrics = jest.fn();
  // Add debug and warn methods spies
  const debugSpy = jest.spyOn(logger, 'debug').mockImplementation(() => logger);
  const warnSpy = jest.spyOn(logger, 'warn').mockImplementation(() => logger);

  // Add a restore method to clean up spies
  logger.restore = () => {
    infoSpy.mockRestore();
    errorSpy.mockRestore();
    debugSpy.mockRestore();
    warnSpy.mockRestore();
  };

  return logger;
};

/**
 * Sets up common mocks for testing, including the Logger factory
 *
 * The returned mockLogger includes a restore() method that should be called in afterEach
 * to clean up spies and prevent state bleed between tests.
 *
 * @example
 * ```typescript
 * let mockLogger: Logger & { restore: () => void };
 *
 * beforeEach(() => {
 *   const mocks = setupTestMocks();
 *   mockLogger = mocks.mockLogger;
 * });
 *
 * afterEach(() => {
 *   mockLogger.restore();
 * });
 * ```
 *
 * @param serviceName Optional service name for the logger (defaults to 'test-service')
 * @returns An object containing the mocked logger and any other common mocks
 */
export const setupTestMocks = (
  serviceName = 'test-service',
): { mockLogger: Logger & { restore: () => void } } => {
  // Create the test logger
  const mockLogger = createTestLogger(serviceName);

  // Mock the LoggerFactory
  // We need to ensure createContextLogger is properly set up as a Jest mock function
  // Only mock if it's not already mocked (some tests mock the entire module)
  if (
    typeof createContextLogger === 'function' &&
    'mockImplementation' in createContextLogger
  ) {
    const mockCreateContextLogger = createContextLogger as jest.Mock;
    mockCreateContextLogger.mockImplementation(() => mockLogger);
  }

  return {
    mockLogger,
  };
};

/**
 * Creates mock ScheduledAction objects for testing
 * Generates unique actions for any requested count
 */
export const createMockScheduledActions = (
  count: number = 2,
  overrides: Partial<ScheduledAction>[] = [],
): ScheduledAction[] => {
  // Generate the requested number of unique actions
  const actions: ScheduledAction[] = Array.from(
    { length: count },
    (_, index) => {
      // Alternate between SEO and BLOG workflow types
      const workflowType =
        index % 2 === 0 ? WorkflowType.SEO : WorkflowType.BLOG;

      // Create a date that's different for each action (one day apart)
      const date = new Date('2025-01-01T00:00:00Z');
      date.setDate(date.getDate() + index);
      const dateString = date.toISOString();

      return {
        id: `action-${index + 1}`,
        companyId: `company-${index + 1}`,
        workflowType,
        status: Status.DRAFT_PENDING,
        createdAt: dateString,
        updatedAt: dateString,
        surfacedAt: null,
        scheduledToBeSurfacedAt: null,
        scheduledToBePublishedAt: null,
        publishedAt: null,
        contentPayload: {}, // Will be populated with StoreSEODraftResponse for SURFACING_PENDING+ statuses
        generationPayload: { key: `value-${index + 1}` },
        failureReason: {},
        executionName: null,
        executionArn: null,
      };
    },
  );

  // Apply overrides if provided
  overrides.forEach((override, index) => {
    if (index < actions.length) {
      actions[index] = { ...actions[index], ...override };
    }
  });

  return actions;
};

/**
 * Creates mock EntitlementDTO objects for testing
 */
export const createMockEntitlementDTOs = (
  count: number = 3,
): EntitlementDTO[] => {
  const currentDate = new Date();
  const pastDate = new Date(currentDate);
  pastDate.setFullYear(pastDate.getFullYear() - 1);
  const futureDate = new Date(currentDate);
  futureDate.setFullYear(futureDate.getFullYear() + 1);

  return Array.from({ length: count }, (_, i) => ({
    startDate: pastDate.toISOString(),
    endDate: futureDate.toISOString(),
    company: {
      website: `test${i + 1}.com`,
      name: `Test Company ${i + 1}`,
    },
    displayId: `test-${i + 1}`,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    productId: 'product1',
    companyId: `company${i + 1}`,
    units: 1,
    salesforceServiceId: `sf${i + 1}`,
    product: {
      id: 'product1',
      name: 'Test Product',
    },
  }));
};

/**
 * Creates mock Entitlement objects for testing
 */
export const createMockEntitlements = (
  count: number = 45,
  overrides: Partial<Entitlement>[] = [],
): Entitlement[] => {
  const entitlements: Entitlement[] = Array.from({ length: count }, (_, i) => ({
    id: `entitlement-${i}`,
    productId: `product-${i}`,
    companyId: `company-${i}`,
    productName: `Product ${i}`,
    units: 2,
    websiteUrl: `https://www.test-website-${i}.com`,
    displayName: `Test Entitlement ${i}`,
    endDate: new Date().toISOString(),
    startDate: new Date().toISOString(),
    status: 'active',
    type: 'standard',
  }));

  // Apply overrides if provided
  overrides.forEach((override, index) => {
    if (index < entitlements.length) {
      entitlements[index] = { ...entitlements[index], ...override };
    }
  });

  return entitlements;
};

/**
 * Creates a mock AWS Lambda Context for testing
 */
export const createMockLambdaContext = (
  overrides: Partial<Context> = {},
): Context => ({
  awsRequestId: 'test-request-id',
  callbackWaitsForEmptyEventLoop: true,
  functionName: 'test-function',
  functionVersion: '1',
  invokedFunctionArn: 'test:arn',
  logGroupName: 'test-log-group',
  logStreamName: 'test-log-stream',
  memoryLimitInMB: '128',
  getRemainingTimeInMillis: () => 1000,
  done: () => {},
  fail: () => {},
  succeed: () => {},
  ...overrides,
});

/**
 * Creates a mock API Gateway Client for testing
 */
export const createMockSeoApiGatewayClient =
  (): jest.Mocked<ISeoApiGatewayClient> => ({
    getDraftPendingActions: jest.fn(),
    getSurfacedActions: jest.fn(),
    updateScheduledAction: jest.fn(),
    getUpcomingActions: jest.fn(),
    createOwedActions: jest.fn(),
    getPagesByCompanyId: jest.fn(),
    getAllActionsToSurface: jest.fn(),
    processActionsToSurfaceInBatches: jest.fn(),
    fetchAllPaginatedActions: jest.fn(),
    getAction: jest.fn(),
    storeSEODraft: jest.fn(),
    getPageById: jest.fn(),
    getEmailContent: jest.fn(),
    sendEmail: jest.fn(),
    updatePageKeywordRank: jest.fn(),
    getSurfacedAndPublishedGroups: jest.fn(),
    getSurfacedAndPublishedGroupsForRanking: jest.fn(),
    getSurfacedAndPublishedGroupsForRankingPaginated: jest.fn(),
    getRecommendationById: jest.fn(),
    applyRecommendation: jest.fn(),
    getRecommendationsByGroupId: jest.fn(),
  });

/**
 * Creates a mock GraphQL Client for testing
 */
export const createMockGraphQLClient = (): jest.Mocked<GraphQLClient> =>
  ({
    request: jest.fn(),
  }) as unknown as jest.Mocked<GraphQLClient>;

/**
 * Creates a pure mock Tenant Client for testing without calling the real constructor
 * This prevents any side effects from the real constructor
 */
export const createMockTenantClient = (
  mockLogger: ContextLogger,
): jest.Mocked<TenantClient> =>
  ({
    // Mock required properties
    baseUrl: 'http://test-url',
    entitlementsLimit: 100,
    logger: mockLogger,
    productIds: ['product1', 'product2'],

    // Mock methods
    getAllEntitlements: jest.fn(),
    getEntitlementsForCompany: jest.fn(),
  }) as unknown as jest.Mocked<TenantClient>;

/**
 * Creates a mock Scheduled Action Service for testing
 */
export const createMockScheduledActionService =
  (): jest.Mocked<IScheduledActionService> => ({
    getDraftPendingActions: jest.fn(),
    getSurfacedActions: jest.fn(),
    getUpcomingActions: jest.fn(),
    createScheduledActions: jest.fn(),
    updateStatus: jest.fn(),
    updateWithFailure: jest.fn(),
    update: jest.fn(),
    findActionsToSurface: jest.fn(),
    processActionsToSurfaceInBatches: jest.fn(),
    getAction: jest.fn(),
    checkAction: jest.fn(),
    storeSEODraft: jest.fn(),
  });

/**
 * Creates a mock Tenant Service for testing
 */
export const createMockTenantService = (): jest.Mocked<ITenantService> => ({
  getEntitlements: jest.fn(),
  getCompanyAdmins: jest.fn(),
});

/**
 * Creates a mock SFN Client send function for testing
 */
export const createMockSFNSend = () =>
  jest.fn().mockResolvedValue({
    executionArn: 'mock-execution-arn',
  });

/**
 * Creates a mock configuration object for testing
 */
export const createMockConfig = (): EnvironmentConfig => {
  const prompt: PromptConfig = {
    langfuse: {
      secretKey: 'fake-langfuse-secret-key',
      publicKey: 'fake-langfuse-public-key',
      baseUrl: 'https://us.cloud.langfuse.com',
      environment: 'test',
      flushAt: 0,
      prompts: {
        keywordMapper: 'fake-keyword-mapper-prompt',
        recommendationGenerator: 'fake-recommendation-generator-prompt',
      },
    },
    openai: {
      modelName: 'gpt-4o',
      temperature: 0,
      apiKey: 'fake-openai-api-key',
    },
  };

  const defaultConfig: EnvironmentConfig = {
    apiGateway: {
      url: 'https://test-api.com',
      superUser: 'test-super-user',
      apiGatewayKey: 'test-key',
    },
    stateMachine: {
      seoDraftArn:
        'arn:aws:states:us-east-1:123456789012:stateMachine:seo-draft-sfn',
      blogDraftArn:
        'arn:aws:states:us-east-1:123456789012:stateMachine:blog-draft-sfn',
    },
    actionChunkSize: 5,
    mock: {
      useMockClients: false,
      useMockStar: false,
    },
    seoAutomationApi: {
      url: 'https://test-api.com',
      m2mSuperApiKey: 'test-key',
    },
    tenantApi: {
      url: 'https://test-tenant-api.com',
      entitlementsLimit: 100,
      entitlementsChunkSize: 20,
      productIds: ['test-product'],
    },
    cmsApi: {
      url: 'https://test-cms-api.com',
    },
    environment: 'test',
    launchDarklyKey: 'mock-ld-key',
    generatePostFunctionName: 'mock-generate-post-function',
    screenshotsS3: {
      bucketName: 'mock-bucket',
      region: 'mock-region',
      endpoint: 'https://mock-r2.com',
      credentials: {
        accessKeyId: 'mock-access-key-id',
        secretAccessKey: 'mock-secret-access-key',
      },
    },
    apiResponseS3: {
      bucketName: 'mock-bucket',
    },
    firecrawl: {
      apiKey: 'mock-firecrawl-key',
    },
    prompt,
    sendgrid: {
      weeklyDigestTemplateId: 'mock-weekly-digest-template-id',
      asmGroupId: 'mock-asm-group-id',
      bccEmails: ['<EMAIL>'],
    },
    dataForSeo: {
      username: 'mock-username',
      password: 'mock-password',
      postbackUrl: 'https://mock-postback.com',
      baseUrl: 'https://api.dataforseo.com',
    },
    clientMarketingApi: {
      url: 'https://client-marketing-service.com',
    },
    starApi: {
      url: 'https://test-star-api.com',
    },
    slack: {
      webhookUrl: 'https://slack.com',
      enabled: true,
      seoAlertsWebhookUrl: 'https://slack.com',
    },
  };

  return defaultConfig;
};

/**
 * Creates a mock DataForSeoClient for testing
 */
export const createMockDataForSeoClient = (): jest.Mocked<any> => ({
  enqueueTasks: jest.fn(),
});

/**
 * Creates mock Website objects for testing
 */
export const createMockWebsites = (
  count: number = 2,
  overrides: Partial<Website>[] = [],
): Website[] => {
  const websites: Website[] = Array.from({ length: count }, (_, i) => ({
    name: `Test Website ${i + 1}`,
    hostname: `test-website-${i + 1}.com`,
    category: 'BRAND',
  }));

  // Apply overrides if provided
  overrides.forEach((override, index) => {
    if (index < websites.length) {
      websites[index] = { ...websites[index], ...override };
    }
  });

  return websites;
};

/**
 * Creates mock Page objects for testing
 */
export const createMockScrapedPages = (
  count: number = 2,
  overrides: Partial<StoredScrapedPage>[] = [],
): StoredScrapedPage[] => {
  const scrapedPages: StoredScrapedPage[] = Array.from(
    { length: count },
    (_, i) => ({
      id: `scrape-${i + 1}`,
      url: `https://test-company.com/page-${i + 1}`,
      pageName: `Test Page ${i + 1}`,
      pageType: i % 2 === 0 ? ScrapedPageType.HOMEPAGE : ScrapedPageType.BLOG,
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      recommendations: [],
      scrapes: [],
      pageKeywordHistory: [],
      pageKeywords: [],
      companyId: `company-${i + 1}`,
    }),
  );

  // Apply overrides if provided
  overrides.forEach((override, index) => {
    if (index < scrapedPages.length) {
      scrapedPages[index] = { ...scrapedPages[index], ...override };
    }
  });

  return scrapedPages;
};

/**
 * Creates mock StoredRecommendation objects for testing
 * @param overrides - An object containing field overrides for specific recommendations
 * @returns An object containing the three standard recommendations (metaTitle, metaDescription, mainHeading)
 */
export const createMockStoredRecommendations = (
  overrides: {
    metaTitle?: Partial<StoredRecommendation>;
    metaDescription?: Partial<StoredRecommendation>;
    mainHeading?: Partial<StoredRecommendation>;
  } = {},
): {
  metaTitle: StoredRecommendation;
  metaDescription: StoredRecommendation;
  mainHeading: StoredRecommendation;
} => {
  const baseDate = new Date('2024-01-01');

  const defaultMetaTitle: StoredRecommendation = {
    id: 'rec-1',
    scrapeId: 'scrape-123',
    groupId: 'group-123',
    type: RecommendationType.META_TITLE,
    currentValue: 'Old Meta Title',
    recommendationValue: 'New Meta Title',
    reasoning: 'SEO improvement',
    status: RecommendationStatus.PENDING,
    rejectionReason: null,
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  const defaultMetaDescription: StoredRecommendation = {
    id: 'rec-2',
    scrapeId: 'scrape-123',
    groupId: 'group-123',
    type: RecommendationType.META_DESCRIPTION,
    currentValue: 'Old meta description',
    recommendationValue: 'New meta description',
    reasoning: 'SEO improvement',
    status: RecommendationStatus.PENDING,
    rejectionReason: null,
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  const defaultMainHeading: StoredRecommendation = {
    id: 'rec-3',
    scrapeId: 'scrape-123',
    groupId: 'group-123',
    type: RecommendationType.MAIN_HEADING,
    currentValue: 'Old H1 Content',
    recommendationValue: 'New H1 Content',
    reasoning: 'SEO improvement',
    status: RecommendationStatus.PENDING,
    rejectionReason: null,
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  return {
    metaTitle: { ...defaultMetaTitle, ...overrides.metaTitle },
    metaDescription: {
      ...defaultMetaDescription,
      ...overrides.metaDescription,
    },
    mainHeading: { ...defaultMainHeading, ...overrides.mainHeading },
  };
};

/**
 * Creates an array of mock StoredRecommendation objects for testing
 * @param overrides - An object containing field overrides for specific recommendations
 * @returns An array containing the three standard recommendations
 */
export const createMockStoredRecommendationsArray = (
  overrides: {
    metaTitle?: Partial<StoredRecommendation>;
    metaDescription?: Partial<StoredRecommendation>;
    mainHeading?: Partial<StoredRecommendation>;
  } = {},
): StoredRecommendation[] => {
  const recommendations = createMockStoredRecommendations(overrides);
  return [
    recommendations.metaTitle,
    recommendations.metaDescription,
    recommendations.mainHeading,
  ];
};

/**
 * Creates a mock StoreSEODraftResponse structure for testing
 * @param companyId - The company ID for the response
 * @param keyword - Optional keyword override
 * @param url - Optional URL override
 * @returns A complete StoreSEODraftResponse structure
 */
export const createMockStoreSEODraftResponse = (
  companyId: string,
  keyword?: string,
  url?: string,
) => {
  const baseDate = new Date('2024-01-01');
  const keywordValue = keyword || 'real estate';
  const pageUrl = url || 'https://test-company.com/home';

  const savedKeyword = {
    id: 'keyword-123',
    keyword: keywordValue,
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  const savedPage = {
    id: 'page-123',
    companyId,
    url: pageUrl,
    pageName: 'Test Real Estate Company',
    pageType: ScrapedPageType.HOMEPAGE,
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
    deletedAt: null,
  };

  const savedScrape = {
    id: 'scrape-123',
    companyId,
    scrapedPageId: 'page-123',
    rawHtml:
      '<html><head><title>Test Title</title></head><body><h1>Test Heading</h1></body></html>',
    markdown: '# Test Heading\n\nTest content',
    currentScrapedValues: {
      metaTitle: 'Test Title',
      metaDescription: 'Test description',
      mainHeading: 'Test Heading',
    },
    mediaId: 'media-123',
    scrapedAt: baseDate,
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  const savedPageKeyword = {
    id: 'page-keyword-123',
    scrapedPageId: 'page-123',
    keywordId: 'keyword-123',
    originalRank: null,
    currentRank: null,
    rankCheckedAt: null,
    createdAt: baseDate,
    updatedAt: baseDate,
  };

  const savedGroup = {
    id: 'group-123',
    companyId,
    keywordId: 'keyword-123',
    title: 'Homepage SEO Optimization',
    description:
      'Optimize meta titles and descriptions for better search engine visibility',
    metadata: {},
    createdAt: baseDate,
    updatedAt: baseDate,
    deletedAt: null,
  };

  const savedRecommendations = createMockStoredRecommendationsArray({
    metaTitle: { scrapeId: 'scrape-123', groupId: 'group-123' },
    metaDescription: { scrapeId: 'scrape-123', groupId: 'group-123' },
    mainHeading: { scrapeId: 'scrape-123', groupId: 'group-123' },
  });

  return {
    savedKeyword,
    savedRecommendations,
    savedScrape,
    savedPage,
    savedPageKeyword,
    savedGroup,
  };
};

/**
 * Creates a mock context logger for testing purposes
 */
export function createMockContextLogger(): jest.Mocked<ContextLogger> {
  const mockLogger: any = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    addContextValue: jest.fn(),
    setContext: jest.fn(),
    clearContext: jest.fn(),
    clearAllContext: jest.fn(),
    getContext: jest.fn().mockReturnValue({}),
    createChild: jest.fn(),
    createComponentLogger: jest.fn(),
    getBaseLogger: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    }),
  };

  // Make createChild and createComponentLogger return the same mock logger
  mockLogger.createChild.mockReturnValue(mockLogger);
  mockLogger.createComponentLogger.mockReturnValue(mockLogger);

  return mockLogger;
}

/**
 * Creates a mock base logger for testing services
 */
export function createMockBaseLogger() {
  return {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  } as any;
}
