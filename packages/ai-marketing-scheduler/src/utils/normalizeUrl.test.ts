import { normalizeUrl } from './normalizeUrl';

describe('normalizeUrl', () => {
  describe('valid URLs', () => {
    it('should add https:// protocol if missing', () => {
      expect(normalizeUrl('example.com')).toBe('https://example.com');
      expect(normalizeUrl('www.example.com')).toBe('https://example.com');
    });

    it('should preserve existing protocols', () => {
      expect(normalizeUrl('http://example.com')).toBe('http://example.com');
      expect(normalizeUrl('https://example.com')).toBe('https://example.com');
    });

    it('should lowercase the hostname', () => {
      expect(normalizeUrl('https://EXAMPLE.COM')).toBe('https://example.com');
      expect(normalizeUrl('https://ExAmPlE.cOm')).toBe('https://example.com');
    });

    it('should remove www prefix', () => {
      expect(normalizeUrl('https://www.example.com')).toBe(
        'https://example.com',
      );
      expect(normalizeUrl('http://www.example.com')).toBe('http://example.com');
      expect(normalizeUrl('www.example.com')).toBe('https://example.com');
    });

    it('should remove default ports', () => {
      expect(normalizeUrl('http://example.com:80')).toBe('http://example.com');
      expect(normalizeUrl('https://example.com:443')).toBe(
        'https://example.com',
      );
    });

    it('should preserve non-default ports', () => {
      expect(normalizeUrl('http://example.com:8080')).toBe(
        'http://example.com:8080',
      );
      expect(normalizeUrl('https://example.com:8443')).toBe(
        'https://example.com:8443',
      );
    });

    it('should remove trailing slash from pathname', () => {
      expect(normalizeUrl('https://example.com/')).toBe('https://example.com');
      expect(normalizeUrl('https://example.com/path/')).toBe(
        'https://example.com/path',
      );
      expect(normalizeUrl('https://example.com/path/to/resource/')).toBe(
        'https://example.com/path/to/resource',
      );
    });

    it('should preserve root path without trailing slash', () => {
      expect(normalizeUrl('https://example.com')).toBe('https://example.com');
    });

    it('should sort query parameters alphabetically', () => {
      expect(normalizeUrl('https://example.com?z=1&a=2&m=3')).toBe(
        'https://example.com?a=2&m=3&z=1',
      );
      expect(normalizeUrl('https://example.com?foo=bar&baz=qux')).toBe(
        'https://example.com?baz=qux&foo=bar',
      );
    });

    it('should handle multiple values for the same query parameter', () => {
      expect(normalizeUrl('https://example.com?a=1&a=2&b=3')).toBe(
        'https://example.com?a=1&a=2&b=3',
      );
    });

    it('should remove hash/fragment', () => {
      expect(normalizeUrl('https://example.com#section')).toBe(
        'https://example.com',
      );
      expect(normalizeUrl('https://example.com/path#anchor')).toBe(
        'https://example.com/path',
      );
      expect(normalizeUrl('https://example.com?foo=bar#section')).toBe(
        'https://example.com?foo=bar',
      );
    });

    it('should handle complex URLs', () => {
      expect(
        normalizeUrl(
          'HTTP://WWW.EXAMPLE.COM:80/PATH/TO/RESOURCE/?z=1&a=2#section',
        ),
      ).toBe('http://example.com/PATH/TO/RESOURCE?a=2&z=1');

      expect(
        normalizeUrl(
          'https://www.example.com:443/api/v1/?foo=bar&baz=qux#anchor',
        ),
      ).toBe('https://example.com/api/v1?baz=qux&foo=bar');
    });

    it('should trim whitespace', () => {
      expect(normalizeUrl('  https://example.com  ')).toBe(
        'https://example.com',
      );
      expect(normalizeUrl('\nhttps://example.com\t')).toBe(
        'https://example.com',
      );
    });

    it('should handle URLs with authentication', () => {
      expect(normalizeUrl('https://user:<EMAIL>')).toBe(
        'https://user:<EMAIL>',
      );
      expect(normalizeUrl('https://user:<EMAIL>')).toBe(
        'https://user:<EMAIL>',
      );
    });

    it('should handle subdomains (not www)', () => {
      expect(normalizeUrl('https://api.example.com')).toBe(
        'https://api.example.com',
      );
      expect(normalizeUrl('https://blog.example.com')).toBe(
        'https://blog.example.com',
      );
    });

    it('should handle special characters in query parameters', () => {
      expect(
        normalizeUrl(
          'https://example.com?email=<EMAIL>&name=John+Doe',
        ),
      ).toBe('https://example.com?email=test%40example.com&name=John+Doe');
    });
  });

  describe('error handling', () => {
    it('should throw error for invalid input', () => {
      expect(() => normalizeUrl('')).toThrow(
        'Invalid URL: must be a non-empty string',
      );
      expect(() => normalizeUrl(null as any)).toThrow(
        'Invalid URL: must be a non-empty string',
      );
      expect(() => normalizeUrl(undefined as any)).toThrow(
        'Invalid URL: must be a non-empty string',
      );
      expect(() => normalizeUrl(123 as any)).toThrow(
        'Invalid URL: must be a non-empty string',
      );
    });

    it('should throw error for malformed URLs', () => {
      // These are actually valid URLs according to the URL constructor
      // ht!tp://example becomes https://ht!tp://example
      expect(() => normalizeUrl('://example.com')).toThrow(
        'Invalid URL format',
      );
      expect(() => normalizeUrl('http://')).toThrow('Invalid URL format');
      expect(() => normalizeUrl('https://[invalid')).toThrow(
        'Invalid URL format',
      );
    });
  });
});
