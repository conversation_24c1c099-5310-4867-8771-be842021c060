import JWT from 'jsonwebtoken';

import { getConfig } from '../config';

export const getGWAuthToken = (superUser?: string, apiGatewayKey?: string) => {
  // Allow values to be omitted and fallback to config
  if (!superUser || !apiGatewayKey) {
    const { apiGateway } = getConfig();
    superUser = superUser || apiGateway.superUser;
    apiGatewayKey = apiGatewayKey || apiGateway.apiGatewayKey;
  }
  if (!apiGatewayKey) {
    throw new Error('API_GATEWAY_KEY is not set');
  }
  if (!superUser) {
    throw new Error('SUPER_USER is not set');
  }
  const token = JWT.sign({ id: superUser }, apiGatewayKey);
  return token;
};
