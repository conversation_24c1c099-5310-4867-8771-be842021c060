/* This is getting slurped for use by Firecrawl so it needs to remain a JS file */
export const mainHeading = `
(function() {
  const unwantedElements = '[data-uw-rm-ignore], .modal, .pop-up, iframe';

  // Hide elements first: we need them in the DOM so the heading index doesn't shift
  document
    .querySelectorAll(unwantedElements)
    .forEach(el => (el.style.display = 'none'));

  let mainHeading = null;
  const headings = Array.from(document.querySelectorAll('h1, h2'));

  for (const el of headings) {
    if (el.checkVisibility()) {
      mainHeading = el;
      break;
    }
  }

  const lpConfig = window.luxuryPresence?.config || {};
  let response;

  if (mainHeading) {
    const headingTag = mainHeading.tagName.toLowerCase();
    
    // Find the closest parent section with an id like "section-..."
    const sectionEl = mainHeading.closest('[id^="section-"]');

    if (!sectionEl || !sectionEl.id.startsWith('section-')) {
      return {error: 'No parent section element found with id starting with "section-"'};
    }

    // Extract section ID by splitting on "section-"
    const sectionId = sectionEl.id.split('section-')[1];
    const elementId = sectionEl.getAttribute('data-el-id');
    const sectionHeadings = Array.from(sectionEl.querySelectorAll(headingTag));
    const headingIndex = sectionHeadings.indexOf(mainHeading);

    response = {
      section_id: sectionId,
      element_id: elementId,
      tag: headingTag,
      index: headingIndex,
      content: mainHeading.textContent.trim(),
      page_id: lpConfig.pageId || '',
      website_id: lpConfig.websiteId || '',
    };
  }

  // Remove these elements so that their content doesn't appear in the markdown later
  document.querySelectorAll(unwantedElements).forEach(el => el.remove());

  // Disable modals for screenshot tool
  // Method 1: Override modal controller
  const disableModals = () => {
    if (window.luxuryPresence && window.luxuryPresence.modals) {
      // Override show method
      window.luxuryPresence.modals.show = function() {
        return;
      };
      
      // Override initialize method
      const originalInitialize = window.luxuryPresence.modals.initialize;
      window.luxuryPresence.modals.initialize = function() {
        return;
      };
    }
  };
  
  // Method 2: Hide existing modals
  const hideExistingModals = () => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
      modal.style.display = 'none';
    });
  };

  // Add CSS to hide all modals
  const style = document.createElement('style');
  style.textContent = '.modal { display: none !important; } #modals { display: none !important; }';
  document.head.appendChild(style);
  
  // Run immediately
  disableModals();
  hideExistingModals();
  
  // Also run after a short delay to catch late-loading modals
  setInterval(() => {
    disableModals();
    hideExistingModals();
  }, 1000);
  return response;
})();
`;
