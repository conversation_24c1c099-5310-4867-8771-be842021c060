import { ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { WorkflowType } from '../types';

/**
 * Calculates scheduling dates for a new SEO draft
 * @param companyId The company ID
 * @param workflowType The workflow type
 * @param scheduledActionService The scheduled action service
 * @returns Object containing scheduled dates
 */
export async function calculateSchedulingDates(
  companyId: string,
  workflowType: WorkflowType,
  scheduledActionService: IScheduledActionService,
  logger: ContextLogger,
): Promise<{
  scheduledToBeSurfacedAt: string;
}> {
  try {
    // Get upcoming actions to find the latest scheduled date
    const upcomingActions = await scheduledActionService.getUpcomingActions(
      companyId,
      workflowType,
    );

    logger.info('Found upcoming actions', {
      upcomingAction: upcomingActions.map(action => {
        return {
          id: action.id,
          scheduledToBeSurfacedAt: action.scheduledToBeSurfacedAt,
          status: action.status,
        };
      }),
    });
    // Determine the base date for scheduling
    let baseDate: Date;
    let isFirstAction = false;
    if (upcomingActions.length === 0) {
      // If no upcoming actions, start from now
      // Create a date in UTC
      baseDate = new Date(new Date().toISOString());
      logger.info(
        'No upcoming actions found, setting first scheduledToBeSurfacedAt to now',
        {
          scheduledToBeSurfacedAt: baseDate.toISOString(),
        },
      );
      isFirstAction = true;
    } else {
      // Find the latest scheduled date from upcoming actions
      baseDate = upcomingActions.reduce((latest, action) => {
        // Use scheduledToBeSurfacedAt if available
        const actionDate = action.scheduledToBeSurfacedAt
          ? new Date(action.scheduledToBeSurfacedAt)
          : new Date(action.createdAt);
        return actionDate > latest ? actionDate : latest;
      }, new Date(0)); // Start with epoch time as initial value
      logger.info('Found upcoming actions, ', {
        baseDate: baseDate.toISOString(),
      });
    }

    // Calculate surfacing date (immediately for first action, otherwise 7 days after base date)
    const surfacedDate = new Date(baseDate);
    if (!isFirstAction) {
      surfacedDate.setDate(surfacedDate.getDate() + 7);
    }

    logger.info('Calculated scheduling dates', {
      companyId,
      workflowType,
      baseDate: baseDate.toISOString(),
      surfacedDate: surfacedDate.toISOString(),
    });

    return {
      scheduledToBeSurfacedAt: surfacedDate.toISOString(),
    };
  } catch (error) {
    logger.error('Error calculating scheduling dates', {
      companyId,
      workflowType,
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

export function groupByCompany<T extends { companyId: string }>(
  items: Array<T>,
): Array<{ companyId: string; items: Array<T> }> {
  const groupMap = new Map<string, T[]>();

  for (const item of items) {
    const existing = groupMap.get(item.companyId);
    if (existing) {
      existing.push(item);
    } else {
      groupMap.set(item.companyId, [item]);
    }
  }

  return Array.from(groupMap.entries()).map(([companyId, items]) => ({
    companyId,
    items,
  }));
}
