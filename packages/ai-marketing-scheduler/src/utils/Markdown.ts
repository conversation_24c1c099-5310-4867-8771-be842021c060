import { decode } from 'html-entities';
import remarkParse from 'remark-parse';
import remarkStringify from 'remark-stringify';
import { unified } from 'unified';
import { visit } from 'unist-util-visit';

import type { Root, Text, Html } from 'mdast';

function stripLinksAndImages() {
  return (tree: Root) => {
    visit(tree, ['link', 'image'], node => {
      if ('url' in node) {
        node.url = '';
      }
    });
  };
}

function cleanTextNodes() {
  return (tree: Root) => {
    visit(tree, ['text', 'html'], node => {
      const textNode = node as Text | Html; // typescript hell
      textNode.value = textNode.value.replace(/\\+/g, ''); // remove extra backslashes
    });
  };
}

export async function cleanMarkdown(markdownInput: string): Promise<string> {
  const processor = unified()
    .use(remarkParse)
    .use(stripLinksAndImages)
    .use(cleanTextNodes)
    .use(remarkStringify, {
      bullet: '-',
    });

  const processedFile = await processor.process(markdownInput);
  return decode(String(processedFile)) // Decode HTML entities
    .replace(/\n{3,}/g, '\n\n') // Replace 3+ newlines with 2 newlines
    .replace(/^[^a-zA-Z0-9]+\n/gm, '') // Remove leading non-alphanumeric characters
    .trim();
}
