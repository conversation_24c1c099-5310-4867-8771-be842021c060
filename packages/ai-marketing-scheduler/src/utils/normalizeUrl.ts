/**
 * Normalize a URL using the native JavaScript URL API
 * This provides similar functionality to normalize-url library
 */
export function normalizeUrl(urlString: string): string {
  if (!urlString || typeof urlString !== 'string') {
    throw new Error('Invalid URL: must be a non-empty string');
  }

  let processedUrl = urlString.trim();

  // Add https:// if no protocol is specified
  if (!/^https?:\/\//i.test(processedUrl)) {
    processedUrl = 'https://' + processedUrl;
  }

  try {
    const url = new URL(processedUrl);

    // Normalize hostname to lowercase
    url.hostname = url.hostname.toLowerCase();

    // Remove www. prefix if present
    if (url.hostname.startsWith('www.')) {
      url.hostname = url.hostname.slice(4);
    }

    // Remove default ports
    if (
      (url.protocol === 'http:' && url.port === '80') ||
      (url.protocol === 'https:' && url.port === '443')
    ) {
      url.port = '';
    }

    // Remove trailing slash from pathname
    if (url.pathname.endsWith('/') && url.pathname.length > 1) {
      url.pathname = url.pathname.slice(0, -1);
    }

    // Sort query parameters alphabetically
    if (url.searchParams.toString()) {
      url.searchParams.sort();
    }

    // Remove hash/fragment
    url.hash = '';

    // Build the final URL string
    let result = url.protocol + '//';

    // Add authentication if present
    if (url.username || url.password) {
      result += url.username;
      if (url.password) {
        result += ':' + url.password;
      }
      result += '@';
    }

    // Add host (hostname:port)
    result += url.host;

    // Only add pathname if it's not just '/'
    if (url.pathname && url.pathname !== '/') {
      result += url.pathname;
    }

    // Add search params if they exist
    if (url.search) {
      result += url.search;
    }

    return result;
  } catch (error) {
    throw new Error(`Invalid URL format: ${error.message}`);
  }
}
