import { SSMClient, GetParameterCommand } from '@aws-sdk/client-ssm';

const ssmClient = new SSMClient({
  region: process.env.AWS_REGION || 'us-east-1',
});

/**
 * Fetches a parameter from AWS SSM Parameter Store
 * @param parameterName - The name of the parameter to fetch
 * @param withDecryption - Whether to decrypt the parameter (default: true)
 * @returns The parameter value
 */
export async function getSSMParameter(
  parameterName: string,
  withDecryption: boolean = true,
): Promise<string> {
  const command = new GetParameterCommand({
    Name: parameterName,
    WithDecryption: withDecryption,
  });

  const response = await ssmClient.send(command);
  return response.Parameter?.Value || '';
}

/**
 * Fetches multiple parameters from AWS SSM Parameter Store
 * @param parameterNames - Array of parameter names to fetch
 * @param withDecryption - Whether to decrypt the parameters (default: true)
 * @returns Object with parameter names as keys and values as values
 */
export async function getSSMParameters(
  parameterNames: string[],
  withDecryption: boolean = true,
): Promise<Record<string, string>> {
  const results: Record<string, string> = {};

  await Promise.all(
    parameterNames.map(async parameterName => {
      const value = await getSSMParameter(parameterName, withDecryption);
      results[parameterName] = value;
    }),
  );

  return results;
}
