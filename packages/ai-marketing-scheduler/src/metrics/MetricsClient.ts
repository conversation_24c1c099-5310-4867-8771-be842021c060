import { Logger } from '@aws-lambda-powertools/logger';
import { sendDistributionMetric } from 'datadog-lambda-js';

export class MetricsClient {
  private logger?: Logger;
  private globalTags: string[];
  private enabled: boolean;

  constructor(logger?: Logger) {
    this.logger = logger;

    // Global tags for all metrics
    // Note: env, service, team, project tags are automatically added by serverless-plugin-datadog
    this.globalTags = [];

    // Enable metrics based on DD_METRICS_ENABLED env var
    this.enabled = process.env.DD_METRICS_ENABLED === 'true';

    if (!this.enabled && this.logger) {
      this.logger.debug(
        'Metrics disabled - DD_METRICS_ENABLED is not set to true',
      );
    }
  }

  /**
   * Record STAR API successful update
   */
  recordStarApiSuccess(
    companyId?: string,
    elementTag?: string,
    actionId?: string,
  ): void {
    if (!this.enabled) return;

    const metricName = 'ai_marketing_scheduler.star_api.success';
    const tags = [...this.globalTags];

    if (companyId) tags.push(`company_id:${companyId}`);
    if (elementTag) tags.push(`element_tag:${elementTag}`);
    if (actionId) tags.push(`action_id:${actionId}`);

    try {
      sendDistributionMetric(metricName, 1, ...tags);
    } catch (error) {
      if (this.logger) {
        this.logger.debug('Failed to send STAR API success metric', {
          metric: metricName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Record STAR API error
   */
  recordStarApiError(
    errorType: string,
    companyId?: string,
    elementTag?: string,
    actionId?: string,
    httpStatus?: number,
  ): void {
    if (!this.enabled) return;

    const metricName = 'ai_marketing_scheduler.star_api.errors';
    const tags = [...this.globalTags, `error_type:${errorType}`];

    if (companyId) tags.push(`company_id:${companyId}`);
    if (elementTag) tags.push(`element_tag:${elementTag}`);
    if (actionId) tags.push(`action_id:${actionId}`);
    if (httpStatus) tags.push(`http_status:${httpStatus}`);

    try {
      sendDistributionMetric(metricName, 1, ...tags);
    } catch (error) {
      if (this.logger) {
        this.logger.debug('Failed to send STAR API error metric', {
          metric: metricName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Record STAR API response time
   */
  recordStarApiDuration(
    durationMs: number,
    companyId?: string,
    elementTag?: string,
    success?: boolean,
  ): void {
    if (!this.enabled) return;

    const metricName = 'ai_marketing_scheduler.star_api.duration';
    const tags = [...this.globalTags];

    if (companyId) tags.push(`company_id:${companyId}`);
    if (elementTag) tags.push(`element_tag:${elementTag}`);
    if (success !== undefined) tags.push(`success:${success}`);

    try {
      sendDistributionMetric(metricName, durationMs, ...tags);
    } catch (error) {
      if (this.logger) {
        this.logger.debug('Failed to send STAR API duration metric', {
          metric: metricName,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  /**
   * Flush any pending metrics and close the client
   * Note: With datadog-lambda-js, metrics are sent immediately
   * This method is kept for compatibility
   */
  async close(): Promise<void> {
    // No-op for datadog-lambda-js as metrics are sent immediately
    // Kept for backward compatibility
    return Promise.resolve();
  }
}

// Singleton instance for Lambda reuse
let metricsClientInstance: MetricsClient | null = null;

export function getMetricsClient(logger?: Logger): MetricsClient {
  if (!metricsClientInstance) {
    metricsClientInstance = new MetricsClient(logger);
  }
  return metricsClientInstance;
}
