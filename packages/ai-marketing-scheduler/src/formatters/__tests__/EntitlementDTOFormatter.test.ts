import { createMockEntitlementDTOs } from '../../utils/testUtils';
import { EntitlementDTOFormatter } from '../EntitlementDTOFormatter';

describe('EntitlementDTOFormatter', () => {
  describe('formatEntitlementsFromFetch', () => {
    it('should format complete entitlement DTOs correctly', () => {
      const entitlementDTOs = createMockEntitlementDTOs(2);

      const result =
        EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlementDTOs);

      expect(result).toHaveLength(2);

      expect(result[0]).toEqual({
        productId: entitlementDTOs[0].productId,
        companyId: entitlementDTOs[0].companyId,
        productName: entitlementDTOs[0].product.name,
        endDate: entitlementDTOs[0].endDate,
        units: entitlementDTOs[0].units,
        websiteUrl: entitlementDTOs[0].company?.website || '',
        displayName: entitlementDTOs[0].company?.name || '',
      });

      expect(result[1]).toEqual({
        productId: entitlementDTOs[1].productId,
        companyId: entitlementDTOs[1].companyId,
        productName: entitlementDTOs[1].product.name,
        endDate: entitlementDTOs[1].endDate,
        units: entitlementDTOs[1].units,
        websiteUrl: entitlementDTOs[1].company?.website || '',
        displayName: entitlementDTOs[1].company?.name || '',
      });
    });

    it('should handle missing company information', () => {
      const entitlementDTOs = createMockEntitlementDTOs(1).map(dto => ({
        ...dto,
        company: undefined,
      }));

      const result =
        EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlementDTOs);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        productId: entitlementDTOs[0].productId,
        companyId: entitlementDTOs[0].companyId,
        productName: entitlementDTOs[0].product.name,
        endDate: entitlementDTOs[0].endDate,
        units: entitlementDTOs[0].units,
        websiteUrl: '',
        displayName: '',
      });
    });

    it('should handle partial company information', () => {
      const entitlementDTOs = createMockEntitlementDTOs(2).map((dto, i) => {
        if (i === 0) {
          return {
            ...dto,
            displayId: 'display-1',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-02T00:00:00Z',
            productId: 'product-123',
            companyId: 'company-456',
            startDate: '2023-01-01T00:00:00Z',
            endDate: null,
            units: 3,
            salesforceServiceId: 'sf-789',
            product: {
              id: 'product-123',
              name: 'SEO Service',
            },
            company: {
              name: 'Partial Company',
            },
          };
        } else {
          return {
            ...dto,
            displayId: 'display-2',
            createdAt: '2023-02-01T00:00:00Z',
            updatedAt: '2023-02-02T00:00:00Z',
            productId: 'product-456',
            companyId: 'company-789',
            startDate: '2023-02-01T00:00:00Z',
            endDate: null,
            units: 7,
            salesforceServiceId: 'sf-101',
            product: {
              id: 'product-456',
              name: 'Marketing Service',
            },
            company: {
              website: 'https://website-only.com',
            },
          };
        }
      });

      const result =
        EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlementDTOs);

      expect(result).toHaveLength(2);

      expect(result[0]).toEqual({
        productId: 'product-123',
        companyId: 'company-456',
        productName: 'SEO Service',
        endDate: null,
        units: 3,
        websiteUrl: '',
        displayName: 'Partial Company',
      });

      expect(result[1]).toEqual({
        productId: 'product-456',
        companyId: 'company-789',
        productName: 'Marketing Service',
        endDate: null,
        units: 7,
        websiteUrl: 'https://website-only.com',
        displayName: '',
      });
    });

    it('should handle empty array', () => {
      const result = EntitlementDTOFormatter.formatEntitlementsFromFetch([]);
      expect(result).toEqual([]);
    });

    it('should format multiple entitlements using test utility', () => {
      const entitlementDTOs = createMockEntitlementDTOs(3);

      const result =
        EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlementDTOs);

      expect(result).toHaveLength(3);
      expect(result[0]).toEqual({
        productId: 'product1',
        companyId: 'company1',
        productName: 'Test Product',
        endDate: entitlementDTOs[0].endDate,
        units: 1,
        websiteUrl: 'test1.com',
        displayName: 'Test Company 1',
      });
    });

    it('should handle missing product information', () => {
      const entitlementDTOs = createMockEntitlementDTOs(1).map(dto => ({
        ...dto,
        displayId: 'display-1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
        productId: 'product-123',
        companyId: 'company-456',
        startDate: '2023-01-01T00:00:00Z',
        endDate: null,
        units: 2,
        salesforceServiceId: 'sf-789',
        product: {
          id: 'product-123',
          name: 'Test Product',
        },
        company: {
          website: 'https://test.com',
          name: 'Test Company',
        },
      }));

      const result =
        EntitlementDTOFormatter.formatEntitlementsFromFetch(entitlementDTOs);

      expect(result[0].productName).toBe('Test Product');
    });
  });
});
