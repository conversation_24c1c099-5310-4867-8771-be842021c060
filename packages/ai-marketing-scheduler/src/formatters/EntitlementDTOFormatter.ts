import { Entitlement, EntitlementDTO } from 'src/types';

export class EntitlementDTOFormatter {
  static formatEntitlementsFromFetch(
    entitlements: EntitlementDTO[],
  ): Entitlement[] {
    return entitlements.map(e => ({
      productId: e.productId,
      companyId: e.companyId,
      productName: e.product?.name,
      endDate: e.endDate,
      units: e.units,
      websiteUrl: e.company?.website || '',
      displayName: e.company?.name || '',
    }));
  }
}
