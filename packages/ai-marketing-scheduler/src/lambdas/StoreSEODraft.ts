import { Context } from 'aws-lambda';

import { RecommendationsRaw } from '../clients/types/seoApiGateway';
import { InvalidInputError } from '../errors/InvalidIInputError';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';
import { ScraperResult, Status, WorkflowType } from '../types';
import { calculateSchedulingDates } from '../utils/scheduleUtils';

/**
 * Input interface for the StoreSEODraft Lambda
 */
interface StoreSEODraftInput {
  companyId: string;
  workflowType: WorkflowType;
  actionId: string;
  recommendations: RecommendationsRaw;
  metadata?: Record<string, unknown>;
}

/**
 * Response interface for the StoreSEODraft Lambda
 */
interface StoreSEODraftResponse {
  success: boolean;
  actionId: string;
  error?: string;
  scheduledToBeSurfacedAt?: string;
}

/**
 * Handler for the StoreSEODraft Lambda
 * Currently only supports SEO recommendations workflow type
 */
export const handler = async (
  event: StoreSEODraftInput,
  context: Context,
): Promise<StoreSEODraftResponse> => {
  const logger = createContextLogger('store-seo-draft', context, {
    lambdaName: 'store-seo-draft',
    actionId: event.actionId,
    companyId: event.companyId,
    workflowType: event.workflowType,
  });

  logger.info('Starting StoreSEODraft', { event });

  try {
    // Validate input
    if (
      !event.companyId ||
      !event.workflowType ||
      !event.recommendations ||
      !event.actionId
    ) {
      throw new InvalidInputError('store-seo-draft', { event });
    }

    // TODO: Add support for other workflow types nad remove this check
    if (event.workflowType !== WorkflowType.SEO) {
      throw new Error('This Lambda only supports SEO workflow type');
    }

    // Get service instances
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    // Get the scheduled action
    const scheduledAction = await scheduledActionService.getAction(
      event.actionId,
    );

    if (!scheduledAction) {
      handleHandlerError(
        new Error('Scheduled action not found'),
        logger,
        'store-seo-draft',
      );
    }

    if (scheduledAction.status !== Status.DRAFT_PENDING) {
      handleHandlerError(
        new Error(
          `Scheduled action ${event.actionId} is not in draft pending status, current status: ${scheduledAction.status}`,
        ),
        logger,
        'store-seo-draft',
      );
    }

    const { scraperResult, keywords, recommendations } =
      scheduledAction.generationPayload as {
        scraperResult?: ScraperResult;
        keywords?: string;
        recommendations?: RecommendationsRaw;
      };

    if (scraperResult) {
      logger.setContext({
        url: scraperResult.url,
        pageType: scraperResult.type,
      });
      logger.info('Processing SEO draft with scraped content', {
        recommendationCount: recommendations
          ? Object.keys(recommendations).length
          : 0,
      });
    }

    // All fields are required in order to store the SEO draft
    if (!scraperResult || !keywords || !recommendations) {
      handleHandlerError(
        new Error('Missing fields in generationPayload.'),
        logger,
        'store-seo-draft',
      );
    }

    // Call mutation to save related entities from generationPayload
    if (scheduledAction.workflowType === WorkflowType.SEO) {
      const savedEntities = await scheduledActionService.storeSEODraft(
        event.companyId,
        event.actionId,
        scraperResult,
        keywords,
        recommendations,
      );

      logger.info('Successfully stored SEO draft');
      // Save the resulting entities to the scheduled action
      await scheduledActionService.update(event.actionId, {
        contentPayload: savedEntities,
      });
    }

    // Calculate scheduling dates
    const { scheduledToBeSurfacedAt } = await calculateSchedulingDates(
      event.companyId,
      event.workflowType,
      scheduledActionService,
      logger,
    );

    // Update the scheduled action
    const result = await scheduledActionService.update(event.actionId, {
      status: Status.SURFACING_PENDING,
      scheduledToBeSurfacedAt,
      failureReason: {},
    });

    logger.info('Successfully updated scheduled action', {
      status: result.status,
      scheduledToBeSurfacedAt,
    });

    return {
      success: true,
      actionId: result.id,
      scheduledToBeSurfacedAt,
    };
  } catch (error) {
    handleHandlerError(error, logger, 'store-draft');
  }
};
