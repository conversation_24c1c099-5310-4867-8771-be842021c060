import { Context } from 'aws-lambda';

import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import {
  FeedFilters,
  FeedItem,
  HomepageFeed,
} from '../clients/types/seoApiGateway';
import { getConfig } from '../config';
import { ITenantService, serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { ContextLogger, createContextLogger } from '../logger/contextLogger';
import { Entitlement } from '../types/entitlement';
import { addDays, subDays } from '../utils/date';
import { LaunchDarkly } from '../utils/launchdarkly';
import { groupByCompany } from '../utils/scheduleUtils';
import { sendSeoAlertMessage } from '../utils/slack';

interface EmailerDaemonResponse {
  processed: number;
  successful: number;
  failed: number;
}

interface CompanyData {
  website: string;
  companyId: string;
  admins: { email: string }[];
  emailContent: HomepageFeed;
}

interface CompanyResult {
  success: boolean;
  data?: CompanyData;
  error?: string;
}

interface EmailResult {
  companyId: string;
  successfulCount: number;
  failedCount: number;
  totalCount: number;
  success: boolean;
  error?: string;
}

interface EmailConfig {
  weeklyDigestTemplateId: string;
  asmGroupId: string;
  bcc: string[];
}

const CONFIG = {
  BATCH_SIZES: {
    COMPANY_DATA: 25,
    EMAIL_PROCESSING: 15,
  },
  DELAYS: {
    COMPANY_DATA: { base: 100, max: 1000 },
    EMAIL_PROCESSING: { base: 200, max: 2000 },
  },
  TIMEOUTS: {
    COMPANY_DATA: 30000,
    EMAIL_SENDING: 45000,
  },
};

const SERHANT_COMPANY_ID = 'b26ab618-2b1e-4a17-8868-498b96b52dc0';

function getTimeoutPromise(timeoutMs: number, errorMessage: string) {
  return new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
  });
}

/**
 * Process an array of items in batches with linear backoff
 * @param items Array of items to process
 * @param batchSize Size of each batch
 * @param processor Function to process each batch
 * @param baseDelayMs Base delay in milliseconds between batches
 * @param maxDelayMs Maximum delay in milliseconds
 * @returns Array of results from all batches
 */
async function processInBatches<T, R>(
  items: T[],
  batchSize: number,
  processor: (batch: T[]) => Promise<R[]>,
  baseDelayMs: number,
  maxDelayMs: number,
): Promise<R[]> {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await processor(batch);
    results.push(...batchResults);

    // Add linear backoff between batches (except for the last batch)
    if (i + batchSize < items.length) {
      const batchNumber = Math.floor(i / batchSize);
      const delay = Math.min(baseDelayMs + batchNumber * 50, maxDelayMs);

      // Wait before processing the next batch
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  return results;
}

/**
 * Fetch company data (admins and email content) with error handling
 */
async function fetchCompanyData(
  company: {
    companyId: string;
    items: Entitlement[];
  },
  tenantService: ITenantService,
  seoApiGatewayClient: ISeoApiGatewayClient,
  filters: FeedFilters,
  logger: ContextLogger,
  emails?: string[],
): Promise<CompanyResult> {
  const companyLogger = logger.createChild({
    companyId: company.companyId,
    website: company.items[0]?.websiteUrl || company.items[0]?.displayName,
    entitlementCount: company.items.length,
    operation: 'fetchCompanyData',
  });
  try {
    companyLogger.info('Starting company data fetch', {
      hasCustomEmails: !!(emails && emails.length > 0),
      filters,
    });

    // Add timeout to prevent hanging operations
    const timeoutPromise = getTimeoutPromise(
      CONFIG.TIMEOUTS.COMPANY_DATA,
      'Company data fetch timed out',
    );

    const [admins, emailContent] = await Promise.race([
      Promise.all([
        emails && emails.length > 0
          ? Promise.resolve(emails.map((email: string) => ({ email })))
          : tenantService.getCompanyAdmins(company.companyId),
        seoApiGatewayClient.getEmailContent(company.companyId, filters),
      ]),
      timeoutPromise,
    ]);

    companyLogger.info('Company data fetched successfully', {
      adminCount: admins.length,
      emailContentItemCount: emailContent?.items?.length || 0,
    });

    return {
      success: true,
      data: {
        website: company.items[0]?.websiteUrl || company.items[0]?.displayName,
        companyId: company.companyId,
        admins,
        emailContent: emailContent || { items: [] },
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    companyLogger.error('Failed to fetch company data', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Validate company data and return validation result
 */
function validateCompanyData(
  data: CompanyData,
  logger: ContextLogger,
): {
  isValid: boolean;
  error?: string;
} {
  logger.debug('Starting company data validation', {
    companyId: data.companyId,
    hasEmailContent: !!data.emailContent,
    emailItemCount: data.emailContent?.items?.length || 0,
    hasWebsite: !!data.website,
    adminCount: data.admins?.length || 0,
  });
  if (!data.emailContent || data.emailContent.items.length === 0) {
    return { isValid: false, error: 'No email content' };
  }

  if (!data.website) {
    return { isValid: false, error: 'No website found' };
  }

  if (!data.admins || data.admins.length === 0) {
    return { isValid: false, error: 'No admins found' };
  }

  return { isValid: true };
}

/**
 * Filter email content items by timestamp for temporal categorization
 */
function categorizeEmailItems(
  emailContent: HomepageFeed,
  logger: ContextLogger,
): {
  pastItems: FeedItem[];
  futureItems: FeedItem[];
} {
  const now = new Date();

  logger.debug('Categorizing email items', {
    now: now.toISOString(),
    totalItems: emailContent.items.length,
  });

  const pastItems = emailContent.items.filter(
    (item: FeedItem) =>
      item.timestamp &&
      item.timestamp.trim() !== '' &&
      new Date(item.timestamp) <= now,
  );

  const futureItems = emailContent.items.filter(
    (item: FeedItem) =>
      item.timestamp &&
      item.timestamp.trim() !== '' &&
      new Date(item.timestamp) > now,
  );

  logger.info('Email items categorized', {
    pastItemCount: pastItems.length,
    futureItemCount: futureItems.length,
    filteredOutCount:
      emailContent.items.length - pastItems.length - futureItems.length,
  });

  return { pastItems, futureItems };
}

/**
 * Send email for a single company
 */
async function sendCompanyEmail(
  data: CompanyData,
  config: EmailConfig,
  seoApiGatewayClient: ISeoApiGatewayClient,
  today: Date,
  logger: ContextLogger,
): Promise<EmailResult> {
  try {
    let adminEmails = data.admins.map(
      (admin: { email: string }) => admin.email,
    );

    // For SERHANT company, only <NAME_EMAIL>
    if (data.companyId === SERHANT_COMPANY_ID) {
      adminEmails = ['<EMAIL>'];
    }

    const domain = data.website.replace(/^https?:\/\//, '').replace(/\/$/, '');
    const { pastItems, futureItems } = categorizeEmailItems(
      data.emailContent,
      logger,
    );

    const templateData = {
      domain: data.website,
      domainNoLink: domain.replace(/\./g, '.&#8203;'),
      today: today.toISOString(),
      todayFormatted: today.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        timeZone: 'America/Los_Angeles',
      }),
      ...data.emailContent,
      pastItems,
      futureItems,
    };

    logger.info('Sending emails to company', {
      templateId: config.weeklyDigestTemplateId,
      adminCount: adminEmails.length,
      bccCount: config.bcc.length,
      pastItemCount: pastItems.length,
      futureItemCount: futureItems.length,
    });

    const recipients = [...adminEmails, ...config.bcc];

    // Create a shared timeout promise that will be used for each individual email send
    const emailTimeoutPromise = getTimeoutPromise(
      CONFIG.TIMEOUTS.EMAIL_SENDING,
      'Email sending timed out',
    );

    // Wrap each recipient send in its own timeout-aware promise
    // This ensures that when timeout occurs, no individual sends continue running
    const recipientPromises = recipients.map(async (recipient, index) => {
      try {
        const emailPromise = seoApiGatewayClient.sendEmail(
          config.weeklyDigestTemplateId,
          data.companyId,
          [recipient],
          templateData,
          'Weekly Digest',
          [],
          config.asmGroupId,
        );

        // Race each individual email send against the shared timeout
        const result = await Promise.race([emailPromise, emailTimeoutPromise]);

        return {
          status: 'fulfilled' as const,
          value: result,
          recipient,
          index,
        };
      } catch (error) {
        // If this is a timeout error, mark it as such
        if (
          error instanceof Error &&
          error.message === 'Email sending timed out'
        ) {
          return {
            status: 'rejected' as const,
            reason: new Error(`Email to ${recipient} timed out`),
            recipient,
            index,
          };
        }

        // Other errors (like API failures)
        return {
          status: 'rejected' as const,
          reason: error,
          recipient,
          index,
        };
      }
    });

    // Wait for all recipient promises to complete (they're already timeout-protected)
    const results = await Promise.allSettled(recipientPromises);

    // Flatten the results since we're now dealing with nested Promise.allSettled
    const flattenedResults = results.map(result => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        // This shouldn't happen since we're handling errors in the map function
        return {
          status: 'rejected' as const,
          reason: result.reason,
          recipient: 'unknown',
          index: -1,
        };
      }
    });

    // Calculate success/failure metrics
    const successful = flattenedResults.filter(
      r => r.status === 'fulfilled',
    ).length;
    const failed = flattenedResults.filter(r => r.status === 'rejected').length;

    logger.info('Email sending completed', {
      domain,
      totalRecipients: recipients.length,
      successful,
      failed,
      pastItemCount: pastItems.length,
      futureItemCount: futureItems.length,
    });

    // Log detailed failure information if any emails failed
    if (failed > 0) {
      const failedEmails = flattenedResults
        .filter(result => result.status === 'rejected')
        .map(result => ({
          email: result.recipient,
          error: result.reason.message,
        }));

      logger.warn('Some emails failed to send', {
        companyId: data.companyId,
        domain,
        failedEmails,
      });
    }

    return {
      companyId: data.companyId,
      success: successful > 0, // Company succeeds if at least one email succeeds
      successfulCount: successful,
      failedCount: failed,
      totalCount: recipients.length,
      error: successful === 0 ? 'All emails failed to send' : undefined,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error('Error sending email to company', {
      companyId: data.companyId,
      adminCount: data.admins?.length || 0,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      companyId: data.companyId,
      success: false,
      successfulCount: 0,
      failedCount: data.admins?.length || 0,
      totalCount: data.admins?.length || 0,
      error: errorMessage,
    };
  }
}

/**
 * Process a single company for email sending
 */
async function processCompanyEmail(
  companyResult: CompanyResult,
  config: EmailConfig,
  seoApiGatewayClient: ISeoApiGatewayClient,
  today: Date,
  logger: ContextLogger,
): Promise<EmailResult> {
  const companyLogger = logger.createChild({
    companyId: companyResult.data?.companyId || 'unknown',
    website: companyResult.data?.website || 'unknown',
  });

  // Handle data fetching errors
  if (!companyResult.success || !companyResult.data) {
    companyLogger.warn('Company had error during data fetching', {
      error: companyResult.error,
    });
    return {
      companyId: 'unknown',
      success: false,
      successfulCount: 0,
      failedCount: 1,
      totalCount: 0,
      error: companyResult.error,
    };
  }

  // Validate company data
  const validation = validateCompanyData(companyResult.data, companyLogger);
  if (!validation.isValid) {
    companyLogger.warn(`Company validation failed: ${validation.error}`);
    sendSeoAlertMessage('EmailerDaemon failed on error', [
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Company Id:* ${companyResult.data.companyId} \n *Company Name:* ${companyResult.data.website} \n *Error:* ${validation.error}`,
        },
      },
    ]);
    return {
      companyId: companyResult.data.companyId,
      success: false,
      successfulCount: 0,
      failedCount: 1,
      totalCount: 0,
      error: validation.error,
    };
  }

  // Send email
  return await sendCompanyEmail(
    companyResult.data,
    config,
    seoApiGatewayClient,
    today,
    companyLogger,
  );
}

/**
 * Filter entitlements based on LaunchDarkly feature flags
 */
async function filterEntitlementsByFeatureFlag(
  entitlements: Entitlement[],
  logger: ContextLogger,
): Promise<Entitlement[]> {
  const ld = LaunchDarkly.getInstance();

  try {
    const companyIds = entitlements.map(entitlement => entitlement.companyId);
    const flagResults = await ld.checkVariationBatch(
      'enable-client-marketing-2-0',
      false,
      companyIds,
    );

    const supportedEntitlements = entitlements.filter(
      entitlement =>
        flagResults.get(entitlement.companyId) === true ||
        entitlement.companyId === SERHANT_COMPANY_ID,
    );

    logger.info('Flagged companies', {
      totalEntitlements: entitlements.length,
      supportedEntitlements: supportedEntitlements.map(
        entitlement => entitlement.companyId,
      ),
      supportedEntitlementsCount: supportedEntitlements.length,
    });

    return supportedEntitlements;
  } catch (error) {
    logger.error('Failed to check LaunchDarkly flags', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  } finally {
    ld.closeConnection();
  }
}

export const handler = async (
  event: Record<string, unknown>,
  context: Context,
): Promise<EmailerDaemonResponse> => {
  const logger = createContextLogger('emailer-daemon', context, {
    lambdaName: 'emailer-daemon',
  });

  const config = getConfig();
  const emailConfig: EmailConfig = {
    weeklyDigestTemplateId: config.sendgrid.weeklyDigestTemplateId,
    asmGroupId: config.sendgrid.asmGroupId,
    bcc: config.sendgrid.bccEmails,
  };

  logger.info('Starting emailer daemon', {
    event,
    timeoutLimit: `${Math.floor(context.getRemainingTimeInMillis() / 1000)} seconds`,
  });

  const { companyIds, emails } = event as {
    companyIds?: string[];
    emails?: string[];
  };

  // Validate configuration
  if (!emailConfig.weeklyDigestTemplateId) {
    logger.error('Weekly digest template ID is not configured');
    throw new Error('Weekly digest template ID is not configured');
  }

  const today = new Date();
  const filters: FeedFilters = {
    timestampStart: subDays(today, 7).toISOString(),
    timestampEnd: addDays(today, 7).toISOString(),
  };

  try {
    const tenantService = serviceFactory.createTenantService(logger);
    const seoApiGatewayClient =
      serviceFactory.createSeoApiGatewayClient(logger);

    // Fetch and filter entitlements
    logger.info('Fetching entitlements');
    let entitlements = await tenantService.getEntitlements();

    if (companyIds && companyIds.length > 0) {
      logger.info('Filtering entitlements by provided companyIds', {
        companyIds,
      });
      entitlements = entitlements.filter(entitlement =>
        companyIds.includes(entitlement.companyId),
      );
    }

    if (entitlements.length === 0) {
      logger.warn('No entitlements to process');
      return { processed: 0, successful: 0, failed: 0 };
    }

    logger.info('Retrieved entitlements', { count: entitlements.length });

    // Filter by feature flags
    const supportedEntitlements = await filterEntitlementsByFeatureFlag(
      entitlements,
      logger,
    );
    const entitlementsByCompany = groupByCompany(supportedEntitlements);

    logger.info('Processing companies in batches', {
      totalCompanies: entitlementsByCompany.length,
      batchSize: CONFIG.BATCH_SIZES.COMPANY_DATA,
      baseDelayMs: CONFIG.DELAYS.COMPANY_DATA.base,
      maxDelayMs: CONFIG.DELAYS.COMPANY_DATA.max,
    });

    // Fetch company data in batches
    const companyResults = await processInBatches(
      entitlementsByCompany,
      CONFIG.BATCH_SIZES.COMPANY_DATA,
      async companyBatch => {
        return Promise.all(
          companyBatch.map(company =>
            fetchCompanyData(
              company,
              tenantService,
              seoApiGatewayClient,
              filters,
              logger,
              emails,
            ),
          ),
        );
      },
      CONFIG.DELAYS.COMPANY_DATA.base,
      CONFIG.DELAYS.COMPANY_DATA.max,
    );

    logger.info('Fetched email content', {
      dataByCompanyCount: companyResults.length,
    });

    // Process emails
    logger.info('Starting email sending process', {
      totalCompanies: companyResults.length,
    });

    const emailResults = await processInBatches(
      companyResults,
      CONFIG.BATCH_SIZES.EMAIL_PROCESSING,
      async emailBatch => {
        return Promise.all(
          emailBatch.map(companyResult =>
            processCompanyEmail(
              companyResult,
              emailConfig,
              seoApiGatewayClient,
              today,
              logger,
            ),
          ),
        );
      },
      CONFIG.DELAYS.EMAIL_PROCESSING.base,
      CONFIG.DELAYS.EMAIL_PROCESSING.max,
    );

    logger.info('Emailer daemon completed successfully', {
      processed: companyResults.length,
      successful: emailResults.filter(result => result.success).length,
      failed: emailResults.filter(result => !result.success).length,
      remainingTimeMs: context.getRemainingTimeInMillis(),
    });

    return {
      processed: companyResults.length,
      successful: emailResults.filter(result => result.success).length,
      failed: emailResults.filter(result => !result.success).length,
    };
  } catch (error) {
    logger.error('Emailer daemon failed with unexpected error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    handleHandlerError(error, logger, 'emailer-daemon');

    return { processed: 0, successful: 0, failed: 0 };
  }
};
