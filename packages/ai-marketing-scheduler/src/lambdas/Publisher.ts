import { Context } from 'aws-lambda';

import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import {
  StoreSEODraftResponse,
  StoredRecommendation,
} from '../clients/types/seoApiGateway';
import { PublisherError, TransientPublisherError } from '../errors';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger, ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { IStarApiService } from '../services/interfaces/IStarApiService';
import {
  WorkflowType,
  ScraperResult,
  Status,
  ScheduledAction,
  RecommendationType,
} from '../types';
import { isTransientError } from '../utils/errorUtils';
import {
  UpdateResult,
  mapUpdatesToRecommendationTypes,
  buildFailureReasonFromUpdates,
  isPendingRecommendation,
} from '../utils/publisherUtils';

/**
 * Interface for the publisher lambda event
 */
export interface PublisherEvent {
  actionId: string;
  companyId: string;
  workflowType: WorkflowType;
}

/**
 * Interface for the publisher lambda response
 */
interface PublisherResponse {
  actionId: string;
  success: boolean;
  error?: string;
  updates?: UpdateResult[];
}

/**
 * Validates the input event
 */
function validateEvent(event: PublisherEvent): void {
  const { actionId, companyId, workflowType } = event;

  if (!actionId || typeof actionId !== 'string') {
    throw new PublisherError('Action ID is required');
  }

  if (!companyId || typeof companyId !== 'string') {
    throw new PublisherError('Company ID is required');
  }

  if (!workflowType) {
    throw new PublisherError('Workflow type is required');
  }
}

/**
 * Fetches and validates the scheduled action
 */
async function fetchAndValidateAction(
  actionId: string,
  scheduledActionService: IScheduledActionService,
): Promise<ScheduledAction> {
  const action = await scheduledActionService.getAction(actionId);

  if (!action) {
    throw new PublisherError('Action not found', undefined, { actionId });
  }

  if (action.status !== Status.SURFACED) {
    throw new PublisherError(
      `Action is not in SURFACED status. Current status: ${action.status}`,
      undefined,
      { actionId, status: action.status },
    );
  }

  return action;
}

/**
 * Extracts and validates payloads from the action
 */
function extractPayloads(
  action: ScheduledAction,
  actionId: string,
): { scraperResult: ScraperResult; contentPayload: StoreSEODraftResponse } {
  const scraperResult = action.generationPayload?.scraperResult as
    | ScraperResult
    | undefined;

  if (!scraperResult) {
    throw new PublisherError('No scraper result found in action', undefined, {
      actionId,
    });
  }

  const contentPayload = action.contentPayload as StoreSEODraftResponse | null;

  if (!contentPayload) {
    throw new PublisherError('No content payload found in action', undefined, {
      actionId,
    });
  }

  return { scraperResult, contentPayload };
}

/**
 * Fetches PENDING recommendations from the API and returns them mapped by type
 */
async function fetchPendingRecommendations(
  contentPayloadRecommendations: {
    id: string;
    type: RecommendationType;
    groupId: string;
    scrapeId: string;
  }[],
  seoApiGatewayClient: ISeoApiGatewayClient,
  logger: ContextLogger,
): Promise<Map<RecommendationType, StoredRecommendation>> {
  // Create a child logger for this specific operation
  const operationLogger = logger.createChild({
    operation: 'fetchPendingRecommendations',
    recommendationCount: contentPayloadRecommendations.length,
  });

  const pendingRecommendationsByType = new Map<
    RecommendationType,
    StoredRecommendation
  >();

  for (const contentPayloadRecommendation of contentPayloadRecommendations) {
    try {
      const recommendation = await seoApiGatewayClient.getRecommendationById(
        contentPayloadRecommendation.id,
      );

      if (isPendingRecommendation(recommendation)) {
        pendingRecommendationsByType.set(recommendation.type, recommendation);
      }
    } catch (error) {
      operationLogger.error('Failed to fetch recommendation, skipping', {
        recommendationId: contentPayloadRecommendation.id,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
  operationLogger.info('Recommendations eligible for publishing', {
    recommendationIds: Array.from(pendingRecommendationsByType.values()).map(
      r => r.id,
    ),
  });

  return pendingRecommendationsByType;
}

/**
 * Performs all necessary updates via STAR API
 */
async function performSTARUpdates(
  scraperResult: ScraperResult,
  pendingRecommendationsByType: Map<RecommendationType, StoredRecommendation>,
  starApiService: IStarApiService,
  companyId?: string,
  actionId?: string,
): Promise<UpdateResult[]> {
  const updates: UpdateResult[] = [];

  // Get recommendations for each type directly from the map
  const metaTitleRecommendation = pendingRecommendationsByType.get(
    RecommendationType.META_TITLE,
  );
  const metaDescriptionRecommendation = pendingRecommendationsByType.get(
    RecommendationType.META_DESCRIPTION,
  );
  const mainHeadingRecommendation = pendingRecommendationsByType.get(
    RecommendationType.MAIN_HEADING,
  );

  const { url } = scraperResult;
  // Update meta title if present and original content exists
  if (metaTitleRecommendation && scraperResult.metaTitle !== undefined) {
    const updateResult = await starApiService.updateMetaTitle(
      url,
      metaTitleRecommendation.currentValue || '',
      metaTitleRecommendation.recommendationValue,
      companyId,
      actionId,
    );
    updates.push(updateResult);
  }

  // Update meta description if present and original content exists
  if (
    metaDescriptionRecommendation &&
    scraperResult.metaDescription !== undefined
  ) {
    const updateResult = await starApiService.updateMetaDescription(
      url,
      metaDescriptionRecommendation.currentValue || '',
      metaDescriptionRecommendation.recommendationValue,
      companyId,
      actionId,
    );
    updates.push(updateResult);
  }

  // Update main heading if present and original content exists
  if (mainHeadingRecommendation && scraperResult.mainHeading) {
    const updateResult = await starApiService.updateMainHeading(
      url,
      scraperResult.mainHeading.tag,
      scraperResult.mainHeading.index,
      mainHeadingRecommendation.currentValue || '',
      mainHeadingRecommendation.recommendationValue,
      companyId,
      actionId,
      scraperResult.mainHeading.section_id,
    );
    updates.push(updateResult);
  }

  return updates;
}

/**
 * Updates PENDING recommendation statuses to APPLIED based on successful updates
 */
async function updateRecommendationStatuses(
  pendingRecommendationsByType: Map<RecommendationType, StoredRecommendation>,
  successfulUpdates: UpdateResult[],
  seoApiGatewayClient: ISeoApiGatewayClient,
  logger: ContextLogger,
): Promise<void> {
  if (pendingRecommendationsByType.size === 0) return;

  const successfullyPublishedTypes =
    mapUpdatesToRecommendationTypes(successfulUpdates);

  // Update only PENDING recommendations that were successfully published
  const recommendationsToMarkApplied: StoredRecommendation[] = [];
  for (const [type, recommendation] of pendingRecommendationsByType) {
    if (successfullyPublishedTypes.has(type)) {
      recommendationsToMarkApplied.push(recommendation);
    }
  }

  logger.info(
    'Updating successfully published recommendation statuses to APPLIED',
    {
      recommendationsToApply: recommendationsToMarkApplied.map(rec => ({
        recommendationId: rec.id,
        type: rec.type,
      })),
    },
  );

  await Promise.all(
    recommendationsToMarkApplied.map(async recommendation => {
      try {
        await seoApiGatewayClient.applyRecommendation(recommendation.id);
      } catch (error) {
        logger.error('Failed to apply recommendation', {
          recommendationId: recommendation.id,
          type: recommendation.type,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }),
  );
}

/**
 * Processes publishing results and updates recommendation statuses for successfully published items
 */
async function processPublishingResults(
  updates: UpdateResult[],
  actionId: string,
  scheduledActionService: IScheduledActionService,
  logger: ContextLogger,
  seoApiGatewayClient: ISeoApiGatewayClient,
  pendingRecommendationsByType: Map<RecommendationType, StoredRecommendation>,
): Promise<boolean> {
  const successfulUpdates = updates.filter(update => update.success);
  const failedUpdates = updates.filter(update => !update.success);

  if (updates.length === 0) {
    throw new PublisherError(
      'No updates to apply - missing required content',
      undefined,
      { actionId },
    );
  }

  // Mark successfully published PENDING recommendations as APPLIED
  // This happens before checking for failures to ensure successful updates are marked
  await updateRecommendationStatuses(
    pendingRecommendationsByType,
    successfulUpdates,
    seoApiGatewayClient,
    logger,
  );

  if (failedUpdates.length > 0) {
    const failureReason = buildFailureReasonFromUpdates(failedUpdates);

    const hasTransientError = failedUpdates.some(update => {
      if (update.statusCode === undefined) {
        return true;
      }
      return isTransientError(update.statusCode);
    });

    const errorContext = {
      actionId,
      failureReason,
      successfulCount: successfulUpdates.length,
      failedCount: failedUpdates.length,
    };

    // If all updates failed, throw appropriate error type
    if (successfulUpdates.length === 0) {
      const errorMessage = `All STAR updates failed: ${JSON.stringify(failureReason)}`;

      if (hasTransientError) {
        throw new TransientPublisherError(
          errorMessage,
          undefined,
          errorContext,
        );
      } else {
        throw new PublisherError(errorMessage, undefined, errorContext);
      }
    }

    // Partial success: some updates succeeded, some failed
    logger.info('Partial STAR publishing success', {
      successfulCount: successfulUpdates.length,
      failedCount: failedUpdates.length,
      failureReason,
    });

    // Throw error to indicate partial failure (action will be marked as FAILED)
    // but successful recommendations have already been marked as APPLIED
    const errorMessage = `Partial STAR publishing failure: ${successfulUpdates.length} succeeded, ${failedUpdates.length} failed`;

    if (hasTransientError) {
      throw new TransientPublisherError(errorMessage, undefined, errorContext);
    } else {
      throw new PublisherError(errorMessage, undefined, errorContext);
    }
  }

  // All updates succeeded
  await scheduledActionService.update(actionId, {
    status: Status.PUBLISHED,
    publishedAt: new Date().toISOString(),
  });

  logger.info('All STAR updates successful', { count: updates.length });

  return true;
}

/**
 * Main handler function for the publisher lambda
 */
export const handler = async (
  event: PublisherEvent,
  context: Context,
): Promise<PublisherResponse> => {
  const { actionId, companyId, workflowType } = event;

  const logger = createContextLogger('publisher', context, {
    lambdaName: 'publisher',
    actionId,
    companyId,
    workflowType,
  });

  logger.info('Starting publisher', { event });

  try {
    // Validate input
    validateEvent(event);

    // Initialize services with context logger
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);
    const starApiService = serviceFactory.createStarApiService(logger);
    const seoApiGatewayClient =
      serviceFactory.createSeoApiGatewayClient(logger);

    // Fetch and validate action
    const action = await fetchAndValidateAction(
      actionId,
      scheduledActionService,
    );

    // Extract payloads
    const { scraperResult, contentPayload } = extractPayloads(action, actionId);

    // Add groupId to context if available
    if (
      action.groupScheduledActions &&
      action.groupScheduledActions.length > 0
    ) {
      logger.setContext({
        groupId: action.groupScheduledActions[0].groupId,
      });
    }

    // Add URL to context if available
    if (scraperResult.url) {
      logger.setContext({
        url: scraperResult.url,
      });
    }

    // Fetch latest recommendations with the status `PENDING`
    const pendingRecommendationsByType = await fetchPendingRecommendations(
      contentPayload.savedRecommendations || [],
      seoApiGatewayClient,
      logger,
    );

    // Perform updates
    const updates = await performSTARUpdates(
      scraperResult,
      pendingRecommendationsByType,
      starApiService,
      companyId,
      actionId,
    );

    // Process results and update recommendation statuses
    const allSuccessful = await processPublishingResults(
      updates,
      actionId,
      scheduledActionService,
      logger,
      seoApiGatewayClient,
      pendingRecommendationsByType,
    );

    return {
      actionId,
      success: allSuccessful,
      updates,
    };
  } catch (error) {
    return handleHandlerError(error, logger, 'publisher');
  }
};
