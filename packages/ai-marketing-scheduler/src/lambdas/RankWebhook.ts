import util from 'util';
import { gzip } from 'zlib';

import { Context, APIGatewayProxyEvent } from 'aws-lambda';

import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import { S3Client } from '../clients/S3Client';
import { getConfig } from '../config';
import { serviceFactory } from '../factories';
import { createContextLogger, ContextLogger } from '../logger/contextLogger';
import { RankingUrlTask, RankingUrlTasks } from '../types/dataForSeo';
import { normalizeUrl } from '../utils/normalizeUrl';
import { normalizeUrlForComparison } from '../utils/pageUtils';

const gzipAsync = util.promisify(gzip);

async function findPageRank(
  logger: ContextLogger,
  task: RankingUrlTask,
  seoApiGatewayClient: ISeoApiGatewayClient,
) {
  const { keyword, tag: pageId } = task.data;
  logger.info('Finding page rank');
  if (task.result.length !== 1) {
    logger.error(
      `Expected exactly one result in task ${task.id}, but got ${task.result.length}`,
      { task },
    );
    throw new Error(
      `Expected exactly one result in task ${task.id}, but got ${task.result.length}`,
    );
  }
  const organicItems = task.result[0].items.filter(
    item => item.type === 'organic',
  );

  const page = await seoApiGatewayClient.getPageById(pageId);
  if (!page) {
    logger.error(`Page ${pageId} not found`, { pageId });
    throw new Error(`Page ${pageId} not found`);
  }

  let normalizedPageUrl: URL;
  try {
    normalizedPageUrl = new URL(
      normalizeUrlForComparison(normalizeUrl(page.url)),
    );
    normalizedPageUrl.search = '';
    normalizedPageUrl.hash = '';
  } catch {
    logger.error(`Invalid page URL: ${page.url}`, { pageUrl: page.url });
    throw new Error(`Invalid page URL: ${page.url}`);
  }

  const match = organicItems.find(item => {
    try {
      const itemUrl = new URL(
        normalizeUrlForComparison(normalizeUrl(item.url)),
      );
      itemUrl.search = '';
      itemUrl.hash = '';
      return itemUrl.href === normalizedPageUrl.href;
    } catch {
      return false;
    }
  });
  logger.info('Found page rank', { rank: match?.rank_group });

  // Find the page-keyword relationship for this keyword
  const pageKeyword = page.pageKeywords?.find(
    pk => pk.keyword.keyword === keyword,
  );

  return {
    keyword,
    pageId,
    pageKeywordId: pageKeyword?.id,
    datetime: task.result[0].datetime,
    rank: match?.rank_group ?? -1,
    url: page.url,
    companyId: page.companyId,
  };
}

async function saveTask(
  logger: ContextLogger,
  task: RankingUrlTask,
  s3Client: S3Client,
  bucketName: string,
) {
  const now = new Date();
  // Use Hive-style partitioning
  const folders = [
    'rankings',
    `year=${now.getUTCFullYear()}`,
    `month=${String(now.getUTCMonth() + 1).padStart(2, '0')}`,
    `day=${String(now.getUTCDate()).padStart(2, '0')}`,
  ];

  const key = `${folders.join('/')}/${encodeURIComponent(task.data.keyword)}.json.gz`;
  logger.info('Saving task to S3', { key });
  const gzippedBody = await gzipAsync(
    Buffer.from(JSON.stringify(task, null, 2)),
  );
  await s3Client.uploadObject(bucketName, key, gzippedBody);
  logger.info('Task saved to S3');
}

async function updatePageKeywordRank(
  logger: ContextLogger,
  seoApiGatewayClient: ISeoApiGatewayClient,
  pageKeywordId: string | undefined,
  currentRank: number,
) {
  if (!pageKeywordId) {
    logger.warn('No page-keyword ID found, skipping rank update');
    return;
  }

  try {
    await seoApiGatewayClient.updatePageKeywordRank(pageKeywordId, currentRank);
    logger.info('Successfully updated page-keyword rank', {
      pageKeywordId,
      currentRank,
    });
  } catch (error) {
    logger.error('Failed to update page-keyword rank', {
      pageKeywordId,
      currentRank,
      error,
    });
    // Don't throw - we want to continue processing other tasks
  }
}

export async function handler(event: APIGatewayProxyEvent, context: Context) {
  const logger = createContextLogger('rank-webhook', context, {
    lambdaName: 'rank-webhook',
  });
  logger.info('Received rank webhook event', { event });

  try {
    const seoApiGatewayClient =
      serviceFactory.createSeoApiGatewayClient(logger);
    const s3Config = getConfig().apiResponseS3;
    const s3Client = new S3Client(s3Config, logger);

    const body = (
      typeof event.body === 'string' ? JSON.parse(event.body) : event.body
    ) as RankingUrlTasks;

    const tasks = body.tasks ?? [];

    for (const task of tasks) {
      const taskLogger = logger.createChild({
        taskId: task.id,
        keyword: task.data.keyword,
        pageId: task.data.tag,
      });

      try {
        await saveTask(taskLogger, task, s3Client, s3Config.bucketName);
      } catch (error) {
        taskLogger.warn(
          'Failed to save task in S3, continuing with processing',
          {
            error,
          },
        );
      }

      const ranking = await findPageRank(taskLogger, task, seoApiGatewayClient);
      taskLogger.info('Page ranking', { rank: ranking.rank });

      taskLogger.info('Updating page-keyword rank', {
        pageKeywordId: ranking.pageKeywordId,
        rank: ranking.rank,
      });
      // Update the page-keyword rank if we have a page-keyword ID
      await updatePageKeywordRank(
        taskLogger,
        seoApiGatewayClient,
        ranking.pageKeywordId,
        ranking.rank,
      );
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Success',
        processed: tasks.length,
      }),
    };
  } catch (error) {
    logger.error('Error processing rank webhook', { error });
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
    };
  }
}
