import { Context } from 'aws-lambda';

// Import service factories
import { datadogMetrics, METRICS } from '../config/datadog';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';
import { chunkEntitlements } from '../services/EntitlementService';
import { Entitlement } from '../types';

/**
 * Main handler function for the entitlement fetcher
 * Retrieves entitlements and chunks them for processing
 */
export const handler = async (
  event: Record<string, unknown> = {},
  context: Context,
): Promise<{ chunkedEntitlements: Entitlement[][] }> => {
  const logger = createContextLogger('entitlement-fetcher', context, {
    lambdaName: 'entitlement-fetcher',
  });
  logger.info('Starting entitlement fetcher', { event });

  try {
    // Create tenant service using the unified service factory
    const tenantService = serviceFactory.createTenantService(logger);
    const apiGatewayClient = serviceFactory.createApiGatewayClient(logger);

    // Fetch entitlements with timing
    const entitlementsStart = Date.now();
    const entitlements = await tenantService.getEntitlements();
    const entitlementsFetchTime = Date.now() - entitlementsStart;

    // Send business metrics
    await Promise.all([
      datadogMetrics.sendTimingMetric(
        METRICS.ENTITLEMENTS_FETCH_TIME,
        entitlementsFetchTime,
        'entitlement-fetcher',
      ),
      datadogMetrics.sendCountMetric(
        METRICS.ENTITLEMENTS_COUNT,
        entitlements.length,
        'tenant_service',
      ),
    ]);

    if (entitlements.length === 0) {
      logger.info('No entitlements to process');
      return { chunkedEntitlements: [] };
    }

    // Fetch websites with timing
    const websitesStart = Date.now();
    const entitlementsWithWebsites =
      await apiGatewayClient.fetchAllWebsites(entitlements);
    const websitesFetchTime = Date.now() - websitesStart;

    // Send website fetch timing
    await datadogMetrics.sendTimingMetric(
      METRICS.WEBSITES_FETCH_TIME,
      websitesFetchTime,
      'entitlement-fetcher',
    );

    logger.info('Fetched entitlements', {
      originalCount: entitlements.length,
      withWebsitesCount: entitlementsWithWebsites.length,
      companyIds: entitlementsWithWebsites.map(e => e.companyId),
    });

    if (entitlementsWithWebsites.length === 0) {
      logger.info('No entitlements with websites - early return');
      return { chunkedEntitlements: [] };
    }

    // Chunk entitlements by default batch size
    const chunkedEntitlements = chunkEntitlements(entitlementsWithWebsites);

    // Send final business metrics
    await datadogMetrics.sendMetrics([
      {
        name: METRICS.ENTITLEMENTS_PROCESSED,
        value: entitlementsWithWebsites.length,
        type: 'count',
        tags: ['component:entitlement_processor'],
      },
      {
        name: METRICS.CHUNKS_CREATED,
        value: chunkedEntitlements.length,
        type: 'count',
        tags: ['component:chunk_processor'],
      },
    ]);

    logger.info('Chunked entitlements', {
      numChunks: chunkedEntitlements.length,
      totalEntitlements: entitlementsWithWebsites.length,
    });

    return { chunkedEntitlements };
  } catch (error: unknown) {
    return handleHandlerError(error, logger, 'entitlement-fetcher');
  }
};
