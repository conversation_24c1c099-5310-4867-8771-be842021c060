import { Context } from 'aws-lambda';

import { FireCrawlError } from '../clients/errors/FireCrawlError';
import { FirecrawlClient } from '../clients/FirecrawlClient';
import { SnowflakeClient } from '../clients/SnowflakeClient';
import { getConfig, getSnowflakeConfigAsync } from '../config';
import { UrlSelectorError } from '../errors/UrlSelectorError';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';
import { normalizeUrl } from '../utils/normalizeUrl';
import {
  getPageType,
  extractDomainFromUrl,
  normalizeUrlForComparison,
} from '../utils/pageUtils';

const EXPECTED_RESULTS = ['selectedUrl'];

interface UrlSelectorEvent {
  actionId: string;
  companyId: string;
  generationPayload: {
    websiteUrl: string;
    [key: string]: unknown;
  };
}

interface UrlSelectorResponse {
  error?: string;
  actionId: string;
  companyId: string;
  generationPayload: {
    websiteUrl: string;
    selectedUrl?: string;
    [key: string]: unknown;
  };
}

type EnrichedUrl = {
  url: string;
  totalImpressions: number;
  rank: number;
};

type SnowflakeQueryResult = {
  PAGE_URL: string;
  TOTAL_IMPRESSIONS: number;
  AVERAGE_SEARCH_POSITION: number;
};

const enrichUrlsWithSnowflake = async (
  eligibleUrls: string[],
  websiteUrl: string,
  logger: ReturnType<typeof createContextLogger>,
): Promise<EnrichedUrl[]> => {
  try {
    logger.info('Starting Snowflake configuration retrieval', {
      eligibleUrlsCount: eligibleUrls.length,
      websiteUrl,
    });

    let snowflakeConfig;
    try {
      snowflakeConfig = await getSnowflakeConfigAsync();
      logger.info('Successfully retrieved Snowflake configuration', {
        configKeys: snowflakeConfig ? Object.keys(snowflakeConfig) : [],
        hasAccount: !!snowflakeConfig?.account,
        hasUsername: !!snowflakeConfig?.username,
        hasDatabase: !!snowflakeConfig?.database,
        hasPrivateKey: !!snowflakeConfig?.privateKey,
      });
    } catch (configError) {
      logger.error('Failed to retrieve Snowflake configuration', {
        error:
          configError instanceof Error
            ? configError.message
            : String(configError),
        stack: configError instanceof Error ? configError.stack : undefined,
        websiteUrl,
      });
      throw configError;
    }

    logger.info('Creating Snowflake client');
    const snowflakeClient = new SnowflakeClient(snowflakeConfig, logger);

    const domain = extractDomainFromUrl(websiteUrl);
    logger.info('Extracted domain for Snowflake query', { domain });

    const query = `
      SELECT page_url, total_impressions, average_search_position
      FROM analytics.mart_external_models.m_exm_google_search_recent_traffic_by_page 
      WHERE property_domain_name = ?
      AND page_url NOT LIKE CONCAT('https://', ?, '/home-search/%')
      ORDER BY total_impressions DESC
    `;

    logger.info('Executing Snowflake query', {
      query: query.trim(),
      parameters: [domain, domain],
    });

    let results: SnowflakeQueryResult[];
    try {
      results = await snowflakeClient.queryWithRetries(query, [domain, domain]);
      logger.info('Snowflake query executed successfully', {
        resultCount: results.length,
        firstFewResults: results.slice(0, 3).map((r: any) => ({
          pageUrl: r.PAGE_URL,
          totalImpressions: r.TOTAL_IMPRESSIONS,
        })),
      });
    } catch (queryError) {
      logger.error('Failed to execute Snowflake query', {
        error:
          queryError instanceof Error ? queryError.message : String(queryError),
        stack: queryError instanceof Error ? queryError.stack : undefined,
        domain,
        query: query.trim(),
      });
      throw queryError;
    }

    if (results.length === 0) {
      logger.warn('No URL results found in Snowflake for domain', { domain });
    }

    let matchedUrlCount = 0;
    const enrichedUrlList = eligibleUrls.map(url => {
      const matchedResult = results.find(
        (result: SnowflakeQueryResult) => result.PAGE_URL === url,
      );
      if (matchedResult) {
        matchedUrlCount++;
      }
      return {
        url,
        totalImpressions: matchedResult?.TOTAL_IMPRESSIONS || -1,
        rank: matchedResult?.AVERAGE_SEARCH_POSITION || -1,
      };
    });

    logger.info('Matched URLs found in Snowflake', {
      matchedUrlCount,
      eligibleUrlsCount: eligibleUrls.length,
    });

    return enrichedUrlList;
  } catch (error) {
    logger.error('Failed to query Snowflake for URL selection', {
      error: error instanceof Error ? error.message : String(error),
      domain: extractDomainFromUrl(websiteUrl),
    });
    return eligibleUrls.map((url: string) => ({
      url,
      totalImpressions: -1,
      rank: -1,
    }));
  }
};

const selectUrl = async (
  pages: string[],
  websiteUrl: string,
  logger: ReturnType<typeof createContextLogger>,
): Promise<string | null> => {
  // Try to get the URL with the most impressions from Snowflake
  const enrichedUrlList = await enrichUrlsWithSnowflake(
    pages,
    websiteUrl,
    logger,
  );

  // filter out urls with rank 1, keep the negative(not found) ranks
  const filteredUrlList = enrichedUrlList.filter(
    elem => elem.rank > 1 || elem.rank < 0,
  );

  if (filteredUrlList.length < enrichedUrlList.length) {
    logger.info('Filtered out URLs with rank 1', {
      filteredCount: enrichedUrlList.length - filteredUrlList.length,
    });
  }

  if (filteredUrlList.length === 0) {
    const allRank1 = enrichedUrlList.every(url => url.rank === 1);
    if (allRank1 && enrichedUrlList.length > 0) {
      logger.warn(
        'All matched URLs in Snowflake have rank 1 - cannot select any URL',
        {
          totalUrls: enrichedUrlList.length,
          urlsWithRank1: enrichedUrlList.filter(url => url.rank === 1).length,
        },
      );
    } else {
      logger.warn('No URLs found in Snowflake with rank > 1');
    }
    return null;
  }

  // set a random sort index for the remaining urls
  const urlListWithSortIndex = filteredUrlList.map(
    (enrichedUrl: EnrichedUrl) => {
      return {
        ...enrichedUrl,
        sortIndex:
          enrichedUrl.totalImpressions >= 0
            ? enrichedUrl.totalImpressions
            : Math.random() - 1,
      };
    },
  );

  const sortedUrlList = urlListWithSortIndex.sort(
    (a, b) => b.sortIndex - a.sortIndex,
  );

  const selected = sortedUrlList[0];
  logger.info('Selected URL from Snowflake based on impressions', selected);
  return selected.url;
};

const checkForAlreadyProcessedUrls = async (
  recognizedUrls: string[],
  companyId: string,
  actionId: string,
  logger: ReturnType<typeof createContextLogger>,
): Promise<string[]> => {
  const seoApiGatewayClient = serviceFactory.createSeoApiGatewayClient(logger);
  const existingUrls: Set<string> = new Set();

  try {
    const existingScrapedPages =
      await seoApiGatewayClient.getPagesByCompanyId(companyId);
    existingScrapedPages.forEach((scrapedPage: { url: string }) => {
      existingUrls.add(normalizeUrlForComparison(scrapedPage.url));
    });
  } catch (error: unknown) {
    logger.error('Failed to fetch existing scraped pages', {
      error: error instanceof Error ? error.message : String(error),
      companyId,
    });
    throw new UrlSelectorError(
      'Failed to fetch existing scraped pages',
      error instanceof Error ? error : undefined,
      { companyId, actionId },
    );
  }

  return recognizedUrls.filter(
    url => !existingUrls.has(normalizeUrlForComparison(url)),
  );
};

export const handler = async (
  event: UrlSelectorEvent,
  context: Context,
): Promise<UrlSelectorResponse> => {
  const { companyId, generationPayload, actionId } = event;
  const { websiteUrl } = generationPayload;
  const logger = createContextLogger('url-selector', context, {
    lambdaName: 'url-selector',
    actionId,
    companyId,
    websiteUrl,
  });

  logger.info('Starting URL selector', { event });

  const currentConfig = getConfig();
  const firecrawlClient = new FirecrawlClient(currentConfig.firecrawl, logger);
  const scheduledActionService =
    serviceFactory.createScheduledActionService(logger);

  logger.info('Processing URL selector request');

  try {
    if (!actionId || typeof actionId !== 'string') {
      throw new UrlSelectorError('Action ID is required');
    }
    if (!websiteUrl || typeof websiteUrl !== 'string') {
      throw new UrlSelectorError('URL is required');
    }
    if (!companyId || typeof companyId !== 'string') {
      throw new UrlSelectorError('Company ID is required');
    }

    const { action, skipStep } = await scheduledActionService.checkAction(
      actionId,
      EXPECTED_RESULTS,
    );
    if (!action) {
      throw new UrlSelectorError('Action not found');
    }

    let originalGenerationPayload = action.generationPayload || {};

    if (skipStep) {
      logger.info('Possibly qualified to skip step');
      const selectedUrl = action.generationPayload!.selectedUrl as string;

      // Check if the selected URL is still eligible (not already processed by another action)
      const tempEligibleResults = await checkForAlreadyProcessedUrls(
        [selectedUrl],
        companyId,
        actionId,
        logger,
      );

      if (tempEligibleResults.length > 0) {
        logger.info('Skipping step');
        return {
          actionId,
          companyId,
          generationPayload: {
            ...(action.generationPayload || {}),
            websiteUrl,
            selectedUrl,
          },
        };
      }

      // Reset if the selected URL is already processed
      logger.info('Resetting action since selected URL was already processed');
      originalGenerationPayload = { websiteUrl };
      await scheduledActionService.update(actionId, {
        generationPayload: originalGenerationPayload,
        contentPayload: {},
      });
    }

    // Map all URLs from the website
    let allUrls = [];
    try {
      allUrls = await firecrawlClient.mapUrl(websiteUrl);
      allUrls = allUrls.map(url => normalizeUrl(url));
      allUrls = [...new Set(allUrls)];
    } catch (error) {
      logger.error('Failed to map website', {
        url: websiteUrl,
        error,
        companyId,
      });
      throw new FireCrawlError(
        'Failed to map website',
        error instanceof Error ? error : undefined,
        { url: websiteUrl, companyId, actionId },
      );
    }

    // Filter URLs by recognized page types
    const recognizedUrls: string[] = [];
    const unrecognizedUrls: string[] = [];
    allUrls.forEach(url => {
      if (getPageType(url)) {
        recognizedUrls.push(url);
      } else if (!url.includes('/properties/')) {
        unrecognizedUrls.push(url);
      }
    });
    if (unrecognizedUrls.length > 0) {
      logger.info('Ignoring unrecognized URLs', { urls: unrecognizedUrls });
    }

    // Filter out already processed URLs
    const eligibleUrls = await checkForAlreadyProcessedUrls(
      recognizedUrls,
      companyId,
      actionId,
      logger,
    );

    logger.info('Eligible URLs for selection', { count: eligibleUrls.length });

    if (eligibleUrls.length === 0) {
      throw new UrlSelectorError('No eligible URLs found for processing');
    }

    // Select the best URL based on Snowflake data
    const selectedUrl = await selectUrl(eligibleUrls, websiteUrl, logger);
    if (!selectedUrl) {
      logger.warn(
        'No URL selected for processing - all URLs either have rank 1 or no ranking data available. Exiting successfully.',
      );
      return {
        actionId,
        companyId,
        generationPayload: { ...originalGenerationPayload, websiteUrl },
      };
    }

    logger.info('Selected URL', { selectedUrl });

    // Update action with selected URL
    await scheduledActionService.update(actionId, {
      generationPayload: {
        ...originalGenerationPayload,
        selectedUrl,
      },
    });

    return {
      actionId,
      companyId,
      generationPayload: {
        ...originalGenerationPayload,
        websiteUrl,
        selectedUrl,
      },
    };
  } catch (error) {
    handleHandlerError(error, logger, 'url-selector');
  }
};
