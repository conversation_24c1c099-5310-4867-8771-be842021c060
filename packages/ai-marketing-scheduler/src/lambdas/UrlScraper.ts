import { randomUUID } from 'crypto';

import { Context } from 'aws-lambda';

import { FireCrawlError } from '../clients/errors/FireCrawlError';
import { S3Error } from '../clients/errors/S3Error';
import { FirecrawlClient } from '../clients/FirecrawlClient';
import { S3Client } from '../clients/S3Client';
import { getConfig } from '../config';
import { UrlScraperError } from '../errors/UrlScraperError';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';
import { ScraperResult } from '../types';
import { mainHeading as mainHeadingScript } from '../utils/getMainHeading';
import { cleanMarkdown } from '../utils/Markdown';
import { getPageType } from '../utils/pageUtils';

const EXPECTED_RESULTS = ['scraperResult'];

interface UrlScraperEvent {
  actionId: string;
  companyId: string;
  generationPayload: {
    selectedUrl: string;
    [key: string]: unknown;
  };
}

interface UrlScraperResponse {
  error?: string;
  scraperResult?: ScraperResult;
  actionId: string;
}

export const handler = async (
  event: UrlScraperEvent,
  context: Context,
): Promise<UrlScraperResponse> => {
  const { companyId, generationPayload, actionId } = event;
  const { selectedUrl } = generationPayload;
  const logger = createContextLogger('url-scraper', context, {
    lambdaName: 'url-scraper',
    actionId,
    companyId,
    selectedUrl,
  });

  logger.info('Starting URL scraper', { event });

  const currentConfig = getConfig();
  const s3Client = new S3Client(currentConfig.screenshotsS3, logger);
  const firecrawlClient = new FirecrawlClient(currentConfig.firecrawl, logger);
  const scheduledActionService =
    serviceFactory.createScheduledActionService(logger);

  logger.info('Processing URL scraper request');

  try {
    if (!actionId || typeof actionId !== 'string') {
      throw new UrlScraperError('Action ID is required');
    }
    if (!selectedUrl || typeof selectedUrl !== 'string') {
      throw new UrlScraperError('Selected URL is required');
    }
    if (!companyId || typeof companyId !== 'string') {
      throw new UrlScraperError('Company ID is required');
    }

    const { action, skipStep } = await scheduledActionService.checkAction(
      actionId,
      EXPECTED_RESULTS,
    );
    if (!action) {
      throw new UrlScraperError('Action not found');
    }

    const originalGenerationPayload = action.generationPayload || {};

    if (skipStep) {
      logger.info('Skipping step - returning existing scraper result');
      const scraperResult = action.generationPayload!
        .scraperResult as ScraperResult;
      return {
        actionId,
        scraperResult,
      };
    }

    const pageType = getPageType(selectedUrl);
    logger.setContext({ url: selectedUrl, pageType });

    logger.info('Starting to scrape selected URL');

    // Scrape the selected URL
    let scrapeResponse;
    try {
      scrapeResponse = await firecrawlClient.scrapeUrl(
        selectedUrl,
        mainHeadingScript,
      );
    } catch (error: unknown) {
      logger.error('Failed to scrape URL', {
        url: selectedUrl,
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      throw new FireCrawlError(
        'Failed to scrape URL',
        error instanceof Error ? error : undefined,
        { url: selectedUrl, companyId, actionId },
      );
    }

    // Process the scraped content
    const markdown = await cleanMarkdown(scrapeResponse.markdown!);

    // Validate main heading
    if (!scrapeResponse.actions.javascriptReturns?.length) {
      const error = new FireCrawlError(
        `No main heading found in scrape response for ${selectedUrl}`,
      );
      logger.error('Error in URL scraper', { error, scrapeResponse });
      throw error;
    }

    const metaTitle = scrapeResponse.metadata?.title;
    const metaDescription = scrapeResponse.metadata?.description;
    const mainHeading = scrapeResponse.actions.javascriptReturns?.[0].value as {
      section_id: string;
      element_id: string | null;
      tag: string;
      index: number;
      content: string;
    };

    logger.setContext({
      metaTitle,
      metaDescription,
      ...(mainHeading && { mainHeading }),
    });

    logger.info('Scraped URL metadata');

    // Validate meta title
    if (!metaTitle || metaTitle.trim().length === 0) {
      logger.error('Empty title returned from firecrawl', {
        metadata: scrapeResponse.metadata,
        selectedUrl,
        companyId,
        actionId,
      });
      throw new FireCrawlError('Empty title returned from firecrawl');
    }

    // Upload screenshot to S3
    const mediaId = randomUUID();
    logger.info('Uploading screenshot', { mediaId });
    try {
      const screenshotKey = `full/${mediaId}.png`;
      await s3Client.uploadFromUrl(scrapeResponse.screenshot!, screenshotKey);
    } catch (error) {
      logger.error('Failed to upload screenshot', {
        url: selectedUrl,
        screenshot: scrapeResponse.screenshot,
        error: error instanceof Error ? error.message : String(error),
        companyId,
        actionId,
      });
      throw new S3Error(
        'Failed to upload screenshot',
        error instanceof Error ? error : undefined,
        { url: selectedUrl, companyId, actionId },
      );
    }

    // Create scraper result
    const scrapeResult: ScraperResult = {
      firecrawlId: scrapeResponse.firecrawlId,
      url: selectedUrl,
      metaTitle,
      metaDescription,
      mainHeading,
      type: getPageType(selectedUrl)!,
      markdown,
      mediaId,
      companyId,
    };

    // Update action with scraper result
    await scheduledActionService.update(actionId, {
      generationPayload: {
        ...originalGenerationPayload,
        scraperResult: scrapeResult,
      },
    });

    logger.info('Successfully scraped URL and created result');

    return {
      scraperResult: scrapeResult,
      actionId,
    };
  } catch (error) {
    handleHandlerError(error, logger, 'url-scraper');
  }
};
