import { Context } from 'aws-lambda';
import { getWorkflowType, getUnits } from 'src/services/EntitlementService';
import { Entitlement } from 'src/types';

import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';

interface ActionSchedulerEvent {
  entitlements: Entitlement[];
}

interface ActionSchedulerResponse {
  // Overall statistics
  processedEntitlements: number;
  totalActionsCreated: number;
  totalOwedActions: number;
  totalUpcomingActions: number;
  executionTimestamp: string;

  // Detailed results per company
  details: Array<{
    companyId: string;
    workflowType: string;
    entitlementUnits: number;
    upcomingActions: number;
    actionsCreated: number;
    status: 'processed' | 'skipped' | 'error';
    error?: string;
  }>;
}

export const handler = async (
  event: ActionSchedulerEvent,
  context: Context,
): Promise<ActionSchedulerResponse> => {
  const logger = createContextLogger('draft-action-scheduler', context, {
    lambdaName: 'draft-action-scheduler',
  });

  if (event.entitlements.length === 0) {
    logger.info('No entitlements to process');
    return {
      processedEntitlements: 0,
      totalActionsCreated: 0,
      totalOwedActions: 0,
      totalUpcomingActions: 0,
      executionTimestamp: new Date().toISOString(),
      details: [],
    };
  }
  logger.info('Processing entitlements', {
    count: event.entitlements.length,
  });

  try {
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    const details: ActionSchedulerResponse['details'] = [];
    let totalActionsCreated = 0;
    let totalOwedActions = 0;
    let totalUpcomingActions = 0;

    for (const entitlement of event.entitlements) {
      const { companyId, units, websiteUrl } = entitlement;

      const workflowType = getWorkflowType(entitlement, logger);

      if (!workflowType) {
        logger.error('Invalid workflow type for entitlement', { companyId });
        details.push({
          companyId,
          workflowType: 'unknown',
          entitlementUnits: 0,
          upcomingActions: 0,
          actionsCreated: 0,
          status: 'error',
          error: 'Invalid workflow type',
        });
        continue;
      }
      const entitlementUnits = getUnits(units, workflowType);

      // Create a child logger with company context
      const companyLogger = logger.createChild({
        companyId,
        workflowType,
        productId: entitlement.productId,
        productName: entitlement.productName,
      });

      companyLogger.info('Processing draft action entitlement', {
        entitlementUnits,
        websiteUrl,
      });

      let upcomingActions: any[] = [];
      try {
        upcomingActions = await scheduledActionService.getUpcomingActions(
          companyId,
          workflowType,
        );

        const owed = Math.max(0, entitlementUnits - upcomingActions.length);
        totalUpcomingActions += upcomingActions.length;
        totalOwedActions += owed;

        if (owed > 0) {
          await scheduledActionService.createScheduledActions(
            companyId,
            workflowType,
            owed,
            { websiteUrl },
          );
          totalActionsCreated += owed;
          companyLogger.info('Created scheduled actions', {
            actionsCreated: owed,
            entitlementUnits,
            existingUpcomingActions: upcomingActions.length,
          });
        } else {
          companyLogger.debug('Skipped - sufficient upcoming actions', {
            entitlementUnits,
            existingUpcomingActions: upcomingActions.length,
          });
        }

        details.push({
          companyId,
          workflowType,
          entitlementUnits,
          upcomingActions: upcomingActions.length,
          actionsCreated: owed > 0 ? owed : 0,
          status: 'processed',
        });
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        const errorType = error instanceof Error ? error.name : 'UnknownError';

        // Determine which operation failed for better debugging
        const failedOperation =
          upcomingActions && upcomingActions.length > 0
            ? 'createScheduledActions'
            : 'getUpcomingActions';

        companyLogger.error('Failed to process entitlement', {
          error: errorMessage,
          errorType,
          failedOperation,
          entitlementUnits,
          websiteUrl,
        });

        details.push({
          companyId,
          workflowType,
          entitlementUnits,
          upcomingActions: 0,
          actionsCreated: 0,
          status: 'error',
          error: errorMessage,
        });
      }
    }

    const response: ActionSchedulerResponse = {
      processedEntitlements: event.entitlements.length,
      totalActionsCreated,
      totalOwedActions,
      totalUpcomingActions,
      executionTimestamp: new Date().toISOString(),
      details,
    };

    logger.info('Completed action scheduling', { ...response });

    return response;
  } catch (error: unknown) {
    return handleHandlerError(error, logger, 'action-scheduler');
  }
};
