import { Context } from 'aws-lambda';

import { getConfig, EnvironmentConfig } from '../config';
import { DraftActionConsumerError } from '../errors';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger, ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { ScheduledAction, WorkflowType } from '../types';

/**
 * Interface for the publishing action payload that will be passed to the Publisher lambda
 */
interface PublishingAction {
  actionId: string;
  companyId: string;
  workflowType: WorkflowType;
}

/**
 * Interface for the handler's response
 */
interface GetSurfacedActionsResponse {
  actions: PublishingAction[];
}

/**
 * Gets the current date in YYYY-MM-DD format
 */
function getCurrentDate(): string {
  return new Date().toISOString().split('T')[0];
}

/**
 * Creates a publishing action from a scheduled action
 */
function createPublishingAction(action: ScheduledAction): PublishingAction {
  return {
    actionId: action.id,
    companyId: action.companyId,
    workflowType: action.workflowType,
  };
}

/**
 * Initialize the services needed for the handler
 */
function initializeServices(logger: ContextLogger): {
  config: EnvironmentConfig;
  scheduledActionService: IScheduledActionService;
} {
  try {
    const config = getConfig();

    // Create services using the unified service factory
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    logger.info('Services initialized for GetSurfacedActions');

    return { config, scheduledActionService };
  } catch (initError) {
    const errorMessage =
      initError instanceof Error ? initError.message : String(initError);
    logger.error('Failed to initialize services', {
      error: errorMessage,
      stack: initError instanceof Error ? initError.stack : undefined,
    });
    throw new DraftActionConsumerError(
      `Initialization failed: ${errorMessage}`,
      initError instanceof Error ? initError : undefined,
    );
  }
}

/**
 * Main handler function for the get surfaced actions lambda
 */
export const handler = async (
  event: Record<string, unknown>,
  context: Context,
): Promise<GetSurfacedActionsResponse> => {
  const logger = createContextLogger('get-surfaced-actions', context, {
    lambdaName: 'get-surfaced-actions',
  });
  logger.info('Starting get surfaced actions', { event });

  try {
    // Initialize services
    const { scheduledActionService } = initializeServices(logger);

    // Get current date for filtering actions
    const currentDate = getCurrentDate();

    // Get actions to process
    const actions =
      await scheduledActionService.getSurfacedActions(currentDate);

    // Convert scheduled actions to publishing actions
    const publishingActions = actions.map(createPublishingAction);

    logger.info('Actions ready for publishing', {
      count: publishingActions.length,
      actions: publishingActions,
    });

    return {
      actions: publishingActions,
    };
  } catch (error) {
    return handleHandlerError(error, logger, 'get-surfaced-actions');
  }
};
