import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Context } from 'aws-lambda';

import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';

interface RankBatchSplitterEvent {
  keywords?: { keyword: string; pageId: string }[];
}

export const handler = async (
  event: RankBatchSplitterEvent = {},
  context: Context,
): Promise<{
  s3InputLocation: string;
  totalKeywords: number;
  bucketName: string;
  keyPrefix: string;
  truncated: boolean;
}> => {
  const logger = createContextLogger('rank-batch-splitter', context, {
    lambdaName: 'rank-batch-splitter',
  });
  logger.info('Starting rank batch splitter', { event });

  try {
    let allKeywords: { keyword: string; pageId: string }[] = [];
    let wasTruncated = false;

    if (event.keywords && event.keywords.length > 0) {
      logger.info('Using provided keywords', { count: event.keywords.length });
      allKeywords = event.keywords;
    } else {
      // Fetch all data in paginated chunks to prevent memory issues
      const seoApiGatewayClient =
        serviceFactory.createSeoApiGatewayClient(logger);
      let page = 1;
      let hasMore = true;
      const pageSize = 100; // Conservative page size to avoid memory issues

      logger.info('Starting paginated fetch of surfaced groups');

      const maxPages = 1000;
      while (hasMore && page <= maxPages) {
        // Safety limit to prevent infinite loops
        logger.info('Fetching page', { page, pageSize });

        const response =
          await seoApiGatewayClient.getSurfacedAndPublishedGroupsForRankingPaginated(
            page,
            pageSize,
          );

        const pageKeywords = response.data.map(group => ({
          keyword: group.keyword.keyword,
          pageId: group.scrapedPage.id,
        }));

        allKeywords.push(...pageKeywords);
        hasMore = response.hasMore;
        page++;

        logger.info('Fetched page successfully', {
          page: page - 1,
          pageKeywords: pageKeywords.length,
          totalSoFar: allKeywords.length,
          totalCount: response.totalCount,
          hasMore,
        });

        // Add small delay between pages to be gentle on the database
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Check for truncation after loop exit
      if (hasMore && page > maxPages) {
        wasTruncated = true;
        logger.error('Data truncation occurred: reached maximum page limit', {
          maxPages,
          pageSize,
          totalPagesFetched: page - 1,
          totalKeywordsFetched: allKeywords.length,
          nextPageWouldBe: page,
          hasMoreData: hasMore,
          recommendedAction: 'Consider increasing maxPages or pageSize',
          truncationPoint: `Page ${page} of potential additional pages`,
        });
      }

      logger.info('Completed paginated fetch', {
        totalPages: page - 1,
        totalKeywords: allKeywords.length,
        truncated: wasTruncated,
      });
    }

    if (allKeywords.length === 0) {
      logger.info('No keywords to process');
      // Still write empty file for consistency
      allKeywords = [];
    }

    // Write individual keywords to S3 for distributed map processing
    // Use dedicated folder: rank-queue/
    const s3Client = new S3Client({});
    const bucketName = process.env.API_RESPONSE_S3_BUCKET_NAME!;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const keyPrefix = `rank-queue/${timestamp}`;
    const s3Key = `${keyPrefix}/keywords.json`;

    // Format data for distributed map - each keyword becomes a separate item
    const keywordItems = allKeywords.map(kw => ({
      keyword: kw.keyword,
      pageId: kw.pageId,
    }));

    logger.info('Writing keywords to S3', {
      bucketName,
      s3Key,
      totalKeywords: keywordItems.length,
    });

    await s3Client.send(
      new PutObjectCommand({
        Bucket: bucketName,
        Key: s3Key,
        Body: JSON.stringify(keywordItems),
        ContentType: 'application/json',
        // Add metadata for tracking
        Metadata: {
          totalKeywords: keywordItems.length.toString(),
          timestamp,
          source: 'rank-batch-splitter',
        },
      }),
    );

    logger.info('Successfully wrote keywords to S3', {
      bucketName,
      s3Key,
      totalKeywords: keywordItems.length,
    });

    return {
      s3InputLocation: `s3://${bucketName}/${s3Key}`,
      totalKeywords: keywordItems.length,
      bucketName,
      keyPrefix,
      truncated: wasTruncated,
    };
  } catch (error: unknown) {
    return handleHandlerError(error, logger, 'rank-batch-splitter');
  }
};
