import { Context } from 'aws-lambda';
import { DataForSeoClient } from 'clients/DataForSeoClient';
import { getConfig } from 'config';

import { RankQueuerError } from '../errors/RankQueuerError';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger } from '../logger/contextLogger';

interface RankQueuerEvent {
  keywords?: { keyword: string; pageId: string }[];
  // New format from distributed map ItemSelector
  keyword?: string;
  pageId?: string;
}

export const handler = async (event: RankQueuerEvent, context: Context) => {
  const logger = createContextLogger('rank-queuer-daemon', context, {
    lambdaName: 'rank-queuer-daemon',
  });

  logger.info('Starting rank queuer daemon', { event });

  try {
    const currentConfig = getConfig();
    const dataForSeoClient = new DataForSeoClient(
      currentConfig.dataForSeo,
      logger,
    );

    let keywords: { keyword: string; pageId: string }[];

    // Handle distributed map format (individual keyword)
    if (event.keyword && event.pageId) {
      logger.info('Using single keyword from distributed map', {
        keyword: event.keyword,
        pageId: event.pageId,
      });
      keywords = [{ keyword: event.keyword, pageId: event.pageId }];
    }
    // Handle batch format (array of keywords)
    else if (event.keywords && event.keywords.length > 0) {
      logger.info('Using provided keywords batch', {
        count: event.keywords.length,
      });
      keywords = event.keywords;

      // Validate provided keywords
      if (!keywords.every(keyword => keyword.keyword && keyword.pageId)) {
        logger.error(
          'Invalid input: each keyword must have a keyword and pageId',
          {
            invalidKeywords: keywords.filter(k => !k.keyword || !k.pageId),
          },
        );
        throw new RankQueuerError(
          'Invalid input: each keyword must have a keyword and pageId',
          undefined,
          {
            invalidKeywords: keywords.filter(k => !k.keyword || !k.pageId),
          },
        );
      }
    } else {
      const error = new RankQueuerError(
        'RankQueuerDaemon requires keywords to be provided. ' +
          'This lambda is designed for distributed map processing and should not fetch unbounded data directly. ' +
          'Use the distributed map pattern where data is pre-fetched and provided as input.',
      );
      logger.error('Missing required keywords in event', {
        event,
        error: error.message,
        recommendation:
          'Ensure keywords are provided via distributed map ItemSelector or batch input',
      });
      throw error;
    }

    const res = await dataForSeoClient.enqueueTasks(keywords);

    logger.info('Successfully queued tasks', { res });
    return res;
  } catch (error) {
    handleHandlerError(error, logger, 'rank-queuer-daemon');
  }
};
