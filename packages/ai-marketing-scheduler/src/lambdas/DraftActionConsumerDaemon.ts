import { SFNClient, StartExecutionCommand } from '@aws-sdk/client-sfn';
import { Context } from 'aws-lambda';

import { getConfig, EnvironmentConfig } from '../config';
import {
  DraftActionConsumerError,
  StepFunctionExecutionError,
} from '../errors';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger, ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { UpdateScheduledActionResult } from '../services/ScheduledActionService';
import { ScheduledAction, WorkflowType } from '../types';

/**
 * Interface for the handler's response
 */
interface DraftActionConsumerResponse {
  processed: number;
  successful: number;
  failed: number;
  results: UpdateScheduledActionResult[];
}

/**
 * Interface for the action processing result summary
 */
interface ActionProcessingSummary {
  successfulCount: number;
  failedCount: number;
  successfulActionIds: string[];
  failedActionIds: string[];
}

/**
 * Interface for the step function payload
 */
interface StepFunctionPayload {
  companyId: string;
  workflowType: string;
  actionId: string;
  generationPayload: Record<string, unknown> | null;
}

/**
 * Creates a step function payload from a scheduled action
 */
function createStepFunctionPayload(
  action: ScheduledAction,
): StepFunctionPayload {
  return {
    companyId: action.companyId,
    workflowType: action.workflowType,
    actionId: action.id,
    generationPayload: action.generationPayload || null,
  };
}

/**
 * Gets the appropriate state machine ARN based on the workflow type
 */
function getStateMachineArn(
  workflowType: WorkflowType,
  config: EnvironmentConfig,
  logger: ContextLogger,
): string | null {
  const stateMachineArn =
    workflowType === WorkflowType.SEO
      ? config.stateMachine.seoDraftArn
      : config.stateMachine.blogDraftArn;

  if (!stateMachineArn) {
    logger.error('Missing Step Function ARN', {
      workflowType,
    });
  }

  return stateMachineArn;
}

/**
 * Processes a single scheduled action
 */
async function processAction(
  action: ScheduledAction,
  sfnClient: SFNClient,
  scheduledActionService: IScheduledActionService,
  config: EnvironmentConfig,
  logger: ContextLogger,
): Promise<UpdateScheduledActionResult> {
  try {
    logger.info('Processing action');
    const stateMachineArn = getStateMachineArn(
      action.workflowType,
      config,
      logger,
    );
    if (!stateMachineArn) {
      const error = new StepFunctionExecutionError(
        'Missing Step Function ARN',
        action.id,
      );

      logger.error('Missing Step Function ARN for action', {
        actionId: action.id,
        workflowType: action.workflowType,
        error: error.message,
      });

      return {
        id: action.id,
        success: false,
        workflowType: action.workflowType,
        error: error.message,
      };
    }

    const payload = createStepFunctionPayload(action);
    // Sanitize to allowed chars, truncate so name + '-' + timestamp ≤ 80 chars
    const ts = Date.now().toString();
    const rawName = action.id.replace(/[^A-Za-z0-9\-_]/g, '');
    const safeName = rawName.slice(0, 80 - ts.length - 1);
    const executionName = `${safeName}-${ts}`; // minus 1 for the dash

    const startCommand = new StartExecutionCommand({
      stateMachineArn,
      input: JSON.stringify(payload),
      name: executionName,
    });

    let response;
    try {
      response = await sfnClient.send(startCommand);
    } catch (sfnError) {
      // Specific handling for Step Function execution errors
      const errorMessage =
        sfnError instanceof Error ? sfnError.message : String(sfnError);
      throw new StepFunctionExecutionError(
        `Failed to start Step Function execution: ${errorMessage}`,
        action.id,
        sfnError instanceof Error ? sfnError : undefined,
      );
    }

    logger.info('Started Step Function execution', {
      actionId: action.id,
      executionArn: response.executionArn,
      payload,
    });

    // Update the scheduled action status using the service
    await scheduledActionService.update(action.id, {
      executionName,
      executionArn: response.executionArn,
    });

    return {
      id: action.id,
      success: true,
      workflowType: action.workflowType,
    };
  } catch (error) {
    // Enhanced error logging with more details
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorName = error instanceof Error ? error.name : 'UnknownError';
    const errorStack = error instanceof Error ? error.stack : undefined;

    logger.error('Error processing action', {
      actionId: action.id,
      errorType: errorName,
      error: errorMessage,
      stack: errorStack,
      // Include additional context if it's our custom error
      ...(error instanceof StepFunctionExecutionError && {
        metadata: error.metadata,
      }),
    });

    const failureReason = {
      errorType: errorName,
      error: errorMessage,
      stack: errorStack,
    };

    // Update the scheduled action with FAILED status and failure reason using the service
    return await scheduledActionService.updateWithFailure(
      action.id,
      failureReason,
      action.workflowType,
    );
  }
}

/**
 * Summarizes the results of processing actions
 */
function summarizeResults(
  results: UpdateScheduledActionResult[],
): ActionProcessingSummary {
  const successfulActionIds = results.filter(r => r.success).map(r => r.id);
  const failedActionIds = results.filter(r => !r.success).map(r => r.id);

  const counts = results.reduce(
    (acc, r) => ({
      successful: r.success ? acc.successful + 1 : acc.successful,
      failed: r.success ? acc.failed : acc.failed + 1,
    }),
    { successful: 0, failed: 0 },
  );

  return {
    successfulCount: counts.successful,
    failedCount: counts.failed,
    successfulActionIds,
    failedActionIds,
  };
}

/**
 * Creates an empty response for when there are no actions to process
 */
function createEmptyResponse(): DraftActionConsumerResponse {
  return {
    processed: 0,
    successful: 0,
    failed: 0,
    results: [],
  };
}

/**
 * Initialize the services needed for the handler
 */
function initializeServices(logger: ContextLogger): {
  config: EnvironmentConfig;
  scheduledActionService: IScheduledActionService;
  sfnClient: SFNClient;
} {
  try {
    const config = getConfig();

    // Create services using the unified service factory
    // This abstracts away whether we're using mock or real implementations
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    // Create SFN client (mock failures are configured in the factory)
    const sfnClient = serviceFactory.createSfnClient(logger);

    logger.info('Services initialized');

    return { config, scheduledActionService, sfnClient };
  } catch (initError) {
    const errorMessage =
      initError instanceof Error ? initError.message : String(initError);
    logger.error('Failed to initialize services', {
      error: errorMessage,
      stack: initError instanceof Error ? initError.stack : undefined,
    });
    throw new DraftActionConsumerError(
      `Initialization failed: ${errorMessage}`,
      initError instanceof Error ? initError : undefined,
    );
  }
}

/**
 * Process a chunk of actions with error handling
 */
async function processChunk(
  chunk: ScheduledAction[],
  sfnClient: SFNClient,
  scheduledActionService: IScheduledActionService,
  config: EnvironmentConfig,
  logger: ContextLogger,
): Promise<UpdateScheduledActionResult[]> {
  // Process this chunk of actions in parallel
  const chunkPromises: Promise<UpdateScheduledActionResult>[] = chunk.map(
    (action: ScheduledAction) => {
      // Create a child logger with action-specific context
      const actionLogger = logger.createChild({
        actionId: action.id,
        companyId: action.companyId,
        workflowType: action.workflowType,
      });
      return processAction(
        action,
        sfnClient,
        scheduledActionService,
        config,
        actionLogger,
      ).catch(actionError => {
        // This ensures individual action failures don't break the entire batch
        actionLogger.error('Unhandled error in processAction', {
          actionId: action.id,
          error:
            actionError instanceof Error
              ? actionError.message
              : String(actionError),
          stack: actionError instanceof Error ? actionError.stack : undefined,
        });

        return {
          id: action.id,
          success: false,
          workflowType: action.workflowType,
          error: `Unhandled error: ${
            actionError instanceof Error
              ? actionError.message
              : String(actionError)
          }`,
        };
      });
    },
  );

  // Wait for this chunk to complete before processing the next chunk
  return Promise.all(chunkPromises);
}

/**
 * Apply exponential backoff between chunks
 */
async function applyBackoff(
  chunkIndex: number,
  logger: ContextLogger,
): Promise<void> {
  // Calculate exponential backoff delay: baseDelay * (2^chunkIndex) with a max cap
  const baseDelay = 100; // Base delay in ms
  const maxDelay = 5000; // Cap at 5 seconds
  const backoffDelay = Math.min(baseDelay * Math.pow(2, chunkIndex), maxDelay);

  logger.info('Applying exponential backoff between chunks', {
    chunkIndex: chunkIndex + 1,
    delayMs: backoffDelay,
  });

  return new Promise(resolve => setTimeout(resolve, backoffDelay));
}

/**
 * Process actions in chunks with backoff
 */
async function processActionsInChunks(
  actions: ScheduledAction[],
  chunkSize: number,
  sfnClient: SFNClient,
  scheduledActionService: IScheduledActionService,
  config: EnvironmentConfig,
  logger: ContextLogger,
): Promise<UpdateScheduledActionResult[]> {
  const results: UpdateScheduledActionResult[] = [];

  // Split actions into chunks
  for (let i = 0; i < actions.length; i += chunkSize) {
    const chunk = actions.slice(i, i + chunkSize);
    const chunkIndex = Math.floor(i / chunkSize);

    const chunkLogger = logger.createChild({
      chunkIndex: chunkIndex + 1,
      totalChunks: Math.ceil(actions.length / chunkSize),
      chunkSize: chunk.length,
    });

    chunkLogger.info('Processing chunk of actions', {
      startIndex: i,
      endIndex: Math.min(i + chunkSize - 1, actions.length - 1),
      actionIds: chunk.map(a => a.id),
    });

    // Process the current chunk
    const chunkResults = await processChunk(
      chunk,
      sfnClient,
      scheduledActionService,
      config,
      chunkLogger,
    );
    results.push(...chunkResults);

    // Apply backoff if there are more chunks to process
    if (i + chunkSize < actions.length) {
      await applyBackoff(chunkIndex, chunkLogger);
    }
  }

  return results;
}

/**
 * Create response from processing results
 */
function createResponseFromResults(
  actions: ScheduledAction[],
  results: UpdateScheduledActionResult[],
  logger: ContextLogger,
): DraftActionConsumerResponse {
  // Summarize the results
  const summary = summarizeResults(results);

  logger.info('Completed processing actions', {
    total: actions.length,
    ...summary,
  });

  return {
    processed: actions.length,
    successful: summary.successfulCount,
    failed: summary.failedCount,
    results,
  };
}

/**
 * Main handler function for the draft action consumer
 */
export const handler = async (
  event: Record<string, unknown>,
  context: Context,
): Promise<DraftActionConsumerResponse> => {
  const logger = createContextLogger('draft-action-consumer', context, {
    lambdaName: 'draft-action-consumer',
  });
  logger.info('Starting draft action consumer', { event });

  try {
    // Initialize services
    const { config, scheduledActionService, sfnClient } =
      initializeServices(logger);

    // Get actions to process
    const actions = await scheduledActionService.getDraftPendingActions();

    if (actions.length === 0) {
      logger.info('No draft pending actions to process');
      return createEmptyResponse();
    }

    // Process actions in chunks
    const results = await processActionsInChunks(
      actions,
      config.actionChunkSize,
      sfnClient,
      scheduledActionService,
      config,
      logger,
    );

    // Create and return the response
    return createResponseFromResults(actions, results, logger);
  } catch (error) {
    return handleHandlerError(error, logger, 'draft-action-consumer');
  }
};
