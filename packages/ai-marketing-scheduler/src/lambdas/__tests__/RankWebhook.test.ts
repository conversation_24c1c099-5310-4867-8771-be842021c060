import { APIGatewayProxyEvent, Context } from 'aws-lambda';

import { S3Client } from '../../clients/S3Client';
import { StoredScrapedPage } from '../../clients/types/seoApiGateway';
import { ScrapedPageType } from '../../types';
import { RankingUrlTask, RankingUrlTasks } from '../../types/dataForSeo';
import {
  createMockLambdaContext,
  createMockSeoApiGatewayClient,
} from '../../utils/testUtils';
import { handler } from '../RankWebhook';

jest.mock('../../factories/LoggerFactory', () => ({
  createContextLogger: jest.fn(),
}));
jest.mock('../../factories', () => ({
  serviceFactory: {
    createSeoApiGatewayClient: jest.fn(),
  },
}));
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));
jest.mock('../../clients/S3Client');

const { createContextLogger } = jest.requireMock(
  '../../factories/LoggerFactory',
);
const { serviceFactory } = jest.requireMock('../../factories');
const { getConfig } = jest.requireMock('../../config');

describe('RankWebhook', () => {
  let mockLogger: any;
  let mockSeoApiGatewayClient: ReturnType<typeof createMockSeoApiGatewayClient>;
  let mockS3Client: jest.Mocked<S3Client>;
  let mockS3Config: any;
  let mockContext: Context;

  beforeEach(() => {
    jest.clearAllMocks();

    mockContext = createMockLambdaContext();

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };
    createContextLogger.mockReturnValue(mockLogger);

    mockSeoApiGatewayClient = createMockSeoApiGatewayClient();
    serviceFactory.createSeoApiGatewayClient = jest
      .fn()
      .mockReturnValue(mockSeoApiGatewayClient);

    mockS3Config = {
      bucketName: 'test-bucket',
    };

    getConfig.mockReturnValue({
      apiResponseS3: mockS3Config,
    } as any);

    mockS3Client = {
      buildKey: jest.fn(),
      uploadObject: jest.fn(),
    } as any;
    (S3Client as jest.MockedClass<typeof S3Client>).mockImplementation(
      () => mockS3Client,
    );
  });

  describe('handler', () => {
    const createMockTask = (
      keyword: string,
      pageId: string,
      rank: number = 1,
    ): RankingUrlTask => ({
      id: 'task-1',
      status_code: 20000,
      data: {
        keyword,
        tag: pageId,
      },
      result: [
        {
          datetime: '2024-01-01 00:00:00 +00:00',
          items: [
            {
              type: 'organic',
              rank_group: rank,
              url: 'https://example.com/page',
            },
          ],
        },
      ],
    });

    const createMockPage = (
      id: string,
      url: string,
      companyId: string,
      keyword?: string,
    ): StoredScrapedPage => ({
      id,
      url,
      companyId,
      pageName: 'Test Page',
      pageType: ScrapedPageType.HOMEPAGE,
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      recommendations: [],
      scrapes: [],
      pageKeywordHistory: [],
      pageKeywords: keyword
        ? [
            {
              id: 'page-keyword-123',
              scrapedPageId: id,
              keywordId: 'keyword-123',
              originalRank: 10,
              currentRank: 5,
              rankCheckedAt: new Date(),
              createdAt: new Date(),
              updatedAt: new Date(),
              keyword: {
                id: 'keyword-123',
                keyword: keyword,
                metadata: {},
                createdAt: new Date(),
                updatedAt: new Date(),
              },
              scrapedPage: {} as any,
              companyId,
            },
          ]
        : [],
    });

    it('should process tasks successfully', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [createMockTask('test keyword', 'page-123', 5)],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page',
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      } as APIGatewayProxyEvent;

      const result = await handler(event, mockContext);

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });

      expect(mockS3Client.uploadObject).toHaveBeenCalledWith(
        'test-bucket',
        expect.stringMatching(
          /^rankings\/year=\d{4}\/month=\d{2}\/day=\d{2}\/test%20keyword\.json\.gz$/,
        ),
        expect.any(Buffer),
      );

      expect(mockSeoApiGatewayClient.getPageById).toHaveBeenCalledWith(
        'page-123',
      );

      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-123', 5);
    });

    it('should handle page not found in ranking results', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [createMockTask('test keyword', 'page-123', 5)],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://different-url.com/page', // Different URL
        'company-123',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      } as APIGatewayProxyEvent;

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);
      // Page should not be found in results, so rank should be -1
      // but we don't test logs
    });

    it('should handle empty tasks array', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [],
      };

      const event = {
        body: JSON.stringify(mockTasks),
      } as APIGatewayProxyEvent;

      const result = await handler(event, mockContext);

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 0,
        }),
      });

      expect(mockS3Client.uploadObject).not.toHaveBeenCalled();
      expect(mockSeoApiGatewayClient.getPageById).not.toHaveBeenCalled();
    });

    it('should handle parsed body object', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [createMockTask('test keyword', 'page-123')],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page',
        'company-123',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: mockTasks, // Already parsed object
      } as unknown as APIGatewayProxyEvent;

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });
      expect(mockS3Client.uploadObject).toHaveBeenCalled();
    });

    it('should throw error when page is not found', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [createMockTask('test keyword', 'page-123')],
      };

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(null);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 500,
        body: JSON.stringify({
          error: 'Internal Server Error',
          message: 'Page page-123 not found',
        }),
      });
    });

    it('should throw error when task has unexpected result count', async () => {
      const mockTask = createMockTask('test keyword', 'page-123');
      mockTask.result = []; // No results

      const mockTasks: RankingUrlTasks = {
        tasks: [mockTask],
      };

      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 500,
        body: JSON.stringify({
          error: 'Internal Server Error',
          message: 'Expected exactly one result in task task-1, but got 0',
        }),
      });
    });

    it('should process multiple tasks', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [
          createMockTask('keyword1', 'page-1', 3),
          createMockTask('keyword2', 'page-2', 7),
        ],
      };

      const mockPages = [
        createMockPage('page-1', 'https://example.com/page', 'company-1'),
        createMockPage('page-2', 'https://example.com/page', 'company-2'),
      ];

      mockSeoApiGatewayClient.getPageById
        .mockResolvedValueOnce(mockPages[0])
        .mockResolvedValueOnce(mockPages[1]);

      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 2,
        }),
      });
      expect(mockS3Client.uploadObject).toHaveBeenCalledTimes(2);
      expect(mockSeoApiGatewayClient.getPageById).toHaveBeenCalledTimes(2);
    });

    it('should handle URL normalization for comparison', async () => {
      const mockTask = createMockTask('test keyword', 'page-123');
      // Update the URL with query params and hash
      mockTask.result[0].items[0].url =
        'https://example.com/page?utm=test#section';

      const mockTasks: RankingUrlTasks = {
        tasks: [mockTask],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page#different', // Different hash and no query
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });
      // Should find the match despite different query params and hash
      // URL normalization should work properly
    });

    it('should handle http vs https protocol differences in URL comparison', async () => {
      const mockTask = createMockTask('test keyword', 'page-123');
      mockTask.result[0].items[0].url = 'http://example.com/page';

      const mockTasks: RankingUrlTasks = {
        tasks: [mockTask],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page', // Page URL has HTTPS protocol
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });
      // Should find the match despite protocol difference (http vs https)
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-123', 1);
    });

    it('should handle trailing slash differences in URL comparison', async () => {
      const mockTask = createMockTask('test keyword', 'page-123');
      mockTask.result[0].items[0].url = 'https://example.com/page/';

      const mockTasks: RankingUrlTasks = {
        tasks: [mockTask],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page', // Page URL has no trailing slash
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });
      // Should find the match despite trailing slash difference
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-123', 1);
    });

    it('should handle complex URL normalization with protocol, trailing slash, query params, and hash', async () => {
      const mockTask = createMockTask('test keyword', 'page-123');
      mockTask.result[0].items[0].url =
        'http://example.com/page/?utm_source=google&utm_medium=organic#top';

      const mockTasks: RankingUrlTasks = {
        tasks: [mockTask],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page#bottom', // Page URL has HTTPS, no trailing slash, no query, different hash
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      };

      const result = await handler(
        event as unknown as APIGatewayProxyEvent,
        mockContext,
      );

      expect(result).toEqual({
        statusCode: 200,
        body: JSON.stringify({
          message: 'Success',
          processed: 1,
        }),
      });
      // Should find the match despite all URL differences after normalization
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-123', 1);
    });

    it('should update multiple page-keyword ranks when page has multiple keywords', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [
          createMockTask('keyword1', 'page-123', 3),
          createMockTask('keyword2', 'page-123', 7),
        ],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page',
        'company-123',
      );

      // Add multiple keywords to the page
      mockPage.pageKeywords = [
        {
          id: 'page-keyword-1',
          scrapedPageId: 'page-123',
          keywordId: 'keyword-1',
          originalRank: 10,
          currentRank: 5,
          rankCheckedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          keyword: {
            id: 'keyword-1',
            keyword: 'keyword1',
            metadata: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          scrapedPage: {} as any,
          companyId: 'company-123',
        },
        {
          id: 'page-keyword-2',
          scrapedPageId: 'page-123',
          keywordId: 'keyword-2',
          originalRank: 15,
          currentRank: 8,
          rankCheckedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          keyword: {
            id: 'keyword-2',
            keyword: 'keyword2',
            metadata: {},
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          scrapedPage: {} as any,
          companyId: 'company-123',
        },
      ];

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      const event = {
        body: JSON.stringify(mockTasks),
      } as APIGatewayProxyEvent;

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);

      // Verify both rank updates were called
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledTimes(2);
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-1', 3);
      expect(
        mockSeoApiGatewayClient.updatePageKeywordRank,
      ).toHaveBeenCalledWith('page-keyword-2', 7);
    });

    it('should handle rank update failures gracefully', async () => {
      const mockTasks: RankingUrlTasks = {
        tasks: [createMockTask('test keyword', 'page-123', 5)],
      };

      const mockPage = createMockPage(
        'page-123',
        'https://example.com/page',
        'company-123',
        'test keyword',
      );

      mockSeoApiGatewayClient.getPageById.mockResolvedValue(mockPage);
      mockS3Client.uploadObject.mockResolvedValue({} as any);

      // Make the rank update fail
      mockSeoApiGatewayClient.updatePageKeywordRank.mockRejectedValueOnce(
        new Error('Failed to update rank'),
      );

      const event = {
        body: JSON.stringify(mockTasks),
      } as APIGatewayProxyEvent;

      const result = await handler(event, mockContext);

      // Should still process successfully even if rank update fails
      expect(result.statusCode).toBe(200);
    });
  });
});
