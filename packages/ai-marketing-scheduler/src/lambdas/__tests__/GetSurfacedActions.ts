import { Logger } from '@aws-lambda-powertools/logger';
import { Context } from 'aws-lambda';

import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { Status } from '../../types';
import {
  setupTestMocks,
  createMockScheduledActionService,
  createMockLambdaContext,
  createMockScheduledActions,
} from '../../utils/testUtils';
import { handler } from '../GetSurfacedActions';

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.fn(),
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

jest.mock('../../factories', () => ({
  serviceFactory: {
    createScheduledActionService: jest.fn(),
    createLambdaClient: jest.fn(),
  },
}));

describe('GetSurfacedActions', () => {
  let mockLogger: Logger & { restore: () => void };
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;
  let mockContext: Context;

  const mockConfig = {};

  beforeEach(() => {
    // Set up test mocks including logger
    const { mockLogger: logger } = setupTestMocks();
    mockLogger = logger;

    // Connect the mock logger to createContextLogger
    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );

    // Create mocks
    mockScheduledActionService = createMockScheduledActionService();
    mockContext = createMockLambdaContext();

    // Mock dependencies
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { getConfig } = require('../../config');
    getConfig.mockReturnValue(mockConfig);

    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { serviceFactory } = require('../../factories');
    serviceFactory.createScheduledActionService.mockReturnValue(
      mockScheduledActionService,
    );

    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { handleHandlerError } = require('../../factories/LoggerFactory');
    handleHandlerError.mockImplementation((error: unknown) => {
      throw error;
    });
  });

  afterEach(() => {
    // Restores both mocked call history *and* the original implementation of any spied-on globals
    jest.restoreAllMocks();
    mockLogger.restore();
  });

  describe('handler', () => {
    it('should return surfaced actions for Step Function processing', async () => {
      // Mock current date
      jest
        .spyOn(Date.prototype, 'toISOString')
        .mockReturnValue('2023-10-15T00:00:00.000Z');

      // Mock surfaced actions
      const mockActions = createMockScheduledActions(3).map(action => ({
        ...action,
        status: Status.SURFACED,
      }));

      mockScheduledActionService.getSurfacedActions.mockResolvedValueOnce(
        mockActions,
      );

      // Call the handler
      const result = await handler({}, mockContext);

      // Verify service calls
      expect(
        mockScheduledActionService.getSurfacedActions,
      ).toHaveBeenCalledWith('2023-10-15');

      // Verify response
      expect(result).toEqual({
        actions: mockActions.map(action => ({
          actionId: action.id,
          companyId: action.companyId,
          workflowType: action.workflowType,
        })),
      });
    });

    it('should return empty actions array when no surfaced actions found', async () => {
      // Mock current date
      jest
        .spyOn(Date.prototype, 'toISOString')
        .mockReturnValue('2023-10-15T00:00:00.000Z');

      // Mock empty actions
      mockScheduledActionService.getSurfacedActions.mockResolvedValueOnce([]);

      // Call the handler
      const result = await handler({}, mockContext);

      // Verify service calls
      expect(
        mockScheduledActionService.getSurfacedActions,
      ).toHaveBeenCalledWith('2023-10-15');

      // Verify response
      expect(result).toEqual({
        actions: [],
      });
    });

    it('should handle service errors by calling handleHandlerError', async () => {
      // Mock current date
      jest
        .spyOn(Date.prototype, 'toISOString')
        .mockReturnValue('2023-10-15T00:00:00.000Z');

      // Mock service error
      const mockError = new Error('Service error');
      mockScheduledActionService.getSurfacedActions.mockRejectedValueOnce(
        mockError,
      );

      // Call the handler and expect it to throw
      await expect(handler({}, mockContext)).rejects.toThrow('Service error');

      // Verify handleHandlerError was called
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const { handleHandlerError } = require('../../factories/LoggerFactory');
      expect(handleHandlerError).toHaveBeenCalledWith(
        mockError,
        mockLogger,
        'get-surfaced-actions',
      );
    });
  });
});
