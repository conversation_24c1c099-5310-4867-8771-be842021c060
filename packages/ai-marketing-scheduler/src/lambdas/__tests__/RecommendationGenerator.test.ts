import { getConfig } from '../../config';
import { serviceFactory } from '../../factories/UnifiedServiceFactory';
import {
  ScrapedPageType,
  ScraperResult,
  WorkflowType,
  Status,
} from '../../types';
import {
  createMockLambdaContext,
  createMockConfig,
} from '../../utils/testUtils';
import { handler } from '../RecommendationGenerator';

// Mock the dependencies
jest.mock('../../factories/UnifiedServiceFactory');
jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));
jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

describe('RecommendationGenerator Lambda', () => {
  let mockLogger: any;
  const mockContext = createMockLambdaContext();

  const mockedAction = {
    id: 'test-scheduled-action-id',
    companyId: 'test-company-id',
    workflowType: WorkflowType.SEO,
    status: Status.DRAFT_PENDING,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    surfacedAt: null,
    scheduledToBeSurfacedAt: null,
    scheduledToBePublishedAt: null,
    publishedAt: null,
    contentPayload: {},
    generationPayload: {},
    failureReason: {},
    executionName: null,
    executionArn: null,
  };

  const mockScraperResult: ScraperResult = {
    firecrawlId: 'test-scrape-id',
    url: 'https://example.com/test',
    metaTitle: 'Current Meta Title',
    metaDescription: 'Current Meta Description',
    mainHeading: {
      section_id: 'test-section',
      element_id: 'test-element-1',
      tag: 'h1',
      index: 0,
      content: 'Current Main Heading',
    },
    type: ScrapedPageType.NEIGHBORHOOD_GUIDE,
    markdown:
      '# Test Content\n\nThis is a test content for SEO recommendations.',
    mediaId: 'test-media-id',
    companyId: 'test-company-id',
  };

  // Sample test event
  const mockEvent = {
    actionId: 'test-scheduled-action-id',
    scraperResult: mockScraperResult,
    keywords: 'test keyword',
  };

  // Sample recommendation response
  const mockRecommendation = {
    metaTitle: {
      currentValue: 'Current Meta Title',
      recommendationValue: 'New Meta Title',
      reasoning: 'Improved SEO value',
    },
    metaDescription: {
      currentValue: 'Current Meta Description',
      recommendationValue: 'New Meta Description',
      reasoning: 'Better description for search engines',
    },
    mainHeading: {
      currentValue: 'Current Main Heading',
      recommendationValue: 'New Main Heading',
      reasoning: 'More engaging heading',
    },
  };

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );
    (getConfig as jest.Mock).mockReturnValue(createMockConfig());
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should successfully generate recommendations', async () => {
    // Mock the prompt service
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockRecommendation,
      }),
    };
    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const result = await handler(mockEvent, mockContext);

    // Verify the result
    expect(result).toEqual({
      companyId: mockedAction.companyId,
      actionId: mockEvent.actionId,
      workflowType: WorkflowType.SEO,
      recommendations: mockRecommendation,
      metadata: {
        scraperResult: mockEvent.scraperResult,
        keywords: mockEvent.keywords,
      },
    });

    // Verify prompt service was called with correct inputs
    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['recommendations'],
    );

    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      {
        generationPayload: {
          recommendations: mockRecommendation,
        },
      },
    );

    expect(mockPromptService.executeChatPrompt).toHaveBeenCalledWith(
      'fake-recommendation-generator-prompt',
      {
        meta_title: mockEvent.scraperResult.metaTitle,
        meta_description: mockEvent.scraperResult.metaDescription,
        plaintext: mockEvent.scraperResult.markdown,
        primary_keyword: mockEvent.keywords,
        target_url: mockEvent.scraperResult.url,
        main_heading: mockEvent.scraperResult.mainHeading.content,
      },
      expect.any(Object), // SeoRecommendationSchema
    );

    // Verify logging
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Starting recommendation generator',
      { event: mockEvent },
    );
    expect(mockLogger.info).toHaveBeenCalledWith(
      'Recommendation generation completed',
      {
        metaTitle: mockRecommendation.metaTitle,
        metaDescription: mockRecommendation.metaDescription,
        mainHeading: mockRecommendation.mainHeading,
      },
    );
  });

  it('should skip recommendation generation if recommendations already exist', async () => {
    const mockedProcessedAction = {
      ...mockedAction,
      generationPayload: {
        recommendations: mockRecommendation,
      },
    };

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedProcessedAction,
        skipStep: true,
      }),
      update: jest.fn().mockResolvedValue(mockedProcessedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const mockPromptService = {
      executeChatPrompt: jest.fn(),
    };
    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const result = await handler(mockEvent, mockContext);

    expect(result).toEqual({
      companyId: mockedAction.companyId,
      recommendations: mockRecommendation,
      actionId: mockEvent.actionId,
      workflowType: WorkflowType.SEO,
      metadata: {
        scraperResult: mockEvent.scraperResult,
        keywords: mockEvent.keywords,
      },
    });

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['recommendations'],
    );

    expect(mockPromptService.executeChatPrompt).not.toHaveBeenCalled();
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
  });

  it('should handle prompt service errors', async () => {
    const errorMessage = 'Failed to generate recommendations';
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockRejectedValue(new Error(errorMessage)),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    await expect(handler(mockEvent, mockContext)).rejects.toThrow(errorMessage);

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Starting recommendation generator',
      { event: mockEvent },
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in recommendation-generator',
      {
        errorType: 'Error',
        error: errorMessage,
        stack: expect.any(String),
      },
    );
  });

  it('should handle missing page content', async () => {
    const contentMissingPage = {
      ...mockEvent.scraperResult,
      markdown: '',
    };
    const incompleteEvent = {
      actionId: mockEvent.actionId,
      scraperResult: contentMissingPage,
      keywords: mockEvent.keywords,
    };

    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockRecommendation,
      }),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    // Should still work as we don't validate content length
    const result = await handler(incompleteEvent, mockContext);

    expect(result).toEqual({
      companyId: mockedAction.companyId,
      actionId: mockEvent.actionId,
      workflowType: WorkflowType.SEO,
      recommendations: mockRecommendation,
      metadata: {
        scraperResult: contentMissingPage,
        keywords: mockEvent.keywords,
      },
    });
  });

  it('should handle missing meta information', async () => {
    const metaMissingScraperResult = {
      ...mockEvent.scraperResult,
      metaTitle: '',
      metaDescription: '',
      mainHeading: {
        section_id: 'test-section',
        element_id: 'test-element-1',
        tag: 'h1',
        index: 0,
        content: '',
      },
    };
    const incompleteEvent = {
      actionId: mockEvent.actionId,
      scraperResult: metaMissingScraperResult,
      keywords: mockEvent.keywords,
    };

    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockRecommendation,
      }),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    // Should still work as we don't validate content length
    const result = await handler(incompleteEvent, mockContext);

    expect(result).toEqual({
      companyId: mockedAction.companyId,
      actionId: mockEvent.actionId,
      workflowType: WorkflowType.SEO,
      recommendations: mockRecommendation,
      metadata: {
        scraperResult: metaMissingScraperResult,
        keywords: mockEvent.keywords,
      },
    });
  });
  it('should exclude mainHeading for HOMEPAGE page types', async () => {
    const homepageScraperResult = {
      ...mockScraperResult,
      type: ScrapedPageType.HOMEPAGE,
    };
    const homepageEvent = {
      ...mockEvent,
      scraperResult: homepageScraperResult,
    };

    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockRecommendation,
      }),
    };
    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const result = await handler(homepageEvent, mockContext);

    // Verify mainHeading is excluded from recommendations for HOMEPAGE
    const expectedRecommendations = {
      metaTitle: mockRecommendation.metaTitle,
      metaDescription: mockRecommendation.metaDescription,
    };

    expect(result.recommendations).toEqual(expectedRecommendations);
    expect(result.recommendations).not.toHaveProperty('mainHeading');

    // Verify the update call also excludes mainHeading
    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      {
        generationPayload: {
          recommendations: expectedRecommendations,
        },
      },
    );
  });

  it('should include mainHeading for non-HOMEPAGE page types', async () => {
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockRecommendation,
      }),
    };
    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const result = await handler(mockEvent, mockContext);

    // Verify mainHeading is included for non-HOMEPAGE pages
    expect(result.recommendations).toEqual(mockRecommendation);
    expect(result.recommendations).toHaveProperty('mainHeading');

    // Verify the update call includes mainHeading
    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      {
        generationPayload: {
          recommendations: mockRecommendation,
        },
      },
    );
  });

  it('should handle unknown errors', async () => {
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockRejectedValue('Unknown error type'),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    // The error should be re-thrown as is from handleHandlerError
    await expect(handler(mockEvent, mockContext)).rejects.toBe(
      'Unknown error type',
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['recommendations'],
    );

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Starting recommendation generator',
      { event: mockEvent },
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in recommendation-generator',
      {
        errorType: 'UnknownError',
        error: 'Unknown error type',
        stack: undefined,
      },
    );
  });
});
