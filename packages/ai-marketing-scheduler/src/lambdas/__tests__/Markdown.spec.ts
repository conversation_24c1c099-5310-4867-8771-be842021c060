import { cleanMarkdown } from '../../utils/Markdown';

describe('cleanMarkdown', () => {
  it('should remove markdown links but keep the text', async () => {
    const input = 'This is a [link](https://example.com) in markdown';
    const expected = 'This is a [link]() in markdown';
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });

  it('should remove markdown images but keep the alt text', async () => {
    const input = "Here's an image: ![alt text](image.jpg)";
    const expected = "Here's an image: ![alt text]()";
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });

  it('should decode HTML entities', async () => {
    const input = 'This &amp; that';
    const expected = 'This & that';
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });

  it('should remove extra backslashes', async () => {
    const input = 'This has \\extra\\ backslashes';
    const expected = 'This has extra backslashes';
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });

  it('should normalize multiple newlines to maximum of two', async () => {
    const input = 'First line\n\n\n\nSecond line\n\n\n\n\nThird line';
    const expected = 'First line\n\nSecond line\n\nThird line';
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });

  it('should handle empty input', async () => {
    const input = '';
    const expected = '';
    const result = await cleanMarkdown(input);
    expect(result).toBe(expected);
  });
});
