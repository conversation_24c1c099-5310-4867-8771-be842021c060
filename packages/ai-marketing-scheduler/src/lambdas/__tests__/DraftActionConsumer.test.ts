import { Logger } from '@aws-lambda-powertools/logger';
import { StartExecutionCommandInput } from '@aws-sdk/client-sfn';

import { getConfig } from '../../config';
import { serviceFactory } from '../../factories';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { WorkflowType } from '../../types';
import {
  setupTestMocks,
  createMockScheduledActionService,
  createMockSFNSend,
  createMockScheduledActions,
  createMockLambdaContext,
  createMockConfig,
} from '../../utils/testUtils';
import { handler } from '../DraftActionConsumerDaemon';

// Mock for SFNClient using centralized factory
const mockSend = createMockSFNSend();

// Mock AWS SDK
jest.mock('@aws-sdk/client-sfn', () => {
  return {
    SFNClient: jest.fn().mockImplementation(() => ({
      send: mockSend,
    })),
    StartExecutionCommand: jest
      .fn()
      .mockImplementation((input: StartExecutionCommandInput) => input),
  };
});

jest.mock('../../factories', () => ({
  UnifiedServiceFactory: jest.fn().mockImplementation(() => ({
    createScheduledActionService: jest.fn(),
    createSfnClient: jest.fn(),
  })),
  serviceFactory: {
    createScheduledActionService: jest.fn(),
    createSfnClient: jest.fn(),
  },
}));

jest.mock('../../factories/LoggerFactory', () => ({
  createContextLogger: jest.fn(),
  setAwsRequestId: jest.fn(),
  handleHandlerError: jest.fn().mockImplementation(error => {
    // Return a standard error response for handler errors
    return {
      processed: 0,
      successful: 0,
      failed: 0,
      results: [],
      error: error instanceof Error ? error.message : String(error),
    };
  }),
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

describe('draftActionConsumer handler', () => {
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;

  // Mock AWS Lambda context using centralized factory
  const mockContext = createMockLambdaContext();

  // Store the logger to be able to restore it in afterEach
  let mockLogger: Logger & { restore: () => void };

  beforeEach(() => {
    jest.clearAllMocks();

    // Set up test mocks including logger
    const mocks = setupTestMocks('draft-action-consumer');
    mockLogger = mocks.mockLogger;

    // Ensure createChild returns the logger itself
    (mockLogger as any).createChild = jest.fn().mockReturnValue(mockLogger);

    // Mock getConfig using centralized factory
    (getConfig as jest.Mock).mockReturnValue(createMockConfig());

    mockScheduledActionService = createMockScheduledActionService();

    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );
    (serviceFactory.createSfnClient as jest.Mock).mockReturnValue({
      send: mockSend,
    });
  });

  afterEach(() => {
    // Restore logger spies to prevent state bleed between tests
    mockLogger.restore();
  });

  it('should process draft pending actions successfully', async () => {
    const mockActions = createMockScheduledActions(2, [
      {
        id: 'action-1',
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
      },
      {
        id: 'action-2',
        companyId: 'company-2',
        workflowType: WorkflowType.BLOG,
      },
    ]);

    mockScheduledActionService.getDraftPendingActions.mockResolvedValue(
      mockActions,
    );

    // Use mockReturnValueOnce to return different values on sequential calls
    mockScheduledActionService.update
      .mockResolvedValueOnce(mockActions[0])
      .mockResolvedValueOnce(mockActions[1]);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.getDraftPendingActions,
    ).toHaveBeenCalledTimes(1);
    expect(mockSend).toHaveBeenCalledTimes(2);
    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(2);
    mockScheduledActionService.update.mock.calls.forEach((call, index) => {
      const actionId = mockActions[index].id;
      expect(call.length).toBe(2);
      expect(call[0]).toBe(actionId);
      const updateObject = call[1];
      expect(updateObject.executionName).toMatch(
        new RegExp(`${actionId}-\\d+`),
      );
      expect(updateObject.executionArn).toBe('mock-execution-arn');
    });
    expect(result).toEqual({
      processed: 2,
      successful: 2,
      failed: 0,
      results: [
        { id: 'action-1', success: true, workflowType: WorkflowType.SEO },
        { id: 'action-2', success: true, workflowType: WorkflowType.BLOG },
      ],
    });
  });

  it('should handle no draft pending actions', async () => {
    mockScheduledActionService.getDraftPendingActions.mockResolvedValue([]);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.getDraftPendingActions,
    ).toHaveBeenCalledTimes(1);
    expect(mockSend).not.toHaveBeenCalled();
    expect(mockScheduledActionService.updateStatus).not.toHaveBeenCalled();
    expect(result).toEqual({
      processed: 0,
      successful: 0,
      failed: 0,
      results: [],
    });
  });

  it('should handle errors during processing', async () => {
    const mockActions = createMockScheduledActions(1, [
      {
        id: 'action-1',
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
      },
    ]);

    mockScheduledActionService.getDraftPendingActions.mockResolvedValue(
      mockActions,
    );

    // The SFN client's send method should succeed
    // But the updateStatus call should fail
    mockScheduledActionService.update.mockRejectedValue(
      new Error('Update failed'),
    );

    // Mock updateWithFailure to return a failure result
    mockScheduledActionService.updateWithFailure.mockReturnValueOnce(
      Promise.resolve({
        id: 'action-1',
        success: false,
        error: 'Update failed',
        workflowType: WorkflowType.SEO,
      }),
    );

    // Reset the mockSend to ensure we can track calls
    mockSend.mockClear();

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.getDraftPendingActions,
    ).toHaveBeenCalledTimes(1);
    expect(mockSend).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledTimes(
      1,
    );
    expect(result).toEqual({
      processed: 1,
      successful: 0,
      failed: 1,
      results: [
        {
          id: 'action-1',
          success: false,
          error: 'Update failed',
          workflowType: WorkflowType.SEO,
        },
      ],
    });
  });

  it('should handle missing Step Function ARN error', async () => {
    const mockActions = createMockScheduledActions(1, [
      {
        id: 'action-1',
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
      },
    ]);

    mockScheduledActionService.getDraftPendingActions.mockResolvedValue(
      mockActions,
    );

    // Mock config with missing ARN
    const configWithMissingArn = createMockConfig();
    configWithMissingArn.stateMachine.seoDraftArn = ''; // Empty ARN will cause error
    (getConfig as jest.Mock).mockReturnValue(configWithMissingArn);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.getDraftPendingActions,
    ).toHaveBeenCalledTimes(1);
    expect(mockSend).not.toHaveBeenCalled(); // SFN should not be called
    expect(mockScheduledActionService.updateWithFailure).not.toHaveBeenCalled(); // updateWithFailure should not be called for missing ARN
    expect(result).toEqual({
      processed: 1,
      successful: 0,
      failed: 1,
      results: [
        {
          id: 'action-1',
          success: false,
          error: 'Missing Step Function ARN',
          workflowType: WorkflowType.SEO,
        },
      ],
    });
  });

  it('should handle Step Function execution failure', async () => {
    const mockActions = createMockScheduledActions(1, [
      {
        id: 'action-1',
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
      },
    ]);

    mockScheduledActionService.getDraftPendingActions.mockResolvedValue(
      mockActions,
    );

    // Mock SFN to throw an error
    const sfnError = new Error('Step Function execution failed');
    mockSend.mockRejectedValueOnce(sfnError);

    // Mock updateWithFailure to return a failure result
    mockScheduledActionService.updateWithFailure.mockReturnValueOnce(
      Promise.resolve({
        id: 'action-1',
        success: false,
        error:
          'Failed to start Step Function execution: Step Function execution failed',
        workflowType: WorkflowType.SEO,
      }),
    );

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.getDraftPendingActions,
    ).toHaveBeenCalledTimes(1);
    expect(mockSend).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledWith(
      'action-1',
      {
        error:
          'Failed to start Step Function execution: Step Function execution failed',
        errorType: 'StepFunctionExecutionError',
        stack: expect.any(String),
      },
      WorkflowType.SEO,
    );

    expect(result).toEqual({
      processed: 1,
      successful: 0,
      failed: 1,
      results: [
        {
          id: 'action-1',
          success: false,
          error:
            'Failed to start Step Function execution: Step Function execution failed',
          workflowType: WorkflowType.SEO,
        },
      ],
    });
  });
});
