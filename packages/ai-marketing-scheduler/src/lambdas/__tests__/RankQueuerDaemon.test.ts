import { Context } from 'aws-lambda';
import {
  createMockLambdaContext,
  createMockSeoApiGatewayClient,
} from 'src/utils/testUtils';

import { DataForSeoClient } from '../../clients/DataForSeoClient';
import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import { getConfig } from '../../config';
import { RankQueuerError } from '../../errors/RankQueuerError';
import { handler } from '../RankQueuerDaemon';

jest.mock('../../clients/DataForSeoClient');
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));
jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.fn(),
}));
jest.mock('../../factories', () => ({
  serviceFactory: {
    createSeoApiGatewayClient: jest.fn(),
  },
}));

const { createContextLogger } = jest.requireMock('../../logger/contextLogger');
const { handleHandlerError } = jest.requireMock(
  '../../factories/LoggerFactory',
);
const { serviceFactory } = jest.requireMock('../../factories');

describe('RankQueuerDaemon', () => {
  let mockDataForSeoClient: jest.Mocked<DataForSeoClient>;
  let mockSeoApiGatewayClient: jest.Mocked<ISeoApiGatewayClient>;
  let mockLogger: any;
  let mockConfig: any;
  let mockContext: Context;

  beforeEach(() => {
    jest.clearAllMocks();

    mockContext = createMockLambdaContext();
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };
    createContextLogger.mockReturnValue(mockLogger);

    mockConfig = {
      dataForSeo: {
        username: 'test-username',
        password: 'test-password',
        postbackUrl: 'https://test-postback.com',
        baseUrl: 'https://api.dataforseo.com',
      },
    };
    (getConfig as jest.Mock).mockReturnValue(mockConfig);

    mockDataForSeoClient = {
      enqueueTasks: jest.fn(),
    } as any;
    (DataForSeoClient as jest.Mock).mockImplementation(
      () => mockDataForSeoClient,
    );

    mockSeoApiGatewayClient = createMockSeoApiGatewayClient();
    serviceFactory.createSeoApiGatewayClient.mockReturnValue(
      mockSeoApiGatewayClient,
    );
  });

  describe('handler', () => {
    const validEvent = {
      keywords: [
        { keyword: 'test keyword 1', pageId: 'page-1' },
        { keyword: 'test keyword 2', pageId: 'page-2' },
      ],
    };

    it('should successfully enqueue tasks', async () => {
      const mockResult = [
        { keyword: 'test keyword 1', pageId: 'page-1', taskId: 'task-1' },
        { keyword: 'test keyword 2', pageId: 'page-2', taskId: 'task-2' },
      ];

      mockDataForSeoClient.enqueueTasks.mockResolvedValue(mockResult);

      const result = await handler(validEvent, mockContext);

      expect(DataForSeoClient).toHaveBeenCalledWith(
        mockConfig.dataForSeo,
        mockLogger,
      );
      expect(mockDataForSeoClient.enqueueTasks).toHaveBeenCalledWith(
        validEvent.keywords,
      );
      expect(result).toEqual(mockResult);
    });

    it('should throw error for invalid input - missing keyword', async () => {
      const invalidEvent = {
        keywords: [
          { keyword: '', pageId: 'page-1' },
          { keyword: 'test keyword 2', pageId: 'page-2' },
        ],
      };

      handleHandlerError.mockImplementation((error: any) => {
        throw error;
      });

      await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
        RankQueuerError,
      );
      await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
        'Invalid input: each keyword must have a keyword and pageId',
      );
      expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    });

    it('should throw error for invalid input - missing pageId', async () => {
      const invalidEvent = {
        keywords: [
          { keyword: 'test keyword 1', pageId: '' },
          { keyword: 'test keyword 2', pageId: 'page-2' },
        ],
      };

      handleHandlerError.mockImplementation((error: any) => {
        throw error;
      });

      await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
        RankQueuerError,
      );
      await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
        'Invalid input: each keyword must have a keyword and pageId',
      );
      expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    });

    // TODO put back after batching
    // it('should throw error for too many keywords', async () => {
    //   const keywords = Array.from({ length: 101 }, (_, i) => ({
    //     keyword: `keyword ${i}`,
    //     pageId: `page-${i}`,
    //   }));

    //   const invalidEvent = { keywords };

    //   handleHandlerError.mockImplementation((error: any) => {
    //     throw error;
    //   });

    //   await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
    //     RankQueuerError,
    //   );
    // await expect(handler(invalidEvent, mockContext)).rejects.toThrow(
    //   'Maximum 100 keywords allowed, received 101',
    // );
    //   expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    // });

    it('should handle DataForSeoClient errors', async () => {
      const error = new Error('DataForSeo API error');
      mockDataForSeoClient.enqueueTasks.mockRejectedValue(error);

      handleHandlerError.mockImplementation((error: any) => {
        throw error;
      });

      await expect(handler(validEvent, mockContext)).rejects.toThrow(
        'DataForSeo API error',
      );
      expect(mockDataForSeoClient.enqueueTasks).toHaveBeenCalledWith(
        validEvent.keywords,
      );
    });

    it('should throw error for empty keywords array', async () => {
      const emptyEvent = { keywords: [] };

      await expect(handler(emptyEvent, mockContext)).rejects.toThrow(
        'RankQueuerDaemon requires keywords to be provided',
      );

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Missing required keywords in event',
        expect.objectContaining({
          event: emptyEvent,
          error: expect.stringContaining('RankQueuerDaemon requires keywords'),
        }),
      );
      expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    });

    it('should throw error when keywords are not provided', async () => {
      const eventWithoutKeywords = {};

      await expect(
        handler(eventWithoutKeywords as any, mockContext),
      ).rejects.toThrow('RankQueuerDaemon requires keywords to be provided');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Missing required keywords in event',
        expect.objectContaining({
          event: eventWithoutKeywords,
          error: expect.stringContaining('RankQueuerDaemon requires keywords'),
        }),
      );
      expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    });

    it('should throw error when keywords is undefined', async () => {
      const eventWithUndefinedKeywords = { keywords: undefined };

      await expect(
        handler(eventWithUndefinedKeywords as any, mockContext),
      ).rejects.toThrow('RankQueuerDaemon requires keywords to be provided');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Missing required keywords in event',
        expect.objectContaining({
          event: eventWithUndefinedKeywords,
          error: expect.stringContaining('RankQueuerDaemon requires keywords'),
        }),
      );
      expect(mockDataForSeoClient.enqueueTasks).not.toHaveBeenCalled();
    });

    it('should handle exactly 100 keywords', async () => {
      const keywords = Array.from({ length: 100 }, (_, i) => ({
        keyword: `keyword ${i}`,
        pageId: `page-${i}`,
      }));

      const validEvent = { keywords };
      const mockResult = keywords.map((k, i) => ({
        ...k,
        taskId: `task-${i}`,
      }));

      mockDataForSeoClient.enqueueTasks.mockResolvedValue(mockResult);

      const result = await handler(validEvent, mockContext);

      expect(result).toEqual(mockResult);
      expect(mockDataForSeoClient.enqueueTasks).toHaveBeenCalledWith(keywords);
    });

    it('should handle distributed map format with single keyword', async () => {
      const singleKeywordEvent = {
        keyword: 'single keyword',
        pageId: 'page-1',
      };

      const mockResult = [
        { keyword: 'single keyword', pageId: 'page-1', taskId: 'task-1' },
      ];
      mockDataForSeoClient.enqueueTasks.mockResolvedValue(mockResult);

      const result = await handler(singleKeywordEvent, mockContext);

      expect(result).toEqual(mockResult);
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Using single keyword from distributed map',
        {
          keyword: 'single keyword',
          pageId: 'page-1',
        },
      );
      expect(mockDataForSeoClient.enqueueTasks).toHaveBeenCalledWith([
        { keyword: 'single keyword', pageId: 'page-1' },
      ]);
    });

    it('should handle DataForSeoClient errors with distributed map format', async () => {
      const singleKeywordEvent = {
        keyword: 'test keyword',
        pageId: 'page-1',
      };
      const error = new Error('DataForSeo API error');

      mockDataForSeoClient.enqueueTasks.mockRejectedValue(error);
      handleHandlerError.mockImplementation((error: any) => {
        throw error;
      });

      await expect(handler(singleKeywordEvent, mockContext)).rejects.toThrow(
        error,
      );
      expect(mockDataForSeoClient.enqueueTasks).toHaveBeenCalledWith([
        { keyword: 'test keyword', pageId: 'page-1' },
      ]);
    });
  });
});
