// Mock snowflake-sdk before any imports
jest.mock('snowflake-sdk', () => ({
  createPool: jest.fn(),
}));

import { Context } from 'aws-lambda';
import { ISeoApiGatewayClient } from 'src/clients/interfaces/ISeoApiGatewayClient';
import { StoredScrapedPage } from 'src/clients/types/seoApiGateway';

jest.mock('../../config', () => ({
  getConfig: jest.fn(),
  getSnowflakeConfigAsync: jest.fn(),
}));

import { FirecrawlClient } from '../../clients/FirecrawlClient';
import { getConfig } from '../../config';
import { serviceFactory } from '../../factories';
import { WorkflowType, Status, ScrapedPageType } from '../../types';
import * as normalizeUrlModule from '../../utils/normalizeUrl';
import {
  createMockConfig,
  createMockSeoApiGatewayClient,
} from '../../utils/testUtils';
import { handler } from '../UrlSelector';

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    setContext: jest.fn(),
  }),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));

jest.mock('../../factories', () => ({
  serviceFactory: {
    createSeoApiGatewayClient: jest.fn(),
    createScheduledActionService: jest.fn(),
  },
}));

jest.mock('../../clients/FirecrawlClient', () => ({
  FirecrawlClient: jest.fn(),
}));

jest.mock('../../clients/SnowflakeClient', () => ({
  SnowflakeClient: jest.fn().mockImplementation(() => ({
    queryWithRetries: jest.fn().mockResolvedValue([
      {
        PAGE_URL: 'https://example.com/blog/post1',
        TOTAL_IMPRESSIONS: 1000,
        AVERAGE_SEARCH_POSITION: 2,
      },
      {
        PAGE_URL: 'https://example.com/agents/john-doe',
        TOTAL_IMPRESSIONS: 500,
        AVERAGE_SEARCH_POSITION: 3,
      },
    ]),
    destroy: jest.fn(),
  })),
}));

const mockedAction = {
  id: 'test-scheduled-action-id',
  companyId: 'test-company-id',
  workflowType: WorkflowType.SEO,
  status: Status.DRAFT_PENDING,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  surfacedAt: null,
  scheduledToBeSurfacedAt: null,
  scheduledToBePublishedAt: null,
  publishedAt: null,
  contentPayload: {},
  generationPayload: {},
  failureReason: {},
  executionName: null,
  executionArn: null,
};

describe('UrlSelector', () => {
  let mockLogger: any;
  let mockContext: Context;
  let mockSeoApiGatewayClient: jest.Mocked<ISeoApiGatewayClient>;
  let mockFirecrawlClient: jest.Mocked<any>;
  let mockScheduledActionService: jest.Mocked<any>;

  beforeEach(() => {
    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      setContext: jest.fn(),
    };

    mockSeoApiGatewayClient = createMockSeoApiGatewayClient();
    mockFirecrawlClient = {
      mapUrl: jest.fn(),
    };

    (FirecrawlClient as jest.Mock).mockImplementation(
      () => mockFirecrawlClient,
    );

    (getConfig as jest.Mock).mockReturnValue(createMockConfig());

    (serviceFactory.createSeoApiGatewayClient as jest.Mock).mockReturnValue(
      mockSeoApiGatewayClient,
    );

    mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn(),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return error when URL is missing', async () => {
    const errorMessage = 'URL is required';

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          companyId: 'test-company',
          generationPayload: {
            websiteUrl: '',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when companyId is missing', async () => {
    const errorMessage = 'Company ID is required';
    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          companyId: '',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when actionId is missing', async () => {
    const errorMessage = 'Action ID is required';
    await expect(
      handler(
        {
          actionId: '',
          companyId: 'test-company',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });

    expect(mockScheduledActionService.checkAction).not.toHaveBeenCalled();
  });

  it('should return error when action is not found', async () => {
    const errorMessage = 'Action not found';
    mockScheduledActionService.checkAction.mockResolvedValueOnce({
      action: null,
      skipStep: false,
    });

    await expect(
      handler(
        {
          actionId: 'non-existent-action-id',
          companyId: 'test-company',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'non-existent-action-id',
      ['selectedUrl'],
    );

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when no eligible URLs found', async () => {
    const errorMessage = 'No eligible URLs found for processing';
    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/random',
      'https://example.com/properties/123',
      'https://example.com/other',
    ]);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should skip step if selectedUrl already exists and is still eligible', async () => {
    const testUrl = 'https://example.com/blog/post1';
    const testCompanyId = 'test-company';

    const mockedProcessedAction = {
      ...mockedAction,
      generationPayload: {
        selectedUrl: testUrl,
      },
    };

    mockScheduledActionService.checkAction.mockResolvedValueOnce({
      action: mockedProcessedAction,
      skipStep: true,
    });

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: 'https://example.com',
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();

    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        selectedUrl: testUrl,
      },
    });

    expect(mockFirecrawlClient.mapUrl).not.toHaveBeenCalled();
    expect(mockSeoApiGatewayClient.getPagesByCompanyId).toHaveBeenCalledTimes(
      1,
    );
  });

  it('should successfully select URL and return it', async () => {
    const testUrl = 'https://example.com';
    const testCompanyId = 'test-company';

    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/blog/post1',
      'https://example.com/agents/john-doe',
    ]);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: testUrl,
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );

    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        selectedUrl: expect.any(String),
      },
    });

    // Verify the selected URL is one of the expected URLs
    expect([
      'https://example.com/blog/post1',
      'https://example.com/agents/john-doe',
    ]).toContain(result.generationPayload.selectedUrl);

    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update.mock.calls[0][0]).toBe(
      'test-scheduled-action-id',
    );
    expect(mockScheduledActionService.update.mock.calls[0][1]).toMatchObject({
      generationPayload: {
        selectedUrl: result.generationPayload.selectedUrl,
      },
    });

    expect(mockFirecrawlClient.mapUrl).toHaveBeenCalledWith(testUrl);
    expect(mockSeoApiGatewayClient.getPagesByCompanyId).toHaveBeenCalledWith(
      testCompanyId,
    );
  });

  it('should reset action if selected URL was already processed', async () => {
    const testUrl = 'https://example.com/blog/post1';
    const testCompanyId = 'test-company';

    const fakePageRecord: StoredScrapedPage = {
      id: '123',
      url: testUrl,
      companyId: testCompanyId,
      pageName: 'Blog Post Title',
      pageType: ScrapedPageType.BLOG,
      metadata: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      recommendations: [],
      scrapes: [],
      pageKeywordHistory: [],
      pageKeywords: [],
      deletedAt: null,
    };

    const mockedProcessedAction = {
      ...mockedAction,
      generationPayload: {
        selectedUrl: testUrl,
      },
    };

    mockScheduledActionService.checkAction.mockResolvedValueOnce({
      action: mockedProcessedAction,
      skipStep: true,
    });

    // First call returns existing page, second call returns empty array
    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([
      fakePageRecord,
    ]);
    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/agents/john-doe',
    ]);

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: 'https://example.com',
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );

    // Once for reset and once for the update with new selected URL
    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(2);

    expect(mockSeoApiGatewayClient.getPagesByCompanyId).toHaveBeenCalledTimes(
      2,
    );

    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        selectedUrl: 'https://example.com/agents/john-doe',
      },
    });
  });

  it('should return error when Firecrawl mapUrl fails', async () => {
    const errorMessage = 'Failed to map website';
    mockFirecrawlClient.mapUrl.mockRejectedValueOnce(
      new Error('firecrawl map failed'),
    );

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'FireCrawlError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when fetching existing pages fails', async () => {
    const errorMessage = 'Failed to fetch existing scraped pages';
    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/blog/post1',
    ]);
    mockSeoApiGatewayClient.getPagesByCompanyId.mockRejectedValueOnce(
      new Error('fetch failed'),
    );

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-selector', {
      errorType: 'UrlSelectorError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should exit successfully when all URLs have rank 1', async () => {
    const testUrl = 'https://example.com';
    const testCompanyId = 'test-company';

    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/blog/post1',
      'https://example.com/agents/john-doe',
    ]);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    // Mock Snowflake to return data with rank 1 for all URLs
    const { SnowflakeClient } = jest.requireMock(
      '../../clients/SnowflakeClient',
    );
    (SnowflakeClient as jest.Mock).mockImplementationOnce(() => ({
      queryWithRetries: jest.fn().mockResolvedValue([
        {
          PAGE_URL: 'https://example.com/blog/post1',
          TOTAL_IMPRESSIONS: 1000,
          AVERAGE_SEARCH_POSITION: 1,
        },
        {
          PAGE_URL: 'https://example.com/agents/john-doe',
          TOTAL_IMPRESSIONS: 500,
          AVERAGE_SEARCH_POSITION: 1,
        },
      ]),
      destroy: jest.fn(),
    }));

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: testUrl,
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['selectedUrl'],
    );

    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        websiteUrl: testUrl,
      },
    });
    expect(result.generationPayload.selectedUrl).toBeUndefined();

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.warn).toHaveBeenCalledWith(
      'No URL selected for processing - all URLs either have rank 1 or no ranking data available. Exiting successfully.',
    );
  });

  it('should normalize URLs and deduplicate them after mapUrl', async () => {
    const testUrl = 'https://example.com';
    const testCompanyId = 'test-company';

    const normalizeUrlSpy = jest
      .spyOn(normalizeUrlModule, 'normalizeUrl')
      .mockImplementation((url: string) => {
        if (url === 'http://example.com/blog/post1') {
          return 'https://example.com/blog/post1';
        }
        if (url === 'https://example.com/blog/post1') {
          return 'https://example.com/blog/post1';
        }
        if (url === 'https://www.example.com/agents/john-doe') {
          return 'https://example.com/agents/john-doe';
        }
        if (url === 'https://example.com/agents/john-doe') {
          return 'https://example.com/agents/john-doe';
        }
        return normalizeUrlModule.normalizeUrl(url);
      });

    const inputUrls = [
      'http://example.com/blog/post1',
      'https://example.com/blog/post1',
      'https://www.example.com/agents/john-doe',
      'https://example.com/agents/john-doe',
    ];
    mockFirecrawlClient.mapUrl.mockResolvedValueOnce(inputUrls);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce([]);

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: testUrl,
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockFirecrawlClient.mapUrl).toHaveBeenCalledWith(testUrl);
    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        selectedUrl: expect.any(String),
      },
    });

    // Verify that normalizeUrl was called for each input URL
    expect(normalizeUrlSpy).toHaveBeenCalledTimes(4);
    expect(normalizeUrlSpy).toHaveBeenCalledWith(
      'http://example.com/blog/post1',
    );
    expect(normalizeUrlSpy).toHaveBeenCalledWith(
      'https://example.com/blog/post1',
    );
    expect(normalizeUrlSpy).toHaveBeenCalledWith(
      'https://www.example.com/agents/john-doe',
    );
    expect(normalizeUrlSpy).toHaveBeenCalledWith(
      'https://example.com/agents/john-doe',
    );

    // After deduplication, should have 2 unique URLs
    const expectedUrls = [
      'https://example.com/blog/post1',
      'https://example.com/agents/john-doe',
    ];

    expect(result.generationPayload.selectedUrl).toMatch(/^https:/);
    expect(expectedUrls).toContain(result.generationPayload.selectedUrl);

    normalizeUrlSpy.mockRestore();
  });

  it('should treat HTTP and HTTPS URLs as duplicates during deduplication', async () => {
    const testUrl = 'https://example.com';
    const testCompanyId = 'test-company';

    const existingPages = [
      {
        id: '1',
        url: 'http://example.com/blog/post1',
        companyId: testCompanyId,
        pageName: 'Blog Post 1',
        pageType: ScrapedPageType.BLOG,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        recommendations: [],
        scrapes: [],
        pageKeywordHistory: [],
        pageKeywords: [],
        deletedAt: null,
      },
      {
        id: '2',
        url: 'https://example.com/agents/john-doe',
        companyId: testCompanyId,
        pageName: 'Agent Bio',
        pageType: ScrapedPageType.AGENT_BIO,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        recommendations: [],
        scrapes: [],
        pageKeywordHistory: [],
        pageKeywords: [],
        deletedAt: null,
      },
    ];

    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/blog/post1',
      'http://example.com/agents/john-doe',
      'https://example.com/neighborhoods/downtown',
    ]);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce(
      existingPages,
    );

    // Mock Snowflake to return data for the neighborhood guide URL
    const { SnowflakeClient } = jest.requireMock(
      '../../clients/SnowflakeClient',
    );
    (SnowflakeClient as jest.Mock).mockImplementationOnce(() => ({
      queryWithRetries: jest.fn().mockResolvedValue([
        {
          PAGE_URL: 'https://example.com/neighborhoods/downtown',
          TOTAL_IMPRESSIONS: 1000,
          AVERAGE_SEARCH_POSITION: 2,
        },
      ]),
      destroy: jest.fn(),
    }));

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          websiteUrl: testUrl,
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(result).toMatchObject({
      actionId: 'test-scheduled-action-id',
      generationPayload: {
        selectedUrl: 'https://example.com/neighborhoods/downtown',
      },
    });

    expect(mockSeoApiGatewayClient.getPagesByCompanyId).toHaveBeenCalledWith(
      testCompanyId,
    );
  });

  it('should handle case where no eligible URLs remain after HTTP/HTTPS deduplication', async () => {
    const testUrl = 'https://example.com';
    const testCompanyId = 'test-company';

    const existingPages = [
      {
        id: '1',
        url: 'http://example.com/blog/post1',
        companyId: testCompanyId,
        pageName: 'Blog Post 1',
        pageType: ScrapedPageType.BLOG,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        recommendations: [],
        scrapes: [],
        pageKeywordHistory: [],
        pageKeywords: [],
        deletedAt: null,
      },
    ];

    mockFirecrawlClient.mapUrl.mockResolvedValueOnce([
      'https://example.com/blog/post1',
    ]);

    mockSeoApiGatewayClient.getPagesByCompanyId.mockResolvedValueOnce(
      existingPages,
    );

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            websiteUrl: testUrl,
          },
          companyId: testCompanyId,
        },
        mockContext,
      ),
    ).rejects.toThrow('No eligible URLs found for processing');

    expect(mockSeoApiGatewayClient.getPagesByCompanyId).toHaveBeenCalledWith(
      testCompanyId,
    );
  });
});
