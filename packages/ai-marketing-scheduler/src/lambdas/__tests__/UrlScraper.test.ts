import { Context } from 'aws-lambda';
import { mainHeading as mainHeadingScript } from 'src/utils/getMainHeading';

jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

import { FirecrawlClient } from '../../clients/FirecrawlClient';
import { S3Client } from '../../clients/S3Client';
import { getConfig } from '../../config';
import { serviceFactory } from '../../factories';
import { WorkflowType, Status, ScrapedPageType } from '../../types';
import { createMockConfig } from '../../utils/testUtils';
import { handler } from '../UrlScraper';

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    setContext: jest.fn(),
  }),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));

jest.mock('../../factories', () => ({
  serviceFactory: {
    createScheduledActionService: jest.fn(),
  },
}));

jest.mock('../../clients/FirecrawlClient', () => ({
  FirecrawlClient: jest.fn(),
}));

jest.mock('../../clients/S3Client', () => ({
  S3Client: jest.fn(),
}));

const mockedAction = {
  id: 'test-scheduled-action-id',
  companyId: 'test-company-id',
  workflowType: WorkflowType.SEO,
  status: Status.DRAFT_PENDING,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  surfacedAt: null,
  scheduledToBeSurfacedAt: null,
  scheduledToBePublishedAt: null,
  publishedAt: null,
  contentPayload: {},
  generationPayload: {},
  failureReason: {},
  executionName: null,
  executionArn: null,
};

describe('UrlScraper', () => {
  let mockLogger: any;
  let mockContext: Context;
  let mockFirecrawlClient: jest.Mocked<any>;
  let mockScheduledActionService: jest.Mocked<any>;
  let mockS3Client: jest.Mocked<any>;

  beforeEach(() => {
    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      setContext: jest.fn(),
    };

    mockFirecrawlClient = {
      scrapeUrl: jest.fn(),
    };

    (FirecrawlClient as jest.Mock).mockImplementation(
      () => mockFirecrawlClient,
    );

    mockS3Client = {
      uploadFromUrl: jest.fn().mockResolvedValue({}),
    };
    (S3Client as jest.Mock).mockImplementation(() => mockS3Client);

    (getConfig as jest.Mock).mockReturnValue(createMockConfig());

    mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn(),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return error when selectedUrl is missing', async () => {
    const errorMessage = 'Selected URL is required';

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          companyId: 'test-company',
          generationPayload: {
            selectedUrl: '',
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'UrlScraperError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when companyId is missing', async () => {
    const errorMessage = 'Company ID is required';
    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          companyId: '',
          generationPayload: {
            selectedUrl: 'https://example.com/blog/post1',
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'UrlScraperError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when actionId is missing', async () => {
    const errorMessage = 'Action ID is required';
    await expect(
      handler(
        {
          actionId: '',
          companyId: 'test-company',
          generationPayload: {
            selectedUrl: 'https://example.com/blog/post1',
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'UrlScraperError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });

    expect(mockScheduledActionService.checkAction).not.toHaveBeenCalled();
  });

  it('should return error when action is not found', async () => {
    const errorMessage = 'Action not found';
    mockScheduledActionService.checkAction.mockResolvedValueOnce({
      action: null,
      skipStep: false,
    });

    await expect(
      handler(
        {
          actionId: 'non-existent-action-id',
          companyId: 'test-company',
          generationPayload: {
            selectedUrl: 'https://example.com/blog/post1',
            websiteUrl: 'https://example.com',
          },
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'non-existent-action-id',
      ['scraperResult'],
    );

    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'UrlScraperError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should skip step if scraperResult already exists', async () => {
    const testUrl = 'https://example.com/blog/post1';
    const testCompanyId = 'test-company';

    const scraperResult = {
      url: testUrl,
      companyId: testCompanyId,
      mainHeading: {
        tag: 'h1',
        index: 0,
        content: 'Blog Post Title',
      },
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      mediaId: 'test-media-id',
      metaTitle: 'Test Title',
      metaDescription: 'Test Description',
      type: ScrapedPageType.BLOG,
    };

    const mockedProcessedAction = {
      ...mockedAction,
      generationPayload: {
        scraperResult,
      },
    };

    mockScheduledActionService.checkAction.mockResolvedValueOnce({
      action: mockedProcessedAction,
      skipStep: true,
    });

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          selectedUrl: testUrl,
          websiteUrl: 'https://example.com',
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['scraperResult'],
    );

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();

    expect(result).toMatchObject({
      scraperResult,
      actionId: 'test-scheduled-action-id',
    });

    expect(mockFirecrawlClient.scrapeUrl).not.toHaveBeenCalled();
  });

  it('should successfully scrape URL and return scraper result', async () => {
    const testUrl = 'https://example.com/blog/post1';
    const testCompanyId = 'test-company';

    mockFirecrawlClient.scrapeUrl.mockResolvedValueOnce({
      success: true,
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      screenshot: 'mock-screenshot-url',
      actions: {
        javascriptReturns: [
          {
            type: 'executeJavascript',
            value: {
              tag: 'h1',
              index: 0,
              content: 'Blog Post Title',
            },
          },
        ],
      },
      metadata: {
        title: 'Test Page Title',
        description: 'Test Description',
      },
    });

    const result = await handler(
      {
        actionId: 'test-scheduled-action-id',
        generationPayload: {
          selectedUrl: testUrl,
          websiteUrl: 'https://example.com',
        },
        companyId: testCompanyId,
      },
      mockContext,
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['scraperResult'],
    );

    const expectedScrapedPage = {
      companyId: testCompanyId,
      mainHeading: {
        tag: 'h1',
        index: 0,
        content: 'Blog Post Title',
      },
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      mediaId: expect.any(String),
      metaTitle: 'Test Page Title',
      metaDescription: 'Test Description',
      type: ScrapedPageType.BLOG,
      url: testUrl,
    };

    expect(result).toMatchObject({
      scraperResult: expectedScrapedPage,
      actionId: 'test-scheduled-action-id',
    });

    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update.mock.calls[0][0]).toBe(
      'test-scheduled-action-id',
    );
    expect(mockScheduledActionService.update.mock.calls[0][1]).toMatchObject({
      generationPayload: {
        scraperResult: expectedScrapedPage,
      },
    });

    expect(mockFirecrawlClient.scrapeUrl).toHaveBeenCalledWith(
      testUrl,
      mainHeadingScript,
    );
    expect(mockS3Client.uploadFromUrl).toHaveBeenCalledWith(
      'mock-screenshot-url',
      expect.stringMatching(/^full\/.*\.png$/),
    );
  });

  it('should return error when Firecrawl scrapeUrl fails', async () => {
    const errorMessage = 'Failed to scrape URL';
    const testUrl = 'https://example.com/blog/post1';

    mockFirecrawlClient.scrapeUrl.mockRejectedValueOnce(
      new Error('scrape failed'),
    );

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            selectedUrl: testUrl,
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'FireCrawlError',
      error: errorMessage,
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when S3 upload fails', async () => {
    const testUrl = 'https://example.com/blog/post1';

    mockFirecrawlClient.scrapeUrl.mockResolvedValueOnce({
      success: true,
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      screenshot: 'mock-screenshot-url',
      actions: {
        javascriptReturns: [
          {
            type: 'executeJavascript',
            value: {
              tag: 'h1',
              index: 0,
              content: 'Blog Post Title',
            },
          },
        ],
      },
      metadata: {
        title: 'Test Page Title',
        description: 'Test Description',
      },
    });

    // Mock S3 upload to fail
    mockS3Client.uploadFromUrl.mockRejectedValueOnce(
      new Error('upload failed'),
    );

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            selectedUrl: testUrl,
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow('Failed to upload screenshot');

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'S3Error',
      error: 'Failed to upload screenshot',
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });

  it('should return error when no main heading is found', async () => {
    const testUrl = 'https://example.com/blog/post1';
    const errorMessage = `No main heading found in scrape response for ${testUrl}`;

    mockFirecrawlClient.scrapeUrl.mockResolvedValueOnce({
      success: true,
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      screenshot: 'mock-screenshot-url',
      actions: {
        javascriptReturns: [],
      },
      metadata: {
        title: 'Test Page Title',
        description: 'Test Description',
      },
    });

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            selectedUrl: testUrl,
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow(errorMessage);

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'FireCrawlError',
      error: errorMessage,
      stack: expect.any(String),
      metadata: expect.any(Object),
    });
  });

  it('should return error when empty title is returned', async () => {
    const testUrl = 'https://example.com/blog/post1';

    mockFirecrawlClient.scrapeUrl.mockResolvedValueOnce({
      success: true,
      markdown: '# Test Content',
      firecrawlId: 'test-scrape-id',
      screenshot: 'mock-screenshot-url',
      actions: {
        javascriptReturns: [
          {
            type: 'executeJavascript',
            value: {
              tag: 'h1',
              index: 0,
              content: 'Blog Post Title',
            },
          },
        ],
      },
      metadata: {
        title: '',
        description: 'Test Description',
      },
    });

    await expect(
      handler(
        {
          actionId: 'test-scheduled-action-id',
          generationPayload: {
            selectedUrl: testUrl,
            websiteUrl: 'https://example.com',
          },
          companyId: 'test-company',
        },
        mockContext,
      ),
    ).rejects.toThrow('Empty title returned from firecrawl');

    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(mockLogger.error).toHaveBeenCalledWith('Error in url-scraper', {
      errorType: 'FireCrawlError',
      error: 'Empty title returned from firecrawl',
      metadata: expect.any(Object),
      stack: expect.any(String),
    });
  });
});
