import { serviceFactory } from '../../factories';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import {
  setupTestMocks,
  createMockScheduledActionService,
  createMockLambdaContext,
} from '../../utils/testUtils';
import { handler } from '../CatchFailure';

// Mock AWS SDK
jest.mock('@aws-sdk/client-sfn', () => {
  return {
    SFNClient: jest.fn().mockImplementation(() => ({
      send: jest.fn(),
    })),
  };
});

jest.mock('../../factories', () => ({
  UnifiedServiceFactory: jest.fn().mockImplementation(() => ({
    createScheduledActionService: jest.fn(),
    createSfnClient: jest.fn(),
  })),
  serviceFactory: {
    createScheduledActionService: jest.fn(),
    createSfnClient: jest.fn(),
  },
}));

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
  }),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.fn(),
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn().mockReturnValue({}),
}));

describe('catchFailure handler', () => {
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;

  // Mock AWS Lambda context using centralized factory
  const mockContext = createMockLambdaContext();

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    setupTestMocks();

    // Create mock service with default implementation
    mockScheduledActionService = createMockScheduledActionService();

    // Configure factory to return our mock service
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );
  });

  it('should process failure and update action status', async () => {
    // Arrange
    const event = {
      actionId: 'test-action-id',
      error: {
        Error: 'Test error',
        Cause:
          '{"errorType":"Error","errorMessage":"boom!","stackTrace":["at Runtime.handler (/var/task/index.js:10:15)","…"]}',
      },
    };

    const expectedError = JSON.parse(event.error.Cause);

    mockScheduledActionService.updateWithFailure.mockResolvedValueOnce({
      id: event.actionId,
      success: true,
    });

    // Act
    await handler(event, mockContext);

    // Assert
    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledTimes(
      1,
    );
    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledWith(
      event.actionId,
      expectedError,
    );
  });

  it('should log that FAILED status is being set after retries exhausted', async () => {
    // Arrange
    const mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
    };

    const { createContextLogger } = jest.requireMock(
      '../../logger/contextLogger',
    );
    (createContextLogger as jest.Mock).mockReturnValue(mockLogger);

    const event = {
      actionId: 'test-action-id-for-logging',
      error: {
        Error: 'Step Function retry exhausted',
        Cause: '{"errorType":"Error","errorMessage":"All retries failed"}',
      },
    };

    mockScheduledActionService.updateWithFailure.mockResolvedValueOnce({
      id: event.actionId,
      success: true,
    });

    // Act
    await handler(event, mockContext);

    expect(mockLogger.info).toHaveBeenCalledWith(
      'Setting scheduled action to FAILED status after retries exhausted',
      {
        actionId: event.actionId,
        error: event.error,
      },
    );

    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledTimes(
      1,
    );
  });

  it('should handle Publisher errors after retries are exhausted', async () => {
    const event = {
      actionId: 'test-action-id-publisher-error',
      error: {
        Error: 'PublisherError',
        Cause:
          '{"errorType":"PublisherError","errorMessage":"Some updates failed: {\\"metaDescription\\":\\"Failed to update meta description\\"}"}',
      },
    };

    mockScheduledActionService.updateWithFailure.mockResolvedValueOnce({
      id: event.actionId,
      success: true,
    });

    await handler(event, mockContext);

    expect(mockScheduledActionService.updateWithFailure).toHaveBeenCalledWith(
      event.actionId,
      {
        errorType: 'PublisherError',
        errorMessage:
          'Some updates failed: {"metaDescription":"Failed to update meta description"}',
      },
    );
  });
});
