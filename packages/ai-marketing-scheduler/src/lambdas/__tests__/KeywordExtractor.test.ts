import { Context } from 'aws-lambda';
import { getConfig } from 'src/config';

import { serviceFactory } from '../../factories/UnifiedServiceFactory';
import {
  ScrapedPageType,
  ScraperResult,
  Status,
  WorkflowType,
} from '../../types';
import { createMockConfig } from '../../utils/testUtils';
import { handler } from '../KeywordExtractor';

// Mock the dependencies
jest.mock('../../factories/UnifiedServiceFactory');
jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));
// Only mock handleHandlerError but use the real one
jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));
jest.mock('../../services/PromptService');
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));
jest.mock('langfuse', () => ({
  Langfuse: jest.fn().mockImplementation(() => ({
    getPrompt: jest.fn().mockResolvedValue({
      prompt: [
        {
          role: 'system',
          content: 'You are a helpful assistant',
        },
        {
          role: 'user',
          content: 'Extract keywords from the following content: {{markdown}}',
        },
      ],
    }),
  })),
  TextPromptClient: jest.fn(),
  ChatPromptClient: jest.fn(),
}));
jest.mock('langfuse-langchain', () => ({
  CallbackHandler: jest.fn().mockImplementation(() => ({
    handleChainStart: jest.fn(),
    handleChainEnd: jest.fn(),
    handleLLMStart: jest.fn(),
    handleLLMEnd: jest.fn(),
  })),
}));

describe('KeywordExtractor Lambda', () => {
  let mockContext: Context;

  const mockScraperResult: ScraperResult = {
    firecrawlId: 'test-scrape-id',
    url: 'https://example.com/test',
    metaTitle: 'Test Page',
    metaDescription: 'Test Description',
    mainHeading: {
      section_id: 'test-section',
      element_id: 'test-element-1',
      tag: 'h1',
      index: 0,
      content: 'Test Heading',
    },
    type: ScrapedPageType.BUYERS_GUIDE,
    markdown: '# Test Content\nThis is a test content for keyword extraction.',
    mediaId: 'test-media-id',
    companyId: 'test-company-id',
  };
  const mockEvent = {
    scraperResult: mockScraperResult,
    actionId: 'test-scheduled-action-id',
  };

  const mockLogger = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    setContext: jest.fn(),
    getBaseLogger: jest.fn(),
  } as any;

  const mockedAction = {
    id: 'test-scheduled-action-id',
    companyId: 'test-company-id',
    workflowType: WorkflowType.SEO,
    status: Status.DRAFT_PENDING,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    surfacedAt: null,
    scheduledToBeSurfacedAt: null,
    scheduledToBePublishedAt: null,
    publishedAt: null,
    contentPayload: {},
    generationPayload: {},
    failureReason: {},
    executionName: null,
    executionArn: null,
  };

  beforeEach(() => {
    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;
    jest.clearAllMocks();

    // Connect the mock logger to createContextLogger
    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );

    // Mock getConfig using centralized factory
    (getConfig as jest.Mock).mockReturnValue(createMockConfig());
  });

  it('should successfully extract keywords', async () => {
    const mockKeywords = 'test, content, extraction';
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockResolvedValue({
        content: mockKeywords,
        otherData: 'some data',
      }),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const result = await handler(mockEvent, mockContext);

    expect(result).toEqual({
      keywords: mockKeywords,
      status: 'success',
      scraperResult: mockScraperResult,
      actionId: mockEvent.actionId,
    });

    expect(mockPromptService.executeChatPrompt).toHaveBeenCalledWith(
      'fake-keyword-mapper-prompt',
      {
        markdown: mockScraperResult.markdown,
        url: mockScraperResult.url,
      },
    );

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['keywords'],
    );

    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      {
        generationPayload: {
          keywords: mockKeywords,
        },
      },
    );
  });

  it('should handle errors gracefully', async () => {
    const errorMessage = 'Test error message';
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockRejectedValue(new Error(errorMessage)),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    await expect(handler(mockEvent, mockContext)).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in keyword-extractor',
      {
        errorType: 'Error',
        error: errorMessage,
        stack: expect.any(String),
      },
    );
  });

  it('should handle unknown errors', async () => {
    const mockPromptService = {
      executeChatPrompt: jest.fn().mockRejectedValue('Unknown error type'),
    };

    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedAction,
        skipStep: false,
      }),
      update: jest.fn().mockResolvedValue(mockedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    // The error should be re-thrown as is from handleHandlerError
    await expect(handler(mockEvent, mockContext)).rejects.toBe(
      'Unknown error type',
    );

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in keyword-extractor',
      {
        errorType: 'UnknownError',
        error: 'Unknown error type',
        stack: undefined,
      },
    );
  });

  it('should skip keyword extraction if keywords already exist', async () => {
    const mockKeywords = 'existing, keywords';
    const mockedProcessedAction = {
      ...mockedAction,
      generationPayload: {
        keywords: mockKeywords,
      },
    };

    const mockScheduledActionService = {
      checkAction: jest.fn().mockResolvedValue({
        action: mockedProcessedAction,
        skipStep: true,
      }),
      update: jest.fn().mockResolvedValue(mockedProcessedAction),
    };
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    const mockPromptService = {
      executeChatPrompt: jest.fn(),
    };
    (serviceFactory.createPromptService as jest.Mock).mockReturnValue(
      mockPromptService,
    );

    const result = await handler(mockEvent, mockContext);

    expect(result).toEqual({
      keywords: mockKeywords,
      status: 'success',
      scraperResult: mockScraperResult,
      actionId: mockEvent.actionId,
    });

    expect(mockScheduledActionService.checkAction).toHaveBeenCalledWith(
      'test-scheduled-action-id',
      ['keywords'],
    );

    expect(mockPromptService.executeChatPrompt).not.toHaveBeenCalled();
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
  });
});
