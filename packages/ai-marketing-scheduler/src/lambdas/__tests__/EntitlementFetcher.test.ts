import { Logger } from '@aws-lambda-powertools/logger';
import { getConfig } from 'src/config';
import { serviceFactory } from 'src/factories';
import { Entitlement } from 'src/types';

import { ITenantService } from '../../services/interfaces/ITenantService';
import {
  setupTestMocks,
  createMockTenantService,
  createMockLambdaContext,
  createMockConfig,
  createMockEntitlements,
} from '../../utils/testUtils';
import { handler } from '../EntitlementFetcher';

// Mock the dependencies
jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));
jest.mock('../../factories', () => ({
  UnifiedServiceFactory: jest.fn().mockImplementation(() => ({
    createTenantService: jest.fn(),
  })),
  serviceFactory: {
    createTenantService: jest.fn(),
    createApiGatewayClient: jest.fn(),
  },
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

describe('EntitlementFetcher', () => {
  let mockLogger: Logger & { restore: () => void };
  let mockTenantService: jest.Mocked<ITenantService>;
  let mockApiGatewayClient: { fetchAllWebsites: jest.Mock };

  // Mock AWS Lambda context using centralized factory
  const mockContext = createMockLambdaContext();

  beforeEach(() => {
    // Set up test mocks including logger
    const { mockLogger: logger } = setupTestMocks();
    mockLogger = logger;

    // Connect the mock logger to createContextLogger
    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    (contextLoggerModule.createContextLogger as jest.Mock).mockReturnValue(
      mockLogger,
    );

    // Setup TenantService mock using centralized factory
    mockTenantService = createMockTenantService();

    // Mock createTenantService
    (serviceFactory.createTenantService as jest.Mock).mockReturnValue(
      mockTenantService,
    );

    // Mock getConfig using centralized factory
    (getConfig as jest.Mock).mockReturnValue(createMockConfig());

    // Mock ApiGatewayClient
    mockApiGatewayClient = {
      fetchAllWebsites: jest.fn((entitlements: Entitlement[]) =>
        Promise.resolve(
          entitlements.map(e => ({
            ...e,
            websiteUrl:
              e.websiteUrl || `https://www.test-website-${e.companyId}.com`,
          })),
        ),
      ),
    };
    (serviceFactory.createApiGatewayClient as jest.Mock).mockReturnValue(
      mockApiGatewayClient,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
    // Restore logger spies to prevent state bleed between tests
    mockLogger.restore();
  });

  it('should successfully process and chunk entitlements', async () => {
    // Arrange using centralized factory
    const mockEntitlements = createMockEntitlements(45);

    mockTenantService.getEntitlements.mockResolvedValue(mockEntitlements);

    // Act
    const result = await handler(undefined, mockContext);

    // Assert
    expect(mockTenantService.getEntitlements).toHaveBeenCalledTimes(1);
    expect(mockApiGatewayClient.fetchAllWebsites).toHaveBeenCalledTimes(1);
    expect(result.chunkedEntitlements.length).toBe(3); // 45 entitlements should be split into 3 chunks
    expect(result.chunkedEntitlements[0].length).toBe(20); // First chunk should have 20 items
    expect(result.chunkedEntitlements[1].length).toBe(20); // Second chunk should have 20 items
    expect(result.chunkedEntitlements[2].length).toBe(5); // Third chunk should have 5 items
    // Check that websiteUrl is set on all entitlements
    result.chunkedEntitlements.flat().forEach(e => {
      expect(e.websiteUrl).toBeDefined();
      expect(typeof e.websiteUrl).toBe('string');
    });
  });

  it('should handle empty entitlements array', async () => {
    // Arrange
    mockTenantService.getEntitlements.mockResolvedValue([]);

    // Act
    const result = await handler(undefined, mockContext);

    // Assert
    expect(mockTenantService.getEntitlements).toHaveBeenCalledTimes(1);
    expect(result.chunkedEntitlements.length).toBe(0);
    expect(mockApiGatewayClient.fetchAllWebsites).not.toHaveBeenCalled();
  });

  it('should handle error from tenant service', async () => {
    // Arrange
    const error = new Error('Failed to fetch entitlements');
    mockTenantService.getEntitlements.mockRejectedValue(error);

    // Act & Assert
    try {
      await handler({}, mockContext);
      fail('Should have thrown an error');
    } catch (error) {
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error in entitlement-fetcher',
        {
          errorType: 'Error',
          error: 'Failed to fetch entitlements',
          stack: error instanceof Error ? error.stack : undefined,
        },
      );
    }
  });

  it('should handle unknown error type', async () => {
    // Arrange
    const unknownError = 'Unknown error type';
    mockTenantService.getEntitlements.mockRejectedValue(unknownError);

    // Act & Assert
    try {
      await handler({}, mockContext);
      fail('Should have thrown an error');
    } catch {
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error in entitlement-fetcher',
        {
          errorType: 'UnknownError',
          error: unknownError,
        },
      );
    }
    expect(mockLogger.error).toHaveBeenCalledWith(
      'Error in entitlement-fetcher',
      {
        errorType: 'UnknownError',
        error: unknownError,
      },
    );
  });
});
