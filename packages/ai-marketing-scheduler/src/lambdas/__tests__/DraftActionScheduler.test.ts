import { Context } from 'aws-lambda';

import { ScheduledActionService } from '../../services/ScheduledActionService';
import {
  WorkflowType,
  Status,
  ScheduledAction,
  Entitlement,
} from '../../types';
import { handler } from '../DraftActionSchedulerDaemon';

jest.mock('../../services/ScheduledActionService');
jest.mock('../../factories/LoggerFactory', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    createComponentLogger: jest.fn().mockReturnThis(),
    createChild: jest.fn().mockReturnThis(),
  }),
  setAwsRequestId: jest.fn(),
  handleHandlerError: jest.fn().mockImplementation(error => {
    throw error;
  }),
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn().mockReturnValue({
    seoAutomationApi: {
      url: 'https://seo-automation-api.com',
      m2mSuperApiKey: 'm2m-super-api-key',
    },
    tenantApi: {
      url: 'https://tenant-api.com',
      productIds: ['5c9b746c-7327-42ce-b998-ce707e7cee44'],
      entitlementsLimit: 100,
    },
    clientMarketingApi: {
      url: 'https://client-marketing-service.com',
    },
  }),
}));

describe('DraftActionScheduler', () => {
  let mockContext: Context;
  let mockScheduledActionService: jest.Mocked<ScheduledActionService>;
  const blogId = '5c9b746c-7327-42ce-b998-ce707e7cee44';

  beforeEach(() => {
    mockContext = {
      awsRequestId: 'test-request-id',
    } as Context;

    mockScheduledActionService = {
      getUpcomingActions: jest.fn(),
      createScheduledActions: jest.fn(),
    } as unknown as jest.Mocked<ScheduledActionService>;

    (ScheduledActionService as jest.Mock).mockImplementation(
      () => mockScheduledActionService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should process entitlements and create owed actions', async () => {
    const mockEntitlements: Entitlement[] = [
      {
        companyId: 'company-1',
        productId: 'product-1',
        productName: 'SEO Package',
        units: 4,
        websiteUrl: 'https://example.com',
      } as Entitlement,
      {
        companyId: 'company-2',
        productId: blogId,
        productName: 'Blog Package',
        units: 2,
        websiteUrl: 'https://example.com',
      } as Entitlement,
    ];

    const mockUpcomingActions: ScheduledAction[] = [
      {
        id: '1',
        companyId: 'company-1',
        workflowType: WorkflowType.SEO,
        status: Status.DRAFT_PENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        surfacedAt: null,
        scheduledToBePublishedAt: null,
        scheduledToBeSurfacedAt: null,
        publishedAt: null,
        contentPayload: {},
        generationPayload: {},
        failureReason: {},
        executionName: 'test-execution-name',
        executionArn: 'test-execution-arn',
      },
      {
        id: '2',
        companyId: 'company-1',
        workflowType: WorkflowType.BLOG,
        status: Status.DRAFT_PENDING,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        surfacedAt: null,
        scheduledToBePublishedAt: null,
        scheduledToBeSurfacedAt: null,
        publishedAt: null,
        contentPayload: {},
        generationPayload: {},
        failureReason: {},
        executionName: null,
        executionArn: null,
      },
    ];

    mockScheduledActionService.getUpcomingActions
      .mockResolvedValueOnce(mockUpcomingActions) // company-1 has 2 actions
      .mockResolvedValueOnce([]); // company-2 has 0 actions

    const result = await handler(
      {
        entitlements: mockEntitlements,
      },
      mockContext,
    );

    expect(result).toEqual({
      processedEntitlements: 2,
      totalActionsCreated: 4, // 2 for company-1 (4-2) + 2 for company-2 (2-0)
      totalOwedActions: 4,
      totalUpcomingActions: 2,
      executionTimestamp: expect.any(String) as string,
      details: [
        {
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          entitlementUnits: 4,
          upcomingActions: 2,
          actionsCreated: 2,
          status: 'processed',
        },
        {
          companyId: 'company-2',
          workflowType: WorkflowType.BLOG,
          entitlementUnits: 2,
          upcomingActions: 0,
          actionsCreated: 2,
          status: 'processed',
        },
      ],
    });

    expect(
      mockScheduledActionService.createScheduledActions,
    ).toHaveBeenCalledTimes(2);
    expect(
      mockScheduledActionService.createScheduledActions,
    ).toHaveBeenCalledWith('company-1', WorkflowType.SEO, 2, {
      websiteUrl: 'https://example.com',
    });
    expect(
      mockScheduledActionService.createScheduledActions,
    ).toHaveBeenCalledWith('company-2', WorkflowType.BLOG, 2, {
      websiteUrl: 'https://example.com',
    });
  });

  it('should handle errors for individual entitlements', async () => {
    const mockEntitlements: Entitlement[] = [
      {
        companyId: 'company-1',
        productId: 'product-1',
        productName: 'SEO Package',
        units: 4,
      } as Entitlement,
      {
        companyId: 'company-2',
        productId: blogId,
        productName: 'Blog Package',
        units: 2,
      } as Entitlement,
    ];

    const mockUpcomingAction: ScheduledAction = {
      id: '1',
      companyId: 'company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      surfacedAt: null,
      scheduledToBePublishedAt: null,
      scheduledToBeSurfacedAt: null,
      publishedAt: null,
      contentPayload: {},
      generationPayload: {},
      failureReason: {},
      executionName: null,
      executionArn: null,
    };

    mockScheduledActionService.getUpcomingActions
      .mockResolvedValueOnce([mockUpcomingAction])
      .mockRejectedValueOnce(new Error('Failed to fetch actions'));

    const result = await handler(
      {
        entitlements: mockEntitlements,
      },
      mockContext,
    );

    expect(result).toEqual({
      processedEntitlements: 2,
      totalActionsCreated: 3, // Only company-1's actions were created
      totalOwedActions: 3,
      totalUpcomingActions: 1,
      executionTimestamp: expect.any(String) as string,
      details: [
        {
          companyId: 'company-1',
          workflowType: WorkflowType.SEO,
          entitlementUnits: 4,
          upcomingActions: 1,
          actionsCreated: 3,
          status: 'processed',
        },
        {
          companyId: 'company-2',
          workflowType: WorkflowType.BLOG,
          entitlementUnits: 2,
          upcomingActions: 0,
          actionsCreated: 0,
          status: 'error',
          error: 'Failed to fetch actions',
        },
      ],
    });
  });

  it('should handle empty entitlements array', async () => {
    const result = await handler(
      {
        entitlements: [],
      },
      mockContext,
    );

    expect(result).toEqual({
      processedEntitlements: 0,
      totalActionsCreated: 0,
      totalOwedActions: 0,
      totalUpcomingActions: 0,
      executionTimestamp: expect.any(String) as string,
      details: [],
    });

    expect(
      mockScheduledActionService.getUpcomingActions,
    ).not.toHaveBeenCalled();
    expect(
      mockScheduledActionService.createScheduledActions,
    ).not.toHaveBeenCalled();
  });

  it('should handle default units when not specified', async () => {
    const mockEntitlements: Entitlement[] = [
      {
        companyId: 'company-1',
        productId: 'product-1',
        productName: 'SEO Package',
        // units not specified, should default to 4
      } as Entitlement,
    ];

    mockScheduledActionService.getUpcomingActions.mockResolvedValueOnce([]);

    const result = await handler(
      {
        entitlements: mockEntitlements,
      },
      mockContext,
    );

    expect(result.details[0]).toEqual(
      expect.objectContaining({
        entitlementUnits: 4,
        actionsCreated: 4,
      }),
    );
  });

  it('should handle errors when creating scheduled actions', async () => {
    const mockEntitlements: Entitlement[] = [
      {
        companyId: 'company-1',
        productId: 'product-1',
        productName: 'SEO Package',
        units: 4,
      } as Entitlement,
      {
        companyId: 'company-2',
        productId: blogId,
        productName: 'Blog Package',
        units: 2,
      } as Entitlement,
    ];

    mockScheduledActionService.getUpcomingActions.mockResolvedValueOnce([]);
    mockScheduledActionService.createScheduledActions.mockRejectedValueOnce(
      new Error('Failed to create actions'),
    );

    const result = await handler(
      {
        entitlements: mockEntitlements,
      },
      mockContext,
    );

    expect(result.details[0]).toEqual(
      expect.objectContaining({
        status: 'error',
        error: 'Failed to create actions',
        actionsCreated: 0,
      }),
    );
  });
});
