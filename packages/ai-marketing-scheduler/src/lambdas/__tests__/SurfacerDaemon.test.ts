import { Context } from 'aws-lambda';

import { serviceFactory } from '../../factories/UnifiedServiceFactory';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { Status, WorkflowType } from '../../types';
import { createMockScheduledActions } from '../../utils/testUtils';
import { handler } from '../SurfacerDaemon';

jest.mock('../../factories/UnifiedServiceFactory', () => ({
  serviceFactory: {
    createScheduledActionService: jest.fn(),
  },
}));

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    createChild: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      createChild: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
      }),
    }),
  }),
}));

jest.mock('../../config', () => ({
  getConfig: jest.fn().mockReturnValue({
    mock: { useMockClients: true },
  }),
}));

describe('SurfacerDaemon', () => {
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;
  let mockContext: Context;

  beforeEach(() => {
    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;
    mockScheduledActionService = {
      update: jest.fn(),
      findActionsToSurface: jest.fn(),
      processActionsToSurfaceInBatches: jest.fn(),
      getUpcomingActions: jest.fn(),
      createScheduledActions: jest.fn(),
      updateStatus: jest.fn(),
      updateWithFailure: jest.fn(),
      getDraftPendingActions: jest.fn(),
      getSurfacedActions: jest.fn(),
      checkAction: jest.fn(),
      getAction: jest.fn(),
      storeSEODraft: jest.fn(),
    } as unknown as jest.Mocked<IScheduledActionService>;

    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should process actions to surface successfully', async () => {
    const surfacedAt = new Date('2024-03-16T00:00:00Z');
    const scheduledToBeSurfacedAt = new Date('2024-03-15T00:00:00Z');

    const mockActions = createMockScheduledActions(2, [
      {
        id: 'action-1',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: scheduledToBeSurfacedAt.toISOString(),
      },
      {
        id: 'action-2',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: scheduledToBeSurfacedAt.toISOString(),
      },
    ]);

    mockScheduledActionService.findActionsToSurface.mockResolvedValue([
      { companyId: 'company-1', actions: mockActions },
    ]);

    mockScheduledActionService.update
      .mockResolvedValueOnce({
        ...mockActions[0],
        status: Status.SURFACED,
        surfacedAt: surfacedAt.toISOString(),
        workflowType: WorkflowType.SEO,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        scheduledToBePublishedAt: expect.any(Date),
        publishedAt: expect.any(Date),
        scheduledToBeSurfacedAt: expect.any(Date),
        contentPayload: expect.any(Object),
        generationPayload: expect.any(Object),
        failureReason: expect.any(String),
        executionName: expect.any(String),
        executionArn: expect.any(String),
      })
      .mockResolvedValueOnce({
        ...mockActions[1],
        status: Status.SURFACED,
        surfacedAt: surfacedAt.toISOString(),
        workflowType: WorkflowType.SEO,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        scheduledToBePublishedAt: expect.any(Date),
        publishedAt: expect.any(Date),
        scheduledToBeSurfacedAt: expect.any(Date),
        contentPayload: expect.any(Object),
        generationPayload: expect.any(Object),
        failureReason: expect.any(String),
        executionName: expect.any(String),
        executionArn: expect.any(String),
      });

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(2);
    expect(mockScheduledActionService.update).toHaveBeenNthCalledWith(
      1,
      'action-1',
      {
        status: Status.SURFACED,
        surfacedAt: expect.any(String),
        scheduledToBePublishedAt: expect.any(String),
      },
    );
    expect(mockScheduledActionService.update).toHaveBeenNthCalledWith(
      2,
      'action-2',
      {
        status: Status.SURFACED,
        surfacedAt: expect.any(String),
        scheduledToBePublishedAt: expect.any(String),
      },
    );

    expect(result).toEqual({
      success: true,
      processedActions: [
        {
          actionId: 'action-1',
          companyId: 'company-1',
          status: Status.SURFACED,
        },
        {
          actionId: 'action-2',
          companyId: 'company-1',
          status: Status.SURFACED,
        },
      ],
      failedActions: [],
    });
  });

  it('should skip actions not ready to be surfaced', async () => {
    const futureDate = new Date();
    futureDate.setHours(futureDate.getHours() + 1);

    const mockActions = createMockScheduledActions(1, [
      {
        id: 'action-1',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: futureDate.toISOString(),
      },
    ]);

    mockScheduledActionService.findActionsToSurface.mockResolvedValue([
      { companyId: 'company-1', actions: mockActions },
    ]);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(result).toEqual({
      success: true,
      processedActions: [],
      failedActions: [],
    });
  });

  it('should handle actions with no scheduledToBeSurfacedAt date', async () => {
    const mockActions = createMockScheduledActions(1, [
      {
        id: 'action-1',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: null,
      },
    ]);

    mockScheduledActionService.findActionsToSurface.mockResolvedValue([
      { companyId: 'company-1', actions: mockActions },
    ]);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(result).toEqual({
      success: true,
      processedActions: [],
      failedActions: [],
    });
  });

  it('should handle no actions to surface', async () => {
    mockScheduledActionService.findActionsToSurface.mockResolvedValue([]);

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(result).toEqual({
      success: true,
      failedActions: [],
      processedActions: [],
    });
  });

  it('should continue processing remaining actions if one fails', async () => {
    const surfacedAt = new Date('2024-03-15T10:00:00Z');
    const scheduledToBeSurfacedAt = new Date('2024-03-15T09:00:00Z');

    const mockActions = createMockScheduledActions(2, [
      {
        id: 'action-1',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: scheduledToBeSurfacedAt.toISOString(),
      },
      {
        id: 'action-2',
        companyId: 'company-1',
        status: Status.SURFACING_PENDING,
        scheduledToBeSurfacedAt: scheduledToBeSurfacedAt.toISOString(),
      },
    ]);

    mockScheduledActionService.findActionsToSurface.mockResolvedValue([
      { companyId: 'company-1', actions: mockActions },
    ]);

    mockScheduledActionService.update
      .mockRejectedValueOnce(new Error('Failed to update action'))
      .mockResolvedValueOnce({
        ...mockActions[1],
        status: Status.SURFACED,
        surfacedAt: surfacedAt.toISOString(),
        workflowType: WorkflowType.SEO,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
        scheduledToBePublishedAt: expect.any(Date),
        publishedAt: expect.any(Date),
        scheduledToBeSurfacedAt: expect.any(Date),
        contentPayload: expect.any(Object),
        generationPayload: expect.any(Object),
        failureReason: expect.any(String),
        executionName: expect.any(String),
        executionArn: expect.any(String),
      });

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).toHaveBeenCalledTimes(2);
    expect(result).toEqual({
      success: false,
      failedActions: [
        {
          actionId: 'action-1',
          companyId: 'company-1',
          status: Status.SURFACING_PENDING,
          error: 'Failed to update action',
        },
      ],
      processedActions: [
        {
          actionId: 'action-2',
          companyId: 'company-1',
          status: Status.SURFACED,
        },
      ],
    });
  });

  it('should handle service-level errors gracefully', async () => {
    mockScheduledActionService.findActionsToSurface.mockRejectedValue(
      new Error('Service error'),
    );

    const result = await handler({}, mockContext);

    expect(
      mockScheduledActionService.findActionsToSurface,
    ).toHaveBeenCalledTimes(1);
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
    expect(result).toEqual({
      success: false,
      failedActions: [],
      processedActions: [],
      error: 'Service error',
    });
  });
});
