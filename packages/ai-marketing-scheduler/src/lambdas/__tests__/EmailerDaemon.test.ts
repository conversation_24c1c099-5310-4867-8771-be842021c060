import { Context } from 'aws-lambda';

import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import * as configModule from '../../config';
import * as factories from '../../factories';
import { handleHandlerError } from '../../factories/LoggerFactory';
import { ITenantService } from '../../services/interfaces/ITenantService';
import { Entitlement } from '../../types';
import * as dateUtils from '../../utils/date';
import { LaunchDarkly } from '../../utils/launchdarkly';
import * as scheduleUtils from '../../utils/scheduleUtils';
import * as slackUtils from '../../utils/slack';
import {
  createTestEntitlement,
  createMultipleAdmins,
} from '../../utils/testFactories';
import {
  setupCompanyDataError,
  setupEmailSendingError,
  setupLaunchDarklyError,
  setupMixedSuccessScenario,
  setupSuccessfulCompanyTest,
} from '../../utils/testHelpers';
import {
  createMockConfig,
  createMockSeoApiGatewayClient,
  createMockTenantService,
} from '../../utils/testUtils';
import { handler } from '../EmailerDaemon';

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn().mockReturnValue({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    createChild: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    }),
  }),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.fn(),
}));

jest.mock('../../utils/slack', () => ({
  sendSeoAlertMessage: jest.fn(),
}));

describe('EmailerDaemon', () => {
  let mockLogger: any;
  let mockContext: Context;
  let mockTenantService: jest.Mocked<ITenantService>;
  let mockSeoApiGatewayClient: jest.Mocked<ISeoApiGatewayClient>;
  let mockConfig: configModule.EnvironmentConfig;
  let ldInstance: jest.Mocked<LaunchDarkly>;

  beforeEach(() => {
    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;
    jest.clearAllMocks();

    // Create a proper mock logger
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      createChild: jest.fn().mockReturnValue({
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn(),
      }),
    };

    // Mock the createContextLogger to return our mock logger
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { createContextLogger } = require('../../logger/contextLogger');
    createContextLogger.mockReturnValue(mockLogger);

    mockTenantService = createMockTenantService();
    mockSeoApiGatewayClient = createMockSeoApiGatewayClient();
    mockConfig = createMockConfig();

    jest.spyOn(configModule, 'getConfig').mockReturnValue(mockConfig);
    jest
      .spyOn(factories.serviceFactory, 'createTenantService')
      .mockReturnValue(mockTenantService);
    jest
      .spyOn(factories.serviceFactory, 'createSeoApiGatewayClient')
      .mockReturnValue(mockSeoApiGatewayClient);
    jest.spyOn(dateUtils, 'addDays').mockImplementation((date, days) => {
      const d = new Date(date);
      d.setDate(d.getDate() + days);
      return d;
    });
    jest.spyOn(dateUtils, 'subDays').mockImplementation((date, days) => {
      const d = new Date(date);
      d.setDate(d.getDate() - days);
      return d;
    });
    jest.spyOn(scheduleUtils, 'groupByCompany').mockImplementation(items => {
      // Simple grouping for test
      const map = new Map();
      items.forEach(item => {
        if (!map.has(item.companyId)) map.set(item.companyId, []);
        map.get(item.companyId).push(item);
      });
      return Array.from(map.entries()).map(([companyId, items]) => ({
        companyId,
        items,
      }));
    });
    // Mock LaunchDarkly
    ldInstance = {
      checkVariationBatch: jest.fn(),
      closeConnection: jest.fn(),
      checkVariation: jest.fn(),
      getClient: jest.fn(),
      logger: mockLogger,
      config: { key: 'test-key', superUserCompanyId: 'test-company-id' },
    } as unknown as jest.Mocked<LaunchDarkly>;
    jest.spyOn(LaunchDarkly, 'getInstance').mockReturnValue(ldInstance);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Configuration and Constants', () => {
    it('should use correct batch sizes from configuration', async () => {
      const entitlements = [
        createTestEntitlement(),
        createTestEntitlement({ companyId: 'c2', websiteUrl: 'site2.com' }),
      ];

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          entitlements,
          adminCount: 1,
        },
      );

      await handler({}, mockContext);

      // Verify that the correct batch sizes are logged
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing companies in batches',
        expect.objectContaining({
          batchSize: 25, // COMPANY_DATA batch size
        }),
      );

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Starting email sending process',
        expect.objectContaining({
          totalCompanies: 2,
        }),
      );
    });

    it('should use correct delay configuration', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );

      await handler({}, mockContext);

      // Verify delay configuration is logged correctly
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing companies in batches',
        expect.objectContaining({
          baseDelayMs: 100, // COMPANY_DATA base delay
          maxDelayMs: 1000, // COMPANY_DATA max delay
        }),
      );
    });
  });

  describe('Timeout Handling', () => {
    it('should handle company data fetch errors gracefully', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );
      setupCompanyDataError(
        mockTenantService,
        new Error('Database connection failed'),
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0);
      expect(result.failed).toBe(1);

      // Check that the child logger was used for error logging
      expect(mockLogger.createChild).toHaveBeenCalled();
      const childLogger = mockLogger.createChild();
      expect(childLogger.error).toHaveBeenCalledWith(
        'Failed to fetch company data',
        expect.objectContaining({
          error: 'Database connection failed',
        }),
      );
    });

    it('should handle email sending errors gracefully', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          adminCount: 1,
        },
      );
      setupEmailSendingError(
        mockSeoApiGatewayClient,
        new Error('Email service unavailable'),
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0); // Company fails when all emails fail
      expect(result.failed).toBe(1);

      // Check that the child logger was used for error logging
      expect(mockLogger.createChild).toHaveBeenCalled();
      const childLogger = mockLogger.createChild();

      // The function should log that some emails failed, and the company is marked as failed
      expect(childLogger.warn).toHaveBeenCalledWith(
        'Some emails failed to send',
        expect.objectContaining({
          companyId: 'c1',
        }),
      );
    });

    it('should handle email sending timeout correctly and prevent further sends', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          adminCount: 2, // 2 admins + BCC = 3 total recipients
        },
      );

      // Mock sendEmail to simulate a timeout scenario
      // We'll mock it to reject with a timeout error to test the timeout handling logic
      mockSeoApiGatewayClient.sendEmail.mockRejectedValue(
        new Error('Email sending timed out'),
      );

      const result = await handler({}, mockContext);

      // Should process the company but emails should timeout
      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0); // Company fails when all emails timeout
      expect(result.failed).toBe(1);

      // Verify that sendEmail was called for each recipient
      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(3);

      // Check that timeout errors are logged
      expect(mockLogger.createChild).toHaveBeenCalled();
      const childLogger = mockLogger.createChild();
      expect(childLogger.warn).toHaveBeenCalledWith(
        'Some emails failed to send',
        expect.objectContaining({
          companyId: 'c1',
          failedEmails: expect.arrayContaining([
            expect.objectContaining({
              error: expect.stringContaining('timed out'),
            }),
          ]),
        }),
      );
    }, 10000); // 10 second timeout for this test

    it('should return detailed email success/failure counts', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          adminCount: 2, // 2 admins + BCC = 3 total recipients
        },
      );

      // Mock sendEmail to succeed for first 2 recipients, fail for the last one
      mockSeoApiGatewayClient.sendEmail
        .mockResolvedValueOnce(undefined) // First admin - success
        .mockResolvedValueOnce(undefined) // Second admin - success
        .mockRejectedValueOnce(new Error('HTTP 500')); // BCC - failure

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(1); // Company succeeds (at least one email succeeded)
      expect(result.failed).toBe(0);

      // Verify detailed counts are logged
      expect(mockLogger.createChild).toHaveBeenCalled();
      const childLogger = mockLogger.createChild();

      // Should log email sending completion with counts
      expect(childLogger.info).toHaveBeenCalledWith(
        'Email sending completed',
        expect.objectContaining({
          successful: 2,
          failed: 1,
          totalRecipients: 3,
        }),
      );

      // Should log warning about failed emails
      expect(childLogger.warn).toHaveBeenCalledWith(
        'Some emails failed to send',
        expect.objectContaining({
          companyId: 'c1',
          failedEmails: expect.arrayContaining([
            expect.objectContaining({
              error: 'HTTP 500',
            }),
          ]),
        }),
      );
    });
  });

  describe('Batch Processing Optimizations', () => {
    it('should process companies in correct batch sizes', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          companyCount: 30,
          adminCount: 1,
        },
      );

      await handler({}, mockContext);

      // Should process 30 companies in batches of 25 (1 full batch + 1 partial batch)
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Processing companies in batches',
        expect.objectContaining({
          totalCompanies: 30,
          batchSize: 25,
        }),
      );

      // Should process emails in batches (the actual message is different)
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Starting email sending process',
        expect.objectContaining({
          totalCompanies: 30,
        }),
      );
    });

    it('should apply linear delays between batches', async () => {
      const startTime = Date.now();

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          companyCount: 50,
          adminCount: 1,
        },
      );

      await handler({}, mockContext);

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // With 50 companies in batches of 25 = 2 batches
      // Delays: 100ms (first batch) + 150ms (second batch) = 250ms minimum
      // Plus processing time, should be at least 250ms
      expect(totalTime).toBeGreaterThanOrEqual(250);
    });
  });

  describe('Enhanced Logging', () => {
    it('should log timeout limit and expected processing time', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );

      await handler({}, mockContext);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Starting emailer daemon',
        expect.objectContaining({
          timeoutLimit: '10 seconds', // Mock context provides 10 seconds
        }),
      );
    });

    it('should log remaining time on completion', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );

      await handler({}, mockContext);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Emailer daemon completed successfully',
        expect.objectContaining({
          remainingTimeMs: 10000, // From mock context
        }),
      );
    });
  });

  it('should process and send emails for supported companies', async () => {
    const entitlements: Entitlement[] = [
      {
        companyId: 'c1',
        websiteUrl: 'site1.com',
        displayName: 'Site 1',
        productId: 'product1',
        productName: 'Product 1',
        endDate: null,
        units: 1,
      },
      {
        companyId: 'c2',
        websiteUrl: 'site2.com',
        displayName: 'Site 2',
        productId: 'product2',
        productName: 'Product 2',
        endDate: null,
        units: 2,
      },
    ];
    mockTenantService.getEntitlements.mockResolvedValue(entitlements);
    ldInstance.checkVariationBatch.mockReturnValue(
      Promise.resolve(
        new Map([
          ['c1', true],
          ['c2', false],
        ]),
      ),
    );
    mockTenantService.getCompanyAdmins.mockResolvedValueOnce([
      {
        email: '<EMAIL>',
        displayId: 'display1',
        firstName: 'First',
        lastName: 'Last',
        avatar: 'avatar1',
        teamLead: false,
        externalAuthId: 'external1',
        facebookUserId: 'facebook1',
        lastLogin: '2021-01-01',
        notificationsLastSeen: '2021-01-01',
        leadEmail: '<EMAIL>',
        phoneNumber: '1234567890',
        type: 'admin',
      },
    ]);
    mockSeoApiGatewayClient.getEmailContent.mockResolvedValueOnce({
      items: [
        {
          timestamp: '2021-01-01T00:00:00Z',
          itemType: 'type',
          content: { publishedAt: '2021-01-01T00:00:00Z' },
        },
      ],
    });
    mockSeoApiGatewayClient.sendEmail.mockResolvedValue(undefined);

    const result = await handler({}, mockContext);
    expect(result.processed).toBe(1);
    expect(result.successful).toBe(1);
    expect(result.failed).toBe(0);
    expect(mockTenantService.getEntitlements).toHaveBeenCalled();
    expect(ldInstance.checkVariationBatch).toHaveBeenCalledWith(
      'enable-client-marketing-2-0',
      false,
      ['c1', 'c2'],
    );
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith('c1');
    expect(mockSeoApiGatewayClient.getEmailContent).toHaveBeenCalledWith(
      'c1',
      expect.any(Object),
    );

    // Should send individual emails to each recipient (admin + BCC)
    const expectedRecipients = [
      '<EMAIL>',
      ...mockConfig.sendgrid.bccEmails,
    ];
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
      expectedRecipients.length,
    );

    // Check that each recipient gets their own email
    expectedRecipients.forEach((recipient, index) => {
      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
        index + 1,
        mockConfig.sendgrid.weeklyDigestTemplateId,
        'c1',
        [recipient], // Single recipient in "To" field
        expect.objectContaining({
          domain: 'site1.com',
          domainNoLink: 'site1.&#8203;com',
          items: [
            {
              timestamp: '2021-01-01T00:00:00Z',
              itemType: 'type',
              content: { publishedAt: '2021-01-01T00:00:00Z' },
            },
          ],
          pastItems: [
            {
              timestamp: '2021-01-01T00:00:00Z',
              itemType: 'type',
              content: { publishedAt: '2021-01-01T00:00:00Z' },
            },
          ],
          futureItems: [],
        }),
        'Weekly Digest',
        [], // Empty BCC array since each recipient gets their own email
        mockConfig.sendgrid.asmGroupId,
      );
    });
  });

  it('should return 0 processed if no entitlements', async () => {
    mockTenantService.getEntitlements.mockResolvedValue([]);
    const result = await handler({}, mockContext);
    expect(result).toEqual({ processed: 0, successful: 0, failed: 0 });
  });

  it('should skip companies with no admins', async () => {
    const entitlements = [createTestEntitlement()];
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        entitlements,
      },
    );
    mockTenantService.getCompanyAdmins.mockResolvedValueOnce([]);

    const result = await handler({}, mockContext);
    expect(result.processed).toBe(1);
    expect(result.successful).toBe(0);
    expect(result.failed).toBe(1);
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith('c1');
    expect(mockSeoApiGatewayClient.sendEmail).not.toHaveBeenCalled();
  });

  it('should skip companies with no email content', async () => {
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        emailContent: { items: [] },
      },
    );

    const result = await handler({}, mockContext);
    expect(result.processed).toBe(1);
    expect(result.successful).toBe(0);
    expect(result.failed).toBe(1);
    expect(mockSeoApiGatewayClient.sendEmail).not.toHaveBeenCalled();
  });

  it('should skip a company if the query errors', async () => {
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
    );
    mockSeoApiGatewayClient.getEmailContent.mockRejectedValue(
      new Error('Query failed'),
    );

    const result = await handler({}, mockContext);
    expect(result.processed).toBe(1);
    expect(result.successful).toBe(0);
    expect(result.failed).toBe(1);
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith('c1');
    expect(mockSeoApiGatewayClient.sendEmail).not.toHaveBeenCalled();
  });

  it('should handle individual email failures and continue processing others', async () => {
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        adminCount: 2,
      },
    );

    // Override the admin setup to use the expected email addresses with factory
    mockTenantService.getCompanyAdmins.mockResolvedValue(
      createMultipleAdmins(2, 'admin', 'site1.com'),
    );

    // Mock sendEmail to fail for admin1 but succeed for admin2 and BCC
    mockSeoApiGatewayClient.sendEmail
      .mockRejectedValueOnce(new Error('HTTP 500: Service unavailable')) // admin1 fails
      .mockResolvedValueOnce() // admin2 succeeds
      .mockResolvedValueOnce(); // BCC succeeds

    const result = await handler({}, mockContext);

    expect(result.processed).toBe(1);
    expect(result.successful).toBe(1); // Company is still considered successful if any emails sent
    expect(result.failed).toBe(0);

    // Should attempt to send emails to all recipients
    const expectedRecipients = [
      '<EMAIL>',
      '<EMAIL>',
      ...mockConfig.sendgrid.bccEmails,
    ];
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
      expectedRecipients.length,
    );

    // Verify that all emails were attempted
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
      1,
      mockConfig.sendgrid.weeklyDigestTemplateId,
      'c1',
      ['<EMAIL>'],
      expect.any(Object),
      'Weekly Digest',
      [],
      mockConfig.sendgrid.asmGroupId,
    );

    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
      2,
      mockConfig.sendgrid.weeklyDigestTemplateId,
      'c1',
      ['<EMAIL>'],
      expect.any(Object),
      'Weekly Digest',
      [],
      mockConfig.sendgrid.asmGroupId,
    );
  });

  it('should handle errors in sendEmail and continue', async () => {
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
    );

    // Mock all sendEmail calls to fail
    mockSeoApiGatewayClient.sendEmail.mockRejectedValue(
      new Error('HTTP 500: Service unavailable'),
    );

    const result = await handler({}, mockContext);
    expect(result.processed).toBe(1);
    expect(result.successful).toBe(0); // Company fails when all emails fail
    expect(result.failed).toBe(1);
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalled();
  });

  it('should throw if weeklyDigestTemplateId is not configured', async () => {
    jest.spyOn(configModule, 'getConfig').mockReturnValue({
      ...mockConfig,
      sendgrid: {
        weeklyDigestTemplateId: '',
        asmGroupId: '123',
        bccEmails: ['<EMAIL>'],
      },
    });
    await expect(handler({}, mockContext)).rejects.toThrow(
      'Weekly digest template ID is not configured',
    );
  });
  it('should handle unexpected errors and return default response', async () => {
    mockTenantService.getEntitlements.mockRejectedValue(
      new Error('Unexpected'),
    );
    const result = await handler({}, mockContext);
    expect(handleHandlerError).toHaveBeenCalledWith(
      new Error('Unexpected'),
      expect.anything(), // The actual ContextLogger instance
      'emailer-daemon',
    );
    expect(result).toEqual({ processed: 0, successful: 0, failed: 0 });
  });

  it('should send individual emails to each recipient including BCC', async () => {
    // Create admin objects first to get their actual email addresses
    const admins = createMultipleAdmins(1, 'admin', 'test.com');

    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
    );

    const result = await handler({}, mockContext);

    expect(result.processed).toBe(1);
    expect(result.successful).toBe(1);

    // Should send individual emails to each recipient (admin + BCC)
    const expectedRecipients = [
      admins[0].email,
      ...mockConfig.sendgrid.bccEmails,
    ];
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
      expectedRecipients.length,
    );

    // Check that admin gets their own email
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
      1,
      mockConfig.sendgrid.weeklyDigestTemplateId,
      'c1',
      [admins[0].email],
      expect.objectContaining({
        domain: 'site1.com',
        domainNoLink: 'site1.&#8203;com',
        items: [
          {
            timestamp: '2021-01-01T00:00:00Z',
            itemType: 'type',
            content: { publishedAt: '2021-01-01T00:00:00Z' },
          },
        ],
        pastItems: [
          {
            timestamp: '2021-01-01T00:00:00Z',
            itemType: 'type',
            content: { publishedAt: '2021-01-01T00:00:00Z' },
          },
        ],
        futureItems: [],
      }),
      'Weekly Digest',
      [], // Empty BCC array since each recipient gets their own email
      mockConfig.sendgrid.asmGroupId,
    );
  });

  it('should send individual emails to each admin', async () => {
    // Create admin objects first to get their actual email addresses
    const admins = createMultipleAdmins(2, 'admin', 'test.com');

    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        adminCount: 2,
      },
    );

    const result = await handler({}, mockContext);

    expect(result.processed).toBe(1);
    expect(result.successful).toBe(1);

    // Should send individual emails to each recipient (2 admins + BCC)
    const expectedRecipients = [
      admins[0].email,
      admins[1].email,
      ...mockConfig.sendgrid.bccEmails,
    ];
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
      expectedRecipients.length,
    );

    // Check that each admin gets their own individual email
    admins.forEach((admin, index) => {
      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
        index + 1,
        mockConfig.sendgrid.weeklyDigestTemplateId,
        'c1',
        [admin.email], // Single admin in "To" field
        expect.objectContaining({
          domain: 'site1.com',
          domainNoLink: 'site1.&#8203;com',
          items: [
            {
              timestamp: '2021-01-01T00:00:00Z',
              itemType: 'type',
              content: { publishedAt: '2021-01-01T00:00:00Z' },
            },
          ],
          pastItems: [
            {
              timestamp: '2021-01-01T00:00:00Z',
              itemType: 'type',
              content: { publishedAt: '2021-01-01T00:00:00Z' },
            },
          ],
          futureItems: [],
        }),
        'Weekly Digest',
        [], // Empty BCC array since each recipient gets their own email
        mockConfig.sendgrid.asmGroupId,
      );
    });
  });

  describe('temporal filtering', () => {
    it('should filter items into pastItems and futureItems based on timestamps', async () => {
      const pastTimestamp = '2021-01-01T00:00:00Z';
      const futureTimestamp = '2030-01-01T00:00:00Z';

      // Create admin object first to get the actual email address
      const admin = createMultipleAdmins(1, 'admin', 'test.com')[0];

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          emailContent: {
            items: [
              {
                timestamp: pastTimestamp,
                itemType: 'past',
                content: { id: 1, publishedAt: pastTimestamp },
              },
              {
                timestamp: futureTimestamp,
                itemType: 'future',
                content: { id: 2 },
              },
              { timestamp: '', itemType: 'null', content: { id: 3 } },
            ],
          },
        },
      );

      await handler({}, mockContext);

      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledWith(
        mockConfig.sendgrid.weeklyDigestTemplateId,
        'c1',
        [admin.email],
        expect.objectContaining({
          items: [
            {
              timestamp: pastTimestamp,
              itemType: 'past',
              content: { id: 1, publishedAt: pastTimestamp },
            },
            {
              timestamp: futureTimestamp,
              itemType: 'future',
              content: { id: 2 },
            },
            { timestamp: '', itemType: 'null', content: { id: 3 } },
          ],
          pastItems: [
            {
              timestamp: pastTimestamp,
              itemType: 'past',
              content: { id: 1, publishedAt: pastTimestamp },
            },
          ],
          futureItems: [
            {
              timestamp: futureTimestamp,
              itemType: 'future',
              content: { id: 2 },
            },
          ],
        }),
        'Weekly Digest',
        expect.any(Array),
        mockConfig.sendgrid.asmGroupId,
      );
    });

    it('should handle all items being in the past', async () => {
      const pastTimestamp1 = '2021-01-01T00:00:00Z';
      const pastTimestamp2 = '2021-06-01T00:00:00Z';

      // Create admin object first to get the actual email address
      const admin = createMultipleAdmins(1, 'admin', 'test.com')[0];

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          emailContent: {
            items: [
              {
                timestamp: pastTimestamp1,
                itemType: 'past1',
                content: { id: 1, publishedAt: pastTimestamp1 },
              },
              {
                timestamp: pastTimestamp2,
                itemType: 'past2',
                content: { id: 2, publishedAt: pastTimestamp2 },
              },
            ],
          },
        },
      );

      await handler({}, mockContext);

      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledWith(
        mockConfig.sendgrid.weeklyDigestTemplateId,
        'c1',
        [admin.email],
        expect.objectContaining({
          pastItems: [
            {
              timestamp: pastTimestamp1,
              itemType: 'past1',
              content: { id: 1, publishedAt: pastTimestamp1 },
            },
            {
              timestamp: pastTimestamp2,
              itemType: 'past2',
              content: { id: 2, publishedAt: pastTimestamp2 },
            },
          ],
          futureItems: [],
        }),
        'Weekly Digest',
        expect.any(Array),
        mockConfig.sendgrid.asmGroupId,
      );
    });

    it('should handle all items being in the future', async () => {
      const futureTimestamp1 = '2030-01-01T00:00:00Z';
      const futureTimestamp2 = '2030-06-01T00:00:00Z';

      // Create admin object first to get the actual email address
      const admin = createMultipleAdmins(1, 'admin', 'test.com')[0];

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          emailContent: {
            items: [
              {
                timestamp: futureTimestamp1,
                itemType: 'future1',
                content: { id: 1 },
              },
              {
                timestamp: futureTimestamp2,
                itemType: 'future2',
                content: { id: 2 },
              },
            ],
          },
        },
      );

      await handler({}, mockContext);

      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledWith(
        mockConfig.sendgrid.weeklyDigestTemplateId,
        'c1',
        [admin.email],
        expect.objectContaining({
          pastItems: [],
          futureItems: [
            {
              timestamp: futureTimestamp1,
              itemType: 'future1',
              content: { id: 1 },
            },
            {
              timestamp: futureTimestamp2,
              itemType: 'future2',
              content: { id: 2 },
            },
          ],
        }),
        'Weekly Digest',
        expect.any(Array),
        mockConfig.sendgrid.asmGroupId,
      );
    });

    it('should send Slack alert when validation fails', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          emailContent: { items: [] },
        },
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0);
      expect(result.failed).toBe(1);
      expect(slackUtils.sendSeoAlertMessage).toHaveBeenCalledWith(
        'EmailerDaemon failed on error',
        expect.arrayContaining([
          expect.objectContaining({
            type: 'section',
            text: expect.objectContaining({
              type: 'mrkdwn',
              text: expect.stringContaining('No email content'),
            }),
          }),
        ]),
      );
    });
  });

  it('should filter entitlements by provided companyIds', async () => {
    const entitlements: Entitlement[] = [
      createTestEntitlement({ companyId: 'c1', websiteUrl: 'site1.com' }),
      createTestEntitlement({
        companyId: 'c2',
        websiteUrl: 'site2.com',
        units: 2,
      }),
      createTestEntitlement({
        companyId: 'c3',
        websiteUrl: 'site3.com',
        units: 3,
      }),
    ];

    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        entitlements,
      },
    );

    const event = { companyIds: ['c1', 'c3'] };
    const result = await handler(event, mockContext);

    expect(result.processed).toBe(2); // Only c1 and c3 should be processed
    expect(result.successful).toBe(2);
    expect(result.failed).toBe(0);

    // Should only call getCompanyAdmins for filtered companies
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledTimes(2);
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith('c1');
    expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith('c3');
    expect(mockTenantService.getCompanyAdmins).not.toHaveBeenCalledWith('c2');
  });

  it('should use custom emails when provided in event', async () => {
    setupSuccessfulCompanyTest(
      mockTenantService,
      mockSeoApiGatewayClient,
      ldInstance,
      {
        customEmails: ['<EMAIL>', '<EMAIL>'],
      },
    );

    const event = {
      companyIds: ['c1'],
      emails: ['<EMAIL>', '<EMAIL>'],
    };
    const result = await handler(event, mockContext);

    expect(result.processed).toBe(1);
    expect(result.successful).toBe(1);
    expect(result.failed).toBe(0);

    // Should not call getCompanyAdmins when custom emails are provided
    expect(mockTenantService.getCompanyAdmins).not.toHaveBeenCalled();

    // Should send emails to custom emails + BCC
    const expectedRecipients = [
      '<EMAIL>',
      '<EMAIL>',
      ...mockConfig.sendgrid.bccEmails,
    ];
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
      expectedRecipients.length,
    );

    // Check that custom emails are used instead of admin emails
    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
      1,
      mockConfig.sendgrid.weeklyDigestTemplateId,
      'c1',
      ['<EMAIL>'],
      expect.any(Object),
      'Weekly Digest',
      [],
      mockConfig.sendgrid.asmGroupId,
    );

    expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
      2,
      mockConfig.sendgrid.weeklyDigestTemplateId,
      'c1',
      ['<EMAIL>'],
      expect.any(Object),
      'Weekly Digest',
      [],
      mockConfig.sendgrid.asmGroupId,
    );
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle empty batch gracefully', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          emailContent: { items: [] },
        },
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0);
      expect(result.failed).toBe(1);
      expect(slackUtils.sendSeoAlertMessage).toHaveBeenCalled();
    });

    it('should handle mixed success/failure scenarios in batches', async () => {
      setupMixedSuccessScenario(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        30,
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(30);
      expect(result.successful).toBe(1); // Only c2 succeeds
      expect(result.failed).toBe(29); // c1, c3-c30 fail
    });

    it('should handle LaunchDarkly connection errors gracefully', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );
      setupLaunchDarklyError(
        ldInstance,
        new Error('LaunchDarkly connection failed'),
      );

      // The handler should catch the error and return a default response
      const result = await handler({}, mockContext);

      expect(result).toEqual({ processed: 0, successful: 0, failed: 0 });

      // Verify closeConnection is called in finally block
      expect(ldInstance.closeConnection).toHaveBeenCalled();

      // Verify error is handled by handleHandlerError
      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'emailer-daemon',
      );
    });

    it('should handle very large batches efficiently', async () => {
      const startTime = Date.now();

      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
        {
          companyCount: 100,
          adminCount: 1,
        },
      );

      const result = await handler({}, mockContext);

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // 100 companies in batches of 25 = 4 batches
      // Delays: 100ms + 150ms + 200ms + 250ms = 700ms minimum
      // Plus processing time, should be reasonable
      expect(totalTime).toBeGreaterThanOrEqual(700);

      expect(result.processed).toBe(100);
      expect(result.successful).toBe(100);
      expect(result.failed).toBe(0);
    });
  });

  describe('SERHANT Company Special Handling', () => {
    const SERHANT_COMPANY_ID = 'b26ab618-2b1e-4a17-8868-498b96b52dc0';

    it('should bypass feature flag and only send <NAME_EMAIL> for SERHANT company', async () => {
      const entitlements: Entitlement[] = [
        createTestEntitlement({
          companyId: SERHANT_COMPANY_ID,
          websiteUrl: 'serhant.com',
        }),
        createTestEntitlement({
          companyId: 'other-company',
          websiteUrl: 'other.com',
        }),
      ];

      // Mock LaunchDarkly to return false for both companies - SERHANT should still be processed
      ldInstance.checkVariationBatch.mockReturnValue(
        Promise.resolve(
          new Map([
            [SERHANT_COMPANY_ID, false],
            ['other-company', false],
          ]),
        ),
      );

      mockTenantService.getEntitlements.mockResolvedValue(entitlements);

      // Mock multiple admins for SERHANT to verify only coyne gets email
      mockTenantService.getCompanyAdmins.mockResolvedValue([
        {
          email: '<EMAIL>',
          displayId: 'admin1',
          firstName: 'Admin',
          lastName: 'One',
          avatar: 'avatar1',
          teamLead: false,
          externalAuthId: 'external1',
          facebookUserId: 'facebook1',
          lastLogin: '2021-01-01',
          notificationsLastSeen: '2021-01-01',
          leadEmail: '<EMAIL>',
          phoneNumber: '1234567890',
          type: 'admin',
        },
        {
          email: '<EMAIL>',
          displayId: 'admin2',
          firstName: 'Admin',
          lastName: 'Two',
          avatar: 'avatar2',
          teamLead: false,
          externalAuthId: 'external2',
          facebookUserId: 'facebook2',
          lastLogin: '2021-01-01',
          notificationsLastSeen: '2021-01-01',
          leadEmail: '<EMAIL>',
          phoneNumber: '1234567891',
          type: 'admin',
        },
      ]);

      mockSeoApiGatewayClient.getEmailContent.mockResolvedValue({
        items: [
          {
            timestamp: '2021-01-01T00:00:00Z',
            itemType: 'test',
            content: { publishedAt: '2021-01-01T00:00:00Z' },
          },
        ],
      });

      mockSeoApiGatewayClient.sendEmail.mockResolvedValue(undefined);

      const result = await handler({}, mockContext);

      // Only SERHANT should be processed (other-company should be filtered out due to feature flag)
      expect(result.processed).toBe(1);
      expect(result.successful).toBe(1);
      expect(result.failed).toBe(0);

      // Should only call getCompanyAdmins for SERHANT
      expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledTimes(1);
      expect(mockTenantService.getCompanyAdmins).toHaveBeenCalledWith(
        SERHANT_COMPANY_ID,
      );
      expect(mockTenantService.getCompanyAdmins).not.toHaveBeenCalledWith(
        'other-company',
      );

      // Should send emails <NAME_EMAIL> + BCC recipients
      const expectedRecipients = [
        '<EMAIL>',
        ...mockConfig.sendgrid.bccEmails,
      ];
      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenCalledTimes(
        expectedRecipients.length,
      );

      // Verify <EMAIL> gets the email (not the other admins)
      expect(mockSeoApiGatewayClient.sendEmail).toHaveBeenNthCalledWith(
        1,
        mockConfig.sendgrid.weeklyDigestTemplateId,
        SERHANT_COMPANY_ID,
        ['<EMAIL>'],
        expect.objectContaining({
          domain: 'serhant.com',
          domainNoLink: 'serhant.&#8203;com',
        }),
        'Weekly Digest',
        [],
        mockConfig.sendgrid.asmGroupId,
      );

      // <NAME_EMAIL> and <EMAIL> do NOT get emails
      expect(mockSeoApiGatewayClient.sendEmail).not.toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String),
        ['<EMAIL>'],
        expect.any(Object),
        expect.any(String),
        expect.any(Array),
        expect.any(String),
      );

      expect(mockSeoApiGatewayClient.sendEmail).not.toHaveBeenCalledWith(
        expect.any(String),
        expect.any(String),
        ['<EMAIL>'],
        expect.any(Object),
        expect.any(String),
        expect.any(Array),
        expect.any(String),
      );
    });
  });

  describe('Utility Functions', () => {
    it('should handle error scenarios gracefully', async () => {
      setupSuccessfulCompanyTest(
        mockTenantService,
        mockSeoApiGatewayClient,
        ldInstance,
      );
      setupCompanyDataError(
        mockTenantService,
        new Error('Service unavailable'),
      );

      const result = await handler({}, mockContext);

      expect(result.processed).toBe(1);
      expect(result.successful).toBe(0);
      expect(result.failed).toBe(1);

      // Verify the error is logged through the child logger
      expect(mockLogger.createChild).toHaveBeenCalled();
      const childLogger = mockLogger.createChild();
      expect(childLogger.error).toHaveBeenCalledWith(
        'Failed to fetch company data',
        expect.objectContaining({
          error: 'Service unavailable',
        }),
      );
    });
  });
});
