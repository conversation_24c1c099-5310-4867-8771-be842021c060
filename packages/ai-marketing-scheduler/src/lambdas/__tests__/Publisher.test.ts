import { Context } from 'aws-lambda';

import { ISeoApiGatewayClient } from '../../clients/interfaces/ISeoApiGatewayClient';
import {
  RecommendationStatus,
  StoreSEODraftResponse,
  StoredRecommendation,
} from '../../clients/types/seoApiGateway';
import { serviceFactory } from '../../factories';
import { handleHandlerError } from '../../factories/LoggerFactory';
import { IScheduledActionService } from '../../services/interfaces/IScheduledActionService';
import { IStarApiService } from '../../services/interfaces/IStarApiService';
import {
  WorkflowType,
  Status,
  ScheduledAction,
  ScraperResult,
  ScrapedPageType,
  RecommendationType,
} from '../../types';
import {
  createMockStoredRecommendations,
  createMockStoredRecommendationsArray,
} from '../../utils/testUtils';
import { handler } from '../Publisher';

jest.mock('../../factories/LoggerFactory', () => ({
  createContextLogger: jest.fn(),
  createLogger: jest.fn(),
  handleHandlerError: jest.fn().mockImplementation(error => {
    throw error;
  }),
}));

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
  ContextLogger: jest.fn(),
}));

jest.mock('../../factories', () => ({
  serviceFactory: {
    createScheduledActionService: jest.fn(),
    createStarApiService: jest.fn(),
    createSeoApiGatewayClient: jest.fn(),
  },
}));

describe('Publisher Lambda', () => {
  let mockLogger: any;
  let mockScheduledActionService: jest.Mocked<IScheduledActionService>;
  let mockStarApiService: jest.Mocked<IStarApiService>;
  let mockSeoApiGatewayClient: jest.Mocked<ISeoApiGatewayClient>;
  let mockContext: Context;

  const mockScraperResult: ScraperResult = {
    firecrawlId: 'firecrawl-123',
    url: 'https://example.com/page',
    metaTitle: 'Old Meta Title',
    metaDescription: 'Old meta description',
    mainHeading: {
      section_id: 'test-section',
      element_id: 'test-element-1',
      tag: 'h1',
      index: 0,
      content: 'Old H1 Content',
    },
    type: ScrapedPageType.HOMEPAGE,
    markdown: '# Page content',
    mediaId: 'media-123',
    companyId: 'company-123',
  };

  const mockStoredRecommendations: StoredRecommendation[] =
    createMockStoredRecommendationsArray();

  const mockStoreSEODraftResponse: StoreSEODraftResponse = {
    savedKeyword: null,
    savedRecommendations: mockStoredRecommendations.map(recommendation => ({
      id: recommendation.id,
      type: recommendation.type,
      groupId: recommendation.groupId,
      scrapeId: recommendation.scrapeId!,
    })),
    savedScrape: null,
    savedScrapedPage: null,
    savedPageKeyword: null,
    savedGroup: null,
  };

  const mockScheduledAction: ScheduledAction = {
    id: 'action-123',
    companyId: 'company-123',
    workflowType: WorkflowType.SEO,
    status: Status.SURFACED,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    surfacedAt: '2024-01-01T00:00:00Z',
    scheduledToBePublishedAt: '2024-01-15T00:00:00Z',
    scheduledToBeSurfacedAt: '2024-01-10T00:00:00Z',
    publishedAt: null,
    contentPayload: mockStoreSEODraftResponse,
    generationPayload: {
      scraperResult: mockScraperResult,
    },
    failureReason: {},
    executionName: null,
    executionArn: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      setContext: jest.fn(),
      getBaseLogger: jest.fn(),
      createChild: jest.fn().mockReturnThis(),
      createComponentLogger: jest.fn().mockReturnThis(),
    } as any;

    // Connect the mock logger to createContextLogger from contextLogger module
    const { createContextLogger } = jest.requireMock(
      '../../logger/contextLogger',
    );
    createContextLogger.mockReturnValue(mockLogger);

    mockScheduledActionService = {
      getAction: jest.fn(),
      update: jest.fn(),
    } as any;

    mockStarApiService = {
      updateMetaTitle: jest.fn(),
      updateMetaDescription: jest.fn(),
      updateMainHeading: jest.fn(),
    } as any;

    mockSeoApiGatewayClient = {
      applyRecommendation: jest.fn(),
      getRecommendationsByGroupId: jest.fn(),
      getRecommendationById: jest.fn(),
    } as any;

    mockContext = {
      awsRequestId: 'test-request-id',
      functionName: 'test-function',
    } as any;

    (createContextLogger as jest.Mock).mockReturnValue(mockLogger);
    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );
    (serviceFactory.createStarApiService as jest.Mock).mockReturnValue(
      mockStarApiService,
    );
    (serviceFactory.createSeoApiGatewayClient as jest.Mock).mockReturnValue(
      mockSeoApiGatewayClient,
    );
  });

  describe('Input validation', () => {
    it('should throw error when actionId is missing', async () => {
      const event = {
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      } as any;

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Action ID is required',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('Action ID is required');
    });

    it('should throw error when companyId is missing', async () => {
      const event = {
        actionId: 'action-123',
        workflowType: WorkflowType.SEO,
      } as any;

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Company ID is required',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('Company ID is required');
    });

    it('should throw error when workflowType is missing', async () => {
      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
      } as any;

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Workflow type is required',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('Workflow type is required');
    });
  });

  describe('Action validation', () => {
    it('should throw error when action is not found', async () => {
      mockScheduledActionService.getAction.mockResolvedValue(null);

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Action not found',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('Action not found');
    });

    it('should throw error when action is not in SURFACED status', async () => {
      const actionWithWrongStatus = {
        ...mockScheduledAction,
        status: Status.DRAFT_PENDING,
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithWrongStatus,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Action is not in SURFACED status',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toContain('Action is not in SURFACED status');
    });

    it('should throw error when scraper result is missing', async () => {
      const actionWithoutScraperResult = {
        ...mockScheduledAction,
        generationPayload: {},
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithoutScraperResult,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'No scraper result found in action',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('No scraper result found in action');
    });

    it('should throw error when content payload is missing', async () => {
      const actionWithoutContentPayload = {
        ...mockScheduledAction,
        contentPayload: null,
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithoutContentPayload,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'No content payload found in action',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.anything(),
        'publisher',
      );
      const error = (handleHandlerError as any).mock.calls[0][0];
      expect(error.message).toBe('No content payload found in action');
    });
  });

  describe('Successful updates', () => {
    beforeEach(() => {
      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );
      // Mock successful responses for all three update methods
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });
    });

    it('should successfully update all elements and mark action as PUBLISHED', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      const result = await handler(event, mockContext);

      // Verify all three updates were made
      expect(mockStarApiService.updateMetaTitle).toHaveBeenCalledTimes(1);
      expect(mockStarApiService.updateMetaDescription).toHaveBeenCalledTimes(1);
      expect(mockStarApiService.updateMainHeading).toHaveBeenCalledTimes(1);

      // Verify meta title update
      expect(mockStarApiService.updateMetaTitle).toHaveBeenCalledWith(
        'https://example.com/page',
        'Old Meta Title',
        'New Meta Title',
        'company-123',
        'action-123',
      );

      // Verify meta description update
      expect(mockStarApiService.updateMetaDescription).toHaveBeenCalledWith(
        'https://example.com/page',
        'Old meta description',
        'New meta description',
        'company-123',
        'action-123',
      );

      // Verify main heading update
      expect(mockStarApiService.updateMainHeading).toHaveBeenCalledWith(
        'https://example.com/page',
        'h1',
        0,
        'Old H1 Content',
        'New H1 Content',
        'company-123',
        'action-123',
        'test-section',
      );

      // Verify action was marked as PUBLISHED
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );

      // Verify applyRecommendation was called for each successful update
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        3,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-1',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-2',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-3',
      );

      // Verify response
      expect(result).toEqual({
        actionId: 'action-123',
        success: true,
        updates: [
          {
            elementType: 'meta',
            elementProperty: 'title',
            success: true,
          },
          {
            elementType: 'meta',
            elementProperty: 'description',
            success: true,
          },
          {
            elementType: 'h1',
            success: true,
          },
        ],
      });
    });

    it('should handle partial updates when some original content is missing', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      const actionWithPartialContent = {
        ...mockScheduledAction,
        generationPayload: {
          scraperResult: {
            ...mockScraperResult,
            metaDescription: undefined, // Missing original content
          },
        },
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithPartialContent,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await handler(event, mockContext);

      // Should only update title and main heading (skipping metaDescription)
      expect(mockStarApiService.updateMetaTitle).toHaveBeenCalledTimes(1);
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).toHaveBeenCalledTimes(1);

      // Should still mark as PUBLISHED if remaining updates succeed
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );

      // Verify applyRecommendation was called only for successful updates (2 out of 3)
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        2,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-1',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-3',
      );
    });

    it('should handle when no updates are applicable', async () => {
      // Mock getRecommendationById to return non-rejected recommendations
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      const actionWithNoOriginalContent = {
        ...mockScheduledAction,
        generationPayload: {
          scraperResult: {
            ...mockScraperResult,
            metaTitle: undefined,
            metaDescription: undefined,
            mainHeading: undefined, // All original content missing
          },
        },
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithNoOriginalContent,
      );

      const noOriginalContentEvent = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(
        handler(noOriginalContentEvent, mockContext),
      ).rejects.toThrow('No updates to apply - missing required content');

      // No updates should be attempted
      expect(mockStarApiService.updateMetaTitle).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).not.toHaveBeenCalled();

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );
    });

    it('should skip rejected recommendations when fetching latest status', async () => {
      // Mock getRecommendationById to return one rejected recommendation
      const mockRecommendations = createMockStoredRecommendations({
        metaDescription: {
          status: RecommendationStatus.REJECTED,
          rejectionReason: 'Not appropriate for brand',
        },
      });
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );
      // Mock successful responses for all three update methods
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await handler(event, mockContext);

      // Should only update non-rejected recommendations (2 out of 3)
      expect(mockStarApiService.updateMetaTitle).toHaveBeenCalledTimes(1);
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).toHaveBeenCalledTimes(1);

      // Should still mark as PUBLISHED
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );

      // Should only apply recommendations for the 2 successful updates
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        2,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-1',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-3',
      );
    });

    it('should handle errors when fetching recommendation status', async () => {
      // Mock getRecommendationById to fail for one recommendation
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockRejectedValueOnce(new Error('Failed to fetch recommendation'))
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );
      // Mock successful responses for all three update methods
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await handler(event, mockContext);

      // Should still mark as PUBLISHED
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );
    });

    it('should handle errors when applying recommendation status gracefully', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );
      // Mock successful responses for all three update methods
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      // Make applyRecommendation fail for one recommendation
      mockSeoApiGatewayClient.applyRecommendation
        .mockResolvedValueOnce({
          id: 'rec-1',
          status: 'APPLIED',
          type: 'META_TITLE',
          recommendationValue: 'New Meta Title',
          currentValue: 'Old Meta Title',
        })
        .mockRejectedValueOnce(new Error('Failed to apply recommendation'))
        .mockResolvedValueOnce({
          id: 'rec-3',
          status: 'APPLIED',
          type: 'MAIN_HEADING',
          recommendationValue: 'New H1 Content',
          currentValue: 'Old H1 Content',
        });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      const result = await handler(event, mockContext);

      // Should still mark action as PUBLISHED even if one recommendation status update fails
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );

      // Should log error about recommendation status update failure
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to apply recommendation',
        expect.objectContaining({
          recommendationId: 'rec-2',
          error: 'Failed to apply recommendation',
        }),
      );

      expect(result.success).toBe(true);
    });

    it('should handle empty savedRecommendations in content payload', async () => {
      const actionWithEmptyRecommendations = {
        ...mockScheduledAction,
        contentPayload: {
          ...mockStoreSEODraftResponse,
          savedRecommendations: [],
        },
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithEmptyRecommendations,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'No updates to apply - missing required content',
      );

      // Should not fetch any recommendations
      expect(
        mockSeoApiGatewayClient.getRecommendationById,
      ).not.toHaveBeenCalled();

      // Should not update any elements
      expect(mockStarApiService.updateMetaTitle).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).not.toHaveBeenCalled();

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );
    });

    it('should handle null savedRecommendations in content payload', async () => {
      const actionWithNullRecommendations = {
        ...mockScheduledAction,
        contentPayload: {
          ...mockStoreSEODraftResponse,
          savedRecommendations: null,
        },
      };
      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithNullRecommendations,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'No updates to apply - missing required content',
      );

      // Should not fetch any recommendations
      expect(
        mockSeoApiGatewayClient.getRecommendationById,
      ).not.toHaveBeenCalled();

      // Should not update any elements
      expect(mockStarApiService.updateMetaTitle).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).not.toHaveBeenCalled();

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );
    });

    it('should treat "No changes were made" as success and apply recommendations', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      // All updates return "No changes were made" error which should be treated as success
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      const result = await handler(event, mockContext);

      expect(result).toEqual({
        actionId: 'action-123',
        success: true,
        updates: expect.arrayContaining([
          { elementType: 'meta', elementProperty: 'title', success: true },
          {
            elementType: 'meta',
            elementProperty: 'description',
            success: true,
          },
          { elementType: 'h1', success: true },
        ]),
      });

      // Should mark action as PUBLISHED
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.PUBLISHED,
        }),
      );

      // Should apply all recommendations since they're all treated as success
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        3,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        mockRecommendations.metaTitle.id,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        mockRecommendations.metaDescription.id,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        mockRecommendations.mainHeading.id,
      );
    });

    it('should handle all recommendations being rejected', async () => {
      // Mock getRecommendationById to return all rejected statuses
      const mockRecommendations = createMockStoredRecommendations({
        metaTitle: {
          status: RecommendationStatus.REJECTED,
          rejectionReason: 'Not appropriate',
        },
        metaDescription: {
          status: RecommendationStatus.REJECTED,
          rejectionReason: 'Not appropriate',
        },
        mainHeading: {
          status: RecommendationStatus.REJECTED,
          rejectionReason: 'Not appropriate',
        },
      });
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'No updates to apply - missing required content',
      );

      // Should not make any Star API updates
      expect(mockStarApiService.updateMetaTitle).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).not.toHaveBeenCalled();

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );
    });
  });

  describe('Failed updates', () => {
    beforeEach(() => {
      mockScheduledActionService.getAction.mockResolvedValue(
        mockScheduledAction,
      );
    });

    it('should throw PublisherError when partial failures occur', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      // Make meta title succeed but description fail
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: false,
        error: 'Element not found',
        metadata: {
          requestId: '1752164298125-d969a7b6-8626-42e2-9bcd-c3281878d76d',
        },
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Partial STAR publishing failure: 2 succeeded, 1 failed',
      );

      // Should not directly update status to FAILED or PUBLISHED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.PUBLISHED,
        }),
      );

      // Should apply successful recommendations (meta title and main heading)
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        2,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        mockRecommendations.metaTitle.id,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        mockRecommendations.mainHeading.id,
      );
      // Should NOT apply failed recommendation (meta description)
      expect(
        mockSeoApiGatewayClient.applyRecommendation,
      ).not.toHaveBeenCalledWith(mockRecommendations.metaDescription.id);
    });

    it('should handle STAR API client exceptions and mark as FAILED when all fail', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      // Mock all update methods to return failure
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: false,
        error: 'Network timeout',
        metadata: {
          originalError: 'Network timeout',
        },
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: false,
        error: 'Network timeout',
        metadata: {
          originalError: 'Network timeout',
        },
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: false,
        error: 'Network timeout',
        metadata: {
          originalError: 'Network timeout',
        },
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'All STAR updates failed',
      );

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );

      // Should NOT apply any recommendations when all fail
      expect(
        mockSeoApiGatewayClient.applyRecommendation,
      ).not.toHaveBeenCalled();
    });

    it('should include detailed STAR API error messages in failure reason', async () => {
      // Mock getRecommendationById to return non-rejected statuses
      const mockRecommendations = createMockStoredRecommendations();
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce(mockRecommendations.metaTitle)
        .mockResolvedValueOnce(mockRecommendations.metaDescription)
        .mockResolvedValueOnce(mockRecommendations.mainHeading);

      // Mock all update methods to return failure with detailed error
      const errorMetadata = {
        url: 'http://website-service.luxurycoders.com/api/v1/star/editor',
        status: 400,
        statusText: 'Bad Request',
        error: {
          message: '"body.input.newContent" must be a string',
        },
      };
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: false,
        error:
          'STAR API request failed: "body.input.newContent" must be a string',
        metadata: errorMetadata,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: false,
        error:
          'STAR API request failed: "body.input.newContent" must be a string',
        metadata: errorMetadata,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: false,
        error:
          'STAR API request failed: "body.input.newContent" must be a string',
        metadata: errorMetadata,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'All STAR updates failed',
      );

      // Should not directly update status to FAILED
      expect(mockScheduledActionService.update).not.toHaveBeenCalledWith(
        'action-123',
        expect.objectContaining({
          status: Status.FAILED,
        }),
      );

      // Should NOT apply any recommendations when all fail
      expect(
        mockSeoApiGatewayClient.applyRecommendation,
      ).not.toHaveBeenCalled();
    });
  });

  describe('Recommendation status updates', () => {
    it('should handle when recommendation status changed from PENDING to REJECTED after contentPayload was saved', async () => {
      // Create an action where contentPayload has PENDING recommendations
      const actionWithPendingRecommendations = {
        ...mockScheduledAction,
        contentPayload: {
          ...mockStoreSEODraftResponse,
          savedRecommendations: [
            {
              ...mockStoredRecommendations[0],
              status: RecommendationStatus.PENDING, // Originally PENDING
            },
            {
              ...mockStoredRecommendations[1],
              status: RecommendationStatus.PENDING, // Originally PENDING
            },
            {
              ...mockStoredRecommendations[2],
              status: RecommendationStatus.PENDING, // Originally PENDING
            },
          ],
        },
      };

      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithPendingRecommendations,
      );

      // Mock getRecommendationById to return one that was rejected after contentPayload was saved
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce({
          id: 'rec-1',
          scrapeId: 'scrape-123',
          groupId: 'group-123',
          type: RecommendationType.META_TITLE,
          status: RecommendationStatus.PENDING, // Still PENDING
          recommendationValue: 'New Meta Title',
          currentValue: 'Old Meta Title',
          reasoning: 'SEO improvement',
          rejectionReason: null,
          metadata: {},
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        })
        .mockResolvedValueOnce({
          id: 'rec-2',
          scrapeId: 'scrape-123',
          groupId: 'group-123',
          type: RecommendationType.META_DESCRIPTION,
          status: RecommendationStatus.REJECTED, // Changed to REJECTED after contentPayload was saved
          recommendationValue: 'New meta description',
          currentValue: 'Old meta description',
          reasoning: 'SEO improvement',
          rejectionReason: 'User rejected this recommendation',
          metadata: {},
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-02'), // Updated after original save
        })
        .mockResolvedValueOnce({
          id: 'rec-3',
          scrapeId: 'scrape-123',
          groupId: 'group-123',
          type: RecommendationType.MAIN_HEADING,
          status: RecommendationStatus.PENDING, // Still PENDING
          recommendationValue: 'New H1 Content',
          currentValue: 'Old H1 Content',
          reasoning: 'SEO improvement',
          rejectionReason: null,
          metadata: {},
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        });

      // Mock successful responses for all three update methods
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });
      mockStarApiService.updateMainHeading.mockResolvedValue({
        elementType: 'h1',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      const result = await handler(event, mockContext);

      // Should only update non-rejected recommendations (2 out of 3)
      expect(mockStarApiService.updateMetaTitle).toHaveBeenCalledTimes(1);
      expect(mockStarApiService.updateMetaDescription).not.toHaveBeenCalled();
      expect(mockStarApiService.updateMainHeading).toHaveBeenCalledTimes(1);

      // The refactored code doesn't log for rejected recommendations anymore
      // Instead it only logs for eligible recommendations

      // Should still mark as PUBLISHED since other updates succeeded
      expect(mockScheduledActionService.update).toHaveBeenCalledWith(
        'action-123',
        {
          status: Status.PUBLISHED,
          publishedAt: expect.any(String),
        },
      );

      // Should only apply recommendations for the 2 successful updates
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        2,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-1',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-3',
      );
      expect(
        mockSeoApiGatewayClient.applyRecommendation,
      ).not.toHaveBeenCalledWith('rec-2');

      expect(result.success).toBe(true);
    });

    it('should apply recommendations when contentPayload has REJECTED status but current status is PENDING', async () => {
      // Create an action where contentPayload has REJECTED recommendations
      const actionWithRejectedRecommendations = {
        ...mockScheduledAction,
        contentPayload: {
          ...mockStoreSEODraftResponse,
          savedRecommendations: [
            {
              id: 'rec-1',
              type: RecommendationType.META_TITLE,
              status: RecommendationStatus.REJECTED, // Stale status in contentPayload
              groupId: 'group-123',
              currentValue: 'Old Title',
              recommendationValue: 'New Title',
              reasoning: 'Better for SEO',
            },
            {
              id: 'rec-2',
              type: RecommendationType.META_DESCRIPTION,
              status: RecommendationStatus.REJECTED, // Stale status in contentPayload
              groupId: 'group-123',
              currentValue: 'Old Description',
              recommendationValue: 'New Description',
              reasoning: 'Better for SEO',
            },
          ],
        },
      };

      mockScheduledActionService.getAction.mockResolvedValue(
        actionWithRejectedRecommendations,
      );

      // Mock getRecommendationById to return PENDING status (current status)
      mockSeoApiGatewayClient.getRecommendationById
        .mockResolvedValueOnce({
          id: 'rec-1',
          scrapeId: null,
          groupId: 'group-123',
          type: RecommendationType.META_TITLE,
          currentValue: 'Old Title',
          recommendationValue: 'New Title',
          reasoning: 'Better for SEO',
          status: RecommendationStatus.PENDING, // Current status is PENDING
          rejectionReason: null,
          metadata: {},
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        })
        .mockResolvedValueOnce({
          id: 'rec-2',
          scrapeId: null,
          groupId: 'group-123',
          type: RecommendationType.META_DESCRIPTION,
          currentValue: 'Old Description',
          recommendationValue: 'New Description',
          reasoning: 'Better for SEO',
          status: RecommendationStatus.PENDING, // Current status is PENDING
          rejectionReason: null,
          metadata: {},
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01'),
        });

      // Mock successful updates for both elements
      mockStarApiService.updateMetaTitle.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'title',
        success: true,
      });
      mockStarApiService.updateMetaDescription.mockResolvedValue({
        elementType: 'meta',
        elementProperty: 'description',
        success: true,
      });

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      const result = await handler(event, mockContext);

      // Verify getRecommendationById was called to fetch current status
      expect(
        mockSeoApiGatewayClient.getRecommendationById,
      ).toHaveBeenCalledTimes(2); // Only in fetchCurrentRecommendationStatus

      // Verify applyRecommendation was called for both recommendations (since current status is PENDING)
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledTimes(
        2,
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-1',
      );
      expect(mockSeoApiGatewayClient.applyRecommendation).toHaveBeenCalledWith(
        'rec-2',
      );

      expect(result.success).toBe(true);
    });
  });

  describe('Error handling', () => {
    it('should use handleHandlerError for unexpected errors', async () => {
      const unexpectedError = new Error('Unexpected error');
      mockScheduledActionService.getAction.mockRejectedValue(unexpectedError);

      const event = {
        actionId: 'action-123',
        companyId: 'company-123',
        workflowType: WorkflowType.SEO,
      };

      await expect(handler(event, mockContext)).rejects.toThrow(
        'Unexpected error',
      );

      expect(handleHandlerError).toHaveBeenCalledWith(
        unexpectedError,
        expect.anything(),
        'publisher',
      );
    });
  });
});
