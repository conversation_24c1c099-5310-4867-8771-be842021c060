import { Context } from 'aws-lambda';

import { serviceFactory } from '../../factories';
import { ScheduledActionService } from '../../services/ScheduledActionService';
import { ScheduledAction, Status, WorkflowType } from '../../types';
import { createMockScheduledActions } from '../../utils/testUtils';
import { handler } from '../StoreSEODraft';

// Mock the service factory
jest.mock('../../factories', () => ({
  serviceFactory: {
    createScheduledActionService: jest.fn(),
  },
}));

jest.mock('../../logger/contextLogger', () => ({
  createContextLogger: jest.fn(),
}));

jest.mock('../../factories/LoggerFactory', () => ({
  handleHandlerError: jest.requireActual('../../factories/LoggerFactory')
    .handleHandlerError,
}));

describe('StoreSEODraftLambda', () => {
  let mockScheduledActionService: jest.Mocked<ScheduledActionService>;
  let mockLogger: any;
  let mockContext: Context;

  beforeEach(() => {
    // Reset the mock before each test
    jest.clearAllMocks();

    mockScheduledActionService = {
      getUpcomingActions: jest.fn(),
      update: jest.fn(),
      getAction: jest.fn(),
      storeSEODraft: jest.fn(),
    } as unknown as jest.Mocked<ScheduledActionService>;

    (serviceFactory.createScheduledActionService as jest.Mock).mockReturnValue(
      mockScheduledActionService,
    );

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      setContext: jest.fn(),
      addContextValue: jest.fn(),
      clearContext: jest.fn(),
      clearAllContext: jest.fn(),
      getContext: jest.fn(),
      createChild: jest.fn(),
      getBaseLogger: jest.fn(),
    };
    const contextLoggerModule = jest.requireMock('../../logger/contextLogger');
    contextLoggerModule.createContextLogger.mockReturnValue(mockLogger);

    mockContext = {
      functionName: 'test-function',
      functionVersion: '1',
      invokedFunctionArn: 'arn:test',
      memoryLimitInMB: '128',
      awsRequestId: 'test-request-id',
      logGroupName: 'test-log-group',
      logStreamName: 'test-log-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
    } as unknown as Context;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  const createValidInput = () => ({
    companyId: 'test-company-1',
    workflowType: WorkflowType.SEO,
    actionId: 'test-action-1',
    recommendations: {
      metaTitle: {
        currentValue: 'Test SEO Title',
        recommendationValue: 'Test SEO Title',
        reasoning: 'This is a test reasoning for meta title',
      },
      metaDescription: {
        currentValue: 'Test SEO Description',
        recommendationValue: 'Test SEO Description',
        reasoning: 'This is a test reasoning for meta description',
      },
      mainHeading: {
        currentValue: 'Test Main Heading',
        recommendationValue: 'Test Main Heading',
        reasoning: 'This is a test reasoning for main heading',
      },
    },
    metadata: {
      llm_model: 'gpt-4o',
      target_identification_params: {
        keywords: ['test', 'seo'],
      },
    },
  });

  it('should successfully store SEO draft and calculate dates', async () => {
    // Mock the scheduled action
    const mockScheduledAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      generationPayload: {
        scraperResult: {
          id: 'test-page-1',
          companyId: 'test-company-1',
          url: 'https://example.com',
          pageName: 'Test Page',
          pageType: 'HOMEPAGE',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
        keywords: 'test,seo,keywords',
        recommendations: createValidInput().recommendations,
      },
      contentPayload: {},
      failureReason: {},
      createdAt: '2024-03-20T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z',
      surfacedAt: null,
      publishedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.getAction.mockResolvedValue(mockScheduledAction);

    // Mock storeSEODraft
    mockScheduledActionService.storeSEODraft.mockResolvedValue({
      savedKeyword: null,
      savedRecommendations: [],
      savedScrape: null,
      savedScrapedPage: null,
      savedPageKeyword: null,
      savedGroup: null,
    });

    // Mock upcoming actions
    const mockActions = createMockScheduledActions(2, [
      {
        scheduledToBeSurfacedAt: '2024-03-20T00:00:00Z',
      },
    ]);
    mockScheduledActionService.getUpcomingActions.mockResolvedValue(
      mockActions,
    );

    // Mock successful update
    const mockUpdatedAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.SURFACING_PENDING,
      scheduledToBeSurfacedAt: '2024-03-27T00:00:00Z',
      scheduledToBePublishedAt: '2024-04-03T00:00:00Z',
      failureReason: {} as Record<string, unknown>,
      generationPayload: createValidInput().metadata,
      contentPayload: createValidInput().recommendations,
      createdAt: '2024-03-20T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z',
      surfacedAt: null,
      publishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;
    mockScheduledActionService.update.mockResolvedValue(mockUpdatedAction);

    const result = await handler(createValidInput(), mockContext);

    expect(result.success).toBe(true);
    expect(result.actionId).toBe('test-action-1');
    expect(result.scheduledToBeSurfacedAt).toBeDefined();
    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-action-1',
      expect.objectContaining({
        status: Status.SURFACING_PENDING,
      }),
    );
  });

  it('should handle missing input fields', async () => {
    const invalidInput = {
      companyId: 'test-company-1',
      // Missing workflowType, actionId, and recommendations
    };

    const errorMessage = 'Invalid input provided to store-seo-draft lambda';
    await expect(handler(invalidInput as any, mockContext)).rejects.toThrow(
      errorMessage,
    );

    expect(mockLogger.error).toHaveBeenCalledWith('Error in store-draft', {
      errorType: 'InvalidInputError',
      error: errorMessage,
      stack: expect.any(String),
      metadata: {
        event: invalidInput,
      },
    });
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
  });

  it('should reject non-SEO workflow types', async () => {
    const input = {
      ...createValidInput(),
      workflowType: WorkflowType.BLOG,
    };

    const errorMessage = 'This Lambda only supports SEO workflow type';

    await expect(handler(input, mockContext)).rejects.toThrow(errorMessage);

    expect(mockLogger.error).toHaveBeenCalledWith('Error in store-draft', {
      errorType: 'Error',
      error: errorMessage,
      stack: expect.any(String),
    });
    expect(mockScheduledActionService.update).not.toHaveBeenCalled();
  });

  it('should handle service errors', async () => {
    const errorMessage = 'Service error';

    // Mock the scheduled action
    const mockScheduledAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      generationPayload: {
        scraperResult: {
          id: 'test-page-1',
          companyId: 'test-company-1',
          url: 'https://example.com',
          pageName: 'Test Page',
          pageType: 'HOMEPAGE',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
        keywords: 'test,seo,keywords',
        recommendations: createValidInput().recommendations,
      },
      contentPayload: {},
      failureReason: {},
      createdAt: '2024-03-20T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z',
      surfacedAt: null,
      publishedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.getAction.mockResolvedValue(mockScheduledAction);

    mockScheduledActionService.getUpcomingActions.mockRejectedValue(
      new Error(errorMessage),
    );

    await expect(handler(createValidInput(), mockContext)).rejects.toThrow(
      errorMessage,
    );

    expect(mockLogger.error).toHaveBeenCalledWith('Error in store-draft', {
      errorType: 'Error',
      error: errorMessage,
      stack: expect.any(String),
    });
  });

  it('should handle update failures', async () => {
    const errorMessage = 'Update failed';

    // Mock the scheduled action
    const mockScheduledAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      generationPayload: {
        scraperResult: {
          id: 'test-page-1',
          companyId: 'test-company-1',
          url: 'https://example.com',
          pageName: 'Test Page',
          pageType: 'HOMEPAGE',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
        keywords: 'test,seo,keywords',
        recommendations: createValidInput().recommendations,
      },
      contentPayload: {},
      failureReason: {},
      createdAt: '2024-03-20T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z',
      surfacedAt: null,
      publishedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.getAction.mockResolvedValue(mockScheduledAction);

    // Mock storeSEODraft
    mockScheduledActionService.storeSEODraft.mockResolvedValue({
      savedKeyword: null,
      savedRecommendations: [],
      savedScrape: null,
      savedScrapedPage: null,
      savedPageKeyword: null,
      savedGroup: null,
    });

    // Mock upcoming actions
    mockScheduledActionService.getUpcomingActions.mockResolvedValue([]);

    // Mock failed update
    mockScheduledActionService.update.mockRejectedValue(
      new Error(errorMessage),
    );
    await expect(handler(createValidInput(), mockContext)).rejects.toThrow(
      errorMessage,
    );

    expect(mockLogger.error).toHaveBeenCalledWith('Error in store-draft', {
      errorType: 'Error',
      error: errorMessage,
      stack: expect.any(String),
    });
  });

  it('should calculate dates based on latest scheduled action', async () => {
    const baseDate = new Date('2024-03-20T00:00:00Z');

    // Mock the scheduled action
    const mockScheduledAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      generationPayload: {
        scraperResult: {
          id: 'test-page-1',
          companyId: 'test-company-1',
          url: 'https://example.com',
          pageName: 'Test Page',
          pageType: 'HOMEPAGE',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
        keywords: 'test,seo,keywords',
        recommendations: createValidInput().recommendations,
      },
      contentPayload: {},
      failureReason: {},
      createdAt: baseDate.toISOString(),
      updatedAt: baseDate.toISOString(),
      surfacedAt: null,
      publishedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.getAction.mockResolvedValue(mockScheduledAction);

    // Mock storeSEODraft
    mockScheduledActionService.storeSEODraft.mockResolvedValue({
      savedKeyword: null,
      savedRecommendations: [],
      savedScrape: null,
      savedScrapedPage: null,
      savedPageKeyword: null,
      savedGroup: null,
    });

    const mockActions = [
      {
        id: 'existing-action-1',
        companyId: 'test-company-1',
        workflowType: WorkflowType.SEO,
        status: Status.DRAFT_PENDING,
        createdAt: baseDate.toISOString(),
        updatedAt: baseDate.toISOString(),
        surfacedAt: null,
        scheduledToBeSurfacedAt: baseDate.toISOString(),
        scheduledToBePublishedAt: null,
        publishedAt: null,
        contentPayload: {},
        generationPayload: {},
        failureReason: {},
      } as ScheduledAction,
    ];

    mockScheduledActionService.getUpcomingActions.mockResolvedValue(
      mockActions,
    );

    const expectedSurfacedDate = new Date(baseDate);
    expectedSurfacedDate.setDate(expectedSurfacedDate.getDate() + 7);

    const expectedPublishedDate = new Date(expectedSurfacedDate);
    expectedPublishedDate.setDate(expectedPublishedDate.getDate() + 7);

    const mockUpdatedAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.SURFACING_PENDING,
      scheduledToBeSurfacedAt: expectedSurfacedDate.toISOString(),
      scheduledToBePublishedAt: expectedPublishedDate.toISOString(),
      failureReason: {} as Record<string, unknown>,
      generationPayload: createValidInput().metadata,
      contentPayload: createValidInput().recommendations,
      createdAt: baseDate.toISOString(),
      updatedAt: baseDate.toISOString(),
      surfacedAt: null,
      publishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.update.mockResolvedValue(mockUpdatedAction);

    const input = createValidInput();

    const result = await handler(input, mockContext);

    // Verify the mock was called with correct parameters
    expect(mockScheduledActionService.getUpcomingActions).toHaveBeenCalledWith(
      input.companyId,
      input.workflowType,
    );

    expect(result.success).toBe(true);

    // Verify dates are calculated correctly (7 days after base date for surfacing)
    const surfacedDate = new Date(result.scheduledToBeSurfacedAt!);
    expect(surfacedDate.toISOString()).toBe(expectedSurfacedDate.toISOString());
  });

  it('should handle optional metadata field', async () => {
    const inputWithoutMetadata = {
      ...createValidInput(),
      metadata: undefined,
    };

    // Mock the scheduled action
    const mockScheduledAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      workflowType: WorkflowType.SEO,
      status: Status.DRAFT_PENDING,
      generationPayload: {
        scraperResult: {
          id: 'test-page-1',
          companyId: 'test-company-1',
          url: 'https://example.com',
          pageName: 'Test Page',
          pageType: 'HOMEPAGE',
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        },
        keywords: 'test,seo,keywords',
        recommendations: createValidInput().recommendations,
      },
      contentPayload: {},
      failureReason: {},
      createdAt: '2024-03-20T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z',
      surfacedAt: null,
      publishedAt: null,
      scheduledToBeSurfacedAt: null,
      scheduledToBePublishedAt: null,
      executionName: 'test-action-1-timestamp',
      executionArn: 'test-execution-arn',
    } as ScheduledAction;

    mockScheduledActionService.getAction.mockResolvedValue(mockScheduledAction);

    // Mock storeSEODraft
    mockScheduledActionService.storeSEODraft.mockResolvedValue({
      savedKeyword: null,
      savedRecommendations: [],
      savedScrape: null,
      savedScrapedPage: null,
      savedPageKeyword: null,
      savedGroup: null,
    });

    // Mock upcoming actions
    mockScheduledActionService.getUpcomingActions.mockResolvedValue([]);

    // Mock successful update
    const mockUpdatedAction = {
      id: 'test-action-1',
      companyId: 'test-company-1',
      status: Status.SURFACING_PENDING,
      scheduledToBeSurfacedAt: '2024-03-27T00:00:00Z',
      scheduledToBePublishedAt: '2024-04-03T00:00:00Z',
    } as ScheduledAction;
    mockScheduledActionService.update.mockResolvedValue(mockUpdatedAction);

    const result = await handler(inputWithoutMetadata, mockContext);

    expect(result.success).toBe(true);
    expect(mockScheduledActionService.update).toHaveBeenCalledWith(
      'test-action-1',
      expect.objectContaining({
        status: Status.SURFACING_PENDING,
      }),
    );
  });
});
