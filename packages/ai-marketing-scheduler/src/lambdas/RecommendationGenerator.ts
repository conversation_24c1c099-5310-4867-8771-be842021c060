import { Context } from 'aws-lambda';

import { getConfig } from '../config';
import { RecommendationGeneratorError } from '../errors/RecommendationGeneratorError';
import { handleHandlerError } from '../factories/LoggerFactory';
import { serviceFactory } from '../factories/UnifiedServiceFactory';
import { createContextLogger } from '../logger/contextLogger';
import {
  SeoRecommendation,
  SeoRecommendationSchema,
} from '../structuredOutputSchema';
import { ScraperResult, ScrapedPageType, WorkflowType } from '../types';

interface RecommendationGeneratorEvent {
  scraperResult: ScraperResult;
  keywords: string;
  actionId: string;
}

interface ResponseMetaData {
  scraperResult: ScraperResult;
  keywords: string;
}

interface RecommendationGeneratorResponse {
  companyId: string;
  workflowType: WorkflowType;
  recommendations: SeoRecommendation;
  metadata: ResponseMetaData;
  actionId: string;
}

const EXPECTED_RESULTS = ['recommendations'];

function constructInputs(
  scraperResult: ScraperResult,
  keywords: string,
): Record<string, unknown> {
  return {
    meta_title: scraperResult.metaTitle || '',
    meta_description: scraperResult.metaDescription || '',
    plaintext: scraperResult.markdown,
    primary_keyword: keywords,
    target_url: scraperResult.url,
    main_heading: scraperResult.mainHeading?.content || '',
  };
}

export function isValidPageType(value: unknown): value is ScrapedPageType {
  return (
    typeof value === 'string' &&
    Object.values(ScrapedPageType).includes(value as ScrapedPageType)
  );
}

export const handler = async (
  event: RecommendationGeneratorEvent,
  context: Context,
): Promise<RecommendationGeneratorResponse> => {
  const logger = createContextLogger('recommendation-generator', context, {
    lambdaName: 'recommendation-generator',
    actionId: event.actionId,
    companyId: event.scraperResult?.companyId,
  });
  logger.info('Starting recommendation generator', { event });

  const { actionId } = event;

  if (!actionId || typeof actionId !== 'string') {
    throw new RecommendationGeneratorError('Action ID is required');
  }

  try {
    const config = getConfig();
    const promptService = serviceFactory.createPromptService(logger);
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    const { action, skipStep } = await scheduledActionService.checkAction(
      actionId,
      EXPECTED_RESULTS,
    );

    if (!action) {
      throw new RecommendationGeneratorError('Action not found');
    }

    if (skipStep) {
      logger.info('Skipping step');
      // if skipStep then we know that generationPayload exists and has recommendations
      return {
        companyId: action.companyId,
        actionId,
        workflowType: action.workflowType,
        metadata: {
          scraperResult: event.scraperResult,
          keywords: event.keywords,
        },
        recommendations: action.generationPayload!
          .recommendations as SeoRecommendation,
      };
    }

    const oldGenerationPayload = action.generationPayload || {};

    const inputs = constructInputs(event.scraperResult, event.keywords);

    const result = await promptService.executeChatPrompt(
      config.prompt.langfuse.prompts.recommendationGenerator,
      inputs,
      SeoRecommendationSchema,
    );

    const seoRecommendation = result.content as SeoRecommendation;

    const { metaTitle, metaDescription, mainHeading } = seoRecommendation;

    const recommendations = {
      metaTitle,
      metaDescription,
      ...(event.scraperResult.type !== ScrapedPageType.HOMEPAGE && {
        mainHeading,
      }),
    };

    await scheduledActionService.update(actionId, {
      generationPayload: {
        ...oldGenerationPayload,
        recommendations,
      },
    });

    logger.info('Recommendation generation completed', {
      ...recommendations,
    });

    return {
      companyId: action.companyId,
      workflowType: action.workflowType,
      recommendations,
      metadata: {
        scraperResult: event.scraperResult,
        keywords: event.keywords,
      },
      actionId: event.actionId,
    };
  } catch (error) {
    handleHandlerError(error, logger, 'recommendation-generator');
  }
};
