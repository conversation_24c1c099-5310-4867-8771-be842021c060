import { SFNClient } from '@aws-sdk/client-sfn';
import { Context } from 'aws-lambda';

import { getConfig, EnvironmentConfig } from '../config';
import { serviceFactory } from '../factories';
import { handleHandlerError } from '../factories/LoggerFactory';
import { createContextLogger, ContextLogger } from '../logger/contextLogger';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';

interface StepFunctionError {
  Error: string;
  Cause: string; // A stringified JSON object more likely.
}
interface CatchFailureEvent {
  actionId: string;
  error: StepFunctionError;
  [prop: string]: any; // other stuff from the original step function
}

/**
 * Initialize the services needed for the handler
 */
function initializeServices(logger: ContextLogger): {
  config: EnvironmentConfig;
  scheduledActionService: IScheduledActionService;
  sfnClient: SFNClient;
} {
  const config = getConfig();

  // Create services using the unified service factory
  // This abstracts away whether we're using mock or real implementations
  const scheduledActionService =
    serviceFactory.createScheduledActionService(logger);

  // Create SFN client (mock failures are configured in the factory)
  const sfnClient = serviceFactory.createSfnClient(logger);

  logger.info('Services initialized');

  return { config, scheduledActionService, sfnClient };
}

function processError(error: StepFunctionError): Record<string, unknown> {
  try {
    return JSON.parse(error.Cause);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (_e) {
    return { Error: error.Error, Cause: error.Cause };
  }
}

export const handler = async (
  event: CatchFailureEvent,
  context: Context,
): Promise<void> => {
  const logger = createContextLogger('catch-failure', context, {
    lambdaName: 'catch-failure',
    actionId: event.actionId,
  });
  logger.info('Starting catch failure', { event });

  try {
    // Initialize services
    const { scheduledActionService } = initializeServices(logger);

    // Process the failure - setting status to FAILED after retries exhausted
    logger.info(
      'Setting scheduled action to FAILED status after retries exhausted',
      {
        actionId: event.actionId,
        error: event.error,
      },
    );
    await scheduledActionService.updateWithFailure(
      event.actionId,
      processError(event.error),
    );

    logger.info('Catch failure processed successfully');
  } catch (error) {
    handleHandlerError(error, logger, 'catch-failure');
  }
};
