import { Context } from 'aws-lambda';

import { serviceFactory } from '../factories/UnifiedServiceFactory';
import { createContextLogger } from '../logger/contextLogger';
import { Status } from '../types/scheduledAction';

// IMPORTANT: This output will be used as input for the emailer lambda, next step in the Step functions machine
interface SurfacerDaemonResponse {
  success: boolean;
  processedActions: {
    actionId: string;
    companyId: string;
    status: Status;
  }[];
  failedActions: {
    actionId: string;
    companyId: string;
    status: Status;
    error: string;
  }[];
  error?: string;
}

export const handler = async (
  _event: unknown,
  context: Context,
): Promise<SurfacerDaemonResponse> => {
  const logger = createContextLogger('surfacer-daemon', context, {
    lambdaName: 'surfacer-daemon',
  });

  logger.info('Starting surfacer daemon');

  const processedActions: {
    actionId: string;
    companyId: string;
    status: Status;
  }[] = [];
  const failedActions: {
    actionId: string;
    companyId: string;
    status: Status;
    error: string;
  }[] = [];

  try {
    // Placing the surfacedAt here to have a single PIT
    const surfacedAt = new Date();
    // Calculate publishing date (2 days after surfacing date)
    const publishedDate = new Date(surfacedAt);
    publishedDate.setDate(publishedDate.getDate() + 2);
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    // 1. Find all actions that need to be surfaced grouped by company
    logger.info('Finding actions to surface');
    const actionsByCompany =
      await scheduledActionService.findActionsToSurface();
    logger.info('Found actions:', { actionsByCompany });

    // 2. Process each company's actions
    for (const { companyId, actions } of actionsByCompany) {
      const companyLogger = logger.createChild({ companyId });
      companyLogger.info('Processing actions to surface for company', {
        actionCount: actions.length,
      });

      // Process each action
      for (const action of actions) {
        const actionLogger = companyLogger.createChild({
          actionId: action.id,
        });
        try {
          // Validate the action's scheduledToBeSurfacedAt is in the past
          if (
            !action.scheduledToBeSurfacedAt ||
            new Date(action.scheduledToBeSurfacedAt) > surfacedAt
          )
            continue;

          // Update action status and surfaced_at
          const updatedAction = await scheduledActionService.update(action.id, {
            status: Status.SURFACED,
            surfacedAt: surfacedAt.toISOString(),
            scheduledToBePublishedAt: publishedDate.toISOString(),
          });

          processedActions.push({
            actionId: updatedAction.id,
            companyId: updatedAction.companyId,
            status: updatedAction.status,
          });
        } catch (error) {
          actionLogger.error('Failed to update action', {
            error: error instanceof Error ? error.message : String(error),
          });
          failedActions.push({
            actionId: action.id,
            companyId: action.companyId,
            status: action.status,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
    }

    logger.info('SurfacerDaemon completed', {
      surfacedActionIds: processedActions.map(action => action.actionId),
      failedActionIds: failedActions.map(action => action.actionId),
    });

    return {
      success: failedActions.length === 0,
      processedActions,
      failedActions,
    };
  } catch (error) {
    logger.error('SurfacerDaemon failed', {
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      success: false,
      processedActions,
      failedActions,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};
