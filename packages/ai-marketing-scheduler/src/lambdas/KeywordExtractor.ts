import { Context } from 'aws-lambda';

import { getConfig } from '../config';
import { KeywordExtractorError } from '../errors/';
import { handleHandlerError } from '../factories/LoggerFactory';
import { serviceFactory } from '../factories/UnifiedServiceFactory';
import { createContextLogger } from '../logger/contextLogger';
import { ScraperResult } from '../types';

const EXPECTED_RESULTS = ['keywords'];
interface KeywordExtractorEvent {
  scraperResult: ScraperResult;
  actionId: string;
}

interface KeywordExtractorResponse {
  keywords: string;
  status: 'success' | 'error';
  message?: string;
  scraperResult: ScraperResult;
  actionId: string;
}

export const handler = async (
  event: KeywordExtractorEvent,
  context: Context,
): Promise<KeywordExtractorResponse> => {
  const logger = createContextLogger('keyword-extractor', context, {
    lambdaName: 'keyword-extractor',
    actionId: event.actionId,
    companyId: event.scraperResult?.companyId,
  });

  if (event.scraperResult?.url) {
    logger.setContext({ url: event.scraperResult.url });
  }
  if (event.scraperResult?.type) {
    logger.setContext({ pageType: event.scraperResult.type });
  }

  logger.info('Starting keyword extraction', { event });
  try {
    const config = getConfig();
    const promptService = serviceFactory.createPromptService(logger);
    const scheduledActionService =
      serviceFactory.createScheduledActionService(logger);

    const { actionId } = event;

    if (!actionId || typeof actionId !== 'string') {
      throw new KeywordExtractorError('Action ID is required');
    }

    const { action, skipStep } = await scheduledActionService.checkAction(
      actionId,
      EXPECTED_RESULTS,
    );

    if (!action) {
      throw new KeywordExtractorError('Action not found');
    }

    if (skipStep) {
      logger.info('Skipping step');
      // if skipStep then we know that generationPayload exists and has keywords
      return {
        status: 'success',
        actionId,
        keywords: action.generationPayload!.keywords as string,
        scraperResult: event.scraperResult,
      };
    }

    const originalGenerationPayload = action.generationPayload || {};

    const { content: keywords } = await promptService.executeChatPrompt(
      config.prompt.langfuse.prompts.keywordMapper,
      {
        markdown: event.scraperResult.markdown,
        url: event.scraperResult.url,
      },
    );

    logger.info('Keyword extraction completed', {
      keywords,
    });

    await scheduledActionService.update(actionId, {
      generationPayload: {
        ...originalGenerationPayload,
        keywords,
      },
    });

    return {
      keywords,
      status: 'success',
      scraperResult: event.scraperResult,
      actionId: event.actionId,
    };
  } catch (error) {
    return handleHandlerError(error, logger, 'keyword-extractor');
  }
};
