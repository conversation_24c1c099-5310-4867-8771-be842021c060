// import { APIGatewayProxyEvent, Context } from 'aws-lambda';
// import { Context } from 'aws-lambda';
// import dotenv from 'dotenv';

// import { handler } from './Scraper';

// import { handler } from './StoreSEODraft';
// import { handler } from './EmailerDaemon';
// import { event } from '../testEvents/StoreSEODraftEvent';
// import { event } from '../testEvents/StoreSEODraftEvent';

// import { handler } from './RankQueuerDaemon';
// import { event } from '../testEvents/RankQueuerDaemonEvent';

// async function run() {
//   try {
//     const result = await handler(event, {} as Context);
//     console.log('Lambda Result:', JSON.stringify(result, null, 2));
//   } catch (err) {
//     console.error('Lambda Error:', err);
//   }
//   process.exit(0);
// }

// void run();

// import { handler } from './RankWebhook';
// import { event } from '../testEvents/rankWebhookEvent';

// async function run() {
//   try {
//     const result = await handler(
//       event as unknown as APIGatewayProxyEvent,
//       {} as Context,
//     );
//     console.log('Lambda Result:', JSON.stringify(result, null, 2));
//   } catch (err) {
//     console.error('Lambda Error:', err);
//   }
//   process.exit(0);
// }

// void run();

// import { Context } from 'aws-lambda';
// import dotenv from 'dotenv';

// import { handler } from './GetSurfacedActions';

// dotenv.config();

// async function run() {
//   try {
//     const result = await handler({} as Record<string, unknown>, {} as Context);
//     console.log('Lambda Result:', JSON.stringify(result, null, 2));
//   } catch (err) {
//     console.error('Lambda Error:', err);
//   }
//   process.exit(0);
// }

// void run();

import { Context } from 'aws-lambda';
import dotenv from 'dotenv';

import { handler, PublisherEvent } from './Publisher';

dotenv.config();

async function run() {
  try {
    const result = await handler(
      {
        actionId: '5026ab58-d7fe-40c3-972b-c85336f96243',
        companyId: '56be239a-0cb8-4c0f-8553-0156e96900ce',
        workflowType: 'SEO',
      } as PublisherEvent,
      {} as Context,
    );
    console.log('Lambda Result:', JSON.stringify(result, null, 2));
  } catch (err) {
    console.error('Lambda Error:', err);
  }
  process.exit(0);
}

void run();
