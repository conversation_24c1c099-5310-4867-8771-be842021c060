# Service Factories

This directory contains all factory patterns for creating services and clients in the AI Marketing Scheduler.

## Architecture Overview

The factory pattern is used to abstract the creation of services and clients, allowing for easy switching between real and mock implementations based on configuration.

## Main Factories

### 🎯 `UnifiedServiceFactory` (Primary)

**Main factory that all developers should use.** Automatically chooses between real and mock implementations based on configuration.

```typescript
import { serviceFactory } from '../factories';

// Automatically uses real or mock based on config.mock.useMockClients
const scheduledActionService = serviceFactory.createScheduledActionService(logger);
const tenantService = serviceFactory.createTenantService(logger);
const sfnClient = serviceFactory.createSfnClient(logger);
```

**Features:**

- ✅ Automatic mock/real switching via configuration
- ✅ Comprehensive configuration validation  
- ✅ Configurable mock failure scenarios
- ✅ Extensive logging for debugging
- ✅ Type-safe with full interfaces

### 🧪 `MockServiceFactory` (Testing)

**Specialized factory for creating mock implementations only.** Useful for testing scenarios where you want to force mock behavior.

```typescript
import { MockServiceFactory } from '../factories';

// Always returns mock implementations
const mockScheduledActionService = MockServiceFactory.createScheduledActionService(logger);
```

## Usage Examples

### Usage

```typescript
import { serviceFactory } from '../factories';

// The singleton instance automatically handles configuration
const scheduledActionService = serviceFactory.createScheduledActionService(logger);
```

## Testing with Factories

For testing, use the centralized test utilities from `src/utils/testUtils.ts` which provide mock factory functions:

```typescript
import { 
  createMockScheduledActionService,
  createMockTenantService,
  createMockSeoApiGatewayClient 
} from '../utils/testUtils';

// In tests, these return properly mocked implementations
const mockService = createMockScheduledActionService();
```

## Benefits of This Organization

🎯 **Clear Purpose**: Factories are prominently placed and easy to find  
🔄 **Centralized**: All factory patterns in one location  
🧪 **Testable**: Easy switching between real and mock implementations  
📚 **Maintainable**: Related code stays together  
🚀 **Scalable**: Easy to add new factory patterns  
🔒 **Type-Safe**: Full TypeScript support with interfaces
