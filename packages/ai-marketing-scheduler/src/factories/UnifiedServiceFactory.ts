import { LambdaClient } from '@aws-sdk/client-lambda';
import { SFNClient } from '@aws-sdk/client-sfn';
import { GraphQLClient } from 'graphql-request';

import {
  SeoApiGatewayClient,
  TenantClient,
  ApiGatewayClient,
  StarApiClient,
} from '../clients';
import { IApiGatewayClient } from '../clients/interfaces/IApiGatewayClient';
import { ISeoApiGatewayClient } from '../clients/interfaces/ISeoApiGatewayClient';
import { IStarApiClient } from '../clients/interfaces/IStarApiClient';
import { getConfig, EnvironmentConfig } from '../config';
import { ContextLogger } from '../logger/contextLogger';
import {
  MockSeoApiGatewayClient,
  MockTenantClient,
  MockSFNClient,
  MockApiGatewayClient,
  MockStarApiClient,
  MockScheduledActionService,
  MockStarApiService,
} from '../mocks';
import { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
import { IServiceFactory } from '../services/interfaces/IServiceFactory';
import { IStarApiService } from '../services/interfaces/IStarApiService';
import { ITenantService } from '../services/interfaces/ITenantService';
import { PromptService } from '../services/PromptService';
import { ScheduledActionService } from '../services/ScheduledActionService';
import { StarApiService } from '../services/StarApiService';
import { TenantService } from '../services/TenantService';

/**
 * Unified Service Factory that abstracts the creation of real or mock services
 * based on configuration settings.
 *
 * This factory provides a centralized way to create services with automatic
 * mock/real implementation switching based on configuration.
 *
 * Usage:
 * ```typescript
 * // Using the singleton instance
 * const scheduledActionService = serviceFactory.createScheduledActionService(logger);
 *
 * // Or creating a new instance
 * const factory = new UnifiedServiceFactory();
 * const tenantService = factory.createTenantService(logger);
 * ```
 *
 * Configuration:
 * - Set config.mock.useMockClients = true for mock implementations
 * - Set config.mock.useMockClients = false for real implementations
 * - Set config.mock.actionIdsToFail = ['id1', 'id2'] to customize mock failures
 */
export class UnifiedServiceFactory implements IServiceFactory {
  private readonly forceMockMode?: boolean;
  /**
   * Creates a new factory.
   * @param forceMockMode If `true`, always return mock clients even when the
   *                      config disables them. Defaults to `false`.
   */
  constructor(forceMockMode?: boolean) {
    this.forceMockMode = forceMockMode;
  }

  /**
   * Creates a service for scheduled action operations
   *
   * Mock mode: Uses MockScheduledActionService with in-memory storage for testing
   * Real mode: Uses actual ScheduledActionService with GraphQL API client
   *
   * @param logger Logger instance
   * @returns A scheduled action service implementation
   */
  createScheduledActionService(logger: ContextLogger): IScheduledActionService {
    const { useMockClients } = this.shouldUseMockClients(logger);

    const serviceLogger = logger.createComponentLogger({
      serviceName: 'ScheduledActionService',
    });

    if (useMockClients) {
      return new MockScheduledActionService();
    } else {
      const config = getConfig();

      const seoApiGatewayClient = this.createRealSeoApiGatewayClient(
        config,
        logger,
      );
      return new ScheduledActionService(seoApiGatewayClient, serviceLogger);
    }
  }

  /**
   * Creates an SFN client based on the configuration
   *
   * Mock mode: Uses MockSFNClient with configurable failure actions for testing
   * Real mode: Uses actual AWS SFN client with proper region configuration
   *
   * @param logger Logger instance
   * @returns SFNClient instance
   */
  createSfnClient(logger: ContextLogger): SFNClient {
    const { useMockClients } = this.shouldUseMockClients(logger);

    if (useMockClients) {
      const actionsToFail = this.getMockFailureActions();
      return new MockSFNClient(logger, { actionIdsToFail: actionsToFail });
    } else {
      const awsRegion = process.env.AWS_REGION || 'us-east-1';
      return new SFNClient({
        region: awsRegion,
      });
    }
  }

  /**
   * Creates a Lambda client based on the configuration
   *
   * Mock mode: Uses mock Lambda client for testing (not implemented in this example)
   * Real mode: Uses actual AWS Lambda client with proper region configuration
   *
   * @param logger Logger instance
   * @returns LambdaClient instance
   */
  createLambdaClient(logger: ContextLogger): LambdaClient {
    const { useMockClients } = this.shouldUseMockClients(logger);

    if (useMockClients) {
      const awsRegion = process.env.AWS_REGION || 'us-east-1';
      return new LambdaClient({
        region: awsRegion,
      });
    } else {
      const awsRegion = process.env.AWS_REGION || 'us-east-1';
      return new LambdaClient({
        region: awsRegion,
      });
    }
  }

  /**
   * Creates a service for tenant operations
   *
   * Mock mode: Uses MockTenantClient with predefined entitlements for testing
   * Real mode: Uses actual tenant API client connecting to tenantApi
   *
   * @param logger Logger instance
   * @returns A tenant service implementation
   */
  createTenantService(logger: ContextLogger): ITenantService {
    const { useMockClients } = this.shouldUseMockClients(logger);

    const serviceLogger = logger.createComponentLogger({
      serviceName: 'TenantService',
    });

    if (useMockClients) {
      const mockTenantClient = this.createMockTenantClient(logger);
      return new TenantService(mockTenantClient, serviceLogger);
    } else {
      const config = getConfig();
      const tenantClient = this.createRealTenantClient(config, logger);
      return new TenantService(tenantClient, serviceLogger);
    }
  }

  /**
   * Determines whether to use mock implementations based on configuration
   * @returns boolean indicating if mock clients should be used
   */
  private shouldUseMockClients(logger: ContextLogger): {
    useMockClients: boolean;
    useMockStar: boolean;
  } {
    // If forceMockMode is explicitly set, it takes precedence over config
    if (this.forceMockMode !== undefined) {
      logger.info('Using forceMockMode');
      return {
        useMockClients: this.forceMockMode,
        useMockStar: this.forceMockMode,
      };
    }

    const config = getConfig();
    const useMockClients = config.mock?.useMockClients || false;
    const useMockStar = config.mock?.useMockStar || false;
    if (useMockClients || useMockStar) {
      logger.info('Using mock clients', {
        useMockClients,
        useMockStar,
      });
    }
    return {
      useMockClients,
      useMockStar,
    };
  }

  /**
   * Creates a real API Gateway client with proper configuration
   * @param config Validated environment configuration
   * @param logger Logger instance
   * @returns Configured SeoApiGatewayClient
   */
  private createRealSeoApiGatewayClient(
    config: EnvironmentConfig,
    logger: ContextLogger,
  ): SeoApiGatewayClient {
    const graphqlClient = new GraphQLClient(
      `${config.seoAutomationApi.url}/graphql`,
    );
    const m2mSuperApiKey = String(config.seoAutomationApi.m2mSuperApiKey);
    const clientMarketingServiceUrl = config.clientMarketingApi.url;

    const clientLogger = logger.createComponentLogger({
      clientName: 'SeoApiGatewayClient',
    });

    return new SeoApiGatewayClient(
      graphqlClient,
      clientLogger,
      m2mSuperApiKey,
      clientMarketingServiceUrl,
    );
  }

  /**
   * Creates a mock API Gateway client for testing
   * @param logger Logger instance
   * @returns Configured MockSeoApiGatewayClient
   */
  private createMockSeoApiGatewayClient(
    logger: ContextLogger,
  ): MockSeoApiGatewayClient {
    const clientLogger = logger.createComponentLogger({
      clientName: 'SeoApiGatewayClient',
    });
    return new MockSeoApiGatewayClient(clientLogger);
  }

  /**
   * Creates a real tenant client with proper configuration
   * @param config Validated environment configuration
   * @param logger Logger instance
   * @returns Configured TenantClient
   */
  private createRealTenantClient(
    config: EnvironmentConfig,
    logger: ContextLogger,
  ): TenantClient {
    const clientLogger = logger.createComponentLogger({
      clientName: 'TenantClient',
    });

    return new TenantClient(
      config.tenantApi.url,
      config.tenantApi.entitlementsLimit,
      clientLogger,
      config.tenantApi.productIds,
    );
  }

  /**
   * Creates a mock tenant client for testing
   * @param logger Logger instance
   * @returns Configured MockTenantClient
   */
  private createMockTenantClient(logger: ContextLogger): MockTenantClient {
    const clientLogger = logger.createComponentLogger({
      clientName: 'TenantClient',
    });
    return new MockTenantClient(clientLogger);
  }

  /**
   * Gets the list of action IDs that should fail in mock mode
   * @returns Array of action IDs that should fail
   */
  private getMockFailureActions(): string[] {
    const config = getConfig();
    return config.mock?.actionIdsToFail || [];
  }

  /**
   * Creates an API Gateway client based on configuration
   *
   * Mock mode: Uses MockSeoApiGatewayClient
   * Real mode: Uses actual GraphQL API client connecting to seoAutomationApi
   *
   * @param logger Logger instance
   * @returns An API Gateway client implementation
   */
  createSeoApiGatewayClient(logger: ContextLogger): ISeoApiGatewayClient {
    const { useMockClients } = this.shouldUseMockClients(logger);

    if (useMockClients) {
      return this.createMockSeoApiGatewayClient(logger);
    } else {
      const config = getConfig();
      return this.createRealSeoApiGatewayClient(config, logger);
    }
  }

  private createMockApiGatewayClient(
    logger: ContextLogger,
  ): MockApiGatewayClient {
    return new MockApiGatewayClient(logger);
  }

  createApiGatewayClient(logger: ContextLogger): IApiGatewayClient {
    const { useMockClients } = this.shouldUseMockClients(logger);

    const clientLogger = logger.createComponentLogger({
      clientName: 'ApiGatewayClient',
    });

    if (useMockClients) {
      return this.createMockApiGatewayClient(clientLogger);
    } else {
      const config = getConfig();

      // Validate general API Gateway configuration
      if (!config.apiGateway?.url) {
        throw new Error('Missing required configuration: apiGateway.url');
      }
      if (!config.apiGateway?.superUser) {
        throw new Error('Missing required configuration: apiGateway.superUser');
      }
      if (!config.apiGateway?.apiGatewayKey) {
        throw new Error(
          'Missing required configuration: apiGateway.apiGatewayKey',
        );
      }

      const graphqlClient = new GraphQLClient(
        `${config.apiGateway.url}/graphql`,
      );
      return new ApiGatewayClient(
        config.apiGateway.superUser,
        config.apiGateway.apiGatewayKey,
        graphqlClient,
        clientLogger,
      );
    }
  }

  createPromptService(logger: ContextLogger): PromptService {
    const serviceLogger = logger.createComponentLogger({
      serviceName: 'PromptService',
    });
    return new PromptService(serviceLogger);
  }

  /**
   * Creates a STAR API client based on configuration
   *
   * Mock mode: Returns a MockStarApiClient for testing
   * Real mode: Uses actual STAR API client connecting to starApi URL
   *
   * @param logger Logger instance
   * @returns A STAR API client implementation
   */
  createStarApiClient(logger: ContextLogger): IStarApiClient {
    const { useMockClients, useMockStar } = this.shouldUseMockClients(logger);

    const clientLogger = logger.createComponentLogger({
      clientName: 'StarApiClient',
    });

    // Check both general mock flag and specific STAR mock flag
    if (useMockClients || useMockStar) {
      return new MockStarApiClient(clientLogger);
    }

    // Validate STAR API configuration
    if (!getConfig().starApi?.url) {
      throw new Error('Missing required configuration: starApi.url');
    }

    return new StarApiClient(getConfig().starApi, clientLogger);
  }

  /**
   * Creates a STAR API service based on configuration
   *
   * Mock mode: Returns MockStarApiService for testing
   * Real mode: Uses actual STAR API service with real StarApiClient
   *
   * @param logger Logger instance
   * @returns A STAR API service implementation
   */
  createStarApiService(logger: ContextLogger): IStarApiService {
    const { useMockClients, useMockStar } = this.shouldUseMockClients(logger);

    const serviceLogger = logger.createComponentLogger({
      serviceName: 'StarApiService',
    });

    // Check both general mock flag and specific STAR mock flag
    if (useMockClients || useMockStar) {
      return new MockStarApiService();
    }

    const starApiClient = this.createStarApiClient(logger);
    return new StarApiService(starApiClient, serviceLogger);
  }
}

// Export a singleton instance for convenience
export const serviceFactory = new UnifiedServiceFactory();
