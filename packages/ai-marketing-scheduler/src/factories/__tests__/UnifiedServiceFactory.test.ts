import { StarApiClient } from '../../clients/StarApiClient';
import { EnvironmentConfig } from '../../config';
import { ContextLogger } from '../../logger/contextLogger';
import {
  MockStarApiClient,
  MockScheduledActionService,
  MockStarApiService,
} from '../../mocks';
import { ScheduledActionService } from '../../services/ScheduledActionService';
import { StarApiService } from '../../services/StarApiService';
import { createMockContextLogger } from '../../utils/testUtils';
import { UnifiedServiceFactory } from '../UnifiedServiceFactory';

jest.mock('@aws-lambda-powertools/logger');
jest.mock('../../config', () => ({
  getConfig: jest.fn(),
}));

describe('UnifiedServiceFactory', () => {
  let factory: UnifiedServiceFactory;
  let mockLogger: jest.Mocked<ContextLogger>;

  const createBaseConfig = (overrides?: Partial<EnvironmentConfig>): any => {
    return {
      seoAutomationApi: {
        url: 'https://test-seo-api.com',
        m2mSuperApiKey: 'test-m2m-key',
      },
      tenantApi: {
        url: 'https://test-tenant-api.com',
        entitlementsLimit: 100,
        entitlementsChunkSize: 10,
        productIds: ['product-1'],
      },
      starApi: {
        url: 'https://star-api.example.com',
      },
      clientMarketingApi: {
        url: 'https://test-client-marketing.com',
      },
      mock: {
        useMockClients: false,
        useMockStar: false,
      },
      ...overrides,
    };
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockLogger = createMockContextLogger();

    factory = new UnifiedServiceFactory();
  });

  describe('createStarApiClient', () => {
    it('should create a StarApiClient with valid configuration', () => {
      const mockConfig = createBaseConfig();

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const client = factory.createStarApiClient(mockLogger);

      expect(client).toBeInstanceOf(StarApiClient);
    });

    it('should throw error when starApi.url is missing', () => {
      const mockConfig = createBaseConfig({
        starApi: { url: '' },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      expect(() => factory.createStarApiClient(mockLogger)).toThrow(
        'Missing required configuration: starApi.url',
      );
    });

    it('should throw error when starApi is missing entirely', () => {
      const mockConfig = createBaseConfig();
      delete mockConfig.starApi;

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      expect(() => factory.createStarApiClient(mockLogger)).toThrow(
        'Missing required configuration: starApi.url',
      );
    });

    it('should create MockStarApiClient in mock mode', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true,
          useMockStar: true,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const client = factory.createStarApiClient(mockLogger);

      expect(client).toBeInstanceOf(MockStarApiClient);
    });

    it('should use forceMockMode when set to true', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: false, // This should be overridden by forceMockMode
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithMock = new UnifiedServiceFactory(true);
      const client = factoryWithMock.createStarApiClient(mockLogger);

      expect(client).toBeInstanceOf(MockStarApiClient);
    });

    it('should use forceMockMode when set to false', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true, // This should be overridden by forceMockMode
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithoutMock = new UnifiedServiceFactory(false);
      const client = factoryWithoutMock.createStarApiClient(mockLogger);

      expect(client).toBeInstanceOf(StarApiClient);
    });
  });

  describe('createScheduledActionService', () => {
    it('should create a ScheduledActionService with valid configuration', () => {
      const mockConfig = createBaseConfig();

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const service = factory.createScheduledActionService(mockLogger);

      expect(service).toBeInstanceOf(ScheduledActionService);
    });

    it('should create MockScheduledActionService in mock mode', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true,
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const service = factory.createScheduledActionService(mockLogger);

      expect(service).toBeInstanceOf(MockScheduledActionService);
    });

    it('should use forceMockMode when set to true', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: false, // This should be overridden by forceMockMode
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithMock = new UnifiedServiceFactory(true);
      const service = factoryWithMock.createScheduledActionService(mockLogger);

      expect(service).toBeInstanceOf(MockScheduledActionService);
    });

    it('should use forceMockMode when set to false', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true, // This should be overridden by forceMockMode
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithoutMock = new UnifiedServiceFactory(false);
      const service =
        factoryWithoutMock.createScheduledActionService(mockLogger);

      expect(service).toBeInstanceOf(ScheduledActionService);
    });
  });

  describe('createStarApiService', () => {
    it('should create a StarApiService with valid configuration', () => {
      const mockConfig = createBaseConfig();

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const service = factory.createStarApiService(mockLogger);

      expect(service).toBeInstanceOf(StarApiService);
    });

    it('should create MockStarApiService in mock mode', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true,
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const service = factory.createStarApiService(mockLogger);

      expect(service).toBeInstanceOf(MockStarApiService);
    });

    it('should create MockStarApiService when useMockStar is true', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: false,
          useMockStar: true,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const service = factory.createStarApiService(mockLogger);

      expect(service).toBeInstanceOf(MockStarApiService);
    });

    it('should use forceMockMode when set to true', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: false,
          useMockStar: false,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithMock = new UnifiedServiceFactory(true);
      const service = factoryWithMock.createStarApiService(mockLogger);

      expect(service).toBeInstanceOf(MockStarApiService);
    });

    it('should use forceMockMode when set to false', () => {
      const mockConfig = createBaseConfig({
        mock: {
          useMockClients: true,
          useMockStar: true,
        },
      });

      const configModule = jest.requireMock('../../config');
      configModule.getConfig.mockReturnValue(mockConfig);

      const factoryWithoutMock = new UnifiedServiceFactory(false);
      const service = factoryWithoutMock.createStarApiService(mockLogger);

      expect(service).toBeInstanceOf(StarApiService);
    });
  });
});
