/**
 * Factory exports for creating services and clients
 *
 * This module provides the main entry point for all factory patterns
 * used throughout the application.
 */

// Main factory - this is what most developers should use
export { UnifiedServiceFactory, serviceFactory } from './UnifiedServiceFactory';

export * as LoggerFactory from './LoggerFactory';

// Re-export commonly used types for convenience
export type { IServiceFactory } from '../services/interfaces/IServiceFactory';
export type { IScheduledActionService } from '../services/interfaces/IScheduledActionService';
export type { ITenantService } from '../services/interfaces/ITenantService';
