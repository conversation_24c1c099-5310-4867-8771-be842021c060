import { BaseError } from '../errors';
import {
  Context<PERSON>og<PERSON>,
  LoggerContext,
  ComponentContext,
  createContextLogger,
} from '../logger/contextLogger';
import {
  createLogger,
  setAwsRequestId,
  resetAwsRequestId,
} from '../logger/createLogger';

// Re-export for backward compatibility
export { createLogger, setAwsRequestId, resetAwsRequestId };

/**
 * Handle errors in the main handler
 */
export function handleHandlerError(
  error: unknown,
  logger: ContextLogger,
  handlerName: string,
): never {
  const errorMessage = error instanceof Error ? error.message : String(error);
  const errorStack = error instanceof Error ? error.stack : undefined;
  const errorName = error instanceof Error ? error.name : 'UnknownError';

  logger.error(`Error in ${handlerName}`, {
    errorType: errorName,
    error: errorMessage,
    stack: errorStack,
    // Include additional context if it's our custom error
    ...(error instanceof BaseError && {
      metadata: error.metadata,
    }),
  });

  // Re-throw the error to ensure it bubbles up
  throw error;
}

// Re-export context logger functionality for convenience
export { ContextLogger, LoggerContext, ComponentContext, createContextLogger };
