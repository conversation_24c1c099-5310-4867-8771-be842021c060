import { z } from 'zod';

export const SeoRecommendationItemSchema = z.object({
  currentValue: z.string(),
  recommendationValue: z.string(),
  reasoning: z.string(),
});

export const SeoRecommendationSchema = z.object({
  metaTitle: SeoRecommendationItemSchema,
  metaDescription: SeoRecommendationItemSchema,
  mainHeading: SeoRecommendationItemSchema.nullable().optional(),
});

export type SeoRecommendationItem = z.infer<typeof SeoRecommendationItemSchema>;
export type SeoRecommendation = z.infer<typeof SeoRecommendationSchema>;
