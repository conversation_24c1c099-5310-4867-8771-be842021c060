export enum RecommendationType {
  META_TITLE = 'META_TITLE',
  META_DESCRIPTION = 'META_DESCRIPTION',
  MAIN_HEADING = 'MAIN_HEADING',
}

// Simple mapping object from scraped element keys to recommendation types
// This ensures we have a consistent mapping between scraped elements and recommendation types
// When adding new scraped elements, add them to this mapping
// If they have a new recommendation type, make sure to add it to the enum above
export const scrapedElementToRecommendationType: Record<
  string,
  RecommendationType
> = {
  metaTitle: RecommendationType.META_TITLE,
  metaDescription: RecommendationType.META_DESCRIPTION,
  h1: RecommendationType.MAIN_HEADING,
};

export interface Recommendation {
  metaTitle: { currentValue: string; recommendationValue: string };
  metaDescription: { currentValue: string; recommendationValue: string };
  mainHeading: { currentValue: string; recommendationValue: string };
}

export type ScrapedElementKey = keyof typeof scrapedElementToRecommendationType;
