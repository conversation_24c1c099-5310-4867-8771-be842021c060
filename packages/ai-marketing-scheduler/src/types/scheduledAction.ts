import { StoreSEODraftResponse } from '../clients/types/seoApiGateway';

export enum WorkflowType {
  SEO = 'SEO',
  BLOG = 'BLOG',
}

export enum Status {
  DRAFT_PENDING = 'DRAFT_PENDING',
  HUMAN_QA_PENDING = 'HUMAN_QA_PENDING',
  SURFACING_PENDING = 'SURFACING_PENDING',
  SURFACED = 'SURFACED',
  PUBLISHED = 'PUBLISHED',
  FAILED = 'FAILED',
  INVALIDATED = 'INVALIDATED',
}

export interface ScheduledAction {
  id: string;
  companyId: string;
  workflowType: WorkflowType;
  status: Status;
  createdAt: string;
  updatedAt: string;
  surfacedAt: string | null;
  scheduledToBePublishedAt: string | null;
  scheduledToBeSurfacedAt: string | null;
  publishedAt: string | null;
  contentPayload: Record<string, unknown> | StoreSEODraftResponse | null;
  generationPayload: Record<string, unknown> | null;
  failureReason: Record<string, unknown>;
  executionName: string | null;
  executionArn: string | null;
  groupScheduledActions?: {
    id: string;
    groupId: string;
  }[];
}

export interface CheckActionResult {
  action: ScheduledAction | null;
  skipStep: boolean;
}
