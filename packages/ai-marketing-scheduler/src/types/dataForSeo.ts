// @see https://docs.dataforseo.com/v3/serp/google/organic/task_get/regular/
export type RankingUrlResult = {
  datetime: string;
  items: Array<{
    type: string;
    rank_group: number;
    url: string;
  }>;
};

export type DataForSeoTask<T> = {
  id: string;
  status_code: number;
  status_message?: string;
  result: T[];
  data: {
    keyword: string;
    tag: string;
  };
};

export type DataForSeoResponse<T> = {
  tasks_error?: number;
  tasks: DataForSeoTask<T>[];
};
export type RankingUrlTask = DataForSeoTask<RankingUrlResult>;

export type RankingUrlTasks = DataForSeoResponse<RankingUrlResult>;
