export enum ScrapedPageType {
  HOMEPAGE = 'HOMEPAGE',
  HOME_VALUATION = 'HOME_VALUATION',
  MORTGAGE_CALCULATOR = 'MORTGAGE_CALCULATOR',
  BUYERS_GUIDE = 'BUYERS_GUIDE',
  SELLERS_GUIDE = 'SELLERS_GUIDE',
  NEIGHBORHOOD_GUIDE = 'NEIGHBORHOOD_GUIDE',
  AGENT_BIO = 'AGENT_BIO',
  BLOG = 'BLOG',
}

export interface ScraperResult {
  firecrawlId: string;
  url: string;
  metaTitle?: string;
  metaDescription?: string;
  mainHeading: {
    section_id: string;
    element_id: string | null;
    tag: string;
    index: number;
    content: string;
  };
  type: ScrapedPageType;
  markdown: string;
  mediaId: string;
  companyId: string;
}
