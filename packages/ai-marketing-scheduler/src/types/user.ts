export interface UserDTO {
  displayId: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar: string | null;
  teamLead: boolean;
  externalAuthId: string;
  facebookUserId: string | null;
  lastLogin: string | null;
  notificationsLastSeen: string;
  leadEmail: string | null;
  phoneNumber: string | null;
  type: string;
  // Big company object that is not needed
  companies?: Record<string, string>;
}
