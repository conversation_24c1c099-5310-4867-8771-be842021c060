/**
 * Datadog configuration and utilities for AI Marketing Scheduler
 * Centralizes Datadog setup and provides reusable metric functions
 */

// Conditional import to handle test environment
let sendCustomMetric: (
  name: string,
  value: number,
  // 'type' is ignored at runtime; we send distribution metrics
  type?: string,
  tags?: string[],
) => Promise<void>;

// Initialize sendCustomMetric function
function initializeDatadog() {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const datadogLambda = require('datadog-lambda-js');

    if (typeof datadogLambda.sendDistributionMetric === 'function') {
      // Wrap to keep our API stable while calling the supported Datadog function
      sendCustomMetric = (name, value, _type?, tags?) => {
        const validTags = (tags || []).filter(
          (tag): tag is string => typeof tag === 'string' && tag.length > 0,
        );
        return Promise.resolve(
          datadogLambda.sendDistributionMetric(name, value, ...validTags),
        );
      };
    } else if (
      typeof datadogLambda.sendDistributionMetricWithDate === 'function'
    ) {
      // Fallback to WithDate variant
      sendCustomMetric = (name, value, _type?, tags?) => {
        const validTags = (tags || []).filter(
          (tag): tag is string => typeof tag === 'string' && tag.length > 0,
        );
        return Promise.resolve(
          datadogLambda.sendDistributionMetricWithDate(
            name,
            value,
            new Date(),
            ...validTags,
          ),
        );
      };
    } else {
      throw new Error('sendDistributionMetric not found in datadog-lambda-js');
    }
  } catch {
    // In test environment or when datadog-lambda-js is not available
    sendCustomMetric = (
      name: string,
      value: number,
      type?: string,
      tags?: string[],
    ) => {
      // Silent mock for test environment
      if (process.env.NODE_ENV === 'test') {
        return Promise.resolve();
      }
      console.log(
        `📊 [MOCK METRIC] ${name}: ${value} (${type}) [${tags?.join(', ') || 'no tags'}]`,
      );
      return Promise.resolve();
    };
  }
}

// Initialize on module load
initializeDatadog();

export interface DatadogConfig {
  service: string;
  environment: string;
  version: string;
  defaultTags: string[];
}

export const getDatadogConfig = (): DatadogConfig => {
  const environment = process.env.ENVIRONMENT || 'development';

  return {
    service: 'ai-marketing-scheduler',
    environment,
    version: process.env.GIT_SHA || 'latest',
    defaultTags: [
      `environment:${environment}`,
      'team:client-marketing',
      'platform_version:v3',
      'project:seo-automation',
    ],
  };
};

/**
 * Custom metrics namespace for the AI Marketing Scheduler service
 */
export const METRICS = {
  // Entitlement metrics
  ENTITLEMENTS_FETCH_TIME: 'ai_marketing_scheduler.entitlements.fetch_time',
  ENTITLEMENTS_COUNT: 'ai_marketing_scheduler.entitlements.count',
  ENTITLEMENTS_PROCESSED: 'ai_marketing_scheduler.entitlements.processed',
  ENTITLEMENTS_FILTERED_OUT: 'ai_marketing_scheduler.entitlements.filtered_out',
  ENTITLEMENTS_ERRORS: 'ai_marketing_scheduler.entitlements.errors',

  // Website metrics
  WEBSITES_FETCH_TIME: 'ai_marketing_scheduler.websites.fetch_time',

  // Chunk metrics
  CHUNKS_CREATED: 'ai_marketing_scheduler.chunks.created',

  // Performance metrics (execution time/memory available via CloudWatch/Datadog Layer)

  // Business KPI metrics
  ACTIONS_SCHEDULED: 'ai_marketing_scheduler.actions.scheduled',
  DRAFTS_GENERATED: 'ai_marketing_scheduler.drafts.generated',
  RECOMMENDATIONS_CREATED: 'ai_marketing_scheduler.recommendations.created',

  // Error tracking
  LAMBDA_ERRORS: 'ai_marketing_scheduler.lambda.errors',
  SERVICE_ERRORS: 'ai_marketing_scheduler.service.errors',

  // Step Function metrics
  STEP_FUNCTION_EXECUTIONS: 'ai_marketing_scheduler.step_functions.executions',
  STEP_FUNCTION_FAILURES: 'ai_marketing_scheduler.step_functions.failures',
} as const;

/**
 * Utility functions for sending common metrics with standardized tags
 */
export class DatadogMetrics {
  private config: DatadogConfig;

  constructor() {
    this.config = getDatadogConfig();
  }

  /**
   * Sends a timing metric with lambda-specific tags
   */
  async sendTimingMetric(
    metricName: string,
    value: number,
    lambdaName: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'milliseconds', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send timing metric:', error);
      }
    }
  }

  /**
   * Sends a count metric with standardized tags
   */
  async sendCountMetric(
    metricName: string,
    value: number,
    component: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `component:${component}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'count', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send count metric:', error);
      }
    }
  }

  /**
   * Sends a gauge metric with standardized tags
   */
  async sendGaugeMetric(
    metricName: string,
    value: number,
    component: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `component:${component}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'gauge', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send gauge metric:', error);
      }
    }
  }

  /**
   * Sends an error metric with error context
   */
  async sendErrorMetric(
    metricName: string,
    lambdaName: string,
    errorType: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      `error_type:${errorType}`,
      'status:error',
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, 1, 'count', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send error metric:', error);
      }
    }
  }

  /**
   * Sends multiple metrics in parallel for performance
   */
  async sendMetrics(
    metrics: Array<{
      name: string;
      value: number;
      type: 'count' | 'gauge' | 'histogram' | 'milliseconds' | 'megabytes';
      tags: string[];
    }>,
  ): Promise<void> {
    try {
      await Promise.all(
        metrics.map(metric =>
          sendCustomMetric(metric.name, metric.value, metric.type, [
            ...this.config.defaultTags,
            ...metric.tags,
          ]),
        ),
      );
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send metrics:', error);
      }
    }
  }
}

/**
 * Global instance for easy access throughout the application
 */
export const datadogMetrics = new DatadogMetrics();
