base:
  ENVIRONMENT: ${ENVIRONMENT}
  API_GATEWAY_SUPER_USER_COMPANY_ID: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_SUPER_USER_COMPANY_ID
  API_GATEWAY_KEY: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_KEY
  LAUNCHDARKLY_KEY: vault:secret/data/${VAULT_ENV}/standard#LAUNCHDARKLY_KEY
  TENANT_SERVICE_URL: https://tenant-service.${BASE_DOMAIN}
  STAR_API_URL: https://website-service.${BASE_DOMAIN}
  CMS_SERVICE_URL: https://cms-service.${BASE_DOMAIN}
  CLIENT_MARKETING_SERVICE_URL: https://client-marketing-service.${BASE_DOMAIN}
  API_GATEWAY_URL: https://gw.${BASE_DOMAIN}
  COSMO_GQL_URL: https://graphql.${BASE_DOMAIN}
  LANGFUSE_SECRET_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_SECRET_KEY
  LANGFUSE_PUBLIC_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_PUBLIC_KEY
  LANGFUSE_ENVIRONMENT: staging
  LANGFUSE_FLUSH_AT: 1
  LANGFUSE_BASE_URL: https://us.cloud.langfuse.com
  OPENAI_MODEL_NAME: gpt-4o
  OPENAI_TEMPERATURE: 0
  ENTITLEMENTS_LIMIT: 100
  PRODUCT_IDS: a2b3c4d5-f6e7-8a9b-0c1d-2e3f4a5b6c7d
  SECURITY_GROUP_IDS: ${SECURITY_GROUP_IDS}
  SUBNET_IDS: ${SUBNET_IDS}
  DEPLOY_BUCKET: ${DEPLOY_BUCKET}
  ENTITLEMENTS_CHUNK_SIZE: 10
  FIRECRAWL_API_KEY: vault:secret/data/${VAULT_ENV}/ai-marketing-scheduler#FIRECRAWL_API_KEY
  SCREENSHOTS_R2_ACCESS_KEY_ID: vault:secret/data/${VAULT_ENV}/ai-marketing-scheduler#SCREENSHOTS_R2_ACCESS_KEY_ID
  SCREENSHOTS_R2_SECRET_ACCESS_KEY: vault:secret/data/${VAULT_ENV}/ai-marketing-scheduler#SCREENSHOTS_R2_SECRET_ACCESS_KEY
  SCREENSHOTS_R2_ENDPOINT: https://514de882def69fb6d4d25c4681c63887.r2.cloudflarestorage.com
  M2M_SUPER_API_KEY: vault:secret/data/${VAULT_ENV}/standard#DOMAIN_M2M_KEY
  OPENAI_API_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#OPENAI_API_KEY
  DATAFORSEO_LOGIN: vault:secret/data/${VAULT_ENV}/ai-marketing-scheduler#DATAFORSEO_LOGIN
  DATAFORSEO_PASSWORD: vault:secret/data/${VAULT_ENV}/ai-marketing-scheduler#DATAFORSEO_PASSWORD
  DATAFORSEO_BASE_URL: https://api.dataforseo.com
  # sendgrid variables
  WEEKLY_DIGEST_TEMPLATE_ID: d-238f7a85221549feb4e174d36359a01c
  ASM_GROUP_ID: 33812
  BCC_EMAILS: <EMAIL>
  SLACK_NOTIFICATIONS_ENABLED: true
  SLACK_WEBHOOK_URL: *******************************************************************************
  SLACK_SEO_ALERTS_WEBHOOK_URL: *******************************************************************************
production:
  SCHEDULER_STATE: true
  ENTITLEMENTS_STATE: ENABLED
  PUBLISHER_STATE: ENABLED
  SURFACER_DAEMON_STATE: true
  RANK_QUEUER_DAEMON_STATE: ENABLED
  LANGFUSE_ENVIRONMENT: production
  LANGFUSE_FLUSH_AT: 100
  DATAFORSEO_POSTBACK_URL: https://wrk6os0n67.execute-api.us-east-1.amazonaws.com/production/webhooks/dataforseo/rank
  SLACK_WEBHOOK_URL: *******************************************************************************
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
staging:
  DATAFORSEO_POSTBACK_URL: https://p6jkgevkg9.execute-api.us-east-1.amazonaws.com/staging/webhooks/dataforseo/rank
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
