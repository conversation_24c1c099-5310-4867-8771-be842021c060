/**
 * Configuration module for the AI Marketing Scheduler
 */

import { getEnvironmentConfig, getSnowflakeConfig } from './implementation';

export * from './interfaces';

let cachedConfig: import('./interfaces').EnvironmentConfig | null = null;
let cachedSnowflakeConfig: import('./interfaces').SnowflakeConfig | null = null;

/**
 * Get the environment configuration (synchronous)
 * @returns The environment configuration
 */
export function getConfig(): import('./interfaces').EnvironmentConfig {
  if (!cachedConfig) {
    cachedConfig = getEnvironmentConfig();
  }
  return cachedConfig;
}

/**
 * Get Snowflake configuration from AWS SSM Parameter Store (async)
 * @returns Promise<SnowflakeConfig>
 */
export async function getSnowflakeConfigAsync(): Promise<
  import('./interfaces').SnowflakeConfig
> {
  if (!cachedSnowflakeConfig) {
    cachedSnowflakeConfig = await getSnowflakeConfig();
  }
  return cachedSnowflakeConfig;
}
