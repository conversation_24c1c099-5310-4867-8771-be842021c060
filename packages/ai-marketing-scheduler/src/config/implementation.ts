import {
  CmsConfig,
  EnvironmentConfig,
  PromptConfig,
  SeoAutomationConfig,
  StateMachineConfig,
  TenantConfig,
  MockConfig,
  S3Config,
  FirecrawlConfig,
  DataForSeoConfig,
  ApiResponseS3Config,
  StarApiConfig,
  SlackConfig,
  SnowflakeConfig,
} from './interfaces';
import { getSSMParameters } from '../utils/ssmUtils';

/**
 * Get the environment configuration
 * @returns The environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  // API Configuration
  const seoAutomationApi: SeoAutomationConfig = {
    url: process.env.COSMO_GQL_URL || 'http://localhost:3001/graphql',
    m2mSuperApiKey:
      process.env.M2M_SUPER_API_KEY ||
      (process.env.ENVIRONMENT === 'development' || !process.env.ENVIRONMENT
        ? 'localkey'
        : ''),
  };

  // Tenant Configuration
  const tenant: TenantConfig = {
    url: process.env.TENANT_SERVICE_URL || 'http://localhost:8011',
    entitlementsLimit: parseInt(process.env.ENTITLEMENTS_LIMIT || '100', 10),
    entitlementsChunkSize: parseInt(
      process.env.ENTITLEMENTS_CHUNK_SIZE || '10',
      10,
    ),
    productIds: process.env.PRODUCT_IDS
      ? process.env.PRODUCT_IDS.split(',')
      : [],
  };

  // CMS Configuration
  const cms: CmsConfig = {
    url: process.env.CMS_SERVICE_URL || undefined,
  };

  // State Machine Configuration
  const stateMachine: StateMachineConfig = {
    seoDraftArn: process.env.SEO_DRAFT_SFN_ARN || '',
    blogDraftArn: process.env.BLOG_DRAFT_SFN_ARN || '',
  };

  // Mock Configuration
  const mock: MockConfig = {
    useMockClients: process.env.USE_MOCK_CLIENTS === 'true', // Use all mocks including STAR API Client
    useMockStar: process.env.USE_MOCK_STAR === 'true', // Use only mock STAR API Client
    // Add action id's here to simulate failures
    actionIdsToFail: [],
  };

  // S3 Configuration
  const screenshotsS3Config: S3Config = {
    bucketName: process.env.SCREENSHOTS_R2_BUCKET_NAME || '',
    region: 'auto',
    endpoint: process.env.SCREENSHOTS_R2_ENDPOINT || '',
    credentials: {
      accessKeyId: process.env.SCREENSHOTS_R2_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.SCREENSHOTS_R2_SECRET_ACCESS_KEY || '',
    },
  };

  // Rank Response S3 Configuration
  const apiResponseS3Config: ApiResponseS3Config = {
    bucketName: process.env.API_RESPONSE_S3_BUCKET_NAME || '',
  };

  // Firecrawl Configuration
  const firecrawlConfig: FirecrawlConfig = {
    apiKey: process.env.FIRECRAWL_API_KEY || '',
  };

  // Prompt Configuration
  const prompt: PromptConfig = {
    langfuse: {
      secretKey: process.env.LANGFUSE_SECRET_KEY || '',
      publicKey: process.env.LANGFUSE_PUBLIC_KEY || '',
      baseUrl: process.env.LANGFUSE_BASE_URL || 'https://us.cloud.langfuse.com',
      environment: process.env.LANGFUSE_ENVIRONMENT || 'staging',
      flushAt: process.env.LANGFUSE_FLUSH_AT
        ? parseInt(process.env.LANGFUSE_FLUSH_AT, 10)
        : 1,
      prompts: {
        keywordMapper: process.env.KEYWORD_MAPPER_PROMPT || 'keyword_mapper',
        recommendationGenerator:
          process.env.RECOMMENDATION_GENERATOR_PROMPT ||
          'suggestions_title_description_h1',
      },
    },
    openai: {
      modelName: process.env.OPENAI_MODEL_NAME || 'gpt-4o',
      temperature: process.env.OPENAI_TEMPERATURE
        ? parseFloat(process.env.OPENAI_TEMPERATURE)
        : 0,
      apiKey: process.env.OPENAI_API_KEY || '',
    },
  };

  // API Gateway Configuration
  const apiGatewayConfig = {
    url: process.env.API_GATEWAY_URL || '',
    superUser: process.env.API_GATEWAY_SUPER_USER_COMPANY_ID || '',
    apiGatewayKey: process.env.API_GATEWAY_KEY || '',
  };

  const dataForSeoConfig: DataForSeoConfig = {
    username: process.env.DATAFORSEO_LOGIN || '',
    password: process.env.DATAFORSEO_PASSWORD || '',
    postbackUrl: process.env.DATAFORSEO_POSTBACK_URL || '',
    baseUrl: process.env.DATAFORSEO_BASE_URL || 'https://api.dataforseo.com',
  };

  const clientMarketingApi = {
    url: process.env.CLIENT_MARKETING_SERVICE_URL || '',
  };

  const starApiConfig: StarApiConfig = {
    url: process.env.STAR_API_URL || 'http://localhost:8003',
  };

  // Slack Configuration
  const slackConfig: SlackConfig = {
    webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
    enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === 'true',
    seoAlertsWebhookUrl: process.env.SLACK_SEO_ALERTS_WEBHOOK_URL || '',
  };

  return {
    seoAutomationApi,
    tenantApi: tenant,
    cmsApi: cms,
    stateMachine,
    environment: process.env.ENVIRONMENT || 'development',
    launchDarklyKey: process.env.LAUNCHDARKLY_KEY || '',
    generatePostFunctionName: process.env.GENERATE_POST_FUNCTION_NAME || '',
    actionChunkSize: (() => {
      const raw = process.env.ACTION_CHUNK_SIZE ?? '5';
      const chunkSize = Number.parseInt(raw, 10);
      if (!Number.isFinite(chunkSize) || chunkSize < 1) {
        return 10; // Default to 10 if invalid
      }
      return chunkSize;
    })(),
    publishingLambdaArn: process.env.PUBLISHING_LAMBDA_ARN,
    apiGateway: apiGatewayConfig,
    mock,
    screenshotsS3: screenshotsS3Config,
    apiResponseS3: apiResponseS3Config,
    firecrawl: firecrawlConfig,
    prompt,
    sendgrid: {
      weeklyDigestTemplateId:
        process.env.WEEKLY_DIGEST_TEMPLATE_ID ||
        'd-238f7a85221549feb4e174d36359a01c',
      asmGroupId: process.env.ASM_GROUP_ID || '33812',
      bccEmails: process.env.BCC_EMAILS
        ? process.env.BCC_EMAILS.split(',').map(email => email.trim())
        : ['<EMAIL>'],
    },
    dataForSeo: dataForSeoConfig,
    clientMarketingApi,
    starApi: starApiConfig,
    slack: slackConfig,
  };
}

/**
 * Get Snowflake configuration from AWS SSM Parameter Store
 * @returns Promise<SnowflakeConfig>
 */
export async function getSnowflakeConfig(): Promise<SnowflakeConfig> {
  const environment = process.env.ENVIRONMENT || 'development';
  const parameterNames = [
    `/${environment}/ai-marketing-scheduler/snowflake/account`,
    `/${environment}/ai-marketing-scheduler/snowflake/user`,
    `/${environment}/ai-marketing-scheduler/snowflake/database`,
    `/${environment}/ai-marketing-scheduler/snowflake/private-key`,
  ];

  const parameters = await getSSMParameters(parameterNames, true);

  return {
    account:
      parameters[`/${environment}/ai-marketing-scheduler/snowflake/account`] ||
      '',
    username:
      parameters[`/${environment}/ai-marketing-scheduler/snowflake/user`] || '',
    database:
      parameters[`/${environment}/ai-marketing-scheduler/snowflake/database`] ||
      '',
    privateKey:
      parameters[
        `/${environment}/ai-marketing-scheduler/snowflake/private-key`
      ] || '',
  };
}
