import { S3ClientConfig } from '@aws-sdk/client-s3';

/**
 * Interface for API configuration
 */
export interface SeoAutomationConfig {
  /**
   * API Gateway URL
   */
  url: string;

  /**
   * M2M super API key for authentication
   */
  m2mSuperApiKey: string;
}

/**
 * Interface for tenant service configuration
 */
export interface TenantConfig {
  /**
   * Tenant service URL
   */
  url: string;

  /**
   * Entitlements limit
   */
  entitlementsLimit: number;

  /**
   * Entitlements chunk size
   */
  entitlementsChunkSize: number;

  /**
   * Product IDs
   */
  productIds: string[];
}

/**
 * Interface for CMS service configuration
 */
export interface CmsConfig {
  /**
   * CMS service URL (optional)
   */
  url?: string;
}

/**
 * Interface for state machine configuration
 */
export interface StateMachineConfig {
  /**
   * SEO draft state machine ARN
   */
  seoDraftArn: string;

  /**
   * Blog draft state machine ARN
   */
  blogDraftArn: string;
}

/**
 * Interface for S3 configuration
 */
export type S3Config = S3ClientConfig & {
  bucketName: string;
};

/**
 * Interface for API response bucket configuration
 */
export type ApiResponseS3Config = S3ClientConfig & {
  bucketName: string;
};

/**
 * Interface for Firecrawl configuration
 */
export interface FirecrawlConfig {
  /**
   * Firecrawl API key
   */
  apiKey: string;
}

/**
 * Interface for mock configuration
 */
export interface MockConfig {
  /**
   * Whether to use mock clients instead of real ones
   */
  useMockClients: boolean;

  /**
   * Whether to use mock STAR instead of real one
   */
  useMockStar: boolean;

  /**
   * List of action IDs that should fail in mock mode
   */
  actionIdsToFail?: string[];
}

export interface ApiGatewayConfig {
  url: string;
  superUser: string;
  apiGatewayKey: string;
}

export interface EnvironmentConfig {
  /**
   * API configuration
   */
  seoAutomationApi: SeoAutomationConfig;

  /**
   * Tenant configuration
   */
  tenantApi: TenantConfig;

  /**
   * CMS configuration
   */
  cmsApi: CmsConfig;

  /**
   * State machine configuration
   */
  stateMachine: StateMachineConfig;

  /**
   * Environment name
   */
  environment: string;

  /**
   * LaunchDarkly key
   */
  launchDarklyKey: string;

  /**
   * Generate post function name
   */
  generatePostFunctionName: string;

  /**
   * Number of actions to process in each chunk
   */
  actionChunkSize: number;

  /**
   * ARN of the publishing lambda function
   */
  publishingLambdaArn?: string;

  /**
   * API Gateway configuration
   */
  apiGateway: ApiGatewayConfig;

  /**
   * Mock configuration
   */
  mock: MockConfig;

  /**
   * S3 configuration
   */
  screenshotsS3: S3Config;

  /**
   * API response bucket configuration
   */
  apiResponseS3: ApiResponseS3Config;

  /**
   * Firecrawl configuration
   */
  firecrawl: FirecrawlConfig;

  /**
   * Prompt configuration
   */
  prompt: PromptConfig;

  /**
   * Sendgrid configuration
   */
  sendgrid: SendgridConfig;

  /**
   * DataForSeo configuration
   */
  dataForSeo: DataForSeoConfig;

  /**
   * Client Marketing API configuration
   */
  clientMarketingApi: ClientMarketingApiConfig;

  /**
   * STAR API configuration
   */
  starApi: StarApiConfig;

  /**
   * Slack configuration
   */
  slack: SlackConfig;
}

export interface PromptConfig {
  langfuse: LangfuseConfig;
  openai: OpenaiConfig;
}

export interface LangfuseConfig {
  secretKey: string;
  publicKey: string;
  baseUrl: string;
  environment: string;
  flushAt: number;
  prompts: {
    keywordMapper: string;
    recommendationGenerator: string;
  };
}

export interface OpenaiConfig {
  modelName: string;
  temperature: number;
  apiKey: string;
}

export interface DataForSeoConfig {
  username: string;
  password: string;
  postbackUrl: string;
  baseUrl: string;
}

export interface SendgridConfig {
  weeklyDigestTemplateId: string;
  asmGroupId: string;
  bccEmails: string[];
}

export interface ClientMarketingApiConfig {
  url: string;
}

export interface StarApiConfig {
  /**
   * STAR API URL
   */
  url: string;
}

/**
 * Interface for Snowflake configuration
 */
export interface SnowflakeConfig {
  /**
   * Snowflake account
   */
  account: string;

  /**
   * Snowflake username
   */
  username: string;

  /**
   * Snowflake database
   */
  database: string;

  /**
   * Snowflake private key
   */
  privateKey: string;
}

export interface SlackConfig {
  /**
   * Slack webhook URL for notifications
   */
  webhookUrl: string;

  /**
   * Whether to enable Slack notifications
   */
  enabled: boolean;

  /**
   * Slack webhook URL for SEO alerts
   */
  seoAlertsWebhookUrl: string;
}
