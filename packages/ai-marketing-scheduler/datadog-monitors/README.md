# STAR API Datadog Monitor Setup

This directory contains the configuration and scripts needed to set up Datadog monitors for STAR API error alerting.

## 🎯 Overview

**Purpose**: Set up Datadog monitors for STAR API error alerting based on custom metrics

## 📊 Datadog Monitors Created

### 1. [STAR API] Company-Specific Failures

- **Query**: `sum(last_10m):sum:ai_marketing_scheduler.star_api.errors{http_status:400 OR http_status:404 OR http_status:405} by {company_id}.as_count() >= 1`
- **Purpose**: Detects client-specific STAR API failures (bad payload, URL not found, feature disabled)
- **Threshold**: 1+ company-specific error in 10 minutes per company
- **Severity**: High
- **Escalation**: PagerDuty after 1 hour
- **Scope**: Individual company issues requiring client-specific investigation

### 2. [STAR API] Service-Level Failures

- **Query**: `sum(last_5m):sum:ai_marketing_scheduler.star_api.errors{!http_status:400,!http_status:404,!http_status:405}.as_count() >= 1`
- **Purpose**: Detects service-wide STAR API issues (all non-client-specific errors)
- **Threshold**: 1+ service error in 5 minutes
- **Severity**: Critical
- **Escalation**: Immediate PagerDuty notification
- **Scope**: Service-level failures affecting multiple or all clients

**Error Categories Covered:**
- **HTTP 500**: Server-side infrastructure failures
- **HTTP 401/403**: Authentication/authorization service issues  
- **HTTP 502/503/504**: Gateway/upstream service failures
- **Network Errors**: Connectivity failures between services
- **API Processing Errors**: Response parsing or unexpected behavior
- **Unexpected Errors**: Unhandled exceptions or edge cases

## 🚀 Quick Setup

### 1. Environment Variables

Set these before running the setup:

```bash
# Required Datadog API credentials
export DD_API_KEY="your-datadog-api-key"
export DD_APP_KEY="your-datadog-application-key"

# Enable metrics collection
export DD_METRICS_ENABLED="true"
```

### 2. Deploy Enhanced Logging

Deploy the enhanced StarApiClient logging code to your environment.

### 3. Sync Monitors

```bash
cd packages/ai-marketing-scheduler

# Sync all monitors: create new, update existing, delete removed
npm run sync:datadog-monitors
```

### 4. Test & Monitor

Generate test STAR API errors to verify alerts fire correctly, then monitor for 1-2 weeks to adjust thresholds.

## 🛠️ Automated Monitor Management

The setup script provides automatic lifecycle management:

✅ **Creates** monitors that exist in `star-api-monitors.json` but not in Datadog  
✅ **Updates** existing monitors to match the configuration  
✅ **Handles type changes** by automatically deleting and recreating monitors when type changes (e.g., log alert → metric alert)
✅ **Deletes** monitors that exist in Datadog but are removed from configuration  
✅ **Reports** all operations performed for audit trail
✅ **Template substitution** for notification channels and dashboard links

> **Note**: Datadog doesn't allow changing monitor types via the API. The sync script automatically handles this by deleting the old monitor and creating a new one with the correct type.

## 🎯 Monitor Design Philosophy

### Error Classification

The monitors distinguish between two distinct error categories:

- **Company-Specific Errors** (400, 404, 405): Client configuration or content issues
- **Service-Level Errors** (500): Infrastructure or service-wide failures

### Alert Hierarchy

1. **Company-Specific Failures** (High): Client issues requiring investigation of specific company configuration, content, or feature flags
2. **Service-Level Failures** (Critical): Infrastructure outages affecting all clients requiring immediate platform intervention

### Benefits of Error Classification

- **Targeted Response**: Company errors → client support; Service errors → platform team
- **Appropriate Urgency**: 500 errors get immediate critical alerting (5min window)
- **Reduced Noise**: No false service outage alerts from client-specific issues
- **Clear Ownership**: Different teams handle different error types

## 📝 Code Changes Made

### Metric-Based Monitoring

Monitors use structured metrics from `MetricsClient.ts` instead of log parsing:

```typescript
// Success tracking
this.metricsClient.recordStarApiSuccess(companyId, elementTag, actionId);

// HTTP error tracking
this.metricsClient.recordStarApiError('http_error', companyId, elementTag, actionId, httpStatus);

// Network error tracking  
this.metricsClient.recordStarApiError('network_error', companyId, elementTag, actionId);
```

### Enhanced Logging Context

Added structured logging fields to `StarApiClient.ts` for supplementary debugging:

### New Structured Fields

- `companyId`: For company-specific alerting
- `actionId`: For tracking specific scheduled actions  
- `elementTag`: HTML element being updated (h1, title, meta)
- `elementProperty`: Element property being modified
- `pageUrl`: Specific page URL being updated
- `operation`: Set to 'star-api-update' for filtering

## 🔧 Datadog Configuration

### Custom Metrics Used

All monitors use these custom metrics (already implemented in `MetricsClient.ts`):

```typescript
// Success metric
ai_marketing_scheduler.star_api.success
// Tags: company_id, element_tag, action_id

// Error metric
ai_marketing_scheduler.star_api.errors  
// Tags: company_id, element_tag, action_id, error_type, http_status

// Duration metric
ai_marketing_scheduler.star_api.duration
// Tags: company_id, element_tag, success
```

### Benefits of Metric-Based Monitoring

✅ **Reliable**: Metrics are structurally consistent  
✅ **Fast**: No log parsing overhead  
✅ **Safe**: Code changes won't break monitors  
✅ **Efficient**: Lower Datadog costs than log queries
✅ **Non-redundant**: Simplified 3-monitor architecture eliminates duplicate alerting
✅ **Template-driven**: Centralized notification channels and dashboard links

### Notification Channels

Configure these notification integrations in Datadog:

- `@client-marketing-seo-alerts`
- `@star-api-alerts`
- `@pagerduty-SEO_Squad_All_Services`

### Dashboard Links

Update these dashboard URLs in `star-api-monitors.json`:

- [Client Marketing Dashboard]("https://app.datadoghq.com/dashboard/eui-hck-pnr")
