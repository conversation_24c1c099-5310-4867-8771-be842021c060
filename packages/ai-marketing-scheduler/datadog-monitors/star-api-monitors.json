{"monitors": [{"name": "[STAR API] Company-Specific Failures", "type": "metric alert", "query": "sum(last_10m):sum:ai_marketing_scheduler.star_api.errors{http_status:400,http_status:404,http_status:405} by {company_id}.as_count() >= 1", "message": "{{company_failure_message}}", "tags": ["service:ai-marketing-scheduler", "alert_type:company_failure", "severity:high", "team:engineering"], "priority": 2, "options": {"notify_audit": false, "locked": false, "timeout_h": 0, "include_tags": true, "no_data_timeframe": 20, "require_full_window": true, "new_host_delay": 300, "notify_no_data": false, "renotify_interval": 60, "silenced": {}}}, {"name": "[STAR API] Service-Level Failures", "type": "metric alert", "query": "sum(last_5m):sum:ai_marketing_scheduler.star_api.errors{!http_status:400,!http_status:404,!http_status:405}.as_count() >= 1", "message": "{{service_failure_message}}", "tags": ["service:ai-marketing-scheduler", "alert_type:service_failure", "severity:critical", "team:engineering", "team:platform"], "priority": 1, "options": {"notify_audit": false, "locked": false, "timeout_h": 0, "include_tags": true, "no_data_timeframe": 10, "require_full_window": true, "new_host_delay": 300, "notify_no_data": false, "renotify_interval": 30, "silenced": {}}}], "message_templates": {"company_failure_message": ["🚨 **STAR API Company Element Update Failure - {{company_id.value}}**", "", "**Error Count:** {{value}} company-specific errors in 10 minutes", "", "**Description:** STAR API updates failing for this client due to company-specific element update issues (bad payload, URL not found, or feature disabled).", "", "**Quick Links:**", "• [Admin Account](https://app.luxurypresence.com/admin/accounts/{{company_id.value}})", "• [Company Logs](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40companyId%3A{{company_id.value}})", "• [Element Breakdown](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40companyId%3A{{company_id.value}}%20%40operation%3Astar-api-update)", "• [SEO Dashboard]({{seo_automation_dashboard}})", "", "**Impact:** Website content updates failing for this client. SEO recommendations not being applied.", "", "**Likely Causes:**", "• **400 errors**: Invalid payload format or malformed data", "• **404 errors**: Website URL or component not found (site structure changed)", "• **405 errors**: STAR API feature flag disabled for this client", "", "**Next Steps:**", "1. Check error logs for specific HTTP status codes", "2. For 404s: The website URL or component not found (site structure changed)", "3. For 400s: Validate payload format and content (JOI error)", "4. For 405s: Check FF User Segment for [\"Enable Website Overlay Edits by Company\"](https://app.launchdarkly.com/projects/default/flags/enable-website-overlay-edits-by-company/targeting?env=qa&env=production&selected-env=production)", "5. Review element selectors and content being applied", "", "{{client_marketing_alerts}}"], "service_failure_message": ["🔥 **STAR API Service-Level Failure**", "", "**Error Count:** {{value}} service errors in 5 minutes", "**Severity:** CRITICAL - Service-level failure potentially affecting all clients", "", "**Description:** STAR API is experiencing service-level failures that are not client-specific element update issues.", "", "**Error Categories This Alert Covers:**", "• **HTTP 500**: Server-side infrastructure failures", "• **HTTP 401/403**: Authentication/authorization service issues", "• **HTTP 502/503/504**: Gateway/upstream service failures", "• **Network Errors**: Connectivity failures between services", "• **API Processing Errors**: Response parsing or unexpected API behavior", "• **Unexpected Errors**: Unhandled exceptions or edge cases", "", "**Quick Links:**", "• [All Service Errors](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40operation%3Astar-api-update%20status%3Aerror%20-%40http_status%3A400%20-%40http_status%3A404%20-%40http_status%3A405)", "• [HTTP 500 Errors](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40operation%3Astar-api-update%20%40http_status%3A500)", "• [Network Errors](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40operation%3Astar-api-update%20%40error_type%3Anetwork_error)", "• [API Processing Errors](https://app.datadoghq.com/logs?query=service%3Aai-marketing-scheduler%20%40operation%3Astar-api-update%20%40error_type%3Aapi_error)", "• [SEO Dashboard]({{seo_automation_dashboard}})", "", "**Impact:** Multiple or all client website content updates failing. Potential service-wide SEO automation outage.", "", "{{seo_squad_all_services}}"]}, "notification_channels": {"slack_channels": {"star_api_alerts": "@star-api-alerts", "client_marketing_alerts": "@client-marketing-seo-alerts"}, "pagerduty_channels": {"seo_squad_all_services": "@pagerduty-SEO_Squad_All_Services"}}, "dashboard_links": {"seo_automation_dashboard": "https://app.datadoghq.com/dashboard/eui-hck-pnr"}}