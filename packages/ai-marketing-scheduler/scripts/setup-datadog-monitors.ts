#!/usr/bin/env ts-node

/**
 * <PERSON><PERSON><PERSON> to set up Datadog monitors for STAR API error alerting.
 *
 * This script creates and manages Datadog monitors to replace custom Slack alerts.
 * It validates existing monitors and can create/update them as needed.
 */

import * as fs from 'fs';
import * as path from 'path';

import {
  ContextLogger,
  createContextLogger,
} from '../src/logger/contextLogger';

interface DatadogMonitor {
  id?: number;
  name: string;
  type: string;
  query: string;
  message: string;
  tags?: string[];
  options?: any;
}

interface DatadogApiResponse {
  id: number;
  name: string;
  type: string;
  query: string;
  modified?: string;
}

class DatadogMonitorSetup {
  private logger: ContextLogger;

  constructor() {
    const baseLogger = createContextLogger('datadog-setup');
    this.logger = baseLogger.createComponentLogger({
      serviceName: 'setup-script',
    });
  }

  async run(): Promise<void> {
    this.logger.info('Starting Datadog monitor setup');

    await this.setupMonitors();

    this.logger.info('Monitor setup completed successfully');
  }

  /**
   * Set up Datadog monitors for STAR API alerting
   */
  private async setupMonitors(): Promise<void> {
    this.logger.info('Setting up Datadog monitors');

    // Always sync Datadog monitors
    await this.syncDatadogMonitors();

    this.logger.info('Monitor setup complete', {
      nextSteps: [
        'Datadog monitors are now configured',
        'Update deployment config as needed for alert routing',
        'Test alert notifications through Datadog',
        'Adjust monitor thresholds based on production usage',
      ],
    });
  }

  private loadMonitorConfigurations(): DatadogMonitor[] {
    const configPath = path.join(
      __dirname,
      '..',
      'datadog-monitors',
      'star-api-monitors.json',
    );

    if (!fs.existsSync(configPath)) {
      throw new Error(`Monitor configuration file not found: ${configPath}`);
    }

    const configContent = fs.readFileSync(configPath, 'utf8');
    const configData = JSON.parse(configContent);

    // Handle both array format and object with monitors property
    const monitors = Array.isArray(configData)
      ? configData
      : configData.monitors;

    if (!monitors || !Array.isArray(monitors)) {
      throw new Error(
        'Monitor configuration must contain an array of monitors',
      );
    }

    // Extract and flatten templates for substitution
    const templates: Record<string, string> = {};

    // Recursively flatten all template sections
    this.flattenTemplateSection(configData.message_templates || {}, templates);
    this.flattenTemplateSection(
      configData.notification_channels || {},
      templates,
    );
    this.flattenTemplateSection(configData.dashboard_links || {}, templates);

    // Apply template substitutions
    const processedMonitors = this.applyTemplateSubstitutions(
      monitors,
      templates,
    );

    // Validate that our custom template tokens are resolved, but ignore Datadog runtime tokens
    const flatten = (obj: any) => {
      const out: Record<string, string> = {};
      const walk = (o: any) => {
        if (!o || typeof o !== 'object') return;
        for (const [k, v] of Object.entries(o)) {
          if (typeof v === 'string') out[k] = v;
          else if (v && typeof v === 'object') walk(v);
        }
      };
      walk(obj);
      return out;
    };
    const flatTemplates = flatten(templates);
    const ours = new Set(Object.keys(flatTemplates)); // only enforce our keys
    const unresolved = new Set<string>();
    for (const m of processedMonitors) {
      const tokens = JSON.stringify(m).match(/{{\s*([^}]+?)\s*}}/g) || [];
      for (const t of tokens) {
        const name = t.replace(/[{}]/g, '').trim();
        if (ours.has(name)) unresolved.add(name);
      }
    }
    if (unresolved.size) {
      throw new Error(
        `Unresolved custom template placeholders: ${Array.from(unresolved).join(', ')}`,
      );
    }
    this.logger.info('Loaded monitor configurations with templates', {
      count: processedMonitors.length,
      file: configPath,
      templatesFound: {
        messageTemplates: Object.keys(configData.message_templates || {})
          .length,
        notificationChannels: Object.keys(
          configData.notification_channels || {},
        ).length,
        dashboardLinks: Object.keys(configData.dashboard_links || {}).length,
      },
    });

    return processedMonitors as DatadogMonitor[];
  }

  /**
   * Recursively flattens nested template structures into dot-notation keys.
   * Converts arrays to newline-joined strings and preserves string values.
   */
  private flattenTemplateSection(
    section: Record<string, any>,
    output: Record<string, string>,
    prefix = '',
  ): void {
    for (const [key, value] of Object.entries(section)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;

      if (Array.isArray(value)) {
        // Join arrays with newlines for multi-line templates
        output[fullKey] = value.join('\n');
      } else if (typeof value === 'object' && value !== null) {
        // Recursively flatten nested objects
        this.flattenTemplateSection(value, output, fullKey);
      } else if (typeof value === 'string') {
        // Store string values directly
        output[fullKey] = value;
      }
    }
  }

  private applyTemplateSubstitutions(
    monitors: any[],
    templates: Record<string, string>,
  ): any[] {
    return monitors.map(monitor => {
      // Create a deep copy to avoid modifying the original
      const processedMonitor = JSON.parse(JSON.stringify(monitor));

      // Apply template substitutions recursively
      this.substituteTemplatesInObject(processedMonitor, templates);

      return processedMonitor;
    });
  }

  private substituteTemplatesInObject(
    obj: any,
    templates: Record<string, string>,
  ): void {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        if (typeof obj[key] === 'string') {
          // Replace template placeholders in strings
          obj[key] = this.substituteTemplatesInString(obj[key], templates);
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          // Recursively process nested objects
          this.substituteTemplatesInObject(obj[key], templates);
        }
      }
    }
  }

  private substituteTemplatesInString(
    str: string,
    templates: Record<string, string>,
  ): string {
    let result = str;

    // Replace template variables in the format {{template_name}}
    for (const [templateKey, templateValue] of Object.entries(templates)) {
      const placeholder = `{{${templateKey}}}`;
      result = result.replace(
        new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
        templateValue,
      );
    }

    return result;
  }

  private async getExistingStarApiMonitors(): Promise<DatadogApiResponse[]> {
    const apiKey = process.env.DD_API_KEY;
    const appKey = process.env.DD_APP_KEY;

    if (!apiKey || !appKey) {
      throw new Error(
        'DD_API_KEY and DD_APP_KEY environment variables are required',
      );
    }

    this.logger.info('Fetching existing STAR API monitors from Datadog API');

    try {
      const response = await fetch('https://api.datadoghq.com/api/v1/monitor', {
        method: 'GET',
        headers: {
          'DD-API-KEY': apiKey,
          'DD-APPLICATION-KEY': appKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Datadog API error: ${response.status} ${response.statusText} - ${errorText}`,
        );
      }

      const allMonitors = (await response.json()) as DatadogApiResponse[];

      // Filter for STAR API monitors by name pattern
      const starApiMonitors = allMonitors.filter(monitor =>
        monitor.name.includes('[STAR API]'),
      );

      this.logger.info('Found existing STAR API monitors', {
        totalMonitors: allMonitors.length,
        starApiMonitors: starApiMonitors.length,
        names: starApiMonitors.map(m => m.name),
      });

      return starApiMonitors;
    } catch (error) {
      this.logger.error('Failed to fetch monitors from Datadog API', {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async createSingleMonitor(monitor: DatadogMonitor): Promise<void> {
    const apiKey = process.env.DD_API_KEY;
    const appKey = process.env.DD_APP_KEY;

    if (!apiKey || !appKey) {
      throw new Error(
        'DD_API_KEY and DD_APP_KEY environment variables are required',
      );
    }

    this.logger.info('Creating monitor', { name: monitor.name });

    try {
      const response = await fetch('https://api.datadoghq.com/api/v1/monitor', {
        method: 'POST',
        headers: {
          'DD-API-KEY': apiKey,
          'DD-APPLICATION-KEY': appKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: monitor.name,
          type: monitor.type,
          query: monitor.query,
          message: monitor.message,
          tags: monitor.tags || [],
          options: monitor.options || {},
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to create monitor: ${response.status} ${response.statusText} - ${errorText}`,
        );
      }

      const createdMonitor = (await response.json()) as DatadogApiResponse;

      this.logger.info('Monitor created successfully', {
        name: monitor.name,
        type: monitor.type,
        id: createdMonitor.id,
      });
    } catch (error) {
      this.logger.error('Failed to create monitor', {
        name: monitor.name,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async syncDatadogMonitors(): Promise<void> {
    this.logger.info('Synchronizing Datadog monitors...');

    try {
      const monitorConfigs = this.loadMonitorConfigurations();
      const existingMonitors = await this.getExistingStarApiMonitors();

      // Track operations for summary
      const operations = {
        created: [] as string[],
        updated: [] as string[],
        deleted: [] as string[],
      };

      // Create or update monitors from configuration
      for (const monitor of monitorConfigs) {
        const existing = existingMonitors.find(m => m.name === monitor.name);

        if (existing) {
          // Check if monitor type changed (requires delete + recreate)
          if (existing.type !== monitor.type) {
            this.logger.info('Monitor type changed, recreating', {
              name: monitor.name,
              oldType: existing.type,
              newType: monitor.type,
            });

            // Delete old monitor
            await this.deleteSingleMonitor(existing.id, existing.name);
            operations.deleted.push(`${existing.name} (type change)`);

            // Create new monitor
            await this.createSingleMonitor(monitor);
            operations.created.push(`${monitor.name} (type change)`);
          } else {
            // Update existing monitor
            await this.updateSingleMonitor(existing.id, monitor, existing);
            operations.updated.push(monitor.name);
          }
        } else {
          // Create new monitor
          await this.createSingleMonitor(monitor);
          operations.created.push(monitor.name);
        }
      }

      // Delete monitors that are no longer in configuration
      const configMonitorNames = new Set(monitorConfigs.map(m => m.name));
      const monitorsToDelete = existingMonitors.filter(
        existing => !configMonitorNames.has(existing.name),
      );

      for (const monitor of monitorsToDelete) {
        await this.deleteSingleMonitor(monitor.id, monitor.name);
        operations.deleted.push(monitor.name);
      }

      this.logger.info('Monitor synchronization completed', {
        created: operations.created.length,
        updated: operations.updated.length,
        deleted: operations.deleted.length,
        details: operations,
      });
    } catch (error) {
      this.logger.error('Failed to sync monitors', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  private async deleteSingleMonitor(id: number, name: string): Promise<void> {
    const apiKey = process.env.DD_API_KEY;
    const appKey = process.env.DD_APP_KEY;

    if (!apiKey || !appKey) {
      throw new Error(
        'DD_API_KEY and DD_APP_KEY environment variables are required',
      );
    }

    this.logger.info('Deleting monitor', { id, name });

    try {
      const response = await fetch(
        `https://api.datadoghq.com/api/v1/monitor/${id}`,
        {
          method: 'DELETE',
          headers: {
            'DD-API-KEY': apiKey,
            'DD-APPLICATION-KEY': appKey,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to delete monitor: ${response.status} ${response.statusText} - ${errorText}`,
        );
      }

      this.logger.info('Monitor deleted successfully', {
        id,
        name,
      });
    } catch (error) {
      this.logger.error('Failed to delete monitor', {
        id,
        name,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  private async updateSingleMonitor(
    id: number,
    monitor: DatadogMonitor,
    existing?: DatadogApiResponse,
  ): Promise<void> {
    const apiKey = process.env.DD_API_KEY;
    const appKey = process.env.DD_APP_KEY;

    if (!apiKey || !appKey) {
      throw new Error(
        'DD_API_KEY and DD_APP_KEY environment variables are required',
      );
    }

    this.logger.info('Updating monitor', { id, name: monitor.name });

    try {
      const response = await fetch(
        `https://api.datadoghq.com/api/v1/monitor/${id}`,
        {
          method: 'PUT',
          headers: {
            'DD-API-KEY': apiKey,
            'DD-APPLICATION-KEY': appKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: monitor.name,
            type: monitor.type,
            query: monitor.query,
            message: monitor.message,
            tags: monitor.tags || [],
            options: monitor.options || {},
          }),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `Failed to update monitor: ${response.status} ${response.statusText} - ${errorText}`;

        // Handle specific error cases
        if (
          response.status === 400 &&
          errorText.includes('Monitor type change not allowed')
        ) {
          const oldType = existing?.type || 'unknown';
          errorMessage = `Cannot update monitor type (${oldType} -> ${monitor.type}). Type changes are handled automatically during sync.`;
        }

        throw new Error(errorMessage);
      }

      const updatedMonitor = (await response.json()) as DatadogApiResponse;

      this.logger.info('Monitor updated successfully', {
        id,
        name: monitor.name,
        type: monitor.type,
        lastModified: updatedMonitor.modified,
      });
    } catch (error) {
      this.logger.error('Failed to update monitor', {
        id,
        name: monitor.name,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const setup = new DatadogMonitorSetup();

  try {
    await setup.run();
    console.log(`✅ Datadog monitor setup completed successfully`);
  } catch (error) {
    console.error(`❌ Monitor setup failed:`, error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { DatadogMonitorSetup };
