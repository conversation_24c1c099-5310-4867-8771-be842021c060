# Context Logger Pattern

## Overview

The Context Logger pattern enhances logging by providing architectural component tracking and business context management for better observability in CloudWatch.

## Key Concepts

### 1. Component Logger (Architectural Context)

Use `createComponentLogger()` to identify WHERE in your system architecture the log originated:

- `lambdaName`: Which Lambda function
- `serviceName`: Which service class
- `clientName`: Which client/API wrapper
- `componentName`: Other architectural components
- `layer`: Architectural layer

### 2. Child Logger (Business Context)

Use `createChild()` to add business/operational data:

- Business context (userId, tenantId, companyId)
- Request-specific data (correlationId, requestId)
- Processing context (batchId, itemId, operation)

## Implementation Examples

### Factory Pattern

```typescript
createScheduledActionService(logger: ContextLogger): IScheduledActionService {
  const serviceLogger = logger.createComponentLogger({
    serviceName: 'ScheduledActionService',
  });
  
  // Pass component logger to service
  return new ScheduledActionService(seoApiGatewayClient, serviceLogger);
}
```

### Lambda Handler

```typescript
export const handler = async (event: PublisherEvent, context: Context) => {
  const logger = createContextLogger('publisher', context, {
    lambdaName: 'publisher',
    actionId: event.actionId,
    companyId: event.companyId,
  });
  
  // Services automatically get component loggers via factory
  const scheduledActionService = serviceFactory.createScheduledActionService(logger);
};
```

### Operation-Specific Context

```typescript
async function fetchPendingRecommendations(..., logger: ContextLogger) {
  const operationLogger = logger.createChild({
    operation: 'fetchPendingRecommendations',
    recommendationCount: recommendations.length,
  });
  
  operationLogger.info('Starting fetch operation');
}
```

## Benefits

1. **Better Log Filtering**: Filter logs by architectural component in CloudWatch
2. **Request Tracing**: Track requests through lambda → service → client layers
3. **Debugging**: Quickly identify which component generated specific logs
4. **Performance Analysis**: Analyze performance by component

## CloudWatch Queries

```
# Find all logs from StarApiService
fields @timestamp, @message
| filter serviceName = "StarApiService"

# Track a specific request through all components
fields @timestamp, lambdaName, serviceName, clientName, @message
| filter actionId = "12345"
| sort @timestamp
```
