# Logging Conventions - AI Marketing Scheduler

This document defines the logging conventions and patterns used throughout the ai-marketing-scheduler package to ensure consistent, efficient, and debuggable logging across all components.

## Table of Contents

- [Overview](#overview)
- [Core Principles](#core-principles)
- [Log Levels](#log-levels)
- [Layer-Specific Guidelines](#layer-specific-guidelines)
- [Context Logger Pattern](#context-logger-pattern)
- [Best Practices](#best-practices)
- [Examples](#examples)

## Overview

The ai-marketing-scheduler uses a hierarchical logging approach with the AWS Lambda Powertools Logger wrapped in our ContextLogger class. This provides structured logging with automatic context propagation and seamless integration with Datadog for log aggregation and monitoring.

## Core Principles

1. **Log at the Right Layer**: Avoid duplicate logs between layers
2. **Use Appropriate Log Levels**: Match log level to the type of information
3. **Include Relevant Context**: Always log with business and technical context
4. **Minimize Log Volume**: Reduce log ingestion costs while maintaining debuggability
5. **Structured Logging**: Use objects for context, not string concatenation

## Log Levels

### ERROR

- Use for actual errors that need attention
- Include error messages, stack traces, and context
- Both service and client layers should log errors
- Always include relevant IDs (actionId, companyId, etc.)

### INFO  

- Use for business-level operations and important state changes
- Primary level for service layer logs
- Examples: "Processing scheduled actions", "Creating draft actions"
- Should tell the story of what the system is doing

### DEBUG

- Use for technical implementation details
- Primary level for client layer logs
- Examples: API calls, response details, performance metrics
- Helpful for debugging but not needed for normal operations

### WARN

- Use for recoverable issues or degraded functionality
- Examples: Retries, fallbacks, missing optional data
- Include context about what will happen next

## Layer-Specific Guidelines

### Lambda Layer

Lambdas should create a root logger with architectural context:

```typescript
const logger = createContextLogger('my-lambda', context, {
  lambdaName: 'my-lambda',
  actionId: event.actionId,
  companyId: event.companyId,
});
```

### Service Layer

Services orchestrate business logic and should log at INFO level:

```typescript
// ✅ Good - Business operation
this.logger.info('Creating scheduled actions', { 
  companyId, 
  workflowType,
  count 
});

// ❌ Bad - Technical detail
this.logger.info('Calling GraphQL mutation', { query });
```

### Client Layer  

Clients handle external communications and should log at DEBUG level:

```typescript
// ✅ Good - Technical detail at DEBUG
this.logger.debug('GraphQL query completed', { 
  operation: 'upcomingActions',
  duration: endTime - startTime,
  resultCount: response.length 
});

// ❌ Bad - Duplicate of service log
this.logger.info('Fetching upcoming actions', { companyId });
```

## Context Logger Pattern

### Creating Child Loggers

Use child loggers to add context in loops or nested operations:

```typescript
// In a loop processing items
for (const company of companies) {
  const companyLogger = logger.createChild({
    companyId: company.id,
    companyName: company.name
  });
  
  companyLogger.info('Processing company');
  // All logs within this scope automatically include company context
}
```

### Component Loggers

Use component loggers when calling into different architectural layers:

```typescript
// Service creating a logger for its client
const clientLogger = this.logger.createComponentLogger({
  clientName: 'SeoApiGatewayClient'
});
```

### Bubbling Up Context from Lower Layers

Clients should return a `ClientResponse` wrapper that includes context to propagate:

```typescript
// Client returns response with context (defaults to empty object)
async updateElement(payload: StarApiPayload): Promise<ClientResponse<StarApiResponse>> {
  const response = await this.fetch(/* ... */);
  
  return {
    data: response,
    context: response.requestId ? { starApiRequestId: response.requestId } : {}
  };
}

// Service layer simply spreads the context
const response = await this.starApiClient.updateElement(payload);
this.logger.setContext(response.context);

// All subsequent logs will include any context from the client
this.logger.info('Operation completed successfully');
```

This pattern allows clients to add new context fields without requiring service layer changes.

## Best Practices

### 1. Avoid Duplicate Logs

```typescript
// ❌ Bad - Both layers log the same operation
// Service
this.logger.info('Fetching scheduled actions', { companyId });
// Client  
this.logger.info('Fetching scheduled actions', { companyId });

// ✅ Good - Different concerns at different levels
// Service
this.logger.info('Getting scheduled actions for company', { companyId });
// Client
this.logger.debug('GraphQL query: upcomingActions', { variables });
```

### 2. Include Relevant Context

```typescript
// ❌ Bad - Missing context
this.logger.error('Failed to update action');

// ✅ Good - Rich context for debugging
this.logger.error('Failed to update scheduled action', {
  actionId,
  companyId,
  error: error.message,
  errorType: error.constructor.name,
  updatePayload: delta
});
```

### 3. Use Structured Logging

```typescript
// ❌ Bad - String concatenation
this.logger.info(`Processing ${actions.length} actions for company ${companyId}`);

// ✅ Good - Structured data
this.logger.info('Processing actions', {
  actionCount: actions.length,
  companyId,
  actionIds: actions.map(a => a.id)
});
```

### 4. Log Completion with Metrics

```typescript
// ✅ Good - Start and completion with metrics
this.logger.info('Starting batch processing', { 
  totalItems: items.length 
});

// ... processing ...

this.logger.info('Completed batch processing', {
  totalItems: items.length,
  successful: successCount,
  failed: failureCount,
  duration: Date.now() - startTime
});
```

### 5. Handle Sensitive Data

```typescript
// ❌ Bad - Logging sensitive data
this.logger.info('User login', { 
  email: user.email,
  password: credentials.password 
});

// ✅ Good - Sanitized logging
this.logger.info('User login', { 
  userId: user.id,
  email: user.email,
  // Never log passwords or tokens
});
```

## Examples

### Lambda Handler Example

```typescript
export const handler = async (event: MyEvent, context: Context) => {
  const logger = createContextLogger('my-lambda', context, {
    lambdaName: 'my-lambda',
    requestId: event.requestId,
  });

  logger.info('Starting lambda execution', { event });

  try {
    const service = new MyService(logger);
    const result = await service.process(event);
    
    logger.info('Lambda execution completed', { 
      resultCount: result.items.length 
    });
    
    return result;
  } catch (error) {
    return handleHandlerError(error, logger, 'my-lambda');
  }
};
```

### Service Method Example

```typescript
async processCompanyActions(companyId: string): Promise<void> {
  this.logger.info('Processing company actions', { companyId });
  
  try {
    const actions = await this.client.getActions(companyId);
    
    for (const action of actions) {
      const actionLogger = this.logger.createChild({
        actionId: action.id,
        actionType: action.type
      });
      
      actionLogger.info('Processing action');
      await this.processAction(action, actionLogger);
    }
    
    this.logger.info('Completed processing company actions', {
      companyId,
      processedCount: actions.length
    });
  } catch (error) {
    this.logger.error('Failed to process company actions', {
      companyId,
      error: error.message
    });
    throw error;
  }
}
```

### Client Method Example

```typescript
async fetchData(id: string): Promise<Data> {
  const startTime = Date.now();
  
  try {
    const response = await this.graphqlClient.request(QUERY, { id });
    
    this.logger.debug('GraphQL query completed', {
      operation: 'fetchData',
      id,
      duration: Date.now() - startTime,
      hasResult: !!response.data
    });
    
    return response.data;
  } catch (error) {
    this.logger.error('GraphQL query failed', {
      operation: 'fetchData',
      id,
      error: error.message,
      duration: Date.now() - startTime
    });
    throw error;
  }
}
```

## Monitoring and Debugging

### Datadog Log Queries

Find all errors for a specific lambda:

```
@level:ERROR @lambdaName:"my-lambda"
```

Track processing time for a specific company:

```
@companyId:"company-123" @message:"Completed processing" | stats avg(@duration), sum(@actionCount) by {bin:5m}
```

Find all logs for a specific action across all services:

```
@actionId:"action-123" | sort(@timestamp)
```

Monitor error rate by lambda:

```
@level:ERROR | stats count by @lambdaName | sort(-count)
```

Track slow operations (> 1 second):

```
@duration:>1000 | stats avg(@duration) by @operation
```

Find failed actions with context:

```
@level:ERROR @message:"Failed to process*" | table @timestamp, @actionId, @companyId, @error
```

Dashboard query for action processing metrics:

```
@message:"Completed processing actions" | stats count, avg(@duration), sum(@processedCount) by {bin:15m}
```

## Migration Guide

When updating existing code to follow these conventions:

1. **Identify duplicate logs** between service and client layers
2. **Change client logs** from INFO to DEBUG level
3. **Keep service logs** at INFO for business operations
4. **Add context** where missing (IDs, counts, etc.)
5. **Test log output** to ensure it tells a clear story
6. **Update Datadog monitors** if they depend on specific log patterns
