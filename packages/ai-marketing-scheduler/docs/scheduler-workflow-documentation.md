# AI Marketing Scheduler Workflow Documentation

## Overview

The AI Marketing Scheduler is a serverless application that automates the generation, scheduling, and publishing of SEO content and blog posts. The system follows a multi-stage workflow from entitlement fetching to content publication and ranking.

## High-Level Flow

```
EntitlementFetcherSfn (EntitlementFetcher → DraftActionSchedulerDaemon) 
    ↓
DraftActionConsumerDaemon 
    ↓
SeoKeywordSfn (UrlSelector → UrlScraper → KeywordExtractor → RecommendationGenerator → StoreSEODraft) 
    ↓
SurfacerDaemon 
    ↓
RankQueuer / Rank Webhook 
    ↓
EmailerDaemon
```

## Detailed Workflow Stages

### 1. EntitlementFetcherSfn (Entitlement Processing)

**Schedule**:

- Runs every Monday at 21:30 UTC:
  - 4:30 PM Eastern Standard Time (EST, winter)
  - 5:30 PM Eastern Daylight Time (EDT, summer)

#### Components

- **EntitlementFetcher Lambda**: Fetches company entitlements from tenant service
- **DraftActionSchedulerDaemon Lambda**: Creates scheduled actions based on entitlements

#### Process Flow

1. **EntitlementFetcher**:
   - Fetches entitlements from tenant service
   - Enriches entitlements with website URLs from API Gateway
   - Chunks entitlements into batches (default size: 20)
   - Returns: `{ chunkedEntitlements: Entitlement[][] }`

2. **DraftActionSchedulerDaemon** (Map state - parallel processing):
   - For each entitlement chunk:
     - Determines workflow type (SEO or BLOG)
     - Calculates owed actions: `owed = entitlementUnits - upcomingActions`
     - Creates new scheduled actions if owed > 0

#### Database Operations

- **Read**: Queries existing scheduled actions per company
- **Create**: New `ScheduledAction` records with:
  - `status`: DRAFT_PENDING
  - `workflowType`: SEO or BLOG
  - `generationPayload`: Contains websiteUrl

### 2. DraftActionConsumerDaemon

**Schedule**:

- Runs every Monday at 22:00 UTC:
  - 5:00 PM Eastern Standard Time (EST, winter)
  - 6:00 PM Eastern Daylight Time (EDT, summer)

#### Process Flow

1. Queries all scheduled actions with `status = DRAFT_PENDING`
2. For each action:
   - Starts appropriate Step Function (SEO or BLOG)
   - Updates action with execution details

#### Database Operations

- **Update**: `ScheduledAction` record with:
  - `executionName`: Step Function execution ID
  - `executionArn`: Step Function ARN
  - `generationPayload`: Updated with execution metadata

### 3. SeoKeywordSfn (Content Generation)

#### Components (Sequential)

##### a) UrlSelector Lambda

- Uses Firecrawl to map website URLs 
- Filters URLs by recognized page types (homepage, blog, neighborhood guide, etc.)
- Enriches URLs with Snowflake analytics data (search impressions, rankings)
- Filters out URLs with rank 1 (already well-performing)
- Selects optimal URL based on highest impressions or random for unmeasured URLs
- Excludes URLs that have already been processed by other actions
- Returns: `selectedUrl` string

##### b) UrlScraper Lambda

- Receives `selectedUrl` from previous step
- Scrapes the selected URL content using Firecrawl
- Extracts main heading using JavaScript execution
- Uploads screenshot to S3
- Returns: `ScraperResult` with markdown content and metadata

##### c) KeywordExtractor Lambda

- Uses OpenAI to extract relevant SEO keywords from scraped content
- Analyzes page content for optimal keyword opportunities
- Returns: Comma-separated keyword string

##### d) RecommendationGenerator Lambda

- Generates SEO recommendations using OpenAI
- Creates structured recommendations for:
  - Meta descriptions
  - Page titles
  - Header tags
  - Content improvements
- Returns: `RecommendationsRaw` object

##### e) StoreSEODraft Lambda

- Stores all generated content in database
- Calculates scheduling dates

#### Generation Payload Evolution

The `generation_payload` field in the `ScheduledAction` record gets progressively updated as it flows through the SeoKeywordSfn:

1. **Initial State (from DraftActionSchedulerDaemon)**:

   ```json
   {
     "websiteUrl": "https://example.com"
   }
   ```

2. **After UrlSelector Lambda**:

   ```json
   {
     "websiteUrl": "https://example.com",
     "selectedUrl": "https://example.com/about"
   }
   ```

3. **After UrlScraper Lambda**:

   ```json
   {
     "websiteUrl": "https://example.com",
     "selectedUrl": "https://example.com/about",
     "scraperResult": {
       "url": "https://example.com/about",
       "type": "AGENT_BIO",
       "markdown": "# About Us\n...",
       "metaTitle": "About Our Team",
       "metaDescription": "Learn about our experienced real estate team...",
       "mainHeading": { "content": "About Us", "tag": "h1" },
       "firecrawlId": "uuid-from-firecrawl",
       "mediaId": "screenshot-media-id-from-s3",
       "companyId": "company-uuid"
     }
   }
   ```

4. **After KeywordExtractor Lambda**:

   ```json
   {
     "websiteUrl": "https://example.com",
     "selectedUrl": "https://example.com/about",
     "scraperResult": { /* ... same as above ... */ },
     "keywords": "real estate agent, homes for sale, property listings"
   }
   ```

5. **After RecommendationGenerator Lambda**:

   ```json
   {
     "websiteUrl": "https://example.com",
     "selectedUrl": "https://example.com/about",
     "scraperResult": { /* ... same as above ... */ },
     "keywords": "real estate agent, homes for sale, property listings",
     "recommendations": {
       "metaTitle": "Top Real Estate Agent | Homes for Sale | [Company Name]",
       "metaDescription": "Find your dream home with our expert real estate services...",
       "mainHeading": "Your Trusted Real Estate Partner" // Only for non-homepage
     }
   }
   ```

6. **After StoreSEODraft Lambda**:

   ```json
   {
     "websiteUrl": "https://example.com",
     "selectedUrl": "https://example.com/about",
     "scraperResult": { /* ... same as above ... */ },
     "keywords": "real estate agent, homes for sale, property listings",
     "recommendations": { /* ... same as above ... */ }
   }
   ```

Each lambda checks if its expected result already exists in the `generation_payload` (via the `checkAction` method). If it does, the lambda skips processing and passes the existing data forward, enabling idempotent retries.

#### Database Operations

- **Create**:
  - `ScrapedPage`: URL and company association
  - `Scrape`: Markdown content and media ID
  - `Keyword`: SEO keyword
  - `PageKeyword`: Links page to keyword
  - `Group`: Links company to keyword
  - `Recommendations`: SEO recommendations
- **Update**: `ScheduledAction` with:
  - `status`: SURFACING_PENDING
  - `contentPayload`: Complete draft content (includes all generated data)
  - `scheduledToBeSurfacedAt`: Calculated date
  - `generationPayload`: Final payload with all accumulated data

### 4. SurfacerDaemon

**Schedule**:

- Runs every Tuesday at 17:00 UTC:
  - 12:00 PM Eastern Standard Time (EST, winter)
  - 1:00 PM Eastern Daylight Time (EDT, summer)

#### Process Flow

1. Queries actions with `status = SURFACING_PENDING` and `scheduledToBeSurfacedAt <= now`
2. Updates each action to be surfaced
3. Sets publication date (2 days after surfacing)

#### Database Operations

- **Update**: `ScheduledAction` with:
  - `status`: SURFACED
  - `surfacedAt`: Current timestamp
  - `scheduledToBePublishedAt`: surfacedAt + 2 days

### 5. RankQueuer / Rank Webhook

**RankQueueStateMachine Schedule**:

- Runs every Tuesday at 19:00 UTC:
  - 2:00 PM Eastern Standard Time (EST, winter)
  - 3:00 PM Eastern Daylight Time (EDT, summer)

#### Process Flow

##### RankBatchSplitter

- Queries all surfaced and published groups
- Chunks keywords into batches for processing

##### RankQueuerDaemon

- Submits keywords to DataForSEO for ranking checks
- Configures webhook callback URL

##### RankWebhook (HTTP endpoint)

- Receives ranking results from DataForSEO
- Updates page keyword rankings in database

#### Database Operations

- **Read**: Surfaced and published groups
- **Update**: `PageKeyword` with ranking data

### 6. EmailerDaemon

**Schedule**: Can be triggered manually or via schedule

#### Process Flow

1. Fetches entitlements and filters by company/email if provided
2. Queries homepage feed for each company (last 7 days to next 7 days)
3. Sends weekly digest emails via SendGrid
4. Processes in batches with exponential backoff

#### Database Operations

- **Read**: Homepage feed data for email content

## Scheduled Action Status Flow

```
DRAFT_PENDING 
    ↓ (DraftActionConsumerDaemon starts Step Function)
DRAFT_PENDING (with execution details)
    ↓ (StoreSEODraft completes)
SURFACING_PENDING
    ↓ (SurfacerDaemon processes)
SURFACED
    ↓ (Publisher processes - not shown in main flow)
PUBLISHED
```

## Error Handling

- **FAILED**: Action can transition to this status at any stage if errors occur
- **INVALIDATED**: Used to mark actions that should not be processed
- Each Step Function includes retry logic and catch blocks
- Failed actions are logged with detailed error information in `failureReason`

## Key Configuration

- **Entitlement Processing**: Configurable chunk size and limits
- **Scheduling**: All daemons can be enabled/disabled via environment variables
- **Step Functions**: Separate state machines for different workflows (SEO, BLOG)
- **Concurrency**: Map states use configurable concurrency limits

## Database Schema Relationships

- `Company` → `ScheduledAction` (1:many)
- `ScheduledAction` → `Group` → `Keyword` (content relationships)
- `ScrapedPage` → `PageKeyword` → `Keyword` (SEO relationships)
- `Group` → `Recommendations` (1:many)
