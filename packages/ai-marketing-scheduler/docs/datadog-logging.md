# Datadog Logging Integration

This document describes how the ai-marketing-scheduler package integrates with Datadog for enhanced logging and observability.

## Overview

The ai-marketing-scheduler uses **ContextLogger** as the primary logging mechanism with automatic Datadog integration via:

- Automatic service identification and versioning
- Custom business metrics tracking via `dd.custom.*` attributes
- Seamless integration with Datadog's APM and logging features via the Datadog Lambda Layer
- Optional native metrics support (currently available but not actively used)

## Current Implementation

All Lambda functions use the `createContextLogger()` pattern which automatically includes Datadog attributes:

```typescript
export const handler = async (event: any, context: Context) => {
  const logger = createContextLogger('my-lambda', context, {
    lambdaName: 'my-lambda',
    // Add any initial context
  });
  
  logger.info('Processing started'); // Automatically includes Datadog attributes
};
```

## Automatic Datadog Attributes

All logs automatically include the following Datadog reserved attributes:

### Service Identification

- `dd.env`: Environment (production, staging, dev) - from config
- `dd.service`: Always set to "ai-marketing-scheduler" (consistent across all Lambda functions)
- `dd.version`: Package version from package.json
- `dd.custom.function_name`: Individual Lambda function name (e.g., "entitlement-fetcher", "publisher")
- `environment`: Current environment
- `awsRequestId`: AWS Lambda request ID for correlation

### Trace Correlation

- Automatic trace correlation is handled by the Datadog Lambda Layer
- X-Ray integration is enabled via serverless-plugin-datadog configuration
- No manual trace ID extraction required

## Primary Logging Pattern: Custom Attributes

The main approach for adding business context and debugging information:

### Single Custom Attribute

```typescript
logger.addCustomMetric('processingTime', 145);
logger.addCustomMetric('itemCount', 10);
```

### Multiple Custom Attributes

```typescript
logger.addCustomMetrics({
  successCount: 8,
  failureCount: 2,
  totalDuration: 1250,
  companyId: 'company-123',
  workflowType: 'social_posts'
});
```

These appear in logs as:

- `dd.custom.processingTime`: 145
- `dd.custom.itemCount`: 10
- `dd.custom.successCount`: 8
- `dd.custom.companyId`: "company-123"
- etc.

**Key Benefits:**

- Retained for 15 days in Datadog
- Fully indexed and searchable
- No cardinality concerns for debugging data
- Perfect for company-specific debugging and performance analysis

## Filtering Logs by Function

Since all Lambda functions use the same service name ("ai-marketing-scheduler"), you can filter logs by individual functions using:

### In Datadog Log Explorer:

```
@dd.custom.function_name:entitlement-fetcher
```

### Common filter patterns:
- `@dd.custom.function_name:publisher` - Show only Publisher lambda logs
- `@dd.custom.function_name:(entitlement-fetcher OR publisher)` - Show logs from multiple functions
- `service:ai-marketing-scheduler @dd.custom.function_name:*` - All functions in the service

## Optional: Native Datadog Metrics

The `MetricsClient` class is available but **not currently used** in Lambda implementations. It can be enabled for production monitoring:

```typescript
import { getMetricsClient } from '../metrics/MetricsClient';

const metrics = getMetricsClient();
metrics.recordProcessingDuration(1500, 'scheduler_daemon');
metrics.recordError('timeout', 'social_posts', 'api_call');
await metrics.close();
```

**Configuration Required:**

```yaml
environment:
  DD_METRICS_ENABLED: 'true'  # Must be set to enable native metrics
```

**Available Metrics** (when enabled):

- `scheduler.processing.duration` - Processing times by operation
- `scheduler.errors` - Error counts by type
- `scheduler.entitlements.fetched` - Entitlement counts
- `scheduler.actions.created` - Action creation counts

**Note**: Native metrics avoid high cardinality by not including `companyId` tags.

## Usage Examples

### Current Lambda Pattern (EntitlementFetcher)

```typescript
export const handler = async (
  event: Record<string, unknown> = {},
  context: Context,
): Promise<{ chunkedEntitlements: Entitlement[][] }> => {
  const logger = createContextLogger('entitlement-fetcher', context, {
    lambdaName: 'entitlement-fetcher',
  });
  logger.info('Starting entitlement fetcher', { event });

  try {
    const tenantService = serviceFactory.createTenantService(logger);
    const entitlements = await tenantService.getEntitlements();

    // Add business metrics for debugging
    logger.addCustomMetrics({
      entitlementsCount: entitlements.length,
      companyIds: entitlements.map(e => e.companyId),
    });

    logger.info('Fetched entitlements', {
      count: entitlements.length,
    });

    return { chunkedEntitlements: chunkEntitlements(entitlements) };
  } catch (error: unknown) {
    return handleHandlerError(error, logger, 'entitlement-fetcher');
  }
};
```

### Service with Component Logging

```typescript
class TenantService {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    this.logger = parentLogger.createComponentLogger({
      serviceName: 'TenantService',
    });
  }

  async getEntitlements(): Promise<Entitlement[]> {
    const startTime = Date.now();
    
    this.logger.info('Fetching entitlements from tenant service');
    
    try {
      const entitlements = await this.fetchFromAPI();
      
      // Track performance and results
      this.logger.addCustomMetrics({
        fetchDuration: Date.now() - startTime,
        entitlementsReturned: entitlements.length,
        apiResponseTime: Date.now() - startTime,
      });
      
      this.logger.info('Successfully fetched entitlements');
      return entitlements;
    } catch (error) {
      this.logger.addCustomMetric('fetchError', error.message);
      throw error;
    }
  }
}
```

### Error Handling Pattern

```typescript
export const handler = async (event: any, context: Context) => {
  const logger = createContextLogger('my-lambda', context, {
    lambdaName: 'my-lambda',
    actionId: event.actionId,
    companyId: event.companyId,
  });

  try {
    // Business logic
    const result = await processAction(event);
    
    logger.addCustomMetrics({
      processingSuccess: true,
      processingDuration: result.duration,
      itemsProcessed: result.count,
    });
    
    return result;
  } catch (error: unknown) {
    // Use factory error handler for consistent error processing
    return handleHandlerError(error, logger, 'my-lambda');
  }
};
```

## Datadog Query Examples

### Log Queries (Primary Approach)

#### Find logs by service and environment

```
service:ai-marketing-scheduler env:production
```

#### Performance analysis by Lambda

```
service:ai-marketing-scheduler @dd.custom.processingDuration:>1000
```

#### Find logs for specific company (debugging)

```
service:ai-marketing-scheduler @dd.custom.companyId:"company-123"
```

#### Trace errors by Lambda function

```
service:ai-marketing-scheduler @level:ERROR @lambdaName:"entitlement-fetcher"
```

#### Custom attribute aggregation

```
avg:@dd.custom.processingDuration by @lambdaName
```

#### Find processing issues by workflow

```
service:ai-marketing-scheduler @dd.custom.workflowType:"social_posts" @dd.custom.processingDuration:>5000
```

#### Monitor entitlement fetching

```
service:ai-marketing-scheduler @lambdaName:"entitlement-fetcher" @dd.custom.entitlementsCount:*
```

#### Company-specific performance tracking

```
service:ai-marketing-scheduler @dd.custom.companyId:"company-123" @dd.custom.actionsCreated:>0
```

### Native Metric Queries (If Enabled)

**Note**: These require `DD_METRICS_ENABLED: 'true'` and are not currently in use.

#### Processing duration monitoring

```
avg:scheduler.processing.duration{env:production} by {operation}
```

#### Error rate tracking

```
sum:scheduler.errors{env:production}.as_rate() by {error_type}
```

## Benefits

1. **Automatic Integration**: Datadog Lambda Layer handles trace correlation automatically
2. **Service Map Integration**: Logs appear correctly in Datadog's Service Map via service identification
3. **Simple Implementation**: Single `createContextLogger()` call provides full Datadog integration
4. **Flexible Debugging**: Custom attributes support company-specific debugging without cardinality concerns
5. **Performance Tracking**: Easy to track processing times, counts, and business metrics
6. **Component Tracing**: `createComponentLogger()` enables architectural component tracking
7. **Error Handling**: Consistent error handling via `handleHandlerError()` factory method
8. **Lambda-Optimized**: Uses AWS Lambda Powertools Logger optimized for serverless

## Configuration

### Current Setup (Automatic)

The current implementation requires no additional configuration - Datadog integration is automatic via:

- `serverless-plugin-datadog` in serverless.yml
- Datadog Lambda Layer attachment
- X-Ray tracing enablement

### Environment Variables (Auto-configured)

```yaml
# Automatically set by serverless-plugin-datadog
dd_trace_enabled: true
dd_service: ai-marketing-scheduler
dd_env: ${env:ENVIRONMENT, 'development'}
```

### Optional: Native Metrics (Not Currently Used)

To enable native metrics (currently available but unused):

```yaml
environment:
  DD_METRICS_ENABLED: 'true'
```

## Implementation Notes

1. **Primary Pattern**: ContextLogger with `dd.custom.*` attributes for all observability needs
2. **Automatic Traces**: Datadog Lambda Layer handles trace correlation without manual intervention
3. **Service Factory**: All services use `serviceFactory` pattern with logger injection
4. **Error Handling**: Consistent error processing via `handleHandlerError()` from LoggerFactory
5. **Component Logging**: Services use `createComponentLogger()` for architectural tracing
6. **Custom Attributes**: Company and business data in `dd.custom.*` attributes (no cardinality limits)
7. **No Manual Wrapping**: Lambdas are not manually wrapped - Datadog layer provides automatic instrumentation
8. **X-Ray Integration**: Handled automatically via serverless plugin configuration
