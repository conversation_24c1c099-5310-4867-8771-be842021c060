# Comprehensive examples showing the difference between createChild and createComponentLogger

TLDR:

* createComponentLogger: For ARCHITECTURAL components (where is this code?)
* createChild: For BUSINESS context (what is this code doing?)

```typescript
import { Context } from 'aws-lambda';

import {
  createContextLogger,
  ContextLogger,
} from '../src/logger/contextLogger';

// ============================================================================
// PART 1: ARCHITECTURAL COMPONENT LOGGING (createComponentLogger)
// ============================================================================

/**

* Example 1: Lambda → Service → Client architecture
* Shows how to track a request through architectural layers
 */
export const publisherLambdaHandler = (event: any, context: Context) => {
  // Start with Lambda identification
  const logger = createContextLogger('publisher', context, {
    lambdaName: 'publisher', // ARCHITECTURAL: Which Lambda
    actionId: event.actionId, // BUSINESS: What action
    companyId: event.companyId, // BUSINESS: For which company
  });

  logger.info('Lambda execution started');
  /* LOG OUTPUT:
  {
    "level": "INFO",
    "message": "Lambda execution started",
    "service": "publisher",
    "lambdaName": "publisher",
    "actionId": "action-123",
    "companyId": "company-456",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "awsRequestId": "abc-123-def"
  }
  */

  try {
    // Create service with architectural identification
    const scheduledActionService = new ScheduledActionService(logger);
    const result = scheduledActionService.publishAction(event.actionId);

    logger.info('Lambda execution completed', { result });
    return result;
  } catch (error) {
    logger.error('Lambda execution failed', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
};

class ScheduledActionService {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    // Add service layer identification
    this.logger = parentLogger.createComponentLogger({
      serviceName: 'ScheduledActionService', // ARCHITECTURAL: Which service
    });
  }

  publishAction(actionId: string) {
    this.logger.info('Publishing scheduled action');
    /* LOG OUTPUT:
    {
      "level": "INFO",
      "message": "Publishing scheduled action",
      "service": "publisher",
      "lambdaName": "publisher",
      "serviceName": "ScheduledActionService",
      "actionId": "action-123",
      "companyId": "company-456",
      "timestamp": "2024-01-15T10:30:01.000Z",
      "awsRequestId": "abc-123-def"
    }
    */

    // Create clients with their own identification
    const seoClient = new SeoApiGatewayClient(this.logger);
    const tenantClient = new TenantClient(this.logger);

    const tenantInfo = tenantClient.getTenantInfo();
    const result = seoClient.publishContent(actionId, tenantInfo);

    this.logger.info('Action published successfully');
    return result;
  }
}

class SeoApiGatewayClient {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    // Add client layer identification
    this.logger = parentLogger.createComponentLogger({
      clientName: 'SeoApiGatewayClient', // ARCHITECTURAL: Which client
    });
  }

  publishContent(actionId: string, tenantInfo: any) {
    this.logger.info('Publishing content to SEO API', { actionId, tenantInfo });
    /* LOG OUTPUT:
    {
      "level": "INFO",
      "message": "Publishing content to SEO API",
      "service": "publisher",
      "lambdaName": "publisher",
      "serviceName": "ScheduledActionService",
      "clientName": "SeoApiGatewayClient",
      "actionId": "action-123",
      "companyId": "company-456",
      "tenantInfo": { "tier": "premium" },
      "timestamp": "2024-01-15T10:30:02.000Z",
      "awsRequestId": "abc-123-def"
    }
    */
    // API call logic here
    return { success: true };
  }
}

class TenantClient {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    this.logger = parentLogger.createComponentLogger({
      clientName: 'TenantClient',
    });
  }

  getTenantInfo() {
    this.logger.info('Fetching tenant information');
    /* LOG OUTPUT:
    {
      "level": "INFO",
      "message": "Fetching tenant information",
      "service": "publisher",
      "lambdaName": "publisher",
      "serviceName": "ScheduledActionService",
      "clientName": "TenantClient",
      "actionId": "action-123",
      "companyId": "company-456",
      "timestamp": "2024-01-15T10:30:01.500Z",
      "awsRequestId": "abc-123-def"
    }
    */
    // API call logic here
    return { tier: 'premium' };
  }
}

// ============================================================================
// PART 2: BUSINESS CONTEXT LOGGING (createChild)
// ============================================================================

/**

* Example 2: Batch processing with business context
* Shows how to add operational context for each item being processed
 */
export const batchProcessorHandler = async (event: any, context: Context) => {
  const logger = createContextLogger('batch-processor', context, {
    lambdaName: 'batch-processor',
    batchId: event.batchId,
    totalItems: event.items.length,
  });

  logger.info('Starting batch processing');
  /* LOG OUTPUT:
  {
    "level": "INFO",
    "message": "Starting batch processing",
    "service": "batch-processor",
    "lambdaName": "batch-processor",
    "batchId": "batch-789",
    "totalItems": 3,
    "timestamp": "2024-01-15T10:35:00.000Z",
    "awsRequestId": "xyz-456-abc"
  }
  */

  const results = [];
  for (const [index, item] of event.items.entries()) {
    // Create child logger with business context for THIS specific item
    const itemLogger = logger.createChild({
      itemId: item.id,
      itemType: item.type,
      itemIndex: index,
      customerId: item.customerId, // Business data
      region: item.region, // Business data
      tier: item.tier, // Business data
    });

    itemLogger.info('Processing item');
    /* LOG OUTPUT for first item:
    {
      "level": "INFO",
      "message": "Processing item",
      "service": "batch-processor",
      "lambdaName": "batch-processor",
      "batchId": "batch-789",
      "totalItems": 3,
      "itemId": "item-001",
      "itemType": "recommendation",
      "itemIndex": 0,
      "customerId": "cust-111",
      "region": "us-east-1",
      "tier": "gold",
      "timestamp": "2024-01-15T10:35:01.000Z",
      "awsRequestId": "xyz-456-abc"
    }
    */

    try {
      const result = await processItem(item, itemLogger);
      results.push(result);

      itemLogger.info('Item processed successfully', {
        processingTime: result.duration,
      });
      /* LOG OUTPUT:
      {
        "level": "INFO",
        "message": "Item processed successfully",
        "service": "batch-processor",
        "lambdaName": "batch-processor",
        "batchId": "batch-789",
        "totalItems": 3,
        "itemId": "item-001",
        "itemType": "recommendation",
        "itemIndex": 0,
        "customerId": "cust-111",
        "region": "us-east-1",
        "tier": "gold",
        "processingTime": 145,
        "timestamp": "2024-01-15T10:35:01.145Z",
        "awsRequestId": "xyz-456-abc"
      }
      */
    } catch (error) {
      itemLogger.error('Failed to process item', {
        error: error instanceof Error ? error.message : String(error),
        retryCount: item.retryCount || 0,
      });
      /* LOG OUTPUT for error:
      {
        "level": "ERROR",
        "message": "Failed to process item",
        "service": "batch-processor",
        "lambdaName": "batch-processor",
        "batchId": "batch-789",
        "totalItems": 3,
        "itemId": "item-002",
        "itemType": "recommendation",
        "itemIndex": 1,
        "customerId": "cust-222",
        "region": "eu-west-1",
        "tier": "silver",
        "error": "Connection timeout",
        "retryCount": 2,
        "timestamp": "2024-01-15T10:35:02.500Z",
        "awsRequestId": "xyz-456-abc"
      }
      */
    }
  }

  logger.info('Batch processing completed', {
    successCount: results.filter(r => r.success).length,
    failureCount: results.filter(r => !r.success).length,
  });
  /* LOG OUTPUT:
 {
    "level": "INFO",
    "message": "Batch processing completed",
    "service": "batch-processor",
    "lambdaName": "batch-processor",
    "batchId": "batch-789",
    "totalItems": 3,
    "successCount": 2,
    "failureCount": 1,
    "timestamp": "2024-01-15T10:35:03.000Z",
    "awsRequestId": "xyz-456-abc",
    "environment": "production"
  }
    */
};

async function processItem(_item: any, logger: ContextLogger) {
  const startTime = Date.now();

  // Add processing step context
  const validationLogger = logger.createChild({
    processingStep: 'validation',
    validationType: 'schema',
  });

  validationLogger.info('Validating item');
  /* LOG OUTPUT:
  {
    "level": "INFO",
    "message": "Validating item",
    "service": "batch-processor",
    "lambdaName": "batch-processor",
    "batchId": "batch-789",
    "totalItems": 3,
    "itemId": "item-001",
    "itemType": "recommendation",
    "itemIndex": 0,
    "customerId": "cust-111",
    "region": "us-east-1",
    "tier": "gold",
    "processingStep": "validation",
    "validationType": "schema",
    "timestamp": "2024-01-15T10:35:01.050Z",
    "awsRequestId": "xyz-456-abc"
  }
  */

  // Simulate processing
  await new Promise(resolve => setTimeout(resolve, 100));

  return {
    success: true,
    duration: Date.now() - startTime,
  };
}

// ============================================================================
// PART 3: COMBINING BOTH APPROACHES
// ============================================================================

/**

* Example 3: Real-world scenario combining architectural and business logging
* Processing a recommendation workflow with full context
 */
export const recommendationProcessor = (event: any, context: Context) => {
  // Initial context with both architectural and business data
  const logger = createContextLogger('recommendation-processor', context, {
    lambdaName: 'recommendation-processor', // ARCHITECTURAL
    workflowId: event.workflowId, // BUSINESS
    companyId: event.companyId, // BUSINESS
  });

  logger.info('Starting recommendation workflow');

  // Create service with architectural identification
  const service = new RecommendationService(logger);

  // Process with business context
  const result = service.processRecommendations(
    event.recommendations,
    event.configuration,
  );

  logger.info('Workflow completed', {
    processedCount: result.processed,
    skippedCount: result.skipped,
  });
  /* LOG OUTPUT:
  {
    "level": "INFO",
    "message": "Workflow completed",
    "service": "recommendation-processor",
    "lambdaName": "recommendation-processor",
    "workflowId": "wf-123",
    "companyId": "company-456",
    "processedCount": 8,
    "skippedCount": 2,
    "timestamp": "2024-01-15T10:40:10.000Z",
    "awsRequestId": "qrs-789-tuv"
  }
  */
};

class RecommendationService {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    // Architectural identification
    this.logger = parentLogger.createComponentLogger({
      serviceName: 'RecommendationService',
    });
  }

  processRecommendations(recommendations: any[], config: any) {
    this.logger.info('Processing recommendations', {
      totalCount: recommendations.length,
      config: config.mode || 'default',
    });

    const results = { processed: 0, skipped: 0 };

    for (const recommendation of recommendations) {
      // Business context for each recommendation
      const recLogger = this.logger.createChild({
        recommendationId: recommendation.id,
        recommendationType: recommendation.type,
        priority: recommendation.priority,
        targetUrl: recommendation.url,
      });

      recLogger.info('Processing recommendation');
      /* LOG OUTPUT:
      {
        "level": "INFO",
        "message": "Processing recommendation",
        "service": "recommendation-processor",
        "lambdaName": "recommendation-processor",
        "serviceName": "RecommendationService",
        "workflowId": "wf-123",
        "companyId": "company-456",
        "recommendationId": "rec-456",
        "recommendationType": "meta-description",
        "priority": "high",
        "targetUrl": "https://example.com/page1",
        "timestamp": "2024-01-15T10:40:01.000Z",
        "awsRequestId": "qrs-789-tuv"
      }
      */

      try {
        // Create analyzer with architectural identification
        const analyzer = new RecommendationAnalyzer(recLogger);
        const analysis = analyzer.analyze(recommendation);

        if (analysis.shouldProcess) {
          // Add analysis results as business context
          const processingLogger = recLogger.createChild({
            analysisScore: analysis.score,
            analysisReasons: analysis.reasons,
            estimatedImpact: analysis.impact,
          });

          this.applyRecommendation(recommendation, processingLogger);
          /* LOG OUTPUT when applying:
          {
            "level": "INFO",
            "message": "Applying recommendation",
            "service": "recommendation-processor",
            "lambdaName": "recommendation-processor",
            "serviceName": "RecommendationService",
            "workflowId": "wf-123",
            "companyId": "company-456",
            "recommendationId": "rec-456",
            "recommendationType": "meta-description",
            "priority": "high",
            "targetUrl": "https://example.com/page1",
            "analysisScore": 0.85,
            "analysisReasons": ["high_impact", "low_effort"],
            "estimatedImpact": "high",
            "timestamp": "2024-01-15T10:40:02.000Z",
            "awsRequestId": "qrs-789-tuv"
          }
          */
          results.processed++;
        } else {
          recLogger.info('Skipping recommendation', {
            skipReason: analysis.skipReason,
          });
          results.skipped++;
        }
      } catch (error) {
        recLogger.error('Failed to process recommendation', {
          error: error instanceof Error ? error.message : String(error),
        });
        results.skipped++;
      }
    }

    return results;
  }

  private applyRecommendation(_rec: any, logger: ContextLogger) {
    logger.info('Applying recommendation');
    // Implementation logic
  }
}

class RecommendationAnalyzer {
  private logger: ContextLogger;

  constructor(parentLogger: ContextLogger) {
    // Another architectural component
    this.logger = parentLogger.createComponentLogger({
      componentName: 'RecommendationAnalyzer',
    });
  }

  analyze(recommendation: any) {
    this.logger.info('Analyzing recommendation', { type: recommendation.type });
    /* LOG OUTPUT:
    {
      "level": "INFO",
      "message": "Analyzing recommendation",
      "service": "recommendation-processor",
      "lambdaName": "recommendation-processor",
      "serviceName": "RecommendationService",
      "componentName": "RecommendationAnalyzer",
      "workflowId": "wf-123",
      "companyId": "company-456",
      "recommendationId": "rec-456",
      "recommendationType": "meta-description",
      "priority": "high",
      "targetUrl": "<https://example.com/page1>",
      "type": "meta-description",
      "timestamp": "2024-01-15T10:40:01.500Z",
      "awsRequestId": "qrs-789-tuv"
    }
    */

    // Add analysis context
    const analysisLogger = this.logger.createChild({
      analysisVersion: '2.0',
      analysisMode: 'full',
    });

    analysisLogger.info('Running analysis algorithms');

    return {
      shouldProcess: true,
      score: 0.85,
      reasons: ['high_impact', 'low_effort'],
      impact: 'high',
      analysisVersion: '2.0',
      skipReason: 'low_impact',
    };
  }
}
```

## BEST PRACTICES SUMMARY

* Use createComponentLogger when:
* * Identifying architectural components (Lambda, Service, Client, Middleware)
* * You want to filter logs by "where" they came from
* * Setting up logging in constructors or initialization
*
* Use createChild when:
* * Adding business/operational context (userId, itemId, batchId)
* * Processing items in loops where each item needs unique context
* * Adding request-specific information (correlationId, headers)
* * Temporarily adding context for a specific operation
*
* Often you'll use BOTH:

1. createComponentLogger in your service/client constructors
2. createChild when processing specific business operations

## Datadog Log Query Examples

### ARCHITECTURAL QUERIES (using createComponentLogger fields)

1. Find all logs from a specific Lambda:
   * @lambdaName:publisher

2. Find all logs from a specific service:
   * @serviceName:RecommendationService

3. Track architectural flow for a specific action:
   * @actionId:action-123

### BUSINESS CONTEXT QUERIES (using createChild fields)

1. Find all logs for a specific company:
   * @companyId:company-456

2. Track a specific recommendation:
   * @recommendationId:rec-456

3. Find all failed items in a batch:
   * @batchId:batch-789 @error:*

### COMBINED QUERIES

1. Find all errors in RecommendationService for a specific company:
   * @serviceName:RecommendationService @companyId:company-456 @error:*

2. Performance analysis by component (use Analytics view):
   * avg:@processingTime by @serviceName

3. Track full request flow for a specific action:
   * @actionId:action-123

4. Find logs from publisher Lambda with errors:
   * @lambdaName:publisher @level:ERROR

5. Find all logs from multiple services:
   * @serviceName:(TenantService OR ScheduledActionService)

6. Exclude debug logs from a specific client:
   * @clientName:SeoApiGatewayClient -@level:DEBUG

7. Find logs with high processing time:
   * @processingTime:>1000

8. Search for logs with wildcards:
   * @serviceName:Recommendation* @companyId:company-456

9. Complex query with multiple conditions:
   * @lambdaName:publisher AND (@serviceName:TenantService OR @serviceName:ScheduledActionService) AND -@level:DEBUG

### DATADOG QUERY SYNTAX NOTES

* Use @ prefix for all custom attributes
* AND is implicit between terms (space-separated)
* Use OR for alternatives within parentheses
* Use - for exclusion (NOT)
* Use : for exact match, * for wildcards
* Use > < >= <= for numerical comparisons
* Use [X TO Y] for ranges

## CUSTOM ATTRIBUTES VS METRICS

### Custom Attributes (for debugging)

The ContextLogger supports custom attributes that appear with the `dd.custom.*` prefix in Datadog logs:

### Adding Custom Attributes

```typescript
// Single attribute
logger.addCustomMetric('entitlementsFetched', 42);
// Results in log attribute: dd.custom.entitlementsFetched: 42

// Multiple attributes
logger.addCustomMetrics({
  entitlementsFetched: 100,
  entitlementsWithWebsites: 75,
  processingTimeMs: 1250
});
// Results in log attributes:
// dd.custom.entitlementsFetched: 100
// dd.custom.entitlementsWithWebsites: 75
// dd.custom.processingTimeMs: 1250
```

**Note**: These are log attributes, not custom metrics. They're retained for 15 days and useful for debugging. For dashboards and alerts, use the MetricsClient instead.

### Real-World Custom Attribute Examples

#### EntitlementFetcher Lambda

```typescript
const logger = createContextLogger('entitlement-fetcher', context, {
  lambdaName: 'entitlement-fetcher',
});

// After fetching entitlements
logger.addCustomMetrics({
  entitlementsFetched: entitlements.length,  // Total fetched from tenant service
  validEntitlements: entitlementsWithWebsites.length,  // Only those with websites
});
```

#### DraftActionSchedulerDaemon Lambda

```typescript
// Per-company metrics
const companyLogger = logger.createChild({
  companyId,
  workflowType,
});

companyLogger.addCustomMetrics({
  scheduledActionsCreated: actionsCreated,
  upcomingActions: upcomingActions.length,
  entitlementUnits,
});

// Overall metrics at the end
logger.addCustomMetrics({
  scheduledActionsCreatedTotal: totalActionsCreated,
  totalOwedActions,
  totalUpcomingActions,
  processedEntitlements: event.entitlements.length,
});

// Per-company summary metrics
const perCompanyMetrics = details.reduce(
  (acc, detail) => {
    if (detail.status === 'processed' && detail.actionsCreated > 0) {
      acc[`scheduledActionsCreated_${detail.companyId}`] = detail.actionsCreated;
    }
    return acc;
  },
  {} as Record<string, number>,
);

if (Object.keys(perCompanyMetrics).length > 0) {
  logger.addCustomMetrics(perCompanyMetrics);
}
```

### Querying Custom Attributes in Datadog Logs

1. Search for specific attributes: `@dd.custom.entitlementsFetched:>100`
2. Filter logs based on attribute values
3. Analyze patterns in log data
4. Debug issues using business context

Example queries:

```
# Find logs with high entitlement counts
service:ai-marketing-scheduler @dd.custom.entitlementsFetched:>1000

# Monitor valid entitlements (those with websites)
service:ai-marketing-scheduler @dd.custom.validEntitlements:>0

# Calculate entitlement validation rate
service:ai-marketing-scheduler @dd.custom.validEntitlements:* @dd.custom.entitlementsFetched:*

# Monitor scheduled action creation
service:ai-marketing-scheduler @dd.custom.scheduledActionsCreatedTotal:>0

# Per-company metrics
service:ai-marketing-scheduler @dd.custom.scheduledActionsCreated_company-123:*
```

### Best Practices for Custom Attributes

1. **Use for Business KPIs**: Track measurable values that help monitor business performance

   ```typescript
   logger.addCustomMetric('conversionRate', 0.125);
   logger.addCustomMetric('revenueGenerated', 15000);
   ```

2. **Keep Context for Identifiers**: Use regular context for IDs and non-metric data

   ```typescript
   const logger = createContextLogger('service', context, {
     companyId: 'company-123',  // Regular context, not a metric
     actionId: 'action-456'      // Regular context, not a metric
   });
   ```

3. **Aggregate at Appropriate Levels**: Add summary attributes at the end of batch operations

   ```typescript
   // Process items...
   
   // Then add summary attributes
   logger.addCustomMetrics({
     totalProcessed: processedCount,
     totalFailed: failedCount,
     processingDurationMs: Date.now() - startTime
   });
   ```

## REAL CUSTOM METRICS (For Dashboards & Alerts)

For actual metrics that can be used in dashboards, alerts, and SLOs, use the MetricsClient:

```typescript
import { getMetricsClient } from '../metrics/MetricsClient';

const metrics = getMetricsClient();

// Record various metrics
metrics.recordEntitlementsFetched(100, 'api');
metrics.recordValidEntitlements(85);
metrics.recordOwedActions(10, 5, 'social_posts');
metrics.recordActionsCreated(10, 5, 'social_posts');
metrics.recordProcessingDuration(1500, 'scheduler_daemon');
metrics.recordError('timeout', 'social_posts', 'api_call');

// Metrics are sent immediately with datadog-lambda-js
await metrics.close(); // No-op but kept for compatibility
```

These metrics:

* Are sent as distribution metrics using native Datadog library
* Have 15-month retention
* Can be used for dashboards, alerts, and SLOs
* Don't include company-specific tags to control cardinality
* Must be enabled with `DD_METRICS_ENABLED=true`

See [datadog-logging.md](./datadog-logging.md) for complete metrics documentation.
