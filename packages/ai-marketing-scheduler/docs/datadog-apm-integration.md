# Datadog APM Integration for AI Marketing Scheduler

## Overview

This document describes the Datadog APM (Application Performance Monitoring) integration for the AI Marketing Scheduler service, including Lambda functions and Step Functions.

**Current Implementation**: The service uses the **Datadog Lambda Layer** for automatic APM instrumentation without manual code wrapping.

## Configuration

### Dependencies

The following packages are installed for Datadog integration:

- `dd-trace`: Core tracing library (used by Datadog Layer)
- `datadog-lambda-js`: Lambda-specific Datadog library (for optional native metrics)
- `serverless-plugin-datadog`: Serverless Framework plugin for automatic configuration

### Environment Variables

The following are automatically configured by the serverless plugin:

- `DD_API_KEY`: Datadog API key (from vault)
- `dd_trace_enabled`: Automatically set to `true`
- `dd_service`: Automatically set to `ai-marketing-scheduler`
- `dd_env`: Automatically set to environment (staging/production)
- X-Ray tracing variables (automatically configured)

### Serverless Configuration

The serverless.yml file includes:

```yaml
plugins:
  - serverless-plugin-datadog

custom:
  datadog:
    site: datadoghq.com
    apiKey: ${env:DD_API_KEY}
    enableDDTracing: true          # Enables automatic APM tracing
    enableDDLogs: true             # Enables log correlation
    enableXrayTracing: true        # Enables X-Ray integration
    enableSourceCodeIntegration: true  # Links traces to source code
    captureLambdaPayload: true     # Captures Lambda event payloads
    propagateUpstreamTrace: true   # Propagates traces across services
    addLayers: true                # Adds Datadog Lambda Layer automatically
    env: ${self:provider.stage}
    service: ai-marketing-scheduler
    version: ${env:GIT_SHA, 'latest'}
```

**Key Benefits**:

- Automatic layer attachment to all Lambda functions
- No manual code changes required for APM
- Automatic trace correlation with logs
- Step Function trace propagation

## Implementation Details

### Automatic Lambda Instrumentation

**Current Approach**: Lambda functions are **not manually wrapped**. Instead, the Datadog Lambda Layer provides automatic instrumentation:

```typescript
// Current implementation - no manual wrapping needed
export const handler = async (event: any, context: Context) => {
  const logger = createContextLogger('my-lambda', context, {
    lambdaName: 'my-lambda',
  });
  
  // Business logic - tracing happens automatically
  logger.info('Processing started');
  // ... rest of handler
};
```

**What the Datadog Layer Provides**:

- Automatic trace creation for Lambda invocations
- Cold start tracking
- AWS service call instrumentation
- Error and exception tracking
- Performance metrics

### No Manual Tracer Configuration

**Current Implementation**: No manual tracer initialization is required. The Datadog Lambda Layer handles:

- Service identification (`dd.service: ai-marketing-scheduler`)
- Environment tagging (`dd.env: production/staging`)
- Version tracking (`dd.version` from GIT_SHA)
- Log injection and correlation
- Runtime metrics collection

### Step Function Integration

**Current Approach**: Step Functions are automatically traced via:

1. **serverless-plugin-datadog** configuration:
   - `propagateUpstreamTrace: true`
   - `enableXrayTracing: true`

2. **No manual client wrapping** - AWS SDK calls are automatically instrumented by the layer

3. **Step Function State Machine traces** appear as distributed traces showing:
   - Individual Lambda executions
   - State transitions
   - Execution duration and status

## Monitoring Features

### Automatic Metrics (Available Now)

- **Lambda Performance**: Invocation counts, durations, memory usage
- **Cold Start Tracking**: Automatic cold start detection and measurement
- **Error Tracking**: Exception capture with stack traces
- **AWS Service Calls**: Automatic instrumentation of AWS SDK calls
- **Step Function Tracing**: State machine execution flows
- **Infrastructure Metrics**: Through Lambda Layer integration

### Distributed Tracing

**Automatic trace correlation across**:

- **Lambda Invocations**: Each function execution creates spans
- **Step Function Executions**: Parent spans for state machine runs
- **AWS Service Calls**: DynamoDB, S3, SQS, etc. automatically instrumented
- **HTTP Requests**: External API calls tracked
- **Error Propagation**: Exceptions traced across service boundaries

### Log-Trace Correlation

- **Automatic**: CloudWatch logs automatically include trace context
- **ContextLogger Integration**: Business context in `dd.custom.*` attributes linked to traces
- **Service Map**: Traces populate Datadog's Service Map automatically
- **No Configuration**: Handled by Datadog Lambda Layer

## Viewing Traces and Monitoring

### Datadog APM Dashboard

1. Navigate to **APM > Services** in Datadog
2. Look for service: **`ai-marketing-scheduler`**
3. Filter by environment: **`staging`** or **`production`**

### Available Views

- **Service Overview**: Performance, throughput, and error rates
- **Service Map**: Visual representation of service dependencies
- **Traces**: Individual execution traces with timing details
- **Infrastructure**: Lambda metrics, cold starts, memory usage

### Trace Search and Filtering

Use these tags to filter traces:

- `service:ai-marketing-scheduler`
- `env:staging` or `env:production`
- `resource_names:"lambda_name"` (specific Lambda function)
- `team:client-marketing`
- `Project:seo-automation`

### Step Function Monitoring

**Distributed traces show**:

- **Parent Span**: Complete Step Function execution
- **Child Spans**: Individual Lambda invocations
- **State Transitions**: Timing between states
- **Error Locations**: Exactly which step failed
- **Parallel Execution**: Concurrent Lambda invocations in Map states

### Lambda-Specific Views

For each Lambda function:

- **Invocation Details**: Duration, memory usage, cold starts
- **Error Analysis**: Stack traces and error patterns
- **Performance Trends**: Response time percentiles
- **Dependency Mapping**: External service calls

## Troubleshooting

### Common Issues

1. **Traces not appearing**
   - Verify `DD_API_KEY` is set correctly in environment
   - Check that Datadog Lambda Layer is attached (automatic via plugin)
   - Confirm serverless-plugin-datadog is in plugins list
   - Verify environment variables are set correctly by plugin

2. **Missing Step Function traces**
   - Confirm `propagateUpstreamTrace: true` in serverless.yml
   - Verify `enableXrayTracing: true` is set
   - Check that Step Function execution role has X-Ray permissions
   - Ensure Lambda functions have proper IAM permissions for X-Ray

3. **Logs not correlated with traces**
   - Verify CloudWatch logs are being forwarded to Datadog
   - Check that `enableDDLogs: true` in serverless configuration
   - Confirm ContextLogger is being used (automatic trace correlation)

4. **Service not appearing in Service Map**
   - Verify `dd.service` attribute is set correctly (automatic)
   - Check that traffic is flowing through the service
   - Confirm traces are being sent to Datadog

### Verification Steps

1. **Check Datadog Layer Attachment**:

   ```bash
   aws lambda get-function --function-name ai-marketing-scheduler-entitlement-fetcher
   # Should show Datadog layer in response
   ```

2. **Verify Environment Variables**:
   Check Lambda console for automatic variables:
   - `dd_trace_enabled`: should be `true`
   - `dd_service`: should be `ai-marketing-scheduler`
   - `dd_env`: should match your environment

3. **Test Trace Generation**:
   - Manually invoke a Lambda function
   - Check Datadog APM for traces within 2-3 minutes
   - Look for service `ai-marketing-scheduler` in Service Map

### Debug Mode

Enable debug logging for the Datadog layer:

```yaml
# In serverless.yml environment variables
DD_TRACE_DEBUG: true
DD_LOG_LEVEL: debug
```

## Performance Impact

- **Minimal Overhead**: ~1-2ms per invocation from Datadog Layer
- **Cached Layers**: Lambda layers are cached, reducing cold start impact
- **Automatic Optimization**: Layer handles sampling and optimization automatically
- **No Code Changes**: Zero performance impact from manual instrumentation (not used)

## Best Practices

### Current Implementation (Recommended)

1. **Use ContextLogger for Business Context**:

```typescript
// Add business context that appears in traces
logger.addCustomMetrics({
  companyId: 'company-123',
  workflowType: 'social_posts',
  processingDuration: 1500,
});
```

2. **Component-Level Logging**:

```typescript
// Creates traceable components in service architecture
const serviceLogger = parentLogger.createComponentLogger({
  serviceName: 'TenantService',
});
```

3. **Error Handling with Context**:

```typescript
// Use factory error handler for consistent trace error context
try {
  const result = await processData();
  return result;
} catch (error: unknown) {
  return handleHandlerError(error, logger, 'lambda-name');
}
```

### Advanced (If Manual Instrumentation Needed)

**Note**: Current implementation doesn't use manual spans, but they're available:

```typescript
// Only if you need custom spans beyond automatic instrumentation
import tracer from 'dd-trace';

const span = tracer.startSpan('custom.operation');
span.setTag('business.context', 'value');
// ... operation
span.finish();
```

### Monitoring Strategy

1. **Primary**: Use ContextLogger `dd.custom.*` attributes for business monitoring
2. **APM**: Rely on automatic traces for performance and error tracking
3. **Service Map**: Monitor service dependencies automatically
4. **Custom Metrics**: Enable MetricsClient only if needed for alerting

## Deployment

### Staging

```bash
ENVIRONMENT=staging pnpm deploy
```

### Production

```bash
ENVIRONMENT=production pnpm deploy
```

**What Happens During Deployment**:

1. serverless-plugin-datadog automatically attaches Datadog Layer
2. Environment variables are set automatically
3. X-Ray tracing permissions are configured
4. CloudWatch log forwarding is set up (if configured)

## Current Status

✅ **Implemented and Working**:

- Automatic APM tracing via Datadog Lambda Layer
- Service identification (`ai-marketing-scheduler`)
- Step Function distributed tracing
- Log-trace correlation
- Error and exception tracking
- Performance monitoring
- Service Map population

✅ **No Code Changes Needed**:

- Lambdas use standard implementation without manual wrapping
- ContextLogger provides business context that appears in traces
- Datadog Layer handles all APM instrumentation automatically

⚠️ **Optional Enhancements Available**:

- Native metrics via MetricsClient (not currently used)
- Custom span creation (not currently needed)
- Advanced sampling configuration

## Support

For issues or questions about Datadog APM integration:

- **Team**: client-marketing
- **Slack**: #client-marketing-alerts (for alerts and monitoring issues)
- **Service Catalog**: [Datadog Service Catalog](https://app.datadoghq.com/services)
- **Documentation**: [Datadog Lambda Docs](https://docs.datadoghq.com/serverless/)
- **APM Service**: [ai-marketing-scheduler APM](https://app.datadoghq.com/apm/services)
