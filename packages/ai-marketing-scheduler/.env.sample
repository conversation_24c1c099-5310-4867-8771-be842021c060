REGION=us-east-1
SEO_DRAFT_SFN_ARN=arn:aws:states:us-east-1:123456789012:stateMachine:seo-draft-sfn
BLOG_DRAFT_SFN_ARN=arn:aws:states:us-east-1:123456789012:stateMachine:blog-draft-sfn
SECURITY_GROUP_IDS=sg-xxxxx
SUBNET_IDS=subnet-xxxxx,subnet-yyyyy
ACTION_CHUNK_SIZE=5
TENANT_SERVICE_URL=https://tenant-service.luxurycoders.com
CMS_SERVICE_URL=https://cms-service.luxurycoders.com
COSMO_GQL_URL=http://localhost:3000 # the cosmo gql url doesn't work locally because of some auth stuff... Just use the sub graph
ENTITLEMENTS_LIMIT=100
ENTITLEMENTS_CHUNK_SIZE=10
PRODUCT_IDS=5c9b746c-7327-42ce-b998-ce707e7cee44
LAUNCHDARKLY_KEY=your-launchdarkly-key
GENERATE_POST_FUNCTION_NAME=generatePost
M2M_SUPER_API_KEY=localkey
SCHEDULER_STATE=false
ENTITLEMENTS_STATE=DISABLED
SURFACER_DAEMON_STATE=false
RANK_QUEUER_DAEMON_STATE=false
USE_MOCK_CLIENTS=false
WEEKLY_DIGEST_TEMPLATE_ID=some-template-id
ASM_GROUP_ID=123456
BCC_EMAILS=<EMAIL>
FIRECRAWL_API_KEY=your-firecrawl-api-key
DATAFORSEO_LOGIN=<EMAIL>
DATAFORSEO_PASSWORD=bdd75268e6bb2efb
DATAFORSEO_POSTBACK_URL=https://eoe9ydk095j4qdf.m.pipedream.net
LANGFUSE_SECRET_KEY=sk-lf-
LANGFUSE_PUBLIC_KEY=pk-lf-
OPENAI_API_KEY=sk-proj-
CLIENT_MARKETING_SERVICE_URL=https://client-marketing-service.luxurycoders.com
STAR_API_URL=http://localhost:8003
API_GATEWAY_URL=https://your-api-gateway.com
API_GATEWAY_SUPER_USER_COMPANY_ID=super-user-id
API_GATEWAY_KEY=your-api-key
SLACK_NOTIFICATIONS_ENABLED=false
SLACK_WEBHOOK_URL=slack-webhook-url
DD_API_KEY=your-datadog-api-key
