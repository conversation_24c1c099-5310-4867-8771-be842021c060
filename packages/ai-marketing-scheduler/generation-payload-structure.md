# Generation Payload Structure Documentation

This document describes how the `generationPayload` field evolves through each Lambda function in the SEO automation pipeline.

## Overview

The `generationPayload` is a JSON field in the `ScheduledAction` entity that accumulates data as it passes through the Lambda pipeline. It starts with minimal information and grows with each processing step.

## Lambda Pipeline Flow

1. **DraftActionSchedulerDaemon** → Creates initial action with `websiteUrl`
2. **UrlSelector** → Adds `selectedUrl` chosen from website mapping and analytics
3. **UrlScraper** → Adds `scraperResult` with page content and metadata
4. **KeywordExtractor** → Adds `keywords` extracted from content
5. **RecommendationGenerator** → Adds `recommendations` for SEO optimization
6. **StoreDraft** → No modifications (only status updates)
7. **Surfacer** → No modifications (only status updates)

## Detailed Structure at Each Step

### 1. Initial State (DraftActionSchedulerDaemon)

```json
{
  "websiteUrl": "https://example.com"
}
```

### 2. After UrlSelector <PERSON>da

```json
{
  "websiteUrl": "https://example.com",
  "selectedUrl": "https://example.com/actual-scraped-url"

}
```

### 3. After UrlScraper <PERSON>da

```json
{
  "websiteUrl": "https://example.com",
  "selectedUrl": "https://example.com/blog/post-title",
  "scraperResult": {
    "firecrawlId": "uuid-from-firecrawl",
    "url": "https://example.com/actual-scraped-url",
    "metaTitle": "Current Page Title",
    "metaDescription": "Current meta description from the page",
    "mainHeading": {
      "tag": "h1",
      "index": 0,
      "content": "Current H1 Heading Text"
    },
    "type": "HOMEPAGE", // PageType enum: HOMEPAGE, BLOG, etc.
    "markdown": "Full markdown content of the scraped page...",
    "mediaId": "screenshot-media-id-from-s3",
    "companyId": "company-uuid"
  }
}
```

### 4. After KeywordExtractor Lambda

```json
{
  "websiteUrl": "https://example.com",
  "selectedUrl": "https://example.com/actual-scraped-url",
  "scraperResult": { 
    // ... same as above 
  },
  "keywords": "real estate, homes for sale, [location], property listings"
}
```

### 5. After RecommendationGenerator Lambda (Final State)

```json
{
  "websiteUrl": "https://example.com",
  "selectedUrl": "https://example.com/actual-scraped-url",
  "scraperResult": { 
    // ... same as above 
  },
  "keywords": "real estate, homes for sale, [location], property listings",
  "recommendations": {
    "metaTitle": {
      "currentValue": "Current Page Title",
      "recommendationValue": "SEO Optimized Title | Brand Name",
      "reasoning": "Added primary keywords and brand name for better SEO visibility"
    },
    "metaDescription": {
      "currentValue": "Current meta description from the page",
      "recommendationValue": "Compelling meta description optimized for target keywords and under 160 characters...",
      "reasoning": "Incorporated primary keywords while maintaining readability and staying within character limit"
    },
    "mainHeading": {
      "currentValue": "Current H1 Heading Text",
      "recommendationValue": "Optimized H1 Heading for Better SEO",
      "reasoning": "Improved heading to include target keywords while maintaining user readability"
    }
  }
}
```

## Key Implementation Details

### Database Storage

- Stored as `JSONObject` type in PostgreSQL
- TypeScript type: `Record<string, unknown> | null`
- Persisted between Lambda invocations

### Update Pattern

Each Lambda uses the `ScheduledActionService.updateScheduledAction()` method to merge new data:

```typescript
await scheduledActionService.updateScheduledAction(actionId, {
  generationPayload: {
    ...existingGenerationPayload,
    newField: newValue
  }
});
```

### Idempotency

- Each Lambda checks if its output already exists in `generationPayload`
- Skips processing if the expected fields are already present
- Uses `checkAction()` method to verify if processing is needed

### Important Notes

1. The `generationPayload` is never cleared or reset during the pipeline
2. Data accumulates but is never removed
3. StoreDraft and subsequent Lambdas do not modify this field
4. The `contentPayload` field (separate from `generationPayload`) stores the final recommendations in a simpler format

## Related Types

### ScraperResult Interface

```typescript
interface ScraperResult {
  firecrawlId: string;
  url: string;
  metaTitle?: string;
  metaDescription?: string;
  mainHeading: {
    tag: string;
    index: number;
    content: string;
  };
  type: PageType;
  markdown: string;
  mediaId: string;
  companyId: string;
}
```

### PageType Enum

```typescript
enum PageType {
  HOMEPAGE = 'HOMEPAGE',
  HOME_VALUATION = 'HOME_VALUATION',
  MORTGAGE_CALCULATOR = 'MORTGAGE_CALCULATOR',
  BUYERS_GUIDE = 'BUYERS_GUIDE',
  SELLERS_GUIDE = 'SELLERS_GUIDE',
  NEIGHBORHOOD_GUIDE = 'NEIGHBORHOOD_GUIDE',
  AGENT_BIO = 'AGENT_BIO',
  BLOG = 'BLOG',
}
```

### SeoRecommendation Interface

```typescript
interface SeoRecommendationItem {
  currentValue: string;
  recommendationValue: string;
  reasoning: string;
}

interface SeoRecommendation {
  metaTitle: SeoRecommendationItem;
  metaDescription: SeoRecommendationItem;
  mainHeading: SeoRecommendationItem;
}
```

### Simple Recommendation Interface (contentPayload)

```typescript
interface Recommendation {
  metaTitle: string;
  metaDescription: string;
  mainHeading: string;
}
```
