'use strict';

// Useful for local deployment to the dev sandbox environment
// where the presence of these variables breaks the deployment

/**
 * Conditionally include configurations based on environment variables
 */
// eslint-disable-next-line @typescript-eslint/require-await
module.exports = async () => {
  const deployBucketConfig = {};

  // If DEPLOY_BUCKET is defined, add the deploymentBucket configuration
  if (process.env.DEPLOY_BUCKET) {
    deployBucketConfig.name = process.env.DEPLOY_BUCKET;
    deployBucketConfig.maxPreviousDeploymentArtifacts = 10;
  }

  return deployBucketConfig;
};
