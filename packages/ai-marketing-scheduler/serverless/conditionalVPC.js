'use strict';

// Useful for local deployment to the dev sandbox environment
// where the presence of these variables breaks the deployment

/**
 * Conditionally include configurations based on environment variables
 */
// eslint-disable-next-line @typescript-eslint/require-await
module.exports = async () => {
  const configVPC = {};

  // If SECURITY_GROUP_IDS and SUBNET_IDS are defined, add the vpc configuration
  if (process.env.SECURITY_GROUP_IDS && process.env.SUBNET_IDS) {
    configVPC.securityGroupIds = {
      'Fn::Split': [',', process.env.SECURITY_GROUP_IDS],
    };
    configVPC.subnetIds = {
      'Fn::Split': [',', process.env.SUBNET_IDS],
    };
  }

  return configVPC;
};
