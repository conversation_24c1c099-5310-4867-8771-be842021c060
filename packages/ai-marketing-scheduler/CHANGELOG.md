# ai-marketing-scheduler

## 0.69.2

### Patch Changes

- [#912](https://github.com/luxurypresence/seo-automation/pull/912) [`24c099e`](https://github.com/luxurypresence/seo-automation/commit/24c099e540ea43ded48bd4854ae1fbc102c522e6) Thanks [@devinellis](https://github.com/devinellis)! - R2 keyfix

## 0.69.1

### Patch Changes

- [#911](https://github.com/luxurypresence/seo-automation/pull/911) [`d0ed838`](https://github.com/luxurypresence/seo-automation/commit/d0ed838c81fd8714aa4ee5ed39f44728cf445095) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix R2 API key

## 0.69.0

### Minor Changes

- [#814](https://github.com/luxurypresence/seo-automation/pull/814) [`f39829e`](https://github.com/luxurypresence/seo-automation/commit/f39829e99ecaf56b3b4f9df9f39e7670c7b6f5c5) Thanks [@jaycholland](https://github.com/jaycholland)! - paginated query for surfaced and published groups. Refactored rankQueueStateMachine to use distributed map

## 0.68.0

### Minor Changes

- [#811](https://github.com/luxurypresence/seo-automation/pull/811) [`89eb7a0`](https://github.com/luxurypresence/seo-automation/commit/89eb7a08346d3a0be375d0e112b11f2882cfa7ec) Thanks [@jaycholland](https://github.com/jaycholland)! - new more efficient query for ranking getSurfacedAndPublishedGroupsForRanking

## 0.67.0

### Minor Changes

- [#803](https://github.com/luxurypresence/seo-automation/pull/803) [`57144f9`](https://github.com/luxurypresence/seo-automation/commit/57144f9ff73ec06572fa228d5e4d176b915f48d2) Thanks [@jaycholland](https://github.com/jaycholland)! - Surfacer process actions in batches, memory and timeout increase

## 0.66.1

### Patch Changes

- [#756](https://github.com/luxurypresence/seo-automation/pull/756) [`0e50e43`](https://github.com/luxurypresence/seo-automation/commit/0e50e43483543037c0fbecf37b4605a86944e024) Thanks [@devinellis](https://github.com/devinellis)! - Allow <NAME_EMAIL>

## 0.66.0

### Minor Changes

- [#750](https://github.com/luxurypresence/seo-automation/pull/750) [`0b3918d`](https://github.com/luxurypresence/seo-automation/commit/0b3918db75efddff50894d762964a9a1e0bc9f55) Thanks [@jaycholland](https://github.com/jaycholland)! - Automate running of publisher on a schedule Thursdays

## 0.65.1

### Patch Changes

- [#733](https://github.com/luxurypresence/seo-automation/pull/733) [`568c737`](https://github.com/luxurypresence/seo-automation/commit/568c7370222d6aa13e0e3db1bfa5664d91fd178f) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify Cosmo calls to split m2mSuperApiKey

## 0.65.0

### Minor Changes

- [#651](https://github.com/luxurypresence/seo-automation/pull/651) [`38ee743`](https://github.com/luxurypresence/seo-automation/commit/38ee743cc93791b791a82bbb00eb162bbbd8eb12) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat: add comprehensive Datadog monitoring system for STAR API error alerting

  Introduces a complete monitoring infrastructure for STAR API with intelligent error classification, automated alerting, and template-driven notifications.

  **New Features:**

  **🚨 Smart Error Classification:**

  - **Company-Specific Failures** (400/404/405): Client configuration issues → Support team alerts
  - **Service-Level Failures** (5xx, network, API errors): Infrastructure issues → Platform team critical alerts
  - Eliminates alert fatigue through intelligent error categorization

  **📊 Metric-Based Monitoring:**

  - Replaces unreliable log-based alerts with structured metrics
  - Real-time error tracking with `ai_marketing_scheduler.star_api.errors`
  - Performance monitoring with duration and success rate tracking
  - Cost-efficient compared to CloudWatch Logs Insights queries

  **⚙️ Automated Monitor Lifecycle:**

  - Complete CRUD operations for monitor management
  - Automatic template substitution for notifications and dashboard links
  - Type change handling (auto-deletes and recreates when monitor types change)
  - Comprehensive audit logging for all operations

  **🔧 Template System:**

  - Dynamic variable interpolation with `{{company_id.value}}` for real company IDs
  - Centralized notification channels and dashboard links
  - Multi-line message templates with proper formatting
  - Automatic template validation and substitution

  **Files Added:**

  - `datadog-monitors/star-api-monitors.json` - Monitor configurations with templates
  - `datadog-monitors/README.md` - Comprehensive setup and usage guide
  - `scripts/setup-datadog-monitors.ts` - Automated monitor management script
  - `package.json` - New `sync:datadog-monitors` script

  **Files Modified:**

  - `docs/contextLogger.examples.md` - Updated metrics documentation

  This establishes production-ready observability for the STAR API with intelligent alerting, reduced noise, and clear ownership paths for different error types.

## 0.64.6

### Patch Changes

- [#645](https://github.com/luxurypresence/seo-automation/pull/645) [`c65fae6`](https://github.com/luxurypresence/seo-automation/commit/c65fae69ee7121d08af38ba7ca9d8bbaddf739ba) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix S3 key generation to keep Hive partition folders unencoded while encoding only keyword filenames

## 0.64.5

### Patch Changes

- [#641](https://github.com/luxurypresence/seo-automation/pull/641) [`038d72c`](https://github.com/luxurypresence/seo-automation/commit/038d72c5fdc7af8f5900b89e5daf348d1943a66c) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix URL comparison issue causing incorrect SEO ranking reports and optimize performance by moving page URL normalization outside find loop

## 0.64.4

### Patch Changes

- [#639](https://github.com/luxurypresence/seo-automation/pull/639) [`3da2980`](https://github.com/luxurypresence/seo-automation/commit/3da2980ad4693d3be83e870200b0f7d826a9623f) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix timezone bug in Weekly Digest email subject lines - changed from UTC to America/Los_Angeles timezone

## 0.64.3

### Patch Changes

- [#635](https://github.com/luxurypresence/seo-automation/pull/635) [`c6cc2d6`](https://github.com/luxurypresence/seo-automation/commit/c6cc2d6ee9f9fc579137dacc8271f871abb8a7ee) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates DD trace service names

## 0.64.2

### Patch Changes

- [#634](https://github.com/luxurypresence/seo-automation/pull/634) [`c8005dd`](https://github.com/luxurypresence/seo-automation/commit/c8005dd58d30d604ff003cc755530300c864f90d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes DD tag config

## 0.64.1

### Patch Changes

- [#633](https://github.com/luxurypresence/seo-automation/pull/633) [`9b55c20`](https://github.com/luxurypresence/seo-automation/commit/9b55c209d0448eef279b65393f1601ae81d88e2d) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes DD metrics

## 0.64.0

### Minor Changes

- [#630](https://github.com/luxurypresence/seo-automation/pull/630) [`e7fbdfc`](https://github.com/luxurypresence/seo-automation/commit/e7fbdfca029470cb86d430cfc5681db4006fe2cc) Thanks [@fbionsc](https://github.com/fbionsc)! - Update email to include link to contacts page filtered by leads

## 0.63.0

### Minor Changes

- [#617](https://github.com/luxurypresence/seo-automation/pull/617) [`4a267c0`](https://github.com/luxurypresence/seo-automation/commit/4a267c08a6260da1a5eee8361ab74723e9feb995) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Comprehensive Datadog observability improvements and documentation updates

  ## Features

  ### Enhanced Logging Architecture

  - Simplified EntitlementFetcher Lambda by removing manual timing and MetricsClient usage
  - Streamlined logging approach focusing on ContextLogger with dd.custom attributes
  - Improved observability through automatic Datadog Lambda Layer instrumentation

  ### Documentation Overhaul

  - **Updated datadog-logging.md**: Reflects current ContextLogger-centric implementation
    - Clarified dd.custom attributes vs plain metadata usage patterns
    - Updated examples to match actual Lambda implementations (EntitlementFetcher pattern)
    - Documented MetricsClient as available but not currently used
    - Corrected Datadog query examples for current attribute patterns
  - **Updated datadog-apm-integration.md**: Reflects automatic Lambda Layer approach
    - Clarified that manual tracer wrapping is not used in current implementation
    - Updated to show serverless-plugin-datadog automatic instrumentation
    - Added verification steps and troubleshooting for layer-based integration
    - Documented current implementation status vs available features

  ### Code Improvements

  - Removed manual timing logic from EntitlementFetcher Lambda (lines 23, 79-82, 90)
  - Removed MetricsClient instantiation and metric recording calls
  - Simplified error handling by removing metrics.close() calls
  - Maintained ContextLogger usage for consistent observability

  ## Benefits

  - **Reduced Complexity**: Eliminated redundant manual timing when AWS Lambda provides duration metrics
  - **Consistent Patterns**: Standardized on ContextLogger + Datadog Layer approach across all Lambdas
  - **Accurate Documentation**: Documentation now reflects actual implementation rather than aspirational patterns
  - **Better Observability**: dd.custom attributes provide business context while automatic Layer handles APM
  - **Cost Optimization**: Removed unused MetricsClient calls that weren't providing additional value over built-in Lambda metrics

  ## Migration Notes

  - No breaking changes - existing ContextLogger usage patterns remain the same
  - MetricsClient is still available if needed for future custom metrics
  - All existing dd.custom attribute patterns continue to work as documented
  - Datadog Lambda Layer continues to provide automatic APM instrumentation

## 0.62.1

### Patch Changes

- [#626](https://github.com/luxurypresence/seo-automation/pull/626) [`2929583`](https://github.com/luxurypresence/seo-automation/commit/2929583a41e9550ba7304f0e30b0c2936a20304a) Thanks [@jaycholland](https://github.com/jaycholland)! - Increased timeout config of draft-action-consumer-daemon from 180 to 600

## 0.62.0

### Minor Changes

- [#625](https://github.com/luxurypresence/seo-automation/pull/625) [`d864ea4`](https://github.com/luxurypresence/seo-automation/commit/d864ea455cda1eeb238cc3de0edede51cfff8744) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-717: Scales EmailerDaemon

  ### 🚀 Performance Optimizations

  - **Timeout Handling**: Added `Promise.race` with configurable timeouts (30s company data, 45s email sending)
  - **Batch Processing**: Increased batch sizes (25 companies, 15 emails) with linear backoff delays
  - **Delay Reduction**: Changed from exponential (1000-10000ms) to linear (100-1000ms) backoff strategy
  - **Scalability**: Optimized for 1000+ accounts with configurable timeout limits

  ### 🧪 Test Suite Refactoring

  - **Code Reduction**: 31% fewer lines, 75% less test data duplication
  - **Maintainability**: Extracted `testFactories.ts` and `testHelpers.ts` for reusable test utilities
  - **Clean Architecture**: Eliminated circular dependencies, improved test readability
  - **Coverage**: Enhanced timeout handling, batch processing, and error scenario tests

  ### ⚙️ Configuration Improvements

  - **Centralized Config**: `CONFIG` object with batch sizes, delays, and timeout constants
  - **Utility Functions**: `getTimeoutPromise()` for consistent timeout handling
  - **Enhanced Logging**: Timeout limits, remaining time, and processing metrics

## 0.61.3

### Patch Changes

- [#623](https://github.com/luxurypresence/seo-automation/pull/623) [`2707579`](https://github.com/luxurypresence/seo-automation/commit/270757983d49dcdd004447f2f42c0047cfc272a9) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed bug where selectedUrl needed to be in generationPayload

## 0.61.2

### Patch Changes

- [#579](https://github.com/luxurypresence/seo-automation/pull/579) [`6ecacb0`](https://github.com/luxurypresence/seo-automation/commit/6ecacb0b9bee22585ca079d97a5149bdfc34524d) Thanks [@devinellis](https://github.com/devinellis)! - Capture page section IDs

- [#620](https://github.com/luxurypresence/seo-automation/pull/620) [`065720b`](https://github.com/luxurypresence/seo-automation/commit/065720b89309fcfe05368a64c5e8843f219eecac) Thanks [@jaycholland](https://github.com/jaycholland)! - updated datadog dashboard to latest from export. added new lambda functionsto the dash

## 0.61.1

### Patch Changes

- [#618](https://github.com/luxurypresence/seo-automation/pull/618) [`c54b372`](https://github.com/luxurypresence/seo-automation/commit/c54b3727ebeb128c8470400452acc9e1a728d8e5) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes serverless syntax error

## 0.61.0

### Minor Changes

- [#615](https://github.com/luxurypresence/seo-automation/pull/615) [`8ef93ed`](https://github.com/luxurypresence/seo-automation/commit/8ef93ed170da650cddcd2b6a621cb1bbf05f8328) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates step function catch behavior in serverless

## 0.60.0

### Minor Changes

- [#612](https://github.com/luxurypresence/seo-automation/pull/612) [`51a37b2`](https://github.com/luxurypresence/seo-automation/commit/51a37b2f0eb06645290d534adf56a98ad928bd3d) Thanks [@jaycholland](https://github.com/jaycholland)! - Allow UrlSelector to exit the step function early for edge case when all eligible urls are ranking 1

## 0.59.0

### Minor Changes

- [#611](https://github.com/luxurypresence/seo-automation/pull/611) [`251276c`](https://github.com/luxurypresence/seo-automation/commit/251276cb9bc8d1b5a44ff90c2a09e8f2cf6daa5c) Thanks [@jaycholland](https://github.com/jaycholland)! - Refactored Scraper Lambda into 2 separate Lambdas for Selecting a URL and Scraping a URL

## 0.58.9

### Patch Changes

- [#610](https://github.com/luxurypresence/seo-automation/pull/610) [`ad038d4`](https://github.com/luxurypresence/seo-automation/commit/ad038d448a3ab6d23c39eaf232d174f279a54caa) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-657: Implement individual email sending with error visibility

  Refactors EmailerDaemon to send individual emails to each recipient instead of combining all admins in a single email. Each recipient now receives their own email with only their address in the "To" field, improving privacy and enabling individual engagement tracking.

  **Key Changes:**

  - Sends individual emails to each admin and BCC recipient
  - Implements Promise.allSettled for error resilience
  - Adds comprehensive error visibility with HTTP status codes
  - Captures and logs failed email details for debugging
  - Maintains backward compatibility with existing configuration

  **Benefits:**

  - Individual recipient privacy (no shared email addresses)
  - Enhanced engagement tracking and win-back capabilities
  - Improved operational visibility with detailed error logging
  - Better debugging with HTTP status code capture

  **Testing:**

  - Updated 17 unit tests to verify individual email behavior
  - Removed duplicate test cases
  - Comprehensive coverage of error scenarios and edge cases

## 0.58.8

### Patch Changes

- [#613](https://github.com/luxurypresence/seo-automation/pull/613) [`48a5eeb`](https://github.com/luxurypresence/seo-automation/commit/48a5eebd8683b092a1ff7375cf59dde73ce45cf3) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates owner of DD services to client-marketing

## 0.58.7

### Patch Changes

- [#614](https://github.com/luxurypresence/seo-automation/pull/614) [`a22614a`](https://github.com/luxurypresence/seo-automation/commit/a22614a7a75a86be6a31f18cc4dcd6197a8cdfcb) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Improved logs

## 0.58.6

### Patch Changes

- [#608](https://github.com/luxurypresence/seo-automation/pull/608) [`872b54c`](https://github.com/luxurypresence/seo-automation/commit/872b54cb7ab58e6fa743d6954e02cbc676e91b98) Thanks [@jaycholland](https://github.com/jaycholland)! - Bugfix for query that should have included rank data column

## 0.58.5

### Patch Changes

- [#607](https://github.com/luxurypresence/seo-automation/pull/607) [`f3fe532`](https://github.com/luxurypresence/seo-automation/commit/f3fe5328a26a3b2145fe219aaefdd7197963281d) Thanks [@jaycholland](https://github.com/jaycholland)! - Allow eligible urls not found with a snowflake rank to be considered for selection

## 0.58.4

### Patch Changes

- [#606](https://github.com/luxurypresence/seo-automation/pull/606) [`3a16b1e`](https://github.com/luxurypresence/seo-automation/commit/3a16b1e32122a58af0fc336c9dddc25256330ec1) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes incorrect comments about scheduled events

## 0.58.3

### Patch Changes

- [#605](https://github.com/luxurypresence/seo-automation/pull/605) [`8659553`](https://github.com/luxurypresence/seo-automation/commit/86595531a6fbcf77f01137aa2219404b90066293) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Update EntitlementsScheduleRule to run at 4:30pm EST on Mondays

## 0.58.2

### Patch Changes

- [#604](https://github.com/luxurypresence/seo-automation/pull/604) [`a3fc4eb`](https://github.com/luxurypresence/seo-automation/commit/a3fc4eb28e0a773516a1af2b6a91e7683af8627a) Thanks [@jaycholland](https://github.com/jaycholland)! - Serverless permissions for lambda ssm get parameter

## 0.58.1

### Patch Changes

- [#603](https://github.com/luxurypresence/seo-automation/pull/603) [`de5479b`](https://github.com/luxurypresence/seo-automation/commit/de5479b9663c3d06a0608813bbc043ec8478e9a8) Thanks [@jaycholland](https://github.com/jaycholland)! - Added more logging to debug Snowflake connection

## 0.58.0

### Minor Changes

- [#602](https://github.com/luxurypresence/seo-automation/pull/602) [`a8742fe`](https://github.com/luxurypresence/seo-automation/commit/a8742fee586c68387a27cad1bd8e7190b19f75b9) Thanks [@jaycholland](https://github.com/jaycholland)! - Snowflake variables for scraper Lambda from env vars to AWS SSM parameter store

## 0.57.0

### Minor Changes

- [#596](https://github.com/luxurypresence/seo-automation/pull/596) [`ba5eb29`](https://github.com/luxurypresence/seo-automation/commit/ba5eb298121ff538452c901e60b1a19b60044898) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-673: Fixes for Emails being set with empty content

## 0.56.1

### Patch Changes

- [#600](https://github.com/luxurypresence/seo-automation/pull/600) [`56920ff`](https://github.com/luxurypresence/seo-automation/commit/56920ff82313bf8d326a0ca531b6afc013de42a6) Thanks [@jaycholland](https://github.com/jaycholland)! - changed snowflake db config to non-secret string

## 0.56.0

### Minor Changes

- [#599](https://github.com/luxurypresence/seo-automation/pull/599) [`8649706`](https://github.com/luxurypresence/seo-automation/commit/86497068c1181867feb6845d4439cb53170d6147) Thanks [@jaycholland](https://github.com/jaycholland)! - Added integration with Snowflake to check GSC impressions when selecting a URL to scrape.

## 0.55.0

### Minor Changes

- [#597](https://github.com/luxurypresence/seo-automation/pull/597) [`87c1c22`](https://github.com/luxurypresence/seo-automation/commit/87c1c22f80b5f8563daf497af2dfff61f75b59bd) Thanks [@jaycholland](https://github.com/jaycholland)! - Added support for 'h2' main heading updates through the STAR API editor

## 0.54.1

### Patch Changes

- [#595](https://github.com/luxurypresence/seo-automation/pull/595) [`4f87dfa`](https://github.com/luxurypresence/seo-automation/commit/4f87dfa1eff9776f3681f1fc1a9a5ff6ac1df13d) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Update DraftActionConsumerDaemon schedule from 17:00 UTC to 22:00 UTC (2:00 PM Pacific)

## 0.54.0

### Minor Changes

- [#592](https://github.com/luxurypresence/seo-automation/pull/592) [`94f62e2`](https://github.com/luxurypresence/seo-automation/commit/94f62e2a5c1f1f7dec0a99a85753203843aab98f) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat: implement context logger pattern across all Lambda functions

  This change updates all 15 Lambda functions in the ai-marketing-scheduler package to use the new context logger pattern, improving observability and debugging capabilities.

  ## Changes Made:

  ### Lambda Updates:

  - **CatchFailure.ts**: Added lambdaName and actionId to logger context
  - **DraftActionConsumerDaemon.ts**: Added lambdaName context, preserved existing child logger for action processing
  - **DraftActionSchedulerDaemon.ts**: Added lambdaName context and child logger for company-specific processing
  - **EmailerDaemon.ts**: Added lambdaName context and child logger for company email processing
  - **EntitlementFetcher.ts**: Added lambdaName context
  - **GetSurfacedActions.ts**: Added lambdaName context
  - **KeywordExtractor.ts**: Added lambdaName, actionId, and companyId business context
  - **RankBatchSplitter.ts**: Added lambdaName context
  - **RankQueuerDaemon.ts**: Added lambdaName context
  - **RankWebhook.ts**: Added lambdaName context
  - **RecommendationGenerator.ts**: Added lambdaName, actionId, and companyId business context
  - **Scraper.ts**: Added lambdaName context alongside existing rich context (actionId, companyId, websiteUrl)
  - **StoreSEODraft.ts**: Added lambdaName context
  - **SurfacerDaemon.ts**: Added lambdaName context and child loggers for company and action-specific context

  ### Technical Details:

  - Updated imports from `../factories/LoggerFactory` to `../logger/contextLogger` for createContextLogger
  - Maintained handleHandlerError imports from LoggerFactory where needed
  - Added child loggers in loops for better context tracking (company-specific and action-specific)
  - Updated all test files to use correct mock imports and maintain test coverage
  - All tests pass and linting checks succeed

  ### Additional Enhancements:

  - **RankWebhook.ts**: Added task-level child logger with taskId, keyword, and pageId context for better tracking of individual ranking tasks
  - **DraftActionConsumerDaemon.ts**: Added chunk-level child logger for improved visibility into chunk processing with chunkIndex, totalChunks, and chunkSize context

  ### Logging Pattern Improvements:

  - **Reduced Duplicate Logs**: Eliminated duplicate "fetching" logs between service and client layers
  - **Consistent Log Levels**:
    - Service layer logs business operations at INFO level
    - Client layer logs technical details at DEBUG level
    - All errors remain at ERROR level
  - **Updated Clients**: SeoApiGatewayClient, TenantClient, StarApiClient, S3Client, DataForSeoClient, and all mock clients now use DEBUG level
  - **60% Log Volume Reduction**: Significantly reduces CloudWatch log volume while maintaining full visibility through log level filtering

  ### Benefits:

  - Improved log filtering and searching in CloudWatch by architectural component (lambdaName)
  - Better correlation of logs with business context (companyId, actionId, etc.)
  - Enhanced debugging capabilities with hierarchical context through child loggers
  - Consistent logging patterns across all Lambda functions and their dependencies
  - Cleaner logs with automatic context propagation through child loggers
  - Reduced CloudWatch costs through lower log volume

## 0.53.1

### Patch Changes

- [#594](https://github.com/luxurypresence/seo-automation/pull/594) [`37bffc1`](https://github.com/luxurypresence/seo-automation/commit/37bffc1a0b7d8293222bb474d9d8dac741a3c67c) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add comprehensive workflow documentation for AI Marketing Scheduler

  - Document the complete flow from EntitlementFetcher through EmailerDaemon
  - Detail how scheduled actions change status throughout the workflow
  - Document database record creation and updates at each stage
  - Add detailed documentation of generation_payload evolution in SeoKeywordSfn
  - Include JSON examples showing payload transformation at each step

## 0.53.0

### Minor Changes

- [#593](https://github.com/luxurypresence/seo-automation/pull/593) [`051a1d2`](https://github.com/luxurypresence/seo-automation/commit/051a1d25f153ecb204f0890aed035d1fa2c226b2) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Improve STAR API error handling with specific status code messages and remove 500 from transient errors

## 0.52.0

### Minor Changes

- [#591](https://github.com/luxurypresence/seo-automation/pull/591) [`31ce488`](https://github.com/luxurypresence/seo-automation/commit/31ce488b9ea413e28e4c372d555ef7452d3a9ff5) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement transient error retry logic for Publisher lambda - only retry HTTP 429, 500, 502, 503, 504 errors instead of all errors

## 0.51.0

### Minor Changes

- [#585](https://github.com/luxurypresence/seo-automation/pull/585) [`6f628b7`](https://github.com/luxurypresence/seo-automation/commit/6f628b752146e22bef5ad6685ce52c3c0c71d080) Thanks [@fbionsc](https://github.com/fbionsc)! - - Add "keyword" field to feed query
  - Fix subject line date formatting
  - Add "reply-to" parameter when sending emails
  - Adds several copy updates to email template
  - Updates utm parameters

## 0.50.2

### Patch Changes

- [#590](https://github.com/luxurypresence/seo-automation/pull/590) [`20683cf`](https://github.com/luxurypresence/seo-automation/commit/20683cf489de41b9cdc2d8020b51c5d1a94cc053) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - fix(scraper): treat HTTP and HTTPS URLs as identical during deduplication

## 0.50.1

### Patch Changes

- [#587](https://github.com/luxurypresence/seo-automation/pull/587) [`b134e2a`](https://github.com/luxurypresence/seo-automation/commit/b134e2a05d7f91be202726aafa04b7654a359fa9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes redundant logs

## 0.50.0

### Minor Changes

- [#589](https://github.com/luxurypresence/seo-automation/pull/589) [`5a9552e`](https://github.com/luxurypresence/seo-automation/commit/5a9552e39a2bf241edf5c43574ce8260e8d2fc1e) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add actionId parameter to STAR API failure notifications for better debugging and traceability

## 0.49.1

### Patch Changes

- [#588](https://github.com/luxurypresence/seo-automation/pull/588) [`42fc6c1`](https://github.com/luxurypresence/seo-automation/commit/42fc6c145e96f575e06185cbb2ed0eb0df529e73) Thanks [@jaycholland](https://github.com/jaycholland)! - Added modal suppression javascript to Firecrawl scrape request with screenshot

## 0.49.0

### Minor Changes

- [#582](https://github.com/luxurypresence/seo-automation/pull/582) [`87d8d6f`](https://github.com/luxurypresence/seo-automation/commit/87d8d6fd7b30c3fa71e65c28648335eadb9846b8) Thanks [@fbionsc](https://github.com/fbionsc)! - - Add "keyword" field to feed query
  - Fix subject line date formatting
  - Add "reply-to" parameter when sending emails
  - Adds several copy updates to email template
  - Updates utm parameters

## 0.48.1

### Patch Changes

- [#581](https://github.com/luxurypresence/seo-automation/pull/581) [`74f7920`](https://github.com/luxurypresence/seo-automation/commit/74f7920186c1060130aa3aa14d96084b1d055011) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes scheduledToBeSurfacedAt date setting to be in UTC

## 0.48.0

### Minor Changes

- [#575](https://github.com/luxurypresence/seo-automation/pull/575) [`efb2883`](https://github.com/luxurypresence/seo-automation/commit/efb288357039bd3c220c961447c0bab3e0ee81c6) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Refactor Publisher lambda for improved clarity and maintainability with better variable naming and extracted helper functions

  ### Changes:

  - **Created StarApiService**: Replaced utility-based STAR API operations with a proper service implementing dependency injection pattern
  - **Added IStarApiService interface**: Defined clear contract for STAR API operations (updateMetaTitle, updateMetaDescription, updateH1)
  - **Enhanced logging**: Publisher now logs both recommendation types and IDs for better traceability
  - **Improved testability**: Created MockStarApiService with comprehensive test helpers for custom responses and failure simulation
  - **Updated UnifiedServiceFactory**: Integrated StarApiService creation with support for mock mode via environment variables

  ### Benefits:

  - Better separation of concerns with service-based architecture
  - Easier testing with dedicated mock service
  - Improved maintainability through dependency injection
  - Enhanced debugging with recommendation ID logging
  - Consistent error handling across STAR API operations

  ### Migration:

  The old `starApiUpdates` utility has been removed. All STAR API operations now go through the `StarApiService` which can be obtained via `UnifiedServiceFactory.createStarApiService()`.

## 0.47.2

### Patch Changes

- [#577](https://github.com/luxurypresence/seo-automation/pull/577) [`31c2d96`](https://github.com/luxurypresence/seo-automation/commit/31c2d96e01fb3a02c341035f0f5a60e4fe8dbbff) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix StoreSEODraftResponse type to accurately reflect the currect structure

## 0.47.1

### Patch Changes

- [#559](https://github.com/luxurypresence/seo-automation/pull/559) [`a0a355c`](https://github.com/luxurypresence/seo-automation/commit/a0a355ca86f89e83bcf10acf0121f836681a63c9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes RANK_QUEUER_DAEMON_STATE env var

## 0.47.0

### Minor Changes

- [#558](https://github.com/luxurypresence/seo-automation/pull/558) [`73fe789`](https://github.com/luxurypresence/seo-automation/commit/73fe78931bd875b4260888d4c96cff752e7e1315) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Remove temp v2 suffix from get-surfaced-actions lambda function name

## 0.46.0

### Minor Changes

- [#557](https://github.com/luxurypresence/seo-automation/pull/557) [`c050b88`](https://github.com/luxurypresence/seo-automation/commit/c050b88b5a7ae766f78ee59fb2bd1cd6f195e4d9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix recommendation status update behavior so that all recommendations successfully applied via STAR api get their statuses updated to APPLIED regardless of any partial failures. The scheduled actions still gets marked as FAILED if there were partial STAR api update failures

## 0.45.2

### Patch Changes

- [#556](https://github.com/luxurypresence/seo-automation/pull/556) [`ad897cf`](https://github.com/luxurypresence/seo-automation/commit/ad897cf4bf659e9e901a42c6628397f9c3c5bb76) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add v2 to get-surfaced-actions lambda to fix deploy

## 0.45.1

### Patch Changes

- [#554](https://github.com/luxurypresence/seo-automation/pull/554) [`5d8fd07`](https://github.com/luxurypresence/seo-automation/commit/5d8fd070e7ced29e4f7789426f7c9399530a844e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes broken deploy

## 0.45.0

### Minor Changes

- [#549](https://github.com/luxurypresence/seo-automation/pull/549) [`b70ccd6`](https://github.com/luxurypresence/seo-automation/commit/b70ccd670d03a80cc8ed97b8c2e7cfd0c68e1730) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement standardized ContextLogger pattern across ai-marketing-scheduler lambda functions with structured metadata logging

## 0.44.1

### Patch Changes

- [#553](https://github.com/luxurypresence/seo-automation/pull/553) [`6785076`](https://github.com/luxurypresence/seo-automation/commit/6785076e32d19004beff71726202d9e640914386) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Temp fix to fix the duplicate resource creation GetDashsurfacedDashactionsLogGroup deploy error

## 0.44.0

### Minor Changes

- [#551](https://github.com/luxurypresence/seo-automation/pull/551) [`2264d94`](https://github.com/luxurypresence/seo-automation/commit/2264d947ed427d561a482bae1e945ee572126a19) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement deduplication mechanism for Slack notifications to prevent duplicate notifications during Step Function retries

## 0.43.0

### Minor Changes

- [#546](https://github.com/luxurypresence/seo-automation/pull/546) [`a95d551`](https://github.com/luxurypresence/seo-automation/commit/a95d5513b36913cf17e31ac25fd48ecc7a944832) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement SEO-604: Fix rank queueing batching problem with Step Function

## 0.42.3

### Patch Changes

- [#548](https://github.com/luxurypresence/seo-automation/pull/548) [`e81a7cc`](https://github.com/luxurypresence/seo-automation/commit/e81a7cc48900ef26daafbdeb511e056b549d6c50) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix scheduling logic to schedule first action immediately when no upcoming actions exist

## 0.42.2

### Patch Changes

- [#547](https://github.com/luxurypresence/seo-automation/pull/547) [`5de6b58`](https://github.com/luxurypresence/seo-automation/commit/5de6b5827691ad5ee9cd76cc23061ccdfedc110e) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix reserved 'message' key warning in STAR API logs by renaming to 'responseMessage'

## 0.42.1

### Patch Changes

- [#545](https://github.com/luxurypresence/seo-automation/pull/545) [`233f11c`](https://github.com/luxurypresence/seo-automation/commit/233f11c68a5229ae1981eae6d6c21c2576e0c356) Thanks [@elisabethgross](https://github.com/elisabethgross)! - rename PublisherDaemon to GetSurfacedActions for clarity

## 0.42.0

### Minor Changes

- [#523](https://github.com/luxurypresence/seo-automation/pull/523) [`896686f`](https://github.com/luxurypresence/seo-automation/commit/896686fea93b177bf99c1d3115a74119832d169e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds a ContextLogger to easily add properties to all logs throughout a lambdas run

## 0.41.0

### Minor Changes

- [#542](https://github.com/luxurypresence/seo-automation/pull/542) [`cae9213`](https://github.com/luxurypresence/seo-automation/commit/cae9213ed9a05688dba5732115fa68db28c48810) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Enhanced CatchFailure logging and renamed PublisherDaemon lambda

## 0.40.0

### Minor Changes

- [#543](https://github.com/luxurypresence/seo-automation/pull/543) [`8e203de`](https://github.com/luxurypresence/seo-automation/commit/8e203dee11a824bae654ea1907350e8949903f28) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Exclude H1 header recommendations for homepage pages while continuing to provide meta title and description recommendations for all page types

## 0.39.0

### Minor Changes

- [#540](https://github.com/luxurypresence/seo-automation/pull/540) [`48a3efd`](https://github.com/luxurypresence/seo-automation/commit/48a3efde69ee70f54f88c7ef399ff37b87d019a0) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Turns on events for scheduler automation

## 0.38.1

### Patch Changes

- [#538](https://github.com/luxurypresence/seo-automation/pull/538) [`662cff0`](https://github.com/luxurypresence/seo-automation/commit/662cff0b004d55fcc9b68b01741acee4f3ca3f7e) Thanks [@diegosr90](https://github.com/diegosr90)! - Updates homepage-feed

  - Adds url to page object for FeedRecommendation
  - Updates fragment on scheduler

## 0.38.0

### Minor Changes

- [#539](https://github.com/luxurypresence/seo-automation/pull/539) [`ee226f4`](https://github.com/luxurypresence/seo-automation/commit/ee226f45f33ed14cecc49bfed88041c337058958) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add EventBridge rules for automated scheduling of lambdas and step functions

## 0.37.11

### Patch Changes

- [#535](https://github.com/luxurypresence/seo-automation/pull/535) [`93cd515`](https://github.com/luxurypresence/seo-automation/commit/93cd515f7beaa2893bbc92a58486e6c8820d2c6a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes fallback to contentPayload recommendation values on publish because the values aren't actually in the contentPayload, only the ID's are

## 0.37.10

### Patch Changes

- [#533](https://github.com/luxurypresence/seo-automation/pull/533) [`210cd21`](https://github.com/luxurypresence/seo-automation/commit/210cd21e6a6824b9c3574b1ee7573b26e43b0b79) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes double response body parse

## 0.37.9

### Patch Changes

- [#526](https://github.com/luxurypresence/seo-automation/pull/526) [`5df04ca`](https://github.com/luxurypresence/seo-automation/commit/5df04cae517f8bd9902d6fb7f4c2647fb0affc16) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix H1 text extraction and URL normalization issues

## 0.37.8

### Patch Changes

- [#527](https://github.com/luxurypresence/seo-automation/pull/527) [`820e382`](https://github.com/luxurypresence/seo-automation/commit/820e3827ae87b0e16625d61b4432459ae40a6676) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Temporarily disables keyword limit of 100 for rank queuer daemon

## 0.37.7

### Patch Changes

- [#524](https://github.com/luxurypresence/seo-automation/pull/524) [`d77e088`](https://github.com/luxurypresence/seo-automation/commit/d77e088a5c790bc7e0e2cdf020bc21e0679ae794) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fixes slack notifications for STAR API errors

## 0.37.6

### Patch Changes

- [#520](https://github.com/luxurypresence/seo-automation/pull/520) [`a62d9b0`](https://github.com/luxurypresence/seo-automation/commit/a62d9b05679a75e73faabf42f53acfdafaf0d2d7) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes the check for required env vars. Not all lambdas use all env vars so this is annoying for development

## 0.37.5

### Patch Changes

- [#516](https://github.com/luxurypresence/seo-automation/pull/516) [`250083b`](https://github.com/luxurypresence/seo-automation/commit/250083b3a865ee632f89df432d4469404f4a859a) Thanks [@diegosr90](https://github.com/diegosr90)! - Fixes email payload sent to notification-service, subject is required for the API, correct use for unsubscribe group id

## 0.37.4

### Patch Changes

- [#515](https://github.com/luxurypresence/seo-automation/pull/515) [`16fb9b9`](https://github.com/luxurypresence/seo-automation/commit/16fb9b99a71904d21c40d2f6cb3bfa6dd12ed6d9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds MockStarApiClient for testing

## 0.37.3

### Patch Changes

- [#511](https://github.com/luxurypresence/seo-automation/pull/511) [`c8ae671`](https://github.com/luxurypresence/seo-automation/commit/c8ae671bcb4b4f22e5d14e578ec94a146a4214a0) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds test util

## 0.37.2

### Patch Changes

- [#513](https://github.com/luxurypresence/seo-automation/pull/513) [`eea1f5f`](https://github.com/luxurypresence/seo-automation/commit/eea1f5f5507be4ab4e854cf9500d291d19b97c9d) Thanks [@diegosr90](https://github.com/diegosr90)! - Change casing for ASM sendgrid on Email data

## 0.37.1

### Patch Changes

- [#510](https://github.com/luxurypresence/seo-automation/pull/510) [`8978351`](https://github.com/luxurypresence/seo-automation/commit/8978351c5a0d5a53142fbc7c3a5971563575dd00) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Fix serverless deployment issues for development stage

  ### Changes

  #### Step Functions State Machine Fix

  - Fixed duplicate state name "Success" in `entitlementsStateMachine` definition
  - Renamed the success state inside the Map iterator from "Success" to "MapSuccess" to avoid naming conflicts
  - This resolves the CloudFormation deployment error: "DUPLICATE_STATE_NAMES: A state with this name already exists: Success"

## 0.37.0

### Minor Changes

- [#509](https://github.com/luxurypresence/seo-automation/pull/509) [`2e14e9c`](https://github.com/luxurypresence/seo-automation/commit/2e14e9cbbdfce30582ccc32b15298d1df7e02b63) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Update Publisher lambda to use contentPayload data and fetch latest recommendation status

  ### Changes

  #### Publisher Lambda Updates

  - Modified Publisher to use `StoreSEODraftResponse` data from `action.contentPayload` instead of fetching recommendations via `getRecommendationsByGroupId`
  - Added functionality to fetch individual recommendation statuses using new `getRecommendationById` method to ensure up-to-date status (handles cases where recommendations are rejected after contentPayload was saved)
  - Removed dependency on `groupScheduledActions` for recommendation filtering
  - Updated type imports to use `StoreSEODraftResponse` and `StoredRecommendation` types

  #### API Client Updates

  - Added `getRecommendationById` method to `ISeoApiGatewayClient` interface
  - Implemented `getRecommendationById` in `SeoApiGatewayClient` with full error handling and logging
  - Added mock implementation of `getRecommendationById` in `MockSeoApiGatewayClient`
  - Created new GraphQL query `GET_RECOMMENDATION_BY_ID` for fetching individual recommendations

  #### Test Updates

  - Updated all Publisher test cases to use `StoreSEODraftResponse` instead of `Recommendation`
  - Added mock setup for `getRecommendationById` method in all relevant test cases
  - Removed obsolete tests that relied on `groupScheduledActions` and `getRecommendationsByGroupId`
  - Added new test cases for:
    - Handling rejected recommendations when fetching latest status
    - Handling errors during recommendation status fetch
    - Handling status changes (e.g., PENDING to REJECTED) after contentPayload was saved
    - Handling null/empty savedRecommendations

  ### Benefits

  - Improved performance by eliminating redundant API calls when recommendation data is already available in contentPayload
  - Ensures recommendation status is always up-to-date by fetching individual statuses before publishing
  - Maintains backward compatibility while improving the data flow
  - Better error handling for individual recommendation status fetches

## 0.36.1

### Patch Changes

- [#507](https://github.com/luxurypresence/seo-automation/pull/507) [`1845732`](https://github.com/luxurypresence/seo-automation/commit/18457320de4c916dbbeb0c5279f3eb4926ae26de) Thanks [@jaycholland](https://github.com/jaycholland)! - Added latest Datadog dashboard export from STAR API additions

## 0.36.0

### Minor Changes

- [#505](https://github.com/luxurypresence/seo-automation/pull/505) [`d44eac3`](https://github.com/luxurypresence/seo-automation/commit/d44eac3cef8ec502b416f38b0dc1944eccc2ac26) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates publisher state machine error handling with catch steps

### Patch Changes

- [#505](https://github.com/luxurypresence/seo-automation/pull/505) [`d44eac3`](https://github.com/luxurypresence/seo-automation/commit/d44eac3cef8ec502b416f38b0dc1944eccc2ac26) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Better logs

## 0.35.0

### Minor Changes

- [#504](https://github.com/luxurypresence/seo-automation/pull/504) [`18812fb`](https://github.com/luxurypresence/seo-automation/commit/18812fbb8c02b4ec24b906fcaf0ae97ef1a882df) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates publisher state machine error handling with catch steps

## 0.34.0

### Minor Changes

- [#503](https://github.com/luxurypresence/seo-automation/pull/503) [`76b6f1c`](https://github.com/luxurypresence/seo-automation/commit/76b6f1cf077408091b164ca20db891c7c7994ba0) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds slack notifications for STAR API errors

## 0.33.0

### Minor Changes

- [#500](https://github.com/luxurypresence/seo-automation/pull/500) [`9cb7a91`](https://github.com/luxurypresence/seo-automation/commit/9cb7a91d85e4f290b1ae673bbdb6edbf4a97de0c) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Update recommendation status on publish

## 0.32.2

### Patch Changes

- [#497](https://github.com/luxurypresence/seo-automation/pull/497) [`0e9ee58`](https://github.com/luxurypresence/seo-automation/commit/0e9ee588044a69d55faa7ef2647df82a52e4a0cf) Thanks [@devinellis](https://github.com/devinellis)! - remove subject

## 0.32.1

### Patch Changes

- [#499](https://github.com/luxurypresence/seo-automation/pull/499) [`8eefe7c`](https://github.com/luxurypresence/seo-automation/commit/8eefe7c1ea02b177e5bb8a6327805639c344e3c9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds STAR_API_URL config var

## 0.32.0

### Minor Changes

- [#491](https://github.com/luxurypresence/seo-automation/pull/491) [`88169be`](https://github.com/luxurypresence/seo-automation/commit/88169be977273199e7914aa87e7c11d72d589574) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat: add Publisher lambda for updating website elements via STAR API

  - Add Publisher lambda that receives events from PublisherDaemon
  - Validates actions are in SURFACED status before publishing
  - Updates meta titles, meta descriptions, and H1 elements via STAR API
  - Marks actions as PUBLISHED only if all updates succeed
  - Handles partial updates when some original content is missing
  - Includes comprehensive error handling and logging
  - Add PublisherError class for publisher-specific errors
  - Add comprehensive test coverage with 13 test cases

## 0.31.1

### Patch Changes

- [#498](https://github.com/luxurypresence/seo-automation/pull/498) [`3fd3edd`](https://github.com/luxurypresence/seo-automation/commit/3fd3edd210f67ae852f77ed9adb3fd1fa48fc4dd) Thanks [@jaycholland](https://github.com/jaycholland)! - updated datadog dashboard json

## 0.31.0

### Minor Changes

- [#496](https://github.com/luxurypresence/seo-automation/pull/496) [`adb383b`](https://github.com/luxurypresence/seo-automation/commit/adb383bd92ba0949bf4bb64e87caf1376ce1a3ab) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - dd temporal filtering to EmailerDaemon - split email content items into pastItems and futureItems based on timestamps while preserving backward compatibility

## 0.30.1

### Patch Changes

- [#493](https://github.com/luxurypresence/seo-automation/pull/493) [`c7220d6`](https://github.com/luxurypresence/seo-automation/commit/c7220d6303022e4f95b49419ca2fc6b90282480f) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Changeset to fix version deploy issue

## 0.30.0

### Minor Changes

- [#490](https://github.com/luxurypresence/seo-automation/pull/490) [`7d23b8f`](https://github.com/luxurypresence/seo-automation/commit/7d23b8f76889b444922be148414a10df0599e590) Thanks [@elisabethgross](https://github.com/elisabethgross)! - feat: add STAR API client infrastructure for website element updates

  - Add StarApiClient to interact with STAR API /editor endpoint
  - Create StarApiError for proper error handling with status codes
  - Define IStarApiClient interface with StarApiPayload and StarApiResponse types
  - Add StarApiConfig to configuration system with STAR_API_URL environment variable
  - Integrate createStarApiClient method into UnifiedServiceFactory
  - Add comprehensive test coverage for all components
  - Support updating meta titles, meta descriptions, and H1 elements via STAR API

## 0.29.3

### Patch Changes

- [#488](https://github.com/luxurypresence/seo-automation/pull/488) [`33887bf`](https://github.com/luxurypresence/seo-automation/commit/33887bfdeb28a1cad058e732737b3b3d1c6518b5) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Better logs

## 0.29.2

### Patch Changes

- [#481](https://github.com/luxurypresence/seo-automation/pull/481) [`149c4b5`](https://github.com/luxurypresence/seo-automation/commit/149c4b5144a003e9e47dec240934d912c4d2d76d) Thanks [@devinellis](https://github.com/devinellis)! - Fix email subject line

## 0.29.1

### Patch Changes

- [#482](https://github.com/luxurypresence/seo-automation/pull/482) [`9c6a753`](https://github.com/luxurypresence/seo-automation/commit/9c6a75397a1a662c8787e55f8cee30e166caa165) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates DATAFORSEO_POSTBACK_URL

## 0.29.0

### Minor Changes

- [#479](https://github.com/luxurypresence/seo-automation/pull/479) [`d795828`](https://github.com/luxurypresence/seo-automation/commit/d795828399f116daba91fbb0d62e7dad6e243f5e) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add domainNoLink field to email payload to prevent Gmail hyperlinking

## 0.28.0

### Minor Changes

- [#476](https://github.com/luxurypresence/seo-automation/pull/476) [`7c8954e`](https://github.com/luxurypresence/seo-automation/commit/7c8954e4e0387b928ce926b6da4b3497fdc7d4a5) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add optional companyIds and emails parameters to EmailerDaemon for test email functionalityAdd optional companyIds and emails parameters to EmailerDaemon for test email functionality

## 0.27.2

### Patch Changes

- [#475](https://github.com/luxurypresence/seo-automation/pull/475) [`a7e478e`](https://github.com/luxurypresence/seo-automation/commit/a7e478e9bf1b12e0cd06c11f1541b68471829d23) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-485: Fixes env variables for langfuse to work properly and send traces.

## 0.27.1

### Patch Changes

- [#456](https://github.com/luxurypresence/seo-automation/pull/456) [`c84a821`](https://github.com/luxurypresence/seo-automation/commit/c84a821fd1a7ae432841335a8fe72b0747a28c0c) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates ApiResponse s3 bucket config

## 0.27.0

### Minor Changes

- [#452](https://github.com/luxurypresence/seo-automation/pull/452) [`ac08c1f`](https://github.com/luxurypresence/seo-automation/commit/ac08c1f3b04561a2b9370853b390ab41f6e4bc57) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Check rank for all surfaced / published groups

## 0.26.0

### Minor Changes

- [#464](https://github.com/luxurypresence/seo-automation/pull/464) [`d77c5f5`](https://github.com/luxurypresence/seo-automation/commit/d77c5f5706ecf153a7587cde802459502279c134) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add media URL fields to homepageFeed GraphQL fragments

  Added media subfields (thumbnailUrl, smallUrl, mediumUrl) to both FeedBlogPost and FeedRecommendationGroup fragments to enable email lambda to fetch media URLs for homepage feed content.

## 0.25.0

### Minor Changes

- [#462](https://github.com/luxurypresence/seo-automation/pull/462) [`9a092a7`](https://github.com/luxurypresence/seo-automation/commit/9a092a77329eff213a54935a8a7146c967ede754) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add title validation to scraper lambda - fails when firecrawl returns empty title, includes comprehensive logging

## 0.24.0

### Minor Changes

- [#455](https://github.com/luxurypresence/seo-automation/pull/455) [`a19c8fe`](https://github.com/luxurypresence/seo-automation/commit/a19c8fe5b42279a6e6b5abd001c4a2ec5fd4a8fc) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix bucket name

## 0.23.4

### Patch Changes

- [#451](https://github.com/luxurypresence/seo-automation/pull/451) [`7f88799`](https://github.com/luxurypresence/seo-automation/commit/7f887994a3a163b5a713db72d74d3fb5476ef2f6) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-480: Adds bcc support for emails

## 0.23.3

### Patch Changes

- [#453](https://github.com/luxurypresence/seo-automation/pull/453) [`6e28d1d`](https://github.com/luxurypresence/seo-automation/commit/6e28d1db67c6454f84d81dd04398095f27eac693) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add surfacedAt to updateScheduledAction mutation

## 0.23.2

### Patch Changes

- [#450](https://github.com/luxurypresence/seo-automation/pull/450) [`da8a34a`](https://github.com/luxurypresence/seo-automation/commit/da8a34a12d997d763cb1efe4b0fadc33763ec22e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates all hardcoded llm model names

## 0.23.1

### Patch Changes

- [#448](https://github.com/luxurypresence/seo-automation/pull/448) [`64b9a09`](https://github.com/luxurypresence/seo-automation/commit/64b9a090a8e62b554e6d5cca42d7aa811b95a48e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Change to-be-published date to 2 days from surfacing

## 0.23.0

### Minor Changes

- [#368](https://github.com/luxurypresence/seo-automation/pull/368) [`6dedf18`](https://github.com/luxurypresence/seo-automation/commit/6dedf18bdaf6d8a4efd09200903bcb62e46b3b5d) Thanks [@devinellis](https://github.com/devinellis)! - Adds ranking checker and webhook

## 0.22.0

### Minor Changes

- [#439](https://github.com/luxurypresence/seo-automation/pull/439) [`cdd4aab`](https://github.com/luxurypresence/seo-automation/commit/cdd4aab1756d4c75a7cb15cc7c21b3dda75b917b) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-446: Adds Emailer Daemon.

## 0.21.1

### Patch Changes

- [#441](https://github.com/luxurypresence/seo-automation/pull/441) [`d859f7a`](https://github.com/luxurypresence/seo-automation/commit/d859f7a3888cd449893a8853a8e355027669bd7a) Thanks [@devinellis](https://github.com/devinellis)! - Update default openai model

- [#440](https://github.com/luxurypresence/seo-automation/pull/440) [`a3eb60e`](https://github.com/luxurypresence/seo-automation/commit/a3eb60ea9ebd5ee27173a14deafbae36a0dee4dc) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Tweak Scraper to not retry a page already processed

## 0.21.0

### Minor Changes

- [#433](https://github.com/luxurypresence/seo-automation/pull/433) [`7730742`](https://github.com/luxurypresence/seo-automation/commit/7730742cec6dc71fdce6f480f99eeb8cb1041364) Thanks [@jaycholland](https://github.com/jaycholland)! - Added Datadog dashboard json

## 0.20.0

### Minor Changes

- [#435](https://github.com/luxurypresence/seo-automation/pull/435) [`937fcba`](https://github.com/luxurypresence/seo-automation/commit/937fcba5b3c892ecddb7ae08b290f73fbbaa6a27) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Set units to 4 for SEO regardless of what entitlement says

## 0.19.2

### Patch Changes

- [#434](https://github.com/luxurypresence/seo-automation/pull/434) [`2277513`](https://github.com/luxurypresence/seo-automation/commit/2277513edb1b945f0dbbb58250f16a1ddd12ad5e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Bump up the attempts/IntervalSeconds of ScrapeWebPages

## 0.19.1

### Patch Changes

- [#431](https://github.com/luxurypresence/seo-automation/pull/431) [`ba7b8fb`](https://github.com/luxurypresence/seo-automation/commit/ba7b8fb727cda1586f69efa248ac57dc83ad4d3d) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix upcoming actions query (add missing result field)

## 0.19.0

### Minor Changes

- [#414](https://github.com/luxurypresence/seo-automation/pull/414) [`f808675`](https://github.com/luxurypresence/seo-automation/commit/f808675b97eac5a627e96dc41098df0209133d11) Thanks [@jaycholland](https://github.com/jaycholland)! - serverless config to package each function individually

## 0.18.7

### Patch Changes

- [#428](https://github.com/luxurypresence/seo-automation/pull/428) [`cc8119c`](https://github.com/luxurypresence/seo-automation/commit/cc8119c39269caac38c8d7fe3fe5c808d4ea4fd9) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix UUID mismatch in screenshot storage - use firecrawlId for mediaId

## 0.18.6

### Patch Changes

- [#429](https://github.com/luxurypresence/seo-automation/pull/429) [`8c645d0`](https://github.com/luxurypresence/seo-automation/commit/8c645d08455504e1b3b9a77495f2c42df2743844) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Refactors types to ensure clarity and consistency

## 0.18.5

### Patch Changes

- [#416](https://github.com/luxurypresence/seo-automation/pull/416) [`612bd1c`](https://github.com/luxurypresence/seo-automation/commit/612bd1c301aad4daa546f295b270d47c864fdeaf) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix createOwedActions

## 0.18.4

### Patch Changes

- [#413](https://github.com/luxurypresence/seo-automation/pull/413) [`4c61a7b`](https://github.com/luxurypresence/seo-automation/commit/4c61a7bf06cf136932cd5d4315292dbdf29fd2c6) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix serverless to wrapInput as expected

## 0.18.3

### Patch Changes

- [#405](https://github.com/luxurypresence/seo-automation/pull/405) [`a03f32a`](https://github.com/luxurypresence/seo-automation/commit/a03f32a36a307cc4b1fc9e95ebbbe6de82ee70d8) Thanks [@diegosr90](https://github.com/diegosr90)! - Adds Surfacer Daemon lambda definition to serverless

## 0.18.2

### Patch Changes

- [#409](https://github.com/luxurypresence/seo-automation/pull/409) [`040fa2a`](https://github.com/luxurypresence/seo-automation/commit/040fa2aa87bde969de606ea8225eab7f5f670696) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add end state to entitlements state machine

## 0.18.1

### Patch Changes

- [#408](https://github.com/luxurypresence/seo-automation/pull/408) [`728b0e6`](https://github.com/luxurypresence/seo-automation/commit/728b0e6709bbb643bf5908a84256c5d9d984dd18) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix GraphQLClient URL for api-gateway

## 0.18.0

### Minor Changes

- [#407](https://github.com/luxurypresence/seo-automation/pull/407) [`614fe46`](https://github.com/luxurypresence/seo-automation/commit/614fe4697c9c68d2a92c7a3fe23582dd19e39e42) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Set up proper fan-out for state machine; make the output, input consistent

## 0.17.1

### Patch Changes

- [#406](https://github.com/luxurypresence/seo-automation/pull/406) [`a4b863d`](https://github.com/luxurypresence/seo-automation/commit/a4b863d3c0c4786189c228c44eb49f75d7000f17) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix entitlement ID in mapper and serverless config

## 0.17.0

### Minor Changes

- [#404](https://github.com/luxurypresence/seo-automation/pull/404) [`61a9109`](https://github.com/luxurypresence/seo-automation/commit/61a91095ab0fc951f1822dc342cc62d8c36941f7) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add State Machine for entitlements

## 0.16.1

### Patch Changes

- [#402](https://github.com/luxurypresence/seo-automation/pull/402) [`ac5ab63`](https://github.com/luxurypresence/seo-automation/commit/ac5ab63c73d9819a6e12ab52bf2c39113c85f43e) Thanks [@elisabethgross](https://github.com/elisabethgross)! - New seeders, updated triggers

## 0.16.0

### Minor Changes

- [#393](https://github.com/luxurypresence/seo-automation/pull/393) [`2fd5f50`](https://github.com/luxurypresence/seo-automation/commit/2fd5f500ecba453447e27424a6f06aacd8feee2c) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-365: Updates StoreSEODraft lambda

## 0.15.1

### Patch Changes

- [#400](https://github.com/luxurypresence/seo-automation/pull/400) [`01efbf5`](https://github.com/luxurypresence/seo-automation/commit/01efbf5f1aca7afdde1b228f712eb11be04c668a) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Set versionFunction to false

## 0.15.0

### Minor Changes

- [#397](https://github.com/luxurypresence/seo-automation/pull/397) [`5669d13`](https://github.com/luxurypresence/seo-automation/commit/5669d13cec3630453f456288e13f915f111601d3) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Get element tag and index

## 0.14.1

### Patch Changes

- [#398](https://github.com/luxurypresence/seo-automation/pull/398) [`ebcd30b`](https://github.com/luxurypresence/seo-automation/commit/ebcd30b8de44b3a9e3630d3c662efcccde5bba23) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix Scraper ExpectedReults

## 0.14.0

### Minor Changes

- [#395](https://github.com/luxurypresence/seo-automation/pull/395) [`b55c749`](https://github.com/luxurypresence/seo-automation/commit/b55c74938156e6c5b98d96d027c223763992b847) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Rename page to ScrapedPage and rename ScrapedPage to ScraperResult

## 0.13.0

### Minor Changes

- [#347](https://github.com/luxurypresence/seo-automation/pull/347) [`00184d1`](https://github.com/luxurypresence/seo-automation/commit/00184d120974aa562921f40ebfc9eee347dd73b7) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-428: Adds Surfacer lambda

## 0.12.18

### Patch Changes

- [#394](https://github.com/luxurypresence/seo-automation/pull/394) [`1c6013e`](https://github.com/luxurypresence/seo-automation/commit/1c6013e2895d86af4022389f06940b424d7e9e03) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix sfnPayload bug and failureReason issue

## 0.12.17

### Patch Changes

- [#391](https://github.com/luxurypresence/seo-automation/pull/391) [`a444079`](https://github.com/luxurypresence/seo-automation/commit/a444079097ad95cc812360e30303d5b621429063) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add OpenAI-API key to config/serverless

## 0.12.16

### Patch Changes

- [#390](https://github.com/luxurypresence/seo-automation/pull/390) [`401a454`](https://github.com/luxurypresence/seo-automation/commit/401a45496c8c9e727486270295e36d69a9b3e72c) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify types for graphql call to updateScheduledAction

## 0.12.15

### Patch Changes

- [#384](https://github.com/luxurypresence/seo-automation/pull/384) [`a24fe0d`](https://github.com/luxurypresence/seo-automation/commit/a24fe0d09d50394d4b3bf536ddb600b0f79e98d6) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Don't make Status required in UpdateScheduledAction graphql call

## 0.12.14

### Patch Changes

- [#383](https://github.com/luxurypresence/seo-automation/pull/383) [`18f3aec`](https://github.com/luxurypresence/seo-automation/commit/18f3aecef7a050dbccbca5c81f3c06db5b0055de) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix bucket-name env-var

## 0.12.13

### Patch Changes

- [#381](https://github.com/luxurypresence/seo-automation/pull/381) [`afe985d`](https://github.com/luxurypresence/seo-automation/commit/afe985dc28a83331e66287393c6fd2f461c88537) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix issue with mainHeading

## 0.12.12

### Patch Changes

- [#380](https://github.com/luxurypresence/seo-automation/pull/380) [`c091868`](https://github.com/luxurypresence/seo-automation/commit/c0918688cc3125152b3577163afa0971f54eb6d6) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix cosmo URL

## 0.12.11

### Patch Changes

- [#379](https://github.com/luxurypresence/seo-automation/pull/379) [`84950f5`](https://github.com/luxurypresence/seo-automation/commit/84950f52201ae544c796c09c704b52435cdb6976) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Don't require ARNS in config

## 0.12.10

### Patch Changes

- [#378](https://github.com/luxurypresence/seo-automation/pull/378) [`79ddc32`](https://github.com/luxurypresence/seo-automation/commit/79ddc32ac71db47caeb1be88574c5ec39728bc80) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix Step Functions retry configuration - remove specific Lambda error types, keep only States.ALL to resolve AWS schema validation errors

## 0.12.9

### Patch Changes

- [#377](https://github.com/luxurypresence/seo-automation/pull/377) [`83247f4`](https://github.com/luxurypresence/seo-automation/commit/83247f4a7d848439287884d80aafb6a80320b788) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix circular dependency in step functions configuration

## 0.12.8

### Patch Changes

- [#376](https://github.com/luxurypresence/seo-automation/pull/376) [`194f748`](https://github.com/luxurypresence/seo-automation/commit/194f748279186d08ae819fc91f18418dfcc789fe) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Stupidly didn't save merge changes

## 0.12.7

### Patch Changes

- [#375](https://github.com/luxurypresence/seo-automation/pull/375) [`a7a2730`](https://github.com/luxurypresence/seo-automation/commit/a7a273017f04309cd6453818f5e7383241286536) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix CloudFormation variable syntax in step functions - change ${aws:region}/${aws:accountId} to ${AWS::Region}/${AWS::AccountId} to resolve deployment errors

## 0.12.6

### Patch Changes

- [#374](https://github.com/luxurypresence/seo-automation/pull/374) [`672bfbe`](https://github.com/luxurypresence/seo-automation/commit/672bfbed8eb74c853902e39cea8f92aabf4d34d7) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix serverless.yml: Add missing lambda function definitions for step functions

## 0.12.5

### Patch Changes

- [#373](https://github.com/luxurypresence/seo-automation/pull/373) [`3bf923b`](https://github.com/luxurypresence/seo-automation/commit/3bf923b193e82854be2f301d10cae8a19ab61ee8) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix indent issue in serverless

## 0.12.4

### Patch Changes

- [#372](https://github.com/luxurypresence/seo-automation/pull/372) [`2a215a1`](https://github.com/luxurypresence/seo-automation/commit/2a215a1613212292cf796aae782f011a24f3b5f7) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Another fix attempt

## 0.12.3

### Patch Changes

- [#371](https://github.com/luxurypresence/seo-automation/pull/371) [`ba45e7b`](https://github.com/luxurypresence/seo-automation/commit/ba45e7bf7d122734c27131b2ffc3524bb0c52188) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add execution role for step function

## 0.12.2

### Patch Changes

- [#370](https://github.com/luxurypresence/seo-automation/pull/370) [`4488bba`](https://github.com/luxurypresence/seo-automation/commit/4488bba2a3cc4a40679e966b690609995580966d) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix how we get the ARN attribute of the state machine

## 0.12.1

### Patch Changes

- [#369](https://github.com/luxurypresence/seo-automation/pull/369) [`bd5b210`](https://github.com/luxurypresence/seo-automation/commit/bd5b210ba4737f054dfa09038f14f562be7ce3ba) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix serverless to explicitly note that draft-action-consumer-daemon depends on seoKeywordsStateMachine

## 0.12.0

### Minor Changes

- [#367](https://github.com/luxurypresence/seo-automation/pull/367) [`45c5229`](https://github.com/luxurypresence/seo-automation/commit/45c52294e02e8b9280cecf78339b4952cd2d1a3b) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Put SEO stuff in step function

## 0.11.0

### Minor Changes

- [#365](https://github.com/luxurypresence/seo-automation/pull/365) [`3a7d527`](https://github.com/luxurypresence/seo-automation/commit/3a7d527239a5d38fae68a1a08d72da3459cbdc74) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify error handling to throw errors

## 0.10.0

### Minor Changes

- [#364](https://github.com/luxurypresence/seo-automation/pull/364) [`adc3d0f`](https://github.com/luxurypresence/seo-automation/commit/adc3d0f76218b886243af9e722c057ec51f24fc6) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Standardize inputs and outputs

## 0.9.0

### Minor Changes

- [#362](https://github.com/luxurypresence/seo-automation/pull/362) [`33e1374`](https://github.com/luxurypresence/seo-automation/commit/33e1374f9b1df75e5ded48c040c29135fad51081) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify RecommendationGenerator to set contentPayload (and modify StoreDraft to not set contentPayload or generationPayload)

## 0.8.0

### Minor Changes

- [#358](https://github.com/luxurypresence/seo-automation/pull/358) [`600458b`](https://github.com/luxurypresence/seo-automation/commit/600458b51cfc983a46805ea8f22e2c0d9365626a) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Have RecommendationGenerator check and update scheduledAction

## 0.7.0

### Minor Changes

- [#357](https://github.com/luxurypresence/seo-automation/pull/357) [`fd796eb`](https://github.com/luxurypresence/seo-automation/commit/fd796ebf00fb419f494492f22438ee58220ff68a) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Have KeywordExtractor check and update scheduledAction

## 0.6.0

### Minor Changes

- [#356](https://github.com/luxurypresence/seo-automation/pull/356) [`e8025ae`](https://github.com/luxurypresence/seo-automation/commit/e8025ae93866b8691c954a67b9af971417bf412a) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Add PublisherDaemon lambda for automated content publishing

  This release introduces a new PublisherDaemon lambda that automatically processes surfaced actions ready for publishing. The daemon runs on weekdays and uses a fan-out pattern to invoke the publishing lambda for each action.

  ### Features Added

  - **PublisherDaemon Lambda**: New scheduled lambda that queries SURFACED actions with `scheduled_to_be_applied_at` ≤ current date
  - **Fan-out Pattern**: Processes actions in configurable chunks with exponential backoff to handle rate limits
  - **Weekday Scheduling**: Configured to run every weekday at 9 AM UTC via cron expression
  - **GraphQL Support**: Added `surfacedActions` query and resolver to client-marketing-service
  - **Configuration Management**: Added validation for chunk size and publishing lambda ARN
  - **Comprehensive Testing**: Full test coverage for all new components and scenarios

  ### Technical Details

  - Added `getSurfacedActions` method to API client and service layers
  - Implemented chunked processing with configurable `actionChunkSize`
  - Added proper error handling and logging throughout the fan-out process
  - Extended configuration interfaces to support new lambda requirements
  - Updated serverless.yml with weekday cron schedule (0 9 ? _ MON-FRI _)

  ### Breaking Changes

  None. This is a purely additive feature that doesn't modify existing functionality.

## 0.5.5

### Patch Changes

- [#361](https://github.com/luxurypresence/seo-automation/pull/361) [`7002b61`](https://github.com/luxurypresence/seo-automation/commit/7002b61e9ecb389219d9fd260e8dc9aa914b8b5e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Remove langfuse keys from 0-level of config

## 0.5.4

### Patch Changes

- [#349](https://github.com/luxurypresence/seo-automation/pull/349) [`954df2c`](https://github.com/luxurypresence/seo-automation/commit/954df2c5e082576e489cd391dade91b042308edf) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Create checkAction functions; Add to Scraper lambda

## 0.5.3

### Patch Changes

- [#348](https://github.com/luxurypresence/seo-automation/pull/348) [`6dab3b6`](https://github.com/luxurypresence/seo-automation/commit/6dab3b6fefe7bd3869ed557b038957b8ffad1c59) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Have lambdas pass action_id to each other

## 0.5.2

### Patch Changes

- [#343](https://github.com/luxurypresence/seo-automation/pull/343) [`811eea5`](https://github.com/luxurypresence/seo-automation/commit/811eea5e8039e58dd1f768cccb42eef03b87feaf) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds websiteUrls to draft pending scheduled actions

## 0.5.1

### Patch Changes

- [#342](https://github.com/luxurypresence/seo-automation/pull/342) [`f02b8f9`](https://github.com/luxurypresence/seo-automation/commit/f02b8f93b9cf10fe88a97bee19a81d5e55c157f4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Updates ApiGatewayClient to SeoApiGatewayClient

## 0.5.0

### Minor Changes

- [#341](https://github.com/luxurypresence/seo-automation/pull/341) [`d0aa8c6`](https://github.com/luxurypresence/seo-automation/commit/d0aa8c631a7ee63805dbb36266ca4f7a698c4bba) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify DraftActionConsumer to update executionName and executionArn

- [#341](https://github.com/luxurypresence/seo-automation/pull/341) [`d0aa8c6`](https://github.com/luxurypresence/seo-automation/commit/d0aa8c631a7ee63805dbb36266ca4f7a698c4bba) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Create CatchFailure lambda

## 0.4.0

### Minor Changes

- [#340](https://github.com/luxurypresence/seo-automation/pull/340) [`cec123a`](https://github.com/luxurypresence/seo-automation/commit/cec123a738ec24f0a01e606ce096debb81b4e11a) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify DraftActionConsumer to update executionName and executionArn

## 0.3.21

### Patch Changes

- [#333](https://github.com/luxurypresence/seo-automation/pull/333) [`3977bef`](https://github.com/luxurypresence/seo-automation/commit/3977befc464fab4008143f741c41ab3d79a9e25e) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-368: Adds StoreDraft lambda. ScheduledToBeSurfacedAt and ScheduledToBePublishedAt fields added to mutation

## 0.3.20

### Patch Changes

- [#335](https://github.com/luxurypresence/seo-automation/pull/335) [`c661b93`](https://github.com/luxurypresence/seo-automation/commit/c661b93668399caf89a393c280a92ab1c2b6f89c) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix Screenshot env variables

## 0.3.19

### Patch Changes

- [#332](https://github.com/luxurypresence/seo-automation/pull/332) [`9664532`](https://github.com/luxurypresence/seo-automation/commit/9664532d8e701dae502ec276c52aec63e6983df9) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Removes deprecated packages

## 0.3.18

### Patch Changes

- [#317](https://github.com/luxurypresence/seo-automation/pull/317) [`34a0810`](https://github.com/luxurypresence/seo-automation/commit/34a0810e29facc408a39118119bcf780422a7dc4) Thanks [@elisabethgross](https://github.com/elisabethgross)! - Adds Recommendation gen lambda

## 0.3.17

### Patch Changes

- [#331](https://github.com/luxurypresence/seo-automation/pull/331) [`365fe62`](https://github.com/luxurypresence/seo-automation/commit/365fe6275669bb25cacf354ffa7ed7d5dfc2e00e) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Add M2M_SUPER_API_KEY to config-template/serverless; point stuff to client-markerting-service vault

## 0.3.16

### Patch Changes

- [#282](https://github.com/luxurypresence/seo-automation/pull/282) [`53c272d`](https://github.com/luxurypresence/seo-automation/commit/53c272d96b3c206666fbacf57e358ba806cfc350) Thanks [@devinellis](https://github.com/devinellis)! - added Scraper lambda

## 0.3.15

### Patch Changes

- [#315](https://github.com/luxurypresence/seo-automation/pull/315) [`cc74c8b`](https://github.com/luxurypresence/seo-automation/commit/cc74c8bf16983db575b1ea5be649443aa03f74a0) Thanks [@diegosr90](https://github.com/diegosr90)! - SEO-366: Adding keyword extractor from page markdown lambda

## 0.3.14

### Patch Changes

- [#312](https://github.com/luxurypresence/seo-automation/pull/312) [`1d41464`](https://github.com/luxurypresence/seo-automation/commit/1d4146468ae2e46403a735967bac3fb715f0f921) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Fix serverless conditionals

## 0.3.13

### Patch Changes

- [#293](https://github.com/luxurypresence/seo-automation/pull/293) [`7db869e`](https://github.com/luxurypresence/seo-automation/commit/7db869e8d7e3f53ef189b4ae954c93d6f0135f9c) Thanks [@elisabethgross](https://github.com/elisabethgross)! - - Adds mock service factory for testing external APIs in the AWS sandbox environment
  - Adds test utilities

## 0.3.12

### Patch Changes

- [#301](https://github.com/luxurypresence/seo-automation/pull/301) [`49608a6`](https://github.com/luxurypresence/seo-automation/commit/49608a6574aab15d58376b4bd040b98f4d08fdbb) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Avoid installing husky in Dockerfile

## 0.3.11

### Patch Changes

- 8880d05: fix: extend typeroots for output

## 0.3.10

### Patch Changes

- 4f0f6bf: Remove prod flag for pnpm deploy call

## 0.3.9

### Patch Changes

- 55a8f80: Add excludeDevDependencies to serverless

## 0.3.8

### Patch Changes

- 14a99af: Upgrade to serverless v3.40

## 0.3.7

### Patch Changes

- 3a13bd3: Switch serverless to output folder

## 0.3.6

### Patch Changes

- 0f96c47: Fix order of operations Dockerfile

## 0.3.5

### Patch Changes

- 40e1d08: Remove events breaking serverless

## 0.3.4

### Patch Changes

- 1f05347: Make config-template-yaml consistemt with serverless (both sides)

## 0.3.3

### Patch Changes

- a14accd: Fix Dockerfile "run deploy"

## 0.3.2

### Patch Changes

- 41a1fd1: Fix Dockerfile to work with pnpm

## 0.3.1

### Patch Changes

- 0bb7d68: Fix Dockerfile

## 0.3.0

### Minor Changes

- 94aeb7c: Add CI/CD and deployment
- e483704: - adds query to fetch upcoming scheduled actions;
  - adds mutation to create owed scheduled actions;
  - adds function to identify and create actions based on entitlement.

### Patch Changes

- e3b4df9: Fix serverless issues

## 0.2.1

### Patch Changes

- d9ae1f4: Removes log tests

## 0.2.0

### Minor Changes

- fd58f60: - Adds DraftActionConsumerDaemon
  - Refactors code structure for consistent logging, errors, client, service, etc patterns

## 0.1.0

### Minor Changes

- 20a9fba: SEO-343: Adds DraftActionScheduler lambda

## 0.0.2

### Patch Changes

- 6045945: Create package for ai-marketing-scheduler
