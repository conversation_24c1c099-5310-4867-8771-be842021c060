# AI Marketing Scheduler

## Package Purpose

Orchestrates AI-powered SEO content generation using AWS Lambda and Step Functions.

## Architecture Patterns

### Factory Pattern with Dependency Injection

- Use interface-based design (e.g., `ISeoApiGatewayClient`)
- Place mock implementations alongside real ones
- Use factory pattern for service creation

### Mock Mode Development

For local development without VPC access:

```bash
USE_MOCK_CLIENTS=true pnpm test
```

### Step Functions Workflow Orchestration

- Complex pipelines managed through AWS Step Functions
- Event-driven processing architecture

## Key Files and Patterns

### Test Utilities

- Centralized test utilities in `src/utils/testUtils.ts`
- Mock external services for unit tests
- Test error scenarios explicitly

### Logging Pattern

Use the new context logger pattern:

- `src/logger/contextLogger.ts`
- `docs/context-logger-pattern.md`
- `docs/contextLogger.examples.ts`

## Development Commands

```bash
# Run Lambda locally (first import handler in src/run.ts)
pnpm run:lambda

# Test with mocks
USE_MOCK_CLIENTS=true pnpm test

# Deploy (requires AWS credentials)
AWS_PROFILE=your-profile pnpm deploy
```

## Testing Conventions

- Write tests alongside implementation files
- Use centralized test utilities
- Mock external services for unit tests
- Never add test cases for logging statements
- Test error scenarios explicitly

## Environment Configuration

- AWS resources (Step Functions, S3 buckets)
- API endpoints (GraphQL, CMS, Tenant Service)
- Authentication keys (stored in vault)
- Feature flags (LaunchDarkly)
- Mock mode: `USE_MOCK_CLIENTS=true` for local development
