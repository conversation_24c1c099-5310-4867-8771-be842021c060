'use strict';

/* eslint-disable @typescript-eslint/no-require-imports */
const { S3Client, HeadBucketCommand } = require('@aws-sdk/client-s3');

/**
 * Conditionally create S3 bucket resources
 * Checks if bucket exists before attempting to create it in CloudFormation
 *
 * Note: This runs at deployment time during serverless packaging
 */
module.exports = async ({ resolveVariable }) => {
  // Get the stage from serverless
  const stage = await resolveVariable('self:provider.stage');
  const bucketName = `brand-profile-generator-${stage}`;

  console.log(`Checking S3 bucket: ${bucketName}`);

  // Skip bucket management for local development
  if (process.env.SKIP_BUCKET_CREATION === 'true') {
    console.log('Skipping bucket creation (SKIP_BUCKET_CREATION=true)');
    return {
      Resources: {},
    };
  }

  // Always check if bucket exists for all environments
  let bucketExists = false;
  try {
    const client = new S3Client({ region: 'us-east-1' });
    await client.send(new HeadBucketCommand({ Bucket: bucketName }));
    bucketExists = true;
    console.log(`Bucket ${bucketName} already exists`);
  } catch (error) {
    if (error.name === 'NotFound' || error.name === 'NoSuchBucket') {
      console.log(`Bucket ${bucketName} does not exist, will create it`);
      bucketExists = false;
    } else if (error.name === 'Forbidden' || error.name === 'AccessDenied') {
      // If we can't check, assume it exists (common in CI/CD with limited permissions)
      console.log(
        `Cannot check bucket ${bucketName} (insufficient permissions), assuming it exists`,
      );
      bucketExists = true;
    } else {
      console.error(`Error checking bucket ${bucketName}:`, error.message);
      // Assume it exists to avoid creation failures
      bucketExists = true;
    }
  }

  // If bucket exists, don't create it in CloudFormation
  if (bucketExists) {
    return {
      Resources: {},
      Outputs: {
        BrandProfileGeneratorBucketName: {
          Description: 'Name of the S3 bucket for brand profiles (existing)',
          Value: bucketName,
          Export: {
            Name: {
              'Fn::Sub': '${AWS::StackName}-BucketName',
            },
          },
        },
      },
    };
  }

  // Bucket doesn't exist, create it
  console.log(`Creating S3 bucket resource in CloudFormation`);
  return {
    Resources: {
      BrandProfileGeneratorBucket: {
        Type: 'AWS::S3::Bucket',
        DeletionPolicy: 'Retain', // Prevent accidental deletion
        UpdateReplacePolicy: 'Retain', // Prevent replacement on stack updates
        Properties: {
          BucketName: bucketName,
          VersioningConfiguration: {
            Status: 'Enabled',
          },
          PublicAccessBlockConfiguration: {
            BlockPublicAcls: true,
            BlockPublicPolicy: true,
            IgnorePublicAcls: true,
            RestrictPublicBuckets: true,
          },
          BucketEncryption: {
            ServerSideEncryptionConfiguration: [
              {
                ServerSideEncryptionByDefault: {
                  SSEAlgorithm: 'AES256',
                },
              },
            ],
          },
          LifecycleConfiguration: {
            Rules: [
              {
                Id: 'DeleteOldVersions',
                Status: 'Enabled',
                NoncurrentVersionExpiration: {
                  NoncurrentDays: 30,
                },
              },
            ],
          },
        },
      },
    },
    Outputs: {
      BrandProfileGeneratorBucketName: {
        Description: 'Name of the S3 bucket for brand profiles',
        Value: {
          Ref: 'BrandProfileGeneratorBucket',
        },
        Export: {
          Name: {
            'Fn::Sub': '${AWS::StackName}-BucketName',
          },
        },
      },
    },
  };
};
