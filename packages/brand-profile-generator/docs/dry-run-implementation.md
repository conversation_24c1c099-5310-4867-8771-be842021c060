# Dry Run Mode Implementation

## Overview
The Brand Profile Generator Step Function now supports a `dryRun` parameter that controls whether the execution performs real external API calls and database writes or runs in a safe test mode.

## Input Parameters

### Step Function Input
```typescript
{
  // Optional array of company IDs to process (bypasses entitlement check)
  companyIds?: string[];
  
  // Whether to regenerate existing profiles (default: false)
  regenerateIfExisting?: boolean;
  
  // Controls dry run vs production mode (default: true for safety)
  dryRun?: boolean;
}
```

### Key Changes
- **Replaced**: `saveToDatabase` parameter
- **With**: `dryRun` parameter (inverted logic for clarity)
- **Default**: `true` (safe mode by default)

## Behavior by Mode

### Dry Run Mode (`dryRun: true` - DEFAULT)
- ✅ **S3 Writes**: Always writes outputs to S3 for debugging/evaluation
- ❌ **Database Writes**: Skipped completely
- ❌ **External API Calls**: No calls to OpenAI, LangFuse, or other services
- 🎭 **Mock Data**: Uses placeholder/mock responses prefixed with `[DRY RUN]`
- 💰 **Cost**: Zero API costs
- ⚡ **Speed**: no waiting for API responses

### Production Mode (`dryRun: false`)
- ✅ **S3 Writes**: Outputs written to S3
- ✅ **Database Writes**: Brand profiles saved to database
- ✅ **External API Calls**: Real calls to OpenAI Deep Research, synthesis, etc.
- 📊 **Real Data**: Actual brand research and profile generation
- 💰 **Cost**: Normal API usage charges apply
- ⏱️ **Speed**: Depends on API response times (polling for research completion)

## Lambda Function Changes

### 1. list-entitled-urls.ts
- Accepts `dryRun` parameter
- Passes it through to downstream lambdas
- Default: `true` if not specified

### 2. brand-research.ts
- In dry run: Returns mock research request ID immediately
- In production: Would call OpenAI Deep Research API

### 3. get-research-job-status.ts
- In dry run: Returns mock research data immediately (no polling)
- In production: Would poll OpenAI API for research completion

### 4. create-brand-profile-from-research.ts
- In dry run: Uses mock brand profile synthesis
- In production: Would call OpenAI for profile synthesis

### 5. handle-brand-profile-output.ts
- In dry run: Saves to S3 only, skips database
- In production: Saves to both S3 and database

## Step Function State Machine Updates
The serverless.yml configuration now passes `dryRun` parameter through all states:
- ListEntitledUrls receives it from input
- Map state passes it to each company processor
- All Lambda tasks receive `dryRun` from context

## Testing Strategy

### Safe Testing in Staging/Production
```bash
# Safe test run (default - no side effects except S3)
aws stepfunctions start-execution \
  --state-machine-arn arn:aws:states:... \
  --input '{"companyIds": ["test-company-1"]}'

# Explicit dry run
aws stepfunctions start-execution \
  --state-machine-arn arn:aws:states:... \
  --input '{"companyIds": ["test-company-1"], "dryRun": true}'
```

### Production Execution
```bash
# DANGER: Real API calls and database writes
aws stepfunctions start-execution \
  --state-machine-arn arn:aws:states:... \
  --input '{"companyIds": ["real-company-1"], "dryRun": false}'
```

## Benefits
1. **Safety First**: Default dry run prevents accidental API costs
2. **Testing Friendly**: Can test full workflow without side effects
3. **Clear Intent**: `dryRun` is clearer than double-negative `no-dry-run`
4. **Debugging**: S3 outputs always available for inspection
5. **Cost Control**: Explicit opt-in for expensive operations

## Migration Notes
- Previous `saveToDatabase: true` → New `dryRun: false`
- Previous `saveToDatabase: false` → New `dryRun: true`
- Default changed from "save to database" to "don't save" for safety