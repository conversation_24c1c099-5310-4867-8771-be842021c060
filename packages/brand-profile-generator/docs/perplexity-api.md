# Perplexity API

## Create Async Chat Completion
https://docs.perplexity.ai/api-reference/async-chat-completions-post

### Example request
curl --request POST \
  --url https://api.perplexity.ai/async/chat/completions \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: application/json' \
  --data '{
  "request": {
    "model": "sonar-deep-research",
    "messages": [
      {
        "role": "system",
        "content": "Be precise and concise."
      },
      {
        "role": "user",
        "content": "{{prompt}}"
      }
    ],
    "search_mode": "web",
    "reasoning_effort": "medium",
    "max_tokens": 5000,
    "temperature": 0.2,
    "return_images": false,
    "return_related_questions": false,
    "search_recency_filter": "<string>",
    "search_after_date_filter": "<string>",
    "search_before_date_filter": "<string>",
    "last_updated_after_filter": "<string>",
    "last_updated_before_filter": "<string>",
    "top_k": 0,
    "stream": false,
    "presence_penalty": 0,
    "frequency_penalty": 0,
    "response_format": {},
    "disable_search": false,
    "enable_search_classifier": false,
    "web_search_options": {
      "search_context_size": "medium"
    }
  }
}'

### Example response

200
{
  "id": "<string>",
  "model": "<string>",
  "created_at": 123,
  "started_at": 123,
  "completed_at": 123,
  "response": {
    "id": "<string>",
    "model": "<string>",
    "created": 123,
    "usage": {
      "prompt_tokens": 123,
      "completion_tokens": 123,
      "total_tokens": 123,
      "search_context_size": "<string>",
      "citation_tokens": 123,
      "num_search_queries": 123,
      "reasoning_tokens": 123
    },
    "object": "chat.completion",
    "choices": [
      {
        "index": 123,
        "finish_reason": "stop",
        "message": {
          "content": "<string>",
          "role": "system"
        }
      }
    ],
    "search_results": [
      {
        "title": "<string>",
        "url": "<string>",
        "date": "2023-12-25"
      }
    ]
  },
  "failed_at": 123,
  "error_message": "<string>",
  "status": "CREATED"
}

## Get Async Chat Completion Response
https://docs.perplexity.ai/api-reference/async-chat-completions-request_id-get

### Example request

curl --request GET \
  --url https://api.perplexity.ai/async/chat/completions/{request_id} \
  --header 'Authorization: Bearer <token>'

### Example response

200
{
  "id": "<string>",
  "model": "<string>",
  "created_at": 123,
  "started_at": 123,
  "completed_at": 123,
  "response": {
    "id": "<string>",
    "model": "<string>",
    "created": 123,
    "usage": {
      "prompt_tokens": 123,
      "completion_tokens": 123,
      "total_tokens": 123,
      "search_context_size": "<string>",
      "citation_tokens": 123,
      "num_search_queries": 123,
      "reasoning_tokens": 123
    },
    "object": "chat.completion",
    "choices": [
      {
        "index": 123,
        "finish_reason": "stop",
        "message": {
          "content": "<string>",
          "role": "system"
        }
      }
    ],
    "search_results": [
      {
        "title": "<string>",
        "url": "<string>",
        "date": "2023-12-25"
      }
    ]
  },
  "failed_at": 123,
  "error_message": "<string>",
  "status": "CREATED"
}