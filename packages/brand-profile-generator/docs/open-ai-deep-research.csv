POST: https://api.openai.com/v1/responses,,
,,
Parameter,Value (example),Why / Best practice for deep research + web search
model,o4-mini-deep-research,Model ID used to generate the response.
background,TRUE,Whether to run the model response in the background. Deep research can take a long time; OpenAI strongly recommends using background to avoid timeouts.
instructions,{{see prompt tab}},A system (or developer) message inserted into the model's context.
tools[0].type,web_search,Search the Internet for sources related to the prompt. 
tools[0].filters.search_context_size,medium,Reasonable context budget; bump to high for very broad topics.
include,web_search_call.action.sources,Include the sources of the web search tool call.
max_tool_calls,20,"The maximum number of total calls to built-in tools that can be processed in a response. This maximum number applies across all built-in tool calls, not per individual tool. Any further attempts to call a tool by the model will be ignored."
max_output_tokens,"10,000","An upper bound for the number of tokens that can be generated for a response, including visible output tokens and reasoning tokens."
stream,FALSE,Background requests aren’t streamed; keep false.
parallel_tool_calls,TRUE,Allows concurrent search actions when useful.
temperature,0.2,"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic. We generally recommend altering this or top_p but not both."