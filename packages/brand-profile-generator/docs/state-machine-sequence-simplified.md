# Brand Profile Generator State Machine

## Sequence Diagram (Simplified)

```mermaid
sequenceDiagram
    participant StateMachine as State Machine
    participant ListUrls as ListEntitledUrls
    participant CheckExistingBrandProfile as CheckExistingBrandProfile
    participant Research as BrandResearch
    participant C<PERSON><PERSON><PERSON><PERSON><PERSON> as CreateBrandProfile
    participant HandleOutput as HandleBrandProfileOutput
    participant DB as Database
    
    StateMachine->>ListUrls: input { }
    ListUrls->>CheckExistingBrandProfile: urls [ ]
    CheckExistingBrandProfile->>Research: urls [ ]
    Research->>CreateProfile: research
    CreateProfile->>HandleOutput: brand profile
    HandleOutput->>DB: save brand profile
    HandleOutput-->>StateMachine: execution complete
```
