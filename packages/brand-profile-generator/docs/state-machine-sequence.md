# Brand Profile Generator State Machine

## Sequence Diagram

```mermaid
sequenceDiagram
    participant Tri<PERSON> as Execution Runner
    participant StateMachine as State Machine
    participant ListUrls as ListEntitledUrls
    participant S3Urls as S3 Urls
    participant MapState as Map State
    participant CheckExistingBrandProfile as CheckExistingBrandProfile
    participant Research as BrandResearch
    participant Langfuse
    participant WaitState as Wait State
    participant ResearchStatus as GetResearchJobStatus
    participant ResearchAPI as Research API
    participant CreateProfile as CreateBrandProfile
    participant OpenAIAPI as OpenAI API
    participant HandleOutput as HandleBrandProfileOutput
    participant S3Output as S3 Output
    participant ClientMarketingService as ClientMarketingService
    participant DB as Database

    Trigger->>StateMachine: Start Execution
    Note over Trigger,StateMachine: Input: {companyIds[], regenerateIfExisting, dryRun}
    
    StateMachine->>ListUrls: Invoke with input
    ListUrls->>ListUrls: Query entitled URLs based on input
    ListUrls->>S3Urls: Write URL list to S3
    S3Urls-->>ListUrls: Confirm S3 write
    ListUrls-->>StateMachine: Return S3 location with URL list
    
    Note over StateMachine,MapState: Map State begins (max concurrency configured)
    
    StateMachine->>MapState: Process URLs from S3 output
    
    loop Iterate for each URL from S3 file
        MapState->>CheckExistingBrandProfile: Check existing data
        CheckExistingBrandProfile->>Research: Invoke BrandResearch
        Research->>Langfuse: Get prompt from Langfuse
        Langfuse-->>Research: Return prompt from Langfuse
        Research->>ResearchAPI: Deep Research
        ResearchAPI-->>Research: Async status with job ID
        Research->>WaitState: Output job ID
        
        loop Wait and poll until complete
            WaitState->>ResearchStatus: Invoke GetResearchJobStatus
            ResearchStatus->>ResearchAPI: Query Research API
            ResearchAPI-->>ResearchStatus: Return async job status
            ResearchStatus-->>WaitState: Return status
        end
        
        WaitState->>CreateProfile: Research complete: Invoke CreateBrandProfile
        CreateProfile->>Langfuse: Get prompt from Langfuse
        Langfuse-->>CreateProfile: Return prompt from Langfuse
        CreateProfile->>OpenAIAPI: Call OpenAI API
        OpenAIAPI-->>CreateProfile: Brand Profile response
        CreateProfile-->>HandleOutput: Output Brand Profile data
        
        alt no-dry-run = true
            HandleOutput->>ClientMarketingService: mutation
            ClientMarketingService->>DB: Save brand profile
            ClientMarketingService->>DB: Save brand profile history
            ClientMarketingService-->>HandleOutput: mutation response
        end
        
        HandleOutput->>S3Output: Save profile as artifact
        S3Output-->>HandleOutput: Confirm S3 save
        HandleOutput-->>MapState: Iteration Complete

      end
    
    MapState-->>StateMachine: Execution Complete
```
