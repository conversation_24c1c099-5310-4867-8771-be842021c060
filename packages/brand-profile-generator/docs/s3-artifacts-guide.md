# S3 Artifacts Guide - Brand Profile Generator

## Overview

The Brand Profile Generator state machine uses S3 to store input and output artifacts for distributed map processing. This guide explains the S3 bucket structure, file naming conventions, and how to access execution data.

## S3 Bucket Configuration

### Bucket Name
`brand-profile-generator-{stage}`

- Staging: `brand-profile-generator-staging`
- Production: `brand-profile-generator-production`

### Bucket Settings
- **Versioning:** Enabled
- **Public Access:** Blocked (all public access denied)
- **Lifecycle:** No auto-deletion (artifacts kept indefinitely)
- **IAM Access:** Lambda functions have read/write permissions

## Directory Structure

```
brand-profile-jobs/
├── {date}/                            # Date in YYYY-MM-DD format
│   └── {execution-name}/              # Step Functions execution ID
│       ├── input.json                 # Input data for the execution
│       └── results/
│           ├── {companyId}.json       # Brand profile for each company
│           ├── {companyId}.json
│           └── ...
```

## File Naming Conventions

### Input File

**Path Pattern:** `brand-profile-jobs/{date}/{execution-name}/input.json`

- **Date:** Execution start date in YYYY-MM-DD format (e.g., `2024-01-15`)
- **Execution Name:** Auto-generated GUID by Step Functions (e.g., `a1b2c3d4-e5f6-7890-abcd-ef1234567890`)
- **Content:** JSON array of companies to process
- **Created By:** `PrepareS3Input` state in the state machine

**Example Path:**
```
brand-profile-jobs/2024-01-15/a1b2c3d4-e5f6-7890-abcd-ef1234567890/input.json
```

**Example Content:**
```json
[
  {
    "companyId": "123",
    "websiteUrl": "https://example.com"
  },
  {
    "companyId": "456",
    "websiteUrl": "https://another-example.com"
  }
]
```

### Result Files

**Path Pattern:** `brand-profile-jobs/{date}/{execution-name}/results/{companyId}.json`

- **Date:** Execution start date in YYYY-MM-DD format (e.g., `2024-01-15`)
- **Execution Name:** Auto-generated GUID by Step Functions
- **Company ID:** The unique identifier of the company
- **Content:** Brand profile for the specific company
- **Created By:** `HandleOutput` Lambda function in each child execution

**Example Paths:**
```
brand-profile-jobs/2024-01-15/a1b2c3d4-e5f6-7890-abcd-ef1234567890/results/company-123.json
brand-profile-jobs/2024-01-15/a1b2c3d4-e5f6-7890-abcd-ef1234567890/results/company-456.json
brand-profile-jobs/2024-01-15/a1b2c3d4-e5f6-7890-abcd-ef1234567890/results/company-789.json
```

### Individual Company Profiles (Direct Invocation)

**Path Pattern:** `profiles/{companyId}/{iso-timestamp}-brand-profile.json`

- **Company ID:** The unique identifier of the company
- **ISO Timestamp:** ISO 8601 timestamp with colons replaced by hyphens (e.g., `2025-08-21T10-30-45.123Z`)
- **Content:** Brand profile for the specific company
- **Created By:** `HandleOutput` Lambda when invoked directly (not via Step Functions)
- **Note:** Each execution gets a unique timestamp, preventing overwrites

**Example Paths:**
```
profiles/company-123/2025-08-21T10-30-45.123Z-brand-profile.json
profiles/company-456/2025-08-21T14-22-30.456Z-brand-profile.json
```

### Company Website Lists

**Path Pattern:** `company-websites/{iso-timestamp}-company-websites.json`

- **ISO Timestamp:** ISO 8601 timestamp with colons replaced by hyphens (e.g., `2025-08-21T10-30-45.123Z`)
- **Content:** JSON array of company IDs and website URLs
- **Created By:** `ListEntitledUrls` Lambda function
- **Note:** Each execution gets a unique timestamp, preventing overwrites

**Example Paths:**
```
company-websites/2025-08-21T10-30-45.123Z-company-websites.json
company-websites/2025-08-21T14-22-30.456Z-company-websites.json
```

## Accessing S3 Data

### AWS CLI Commands

#### List Recent Executions (By Date)
```bash
# List all dates with executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/

# List today's executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/$(date +%Y-%m-%d)/

# List executions from specific date
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/2024-01-15/
```

#### View Input for Specific Execution
```bash
# Display input JSON
aws s3 cp s3://brand-profile-generator-production/brand-profile-jobs/{date}/{execution-name}/input.json -

# Example
aws s3 cp s3://brand-profile-generator-production/brand-profile-jobs/2024-01-15/a1b2c3d4-e5f6-7890-abcd-ef1234567890/input.json -
```

#### List Results for an Execution
```bash
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/{date}/{execution-name}/results/
```

#### Download All Results for an Execution
```bash
aws s3 sync s3://brand-profile-generator-production/brand-profile-jobs/{date}/{execution-name}/results/ ./local-results/
```

#### Get Most Recent Executions
```bash
# Get the most recent date with executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/ | tail -1

# List all executions from the last 7 days
for i in {0..6}; do
  date=$(date -d "$i days ago" +%Y-%m-%d)
  echo "Executions for $date:"
  aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/$date/ 2>/dev/null
done
```

#### Get Specific Company's Brand Profile
```bash
# Find brand profile for a specific company across all executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/ --recursive | grep "results/company-123.json"

# Get the latest brand profile for a company
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/ --recursive | grep "results/company-123.json" | tail -1
```

### AWS Console Navigation

1. Navigate to S3 in AWS Console
2. Select bucket: `brand-profile-generator-{stage}`
3. Browse to `brand-profile-jobs/`
4. Select date folder (e.g., `2024-01-15/`)
5. Select execution folder by ID
6. View `input.json` or browse `results/` folder

## Data Correlation

### Matching Results to Input

The companyId in the result filename directly matches the companyId in `input.json`:

- `company-123.json` → Company with ID "company-123" in input array
- `company-456.json` → Company with ID "company-456" in input array
- `company-789.json` → Company with ID "company-789" in input array

### Finding Executions

#### By Date
```bash
# Today's executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/$(date +%Y-%m-%d)/

# Yesterday's executions
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/$(date -d yesterday +%Y-%m-%d)/
```

#### By Execution ID
If you know the execution ID but not the date:
```bash
# Search for execution across all dates
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/ --recursive | grep {execution-id}
```

#### From Step Functions
1. Go to Step Functions Console
2. Find your execution and copy the ID
3. Note the execution start time to determine the date folder

## Use Cases

### Debugging Failed Executions

1. Identify the execution name from Step Functions console
2. Check the input file to see what was requested
3. List results to see which companies completed
4. Missing company IDs in results indicate failed processing
5. Cross-reference with CloudWatch logs for error details

Example:
```bash
# Check which companies were processed successfully
aws s3 ls s3://brand-profile-generator-production/brand-profile-jobs/2024-01-15/{execution-id}/results/

# Compare with input to find missing companies
aws s3 cp s3://brand-profile-generator-production/brand-profile-jobs/2024-01-15/{execution-id}/input.json - | jq '.[].companyId'
```

### Historical Analysis

1. List all executions to see processing history
2. Download results for trend analysis
3. Compare brand profiles over time for the same company
4. Analyze processing times from timestamp patterns

### Data Recovery

Since artifacts are kept indefinitely:
- Retrieve any historical brand profile
- Replay failed executions with the same input
- Build analytics on all processed companies
- Audit trail for compliance

## Monitoring and Alerts

Consider setting up:
- S3 event notifications for new results
- CloudWatch alarms for S3 storage metrics
- AWS Config rules for bucket compliance
- S3 Inventory for large-scale analysis

## Best Practices

1. **Date-Based Browsing:** The date folder structure makes it easy to find recent executions without timestamps
2. **Archival:** Consider lifecycle policies for very old dates (e.g., move to Glacier after 90 days)
3. **Monitoring:** Set up alerts for dates with unusually high execution counts
4. **Access Patterns:** Use date prefixes to efficiently list recent activity
5. **Cost Optimization:** Use S3 Intelligent-Tiering for older date folders
6. **Security:** Enable S3 access logging for audit trails