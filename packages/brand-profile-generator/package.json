{"name": "brand-profile-generator", "version": "1.24.0", "description": "Brand Profile Generator State Machine", "author": "<PERSON><PERSON>ury Presence Dev Team", "private": true, "license": "UNLICENSED", "repository": {"type": "git", "url": "**************:luxurypresence/seo-automation.git", "directory": "packages/brand-profile-generator"}, "scripts": {"build": "tsc", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "format": "prettier --check \"src/**/*.ts\"", "format:fix": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "cross-env LOAD_TEST_CONFIG='true' jest --runInBand --verbose=true --forceExit  --detectOpenHandles", "test:cov": "cross-env LOAD_TEST_CONFIG='true' jest --coverage --runInBand --forceExit --detectOpenHandles", "deploy": "serverless deploy --verbose --force && serverless doctor", "run:lambda": "ts-node -r dotenv/config -r tsconfig-paths/register src/lambdas/run.ts", "changeset": "cd ../../ && changeset"}, "dependencies": {"@aws-lambda-powertools/logger": "^2.25.2", "@aws-sdk/client-lambda": "^3.876.0", "@aws-sdk/client-s3": "^3.876.0", "@aws-sdk/client-sfn": "^3.876.0", "@langchain/core": "0.3.42", "@langchain/openai": "^0.4.4", "@langfuse/client": "^4.0.0", "@langfuse/openai": "^4.0.0", "@langfuse/otel": "^4.0.0", "@opentelemetry/sdk-node": "^0.204.0", "aws-lambda": "^1.0.7", "cross-env": "^7.0.3", "datadog-lambda-js": "^12.127.0", "dd-trace": "^5.64.0", "dotenv": "^17.2.1", "graphql": "^16.11.0", "graphql-request": "6.1.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.19", "langfuse": "^3.38.4", "langfuse-langchain": "^3.38.4", "openai": "^5.19.1", "p-limit": "^3.1.0", "source-map-support": "^0.5.21", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.34.0", "@types/aws-lambda": "^8.10.152", "@types/babel__core": "^7.20.5", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "babel-jest": "^30.1.1", "esbuild": "^0.25.5", "eslint": "9.34.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^16.3.0", "jest": "^30.0.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "serverless": "^3.40.0", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.54.5", "serverless-offline": "^14.3.4", "serverless-plugin-datadog": "^5.101.0", "serverless-step-functions": "^3.21.1", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "~5.9.0", "typescript-eslint": "^8.41.0"}, "jest": {"transform": {"^.+\\.[tj]sx?$": "babel-jest"}, "moduleNameMapper": {"^src/(.*)": "<rootDir>/src/$1"}, "modulePaths": ["<rootDir>/src"], "modulePathIgnorePatterns": ["node_modules", "dist", ".history"], "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["src/config/*", "mock", "testEvents", "factories", "run.ts"], "moduleDirectories": ["node_modules", "src"], "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/test"], "transformIgnorePatterns": ["/node_modules/.pnpm/(?!remark-parse|remark-stringify|unified|unist-util-visit|mdast-util-.*|micromark.*|decode-named-character-reference|character-entities|@mendable|langchain|@langchain)@"]}, "keywords": []}