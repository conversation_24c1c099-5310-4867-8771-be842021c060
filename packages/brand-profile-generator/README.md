# Brand Profile Generator

AWS Lambda-based service for generating and managing brand profiles using Step Functions orchestration.

## Overview

This service handles:

- Brand profile generation from various data sources

## Architecture

### Lambda Functions

- **Example Handler**: Template Lambda for brand profile processing workflows

### Step Functions

State machine orchestration for brand profile generation workflow (to be implemented).

## Development

### Prerequisites

- Node.js 18+
- pnpm package manager

### Setup

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build
```

### Environment Variables

Copy `.env.example` to `.env` and configure required variables. See `config-template.yaml` for vault-managed secrets.

## Running the State Machine

### AWS Console Execution

1. **Navigate to Step Functions**
   - AWS Console → Step Functions → State machines
   - Select `brand-profile-generator-[stage]`

2. **Start Execution**
   - Click "Start execution"
   - Provide input JSON (see examples below)

### Input Parameters

**Minimal Input (processes all entitled companies):**
```json
{}
```

**Specific Companies:**
```json
{
  "companyIds": ["comp_123", "comp_456"]
}
```

**Regenerate Existing Profiles:**
```json
{
  "companyIds": ["comp_123"],
  "regenerateIfExisting": true
}
```

**Production Run (saves to database):**
```json
{
  "companyIds": ["comp_123"],
  "dryRun": false
}
```

**Full Configuration:**
```json
{
  "companyIds": ["comp_123", "comp_456"],
  "regenerateIfExisting": false,
  "dryRun": true
}
```

### Input Parameters Explained

- **companyIds** (array, optional): Specific company IDs to process. If empty/omitted, queries all entitled companies
- **regenerateIfExisting** (boolean, default: false): Whether to regenerate profiles for companies that already have one
- **dryRun** (boolean, default: true): If true, saves only to S3; if false, also saves to database via GraphQL

### Output Locations

**CloudWatch Logs:**
- Lambda logs: `/aws/lambda/brand-profile-generator-[stage]-[function]`
- Step Function logs: AWS Console → Step Functions → Execution History

**S3 Files:**
- Bucket: `brand-profile-generator-[stage]`
- Structure:
  ```
  brand-profile-jobs/
  └── YYYY-MM-DD/                          # Date from execution start time
      └── [execution-name]/                # With -dry-run suffix if dryRun: true
          ├── input.json                   # List of companies to process (or input-dry-run.json)
          ├── [UUID]/                      # ResultWriter output folder (auto-generated)
          │   ├── manifest.json            # Map state execution manifest
          │   └── SUCCEEDED_0.json        # Processing status for each company
          ├── results/                     # Brand profile outputs
          │   └── [companyId].json         # Individual profiles (or [companyId]-dry-run.json)
          └── execution-report.json        # Final execution summary (or execution-report-dry-run.json)
  ```

**Database (when dryRun: false):**
- Brand profiles saved to `brand_profile` table via GraphQL mutation
- Query via GraphQL: `brandProfile(companyId: "comp_123")`

### Monitoring Execution

1. **Real-time Status**: Step Functions console shows current state and progress
2. **Map State Progress**: View parallel processing of companies in Map state
3. **Error Details**: Failed states show error messages in execution event history
4. **Slack Notifications**: Sent on completion if `SLACK_NOTIFICATIONS_ENABLED=true`

### Execution Report

The final report includes:
- Total companies processed
- Success/skip/failure counts
- Success rate percentage
- Individual company summaries with S3 locations
- Execution duration

## Testing

```bash
# Run unit tests
pnpm test

# Lint code
pnpm lint
```

## Deployment

The service is deployed using Serverless Framework
