/**
 * Step Function input parameters for Brand Profile Generator
 */
export interface BrandProfileGeneratorInput {
  /**
   * Optional array of company IDs to process.
   * If provided, bypasses entitlement check and only processes these companies.
   */
  companyIds?: string[];

  /**
   * Whether to regenerate brand profile even if one already exists.
   * When true, existing profiles will be overwritten (history preserved in brand_profile_history table).
   * @default false
   */
  regenerateIfExisting?: boolean;

  /**
   * Controls whether this is a dry run or production execution.
   * When true (default), no external API calls or database writes will be made.
   * Dummy LLM responses will be used and outputs will only be written to S3 for evaluation.
   * When false, real API calls to OpenAI/LLMs will be made and results saved to database.
   * @default true
   */
  dryRun?: boolean;
}

/**
 * Extended input for individual Lambda functions that includes execution context
 */
export interface LambdaExecutionContext {
  executionName?: string;
  executionStartTime?: string;
  dryRun: boolean; // Required at Lambda level, with default applied by Step Function
}
