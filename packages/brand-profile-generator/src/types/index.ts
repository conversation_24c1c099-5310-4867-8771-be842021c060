export interface BrandProfile {
  companyId: string;
  generatedAt: string;
  aboutTheBrand?: string;
  strategicFocus?: string;
  valueProposition?: string;
  idealCustomerProfiles?: string;
  missionAndCoreValues?: string;
  brandPointOfView?: string;
  toneOfVoice?: string;
  ctaText?: string;
  authorPersona?: string;
  researchSources?: Array<{
    url: string;
    title?: string;
  }>;
}

export interface ProcessingResult {
  success: boolean;
  data?: any;
  error?: string;
}
