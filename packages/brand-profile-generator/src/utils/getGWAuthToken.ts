import * as jwt from 'jsonwebtoken';

/**
 * Generate a JWT token for API Gateway authentication
 * @param apiGatewaySuperUser The super user company ID
 * @param apiGatewayKey The API Gateway key
 * @returns The JWT token
 */
export function getGWAuthToken(
  apiGatewaySuperUser: string,
  apiGatewayKey: string,
): string {
  const header = {
    alg: 'HS256',
    typ: 'JWT',
  };

  const payload = {
    id: apiGatewaySuperUser,
  };

  return jwt.sign(payload, apiGatewayKey, { header });
}
