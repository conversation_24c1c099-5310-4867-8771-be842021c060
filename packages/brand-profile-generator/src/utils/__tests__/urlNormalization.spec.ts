import { normalizeWebsiteUrl } from '../urlNormalization';

describe('normalizeWebsiteUrl', () => {
  it('should add https:// to URLs without protocol', () => {
    expect(normalizeWebsiteUrl('example.com')).toBe('https://example.com');
    expect(normalizeWebsiteUrl('www.example.com')).toBe(
      'https://www.example.com',
    );
  });

  it('should replace http:// with https://', () => {
    expect(normalizeWebsiteUrl('http://example.com')).toBe(
      'https://example.com',
    );
    expect(normalizeWebsiteUrl('HTTP://example.com')).toBe(
      'https://example.com',
    );
  });

  it('should keep https:// URLs unchanged', () => {
    expect(normalizeWebsiteUrl('https://example.com')).toBe(
      'https://example.com',
    );
    expect(normalizeWebsiteUrl('HTTPS://example.com')).toBe(
      'HTTPS://example.com',
    );
  });

  it('should handle URLs with paths and query parameters', () => {
    expect(normalizeWebsiteUrl('example.com/path?query=1')).toBe(
      'https://example.com/path?query=1',
    );
    expect(normalizeWebsiteUrl('http://example.com/path?query=1')).toBe(
      'https://example.com/path?query=1',
    );
  });

  it('should trim whitespace', () => {
    expect(normalizeWebsiteUrl('  example.com  ')).toBe('https://example.com');
    expect(normalizeWebsiteUrl('  http://example.com  ')).toBe(
      'https://example.com',
    );
  });

  it('should throw error for invalid inputs', () => {
    expect(() => normalizeWebsiteUrl('')).toThrow(
      'Invalid websiteUrl: must be a non-empty string',
    );
    expect(() => normalizeWebsiteUrl('   ')).toThrow(
      'Invalid websiteUrl: must be a non-empty string',
    );
    expect(() => normalizeWebsiteUrl(null as any)).toThrow(
      'Invalid websiteUrl: must be a non-empty string',
    );
    expect(() => normalizeWebsiteUrl(undefined as any)).toThrow(
      'Invalid websiteUrl: must be a non-empty string',
    );
  });
});
