/**
 * Normalize a website URL to ensure it has a proper https:// protocol
 * @param websiteUrl The raw website URL that may or may not have a protocol
 * @returns A normalized URL with https:// protocol
 */
export function normalizeWebsiteUrl(websiteUrl: string): string {
  if (!websiteUrl || typeof websiteUrl !== 'string') {
    throw new Error('Invalid websiteUrl: must be a non-empty string');
  }

  let normalizedUrl = websiteUrl.trim();

  if (!normalizedUrl) {
    throw new Error('Invalid websiteUrl: must be a non-empty string');
  }

  // Handle various URL formats
  if (
    !normalizedUrl.toLowerCase().startsWith('http://') &&
    !normalizedUrl.toLowerCase().startsWith('https://')
  ) {
    // No protocol, add https://
    normalizedUrl = `https://${normalizedUrl}`;
  } else if (normalizedUrl.toLowerCase().startsWith('http://')) {
    // Replace http:// with https://
    normalizedUrl = normalizedUrl.replace(/^https?:\/\//i, 'https://');
  }
  // If already https://, use as-is

  return normalizedUrl;
}
