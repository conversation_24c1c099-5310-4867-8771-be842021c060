import { Logger } from '@aws-lambda-powertools/logger';

const serviceName = 'brand-profile-generator';

export const createLogger = (context?: string): Logger => {
  const logLevel = process.env.LOG_LEVEL || 'INFO';
  const logger = new Logger({
    serviceName,
    environment: process.env.ENVIRONMENT || 'development',
    logLevel: logLevel as 'INFO' | 'DEBUG' | 'WARN' | 'ERROR',
  });

  if (context) {
    logger.appendKeys({ context });
  }

  return logger;
};

export const defaultLogger = createLogger();
