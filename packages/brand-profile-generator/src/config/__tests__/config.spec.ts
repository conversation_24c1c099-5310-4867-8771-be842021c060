import { getEnvironmentConfig } from '../implementation';
import { getConfig } from '../index';

describe('Configuration Module', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('getEnvironmentConfig', () => {
    it('should return default configuration when no env vars are set', () => {
      const config = getEnvironmentConfig();

      expect(config).toMatchObject({
        environment: 'development',
        apiGateway: {
          url: '',
          superUser: '',
          apiGatewayKey: '',
        },
        tenantApi: {
          url: 'http://localhost:8011',
        },
        mock: {
          useMockClients: false,
        },
      });
    });

    it('should use environment variables when provided', () => {
      process.env.ENVIRONMENT = 'production';
      process.env.API_GATEWAY_URL = 'https://api.example.com';
      process.env.API_GATEWAY_SUPER_USER_COMPANY_ID = 'super-123';
      process.env.API_GATEWAY_KEY = 'api-key-456';
      process.env.TENANT_SERVICE_URL = 'https://tenant.example.com';
      process.env.USE_MOCK_CLIENTS = 'true';

      const config = getEnvironmentConfig();

      expect(config).toMatchObject({
        environment: 'production',
        apiGateway: {
          url: 'https://api.example.com',
          superUser: 'super-123',
          apiGatewayKey: 'api-key-456',
        },
        tenantApi: {
          url: 'https://tenant.example.com',
        },
        mock: {
          useMockClients: true,
        },
      });
    });

    it('should configure Langfuse settings correctly', () => {
      process.env.LANGFUSE_SECRET_KEY = 'secret-key';
      process.env.LANGFUSE_PUBLIC_KEY = 'public-key';
      process.env.LANGFUSE_BASE_URL = 'https://langfuse.example.com';
      process.env.LANGFUSE_ENVIRONMENT = 'production';
      process.env.LANGFUSE_FLUSH_AT = '10';
      process.env.BRAND_PROFILE_GENERATOR_PROMPT = 'custom-prompt';

      const config = getEnvironmentConfig();

      expect(config.prompt.langfuse).toMatchObject({
        secretKey: 'secret-key',
        publicKey: 'public-key',
        baseUrl: 'https://langfuse.example.com',
        environment: 'production',
        flushAt: 10,
        prompts: {
          brandProfileGenerator: 'custom-prompt',
        },
      });
    });

    it('should configure OpenAI settings correctly', () => {
      process.env.OPENAI_API_KEY = 'sk-test-key';
      process.env.OPENAI_MODEL_NAME = 'gpt-4-turbo';
      process.env.OPENAI_TEMPERATURE = '0.7';

      const config = getEnvironmentConfig();

      expect(config.prompt.openai).toMatchObject({
        apiKey: 'sk-test-key',
        modelName: 'gpt-4-turbo',
        temperature: 0.7,
      });
    });

    it('should configure Slack settings correctly', () => {
      process.env.SLACK_WEBHOOK_URL = 'https://hooks.slack.com/test';
      process.env.SLACK_NOTIFICATIONS_ENABLED = 'true';

      const config = getEnvironmentConfig();

      expect(config.slack).toMatchObject({
        webhookUrl: 'https://hooks.slack.com/test',
        enabled: true,
      });
    });

    it('should handle S3 configuration when provided', () => {
      process.env.BRAND_PROFILES_S3_BUCKET_NAME = 'my-brand-profiles';
      process.env.BRAND_PROFILES_S3_REGION = 'us-west-2';

      const config = getEnvironmentConfig();

      expect(config.brandProfilesS3).toMatchObject({
        bucketName: 'my-brand-profiles',
        region: 'us-west-2',
      });
    });

    it('should not include S3 config when bucket name is not provided', () => {
      delete process.env.BRAND_PROFILES_S3_BUCKET_NAME;

      const config = getEnvironmentConfig();

      expect(config.brandProfilesS3).toBeUndefined();
    });
  });

  describe('getConfig', () => {
    it('should cache configuration on first call', () => {
      const config1 = getConfig();
      const config2 = getConfig();

      expect(config1).toBe(config2);
    });

    it('should return valid configuration structure', () => {
      const config = getConfig();

      expect(config).toHaveProperty('environment');
      expect(config).toHaveProperty('apiGateway');
      expect(config).toHaveProperty('tenantApi');
      expect(config).toHaveProperty('cmsApi');
      expect(config).toHaveProperty('seoAutomationApi');
      expect(config).toHaveProperty('stateMachine');
      expect(config).toHaveProperty('launchDarklyKey');
      expect(config).toHaveProperty('mock');
      expect(config).toHaveProperty('prompt');
      expect(config).toHaveProperty('slack');
    });
  });

  describe('Environment-specific configurations', () => {
    it('should set development defaults correctly', () => {
      process.env.ENVIRONMENT = 'development';

      const config = getEnvironmentConfig();

      expect(config.environment).toBe('development');
      expect(config.seoAutomationApi.m2mSuperApiKey).toBe('localkey');
    });

    it('should not set default m2m key for production', () => {
      process.env.ENVIRONMENT = 'production';

      const config = getEnvironmentConfig();

      expect(config.environment).toBe('production');
      expect(config.seoAutomationApi.m2mSuperApiKey).toBe('');
    });

    it('should handle staging environment', () => {
      process.env.ENVIRONMENT = 'staging';
      process.env.M2M_SUPER_API_KEY = 'staging-key';

      const config = getEnvironmentConfig();

      expect(config.environment).toBe('staging');
      expect(config.seoAutomationApi.m2mSuperApiKey).toBe('staging-key');
    });
  });
});
