import {
  ApiGatewayConfig,
  CmsConfig,
  EnvironmentConfig,
  LangfuseConfig,
  MockConfig,
  OpenaiConfig,
  PromptConfig,
  S3Config,
  SeoAutomationConfig,
  SlackConfig,
  StateMachineConfig,
  TenantConfig,
} from './interfaces';

/**
 * Get the environment configuration
 * @returns The environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  // API Gateway Configuration
  const apiGatewayConfig: ApiGatewayConfig = {
    url: process.env.API_GATEWAY_URL || '',
    superUser: process.env.API_GATEWAY_SUPER_USER_COMPANY_ID || '',
    apiGatewayKey: process.env.API_GATEWAY_KEY || '',
  };

  // Tenant Configuration
  const tenantConfig: TenantConfig = {
    url: process.env.TENANT_SERVICE_URL || 'http://localhost:8011',
    productIds: process.env.PRODUCT_IDS
      ? process.env.PRODUCT_IDS.split(',').map(id => id.trim())
      : ['5c9b746c-7327-42ce-b998-ce707e7cee44'], // Default product ID
  };

  // CMS Configuration
  const cmsConfig: CmsConfig = {
    url: process.env.CMS_SERVICE_URL || undefined,
  };

  // SEO Automation API Configuration
  // Ensure the URL has /graphql path if not already present
  const cosmoUrl = process.env.COSMO_GQL_URL || 'http://localhost:3001';

  // Handle the URL path properly
  let graphqlUrl: string;
  if (cosmoUrl.endsWith('/graphql')) {
    // Already has /graphql
    graphqlUrl = cosmoUrl;
  } else if (cosmoUrl.endsWith('/')) {
    // Has trailing slash, append graphql
    graphqlUrl = `${cosmoUrl}graphql`;
  } else {
    // No trailing slash, append /graphql
    graphqlUrl = `${cosmoUrl}/graphql`;
  }

  // Log URL transformation for debugging
  if (cosmoUrl !== graphqlUrl) {
    console.log(
      `[Config] Transformed COSMO_GQL_URL from "${cosmoUrl}" to "${graphqlUrl}"`,
    );
  }

  const seoAutomationApi: SeoAutomationConfig = {
    url: graphqlUrl,
    m2mSuperApiKey:
      process.env.M2M_SUPER_API_KEY ||
      (process.env.ENVIRONMENT === 'development' || !process.env.ENVIRONMENT
        ? 'localkey'
        : ''),
  };

  // State Machine Configuration
  const stateMachine: StateMachineConfig = {
    brandProfileArn: process.env.BRAND_PROFILE_SFN_ARN || '',
  };

  // Mock Configuration
  const mock: MockConfig = {
    useMockClients: process.env.USE_MOCK_CLIENTS === 'true',
  };

  // Langfuse Configuration
  const langfuseConfig: LangfuseConfig = {
    secretKey: process.env.LANGFUSE_SECRET_KEY || '',
    publicKey: process.env.LANGFUSE_PUBLIC_KEY || '',
    baseUrl: process.env.LANGFUSE_BASE_URL || 'https://us.cloud.langfuse.com',
    environment: process.env.LANGFUSE_ENVIRONMENT || 'staging',
    flushAt: process.env.LANGFUSE_FLUSH_AT
      ? parseInt(process.env.LANGFUSE_FLUSH_AT, 10)
      : 1,
    prompts: {
      brandProfileGenerator:
        process.env.BRAND_PROFILE_GENERATOR_PROMPT || 'brand_profile_generator',
    },
  };

  // OpenAI Configuration
  const openaiConfig: OpenaiConfig = {
    modelName: process.env.OPENAI_MODEL_NAME || 'gpt-4o',
    temperature: process.env.OPENAI_TEMPERATURE
      ? parseFloat(process.env.OPENAI_TEMPERATURE)
      : 0,
    apiKey: process.env.OPENAI_API_KEY || '',
  };

  // Prompt Configuration
  const prompt: PromptConfig = {
    langfuse: langfuseConfig,
    openai: openaiConfig,
  };

  // Slack Configuration
  const slackConfig: SlackConfig = {
    webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
    enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === 'true',
  };

  // S3 Configuration for Brand Profiles (optional)
  const brandProfilesS3Config: S3Config | undefined = process.env
    .BRAND_PROFILES_S3_BUCKET_NAME
    ? {
        bucketName: process.env.BRAND_PROFILES_S3_BUCKET_NAME,
        region: process.env.BRAND_PROFILES_S3_REGION || 'us-east-1',
      }
    : undefined;

  return {
    apiGateway: apiGatewayConfig,
    tenantApi: tenantConfig,
    cmsApi: cmsConfig,
    seoAutomationApi,
    stateMachine,
    environment: process.env.ENVIRONMENT || 'development',
    launchDarklyKey: process.env.LAUNCHDARKLY_KEY || '',
    mock,
    prompt,
    slack: slackConfig,
    brandProfilesS3: brandProfilesS3Config,
  };
}
