base:
  ENVIRONMENT: ${ENVIRONMENT}
  API_GATEWAY_SUPER_USER_COMPANY_ID: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_SUPER_USER_COMPANY_ID
  API_GATEWAY_KEY: vault:secret/data/${VAULT_ENV}/standard#API_GATEWAY_KEY
  LAUNCHDARKLY_KEY: vault:secret/data/${VAULT_ENV}/standard#LAUNCHDARKLY_KEY
  TENANT_SERVICE_URL: https://tenant-service.${BASE_DOMAIN}
  CMS_SERVICE_URL: https://cms-service.${BASE_DOMAIN}
  API_GATEWAY_URL: https://gw.${BASE_DOMAIN}
  COSMO_GQL_URL: https://graphql.${BASE_DOMAIN}
  LANGFUSE_SECRET_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_SECRET_KEY
  LANGFUSE_PUBLIC_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#LANGFUSE_PUBLIC_KEY
  LANGFUSE_ENVIRONMENT: staging
  LANGFUSE_FLUSH_AT: 1
  LANGFUSE_BASE_URL: https://us.cloud.langfuse.com
  OPENAI_MODEL_NAME: gpt-4o
  OPENAI_TEMPERATURE: 0
  SECURITY_GROUP_IDS: ${SECURITY_GROUP_IDS}
  SUBNET_IDS: ${SUBNET_IDS}
  DEPLOY_BUCKET: ${DEPLOY_BUCKET}
  M2M_SUPER_API_KEY: vault:secret/data/${VAULT_ENV}/standard#DOMAIN_M2M_KEY
  OPENAI_API_KEY: vault:secret/data/${VAULT_ENV}/client-marketing-service#OPENAI_API_KEY
  SLACK_NOTIFICATIONS_ENABLED: true
  SLACK_WEBHOOK_URL: *******************************************************************************
  # Product IDs for filtering entitlements (comma-separated if multiple)
  PRODUCT_IDS: 5c9b746c-7327-42ce-b998-ce707e7cee44
  # Brand Profile Generator specific prompts
  BRAND_PROFILE_GENERATOR_PROMPT: brand_profile_generator
  # S3 bucket for storing brand profile generator inputs and outputs
  BRAND_PROFILES_S3_BUCKET_NAME: brand-profile-generator-${ENVIRONMENT}
  BRAND_PROFILES_S3_REGION: us-east-1

production:
  LANGFUSE_ENVIRONMENT: production
  LANGFUSE_FLUSH_AT: 1
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
  # Enable any scheduled tasks for production
  # BRAND_PROFILE_SCHEDULER_STATE: ENABLED

staging:
  DD_API_KEY: vault:secret/data/${VAULT_ENV}/standard#LAMBDA_DATADOG_KEY
  # Enable any scheduled tasks for staging
  # BRAND_PROFILE_SCHEDULER_STATE: DISABLED
