import { S3ClientConfig } from '@aws-sdk/client-s3';

/**
 * Interface for API Gateway configuration
 */
export interface ApiGatewayConfig {
  url: string;
  superUser: string;
  apiGatewayKey: string;
}

/**
 * Interface for tenant service configuration
 */
export interface TenantConfig {
  /**
   * Tenant service URL
   */
  url: string;

  /**
   * Product IDs to filter entitlements
   */
  productIds: string[];
}

/**
 * Interface for CMS service configuration
 */
export interface CmsConfig {
  /**
   * CMS service URL (optional)
   */
  url?: string;
}

/**
 * Interface for SEO Automation API configuration
 */
export interface SeoAutomationConfig {
  /**
   * API Gateway URL
   */
  url: string;

  /**
   * M2M super API key for authentication
   */
  m2mSuperApiKey: string;
}

/**
 * Interface for state machine configuration
 */
export interface StateMachineConfig {
  /**
   * Brand profile generator state machine ARN
   */
  brandProfileArn: string;
}

/**
 * Interface for S3 configuration
 */
export type S3Config = S3ClientConfig & {
  bucketName: string;
};

/**
 * Interface for mock configuration
 */
export interface MockConfig {
  /**
   * Whether to use mock clients instead of real ones
   */
  useMockClients: boolean;
}

/**
 * Interface for Langfuse configuration
 */
export interface LangfuseConfig {
  secretKey: string;
  publicKey: string;
  baseUrl: string;
  environment: string;
  flushAt: number;
  prompts: {
    brandProfileGenerator?: string;
  };
}

/**
 * Interface for OpenAI configuration
 */
export interface OpenaiConfig {
  modelName: string;
  temperature: number;
  apiKey: string;
}

/**
 * Interface for prompt configuration
 */
export interface PromptConfig {
  langfuse: LangfuseConfig;
  openai: OpenaiConfig;
}

/**
 * Interface for Slack configuration
 */
export interface SlackConfig {
  /**
   * Slack webhook URL for notifications
   */
  webhookUrl: string;

  /**
   * Whether to enable Slack notifications
   */
  enabled: boolean;
}

/**
 * Main environment configuration interface for Brand Profile Generator
 */
export interface EnvironmentConfig {
  /**
   * API Gateway configuration
   */
  apiGateway: ApiGatewayConfig;

  /**
   * Tenant service configuration
   */
  tenantApi: TenantConfig;

  /**
   * CMS service configuration
   */
  cmsApi: CmsConfig;

  /**
   * SEO Automation API configuration
   */
  seoAutomationApi: SeoAutomationConfig;

  /**
   * State machine configuration
   */
  stateMachine: StateMachineConfig;

  /**
   * Environment name
   */
  environment: string;

  /**
   * LaunchDarkly key
   */
  launchDarklyKey: string;

  /**
   * Mock configuration
   */
  mock: MockConfig;

  /**
   * Prompt configuration
   */
  prompt: PromptConfig;

  /**
   * Slack configuration
   */
  slack: SlackConfig;

  /**
   * S3 configuration for storing brand profiles
   */
  brandProfilesS3?: S3Config;
}
