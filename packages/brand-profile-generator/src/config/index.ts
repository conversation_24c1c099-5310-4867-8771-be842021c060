/**
 * Configuration module for the Brand Profile Generator
 */

import { getEnvironmentConfig } from './implementation';

export * from './interfaces';

let cachedConfig: import('./interfaces').EnvironmentConfig | null = null;

/**
 * Get the environment configuration (synchronous)
 * @returns The environment configuration
 */
export function getConfig(): import('./interfaces').EnvironmentConfig {
  if (!cachedConfig) {
    cachedConfig = getEnvironmentConfig();
  }
  return cachedConfig;
}
