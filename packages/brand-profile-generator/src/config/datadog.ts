/**
 * Datadog configuration and utilities for Brand Profile Generator
 * Centralizes Datadog setup and provides reusable metric functions
 */

// Conditional import to handle test environment
let sendCustomMetric: (
  name: string,
  value: number,
  // 'type' is ignored at runtime; we send distribution metrics
  type?: string,
  tags?: string[],
) => Promise<void>;

// Initialize sendCustomMetric function
function initializeDatadog() {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const datadogLambda = require('datadog-lambda-js');

    if (typeof datadogLambda.sendDistributionMetric === 'function') {
      // Wrap to keep our API stable while calling the supported Datadog function
      sendCustomMetric = (name, value, _type?, tags?) => {
        const validTags = (tags || []).filter(
          (tag): tag is string => typeof tag === 'string' && tag.length > 0,
        );
        return Promise.resolve(
          datadogLambda.sendDistributionMetric(name, value, ...validTags),
        );
      };
    } else if (
      typeof datadogLambda.sendDistributionMetricWithDate === 'function'
    ) {
      // Fallback to WithDate variant
      sendCustomMetric = (name, value, _type?, tags?) => {
        const validTags = (tags || []).filter(
          (tag): tag is string => typeof tag === 'string' && tag.length > 0,
        );
        return Promise.resolve(
          datadogLambda.sendDistributionMetricWithDate(
            name,
            value,
            new Date(),
            ...validTags,
          ),
        );
      };
    } else {
      throw new Error('sendDistributionMetric not found in datadog-lambda-js');
    }
  } catch {
    // In test environment or when datadog-lambda-js is not available
    sendCustomMetric = (
      name: string,
      value: number,
      type?: string,
      tags?: string[],
    ) => {
      // Silent mock for test environment
      if (process.env.NODE_ENV === 'test') {
        return Promise.resolve();
      }
      console.log(
        `📊 [MOCK METRIC] ${name}: ${value} (${type}) [${tags?.join(', ') || 'no tags'}]`,
      );
      return Promise.resolve();
    };
  }
}

// Initialize on module load
initializeDatadog();

export interface DatadogConfig {
  service: string;
  environment: string;
  version: string;
  defaultTags: string[];
}

export const getDatadogConfig = (): DatadogConfig => {
  const environment = process.env.ENVIRONMENT || 'development';

  return {
    service: 'brand-profile-generator',
    environment,
    version: process.env.GIT_SHA || 'latest',
    defaultTags: [
      `environment:${environment}`,
      'team:client-marketing',
      'platform_version:v3',
      'project:seo-automation',
    ],
  };
};

/**
 * Custom metrics namespace for the Brand Profile Generator service
 */
export const METRICS = {
  // Profile generation metrics
  PROFILE_GENERATION_TIME: 'brand_profile_generator.profile.generation_time',
  PROFILE_GENERATION_COUNT: 'brand_profile_generator.profile.count',
  PROFILE_GENERATION_SUCCESS: 'brand_profile_generator.profile.success',
  PROFILE_GENERATION_FAILURE: 'brand_profile_generator.profile.failure',

  // Data processing metrics
  DATA_FETCH_TIME: 'brand_profile_generator.data.fetch_time',
  DATA_PROCESSING_TIME: 'brand_profile_generator.data.processing_time',

  // Storage metrics
  PROFILE_STORAGE_TIME: 'brand_profile_generator.storage.time',
  PROFILE_STORAGE_SIZE: 'brand_profile_generator.storage.size',

  // Error tracking
  LAMBDA_ERRORS: 'brand_profile_generator.lambda.errors',
  SERVICE_ERRORS: 'brand_profile_generator.service.errors',

  // Step Function metrics
  STEP_FUNCTION_EXECUTIONS: 'brand_profile_generator.step_functions.executions',
  STEP_FUNCTION_FAILURES: 'brand_profile_generator.step_functions.failures',
} as const;

/**
 * Utility functions for sending common metrics with standardized tags
 */
export class DatadogMetrics {
  private config: DatadogConfig;

  constructor() {
    this.config = getDatadogConfig();
  }

  /**
   * Sends a timing metric with lambda-specific tags
   */
  async sendTimingMetric(
    metricName: string,
    value: number,
    lambdaName: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'milliseconds', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send timing metric:', error);
      }
    }
  }

  /**
   * Sends a count metric with standardized tags
   */
  async sendCountMetric(
    metricName: string,
    value: number,
    component: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `component:${component}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'count', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send count metric:', error);
      }
    }
  }

  /**
   * Sends a gauge metric with standardized tags
   */
  async sendGaugeMetric(
    metricName: string,
    value: number,
    component: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `component:${component}`,
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, value, 'gauge', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send gauge metric:', error);
      }
    }
  }

  /**
   * Sends an error metric with error context
   */
  async sendErrorMetric(
    metricName: string,
    lambdaName: string,
    errorType: string,
    additionalTags: string[] = [],
  ): Promise<void> {
    const tags = [
      ...this.config.defaultTags,
      `lambda:${lambdaName}`,
      `error_type:${errorType}`,
      'status:error',
      ...additionalTags,
    ];

    try {
      await sendCustomMetric(metricName, 1, 'count', tags);
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send error metric:', error);
      }
    }
  }

  /**
   * Sends multiple metrics in parallel for performance
   */
  async sendMetrics(
    metrics: Array<{
      name: string;
      value: number;
      type: 'count' | 'gauge' | 'histogram' | 'milliseconds' | 'megabytes';
      tags: string[];
    }>,
  ): Promise<void> {
    try {
      await Promise.all(
        metrics.map(metric =>
          sendCustomMetric(metric.name, metric.value, metric.type, [
            ...this.config.defaultTags,
            ...metric.tags,
          ]),
        ),
      );
    } catch (error) {
      // Silently handle errors in test environment
      if (process.env.NODE_ENV !== 'test') {
        console.warn('Failed to send metrics:', error);
      }
    }
  }
}

/**
 * Global instance for easy access throughout the application
 */
export const datadogMetrics = new DatadogMetrics();
