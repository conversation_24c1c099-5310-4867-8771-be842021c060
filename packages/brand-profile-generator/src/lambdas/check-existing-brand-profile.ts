import { Logger } from '@aws-lambda-powertools/logger';
import { Handler } from 'aws-lambda';

import { BrandProfileClient } from '../clients/BrandProfileClient';
import { getEnvironmentConfig } from '../config/implementation';
import {
  CheckBrandProfileError,
  TransientCheckBrandProfileError,
} from '../errors';

export interface CheckExistingBrandProfileInput {
  companyId: string;
  websiteUrl: string;
  regenerateIfExisting?: boolean;
}

export interface CheckExistingBrandProfileOutput {
  companyId: string;
  websiteUrl: string;
  hasExistingProfile: boolean;
  shouldProcess: boolean;
  decisionReason?: string;
}

// Initialize logger and configuration outside handler for reuse
const logger = new Logger({
  serviceName: 'check-existing-brand-profile',
});

const config = getEnvironmentConfig();

// Log configuration details for debugging
logger.info('Lambda initialization', {
  seoAutomationApiUrl: config.seoAutomationApi?.url || 'NOT_CONFIGURED',
  hasApiKey: !!config.seoAutomationApi?.m2mSuperApiKey,
  apiKeyLength: config.seoAutomationApi?.m2mSuperApiKey?.length || 0,
  apiKeyPrefix:
    config.seoAutomationApi?.m2mSuperApiKey?.substring(0, 5) || 'NONE',
  environment: process.env.ENVIRONMENT || 'unknown',
  nodeEnv: process.env.NODE_ENV || 'unknown',
  rawCosmoGqlUrl: process.env.COSMO_GQL_URL || 'NOT_SET',
  finalGraphqlUrl: config.seoAutomationApi?.url,
  m2mApiKeyEnv: process.env.M2M_SUPER_API_KEY ? 'SET' : 'NOT_SET',
});

// Validate configuration before creating client
if (!config.seoAutomationApi?.url) {
  logger.error('SEO Automation API URL is not configured', {
    config: config.seoAutomationApi,
  });
  throw new Error('SEO Automation API URL is not configured');
}

if (!config.seoAutomationApi?.m2mSuperApiKey) {
  logger.error('M2M Super API Key is not configured', {
    config: config.seoAutomationApi,
  });
  throw new Error('M2M Super API Key is not configured');
}

// Create the brand profile client
const brandProfileClient = new BrandProfileClient(
  config.seoAutomationApi.url,
  config.seoAutomationApi.m2mSuperApiKey,
  logger,
);

export const handler: Handler<
  CheckExistingBrandProfileInput,
  CheckExistingBrandProfileOutput
> = async event => {
  const startTime = Date.now();

  logger.info('CheckExistingBrandProfile Lambda invoked', {
    companyId: event.companyId,
    websiteUrl: event.websiteUrl,
    regenerateIfExisting: event.regenerateIfExisting,
  });

  try {
    // Early runtime validation
    if (!event.companyId || typeof event.companyId !== 'string') {
      throw new CheckBrandProfileError(
        'Invalid or missing companyId',
        undefined,
        { companyId: event.companyId, websiteUrl: event.websiteUrl },
      );
    }

    if (!event.websiteUrl || typeof event.websiteUrl !== 'string') {
      throw new CheckBrandProfileError(
        'Invalid or missing websiteUrl',
        undefined,
        { companyId: event.companyId, websiteUrl: event.websiteUrl },
      );
    }

    // Default regenerateIfExisting to false if not provided
    const regenerateIfExisting = event.regenerateIfExisting ?? false;

    // If regenerateIfExisting is true, always process
    if (regenerateIfExisting) {
      logger.info(
        `[REGENERATE_MODE] Company ${event.companyId} will be processed (regenerateIfExisting=true)`,
      );

      return {
        companyId: event.companyId,
        websiteUrl: event.websiteUrl,
        hasExistingProfile: true, // May or may not exist, but we're regenerating anyway
        shouldProcess: true,
        decisionReason: 'force_regenerate',
      };
    }

    // Query client-marketing-service for existing brand profile
    logger.info('Querying for existing brand profile', {
      companyId: event.companyId,
    });

    const existingProfile = await brandProfileClient.getBrandProfile(
      event.companyId,
    );

    const hasExistingProfile = existingProfile !== null;

    logger.info('Brand profile query completed', {
      companyId: event.companyId,
      hasExistingProfile,
      profileId: existingProfile?.id || null,
    });

    const shouldProcess = !hasExistingProfile;
    const processingTime = Date.now() - startTime;

    // Enhanced logging for decision tracking
    if (shouldProcess) {
      logger.info(
        `[WILL_PROCESS] Company ${event.companyId} - No existing profile found`,
        {
          companyId: event.companyId,
          websiteUrl: event.websiteUrl,
          decision: 'process',
          reason: 'no_existing_profile',
          processingTimeMs: processingTime,
        },
      );
    } else {
      logger.info(
        `[SKIP] Company ${event.companyId} - Existing profile found`,
        {
          companyId: event.companyId,
          websiteUrl: event.websiteUrl,
          decision: 'skip',
          reason: 'existing_profile',
          processingTimeMs: processingTime,
          profileId: existingProfile?.id,
        },
      );
    }

    return {
      companyId: event.companyId,
      websiteUrl: event.websiteUrl,
      hasExistingProfile,
      shouldProcess,
      decisionReason: shouldProcess
        ? 'no_existing_profile'
        : 'existing_profile_found',
    };
  } catch (error) {
    logger.error('Error checking existing brand profile:', {
      error: error instanceof Error ? error.message : String(error),
      errorType: error?.constructor?.name,
      stack: error instanceof Error ? error.stack : undefined,
      companyId: event.companyId,
      websiteUrl: event.websiteUrl,
      processingTimeMs: Date.now() - startTime,
    });

    // Check if it's a transient error
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      const isTransientError =
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('econnrefused') ||
        message.includes('enotfound') ||
        message.includes('etimedout') ||
        message.includes('socket hang up') ||
        message.includes('429') || // Too many requests
        message.includes('502') || // Bad gateway
        message.includes('503') || // Service unavailable
        message.includes('504'); // Gateway timeout

      if (isTransientError) {
        throw new TransientCheckBrandProfileError(
          `Transient error checking brand profile for company ${event.companyId}`,
          error,
          { companyId: event.companyId, websiteUrl: event.websiteUrl },
        );
      }
    }

    // Non-transient error
    throw new CheckBrandProfileError(
      `Failed to check brand profile for company ${event.companyId}`,
      error instanceof Error ? error : new Error(String(error)),
      { companyId: event.companyId, websiteUrl: event.websiteUrl },
    );
  }
};
