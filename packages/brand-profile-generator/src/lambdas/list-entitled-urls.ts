import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Handler } from 'aws-lambda';

import { ApiGatewayClient } from '../clients/ApiGatewayClient';
import { TenantClient } from '../clients/TenantClient';
import { getEnvironmentConfig } from '../config/implementation';
import {
  ListEntitledUrlsError,
  TransientListEntitledUrlsError,
} from '../errors';
import { createLogger } from '../utils/logger';
import { normalizeWebsiteUrl } from '../utils/urlNormalization';

const config = getEnvironmentConfig();

// Validate S3 configuration at startup
if (!config.brandProfilesS3 && process.env.NODE_ENV !== 'test') {
  throw new Error(
    'brandProfilesS3 configuration is required. Set BRAND_PROFILES_S3_BUCKET_NAME environment variable.',
  );
}

const s3Client = new S3Client({
  region: config.brandProfilesS3?.region || 'us-east-1',
});

export interface ListEntitledUrlsInput {
  companyIds?: string[];
  regenerateIfExisting?: boolean;
  dryRun?: boolean;
  executionName?: string;
  executionStartTime?: string;
}

export interface CompanyWebsite {
  companyId: string;
  websiteUrl: string;
}

export interface ListEntitledUrlsOutput {
  s3Bucket: string;
  s3Key: string;
  count: number;
  companyWebsites: CompanyWebsite[];
  regenerateIfExisting: boolean;
  dryRun: boolean;
  executionName?: string;
  executionStartTime?: string;
  executionFolderName?: string; // executionName with dryRunSuffix appended
}

export const handler: Handler<
  ListEntitledUrlsInput,
  ListEntitledUrlsOutput
> = async event => {
  const logger = createLogger('list-entitled-urls');
  logger.info('ListEntitledUrls Lambda invoked', { event });

  try {
    let companyWebsites: CompanyWebsite[] = [];

    // Validate required API Gateway configuration
    const missingFields: string[] = [];

    if (!config.apiGateway) {
      missingFields.push('apiGateway');
    } else {
      if (!config.apiGateway.url?.trim()) {
        missingFields.push('apiGateway.url');
      }
      if (!config.apiGateway.superUser?.trim()) {
        missingFields.push('apiGateway.superUser');
      }
      if (!config.apiGateway.apiGatewayKey?.trim()) {
        missingFields.push('apiGateway.apiGatewayKey');
      }
    }

    if (missingFields.length > 0) {
      const errorMessage = `Missing required configuration fields: ${missingFields.join(', ')}. Please ensure these environment variables are set: ${missingFields
        .map(field => {
          if (field === 'apiGateway.url') return 'API_GATEWAY_URL';
          if (field === 'apiGateway.superUser')
            return 'API_GATEWAY_SUPER_USER_COMPANY_ID';
          if (field === 'apiGateway.apiGatewayKey') return 'API_GATEWAY_KEY';
          return field;
        })
        .join(', ')}`;

      logger.error('Configuration validation failed', {
        missingFields,
        errorMessage,
      });

      throw new ListEntitledUrlsError(
        errorMessage,
        new Error('Invalid configuration'),
        { missingFields },
      );
    }

    // Initialize clients after validation
    const apiGatewayClient = new ApiGatewayClient(
      config.apiGateway.url,
      config.apiGateway.superUser,
      config.apiGateway.apiGatewayKey,
      logger,
    );

    if (event.companyIds && event.companyIds.length > 0) {
      // Use hybrid approach: get company data from tenant-service first, then enrich with API Gateway
      logger.info(
        'Using provided company IDs with hybrid approach (tenant-service + API Gateway)',
        {
          count: event.companyIds.length,
        },
      );

      // Validate required Tenant configuration for hybrid approach
      const tenantMissingFields: string[] = [];
      if (!config.tenantApi) {
        tenantMissingFields.push('tenantApi');
      } else {
        if (!config.tenantApi.url?.trim()) {
          tenantMissingFields.push('tenantApi.url');
        }
        if (
          !config.tenantApi.productIds ||
          config.tenantApi.productIds.length === 0
        ) {
          tenantMissingFields.push('tenantApi.productIds');
        }
      }

      if (tenantMissingFields.length > 0) {
        logger.warn(
          'Tenant configuration missing for hybrid approach, falling back to API Gateway only',
          {
            missingFields: tenantMissingFields,
          },
        );
        // Fallback to API Gateway only
        const entitlements = event.companyIds.map(id => ({ companyId: id }));
        const results = await apiGatewayClient.fetchAllWebsites(entitlements);
        companyWebsites = results.map(r => ({
          companyId: r.companyId,
          websiteUrl: normalizeWebsiteUrl(r.websiteUrl),
        }));
      } else {
        // Initialize tenant client
        const tenantClient = new TenantClient(
          config.tenantApi.url,
          config.tenantApi.productIds,
          logger,
        );

        // Get all entitled companies from tenant-service
        const allEntitledCompanies = await tenantClient.getEntitledCompanies();

        // Filter to only the requested company IDs and include tenant-service website data
        const requestedCompanies = event.companyIds.map(companyId => {
          const entitledCompany = allEntitledCompanies.find(
            c => c.companyId === companyId,
          );
          return {
            companyId,
            websiteUrl: entitledCompany?.websiteUrl,
          };
        });

        logger.info(
          'Fetched company data from tenant-service for hybrid approach',
          {
            requestedCompanies: requestedCompanies.length,
            companiesWithTenantWebsites: requestedCompanies.filter(
              c => c.websiteUrl,
            ).length,
          },
        );

        // Enrich with API Gateway data
        const results =
          await apiGatewayClient.fetchAllWebsites(requestedCompanies);
        companyWebsites = results.map(r => ({
          companyId: r.companyId,
          websiteUrl: normalizeWebsiteUrl(r.websiteUrl),
        }));
      }

      logger.info('Hybrid approach results for companyIds', {
        totalCompanies: event.companyIds.length,
        companiesWithWebsites: companyWebsites.length,
        coverage: Math.round(
          (companyWebsites.length / event.companyIds.length) * 100,
        ),
      });

      // Log raw results before filtering for companyIds path
      logger.info('Raw results from hybrid approach (companyIds path)', {
        totalResults: companyWebsites.length,
        samples: companyWebsites.slice(0, 3).map(r => ({
          hasCompanyId: !!r.companyId,
          hasWebsiteUrl: !!r.websiteUrl,
          data: r,
        })),
      });
    } else {
      // Query entitled companies from tenant service
      logger.info('Fetching entitled companies from tenant service');

      // Validate required Tenant configuration
      const tenantMissingFields: string[] = [];

      if (!config.tenantApi) {
        tenantMissingFields.push('tenantApi');
      } else {
        if (!config.tenantApi.url?.trim()) {
          tenantMissingFields.push('tenantApi.url');
        }
        if (
          !config.tenantApi.productIds ||
          config.tenantApi.productIds.length === 0
        ) {
          tenantMissingFields.push('tenantApi.productIds');
        }
      }

      if (tenantMissingFields.length > 0) {
        const errorMessage = `Missing required tenant configuration fields: ${tenantMissingFields.join(', ')}. Please ensure these environment variables are set: ${tenantMissingFields
          .map(field => {
            if (field === 'tenantApi.url') return 'TENANT_SERVICE_URL';
            if (field === 'tenantApi.productIds') return 'PRODUCT_IDS';
            return field;
          })
          .join(', ')}`;

        logger.error('Tenant configuration validation failed', {
          missingFields: tenantMissingFields,
          errorMessage,
        });

        throw new ListEntitledUrlsError(
          errorMessage,
          new Error('Invalid tenant configuration'),
          { missingFields: tenantMissingFields },
        );
      }

      const tenantClient = new TenantClient(
        config.tenantApi.url,
        config.tenantApi.productIds,
        logger,
      );

      // Get entitled companies from tenant service (includes website data)
      const entitledCompanies = await tenantClient.getEntitledCompanies();
      logger.info(
        'Fetched entitled companies with tenant-service website data',
        {
          count: entitledCompanies.length,
          companiesWithTenantWebsites: entitledCompanies.filter(
            c => c.websiteUrl,
          ).length,
        },
      );

      // Validate that all entitled companies have companyId
      const invalidCompanies = entitledCompanies.filter(c => !c.companyId);
      if (invalidCompanies.length > 0) {
        logger.error('Entitled companies missing companyId', {
          invalidCount: invalidCompanies.length,
          samples: invalidCompanies.slice(0, 3),
        });
        throw new ListEntitledUrlsError(
          `Found ${invalidCompanies.length} entitled companies without companyId`,
          new Error('Invalid entitlement data'),
          { invalidCompanies: invalidCompanies.slice(0, 5) },
        );
      }

      // Enrich/overwrite with API Gateway websites (hybrid approach)
      const results =
        await apiGatewayClient.fetchAllWebsites(entitledCompanies);

      companyWebsites = results.map(r => ({
        companyId: r.companyId,
        websiteUrl: normalizeWebsiteUrl(r.websiteUrl),
      }));

      logger.info('Hybrid approach results for entitled companies', {
        totalEntitled: entitledCompanies.length,
        companiesWithWebsites: companyWebsites.length,
        coverage: Math.round(
          (companyWebsites.length / entitledCompanies.length) * 100,
        ),
        tenantServiceCoverage: Math.round(
          (entitledCompanies.filter(c => c.websiteUrl).length /
            entitledCompanies.length) *
            100,
        ),
      });

      // Log raw results before filtering for entitled companies path
      logger.info(
        'Raw results from hybrid approach (entitled companies path)',
        {
          totalResults: companyWebsites.length,
          samples: companyWebsites.slice(0, 3).map(r => ({
            hasCompanyId: !!r.companyId,
            hasWebsiteUrl: !!r.websiteUrl,
            data: r,
          })),
        },
      );
    }

    // Validate that all entries have required fields
    const invalidEntries = companyWebsites.filter(
      cw => !cw.companyId || !cw.websiteUrl,
    );

    if (invalidEntries.length > 0) {
      logger.error('Invalid entries found missing companyId or websiteUrl', {
        invalidCount: invalidEntries.length,
        samples: invalidEntries.slice(0, 3),
      });

      // Filter out invalid entries
      companyWebsites = companyWebsites.filter(
        cw => cw.companyId && cw.websiteUrl,
      );
    }

    logger.info('Collected company websites', {
      count: companyWebsites.length,
      sample: companyWebsites.slice(0, 3), // Log first 3 for debugging
      hasCompanyIds: companyWebsites.every(cw => cw.companyId),
      hasWebsiteUrls: companyWebsites.every(cw => cw.websiteUrl),
    });

    // Use the Step Functions bucket for storing the input
    const s3Bucket =
      process.env.STEP_FUNCTION_S3_BUCKET ||
      config.brandProfilesS3?.bucketName ||
      `brand-profile-generator-${process.env.ENVIRONMENT || 'dev'}`;

    // Create S3 key in the format expected by the Map state
    // If executionName and executionStartTime are provided, use them for the path
    // Otherwise, fall back to a timestamp-based path
    // Add dry-run suffix for easy identification
    const isDryRun = event.dryRun ?? true;
    const dryRunSuffix = isDryRun ? '-dry-run' : '';

    let s3Key: string;
    if (event.executionName && event.executionStartTime) {
      // Extract date from execution start time (format: YYYY-MM-DDTHH:MM:SS.sssZ)
      const executionDate = event.executionStartTime.split('T')[0];
      s3Key = `brand-profile-jobs/${executionDate}/${event.executionName}${dryRunSuffix}/input${dryRunSuffix}.json`;
    } else {
      // Fallback for direct invocation or testing
      const isoTimestamp = new Date().toISOString().replace(/:/g, '-');
      const dateOnly = isoTimestamp.split('T')[0];
      s3Key = `brand-profile-jobs/${dateOnly}/direct-${isoTimestamp}${dryRunSuffix}/input${dryRunSuffix}.json`;
    }

    // Write companyWebsites array to S3 in the format expected by Map state
    // Map state expects a direct array, not an object
    // Store metadata in S3 object tags instead of the JSON body
    logger.info('Writing to S3', {
      bucket: s3Bucket,
      key: s3Key,
      dataStructure: companyWebsites.slice(0, 2).map(cw => ({
        hasCompanyId: !!cw.companyId,
        hasWebsiteUrl: !!cw.websiteUrl,
        companyId: cw.companyId,
        websiteUrl: cw.websiteUrl,
      })),
    });

    // Final validation before writing to S3
    const dataToWrite = companyWebsites;
    const jsonData = JSON.stringify(dataToWrite);

    logger.info('Final S3 write details', {
      bucket: s3Bucket,
      key: s3Key,
      arrayLength: dataToWrite.length,
      jsonLength: jsonData.length,
      firstTwoItems: dataToWrite.slice(0, 2),
      allHaveCompanyId: dataToWrite.every(item => item.companyId),
      allHaveWebsiteUrl: dataToWrite.every(item => item.websiteUrl),
    });

    await s3Client.send(
      new PutObjectCommand({
        Bucket: s3Bucket,
        Key: s3Key,
        Body: jsonData, // Direct array for Map state
        ContentType: 'application/json',
        // Store metadata as tags for traceability
        Tagging: `dryRun=${isDryRun}&environment=${process.env.ENVIRONMENT || 'dev'}&count=${companyWebsites.length}&regenerateIfExisting=${event.regenerateIfExisting || false}`,
        Metadata: {
          dryRun: String(isDryRun),
          generatedAt: new Date().toISOString(),
          executionName: event.executionName || '',
          executionStartTime: event.executionStartTime || '',
          regenerateIfExisting: String(event.regenerateIfExisting || false),
          environment: process.env.ENVIRONMENT || 'dev',
          count: String(companyWebsites.length),
        },
      }),
    );

    return {
      s3Bucket,
      s3Key,
      count: companyWebsites.length,
      companyWebsites,
      regenerateIfExisting: event.regenerateIfExisting || false,
      dryRun: isDryRun, // Default to true (safe mode)
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
      executionFolderName: event.executionName
        ? `${event.executionName}${dryRunSuffix}`
        : undefined, // For Step Functions ResultWriter
    };
  } catch (error) {
    console.error('Error in list-entitled-urls:', error);

    // Check if it's a transient error
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('throttl') ||
        message.includes('429') ||
        message.includes('503') ||
        message.includes('502') ||
        message.includes('s3 service')
      ) {
        throw new TransientListEntitledUrlsError(
          `Transient error while listing entitled URLs`,
          error,
          { companyIds: event.companyIds, executionName: event.executionName },
        );
      }
    }

    // Non-transient error
    throw new ListEntitledUrlsError(
      `Failed to list entitled URLs`,
      error instanceof Error ? error : new Error(String(error)),
      { companyIds: event.companyIds, executionName: event.executionName },
    );
  }
};
