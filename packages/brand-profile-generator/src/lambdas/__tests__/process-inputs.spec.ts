import { Context } from 'aws-lambda';

import { ProcessInputsError } from '../../errors';
import { handler } from '../process-inputs';

describe('ProcessInputs Lambda', () => {
  const mockContext: Context = {
    functionName: 'process-inputs',
    functionVersion: '1',
    invokedFunctionArn:
      'arn:aws:lambda:us-east-1:123456789012:function:process-inputs',
    memoryLimitInMB: '512',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/process-inputs',
    logStreamName: '2024/01/01/[$LATEST]test',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
    callbackWaitsForEmptyEventLoop: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Input Processing', () => {
    it('should apply all defaults when no inputs provided', async () => {
      const result = await handler({}, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: [],
        regenerateIfExisting: false,
        dryRun: true,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should use provided values when all inputs are specified', async () => {
      const event = {
        companyIds: ['company-1', 'company-2'],
        regenerateIfExisting: true,
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: ['company-1', 'company-2'],
        regenerateIfExisting: true,
        dryRun: false,
        executionName: 'test-execution',
        executionStartTime: '2024-01-01T00:00:00Z',
      });
    });

    it('should handle partial inputs correctly', async () => {
      const event = {
        companyIds: ['company-1'],
        dryRun: false,
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: ['company-1'],
        regenerateIfExisting: false, // Default
        dryRun: false,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should handle false boolean values correctly', async () => {
      const event = {
        regenerateIfExisting: false,
        dryRun: false,
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: [],
        regenerateIfExisting: false,
        dryRun: false,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });

    it('should handle true boolean values correctly', async () => {
      const event = {
        regenerateIfExisting: true,
        dryRun: true,
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: [],
        regenerateIfExisting: true,
        dryRun: true,
        executionName: undefined,
        executionStartTime: undefined,
      });
    });
  });

  describe('Input Validation', () => {
    it('should throw ProcessInputsError if companyIds is not an array', async () => {
      const event = {
        companyIds: 'not-an-array' as any,
      };

      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        ProcessInputsError,
      );
      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        'companyIds must be an array',
      );
    });

    it('should throw ProcessInputsError if regenerateIfExisting is not a boolean', async () => {
      const event = {
        regenerateIfExisting: 'not-a-boolean' as any,
      };

      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        ProcessInputsError,
      );
      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        'regenerateIfExisting must be a boolean',
      );
    });

    it('should throw ProcessInputsError if dryRun is not a boolean', async () => {
      const event = {
        dryRun: 'not-a-boolean' as any,
      };

      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        ProcessInputsError,
      );
      await expect(handler(event, mockContext, jest.fn())).rejects.toThrow(
        'dryRun must be a boolean',
      );
    });

    it('should accept empty company IDs array', async () => {
      const event = {
        companyIds: [],
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result!.companyIds).toEqual([]);
    });

    it('should accept company IDs with multiple entries', async () => {
      const event = {
        companyIds: ['company-1', 'company-2', 'company-3'],
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result!.companyIds).toEqual([
        'company-1',
        'company-2',
        'company-3',
      ]);
    });
  });

  describe('Nested Input Structure (Step Functions)', () => {
    it('should handle nested input structure from Step Functions', async () => {
      const event = {
        input: {
          companyIds: ['company-1', 'company-2'],
          regenerateIfExisting: true,
          dryRun: false,
        },
        executionName: 'step-function-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: ['company-1', 'company-2'],
        regenerateIfExisting: true,
        dryRun: false,
        executionName: 'step-function-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      });
    });

    it('should apply defaults with nested input structure', async () => {
      const event = {
        input: {},
        executionName: 'step-function-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: [],
        regenerateIfExisting: false,
        dryRun: true,
        executionName: 'step-function-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      });
    });

    it('should handle partial nested input', async () => {
      const event = {
        input: {
          companyIds: ['company-1'],
        },
        executionName: 'partial-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: ['company-1'],
        regenerateIfExisting: false,
        dryRun: true,
        executionName: 'partial-execution',
        executionStartTime: '2024-01-01T12:00:00Z',
      });
    });

    it('should handle missing input field with execution context', async () => {
      const event = {
        executionName: 'no-input-field',
        executionStartTime: '2024-01-01T12:00:00Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result).toEqual({
        companyIds: [],
        regenerateIfExisting: false,
        dryRun: true,
        executionName: 'no-input-field',
        executionStartTime: '2024-01-01T12:00:00Z',
      });
    });
  });

  describe('Execution Context', () => {
    it('should pass through execution context parameters', async () => {
      const event = {
        executionName: 'brand-profile-2024-01-01',
        executionStartTime: '2024-01-01T12:00:00.000Z',
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result!.executionName).toBe('brand-profile-2024-01-01');
      expect(result!.executionStartTime).toBe('2024-01-01T12:00:00.000Z');
    });

    it('should handle missing execution context gracefully', async () => {
      const event = {
        companyIds: ['company-1'],
      };

      const result = await handler(event, mockContext, jest.fn());

      expect(result!.executionName).toBeUndefined();
      expect(result!.executionStartTime).toBeUndefined();
    });
  });
});
