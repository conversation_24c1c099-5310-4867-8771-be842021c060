import { Context } from 'aws-lambda';

import {
  handler,
  GetResearchJobStatusInput,
  GetResearchJobStatusOutput,
  ResearchData,
} from '../get-research-job-status';

describe('GetResearchJobStatus Lambda Handler', () => {
  let mockContext: Context;

  beforeEach(() => {
    jest.clearAllMocks();

    mockContext = {
      functionName: 'get-research-job-status',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:get-research-job-status',
      memoryLimitInMB: '512',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/get-research-job-status',
      logStreamName: '2024/01/01/[$LATEST]test-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      callbackWaitsForEmptyEventLoop: true,
    };

    process.env.OPENAI_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    delete process.env.OPENAI_API_KEY;
  });

  describe('Status checking', () => {
    it('should return completed status with research data', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345-company-123',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(result).toMatchObject({
        companyId: 'company-123',
        researchRequestId: 'request-12345-company-123',
        status: 'completed',
        researchData: expect.objectContaining({
          content: expect.any(String),
          searchResults: expect.any(Array),
          usage: expect.objectContaining({
            prompt_tokens: expect.any(Number),
            completion_tokens: expect.any(Number),
            total_tokens: expect.any(Number),
          }),
        }),
      });
    });

    it('should return valid status values', async () => {
      const validStatuses = [
        'validating',
        'queued',
        'in_progress',
        'finalizing',
        'completed',
        'failed',
        'expired',
        'cancelling',
        'cancelled',
      ];

      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(validStatuses).toContain(result.status);
    });

    it('should include research data when status is completed', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-completed',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      // In dry run implementation, always returns completed
      expect(result.status).toBe('completed');
      expect(result.researchData).toBeDefined();
      expect(result.researchData?.content).toBeTruthy();
      expect(result.researchData?.searchResults).toBeInstanceOf(Array);
    });
  });

  describe('Research data structure', () => {
    it('should return properly structured research data', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      const researchData = result.researchData as ResearchData;

      // Check content
      expect(researchData.content).toBeDefined();
      expect(typeof researchData.content).toBe('string');

      // Check search results (optional field)
      if (researchData.searchResults) {
        expect(researchData.searchResults).toBeInstanceOf(Array);
        if (researchData.searchResults.length > 0) {
          const firstResult = researchData.searchResults[0];
          expect(firstResult).toHaveProperty('title');
          expect(firstResult).toHaveProperty('url');
          // date and snippet are optional
        }
      }

      // Check usage metrics
      expect(researchData.usage).toBeDefined();
      expect(researchData.usage?.prompt_tokens).toBeGreaterThanOrEqual(0);
      expect(researchData.usage?.completion_tokens).toBeGreaterThanOrEqual(0);
      expect(researchData.usage?.total_tokens).toBeGreaterThanOrEqual(0);
    });

    it('should handle search results with optional date field', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      if (
        result.researchData?.searchResults &&
        result.researchData.searchResults.length > 0
      ) {
        const searchResult = result.researchData.searchResults[0];

        // Date is optional
        if (searchResult.date) {
          expect(typeof searchResult.date).toBe('string');
        }
      }
    });
  });

  describe('Request ID handling', () => {
    it('should preserve the original request ID', async () => {
      const requestIds = [
        'request-12345-company-abc',
        'request-67890-company-xyz',
        'simple-request-id',
        'complex-request-id-with-many-segments-123-456',
      ];

      for (const requestId of requestIds) {
        const event: GetResearchJobStatusInput = {
          companyId: 'test-company',
          researchRequestId: requestId,
          dryRun: true,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as GetResearchJobStatusOutput;

        expect(result.researchRequestId).toBe(requestId);
      }
    });

    it('should handle various request ID formats', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'req_aBc123XyZ-456_special',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(result.researchRequestId).toBe('req_aBc123XyZ-456_special');
      expect(result.companyId).toBe('company-123');
    });
  });

  describe('Company ID handling', () => {
    it('should preserve company ID through the status check', async () => {
      const companyIds = [
        'simple-company',
        'company_with_underscore',
        'company.with.dots',
        'UPPERCASE-COMPANY',
        'company-123-456',
      ];

      for (const companyId of companyIds) {
        const event: GetResearchJobStatusInput = {
          companyId,
          researchRequestId: 'request-12345',
          dryRun: true,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as GetResearchJobStatusOutput;

        expect(result.companyId).toBe(companyId);
      }
    });
  });

  describe('Edge cases', () => {
    it('should handle very long request IDs', async () => {
      const longRequestId = 'request-' + 'a'.repeat(500);
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: longRequestId,
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(result.researchRequestId).toBe(longRequestId);
    });

    it('should handle empty company ID', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: '',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(result.companyId).toBe('');
    });

    it('should handle special characters in IDs', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-@#$%',
        researchRequestId: 'request-!@#$%^&*()',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      expect(result.companyId).toBe('company-@#$%');
      expect(result.researchRequestId).toBe('request-!@#$%^&*()');
    });
  });

  describe('Performance', () => {
    it('should complete quickly', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const startTime = Date.now();
      await handler(event, mockContext, () => {});
      const endTime = Date.now();

      // Should complete very quickly in placeholder mode
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should handle concurrent status checks', async () => {
      const events: GetResearchJobStatusInput[] = Array.from(
        { length: 10 },
        (_, i) => ({
          companyId: `company-${i}`,
          researchRequestId: `request-${i}`,
          dryRun: true,
        }),
      );

      const results = await Promise.all(
        events.map(
          event =>
            handler(
              event,
              mockContext,
              () => {},
            ) as Promise<GetResearchJobStatusOutput>,
        ),
      );

      expect(results).toHaveLength(10);

      // Each should have matching IDs
      results.forEach((result, index) => {
        expect(result.companyId).toBe(`company-${index}`);
        expect(result.researchRequestId).toBe(`request-${index}`);
      });
    });
  });

  describe('Usage metrics', () => {
    it('should return valid token counts', async () => {
      const event: GetResearchJobStatusInput = {
        companyId: 'company-123',
        researchRequestId: 'request-12345',
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as GetResearchJobStatusOutput;

      if (result.researchData?.usage) {
        const { prompt_tokens, completion_tokens, total_tokens } =
          result.researchData.usage;

        expect(prompt_tokens).toBeGreaterThanOrEqual(0);
        expect(completion_tokens).toBeGreaterThanOrEqual(0);
        expect(total_tokens).toBe(prompt_tokens + completion_tokens);
      }
    });
  });
});
