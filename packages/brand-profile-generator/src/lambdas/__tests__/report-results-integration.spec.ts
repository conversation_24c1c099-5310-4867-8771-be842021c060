import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';

jest.mock('@aws-sdk/client-s3');

jest.mock('../../config/implementation', () => ({
  getEnvironmentConfig: jest.fn(() => ({
    brandProfilesS3: {
      bucketName: 'test-brand-profiles-bucket',
      region: 'us-east-1',
    },
    environment: 'test',
    slack: {
      enabled: false,
      webhookUrl: '',
    },
  })),
}));

import {
  handler,
  ReportResultsInput,
  ReportResultsOutput,
  ProcessedCompany,
} from '../report-results';

const MockedS3Client = S3Client as jest.MockedClass<typeof S3Client>;

describe('report-results - Integration Tests', () => {
  const mockSend = jest.fn();
  const originalLog = console.log;
  const mockConsoleLog = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    console.log = mockConsoleLog;
    MockedS3Client.prototype.send = mockSend;
    mockSend.mockResolvedValue({});
  });

  afterEach(() => {
    console.log = originalLog;
  });

  describe('Simulating Real Step Functions Map State Output', () => {
    it('should handle Map state with distributed processing ResultWriter format', async () => {
      // Simulate the actual output from a distributed Map state with ResultWriter
      // This is what AWS Step Functions actually passes to the lambda
      const mapStateOutput = {
        ResultWriterDetails: {
          Bucket: 'brand-profile-generator-dev',
          Key: 'brand-profile-jobs/2024-01-15/execution-123/',
        },
      };

      // Simulate multiple result files written by Map state
      const mockResults: ProcessedCompany[] = [
        {
          companyId: 'company-1',
          websiteUrl: 'https://example1.com',
          hasExistingProfile: false,
          shouldProcess: true,
          decisionReason: 'successfully_processed',
          s3Location: 's3://bucket/company-1-profile.json',
        },
        {
          companyId: 'company-2',
          websiteUrl: 'https://example2.com',
          hasExistingProfile: true,
          shouldProcess: false,
          decisionReason: 'existing_profile',
        },
        {
          companyId: 'company-3',
          websiteUrl: 'https://example3.com',
          hasExistingProfile: false,
          shouldProcess: false,
          decisionReason: 'processing_failed',
          error: { message: 'Research API timeout' },
        },
      ];

      // Create a proper manifest structure
      const manifest = {
        DestinationBucket: 'brand-profile-generator-dev',
        MapRunArn:
          'arn:aws:states:us-east-1:123456789012:mapRun:brand-profile-generator-dev/test-run',
        ResultFiles: {
          SUCCEEDED: [
            {
              Key: 'brand-profile-jobs/2024-01-15/execution-123/SUCCEEDED_0.json',
              Size: 1000,
            },
          ],
          FAILED: [],
          PENDING: [],
        },
      };

      // Mock S3 operations
      mockSend
        // First, mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [
            { Prefix: 'brand-profile-jobs/2024-01-15/execution-123/uuid-123/' },
          ],
        })
        // Then, mock reading the manifest.json from UUID folder
        .mockResolvedValueOnce({
          Body: {
            transformToString: () => Promise.resolve(JSON.stringify(manifest)),
          },
        })
        // Then mock reading the SUCCEEDED_0.json file which contains all results
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockResults)),
          },
        })
        // Mock the final S3 put for the report
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'execution-123',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        regenerateIfExisting: false,
        results: mapStateOutput as any,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      // Verify the lambda correctly processed the Map state output
      expect(result.executionSummary.totalCompanies).toBe(3);
      expect(result.executionSummary.processedCount).toBe(1);
      expect(result.executionSummary.skippedCount).toBe(1);
      expect(result.executionSummary.failedCount).toBe(1);

      // Verify S3 operations
      expect(mockSend).toHaveBeenCalledWith(expect.any(GetObjectCommand)); // manifest read
      expect(mockSend).toHaveBeenCalledTimes(4); // 1 list + 1 manifest get + 1 results get + 1 put

      // Verify logging for debugging
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Results written to S3:'),
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Total results to process: 3',
      );
    });

    it('should handle Map state with no results (empty execution)', async () => {
      // When Map processes 0 items, ResultWriter still creates the prefix
      const mapStateOutput = {
        ResultWriterDetails: {
          Bucket: 'brand-profile-generator-dev',
          Key: 'brand-profile-jobs/2024-01-15/execution-empty/',
        },
      };

      // Create an empty manifest
      const emptyManifest = {
        DestinationBucket: 'brand-profile-generator-dev',
        MapRunArn:
          'arn:aws:states:us-east-1:123456789012:mapRun:brand-profile-generator-dev/test-empty',
        ResultFiles: {
          SUCCEEDED: [],
          FAILED: [],
          PENDING: [],
        },
      };

      // Mock S3 operations for empty results
      mockSend
        // First, mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [
            {
              Prefix: 'brand-profile-jobs/2024-01-15/execution-empty/uuid-456/',
            },
          ],
        })
        // Mock reading the manifest.json with empty results
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(emptyManifest)),
          },
        })
        // Mock the final S3 put for the report
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'execution-empty',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: mapStateOutput as any,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.totalCompanies).toBe(0);
      expect(result.executionSummary.processedCount).toBe(0);
      expect(result.executionSummary.skippedCount).toBe(0);
      expect(result.executionSummary.failedCount).toBe(0);

      // Verify appropriate logging for empty manifest
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Found manifest.json:'),
        expect.any(String),
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Successfully processed manifest, found 0 total results',
      );
    });

    it('should handle Map state with single consolidated result file', async () => {
      // Some Map configurations might write a single consolidated file
      const mapStateOutput = {
        ResultWriterDetails: {
          Bucket: 'brand-profile-generator-dev',
          Key: 'brand-profile-jobs/2024-01-15/execution-456/results.json',
        },
      };

      const mockResults: ProcessedCompany[] = [
        {
          companyId: 'company-1',
          websiteUrl: 'https://example1.com',
          hasExistingProfile: false,
          shouldProcess: true,
          s3Location: 's3://bucket/company-1.json',
        },
        {
          companyId: 'company-2',
          websiteUrl: 'https://example2.com',
          hasExistingProfile: false,
          shouldProcess: true,
          s3Location: 's3://bucket/company-2.json',
        },
      ];

      // Create manifest with single consolidated result
      const singleFileManifest = {
        DestinationBucket: 'brand-profile-generator-dev',
        MapRunArn:
          'arn:aws:states:us-east-1:123456789012:mapRun:brand-profile-generator-dev/test-single',
        ResultFiles: {
          SUCCEEDED: [
            {
              Key: 'brand-profile-jobs/2024-01-15/execution-456/SUCCEEDED_0.json',
              Size: 500,
            },
          ],
          FAILED: [],
          PENDING: [],
        },
      };

      // Mock S3 operations
      mockSend
        // First, mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [
            { Prefix: 'brand-profile-jobs/2024-01-15/execution-456/uuid-789/' },
          ],
        })
        // Mock reading the manifest
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(singleFileManifest)),
          },
        })
        // Mock reading the single results file
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockResults)),
          },
        })
        // Mock the final S3 put for the report
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'execution-456',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: mapStateOutput as any,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.totalCompanies).toBe(2);
      expect(result.executionSummary.processedCount).toBe(2);

      // Verify it processed through manifest
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Processing 1 succeeded results from manifest'),
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Found 2 results in brand-profile-jobs/2024-01-15/execution-456/SUCCEEDED_0.json',
      );
    });

    it('should handle Map state with both SUCCEEDED and FAILED results', async () => {
      const successResults: ProcessedCompany[] = [
        {
          companyId: 'company-1',
          websiteUrl: 'https://example1.com',
          hasExistingProfile: false,
          shouldProcess: true,
          s3Location: 's3://bucket/company-1-profile.json',
        },
        {
          companyId: 'company-2',
          websiteUrl: 'https://example2.com',
          hasExistingProfile: true,
          shouldProcess: false,
        },
      ];

      // Step Functions Map state failure format
      const failedResults = [
        {
          Input: {
            companyId: 'company-3',
            websiteUrl: 'https://example3.com',
          },
          ErrorMessage: 'Lambda function timeout',
          Cause: 'Task timed out after 900.00 seconds',
        },
      ];

      const manifest = {
        DestinationBucket: 'brand-profile-generator-dev',
        MapRunArn:
          'arn:aws:states:us-east-1:123456789012:mapRun:brand-profile-generator-dev/test-mixed',
        ResultFiles: {
          SUCCEEDED: [
            {
              Key: 'brand-profile-jobs/2024-01-15/execution-mixed/SUCCEEDED_0.json',
              Size: 1000,
            },
          ],
          FAILED: [
            {
              Key: 'brand-profile-jobs/2024-01-15/execution-mixed/FAILED_0.json',
              Size: 500,
            },
          ],
          PENDING: [],
        },
      };

      mockSend
        // First, mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [
            {
              Prefix: 'brand-profile-jobs/2024-01-15/execution-mixed/uuid-abc/',
            },
          ],
        })
        // Mock reading the manifest
        .mockResolvedValueOnce({
          Body: {
            transformToString: () => Promise.resolve(JSON.stringify(manifest)),
          },
        })
        // Mock reading the SUCCEEDED file
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(successResults)),
          },
        })
        // Mock reading the FAILED file
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(failedResults)),
          },
        })
        // Mock the final S3 put for the report
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'execution-mixed',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: {
          ResultWriterDetails: {
            Bucket: 'brand-profile-generator-dev',
            Key: 'brand-profile-jobs/2024-01-15/execution-mixed/',
          },
        } as any,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      // Verify the results include both succeeded and failed
      expect(result.executionSummary.totalCompanies).toBe(3);
      expect(result.executionSummary.processedCount).toBe(1);
      expect(result.executionSummary.skippedCount).toBe(1);
      expect(result.executionSummary.failedCount).toBe(1);

      // Verify failed company details
      const failedCompany = result.companySummaries.find(
        c => c.companyId === 'company-3',
      );
      expect(failedCompany).toBeDefined();
      expect(failedCompany?.status).toBe('failed');
      expect(failedCompany?.reason).toContain('Lambda function timeout');

      // Verify logging
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Processing 1 succeeded results from manifest',
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Processing 1 failed results from manifest',
      );
    });

    it('should provide detailed categorization logging', async () => {
      const mockResults: ProcessedCompany[] = [
        {
          companyId: 'company-success',
          websiteUrl: 'https://success.com',
          hasExistingProfile: false,
          shouldProcess: true,
          s3Location: 's3://bucket/company-success.json',
        },
        {
          companyId: 'company-skip',
          websiteUrl: 'https://skip.com',
          hasExistingProfile: true,
          shouldProcess: false,
        },
        {
          companyId: 'company-fail',
          websiteUrl: 'https://fail.com',
          hasExistingProfile: false,
          shouldProcess: false,
          decisionReason: 'processing_failed',
          error: { message: 'Connection timeout' },
        },
      ];

      const event: ReportResultsInput = {
        executionName: 'debug-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: mockResults,
      };

      await handler(event, {} as any, {} as any);

      // Verify detailed categorization logging
      expect(mockConsoleLog).toHaveBeenCalledWith('Categorizing results...');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Processed: company-success - created and saved to s3://bucket/company-success.json',
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Skipped: company-skip - existing profile (not regenerated) (hasExistingProfile: true, shouldProcess: false, decisionReason: undefined)',
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Failed: company-fail - Connection timeout',
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Categorization complete: 1 processed, 1 skipped, 1 failed',
      );
    });
  });
});
