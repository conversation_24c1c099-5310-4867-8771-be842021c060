import { Context } from 'aws-lambda';

import {
  handler,
  CreateBrandProfileInput,
  CreateBrandProfileOutput,
} from '../create-brand-profile';

describe('CreateBrandProfile Lambda Handler', () => {
  let mockContext: Context;

  beforeEach(() => {
    jest.clearAllMocks();

    mockContext = {
      functionName: 'create-brand-profile',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:create-brand-profile',
      memoryLimitInMB: '1024',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/create-brand-profile',
      logStreamName: '2024/01/01/[$LATEST]test-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      callbackWaitsForEmptyEventLoop: true,
    };

    process.env.OPENAI_API_KEY = 'test-openai-key';
  });

  afterEach(() => {
    delete process.env.OPENAI_API_KEY;
  });

  describe('Brand profile creation', () => {
    it('should create brand profile from research data', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: {
          content: 'Company research content about brand values and mission',
          searchResults: [
            { title: 'Company Website', url: 'https://example.com' },
            { title: 'News Article', url: 'https://news.com/article' },
          ],
        },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result).toMatchObject({
        companyId: 'company-123',
        brandProfile: expect.objectContaining({
          companyId: 'company-123',
          generatedAt: expect.any(String),
        }),
      });
    });

    it('should include timestamp in generated profile', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: { content: 'Research content' },
        dryRun: true,
      };

      const beforeTime = new Date().toISOString();
      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;
      const afterTime = new Date().toISOString();

      expect(result.brandProfile.generatedAt).toBeDefined();
      expect(
        new Date(result.brandProfile.generatedAt).getTime(),
      ).toBeGreaterThanOrEqual(new Date(beforeTime).getTime());
      expect(
        new Date(result.brandProfile.generatedAt).getTime(),
      ).toBeLessThanOrEqual(new Date(afterTime).getTime());
    });

    it('should handle various research data formats', async () => {
      const testCases = [
        { content: 'Simple content' },
        { content: 'Content with special chars: @#$%^&*()' },
        {
          content: 'Long content ' + 'a'.repeat(10000),
          searchResults: Array(100).fill({
            title: 'Result',
            url: 'https://example.com',
          }),
        },
        { content: '' }, // Empty content
      ];

      for (const researchData of testCases) {
        const event: CreateBrandProfileInput = {
          companyId: 'test-company',
          researchData,
          dryRun: true,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as CreateBrandProfileOutput;

        expect(result.companyId).toBe('test-company');
        expect(result.brandProfile).toBeDefined();
      }
    });
  });

  describe('Company ID handling', () => {
    it('should preserve company ID in brand profile', async () => {
      const companyIds = [
        'simple-id',
        'company_with_underscore',
        'company.with.dots',
        'UPPERCASE-ID',
        '123-numeric',
      ];

      for (const companyId of companyIds) {
        const event: CreateBrandProfileInput = {
          companyId,
          researchData: { content: 'Research content' },
          dryRun: true,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as CreateBrandProfileOutput;

        expect(result.companyId).toBe(companyId);
        expect(result.brandProfile.companyId).toBe(companyId);
      }
    });
  });

  describe('Research data processing', () => {
    it('should handle research data with search results', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: {
          content: 'Brand research content',
          searchResults: [
            {
              title: 'Result 1',
              url: 'https://example1.com',
              date: '2024-01-01',
            },
            { title: 'Result 2', url: 'https://example2.com' },
            {
              title: 'Result 3',
              url: 'https://example3.com',
              date: '2024-01-02',
            },
          ],
          usage: {
            prompt_tokens: 100,
            completion_tokens: 500,
            total_tokens: 600,
          },
        },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
      // The actual implementation would process search results into the brand profile
    });

    it('should handle research data without search results', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: {
          content: 'Brand research content only',
        },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
    });

    it('should handle complex nested research data', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: {
          content: JSON.stringify({
            brand: 'Example Brand',
            values: ['Innovation', 'Quality'],
            mission: 'To provide excellent service',
          }),
          sources: [
            {
              url: 'https://example.com',
              title: 'Research Source',
            },
          ],
        },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
    });
  });

  describe('Edge cases', () => {
    it('should handle empty research data content', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: { content: '' },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
      expect(result.brandProfile.companyId).toBe('company-123');
    });

    it('should handle very large research data', async () => {
      const largeContent = 'x'.repeat(100000); // 100KB of content
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: { content: largeContent },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
    });

    it('should handle special characters in research content', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: {
          content: '特殊文字 emoji 😊 symbols ™®© quotes "test" \'test\'',
        },
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CreateBrandProfileOutput;

      expect(result.brandProfile).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should complete quickly', async () => {
      const event: CreateBrandProfileInput = {
        companyId: 'company-123',
        researchData: { content: 'Research content' },
        dryRun: true,
      };

      const startTime = Date.now();
      await handler(event, mockContext, () => {});
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should handle concurrent profile creation', async () => {
      const events = Array.from({ length: 10 }, (_, i) => ({
        companyId: `company-${i}`,
        researchData: { content: `Research for company ${i}` },
        dryRun: true,
      }));

      const results = await Promise.all(
        events.map(
          event =>
            handler(
              event,
              mockContext,
              () => {},
            ) as Promise<CreateBrandProfileOutput>,
        ),
      );

      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.companyId).toBe(`company-${index}`);
        expect(result.brandProfile.companyId).toBe(`company-${index}`);
      });
    });
  });
});
