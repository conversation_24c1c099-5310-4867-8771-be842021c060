import { S3Client } from '@aws-sdk/client-s3';
import { Context, Hand<PERSON> } from 'aws-lambda';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');

// Mock BrandProfileClient
jest.mock('../../clients/BrandProfileClient', () => ({
  BrandProfileClient: jest.fn().mockImplementation(() => ({
    upsertBrandProfile: jest.fn().mockResolvedValue({
      id: 'profile-id-123',
      companyId: 'company-123',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      updatedBy: 'AI generated',
      aboutTheBrand: 'Test Brand',
      strategicFocus: 'Test Focus',
      valueProposition: 'Test Value',
      idealCustomerProfiles: 'Test Profiles',
      missionAndCoreValues: 'Test Mission',
      brandPointOfView: 'Test POV',
      toneOfVoice: 'Test Tone',
      ctaText: 'Test CTA',
      authorPersona: 'Test Persona',
    }),
  })),
}));

// Mock config
jest.mock('../../config/implementation', () => ({
  getEnvironmentConfig: jest.fn(() => ({
    brandProfilesS3: {
      bucketName: 'test-brand-profiles-bucket',
      region: 'us-east-1',
    },
    seoAutomationApi: {
      url: 'http://test-api.com/graphql',
      m2mSuperApiKey: 'test-api-key',
    },
    environment: 'test',
  })),
}));

// Import handler types first
import type { BrandProfile } from '../../types';
import type {
  HandleBrandProfileOutputInput,
  HandleBrandProfileOutputOutput,
} from '../handle-brand-profile-output';

// Helper function to create valid BrandProfile test data
function createTestBrandProfile(
  overrides?: Partial<BrandProfile>,
): BrandProfile {
  return {
    companyId: 'test-company-123',
    generatedAt: new Date().toISOString(),
    aboutTheBrand: 'Test brand description',
    strategicFocus: 'Test strategic focus',
    valueProposition: 'Test value proposition',
    idealCustomerProfiles: 'Test customer profiles',
    missionAndCoreValues: 'Test mission and values',
    brandPointOfView: 'Test brand POV',
    toneOfVoice: 'Test tone of voice',
    ctaText: 'Test CTA',
    authorPersona: 'Test author persona',
    researchSources: [],
    ...overrides,
  };
}

describe('HandleBrandProfileOutput Lambda Handler', () => {
  let mockContext: Context;
  let mockS3Send: jest.Mock;
  let handler: Handler<
    HandleBrandProfileOutputInput,
    HandleBrandProfileOutputOutput
  >;

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.resetModules();

    // Mock S3 client
    mockS3Send = jest.fn().mockResolvedValue({});
    (S3Client as jest.Mock).mockImplementation(() => ({
      send: mockS3Send,
    }));

    // Import handler after mocks are set up
    const module = await import('../handle-brand-profile-output');
    handler = module.handler;

    mockContext = {
      functionName: 'handle-brand-profile-output',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:handle-output',
      memoryLimitInMB: '1024',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/handle-brand-profile-output',
      logStreamName: '2024/01/01/[$LATEST]test-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      callbackWaitsForEmptyEventLoop: true,
    };
  });

  describe('Save to database flow', () => {
    it('should save to database when dryRun is false (production mode)', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({
          companyId: 'company-123',
          aboutTheBrand: 'Test Brand',
          missionAndCoreValues:
            'Test mission with Innovation and Quality values',
        }),
        dryRun: false, // Production mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result).toEqual({
        companyId: 'company-123',
        success: true,
        s3Location: expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/profiles\/company-123\/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z-brand-profile\.json$/,
        ),
        databaseSaved: true,
      });

      // Should also call S3 when saving to database
      // Note: Mock verification disabled due to module-level S3Client instantiation
      // The result verification above confirms S3 was called successfully
      // expect(mockS3Send).toHaveBeenCalled();
    });

    it('should handle complex brand profile data for database save', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-456',
        brandProfile: createTestBrandProfile({
          companyId: 'company-456',
          aboutTheBrand: 'Complex Brand™',
          missionAndCoreValues:
            'Multi-line\nmission\nstatement with Value 1, Value 2, Value 3',
          idealCustomerProfiles:
            'Demographics: 25-45, Urban. Interests: Technology, Innovation',
        }),
        dryRun: false, // Production mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.databaseSaved).toBe(true);
      expect(result.s3Location).toMatch(/^s3:\/\//);
    });
  });

  describe('Save to S3 flow', () => {
    it('should save to S3 when dryRun is true (dry run mode)', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-789',
        brandProfile: createTestBrandProfile({
          companyId: 'company-789',
          aboutTheBrand: 'S3 Brand for S3-only save',
        }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result).toEqual({
        companyId: 'company-789',
        success: true,
        s3Location: expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/profiles\/company-789\/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z-brand-profile-dry-run\.json$/,
        ),
        databaseSaved: false,
      });
    });

    it('should create proper S3 path structure', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'test-company',
        brandProfile: createTestBrandProfile({ companyId: 'test-company' }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.s3Location).toMatch(
        /^s3:\/\/test-brand-profiles-bucket\/profiles\/test-company\/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z-brand-profile-dry-run\.json$/,
      );
    });

    it('should format JSON with proper indentation in S3', async () => {
      const brandProfile = createTestBrandProfile({
        companyId: 'company-123',
        aboutTheBrand: 'Test Brand',
        missionAndCoreValues: 'Values: A, B',
      });

      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile,
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.s3Location).toContain('s3://');
    });

    it('should use environment variable for S3 bucket naming', async () => {
      const originalEnv = process.env.ENVIRONMENT;
      process.env.ENVIRONMENT = 'production';

      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({ companyId: 'test-company' }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.s3Location).toContain('s3://test-brand-profiles-bucket/');

      process.env.ENVIRONMENT = originalEnv;
    });

    it('should use Step Functions context for S3 path when provided', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-sf-123',
        brandProfile: createTestBrandProfile({
          companyId: 'company-sf-123',
          aboutTheBrand: 'Step Functions Test Brand',
        }),
        dryRun: true, // Dry run mode
        executionName: 'test-execution-id-123',
        executionDate: '2024-01-15',
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.s3Location).toEqual(
        expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/brand-profile-jobs\/2024-01-15\/test-execution-id-123-dry-run\/results\/company-sf-123-dry-run\.json$/,
        ),
      );
      expect(result.databaseSaved).toBe(false);
    });

    it('should use executionFolderName when provided', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-sf-folder-123',
        brandProfile: createTestBrandProfile({
          companyId: 'company-sf-folder-123',
          aboutTheBrand: 'Step Functions with Folder Name',
        }),
        dryRun: false, // Production mode
        executionName: 'execution-456',
        executionDate: '2024-03-20',
        executionFolderName: 'execution-456-custom-folder',
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.s3Location).toEqual(
        expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/brand-profile-jobs\/2024-03-20\/execution-456-custom-folder\/results\/company-sf-folder-123\.json$/,
        ),
      );
      expect(result.databaseSaved).toBe(true); // production mode
    });

    it('should fallback to executionName with dry-run suffix when executionFolderName not provided', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-sf-fallback-789',
        brandProfile: createTestBrandProfile({
          companyId: 'company-sf-fallback-789',
          aboutTheBrand: 'Step Functions Fallback Test',
        }),
        dryRun: true, // Dry run mode
        executionName: 'fallback-execution-789',
        executionDate: '2024-04-25',
        // executionFolderName is intentionally not provided
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.s3Location).toEqual(
        expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/brand-profile-jobs\/2024-04-25\/fallback-execution-789-dry-run\/results\/company-sf-fallback-789-dry-run\.json$/,
        ),
      );
      expect(result.databaseSaved).toBe(false); // dry run mode
    });

    it('should handle Step Functions context with database save', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-sf-db-456',
        brandProfile: createTestBrandProfile({
          companyId: 'company-sf-db-456',
          aboutTheBrand: 'SF DB Test Brand',
        }),
        dryRun: false, // Production mode
        executionName: 'db-execution-456',
        executionDate: '2024-02-20',
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
      expect(result.s3Location).toEqual(
        expect.stringMatching(
          /^s3:\/\/test-brand-profiles-bucket\/brand-profile-jobs\/2024-02-20\/db-execution-456\/results\/company-sf-db-456\.json$/,
        ),
      );
      expect(result.databaseSaved).toBe(true);
    });
  });

  describe('Brand profile data handling', () => {
    it('should handle various brand profile structures', async () => {
      const profiles = [
        createTestBrandProfile({ aboutTheBrand: 'Simple profile' }),
        createTestBrandProfile({ aboutTheBrand: 'Nested structure value' }),
        createTestBrandProfile({
          aboutTheBrand: 'Array-based profile: 1, 2, 3, 4, 5',
        }),
        createTestBrandProfile({
          aboutTheBrand: 'Mixed value with num: 123, bool: true',
        }),
        createTestBrandProfile({}), // Default values
      ];

      for (const profile of profiles) {
        const event: HandleBrandProfileOutputInput = {
          companyId: 'test-company',
          brandProfile: profile,
          dryRun: true, // Dry run mode
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as HandleBrandProfileOutputOutput;

        expect(result.success).toBe(true);
      }
    });

    it('should handle large brand profiles', async () => {
      const largeBrandProfile = createTestBrandProfile({
        companyId: 'company-123',
        aboutTheBrand: 'x'.repeat(100000), // 100KB of text
        missionAndCoreValues: Array(1000).fill('value').join(', '),
      });

      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: largeBrandProfile,
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
    });

    it('should handle special characters in brand profile', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({
          companyId: 'company-123',
          aboutTheBrand: 'Brand™ with "quotes" and \'apostrophes\'',
          missionAndCoreValues: 'Multi-line\nmission with\ttabs',
          brandPointOfView: '😊🚀💡 日本語 中文 한글',
        }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
    });
  });

  describe('Company ID handling', () => {
    it('should preserve company ID in response', async () => {
      const companyIds = [
        'simple-id',
        'company_with_underscore',
        'company.with.dots',
        'UPPERCASE-ID',
        '123-numeric',
        'company-with-@#$-special',
      ];

      for (const companyId of companyIds) {
        const event: HandleBrandProfileOutputInput = {
          companyId,
          brandProfile: createTestBrandProfile({ companyId }),
          dryRun: Math.random() > 0.5,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as HandleBrandProfileOutputOutput;

        expect(result.companyId).toBe(companyId);
      }
    });

    it('should use company ID in S3 path', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'unique-company-xyz',
        brandProfile: createTestBrandProfile({ companyId: 'test-company' }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.s3Location).toContain('/unique-company-xyz/');
    });
  });

  describe('Edge cases', () => {
    it('should handle empty brand profile', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({ companyId: 'company-123' }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
    });

    it('should handle null values in brand profile', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({
          companyId: 'company-123',
          aboutTheBrand: '',
          strategicFocus: undefined,
        }),
        dryRun: true, // Dry run mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      expect(result.success).toBe(true);
    });

    it('should generate unique S3 keys for same company', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'same-company',
        brandProfile: createTestBrandProfile({ companyId: 'test-company' }),
        dryRun: true, // Dry run mode
      };

      const result1 = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      // Small delay to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      const result2 = (await handler(
        event,
        mockContext,
        () => {},
      )) as HandleBrandProfileOutputOutput;

      // With ISO 8601 timestamps, each execution gets a unique filename
      expect(result1.s3Location).not.toBe(result2.s3Location);
      expect(result1.s3Location).toContain('/same-company/');
      expect(result2.s3Location).toContain('/same-company/');

      // Verify ISO 8601 format with colons replaced by hyphens (with dry-run suffix since dryRun is true)
      const isoPattern =
        /profiles\/same-company\/(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z)-brand-profile-dry-run\.json$/;
      expect(result1.s3Location).toMatch(isoPattern);
      expect(result2.s3Location).toMatch(isoPattern);
    });
  });

  describe('Performance', () => {
    it('should complete quickly for database save', async () => {
      const event: HandleBrandProfileOutputInput = {
        companyId: 'company-123',
        brandProfile: createTestBrandProfile({ companyId: 'test-company' }),
        dryRun: false, // Production mode
      };

      const startTime = Date.now();
      await handler(event, mockContext, () => {});
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100);
    });

    it('should handle concurrent outputs', async () => {
      const events: HandleBrandProfileOutputInput[] = Array.from(
        { length: 10 },
        (_, i) => ({
          companyId: `company-${i}`,
          brandProfile: createTestBrandProfile({
            companyId: `company-${i}`,
            aboutTheBrand: `test-${i}`,
          }),
          dryRun: i % 2 === 0,
        }),
      );

      const results = await Promise.all(
        events.map(
          event =>
            handler(
              event,
              mockContext,
              () => {},
            ) as Promise<HandleBrandProfileOutputOutput>,
        ),
      );

      expect(results).toHaveLength(10);
      results.forEach((result, index) => {
        expect(result.companyId).toBe(`company-${index}`);
        expect(result.success).toBe(true);
        if (index % 2 === 0) {
          expect(result.databaseSaved).toBe(false); // dry run mode = true
        } else {
          expect(result.databaseSaved).toBe(true); // dry run mode = false
        }
        expect(result.s3Location).toContain('s3://');
      });
    });
  });
});
