import { S3Client } from '@aws-sdk/client-s3';
import { Context } from 'aws-lambda';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');

// Mock ApiGatewayClient
jest.mock('../../clients/ApiGatewayClient', () => ({
  ApiGatewayClient: jest.fn().mockImplementation(() => ({
    getCompanyWebsite: jest.fn().mockImplementation((companyId: string) =>
      Promise.resolve({
        companyId,
        websiteUrl: `https://example-${companyId}.com`,
      }),
    ),
    fetchAllWebsites: jest
      .fn()
      .mockImplementation(
        (entitlements: { companyId: string; websiteUrl?: string }[]) =>
          Promise.resolve(
            entitlements.map(entitlement => ({
              companyId: entitlement.companyId,
              websiteUrl: entitlement.websiteUrl
                ? entitlement.websiteUrl.replace('tenant-', 'api-gateway-') // Simulate API Gateway enrichment
                : `https://example-${entitlement.companyId}.com`, // Simulate API Gateway only
            })),
          ),
      ),
  })),
}));

// Mock TenantClient
jest.mock('../../clients/TenantClient', () => ({
  TenantClient: jest.fn().mockImplementation(() => ({
    getEntitledCompanies: jest.fn().mockResolvedValue([
      { companyId: 'entitled-1', websiteUrl: undefined },
      { companyId: 'entitled-2', websiteUrl: 'https://tenant-entitled-2.com' },
    ]),
    getEntitledCompaniesWithWebsites: jest.fn().mockImplementation(() => {
      const baseCompanies = [
        { companyId: 'company-1', websiteUrl: 'https://tenant-company-1.com' },
        { companyId: 'company-2', websiteUrl: 'https://tenant-company-2.com' },
        { companyId: 'company-3', websiteUrl: 'https://tenant-company-3.com' },
        {
          companyId: 'single-company',
          websiteUrl: 'https://tenant-single-company.com',
        },
        {
          companyId: 'sf-company-1',
          websiteUrl: 'https://tenant-sf-company-1.com',
        },
        {
          companyId: 'sf-company-2',
          websiteUrl: 'https://tenant-sf-company-2.com',
        },
        {
          companyId: 'direct-company',
          websiteUrl: 'https://tenant-direct-company.com',
        },
        {
          companyId: 'test-company',
          websiteUrl: 'https://tenant-test-company.com',
        },
        {
          companyId: 'company-with-special-chars-@#$',
          websiteUrl: 'https://tenant-special-chars.com',
        },
        {
          companyId: 'company_underscore',
          websiteUrl: 'https://tenant-underscore.com',
        },
        {
          companyId: 'company.with.dots',
          websiteUrl: 'https://tenant-dots.com',
        },
      ];

      for (let i = 0; i < 1000; i++) {
        baseCompanies.push({
          companyId: `company-${i}`,
          websiteUrl: `https://tenant-company-${i}.com`,
        });
      }

      return Promise.resolve(baseCompanies);
    }),
  })),
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  })),
}));

// Mock config
jest.mock('../../config/implementation', () => ({
  getEnvironmentConfig: jest.fn(() => ({
    brandProfilesS3: {
      bucketName: 'brand-profile-generator-test',
      region: 'us-east-1',
    },
    environment: 'test',
    apiGateway: {
      url: 'https://api.test.com/graphql',
      superUser: 'test-super-user',
      apiGatewayKey: 'test-api-key',
    },
    tenantApi: {
      url: 'https://tenant.test.com',
      productIds: ['test-product-id'],
    },
  })),
}));

import {
  handler,
  ListEntitledUrlsInput,
  ListEntitledUrlsOutput,
} from '../list-entitled-urls';

describe('ListEntitledUrls Lambda Handler', () => {
  let mockContext: Context;
  let mockS3Send: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock S3 client
    mockS3Send = jest.fn().mockResolvedValue({});
    (S3Client as jest.Mock).mockImplementation(() => ({
      send: mockS3Send,
    }));

    // Create mock context
    mockContext = {
      functionName: 'list-entitled-urls',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:list-entitled-urls',
      memoryLimitInMB: '1024',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/list-entitled-urls',
      logStreamName: '2024/01/01/[$LATEST]test-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      callbackWaitsForEmptyEventLoop: true,
    };
  });

  describe('With company IDs provided', () => {
    it('should process company IDs and bypass entitlement check', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['company-1', 'company-2', 'company-3'],
        regenerateIfExisting: true,
        dryRun: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result).toMatchObject({
        s3Bucket: expect.stringContaining('brand-profile-generator'),
        s3Key: expect.stringContaining('brand-profile-jobs/'),
        count: 3,
        companyWebsites: expect.arrayContaining([
          {
            companyId: 'company-1',
            websiteUrl: 'https://example-company-1.com',
          },
          {
            companyId: 'company-2',
            websiteUrl: 'https://example-company-2.com',
          },
          {
            companyId: 'company-3',
            websiteUrl: 'https://example-company-3.com',
          },
        ]),
        regenerateIfExisting: true,
        dryRun: true,
      });
    });

    it('should handle empty company IDs array', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: [],
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // Should fallback to entitled companies flow
      expect(result.companyWebsites.length).toBeGreaterThan(0);
      expect(result.regenerateIfExisting).toBe(false);
      expect(result.dryRun).toBe(true); // Default is true
    });

    it('should handle single company ID', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['single-company'],
        dryRun: false, // Production mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.count).toBe(1);
      expect(result.companyWebsites).toHaveLength(1);
      expect(result.companyWebsites[0]).toEqual({
        companyId: 'single-company',
        websiteUrl: 'https://example-single-company.com',
      });
      expect(result.dryRun).toBe(false);
    });
  });

  describe('Without company IDs (entitlement flow)', () => {
    it('should query entitled companies when no companyIds provided', async () => {
      const event: ListEntitledUrlsInput = {
        regenerateIfExisting: false,
        dryRun: false, // Production mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // Verify entitled companies are returned
      expect(result.companyWebsites).toHaveLength(2);
      expect(result.companyWebsites).toEqual(
        expect.arrayContaining([
          {
            companyId: 'entitled-1',
            websiteUrl: 'https://example-entitled-1.com',
          },
          {
            companyId: 'entitled-2',
            websiteUrl: 'https://api-gateway-entitled-2.com',
          },
        ]),
      );
    });

    it('should handle missing all optional parameters', async () => {
      const event: ListEntitledUrlsInput = {};

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.regenerateIfExisting).toBe(false);
      expect(result.dryRun).toBe(true); // Default is true
      expect(result.companyWebsites.length).toBeGreaterThan(0);
    });
  });

  describe('Step Functions integration', () => {
    it('should use Step Functions execution context when provided', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['sf-company-1', 'sf-company-2'],
        executionName: 'test-execution-123',
        executionStartTime: '2024-01-15T10:30:45.123Z',
        regenerateIfExisting: true,
        dryRun: false, // Production mode
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // Should use Step Functions path format with dry-run suffix (dryRun is false)
      expect(result.s3Bucket).toBe('brand-profile-generator-test');
      expect(result.s3Key).toBe(
        'brand-profile-jobs/2024-01-15/test-execution-123/input.json',
      );
      expect(result.regenerateIfExisting).toBe(true);
      expect(result.dryRun).toBe(false);
    });

    it('should fallback to direct invocation path without execution context', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['direct-company'],
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // Should use direct invocation path format with dry-run suffix on both folder and filename (default is true)
      expect(result.s3Key).toMatch(
        /^brand-profile-jobs\/\d{4}-\d{2}-\d{2}\/direct-.*-dry-run\/input-dry-run\.json$/,
      );
    });
  });

  describe('S3 operations', () => {
    it('should write company websites to S3 with correct structure', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['test-company'],
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.s3Bucket).toBe('brand-profile-generator-test');
      expect(result.s3Key).toMatch(
        /^brand-profile-jobs\/\d{4}-\d{2}-\d{2}\/direct-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z-dry-run\/input-dry-run\.json$/,
      );
      expect(result.companyWebsites).toBeDefined();
      expect(Array.isArray(result.companyWebsites)).toBe(true);
    });

    it('should use environment variable for S3 bucket naming', async () => {
      const originalEnv = process.env.ENVIRONMENT;
      process.env.ENVIRONMENT = 'production';

      const event: ListEntitledUrlsInput = {
        companyIds: ['test-company'],
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.s3Bucket).toBe('brand-profile-generator-test');

      // Restore original environment
      process.env.ENVIRONMENT = originalEnv;
    });
  });

  describe('Edge cases', () => {
    it('should handle very large number of company IDs', async () => {
      const largeCompanyList = Array.from(
        { length: 1000 },
        (_, i) => `company-${i}`,
      );
      const event: ListEntitledUrlsInput = {
        companyIds: largeCompanyList,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.count).toBe(1000);
      expect(result.companyWebsites).toHaveLength(1000);
    });

    it('should handle special characters in company IDs', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: [
          'company-with-special-chars-@#$',
          'company_underscore',
          'company.with.dots',
        ],
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      expect(result.companyWebsites).toHaveLength(3);
      expect(result.companyWebsites[0].companyId).toBe(
        'company-with-special-chars-@#$',
      );
    });

    it('should generate unique S3 keys based on timestamp', async () => {
      const event: ListEntitledUrlsInput = {
        companyIds: ['test-company'],
      };

      const result1 = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // Small delay to ensure different timestamp
      await new Promise(resolve => setTimeout(resolve, 10));

      const result2 = (await handler(
        event,
        mockContext,
        () => {},
      )) as ListEntitledUrlsOutput;

      // With ISO 8601 timestamps, each execution gets a unique filename
      expect(result1.s3Key).not.toBe(result2.s3Key);

      // Verify ISO 8601 format with colons replaced by hyphens in the new path structure (with dry-run suffix on both folder and filename)
      const isoPattern =
        /^brand-profile-jobs\/\d{4}-\d{2}-\d{2}\/direct-\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}\.\d{3}Z-dry-run\/input-dry-run\.json$/;
      expect(result1.s3Key).toMatch(isoPattern);
      expect(result2.s3Key).toMatch(isoPattern);
    });
  });
});
