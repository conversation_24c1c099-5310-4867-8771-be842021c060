import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from '@aws-sdk/client-s3';

jest.mock('@aws-sdk/client-s3');

jest.mock('../../config/implementation', () => ({
  getEnvironmentConfig: jest.fn(() => ({
    brandProfilesS3: {
      bucketName: 'test-brand-profiles-bucket',
      region: 'us-east-1',
    },
    environment: 'test',
    slack: {
      enabled: false,
      webhookUrl: '',
    },
  })),
}));

import {
  handler,
  ReportResultsInput,
  ReportResultsOutput,
  ProcessedCompany,
  MapResultsWithWriter,
} from '../report-results';

const MockedS3Client = S3Client as jest.MockedClass<typeof S3Client>;

describe('report-results', () => {
  const mockSend = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.error = jest.fn();

    MockedS3Client.prototype.send = mockSend;
    mockSend.mockResolvedValue({});
  });

  describe('Direct Results Processing', () => {
    it('should handle empty results array', async () => {
      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: true,
        regenerateIfExisting: false,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary).toEqual({
        totalCompanies: 0,
        processedCount: 0,
        skippedCount: 0,
        failedCount: 0,
        executionTime: expect.any(String),
      });

      expect(result.companySummaries).toEqual([]);
      expect(result.s3Key).toContain('execution-report-dry-run.json');

      expect(mockSend).toHaveBeenCalledTimes(1);
      expect(mockSend).toHaveBeenCalledWith(expect.any(PutObjectCommand));
    });

    it('should correctly categorize processed, skipped, and failed companies', async () => {
      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        regenerateIfExisting: false,
        results: [
          {
            companyId: 'company-1',
            websiteUrl: 'https://example1.com',
            hasExistingProfile: true,
            shouldProcess: false,
          },
          {
            companyId: 'company-2',
            websiteUrl: 'https://example2.com',
            hasExistingProfile: false,
            shouldProcess: true,
            s3Location: 's3://bucket/company-2.json',
          },
          {
            companyId: 'company-3',
            websiteUrl: 'https://example3.com',
            hasExistingProfile: false,
            shouldProcess: false,
            decisionReason: 'processing_failed',
            error: { message: 'API error' },
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary).toEqual({
        totalCompanies: 3,
        processedCount: 1,
        skippedCount: 1,
        failedCount: 1,
        executionTime: expect.any(String),
      });

      expect(result.companySummaries).toHaveLength(3);

      const processed = result.companySummaries.filter(
        c => c.status === 'processed',
      );
      expect(processed).toHaveLength(1);
      expect(processed[0].companyId).toBe('company-2');

      const skipped = result.companySummaries.filter(
        c => c.status === 'skipped',
      );
      expect(skipped).toHaveLength(1);
      expect(skipped[0].companyId).toBe('company-1');
      expect(skipped[0].reason).toBe('existing_profile');

      const failed = result.companySummaries.filter(c => c.status === 'failed');
      expect(failed).toHaveLength(1);
      expect(failed[0].companyId).toBe('company-3');
      expect(failed[0].reason).toBe('API error');
    });
  });

  describe('S3 ResultWriter Processing', () => {
    it('should read results from S3 when ResultWriterDetails is provided', async () => {
      const mockS3Results: ProcessedCompany[] = [
        {
          companyId: 'company-1',
          websiteUrl: 'https://example1.com',
          hasExistingProfile: false,
          shouldProcess: true,
          s3Location: 's3://bucket/company-1.json', // Added s3Location for processed
        },
        {
          companyId: 'company-2',
          websiteUrl: 'https://example2.com',
          hasExistingProfile: true,
          shouldProcess: false,
        },
      ];

      // Create a manifest for testing
      const manifest = {
        DestinationBucket: 'test-bucket',
        MapRunArn: 'arn:aws:states:us-east-1:123456789012:mapRun:test',
        ResultFiles: {
          SUCCEEDED: [{ Key: 'results/SUCCEEDED_0.json', Size: 1000 }],
          FAILED: [],
          PENDING: [],
        },
      };

      mockSend
        // Mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [{ Prefix: 'results/uuid-123/' }],
        })
        // Mock reading manifest from UUID folder
        .mockResolvedValueOnce({
          Body: {
            transformToString: () => Promise.resolve(JSON.stringify(manifest)),
          },
        })
        // Mock reading the SUCCEEDED file with both results
        .mockResolvedValueOnce({
          Body: {
            transformToString: () =>
              Promise.resolve(JSON.stringify(mockS3Results)),
          },
        })
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: true,
        results: {
          ResultWriterDetails: {
            Bucket: 'test-bucket',
            Key: 'results/',
          },
        } as MapResultsWithWriter,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(mockSend).toHaveBeenCalledWith(expect.any(GetObjectCommand));
      expect(mockSend).toHaveBeenCalledTimes(4); // list + manifest get + results get + put

      expect(result.executionSummary.totalCompanies).toBe(2);
      expect(result.executionSummary.processedCount).toBe(1);
      expect(result.executionSummary.skippedCount).toBe(1);
    });

    it('should handle empty S3 results', async () => {
      mockSend
        // Mock listing with no UUID folder found
        .mockResolvedValueOnce({
          CommonPrefixes: [],
        })
        // Mock fallback listing with no contents
        .mockResolvedValueOnce({
          Contents: [],
        })
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: true,
        results: {
          ResultWriterDetails: {
            Bucket: 'test-bucket',
            Key: 'results/',
          },
        } as MapResultsWithWriter,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.totalCompanies).toBe(0);
      expect(result.companySummaries).toEqual([]);
    });

    it('should handle S3 read errors gracefully', async () => {
      const manifest = {
        DestinationBucket: 'test-bucket',
        MapRunArn: 'arn:aws:states:us-east-1:123456789012:mapRun:test',
        ResultFiles: {
          SUCCEEDED: [{ Key: 'results/SUCCEEDED_0.json', Size: 1000 }],
          FAILED: [],
          PENDING: [],
        },
      };

      mockSend
        // Mock listing to find UUID folder
        .mockResolvedValueOnce({
          CommonPrefixes: [{ Prefix: 'results/uuid-123/' }],
        })
        // Mock reading manifest successfully
        .mockResolvedValueOnce({
          Body: {
            transformToString: () => Promise.resolve(JSON.stringify(manifest)),
          },
        })
        // Mock failure when reading the SUCCEEDED file - retry 3 times
        .mockRejectedValueOnce(new Error('S3 GetObject failed'))
        .mockRejectedValueOnce(new Error('S3 GetObject failed'))
        .mockRejectedValueOnce(new Error('S3 GetObject failed'))
        // Mock the final S3 put for the report
        .mockResolvedValueOnce({});

      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: true,
        results: {
          ResultWriterDetails: {
            Bucket: 'test-bucket',
            Key: 'results/',
          },
        } as MapResultsWithWriter,
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      // Check that retry logic was triggered and failed after 3 attempts
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('failed after 3 attempts'),
      );

      // Also check the original error message was logged
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('Error reading succeeded result'),
        expect.any(Error),
      );

      expect(result.executionSummary.totalCompanies).toBe(0);
    });
  });

  describe('Slack Message Formatting', () => {
    it('should generate appropriate Slack message for successful run', async () => {
      const event: ReportResultsInput = {
        executionName: 'prod-execution-001',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: [
          {
            companyId: 'company-1',
            websiteUrl: 'https://example1.com',
            hasExistingProfile: false,
            shouldProcess: true,
            s3Location: 's3://bucket/company-1.json', // Added s3Location
          },
          {
            companyId: 'company-2',
            websiteUrl: 'https://example2.com',
            hasExistingProfile: false,
            shouldProcess: true,
            s3Location: 's3://bucket/company-2.json', // Added s3Location
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.slackMessage).toBeDefined();
      expect(result.slackMessage).toContain('✅');
      expect(result.slackMessage).toContain('[TEST]');
      expect(result.slackMessage).toContain('Brand Profile Generation Report');
      expect(result.slackMessage).toContain('prod-execution-001');
      expect(result.slackMessage).toContain('Total Companies: 2');
      expect(result.slackMessage).toContain('Successfully Processed: 2');
    });

    it('should generate warning message when failures occur', async () => {
      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: [
          {
            companyId: 'company-1',
            websiteUrl: 'https://example1.com',
            hasExistingProfile: false,
            shouldProcess: false,
            decisionReason: 'processing_failed',
            error: { message: 'API timeout' },
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.slackMessage).toContain('❌');
      expect(result.slackMessage).toContain('[TEST]');
      expect(result.slackMessage).toContain('Failed Companies:');
      expect(result.slackMessage).toContain('API timeout');
    });

    it('should indicate dry run mode in Slack message', async () => {
      const event: ReportResultsInput = {
        executionName: 'test-execution',
        dryRun: true,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.slackMessage).toContain('[DRY RUN]');
    });
  });

  describe('Edge Cases for Counting Logic', () => {
    it('should correctly categorize all edge cases', async () => {
      const event: ReportResultsInput = {
        executionName: 'edge-case-test',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: [
          // Successfully processed
          {
            companyId: 'success-1',
            websiteUrl: 'https://success1.com',
            hasExistingProfile: false,
            shouldProcess: true,
            s3Location: 's3://bucket/success-1.json',
          },
          // Skipped - existing profile
          {
            companyId: 'skip-existing-1',
            websiteUrl: 'https://existing1.com',
            hasExistingProfile: true,
            shouldProcess: false,
          },
          // Failed - has error field
          {
            companyId: 'fail-error-1',
            websiteUrl: 'https://fail1.com',
            hasExistingProfile: false,
            shouldProcess: true,
            error: { message: 'Network timeout' },
          },
          // Failed - processing_failed decision reason
          {
            companyId: 'fail-reason-1',
            websiteUrl: 'https://fail2.com',
            hasExistingProfile: false,
            shouldProcess: false,
            decisionReason: 'processing_failed',
          },
          // Skipped - shouldProcess is false, no existing profile
          {
            companyId: 'skip-not-selected',
            websiteUrl: 'https://notselected.com',
            hasExistingProfile: false,
            shouldProcess: false,
          },
          // Edge case: shouldProcess=true but no s3Location (incomplete processing)
          {
            companyId: 'incomplete-1',
            websiteUrl: 'https://incomplete.com',
            hasExistingProfile: false,
            shouldProcess: true,
            // Note: no s3Location, so should be skipped not processed
          },
          // Failed - has both error and processing_failed
          {
            companyId: 'fail-both-1',
            websiteUrl: 'https://failboth.com',
            hasExistingProfile: false,
            shouldProcess: true,
            decisionReason: 'processing_failed',
            error: { message: 'Lambda timeout' },
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      // Verify counts
      expect(result.executionSummary.totalCompanies).toBe(7);
      expect(result.executionSummary.processedCount).toBe(1); // Only success-1
      expect(result.executionSummary.skippedCount).toBe(3); // skip-existing-1, skip-not-selected, incomplete-1
      expect(result.executionSummary.failedCount).toBe(3); // fail-error-1, fail-reason-1, fail-both-1

      // Verify specific categorizations
      const summaryMap = new Map(
        result.companySummaries.map(c => [c.companyId, c]),
      );

      expect(summaryMap.get('success-1')?.status).toBe('processed');
      expect(summaryMap.get('skip-existing-1')?.status).toBe('skipped');
      expect(summaryMap.get('fail-error-1')?.status).toBe('failed');
      expect(summaryMap.get('fail-reason-1')?.status).toBe('failed');
      expect(summaryMap.get('skip-not-selected')?.status).toBe('skipped');
      expect(summaryMap.get('incomplete-1')?.status).toBe('skipped');
      expect(summaryMap.get('fail-both-1')?.status).toBe('failed');
    });

    it('should handle empty results correctly', async () => {
      const event: ReportResultsInput = {
        executionName: 'empty-test',
        dryRun: false,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.totalCompanies).toBe(0);
      expect(result.executionSummary.processedCount).toBe(0);
      expect(result.executionSummary.skippedCount).toBe(0);
      expect(result.executionSummary.failedCount).toBe(0);
    });

    it('should correctly handle regenerateIfExisting scenarios', async () => {
      const event: ReportResultsInput = {
        executionName: 'regenerate-test',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        regenerateIfExisting: true,
        results: [
          // Successfully regenerated existing profile
          {
            companyId: 'regenerated-1',
            websiteUrl: 'https://regenerated1.com',
            hasExistingProfile: true,
            shouldProcess: true,
            decisionReason: 'force_regenerate',
            s3Location: 's3://bucket/regenerated-1.json',
          },
          // Failed to regenerate existing profile
          {
            companyId: 'regenerate-fail-1',
            websiteUrl: 'https://regeneratefail.com',
            hasExistingProfile: true,
            shouldProcess: true,
            decisionReason: 'force_regenerate',
            error: { message: 'Regeneration failed' },
          },
          // New profile created (even with regenerateIfExisting=true)
          {
            companyId: 'new-1',
            websiteUrl: 'https://new1.com',
            hasExistingProfile: false,
            shouldProcess: true,
            s3Location: 's3://bucket/new-1.json',
          },
          // Existing profile but regeneration not attempted (edge case)
          {
            companyId: 'skip-existing-2',
            websiteUrl: 'https://existing2.com',
            hasExistingProfile: true,
            shouldProcess: false,
            // This shouldn't happen with regenerateIfExisting=true, but testing for completeness
          },
          // Regeneration attempted but no output (silent failure)
          {
            companyId: 'regenerate-nooutput-1',
            websiteUrl: 'https://nooutput.com',
            hasExistingProfile: true,
            shouldProcess: true,
            decisionReason: 'force_regenerate',
            // Note: no s3Location - regeneration was attempted but produced no output
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      // Verify counts
      expect(result.executionSummary.totalCompanies).toBe(5);
      expect(result.executionSummary.processedCount).toBe(2); // regenerated-1, new-1
      expect(result.executionSummary.skippedCount).toBe(2); // skip-existing-2, regenerate-nooutput-1
      expect(result.executionSummary.failedCount).toBe(1); // regenerate-fail-1

      // Verify specific categorizations
      const summaryMap = new Map(
        result.companySummaries.map(c => [c.companyId, c]),
      );

      expect(summaryMap.get('regenerated-1')?.status).toBe('processed');
      expect(summaryMap.get('regenerated-1')?.reason).toBe(
        'successfully_generated',
      );

      expect(summaryMap.get('regenerate-fail-1')?.status).toBe('failed');
      expect(summaryMap.get('regenerate-fail-1')?.reason).toBe(
        'Regeneration failed',
      );

      expect(summaryMap.get('new-1')?.status).toBe('processed');

      expect(summaryMap.get('skip-existing-2')?.status).toBe('skipped');
      expect(summaryMap.get('skip-existing-2')?.reason).toContain('existing');

      expect(summaryMap.get('regenerate-nooutput-1')?.status).toBe('skipped');
      expect(summaryMap.get('regenerate-nooutput-1')?.reason).toBe(
        'no output generated',
      );
    });

    it('should handle all-failed scenario', async () => {
      const event: ReportResultsInput = {
        executionName: 'all-failed-test',
        dryRun: false,
        results: [
          {
            companyId: 'fail-1',
            websiteUrl: 'https://fail1.com',
            error: { message: 'Error 1' },
          },
          {
            companyId: 'fail-2',
            websiteUrl: 'https://fail2.com',
            decisionReason: 'processing_failed',
          },
        ] as ProcessedCompany[],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.totalCompanies).toBe(2);
      expect(result.executionSummary.processedCount).toBe(0);
      expect(result.executionSummary.skippedCount).toBe(0);
      expect(result.executionSummary.failedCount).toBe(2);
    });
  });

  describe('Report Generation', () => {
    it('should include execution time calculation', async () => {
      const startTime = new Date();
      startTime.setHours(startTime.getHours() - 1);
      startTime.setMinutes(startTime.getMinutes() - 30);

      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: startTime.toISOString(),
        dryRun: false,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.executionSummary.executionTime).toBeDefined();
      expect(result.executionSummary.executionTime).toMatch(/\d+h \d+m \d+s/);
    });

    it('should generate correct S3 key structure', async () => {
      const event: ReportResultsInput = {
        executionName: 'prod-batch-123',
        executionStartTime: '2024-03-15T14:30:00.000Z',
        dryRun: false,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.s3Key).toBe(
        'brand-profile-jobs/2024-03-15/prod-batch-123/execution-report.json',
      );
    });

    it('should handle missing executionName and executionStartTime', async () => {
      const event: ReportResultsInput = {
        dryRun: false,
        results: [],
      };

      const result = (await handler(
        event,
        {} as any,
        {} as any,
      )) as ReportResultsOutput;

      expect(result.s3Key).toMatch(
        /brand-profile-jobs\/\d{4}-\d{2}-\d{2}\/direct-.*\/execution-report\.json/,
      );
    });

    it('should include proper metadata in S3 upload', async () => {
      const event: ReportResultsInput = {
        executionName: 'test-execution',
        executionStartTime: '2024-01-15T10:00:00.000Z',
        dryRun: false,
        results: [
          {
            companyId: 'company-1',
            websiteUrl: 'https://example1.com',
            hasExistingProfile: false,
            shouldProcess: true,
          },
        ] as ProcessedCompany[],
      };

      await handler(event, {} as any, {} as any);

      const putCommand = mockSend.mock.calls[0][0];
      expect(putCommand).toBeInstanceOf(PutObjectCommand);
    });
  });
});
