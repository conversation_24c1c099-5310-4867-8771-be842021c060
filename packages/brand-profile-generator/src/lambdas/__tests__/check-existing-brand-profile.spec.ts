import { Context } from 'aws-lambda';

// Create the mock function that will be shared across all tests
let mockGetBrandProfile: jest.Mock;

// Mock the BrandProfileClient module before any imports
jest.mock('../../clients/BrandProfileClient', () => ({
  BrandProfileClient: jest.fn().mockImplementation(() => ({
    get getBrandProfile() {
      return mockGetBrandProfile;
    },
  })),
}));

import { CheckBrandProfileError } from '../../errors';
import {
  handler,
  CheckExistingBrandProfileInput,
  CheckExistingBrandProfileOutput,
} from '../check-existing-brand-profile';

describe('CheckExistingBrandProfile Lambda Handler', () => {
  let mockContext: Context;

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetBrandProfile = jest.fn();

    mockContext = {
      functionName: 'check-existing-brand-profile',
      functionVersion: '1',
      invokedFunctionArn:
        'arn:aws:lambda:us-east-1:123456789012:function:check-existing-brand-profile',
      memoryLimitInMB: '512',
      awsRequestId: 'test-request-id',
      logGroupName: '/aws/lambda/check-existing-brand-profile',
      logStreamName: '2024/01/01/[$LATEST]test-stream',
      getRemainingTimeInMillis: () => 10000,
      done: jest.fn(),
      fail: jest.fn(),
      succeed: jest.fn(),
      callbackWaitsForEmptyEventLoop: true,
    };
  });

  describe('When regenerateIfExisting is true', () => {
    it('should always return shouldProcess as true', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'company-123',
        websiteUrl: 'https://example.com',
        regenerateIfExisting: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result).toEqual({
        companyId: 'company-123',
        websiteUrl: 'https://example.com',
        hasExistingProfile: true,
        shouldProcess: true,
        decisionReason: 'force_regenerate',
      });
    });

    it('should handle regenerate flag regardless of existing profile', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'existing-company',
        websiteUrl: 'https://existing.com',
        regenerateIfExisting: true,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result.shouldProcess).toBe(true);
      expect(result.hasExistingProfile).toBe(true); // May or may not exist, but we're regenerating anyway
      expect(result.decisionReason).toBe('force_regenerate');
    });
  });

  describe('When regenerateIfExisting is false', () => {
    it('should check for existing profile and return shouldProcess as true when no profile exists', async () => {
      // Mock returns null (no existing profile)
      mockGetBrandProfile.mockResolvedValue(null);

      const event: CheckExistingBrandProfileInput = {
        companyId: 'new-company',
        websiteUrl: 'https://newcompany.com',
        regenerateIfExisting: false,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result).toEqual({
        companyId: 'new-company',
        websiteUrl: 'https://newcompany.com',
        hasExistingProfile: false,
        shouldProcess: true,
        decisionReason: 'no_existing_profile',
      });
    });

    it('should return shouldProcess as false when profile exists', async () => {
      // Mock returns an existing profile
      mockGetBrandProfile.mockResolvedValue({
        id: 'profile-123',
        companyId: 'existing-company',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        aboutTheBrand: 'Test brand description',
        strategicFocus: 'Test strategic focus',
        valueProposition: 'Test value proposition',
      });

      const event: CheckExistingBrandProfileInput = {
        companyId: 'existing-company',
        websiteUrl: 'https://existing.com',
        regenerateIfExisting: false,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      // Assert that the handler returns the correct response for existing profile
      expect(result.shouldProcess).toBe(false);
      expect(result.hasExistingProfile).toBe(true);
      expect(result.decisionReason).toBe('existing_profile_found');
      expect(result.companyId).toBe('existing-company');
      expect(result.websiteUrl).toBe('https://existing.com');
    });
  });

  describe('Input validation', () => {
    beforeEach(() => {
      // All validation tests assume no existing profile
      mockGetBrandProfile.mockResolvedValue(null);
    });

    it('should handle various URL formats', async () => {
      const testCases = [
        'https://example.com',
        'http://example.com',
        'https://subdomain.example.com',
        'https://example.com/path',
        'https://example.com:8080',
      ];

      for (const url of testCases) {
        const event: CheckExistingBrandProfileInput = {
          companyId: 'test-company',
          websiteUrl: url,
          regenerateIfExisting: false,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as CheckExistingBrandProfileOutput;

        expect(result.websiteUrl).toBe(url);
        expect(result.companyId).toBe('test-company');
      }
    });

    it('should handle various company ID formats', async () => {
      const testCases = [
        'simple-id',
        'company_with_underscore',
        'company.with.dots',
        'UPPERCASE-ID',
        '123-numeric-id',
        'very-long-company-id-with-many-segments-that-might-be-used-in-production',
      ];

      for (const companyId of testCases) {
        const event: CheckExistingBrandProfileInput = {
          companyId,
          websiteUrl: 'https://example.com',
          regenerateIfExisting: false,
        };

        const result = (await handler(
          event,
          mockContext,
          () => {},
        )) as CheckExistingBrandProfileOutput;

        expect(result.companyId).toBe(companyId);
      }
    });

    it('should handle empty string websiteUrl', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'test-company',
        websiteUrl: '',
        regenerateIfExisting: false,
      };

      await expect(handler(event, mockContext, () => {})).rejects.toThrow(
        CheckBrandProfileError,
      );
    });
  });

  describe('Edge cases', () => {
    beforeEach(() => {
      // All edge case tests assume no existing profile
      mockGetBrandProfile.mockResolvedValue(null);
    });

    it('should handle very long company IDs', async () => {
      const longId = 'a'.repeat(500);
      const event: CheckExistingBrandProfileInput = {
        companyId: longId,
        websiteUrl: 'https://example.com',
        regenerateIfExisting: false,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result.companyId).toBe(longId);
    });

    it('should handle special characters in company ID', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'company-@#$%^&*()',
        websiteUrl: 'https://example.com',
        regenerateIfExisting: false,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result.companyId).toBe('company-@#$%^&*()');
    });

    it('should handle international domain names', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'international-company',
        websiteUrl: 'https://例え.jp',
        regenerateIfExisting: false,
      };

      const result = (await handler(
        event,
        mockContext,
        () => {},
      )) as CheckExistingBrandProfileOutput;

      expect(result.websiteUrl).toBe('https://例え.jp');
    });
  });

  describe('Performance', () => {
    it('should return quickly when regenerateIfExisting is true', async () => {
      const event: CheckExistingBrandProfileInput = {
        companyId: 'test-company',
        websiteUrl: 'https://example.com',
        regenerateIfExisting: true,
      };

      const startTime = Date.now();
      await handler(event, mockContext, () => {});
      const endTime = Date.now();

      // Should return immediately without checking database
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
