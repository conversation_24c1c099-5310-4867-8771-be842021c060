import { Handler } from 'aws-lambda';

import { ProcessInputsError } from '../errors';

export interface ProcessInputsEvent {
  input?: {
    companyIds?: string[];
    regenerateIfExisting?: boolean;
    dryRun?: boolean;
  };
  companyIds?: string[];
  regenerateIfExisting?: boolean;
  dryRun?: boolean;
  executionName?: string;
  executionStartTime?: string;
}

export interface ProcessInputsResult {
  companyIds: string[];
  regenerateIfExisting: boolean;
  dryRun: boolean;
  executionName?: string;
  executionStartTime?: string;
}

export const handler: Handler<
  ProcessInputsEvent,
  ProcessInputsResult
  // eslint-disable-next-line @typescript-eslint/require-await
> = async event => {
  try {
    console.log('Processing inputs:', JSON.stringify(event, null, 2));

    // Extract input from nested structure or use direct values for backward compatibility
    const input = event.input || event;

    // Apply defaults
    const processedInputs: ProcessInputsResult = {
      companyIds: input.companyIds || [],
      regenerateIfExisting: input.regenerateIfExisting ?? false,
      dryRun: input.dryRun ?? true,
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
    };

    // Validate inputs
    if (!Array.isArray(processedInputs.companyIds)) {
      throw new ProcessInputsError('companyIds must be an array', undefined, {
        providedType: typeof processedInputs.companyIds,
        providedValue: processedInputs.companyIds,
      });
    }

    if (typeof processedInputs.regenerateIfExisting !== 'boolean') {
      throw new ProcessInputsError(
        'regenerateIfExisting must be a boolean',
        undefined,
        {
          providedType: typeof processedInputs.regenerateIfExisting,
          providedValue: processedInputs.regenerateIfExisting,
        },
      );
    }

    if (typeof processedInputs.dryRun !== 'boolean') {
      throw new ProcessInputsError('dryRun must be a boolean', undefined, {
        providedType: typeof processedInputs.dryRun,
        providedValue: processedInputs.dryRun,
      });
    }

    // Log the processed inputs
    console.log('Processed inputs:', JSON.stringify(processedInputs, null, 2));
    console.log(`Processing ${processedInputs.companyIds.length} companies`);
    console.log(
      `Regenerate if existing: ${processedInputs.regenerateIfExisting}`,
    );
    console.log(`Dry run mode: ${processedInputs.dryRun}`);

    return processedInputs;
  } catch (error) {
    if (error instanceof ProcessInputsError) {
      throw error;
    }
    throw new ProcessInputsError(
      'Failed to process inputs',
      error instanceof Error ? error : new Error(String(error)),
      { event },
    );
  }
};
