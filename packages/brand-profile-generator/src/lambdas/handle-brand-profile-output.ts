import { Logger } from '@aws-lambda-powertools/logger';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { Handler } from 'aws-lambda';

import { BrandProfileClient } from '../clients/BrandProfileClient';
import { getEnvironmentConfig } from '../config/implementation';
import { BrandProfile } from '../types';

const config = getEnvironmentConfig();
const logger = new Logger({ serviceName: 'handle-brand-profile-output' });

// Validate S3 configuration at startup
if (!config.brandProfilesS3 && process.env.NODE_ENV !== 'test') {
  throw new Error(
    'brandProfilesS3 configuration is required. Set BRAND_PROFILES_S3_BUCKET_NAME environment variable.',
  );
}

const s3Client = new S3Client({
  region: config.brandProfilesS3?.region || 'us-east-1',
});

// Initialize brand profile client only if configuration is available
let brandProfileClient: BrandProfileClient | null = null;
if (config.seoAutomationApi?.url && config.seoAutomationApi?.m2mSuperApiKey) {
  brandProfileClient = new BrandProfileClient(
    config.seoAutomationApi.url,
    config.seoAutomationApi.m2mSuperApiKey,
    logger,
  );
}

export interface HandleBrandProfileOutputInput {
  companyId: string;
  brandProfile: BrandProfile;
  dryRun?: boolean;
  executionName?: string;
  executionDate?: string;
  executionFolderName?: string; // Optional, will fallback to executionName with suffix
}

export interface HandleBrandProfileOutputOutput {
  companyId: string;
  success: boolean;
  s3Location: string;
  databaseSaved: boolean;
}

export const handler: Handler<
  HandleBrandProfileOutputInput,
  HandleBrandProfileOutputOutput
> = async event => {
  logger.info('HandleBrandProfileOutput Lambda invoked', {
    companyId: event.companyId,
    dryRun: event.dryRun,
    executionName: event.executionName,
    executionDate: event.executionDate,
  });

  try {
    // Default to true (safe mode) if not explicitly set
    const isDryRun = event.dryRun ?? true;

    // Determine S3 bucket and key based on execution context
    let s3Bucket: string;
    let s3Key: string;

    // Add dry-run suffix to file names for easy identification
    const dryRunSuffix = isDryRun ? '-dry-run' : '';

    if (event.executionName && event.executionDate) {
      // When called from Step Functions distributed map
      // Use a different bucket for step function jobs if configured, otherwise use default
      s3Bucket =
        process.env.STEP_FUNCTION_S3_BUCKET ||
        config.brandProfilesS3?.bucketName ||
        `brand-profile-generator-${process.env.ENVIRONMENT || 'dev'}`;

      // Use executionFolderName if provided, otherwise construct it from executionName with suffix
      const folderName =
        event.executionFolderName ||
        (event.executionName
          ? `${event.executionName}${dryRunSuffix}`
          : event.executionName);

      s3Key = `brand-profile-jobs/${event.executionDate}/${folderName}/results/${event.companyId}${dryRunSuffix}.json`;
    } else {
      // Use configured bucket for direct invocation or testing
      s3Bucket =
        config.brandProfilesS3?.bucketName ||
        `brand-profiles-${process.env.ENVIRONMENT || 'dev'}`;
      // Use ISO 8601 format with date and time to avoid same-day conflicts
      // Replace colons with hyphens to make it filesystem-safe
      const isoTimestamp = new Date().toISOString().replace(/:/g, '-');
      s3Key = `profiles/${event.companyId}/${isoTimestamp}-brand-profile${dryRunSuffix}.json`;
    }

    // Wrap brand profile with metadata for better traceability
    const outputData = {
      metadata: {
        dryRun: isDryRun,
        generatedAt: new Date().toISOString(),
        companyId: event.companyId,
        executionName: event.executionName,
        executionDate: event.executionDate,
        environment: process.env.ENVIRONMENT || 'dev',
      },
      brandProfile: event.brandProfile,
    };

    await s3Client.send(
      new PutObjectCommand({
        Bucket: s3Bucket,
        Key: s3Key,
        Body: JSON.stringify(outputData, null, 2),
        ContentType: 'application/json',
        // Add tags for easier filtering in S3
        Tagging: `dryRun=${isDryRun}&environment=${process.env.ENVIRONMENT || 'dev'}&companyId=${event.companyId}`,
      }),
    );

    logger.info('Brand profile saved to S3', {
      companyId: event.companyId,
      s3Location: `s3://${s3Bucket}/${s3Key}`,
      dryRun: isDryRun,
    });

    let databaseSaved = false;

    // Only save to database if NOT in dry run mode
    if (!isDryRun) {
      logger.info(
        'Production mode: Saving brand profile to database via client-marketing-service',
        { companyId: event.companyId },
      );

      if (!brandProfileClient) {
        logger.warn(
          'Brand profile client not initialized - missing SEO Automation API configuration',
          { companyId: event.companyId },
        );

        // In production, this should be an error
        if (process.env.NODE_ENV !== 'test') {
          throw new Error(
            'Cannot save to database: SEO Automation API configuration is missing',
          );
        }
      } else {
        try {
          // Prepare brand profile for database save
          const profileToSave = {
            companyId: event.brandProfile.companyId,
            aboutTheBrand: event.brandProfile.aboutTheBrand,
            strategicFocus: event.brandProfile.strategicFocus,
            valueProposition: event.brandProfile.valueProposition,
            idealCustomerProfiles: event.brandProfile.idealCustomerProfiles,
            missionAndCoreValues: event.brandProfile.missionAndCoreValues,
            brandPointOfView: event.brandProfile.brandPointOfView,
            toneOfVoice: event.brandProfile.toneOfVoice,
            ctaText: event.brandProfile.ctaText,
            authorPersona: event.brandProfile.authorPersona,
          };

          const savedProfile =
            await brandProfileClient.upsertBrandProfile(profileToSave);

          logger.info('Brand profile saved to database successfully', {
            companyId: event.companyId,
            profileId: savedProfile.id,
            updatedAt: savedProfile.updatedAt,
          });

          databaseSaved = true;
        } catch (dbError) {
          logger.error('Failed to save brand profile to database', {
            companyId: event.companyId,
            error: dbError instanceof Error ? dbError.message : String(dbError),
            stack: dbError instanceof Error ? dbError.stack : undefined,
          });

          // Re-throw the error so the lambda fails properly
          throw new Error(
            `Failed to save brand profile to database: ${
              dbError instanceof Error ? dbError.message : String(dbError)
            }`,
          );
        }
      }
    } else {
      logger.info('Dry run mode: Skipping database write', {
        companyId: event.companyId,
      });
    }

    return {
      companyId: event.companyId,
      success: true,
      s3Location: `s3://${s3Bucket}/${s3Key}`,
      databaseSaved,
    };
  } catch (error) {
    logger.error('Error handling brand profile output', {
      companyId: event.companyId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
};
