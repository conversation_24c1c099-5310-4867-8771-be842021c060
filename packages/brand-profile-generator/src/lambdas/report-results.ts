import {
  S3Client,
  PutObjectCommand,
  PutObjectCommandInput,
  GetObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { Handler } from 'aws-lambda';

import { getEnvironmentConfig } from '../config/implementation';

const config = getEnvironmentConfig();

const s3Client = new S3Client({
  region: config.brandProfilesS3?.region || 'us-east-1',
});

// Helper function to retry S3 operations with exponential backoff
async function retryS3Operation<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3,
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;

      // Don't retry on certain error codes
      if (error.Code === 'NoSuchKey' || error.Code === 'NoSuchBucket') {
        throw error;
      }

      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000); // Exponential backoff, max 5s
        console.log(
          `${operationName} failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms...`,
          error.message || error,
        );
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`${operationName} failed after ${maxRetries} attempts`);
  throw lastError;
}

export interface ProcessedCompany {
  companyId: string;
  websiteUrl: string;
  hasExistingProfile: boolean;
  shouldProcess: boolean;
  decisionReason?: string;
  s3Location?: string;
  databaseSaved?: boolean;
  error?: any;
}

export interface ResultWriterDetails {
  Bucket: string;
  Key: string;
}

export interface MapResultsWithWriter {
  ResultWriterDetails: ResultWriterDetails;
}

export interface ReportResultsInput {
  executionName?: string;
  executionStartTime?: string;
  executionArn?: string;
  dryRun?: boolean;
  regenerateIfExisting?: boolean;
  results: ProcessedCompany[] | MapResultsWithWriter;
}

export interface CompanySummary {
  companyId: string;
  websiteUrl: string;
  status: 'processed' | 'skipped' | 'failed';
  reason?: string;
  s3Location?: string;
  timestamp: string;
}

export interface ReportResultsOutput {
  s3Bucket: string;
  s3Key: string;
  executionSummary: {
    totalCompanies: number;
    processedCount: number;
    skippedCount: number;
    failedCount: number;
    executionTime?: string;
  };
  companySummaries: CompanySummary[];
  slackMessage?: string;
}

// Interface for Step Functions Map state manifest
interface MapStateManifest {
  DestinationBucket?: string;
  MapRunArn?: string;
  ResultFiles?: {
    SUCCEEDED?: Array<{
      Key: string;
      Size?: number;
    }>;
    FAILED?: Array<{
      Key?: string;
      Size?: number;
    }>;
    PENDING?: Array<{
      Key?: string;
      Size?: number;
    }>;
  };
}

async function readResultsFromS3(
  bucket: string,
  prefixOrKey: string,
): Promise<ProcessedCompany[]> {
  console.log(`Reading Map results from S3: s3://${bucket}/${prefixOrKey}`);

  try {
    // Check if we were given a direct path to manifest.json or a prefix
    if (prefixOrKey.includes('manifest.json')) {
      // We have a direct path to manifest.json, read it directly
      console.log(`Direct manifest path provided: ${prefixOrKey}`);

      // Retry reading the manifest in case of timing issues
      const manifestContent = await retryS3Operation(async () => {
        const manifestCommand = new GetObjectCommand({
          Bucket: bucket,
          Key: prefixOrKey,
        });
        const manifestResponse = await s3Client.send(manifestCommand);
        return await manifestResponse.Body?.transformToString();
      }, `Reading manifest from ${prefixOrKey}`);

      if (manifestContent) {
        const manifest: MapStateManifest = JSON.parse(manifestContent);
        console.log('Found manifest.json:', JSON.stringify(manifest, null, 2));

        const results: ProcessedCompany[] = [];

        // Process the manifest results (same logic as before)
        if (manifest.ResultFiles) {
          console.log(
            'Manifest ResultFiles - SUCCEEDED:',
            manifest.ResultFiles.SUCCEEDED?.length || 0,
            'FAILED:',
            manifest.ResultFiles.FAILED?.length || 0,
            'PENDING:',
            manifest.ResultFiles.PENDING?.length || 0,
          );

          // Process succeeded results
          if (
            manifest.ResultFiles.SUCCEEDED &&
            Array.isArray(manifest.ResultFiles.SUCCEEDED)
          ) {
            console.log(
              `Processing ${manifest.ResultFiles.SUCCEEDED.length} succeeded results from manifest`,
            );

            for (const succeededItem of manifest.ResultFiles.SUCCEEDED) {
              if (succeededItem.Key) {
                try {
                  console.log(
                    `Reading succeeded result file: ${succeededItem.Key} (Size: ${succeededItem.Size})`,
                  );

                  // Use the destination bucket from manifest if available
                  const resultBucket = manifest.DestinationBucket || bucket;

                  // Retry reading the SUCCEEDED file
                  const resultContent = await retryS3Operation(async () => {
                    const resultCommand = new GetObjectCommand({
                      Bucket: resultBucket,
                      Key: succeededItem.Key,
                    });
                    const resultResponse = await s3Client.send(resultCommand);
                    return await resultResponse.Body?.transformToString();
                  }, `Reading SUCCEEDED file ${succeededItem.Key}`);

                  if (resultContent) {
                    const parsedResults = JSON.parse(resultContent);

                    if (Array.isArray(parsedResults)) {
                      console.log(
                        `Found ${parsedResults.length} results in ${succeededItem.Key}`,
                      );

                      parsedResults.forEach(executionDetail => {
                        // The actual output is in the "Output" field as a stringified JSON
                        if (executionDetail.Output) {
                          try {
                            const result = JSON.parse(executionDetail.Output);
                            console.log(
                              `  - companyId: ${result.companyId}, shouldProcess: ${result.shouldProcess}, decisionReason: ${result.decisionReason}`,
                            );
                            results.push(result);
                          } catch (parseError) {
                            console.error(
                              `Error parsing Output field for execution ${executionDetail.ExecutionArn}:`,
                              parseError,
                            );
                          }
                        } else {
                          console.warn(
                            `No Output field found for execution ${executionDetail.ExecutionArn}`,
                          );
                        }
                      });
                    } else {
                      // Single result - also needs Output field parsing
                      if (parsedResults.Output) {
                        try {
                          const result = JSON.parse(parsedResults.Output);
                          console.log(
                            `Single result from ${succeededItem.Key}: companyId=${result.companyId}`,
                          );
                          results.push(result);
                        } catch (parseError) {
                          console.error(
                            `Error parsing Output field:`,
                            parseError,
                          );
                        }
                      }
                    }
                  }
                } catch (error) {
                  console.error(
                    `Error reading succeeded result ${succeededItem.Key}:`,
                    error,
                  );
                }
              }
            }
          }

          // Process failed results (keeping existing logic)
          if (
            manifest.ResultFiles.FAILED &&
            Array.isArray(manifest.ResultFiles.FAILED)
          ) {
            console.log(
              `Processing ${manifest.ResultFiles.FAILED.length} failed results from manifest`,
            );

            for (const failedItem of manifest.ResultFiles.FAILED) {
              if (failedItem.Key) {
                try {
                  const resultBucket = manifest.DestinationBucket || bucket;
                  const failedCommand = new GetObjectCommand({
                    Bucket: resultBucket,
                    Key: failedItem.Key,
                  });

                  const failedResponse = await s3Client.send(failedCommand);
                  const failedContent =
                    await failedResponse.Body?.transformToString();

                  if (failedContent) {
                    const failedData = JSON.parse(failedContent);
                    console.log(
                      `Found failed results in ${failedItem.Key}`,
                      failedData,
                    );

                    const failedResults = Array.isArray(failedData)
                      ? failedData
                      : [failedData];

                    const processedFailedResults = failedResults.map(item => ({
                      companyId:
                        item.companyId || item.Input?.companyId || 'unknown',
                      websiteUrl:
                        item.websiteUrl || item.Input?.websiteUrl || '',
                      hasExistingProfile: false,
                      shouldProcess: false,
                      decisionReason: 'processing_failed',
                      error: item.Error ||
                        item.error || {
                          message:
                            item.ErrorMessage ||
                            item.Cause ||
                            'Step Functions execution failed',
                        },
                    }));

                    results.push(...processedFailedResults);
                    console.log(
                      `Added ${processedFailedResults.length} failed results from ${failedItem.Key}`,
                    );
                  }
                } catch (error) {
                  console.error(
                    `Error reading failed result ${failedItem.Key}:`,
                    error,
                  );
                }
              }
            }
          }
        }

        console.log(
          `Successfully processed manifest, found ${results.length} total results`,
        );
        return results;
      }
    }

    // Original logic for when we have a prefix (folder path)
    const prefix = prefixOrKey;

    // Step Functions Map ResultWriter creates a UUID subfolder under the prefix
    // We need to list the contents to find the UUID folder, then read manifest.json from it
    const listCommand = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: prefix.endsWith('/') ? prefix : `${prefix}/`,
      Delimiter: '/',
    });

    console.log(
      'ListObjectsV2 command:',
      JSON.stringify(
        {
          Bucket: bucket,
          Prefix: prefix.endsWith('/') ? prefix : `${prefix}/`,
          Delimiter: '/',
        },
        null,
        2,
      ),
    );

    const listResponse = await s3Client.send(listCommand);

    console.log(
      'ListObjectsV2 response:',
      JSON.stringify(
        {
          CommonPrefixes: listResponse.CommonPrefixes,
          Contents: listResponse.Contents?.map(c => ({
            Key: c.Key,
            Size: c.Size,
          })),
          KeyCount: listResponse.KeyCount,
          IsTruncated: listResponse.IsTruncated,
        },
        null,
        2,
      ),
    );

    // Find the UUID subfolder (it will be in CommonPrefixes)
    if (listResponse.CommonPrefixes && listResponse.CommonPrefixes.length > 0) {
      // Get the first subfolder (should be the UUID folder)
      const uuidPrefix = listResponse.CommonPrefixes[0].Prefix;
      console.log(`Found ResultWriter UUID folder: ${uuidPrefix}`);

      // Now read the manifest from the UUID folder
      const manifestKey = `${uuidPrefix}manifest.json`;
      console.log(`Reading manifest from: ${manifestKey}`);

      // Retry reading the manifest in case ResultWriter just finished
      const manifestContent = await retryS3Operation(async () => {
        const manifestCommand = new GetObjectCommand({
          Bucket: bucket,
          Key: manifestKey,
        });
        const manifestResponse = await s3Client.send(manifestCommand);
        return await manifestResponse.Body?.transformToString();
      }, `Reading manifest from ${manifestKey}`);

      if (manifestContent) {
        const manifest: MapStateManifest = JSON.parse(manifestContent);
        console.log('Found manifest.json:', JSON.stringify(manifest, null, 2));

        const results: ProcessedCompany[] = [];

        // Check if manifest has the expected structure
        if (manifest.ResultFiles) {
          console.log(
            'Manifest ResultFiles - SUCCEEDED:',
            manifest.ResultFiles.SUCCEEDED?.length || 0,
            'FAILED:',
            manifest.ResultFiles.FAILED?.length || 0,
            'PENDING:',
            manifest.ResultFiles.PENDING?.length || 0,
          );

          // Process succeeded results
          if (
            manifest.ResultFiles.SUCCEEDED &&
            Array.isArray(manifest.ResultFiles.SUCCEEDED)
          ) {
            console.log(
              `Processing ${manifest.ResultFiles.SUCCEEDED.length} succeeded results from manifest`,
            );

            for (const succeededItem of manifest.ResultFiles.SUCCEEDED) {
              if (succeededItem.Key) {
                try {
                  console.log(
                    `Reading succeeded result file: ${succeededItem.Key} (Size: ${succeededItem.Size})`,
                  );

                  // Use the destination bucket from manifest if available, otherwise use the provided bucket
                  const resultBucket = manifest.DestinationBucket || bucket;

                  // Retry reading the SUCCEEDED file in case of timing issues
                  const resultContent = await retryS3Operation(async () => {
                    const resultCommand = new GetObjectCommand({
                      Bucket: resultBucket,
                      Key: succeededItem.Key,
                    });
                    const resultResponse = await s3Client.send(resultCommand);
                    return await resultResponse.Body?.transformToString();
                  }, `Reading SUCCEEDED file ${succeededItem.Key}`);

                  if (resultContent) {
                    // The SUCCEEDED files contain arrays of results
                    const parsedResults = JSON.parse(resultContent);

                    if (Array.isArray(parsedResults)) {
                      console.log(
                        `Found ${parsedResults.length} results in ${succeededItem.Key}`,
                      );

                      // Log summary of each result
                      parsedResults.forEach(result => {
                        console.log(
                          `  - companyId: ${result.companyId}, shouldProcess: ${result.shouldProcess}, decisionReason: ${result.decisionReason}`,
                        );
                        results.push(result);
                      });
                    } else {
                      // Single result (unlikely for Map state but handle it)
                      console.log(
                        `Single result from ${succeededItem.Key}: companyId=${parsedResults.companyId}`,
                      );
                      results.push(parsedResults);
                    }
                  }
                } catch (error) {
                  console.error(
                    `Error reading succeeded result ${succeededItem.Key}:`,
                    error,
                  );
                }
              }
            }
          }

          // Process failed results (these should be marked as failed in our report)
          if (
            manifest.ResultFiles.FAILED &&
            Array.isArray(manifest.ResultFiles.FAILED)
          ) {
            console.log(
              `Processing ${manifest.ResultFiles.FAILED.length} failed results from manifest`,
            );

            for (const failedItem of manifest.ResultFiles.FAILED) {
              console.log(
                'Failed item in manifest:',
                JSON.stringify(failedItem),
              );
              // Failed items from Map state might have error details we need to parse
              if (failedItem.Key) {
                try {
                  const resultBucket = manifest.DestinationBucket || bucket;
                  const failedCommand = new GetObjectCommand({
                    Bucket: resultBucket,
                    Key: failedItem.Key,
                  });

                  const failedResponse = await s3Client.send(failedCommand);
                  const failedContent =
                    await failedResponse.Body?.transformToString();

                  if (failedContent) {
                    const failedData = JSON.parse(failedContent);
                    console.log(
                      `Found failed results in ${failedItem.Key}`,
                      failedData,
                    );

                    // Handle both array and single object formats
                    const failedResults = Array.isArray(failedData)
                      ? failedData
                      : [failedData];

                    // Map failed results to include error information
                    const processedFailedResults = failedResults.map(item => ({
                      companyId:
                        item.companyId || item.Input?.companyId || 'unknown',
                      websiteUrl:
                        item.websiteUrl || item.Input?.websiteUrl || '',
                      hasExistingProfile: false,
                      shouldProcess: false,
                      decisionReason: 'processing_failed',
                      error: item.Error ||
                        item.error || {
                          message:
                            item.ErrorMessage ||
                            item.Cause ||
                            'Step Functions execution failed',
                        },
                    }));

                    results.push(...processedFailedResults);
                    console.log(
                      `Added ${processedFailedResults.length} failed results from ${failedItem.Key}`,
                    );
                  }
                } catch (error) {
                  console.error(
                    `Error reading failed result ${failedItem.Key}:`,
                    error,
                  );
                }
              }
            }
          }
        }

        console.log(
          `Successfully processed manifest, found ${results.length} total results`,
        );
        return results;
      }
    } else {
      console.log(
        'No UUID folder found in ResultWriter output, falling back to direct listing',
      );
    }

    // Fallback: list all files with the prefix if no manifest found
    const fallbackListCommand = new ListObjectsV2Command({
      Bucket: bucket,
      Prefix: prefix,
      MaxKeys: 1000,
    });

    const fallbackListResponse = await s3Client.send(fallbackListCommand);
    const results: ProcessedCompany[] = [];

    if (
      !fallbackListResponse.Contents ||
      fallbackListResponse.Contents.length === 0
    ) {
      console.log('No result files found in S3 with prefix:', prefix);
      return results;
    }

    console.log(
      `Found ${fallbackListResponse.Contents.length} files in S3 with prefix ${prefix}`,
    );

    // Log all file names to debug
    fallbackListResponse.Contents.forEach(obj => {
      console.log(`  - ${obj.Key}`);
    });

    for (const object of fallbackListResponse.Contents) {
      // Skip directories, manifest files, and the prefix itself
      if (
        !object.Key ||
        object.Key === prefix ||
        object.Key.endsWith('/') ||
        object.Key.endsWith('manifest.json')
      ) {
        console.log(`Skipping: ${object.Key}`);
        continue;
      }

      try {
        console.log(`Reading result file: ${object.Key}`);
        const getCommand = new GetObjectCommand({
          Bucket: bucket,
          Key: object.Key,
        });

        const response = await s3Client.send(getCommand);
        const bodyContents = await response.Body?.transformToString();

        if (bodyContents) {
          const parsedResult = JSON.parse(bodyContents);

          // Check if this is a Step Functions execution result or direct output
          if (Array.isArray(parsedResult)) {
            console.log(`  Found array with ${parsedResult.length} results`);

            // Check if array contains execution details or direct results
            parsedResult.forEach(item => {
              if (item.Output && typeof item.Output === 'string') {
                // This is a Step Functions execution detail, parse the Output field
                try {
                  const result = JSON.parse(item.Output);
                  console.log(
                    `    - companyId: ${result.companyId}, shouldProcess: ${result.shouldProcess}`,
                  );
                  results.push(result);
                } catch (parseError) {
                  console.error(`    Error parsing Output field:`, parseError);
                }
              } else if (item.companyId) {
                // This is already a parsed result
                console.log(
                  `    - companyId: ${item.companyId}, shouldProcess: ${item.shouldProcess}`,
                );
                results.push(item);
              }
            });
          } else {
            // Single result - check if it's execution detail or direct result
            if (
              parsedResult.Output &&
              typeof parsedResult.Output === 'string'
            ) {
              // Step Functions execution detail
              try {
                const result = JSON.parse(parsedResult.Output);
                console.log(
                  `  Found single result: companyId=${result.companyId}, shouldProcess=${result.shouldProcess}`,
                );
                results.push(result);
              } catch (parseError) {
                console.error(`  Error parsing Output field:`, parseError);
              }
            } else if (parsedResult.companyId) {
              // Direct result
              console.log(
                `  Found single result: companyId=${parsedResult.companyId}, shouldProcess=${parsedResult.shouldProcess}`,
              );
              results.push(parsedResult);
            }
          }
        }
      } catch (error) {
        console.error(`Error reading file ${object.Key}:`, error);
      }
    }

    console.log(`Successfully read ${results.length} results from S3 files`);
    return results;
  } catch (error) {
    console.error('Error reading results from S3:', error);
    throw error;
  }
}

function categorizeResults(results: ProcessedCompany[]): {
  processed: ProcessedCompany[];
  skipped: ProcessedCompany[];
  failed: ProcessedCompany[];
} {
  const processed: ProcessedCompany[] = [];
  const skipped: ProcessedCompany[] = [];
  const failed: ProcessedCompany[] = [];

  console.log('Categorizing results...');

  for (const result of results) {
    // Priority 1: Check for failures
    if (result.error || result.decisionReason === 'processing_failed') {
      failed.push(result);
      console.log(
        `Failed: ${result.companyId} - ${result.error?.message || result.decisionReason}`,
      );
    }
    // Priority 2: Check for successful processing
    // A company is processed if:
    // - It was meant to be processed (shouldProcess=true) AND
    // - It has an s3Location (successful output)
    // This includes both new profiles and regenerated existing profiles
    else if (result.shouldProcess && result.s3Location) {
      processed.push(result);
      const action =
        result.decisionReason === 'force_regenerate'
          ? 'regenerated'
          : result.hasExistingProfile
            ? 'updated'
            : 'created';
      console.log(
        `Processed: ${result.companyId} - ${action} and saved to ${result.s3Location}`,
      );
    }
    // Priority 3: Everything else is skipped
    // This includes:
    // - Existing profiles that weren't regenerated (hasExistingProfile=true, shouldProcess=false)
    // - Companies not selected for processing
    // - Companies that were meant to be processed but have no s3Location (failed silently)
    else {
      skipped.push(result);
      const reason =
        result.hasExistingProfile && !result.shouldProcess
          ? 'existing profile (not regenerated)'
          : result.shouldProcess && !result.s3Location
            ? 'no output generated'
            : !result.shouldProcess
              ? 'not selected for processing'
              : 'unknown reason';
      console.log(
        `Skipped: ${result.companyId} - ${reason} (hasExistingProfile: ${result.hasExistingProfile}, shouldProcess: ${result.shouldProcess}, decisionReason: ${result.decisionReason})`,
      );
    }
  }

  console.log(
    `Categorization complete: ${processed.length} processed, ${skipped.length} skipped, ${failed.length} failed`,
  );
  return { processed, skipped, failed };
}

function formatSlackMessage(
  executionSummary: ReportResultsOutput['executionSummary'],
  companySummaries: CompanySummary[],
  dryRun: boolean,
  executionName?: string,
  environment?: string,
  executionArn?: string,
): string {
  const emoji = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️',
    production: '🚀',
    staging: '🧪',
    development: '🔧',
  };

  const statusEmoji =
    executionSummary.failedCount > 0
      ? emoji.error
      : executionSummary.skippedCount > executionSummary.processedCount
        ? emoji.warning
        : emoji.success;

  const envEmoji =
    environment === 'production'
      ? emoji.production
      : environment === 'staging'
        ? emoji.staging
        : emoji.development;

  const envName = environment ? environment.toUpperCase() : 'UNKNOWN';
  const mode = dryRun ? ' [DRY RUN]' : '';

  // Construct Step Functions execution URL if we have the ARN
  let executionInfo = '';
  if (executionName) {
    executionInfo = `\nExecution: \`${executionName}\``;

    if (executionArn) {
      // Parse the ARN to get region and construct the console URL
      // Execution ARN format: arn:aws:states:region:account:execution:stateMachineName:executionName
      const arnParts = executionArn.split(':');
      if (arnParts.length >= 7) {
        const region = arnParts[3] || process.env.AWS_REGION || 'us-east-1';

        // Construct the console URL using the full ARN
        const consoleUrl = `https://console.aws.amazon.com/states/home?region=${region}#/v2/executions/details/${encodeURIComponent(executionArn)}`;
        executionInfo += ` <${consoleUrl}|View in AWS Console>`;
      }
    }
  }

  let message = `${envEmoji} *[${envName}]* ${statusEmoji} *Brand Profile Generation Report*${mode}${executionInfo}\n\n`;

  message += `*Summary:*\n`;
  message += `• Total Companies: ${executionSummary.totalCompanies}\n`;
  message += `• Successfully Processed: ${executionSummary.processedCount}\n`;
  message += `• Skipped: ${executionSummary.skippedCount}\n`;
  message += `• Failed: ${executionSummary.failedCount}\n`;

  if (executionSummary.executionTime) {
    message += `• Execution Time: ${executionSummary.executionTime}\n`;
  }

  const failed = companySummaries.filter(c => c.status === 'failed');
  if (failed.length > 0) {
    message += `\n${emoji.error} *Failed Companies:*\n`;
    failed.slice(0, 5).forEach(company => {
      message += `• ${company.companyId}: ${company.reason || 'Unknown error'}\n`;
    });
    if (failed.length > 5) {
      message += `• _...and ${failed.length - 5} more_\n`;
    }
  }

  const skipped = companySummaries.filter(c => c.status === 'skipped');
  if (skipped.length > 0 && skipped.length <= 10) {
    message += `\n${emoji.info} *Skipped Companies:*\n`;
    const byReason = new Map<string, number>();
    skipped.forEach(company => {
      const reason = company.reason || 'unknown';
      byReason.set(reason, (byReason.get(reason) || 0) + 1);
    });

    byReason.forEach((count, reason) => {
      message += `• ${reason}: ${count} companies\n`;
    });
  }

  const processed = companySummaries.filter(c => c.status === 'processed');
  if (processed.length > 0 && processed.length <= 5) {
    message += `\n${emoji.success} *Successfully Processed:*\n`;
    processed.forEach(company => {
      message += `• ${company.companyId} (${company.websiteUrl})\n`;
    });
  }

  // Add footer with environment and timestamp
  const timestamp = new Date().toISOString().replace('T', ' ').split('.')[0];
  message += `\n───────────────────\n`;
  message += `_Environment: ${envName} | Generated: ${timestamp} UTC_`;

  return message;
}

function calculateExecutionTime(startTime?: string): string | undefined {
  if (!startTime) return undefined;

  try {
    const start = new Date(startTime);
    const end = new Date();
    const diffMs = end.getTime() - start.getTime();

    const hours = Math.floor(diffMs / 3600000);
    const minutes = Math.floor((diffMs % 3600000) / 60000);
    const seconds = Math.floor((diffMs % 60000) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  } catch (error) {
    console.error('Error calculating execution time:', error);
    return undefined;
  }
}

export const handler: Handler<
  ReportResultsInput,
  ReportResultsOutput
> = async event => {
  console.log(
    'ReportResults Lambda invoked with:',
    JSON.stringify({
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
      dryRun: event.dryRun,
      regenerateIfExisting: event.regenerateIfExisting,
      resultsType: Array.isArray(event.results)
        ? 'array'
        : typeof event.results,
      resultsCount: Array.isArray(event.results) ? event.results.length : 'N/A',
      hasResultWriterDetails:
        event.results && 'ResultWriterDetails' in event.results,
    }),
  );

  // Log the full results object structure for debugging
  if (event.results && !Array.isArray(event.results)) {
    console.log(
      'Full results object structure:',
      JSON.stringify(event.results, null, 2),
    );
  }

  let resultsArray: ProcessedCompany[] = [];

  if (Array.isArray(event.results)) {
    resultsArray = event.results;
    console.log(`Received ${resultsArray.length} direct results`);
  } else if (event.results && typeof event.results === 'object') {
    if ('ResultWriterDetails' in event.results) {
      const details = event.results.ResultWriterDetails;
      console.log(
        `Results written to S3: s3://${details.Bucket}/${details.Key}`,
      );

      if (details.Bucket && details.Key) {
        resultsArray = await readResultsFromS3(details.Bucket, details.Key);
      } else {
        console.log(
          'No S3 location provided in ResultWriterDetails, assuming empty results',
        );
        resultsArray = [];
      }
    } else {
      // Handle other object structures (fallback)
      console.log('Attempting to extract results from object structure');
      resultsArray = Object.values(event.results);
    }
  }

  console.log(`Total results to process: ${resultsArray.length}`);

  const { processed, skipped, failed } = categorizeResults(resultsArray);

  const companySummaries: CompanySummary[] = [
    ...processed.map(
      (c): CompanySummary => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        status: 'processed',
        reason: 'successfully_generated',
        s3Location: c.s3Location,
        timestamp: new Date().toISOString(),
      }),
    ),
    ...skipped.map(
      (c): CompanySummary => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        status: 'skipped',
        reason:
          c.hasExistingProfile && !c.shouldProcess
            ? 'existing_profile'
            : c.shouldProcess && !c.s3Location
              ? 'no output generated'
              : !c.shouldProcess
                ? 'not selected for processing'
                : c.decisionReason || 'unknown',
        timestamp: new Date().toISOString(),
      }),
    ),
    ...failed.map(
      (c): CompanySummary => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        status: 'failed',
        reason: c.error?.message || c.decisionReason || 'processing_failed',
        timestamp: new Date().toISOString(),
      }),
    ),
  ];

  const totalCompanies = resultsArray.length;
  const processedCount = processed.length;
  const skippedCount = skipped.length;
  const failedCount = failed.length;

  const executionSummary: ReportResultsOutput['executionSummary'] = {
    totalCompanies,
    processedCount,
    skippedCount,
    failedCount,
    executionTime: calculateExecutionTime(event.executionStartTime),
  };

  console.log(
    `Processing summary: Total=${totalCompanies}, Processed=${processedCount}, Skipped=${skippedCount}, Failed=${failedCount}`,
  );

  const s3Bucket =
    process.env.STEP_FUNCTION_S3_BUCKET ||
    config.brandProfilesS3?.bucketName ||
    `brand-profile-generator-${process.env.ENVIRONMENT || 'dev'}`;

  const isDryRun = event.dryRun ?? true;
  const dryRunSuffix = isDryRun ? '-dry-run' : '';

  let s3Key: string;
  if (event.executionName && event.executionStartTime) {
    const executionDate = event.executionStartTime.split('T')[0];
    s3Key = `brand-profile-jobs/${executionDate}/${event.executionName}${dryRunSuffix}/execution-report${dryRunSuffix}.json`;
  } else {
    const isoTimestamp = new Date().toISOString().replace(/:/g, '-');
    const dateOnly = isoTimestamp.split('T')[0];
    s3Key = `brand-profile-jobs/${dateOnly}/direct-${isoTimestamp}${dryRunSuffix}/execution-report${dryRunSuffix}.json`;
  }

  const slackMessage = formatSlackMessage(
    executionSummary,
    companySummaries,
    isDryRun,
    event.executionName,
    config.environment,
    event.executionArn,
  );

  const fullReport = {
    metadata: {
      executionName: event.executionName,
      executionStartTime: event.executionStartTime,
      executionEndTime: new Date().toISOString(),
      generatedAt: new Date().toISOString(),
      dryRun: isDryRun,
      regenerateIfExisting: event.regenerateIfExisting || false,
      environment: process.env.ENVIRONMENT || 'dev',
    },
    summary: executionSummary,
    companies: {
      processed: processed.map(c => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        s3Location: c.s3Location,
        databaseSaved: c.databaseSaved,
      })),
      skipped: skipped.map(c => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        reason: c.hasExistingProfile ? 'existing_profile' : c.decisionReason,
      })),
      failed: failed.map(c => ({
        companyId: c.companyId,
        websiteUrl: c.websiteUrl,
        reason: c.error?.message || c.decisionReason || 'processing_failed',
        error: c.error,
      })),
    },
    statistics: {
      byStatus: {
        processed: processedCount,
        skipped: skippedCount,
        failed: failedCount,
      },
      byReason: {
        existing_profile: skipped.filter(c => c.hasExistingProfile).length,
        processing_failed: failedCount,
        other: skipped.filter(
          c =>
            !c.hasExistingProfile && c.decisionReason !== 'processing_failed',
        ).length,
      },
    },
  };

  const putObjectParams: PutObjectCommandInput = {
    Bucket: s3Bucket,
    Key: s3Key,
    Body: JSON.stringify(fullReport, null, 2),
    ContentType: 'application/json',
    Metadata: {
      executionName: event.executionName || 'unknown',
      dryRun: String(isDryRun),
      totalCompanies: String(totalCompanies),
      processedCount: String(processedCount),
      skippedCount: String(skippedCount),
      failedCount: String(failedCount),
    },
    Tagging: `dryRun=${isDryRun}&environment=${
      process.env.ENVIRONMENT || 'dev'
    }&totalCompanies=${totalCompanies}&processedCount=${processedCount}&skippedCount=${skippedCount}&failedCount=${failedCount}`,
  };

  await s3Client.send(new PutObjectCommand(putObjectParams));

  console.log(`Execution report written to s3://${s3Bucket}/${s3Key}`);

  if (config.slack?.enabled && config.slack?.webhookUrl) {
    try {
      const response = await fetch(config.slack.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: slackMessage }),
      });

      if (!response.ok) {
        console.error(
          'Failed to send Slack notification:',
          response.statusText,
        );
      } else {
        console.log('Slack notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending Slack notification:', error);
    }
  }

  return {
    s3Bucket,
    s3Key,
    executionSummary,
    companySummaries,
    slackMessage,
  };
};
