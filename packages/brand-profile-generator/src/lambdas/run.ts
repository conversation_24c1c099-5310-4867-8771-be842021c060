// import { randomUUID } from 'node:crypto';
// import { handler, poll } from './brand-research';

// const input = {
//   companyId: '14507dda-d18a-4125-9e71-d5e5765330e1',
//   websiteUrl: 'https://timallenproperties.com/',
//   dryRun: true,
// };

// const context = { Execution: { id: randomUUID() } } as any;

// const callback = () => {};

// (async () => {
//   const result = await handler(input, context, callback);
//   const pollResult = await poll();

//   console.log({ result, pollResult });
// })();
