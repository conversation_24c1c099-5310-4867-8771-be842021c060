import './instrumentation';
import { LangfuseClient } from '@langfuse/client';
import { observeOpenAI } from '@langfuse/openai';
import { Handler } from 'aws-lambda';
import OpenAI from 'openai';

import { BrandResearchError, TransientBrandResearchError } from '../errors';

export interface BrandResearchInput {
  companyId: string;
  websiteUrl: string;
  dryRun?: boolean;
  executionId?: string;
}

export interface BrandResearchOutput {
  companyId: string;
  websiteUrl: string;
  researchRequestId: string;
}

export interface ResearchAsyncRequest {
  model: string;
  background: boolean;
  instructions: string;
  tools: Array<{
    type: 'web_search';
    filters?: {
      search_context_size: 'small' | 'medium' | 'large';
    };
  }>;
  include?: string[];
  max_tool_calls?: number;
  max_output_tokens?: number;
  stream: boolean;
  parallel_tool_calls?: boolean;
  temperature?: number;
}

export interface ResearchAsyncResponse {
  id: string;
  model: string;
  created_at: number;
  status: 'CREATED' | 'PENDING' | 'COMPLETED' | 'FAILED';
}

const promptSettings = {
  model: 'gpt-5-mini',
  text: {
    format: {
      type: 'text',
    },
    verbosity: 'high',
  },
  reasoning: {
    effort: 'medium',
    summary: 'auto',
  },
  tools: [
    {
      type: 'web_search',
      filters: null,
      search_context_size: 'high',
      user_location: {
        type: 'approximate',
        city: null,
        country: null,
        region: null,
        timezone: null,
      },
    },
  ],
  store: true,
  include: ['reasoning.encrypted_content', 'web_search_call.action.sources'],
  background: true,
  stream: false,
};

// TODO: replace console calls with proper logger
export const handler: Handler<
  BrandResearchInput,
  BrandResearchOutput
> = async event => {
  console.log('BrandResearch Lambda invoked with:', JSON.stringify(event));

  try {
    // Default to true (safe mode) if not explicitly set
    const isDryRun = event.dryRun ?? true;

    if (isDryRun) {
      console.log('Dry run mode: Using mock research request ID');
    } else {
      console.log('Production mode: Would call OpenAI Deep Research API');
    }

    if (isDryRun) {
      await Promise.resolve(); // Placeholder await
      const dateStr = new Date().toISOString().replace(/[:.]/g, '-'); // Format: YYYY-MM-DDTHH-MM-SS-sssZ
      const prefix = isDryRun ? 'mock-request' : 'request';
      const researchRequestId = `${prefix}-${dateStr}-${event.companyId}`;

      return {
        companyId: event.companyId,
        websiteUrl: event.websiteUrl,
        researchRequestId,
      };
    }

    const { companyId, websiteUrl } = event;
    const langfuseClient = new LangfuseClient();

    const promptName = 'brand_research';

    // TODO: remove protocol and subdomains
    const cleanDomain = new URL(websiteUrl).hostname;
    const prompt = await langfuseClient.prompt.get(promptName);
    const compiledPrompt = prompt.compile({ domain: cleanDomain });

    const openai = observeOpenAI(new OpenAI(), {
      traceName: promptName,
      generationMetadata: { companyId },
      userId: companyId,
      sessionId: event.executionId,
      langfusePrompt: prompt,
    });

    // @ts-expect-error the library has a known type error
    const response = await openai.responses.create({
      ...promptSettings,
      input: compiledPrompt,
    });

    console.log({ response });

    return {
      companyId: event.companyId,
      websiteUrl: event.websiteUrl,
      researchRequestId: response.id,
    };
  } catch (error) {
    console.error('Error in brand research:', error);

    // Check if it's a transient error (e.g., network issues, rate limits)
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('rate limit') ||
        message.includes('429') ||
        message.includes('503') ||
        message.includes('502')
      ) {
        throw new TransientBrandResearchError(
          `Transient error during brand research for company ${event.companyId}`,
          error,
          { companyId: event.companyId, websiteUrl: event.websiteUrl },
        );
      }
    }

    // Non-transient error
    throw new BrandResearchError(
      `Failed to initiate brand research for company ${event.companyId}`,
      error instanceof Error ? error : new Error(String(error)),
      { companyId: event.companyId, websiteUrl: event.websiteUrl },
    );
  }
};
