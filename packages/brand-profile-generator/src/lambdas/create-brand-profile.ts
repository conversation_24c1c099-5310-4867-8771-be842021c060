import './instrumentation';

import { LangfuseClient } from '@langfuse/client';
import { observeOpenAI } from '@langfuse/openai';
import { Handler } from 'aws-lambda';
import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import {
  ResponseCreateParamsNonStreaming,
  ResponseTextConfig,
} from 'openai/resources/responses/responses';
import { z } from 'zod';

import {
  CreateBrandProfileError,
  TransientCreateBrandProfileError,
} from '../errors';
import { BrandProfile } from '../types';

export interface CreateBrandProfileInput {
  companyId: string;
  dryRun?: boolean;
  executionId?: string;
  researchData: {
    content: string;
    searchResults?: Array<{
      title: string;
      url: string;
      snippet?: string;
      date?: string;
    }>;
    sources?: Array<{
      url: string;
      title?: string;
    }>;
    usage?: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
      reasoning_tokens?: number;
    };
  };
}

export interface CreateBrandProfileOutput {
  companyId: string;
  brandProfile: BrandProfile;
}

// Define the structured output schema for the brand profile
const BrandProfileSchema = z.object({
  aboutTheBrand: z
    .string()
    .describe('Comprehensive overview and description of the brand'),
  strategicFocus: z
    .string()
    .describe('Strategic objectives and key focus areas'),
  valueProposition: z
    .string()
    .describe('Unique value proposition and competitive advantages'),
  idealCustomerProfiles: z
    .string()
    .describe('Target audience segments and detailed customer personas'),
  missionAndCoreValues: z
    .string()
    .describe('Mission statement and fundamental core values'),
  brandPointOfView: z
    .string()
    .describe('Brand positioning, perspective, and market stance'),
  toneOfVoice: z
    .string()
    .describe(
      'Communication style, tone guidelines, and voice characteristics',
    ),
  ctaText: z
    .string()
    .describe('Examples of effective call-to-action text for the brand'),
  authorPersona: z
    .string()
    .describe('Content creation voice, author persona, and writing style'),
});

const promptSettings: ResponseCreateParamsNonStreaming = {
  model: 'gpt-5-mini',
  text: {
    format: {
      type: 'json_schema',
      ...zodResponseFormat(BrandProfileSchema, 'brand_profile').json_schema,
    },
    verbosity: 'high',
  } as ResponseTextConfig,
  reasoning: {
    effort: 'medium',
    summary: 'auto',
  },
  tools: [],
  // @ts-expect-error Their types do not include web_search_call, but their official docs do
  include: ['reasoning.encrypted_content', 'web_search_call.action.sources'],
};

export const handler: Handler<
  CreateBrandProfileInput,
  CreateBrandProfileOutput
> = async event => {
  console.log(
    'CreateBrandProfile Lambda invoked with:',
    JSON.stringify({
      companyId: event.companyId,
      dryRun: event.dryRun,
      researchDataLength: event.researchData.content.length,
      sourcesCount: event.researchData.sources?.length || 0,
    }),
  );

  try {
    // Default to true (safe mode) if not explicitly set
    const isDryRun = event.dryRun ?? true;

    if (isDryRun) {
      console.log('Dry run mode: Using mock brand profile synthesis');

      // Return mock data for dry run
      const brandProfile: BrandProfile = {
        companyId: event.companyId,
        generatedAt: new Date().toISOString(),
        aboutTheBrand:
          '[DRY RUN] A leading real estate company focused on providing exceptional service and innovative solutions in the property market. Known for their customer-centric approach and deep local market expertise.',
        strategicFocus:
          '[DRY RUN] Strategic focus on digital transformation, sustainable development, and community engagement. Key objectives include expanding market share, enhancing customer experience, and leveraging technology for competitive advantage.',
        valueProposition:
          '[DRY RUN] Unmatched local expertise combined with cutting-edge technology to deliver seamless real estate experiences. We make buying, selling, and investing in property simple, transparent, and rewarding.',
        idealCustomerProfiles:
          '[DRY RUN] Primary: First-time homebuyers aged 28-40 seeking guidance and support. Secondary: Property investors looking for market insights and opportunities. Tertiary: Homeowners planning to upgrade or downsize.',
        missionAndCoreValues:
          '[DRY RUN] Mission: To transform real estate experiences through innovation, integrity, and exceptional service. Core Values: Trust, Innovation, Excellence, Community, Sustainability.',
        brandPointOfView:
          "[DRY RUN] Real estate is more than transactions—it's about building communities and creating lasting value. We believe in transparent, ethical practices that benefit all stakeholders.",
        toneOfVoice:
          '[DRY RUN] Professional yet approachable, knowledgeable but not condescending, optimistic and solution-oriented. We speak with clarity, warmth, and authority.',
        ctaText:
          "[DRY RUN] 'Start Your Journey Home' | 'Discover Your Dream Property' | 'Get Expert Guidance Today' | 'Unlock Your Property's Potential'",
        authorPersona:
          '[DRY RUN] A trusted real estate advisor who combines deep market knowledge with genuine care for clients. Writes with authority and empathy, making complex topics accessible.',
        researchSources: event.researchData.sources || [],
      };

      return {
        companyId: event.companyId,
        brandProfile,
      };
    }

    console.log(
      'Production mode: Calling OpenAI API for brand profile synthesis',
    );

    // Initialize Langfuse client to get the prompt
    const langfuseClient = new LangfuseClient();
    const promptName = 'create_brand_profile';

    console.log(`Fetching prompt: ${promptName}`);
    const prompt = await langfuseClient.prompt.get(promptName, {
      type: 'chat',
    });

    // Compile the prompt with the research data
    const compiledPrompt = prompt.compile({
      research: event.researchData.content,
    });

    // Initialize OpenAI client with Langfuse observation
    const openai = observeOpenAI(new OpenAI(), {
      traceName: promptName,
      generationMetadata: {
        companyId: event.companyId,
        step: 'create_profile',
      },
      userId: event.companyId,
      sessionId: event.executionId,
      langfusePrompt: prompt,
    });

    console.log('Calling OpenAI API with structured output schema');

    // Call OpenAI with structured output using Zod schema
    const response = await openai.responses.create({
      ...promptSettings,
      input: compiledPrompt,
    });

    // Extract the structured response
    const structuredResponse = response.output_text;
    if (!structuredResponse) {
      throw new CreateBrandProfileError(
        'No response content from OpenAI',
        new Error('Empty response'),
        { companyId: event.companyId },
      );
    }

    // Parse the JSON response
    let parsedProfile;
    try {
      parsedProfile = JSON.parse(structuredResponse);
    } catch (parseError) {
      throw new CreateBrandProfileError(
        'Failed to parse OpenAI response as JSON',
        parseError instanceof Error
          ? parseError
          : new Error(String(parseError)),
        { companyId: event.companyId, response: structuredResponse },
      );
    }

    // Validate against schema
    const validationResult = BrandProfileSchema.safeParse(parsedProfile);
    if (!validationResult.success) {
      throw new CreateBrandProfileError(
        'OpenAI response does not match expected schema',
        new Error(validationResult.error.message),
        { companyId: event.companyId, errors: validationResult.error.errors },
      );
    }

    // Construct the final brand profile
    const brandProfile: BrandProfile = {
      companyId: event.companyId,
      generatedAt: new Date().toISOString(),
      ...validationResult.data,
      researchSources: event.researchData.sources || [],
    };

    console.log(
      `Brand profile created successfully for company ${event.companyId}`,
    );

    return {
      companyId: event.companyId,
      brandProfile,
    };
  } catch (error) {
    console.error('Error creating brand profile:', error);

    // Check if it's a transient error
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('rate limit') ||
        message.includes('429') ||
        message.includes('503') ||
        message.includes('502')
      ) {
        throw new TransientCreateBrandProfileError(
          `Transient error creating brand profile for company ${event.companyId}`,
          error,
          { companyId: event.companyId },
        );
      }
    }

    // Re-throw if it's already one of our custom errors
    if (
      error instanceof CreateBrandProfileError ||
      error instanceof TransientCreateBrandProfileError
    ) {
      throw error;
    }

    // Non-transient error
    throw new CreateBrandProfileError(
      `Failed to create brand profile for company ${event.companyId}`,
      error instanceof Error ? error : new Error(String(error)),
      { companyId: event.companyId },
    );
  }
};
