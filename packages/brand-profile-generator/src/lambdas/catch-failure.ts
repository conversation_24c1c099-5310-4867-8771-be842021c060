import { Context } from 'aws-lambda';

interface StepFunctionError {
  Error: string;
  Cause: string; // A stringified JSON object more likely.
}

interface CatchFailureEvent {
  companyId?: string;
  executionName?: string;
  error: StepFunctionError;
  context?: {
    companyId?: string;
    websiteUrl?: string;
    config?: Record<string, unknown>;
  };
  [prop: string]: any; // other stuff from the original step function
}

function processError(error: StepFunctionError): Record<string, unknown> {
  try {
    return JSON.parse(error.Cause);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (_e) {
    return { Error: error.Error, Cause: error.Cause };
  }
}

export const handler = (event: CatchFailureEvent, context: Context): void => {
  const companyId = event.companyId || event.context?.companyId;

  console.error('Brand profile generation failure caught', {
    lambdaName: 'catch-failure',
    requestId: context.awsRequestId,
    executionName: event.executionName,
    companyId,
    websiteUrl: event.context?.websiteUrl,
    error: processError(event.error),
    rawError: event.error,
  });

  // In production, you could:
  // 1. Send notifications (Slack, email, etc.)
  // 2. Update a database record to mark the failure
  // 3. Write to S3 for failure tracking
  // 4. Emit metrics to CloudWatch or DataDog

  // For now, we'll just log the error details
  console.log('Failure processing completed', {
    companyId,
    executionName: event.executionName,
  });
};
