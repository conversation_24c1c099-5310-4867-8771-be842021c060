import './instrumentation';
import { observeOpenAI } from '@langfuse/openai';
import { Handler } from 'aws-lambda';
import OpenAI from 'openai';

import {
  ResearchJobStatusError,
  TransientResearchJobStatusError,
} from '../errors';

export interface GetResearchJobStatusInput {
  companyId: string;
  researchRequestId: string;
  dryRun?: boolean;
  executionId?: string;
}

export interface GetResearchJobStatusOutput {
  companyId: string;
  researchRequestId: string;
  status:
    | 'validating'
    | 'queued'
    | 'in_progress'
    | 'finalizing'
    | 'completed'
    | 'failed'
    | 'expired'
    | 'cancelling'
    | 'cancelled';
  researchData?: ResearchData;
}

export interface ResearchData {
  content: string;
  searchResults?: Array<{
    title: string;
    url: string;
    snippet?: string;
    date?: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
    reasoning_tokens?: number;
    cached_tokens?: number;
  };
  sources?: Array<{
    url: string;
    title?: string;
  }>;
}

export interface ResearchAsyncStatusResponse {
  id: string;
  object: 'response';
  created_at: number;
  model: string;
  status:
    | 'validating'
    | 'queued'
    | 'in_progress'
    | 'finalizing'
    | 'completed'
    | 'failed'
    | 'expired'
    | 'cancelling'
    | 'cancelled';
  output?: Array<{
    type: 'message';
    id: string;
    status: 'completed';
    role: 'assistant';
    content: Array<{
      type: 'output_text' | 'tool_use';
      text?: string;
      tool_use?: {
        id: string;
        name: string;
        input?: unknown;
      };
      annotations?: Array<unknown>;
    }>;
  }>;
  usage?: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    output_tokens_details?: {
      reasoning_tokens?: number;
    };
    input_tokens_details?: {
      cached_tokens?: number;
    };
  };
  error?: {
    type: string;
    message: string;
  };
  metadata?: {
    reasoning?: {
      summary?: string;
    };
  };
  tools?: Array<{
    type: 'web_search';
    action?: {
      sources?: Array<{
        url: string;
        title?: string;
        snippet?: string;
      }>;
    };
  }>;
  // Legacy field that might still exist in some responses
  output_legacy?: {
    text: string;
    tool_calls?: Array<{
      type: 'web_search';
      action?: {
        sources?: Array<{
          url: string;
          title?: string;
          snippet?: string;
        }>;
      };
    }>;
  };
}

export const handler: Handler<
  GetResearchJobStatusInput,
  GetResearchJobStatusOutput
> = async event => {
  console.log(
    'GetResearchJobStatus Lambda invoked with:',
    JSON.stringify(event),
  );

  try {
    // Default to true (safe mode) if not explicitly set
    const isDryRun = event.dryRun ?? true;

    if (isDryRun) {
      console.log('Dry run mode: Returning mock research data immediately');

      // For dry run, simulate a completed status with mock data
      const mockPrefix = event.researchRequestId.startsWith('mock-request')
        ? 'Mock brand research completed successfully'
        : 'Dry run research completed';

      return {
        companyId: event.companyId,
        researchRequestId: event.researchRequestId,
        status: 'completed',
        researchData: {
          content: `[DRY RUN] ${mockPrefix}. This would contain comprehensive brand research including company background, products/services, target audience, value propositions, and competitive analysis.`,
          searchResults: [
            {
              title: 'Company Homepage',
              url: 'https://example.com',
              snippet: 'Mock search result snippet',
              date: new Date().toISOString().split('T')[0],
            },
          ],
          usage: {
            prompt_tokens: 100,
            completion_tokens: 500,
            total_tokens: 600,
          },
          sources: [
            {
              url: 'https://example.com',
              title: 'Company Homepage',
            },
          ],
        },
      };
    }

    console.log('Production mode: Polling OpenAI API for research status');

    // Define the prompt name to match brand-research lambda
    const promptName = 'brand_research';

    // Initialize OpenAI client with Langfuse observation
    // Use the same traceName as brand-research to connect the traces
    const openai = observeOpenAI(new OpenAI(), {
      traceName: promptName,
      tags: ['brand_research_polling'],
      generationMetadata: {
        companyId: event.companyId,
        researchRequestId: event.researchRequestId,
        step: 'get_status',
      },
      userId: event.companyId,
      sessionId: event.executionId,
    });

    // Retrieve the async response status from OpenAI
    // @ts-expect-error The responses API is in beta and has known type issues
    const response: ResearchAsyncStatusResponse =
      await openai.responses.retrieve(event.researchRequestId);

    console.log('OpenAI response status:', response.status);

    // Return OpenAI status directly - no mapping needed
    let researchData: ResearchData | undefined;

    // Only extract data if the job completed
    if (response.status === 'completed') {
      console.log('Research job completed successfully');

      // Extract text from the new response structure
      let outputText: string | undefined;

      // Check if output is in the new array format
      if (Array.isArray(response.output) && response.output.length > 0) {
        // Collect all text segments from all messages
        const textSegments: string[] = [];

        for (const message of response.output) {
          if (message.type === 'message' && message.content) {
            for (const content of message.content) {
              if (content.type === 'output_text' && content.text) {
                textSegments.push(content.text);
              }
            }
          }
        }

        // Join all text segments with newlines and normalize whitespace
        if (textSegments.length > 0) {
          outputText = textSegments
            .join('\n\n')
            .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newlines
            .trim();
          console.log(
            `Collected ${textSegments.length} text segments from response`,
          );
        }
      }

      // Fallback to legacy format if no segments were found
      if (!outputText && response.output_legacy?.text) {
        console.log('Using legacy output format');
        outputText = response.output_legacy.text;
      }

      if (!outputText) {
        console.error(
          'Research completed but no output text found. Response structure:',
          JSON.stringify(response, null, 2),
        );
        throw new ResearchJobStatusError(
          'Research completed but no output text found in response',
          new Error('Missing output text in response'),
          {
            companyId: event.companyId,
            researchRequestId: event.researchRequestId,
            hasOutput: !!response.output,
            outputType: Array.isArray(response.output)
              ? 'array'
              : typeof response.output,
          },
        );
      }

      // Extract sources from web search tool calls (may be in tools array or legacy format)
      const sources: ResearchData['sources'] = [];
      const searchResults: ResearchData['searchResults'] = [];

      // Check for tool sources in the tools array
      if (response.tools) {
        for (const tool of response.tools) {
          if (tool.type === 'web_search' && tool.action?.sources) {
            for (const source of tool.action.sources) {
              sources.push({
                url: source.url,
                title: source.title,
              });
              searchResults.push({
                title: source.title || '',
                url: source.url,
                snippet: source.snippet,
                date: new Date().toISOString().split('T')[0],
              });
            }
          }
        }
      }
      // Fallback to legacy tool_calls format
      else if (response.output_legacy?.tool_calls) {
        for (const toolCall of response.output_legacy.tool_calls) {
          if (toolCall.type === 'web_search' && toolCall.action?.sources) {
            for (const source of toolCall.action.sources) {
              sources.push({
                url: source.url,
                title: source.title,
              });
              searchResults.push({
                title: source.title || '',
                url: source.url,
                snippet: source.snippet,
                date: new Date().toISOString().split('T')[0],
              });
            }
          }
        }
      }

      // Map usage data - handle both old and new format
      const usage = response.usage
        ? {
            prompt_tokens: response.usage.input_tokens,
            completion_tokens: response.usage.output_tokens,
            total_tokens: response.usage.total_tokens,
            reasoning_tokens:
              response.usage.output_tokens_details?.reasoning_tokens,
            cached_tokens: response.usage.input_tokens_details?.cached_tokens,
          }
        : undefined;

      researchData = {
        content: outputText,
        searchResults: searchResults.length > 0 ? searchResults : undefined,
        sources: sources.length > 0 ? sources : undefined,
        usage,
      };

      console.log(
        `Research data extracted: ${outputText.length} chars, ${sources.length} sources`,
      );
    } else if (
      response.status === 'failed' ||
      response.status === 'cancelled' ||
      response.status === 'expired'
    ) {
      const errorMessage = response.error?.message || 'Research job failed';
      console.error('Research job failed:', errorMessage);

      throw new ResearchJobStatusError(
        `Research job ${response.status}: ${errorMessage}`,
        new Error(errorMessage),
        {
          companyId: event.companyId,
          researchRequestId: event.researchRequestId,
          errorType: response.error?.type,
        },
      );
    } else {
      // Status is 'validating', 'queued', 'in_progress', 'finalizing', or 'cancelling' - still pending
      console.log(`Research job status: ${response.status}`);
    }

    return {
      companyId: event.companyId,
      researchRequestId: event.researchRequestId,
      status: response.status,
      researchData,
    };
  } catch (error) {
    console.error('Error getting research job status:', error);

    // Check if it's a transient error
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (
        message.includes('timeout') ||
        message.includes('network') ||
        message.includes('429') ||
        message.includes('503') ||
        message.includes('502')
      ) {
        throw new TransientResearchJobStatusError(
          `Transient error getting research job status for ${event.researchRequestId}`,
          error,
          {
            companyId: event.companyId,
            researchRequestId: event.researchRequestId,
          },
        );
      }
    }

    // Re-throw if it's already one of our custom errors
    if (
      error instanceof ResearchJobStatusError ||
      error instanceof TransientResearchJobStatusError
    ) {
      throw error;
    }

    // Non-transient error
    throw new ResearchJobStatusError(
      `Failed to get research job status for ${event.researchRequestId}`,
      error instanceof Error ? error : new Error(String(error)),
      {
        companyId: event.companyId,
        researchRequestId: event.researchRequestId,
      },
    );
  }
};
