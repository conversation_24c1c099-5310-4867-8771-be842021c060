import { Logger } from '@aws-lambda-powertools/logger';
import { GraphQLClient } from 'graphql-request';
import pLimit from 'p-limit';

import { getGWAuthToken } from '../utils/getGWAuthToken';

export interface Website {
  name: string;
  hostname: string;
  category: string;
}

export interface CompanyWebsiteResult {
  companyId: string;
  websiteUrl: string;
}

export interface CompanyWebsiteInput {
  companyId: string;
  websiteUrl?: string;
}

export class ApiGatewayClient {
  private readonly superUserToken: string;
  private readonly graphqlClient: GraphQLClient;

  constructor(
    apiGatewayUrl: string,
    private readonly apiGatewaySuperUser: string,
    private readonly apiGatewayKey: string,
    private readonly logger: Logger,
  ) {
    // Validate inputs
    if (!apiGatewayUrl || !apiGatewaySuperUser || !apiGatewayKey) {
      this.logger.error('Missing required API Gateway configuration', {
        hasUrl: !!apiGatewayUrl,
        hasSuperUser: !!apiGatewaySuperUser,
        hasKey: !!apiGatewayKey,
      });
      throw new Error('Invalid API Gateway configuration');
    }

    // Generate token for super user (used for entitled companies query)
    this.superUserToken = getGWAuthToken(
      this.apiGatewaySuperUser,
      this.apiGatewayKey,
    );
    this.logger.debug('API Gateway client initialized', {
      url: apiGatewayUrl,
      superUser: this.apiGatewaySuperUser,
      tokenLength: this.superUserToken.length,
    });

    this.graphqlClient = new GraphQLClient(`${apiGatewayUrl}/graphql`);
  }

  /**
   * Fetch website URL for a single company
   * @param companyId The company ID
   * @returns The company website result with websiteUrl or empty string if not found
   */
  async getCompanyWebsite(companyId: string): Promise<CompanyWebsiteResult> {
    try {
      const websites = await this.getCompanyWebsites(companyId);

      if (!websites.length) {
        this.logger.info(`No websites found for company ${companyId}`);
        return { companyId, websiteUrl: '' };
      }

      this.logger.info(`Fetched websites from company ${companyId}`, {
        websites,
        count: websites.length,
      });

      // Use the first LIVE BRAND website
      const websiteUrl = `https://${websites[0].hostname}`;
      return { companyId, websiteUrl };
    } catch (error) {
      this.logger.error('Error fetching websites', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
      });
      // Return empty websiteUrl on error to allow processing to continue
      return { companyId, websiteUrl: '' };
    }
  }

  /**
   * For a list of entitlements/companies, fetches website URLs from API Gateway
   * Supports hybrid approach: uses existing websiteUrl as fallback if API Gateway has no data
   * @param entitlements Array of objects with companyId and optional websiteUrl
   * @returns Array of CompanyWebsiteResult objects with valid website URLs
   */
  async fetchAllWebsites(
    entitlements: CompanyWebsiteInput[],
  ): Promise<CompanyWebsiteResult[]> {
    const limit = pLimit(5);
    const results = await Promise.all(
      entitlements.map(entitlement =>
        limit(async () => {
          try {
            const websites = await this.getCompanyWebsites(
              entitlement.companyId,
            );
            if (!websites.length) {
              this.logger.info(
                `No websites found for company ${entitlement.companyId}`,
              );
              return entitlement.websiteUrl
                ? {
                    companyId: entitlement.companyId,
                    websiteUrl: entitlement.websiteUrl,
                  }
                : null;
            }

            const website =
              (entitlement.websiteUrl &&
                websites.find(w =>
                  entitlement.websiteUrl!.includes(w.hostname),
                )) ||
              websites[0];

            return {
              companyId: entitlement.companyId,
              websiteUrl: `https://${website.hostname}`,
            };
          } catch (error) {
            this.logger.error('Failed to fetch website for company', {
              companyId: entitlement.companyId,
              error: error instanceof Error ? error.message : String(error),
            });
            return entitlement.websiteUrl
              ? {
                  companyId: entitlement.companyId,
                  websiteUrl: entitlement.websiteUrl,
                }
              : null;
          }
        }),
      ),
    );

    return results.filter(
      (result): result is CompanyWebsiteResult => result !== null,
    );
  }

  /**
   * Fetch websites for a company from API Gateway
   * @param companyId The company ID
   * @returns Array of websites
   */
  private async getCompanyWebsites(companyId: string): Promise<Website[]> {
    try {
      const websitesQuery = `
        query companyWebsite ($companyId: String) {
          websites(companyId: $companyId, status: LIVE, category: "BRAND") {
            name,
            hostname,
            category
          }
        }
      `;

      const websitesData = await this.graphqlClient.request<{
        websites: Website[];
      }>(websitesQuery, { companyId }, { Authorization: this.superUserToken });

      return websitesData.websites;
    } catch (error) {
      this.logger.error('Failed to fetch company websites from API Gateway', {
        error: error instanceof Error ? error.message : String(error),
        companyId,
        superUser: this.apiGatewaySuperUser,
        tokenPresent: true,
        tokenLength: this.apiGatewayKey?.length,
        errorDetails:
          error instanceof Error
            ? {
                name: error.name,
                stack: error.stack,
              }
            : undefined,
      });
      throw error;
    }
  }
}
