import { Logger } from '@aws-lambda-powertools/logger';

export interface EntitlementDTO {
  id: string;
  companyId: string; // This is at the root level, not under company
  company?: {
    name?: string;
    website?: string;
  };
  productId: string;
  startDate?: string;
  endDate?: string | null;
}

export interface PaginatedResponse<T> {
  data: T[];
  links?: {
    next?: string;
    prev?: string;
  };
  meta?: {
    total: number;
    page: number;
    limit: number;
  };
}

export class TenantServiceError extends Error {
  constructor(
    message: string,
    public readonly statusCode: number,
    public readonly context?: Record<string, unknown>,
  ) {
    super(message);
    this.name = 'TenantServiceError';
  }
}

export class TenantClient {
  private readonly maxRetries = 3;
  private readonly requestTimeoutMs = 3000;
  private readonly retryDelays = [200, 400, 800]; // Exponential backoff delays in ms

  constructor(
    private readonly baseUrl: string,
    private readonly productIds: string[],
    private readonly logger: Logger,
    private readonly entitlementsLimit?: number,
  ) {}

  /**
   * Build the entitlements URL with query parameters
   */
  private getEntitlementsUrl(): string {
    const queryParams: Record<string, string> = {
      'filter.productId': `$in:${this.productIds.join(',')}`,
      'filter.endDate': '$null',
      select: 'company.website,company.name', // companyId is at root level, not in company object
    };

    if (this.entitlementsLimit != null) {
      queryParams.limit = `${this.entitlementsLimit}`;
    }

    const params = new URLSearchParams(queryParams);
    return `${this.baseUrl}/api/v1/entitlements?${params.toString()}`;
  }

  /**
   * Execute fetch with timeout using AbortController
   */
  private async fetchWithTimeout(
    url: string,
    timeoutMs: number,
  ): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Determine if an error/response should be retried
   */
  private shouldRetry(error: unknown, attempt: number): boolean {
    if (attempt >= this.maxRetries) {
      return false;
    }

    // Retry on network errors (including timeouts)
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      if (
        message.includes('network') ||
        message.includes('timeout') ||
        message.includes('aborted') ||
        message.includes('fetch failed')
      ) {
        return true;
      }
    }

    // Check if it's an HTTP response error
    if (error instanceof TenantServiceError) {
      // Retry on 5xx errors
      return error.statusCode >= 500 && error.statusCode < 600;
    }

    return false;
  }

  /**
   * Fetch a single page of entitlements with retry logic
   */
  private async getPageOfEntitlements(
    nextPageUrl?: string,
  ): Promise<PaginatedResponse<EntitlementDTO>> {
    this.logger.debug('Fetching page of entitlements', { nextPageUrl });

    const url = nextPageUrl || this.getEntitlementsUrl();
    let lastError: Error | TenantServiceError | undefined;

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        // Add delay for retry attempts (no delay on first attempt)
        if (attempt > 0) {
          const delay =
            this.retryDelays[attempt - 1] ||
            this.retryDelays[this.retryDelays.length - 1];
          this.logger.debug(
            `Retrying after ${delay}ms (attempt ${attempt + 1}/${this.maxRetries})`,
            {
              url,
              previousError: lastError?.message,
            },
          );
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const response = await this.fetchWithTimeout(
          url,
          this.requestTimeoutMs,
        );

        if (!response.ok) {
          const error = new TenantServiceError(
            `Failed to fetch entitlements status: ${response.status}`,
            response.status,
            {
              url,
              productIds: this.productIds,
            },
          );

          // Check if we should retry this response
          if (this.shouldRetry(error, attempt + 1)) {
            lastError = error;
            continue;
          }

          // Don't retry 4xx errors or if max retries reached
          this.logger.error(
            `Tenant service HTTP error fetching entitlements status: ${response.status}`,
            { attempt: attempt + 1, url },
          );
          throw error;
        }

        // Success - parse and validate the response
        const data =
          (await response.json()) as PaginatedResponse<EntitlementDTO>;

        // Log the structure of the first entitlement for debugging
        if (data.data && data.data.length > 0) {
          this.logger.debug('Sample entitlement structure', {
            firstEntitlement: data.data[0],
            hasCompanyObject: !!data.data[0]?.company,
            hasRootCompanyId: !!data.data[0]?.companyId,
            companyKeys: data.data[0]?.company
              ? Object.keys(data.data[0].company)
              : [],
          });
        }

        return data;
      } catch (error) {
        // Handle network errors and timeouts
        if (error instanceof TenantServiceError) {
          lastError = error;
        } else if (error instanceof Error) {
          lastError = new TenantServiceError(
            `Network error fetching entitlements: ${error.message}`,
            0,
            {
              url,
              productIds: this.productIds,
              originalError: error.message,
            },
          );

          // Check if we should retry
          if (this.shouldRetry(error, attempt + 1)) {
            this.logger.warn(
              `Network error on attempt ${attempt + 1}/${this.maxRetries}`,
              {
                error: error.message,
                url,
              },
            );
            continue;
          }
        }

        // If this was the last attempt or shouldn't retry, throw the error
        if (
          attempt === this.maxRetries - 1 ||
          !this.shouldRetry(lastError, attempt + 1)
        ) {
          this.logger.error(
            `Failed to fetch entitlements after ${attempt + 1} attempts`,
            {
              error: lastError?.message,
              url,
            },
          );
          throw lastError || error;
        }
      }
    }

    // This should never be reached, but TypeScript needs it
    throw (
      lastError ||
      new TenantServiceError(
        'Failed to fetch entitlements after all retries',
        0,
        { url, productIds: this.productIds },
      )
    );
  }

  /**
   * Fetch all entitlements (handles pagination)
   */
  async getAllEntitlements(): Promise<EntitlementDTO[]> {
    let allEntitlements: EntitlementDTO[] = [];
    let nextPageUrl: string | undefined;

    do {
      const paginatedEntitlements =
        await this.getPageOfEntitlements(nextPageUrl);
      allEntitlements = [...allEntitlements, ...paginatedEntitlements.data];
      nextPageUrl = paginatedEntitlements?.links?.next;
    } while (nextPageUrl);

    this.logger.debug('Fetched all entitlements', {
      entitlementsCount: allEntitlements.length,
    });

    return allEntitlements;
  }

  /**
   * Get company IDs from entitlements
   */
  async getEntitledCompanyIds(): Promise<string[]> {
    const entitlements = await this.getAllEntitlements();
    return entitlements.map(e => e.companyId); // Use root-level companyId
  }

  /**
   * Get entitlements with company data
   */
  async getEntitledCompanies(): Promise<
    Array<{ companyId: string; websiteUrl?: string }>
  > {
    const entitlements = await this.getAllEntitlements();

    // Validate and map entitlements
    const mappedCompanies = entitlements.map((e, index) => {
      // Check if companyId exists at root level
      if (!e.companyId) {
        this.logger.error('Entitlement missing companyId field', {
          entitlementIndex: index,
          entitlementId: e.id,
          entitlement: e,
        });
        return null;
      }

      return {
        companyId: e.companyId, // Use root-level companyId
        websiteUrl: e.company?.website, // Optional company object for website
      };
    });

    // Filter out null entries (companies without valid IDs)
    const validCompanies = mappedCompanies.filter(
      (c): c is NonNullable<typeof c> => c !== null,
    );

    // Log if we had to filter out any companies
    if (validCompanies.length < entitlements.length) {
      this.logger.warn(
        'Some entitlements were filtered due to missing companyId',
        {
          totalEntitlements: entitlements.length,
          validCompanies: validCompanies.length,
          filteredOut: entitlements.length - validCompanies.length,
        },
      );
    }

    return validCompanies;
  }

  /**
   * Get entitled companies that have website URLs from tenant-service
   * This is the primary method for the hybrid approach
   */
  async getEntitledCompaniesWithWebsites(): Promise<
    Array<{ companyId: string; websiteUrl: string }>
  > {
    const entitlements = await this.getAllEntitlements();

    // Filter and map entitlements that have website URLs
    const companiesWithWebsites = entitlements
      .filter(e => e.companyId && e.company?.website)
      .map(e => ({
        companyId: e.companyId,
        websiteUrl: e.company!.website!, // Non-null assertion safe due to filter
      }));

    this.logger.debug(
      'Fetched entitled companies with websites from tenant-service',
      {
        totalEntitlements: entitlements.length,
        companiesWithWebsites: companiesWithWebsites.length,
        coveragePercentage: Math.round(
          (companiesWithWebsites.length / entitlements.length) * 100,
        ),
      },
    );

    return companiesWithWebsites;
  }
}
