import { Logger } from '@aws-lambda-powertools/logger';
import { ClientError, GraphQLClient } from 'graphql-request';

export interface BrandProfile {
  id: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  updatedBy?: string;
  aboutTheBrand?: string;
  strategicFocus?: string;
  valueProposition?: string;
  idealCustomerProfiles?: string;
  missionAndCoreValues?: string;
  brandPointOfView?: string;
  toneOfVoice?: string;
  ctaText?: string;
  authorPersona?: string;
}

export class BrandProfileClient {
  private readonly graphqlClient: GraphQLClient;
  private readonly endpoint: string;

  constructor(
    seoAutomationUrl: string,
    m2mSuperApiKey: string,
    private readonly logger: Logger,
  ) {
    this.endpoint = seoAutomationUrl;
    // Log initialization details for debugging
    this.logger.info('Initializing BrandProfileClient', {
      seoAutomationUrl,
      hasApiKey: !!m2mSuperApiKey,
      apiKeyLength: m2mSuperApiKey?.length || 0,
    });

    this.graphqlClient = new GraphQLClient(seoAutomationUrl, {
      headers: {
        'x-lp-api-key': m2mSuperApiKey.split(',')[0],
      },
    });
  }

  /**
   * Upsert (create or update) a brand profile for a company
   * @param brandProfile The brand profile to save
   * @returns The saved brand profile
   */
  async upsertBrandProfile(
    brandProfile: Omit<
      BrandProfile,
      'id' | 'createdAt' | 'updatedAt' | 'updatedBy'
    >,
  ): Promise<BrandProfile> {
    try {
      const mutation = `
        mutation UpsertBrandProfile($input: UpsertBrandProfileDto!) {
          upsertBrandProfile(input: $input) {
            id
            companyId
            createdAt
            updatedAt
            updatedBy
            aboutTheBrand
            strategicFocus
            valueProposition
            idealCustomerProfiles
            missionAndCoreValues
            brandPointOfView
            toneOfVoice
            ctaText
            authorPersona
          }
        }
      `;

      const input = {
        companyId: brandProfile.companyId,
        aboutTheBrand: brandProfile.aboutTheBrand,
        strategicFocus: brandProfile.strategicFocus,
        valueProposition: brandProfile.valueProposition,
        idealCustomerProfiles: brandProfile.idealCustomerProfiles,
        missionAndCoreValues: brandProfile.missionAndCoreValues,
        brandPointOfView: brandProfile.brandPointOfView,
        toneOfVoice: brandProfile.toneOfVoice,
        ctaText: brandProfile.ctaText,
        authorPersona: brandProfile.authorPersona,
      };

      const data = await this.graphqlClient.request<{
        upsertBrandProfile: BrandProfile;
      }>(mutation, { input });

      this.logger.info('Brand profile upserted successfully', {
        companyId: brandProfile.companyId,
        profileId: data.upsertBrandProfile.id,
      });

      return data.upsertBrandProfile;
    } catch (error) {
      this.logger.error('Error upserting brand profile', {
        companyId: brandProfile.companyId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * Check if a brand profile exists for a company
   * @param companyId The company ID
   * @returns The brand profile if it exists, null otherwise
   */
  async getBrandProfile(companyId: string): Promise<BrandProfile | null> {
    // Validate companyId before making the request
    if (!companyId || typeof companyId !== 'string') {
      this.logger.error('Invalid companyId provided to getBrandProfile', {
        companyId,
        companyIdType: typeof companyId,
      });
      throw new Error(`Invalid companyId: ${companyId}`);
    }

    this.logger.info('Starting brand profile query', {
      companyId,
      companyIdLength: companyId.length,
    });

    try {
      const query = `
        query GetBrandProfile($companyId: ID!) {
          brandProfile(companyId: $companyId) {
            id
            companyId
            createdAt
            updatedAt
            updatedBy
            aboutTheBrand
            strategicFocus
            valueProposition
            idealCustomerProfiles
            missionAndCoreValues
            brandPointOfView
            toneOfVoice
            ctaText
            authorPersona
          }
        }
      `;

      const variables = { companyId };

      this.logger.debug('Executing GraphQL query', {
        companyId,
        variables,
        query: query.trim(),
      });

      const data = await this.graphqlClient.request<{
        brandProfile: BrandProfile | null;
      }>(query, variables);

      // Log the raw response for debugging
      this.logger.debug('GraphQL response received', {
        companyId,
        hasData: !!data,
        hasBrandProfile: !!data?.brandProfile,
        responseKeys: data ? Object.keys(data) : [],
      });

      // Handle null response from GraphQL (profile doesn't exist)
      if (data.brandProfile === null) {
        this.logger.info('Brand profile query returned null', { companyId });
        return null;
      }

      this.logger.info('Brand profile fetched successfully', {
        companyId,
        profileId: data.brandProfile.id,
        profileCompanyId: data.brandProfile.companyId,
      });

      return data.brandProfile;
    } catch (error) {
      // Handle structured GraphQL errors
      if (error instanceof ClientError) {
        const graphqlErrors = error.response?.errors;
        const statusCode = error.response?.status;

        // Log the full error response for debugging
        this.logger.error('GraphQL ClientError details', {
          companyId,
          statusCode,
          statusText:
            statusCode === 405
              ? 'METHOD_NOT_ALLOWED - Server expects POST request'
              : undefined,
          errors: graphqlErrors,
          responseData: error.response?.data,
          responseBody: error.response?.body,
          requestVariables: { companyId },
          headers: error.response?.headers,
        });

        // Special handling for 405 Method Not Allowed
        if (statusCode === 405) {
          this.logger.error(
            '405 Method Not Allowed - GraphQL endpoint configuration issue',
            {
              companyId,
              endpoint: this.endpoint,
              message:
                'The GraphQL endpoint is returning 405. Check if the URL is correct and expects POST requests.',
              possibleCauses: [
                'Incorrect endpoint URL',
                'Server expects different HTTP method',
                'Missing or incorrect Content-Type header',
                'API Gateway or proxy configuration issue',
              ],
            },
          );
        }

        if (graphqlErrors && Array.isArray(graphqlErrors)) {
          // Check each error for NOT_FOUND or similar codes
          for (const gqlError of graphqlErrors) {
            const errorCode = gqlError.extensions?.code;
            const errorMessage = gqlError.message || '';
            const errorMessageLower = errorMessage.toLowerCase();

            this.logger.debug('Analyzing GraphQL error', {
              companyId,
              errorCode,
              errorMessage: gqlError.message,
              extensions: gqlError.extensions,
              path: gqlError.path,
            });

            // Check for the specific message you mentioned: "Brand profile not found for the given company ID"
            const isNotFoundError =
              errorMessage ===
                'Brand profile not found for the given company ID' ||
              errorCode === 'NOT_FOUND' ||
              errorCode === 'ENTITY_NOT_FOUND' ||
              errorCode === 'RESOURCE_NOT_FOUND' ||
              errorCode === 'INTERNAL_SERVER_ERROR' || // NestJS NotFoundException often comes as this
              errorMessageLower.includes('not found') ||
              errorMessageLower.includes('does not exist') ||
              errorMessageLower.includes('no brand profile') ||
              errorMessageLower.includes('brand profile not found');

            if (isNotFoundError) {
              // Log info level for expected not-found cases
              this.logger.info('Brand profile not found (expected case)', {
                companyId,
                errorCode,
                errorMessage: gqlError.message,
              });
              return null;
            }
          }
        }

        // Check for authentication/authorization errors
        if (statusCode === 401 || statusCode === 403) {
          this.logger.error('Authentication/Authorization error', {
            companyId,
            statusCode,
            message: 'Check API key configuration',
          });
        }

        // Log full error details for non-not-found errors
        this.logger.error('GraphQL error fetching brand profile (unexpected)', {
          companyId,
          errors: graphqlErrors,
          response: error.response,
          request: error.request,
        });
      } else {
        // Log non-GraphQL errors
        this.logger.error('Unexpected error fetching brand profile', {
          companyId,
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          errorType: error?.constructor?.name,
        });
      }

      throw error;
    }
  }
}
