import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON>r thrown when the check-existing-brand-profile lambda fails
 */
export class CheckBrandProfileError extends BaseError {
  /**
   * Creates a new CheckBrandProfileError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the check-existing-brand-profile lambda encounters transient errors that should be retried
 */
export class TransientCheckBrandProfileError extends CheckBrandProfileError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientCheckBrandProfileError';
  }
}
