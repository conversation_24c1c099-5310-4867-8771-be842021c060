import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON>r thrown when the get-research-job-status lambda fails
 */
export class ResearchJobStatusError extends BaseError {
  /**
   * Creates a new ResearchJobStatusError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the get-research-job-status lambda encounters transient errors that should be retried
 */
export class TransientResearchJobStatusError extends ResearchJobStatusError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientResearchJobStatusError';
  }
}
