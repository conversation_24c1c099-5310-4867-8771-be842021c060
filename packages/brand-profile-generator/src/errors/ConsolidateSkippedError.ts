import { BaseError } from './BaseError';

/**
 * <PERSON>rror thrown when the consolidate-skipped-companies lambda fails
 */
export class ConsolidateSkippedError extends BaseError {
  /**
   * Creates a new ConsolidateSkippedError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the consolidate-skipped-companies lambda encounters transient errors that should be retried
 */
export class TransientConsolidateSkippedError extends ConsolidateSkippedError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientConsolidateSkippedError';
  }
}
