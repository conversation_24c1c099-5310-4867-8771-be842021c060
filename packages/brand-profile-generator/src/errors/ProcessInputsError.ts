import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON>r thrown when the process-inputs lambda fails
 */
export class ProcessInputsError extends BaseError {
  /**
   * Creates a new ProcessInputsError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the process-inputs lambda encounters transient errors that should be retried
 */
export class TransientProcessInputsError extends ProcessInputsError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientProcessInputsError';
  }
}
