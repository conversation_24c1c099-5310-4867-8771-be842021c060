import { BaseError } from './BaseError';

/**
 * <PERSON>rror thrown when the handle-brand-profile-output lambda fails
 */
export class HandleBrandProfileOutputError extends BaseError {
  /**
   * Creates a new HandleBrandProfileOutputError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the handle-brand-profile-output lambda encounters transient errors that should be retried
 */
export class TransientHandleBrandProfileOutputError extends HandleBrandProfileOutputError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientHandleBrandProfileOutputError';
  }
}
