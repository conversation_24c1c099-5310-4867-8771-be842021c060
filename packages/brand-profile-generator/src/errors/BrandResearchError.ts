import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON><PERSON> thrown when the brand-research lambda fails
 */
export class BrandResearchError extends BaseError {
  /**
   * Creates a new BrandResearchError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the brand-research lambda encounters transient errors that should be retried
 */
export class TransientBrandResearchError extends BrandResearchError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientBrandResearchError';
  }
}
