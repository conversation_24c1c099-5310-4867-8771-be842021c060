import { BaseError } from './BaseError';

/**
 * <PERSON>rror thrown when the create-brand-profile lambda fails
 */
export class CreateBrandProfileError extends BaseError {
  /**
   * Creates a new CreateBrandProfileError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the create-brand-profile lambda encounters transient errors that should be retried
 */
export class TransientCreateBrandProfileError extends CreateBrandProfileError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientCreateBrandProfileError';
  }
}
