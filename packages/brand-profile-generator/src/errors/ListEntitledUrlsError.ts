import { BaseError } from './BaseError';

/**
 * <PERSON><PERSON><PERSON> thrown when the list-entitled-urls lambda fails
 */
export class ListEntitledUrlsError extends BaseError {
  /**
   * Creates a new ListEntitledUrlsError
   * @param message - The error message
   * @param cause - The original error that caused the failure
   * @param context - Additional context about the error
   */
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, {
      cause: cause?.message,
      causeStack: cause?.stack,
      context,
    });
  }
}

/**
 * Error thrown when the list-entitled-urls lambda encounters transient errors that should be retried
 */
export class TransientListEntitledUrlsError extends ListEntitledUrlsError {
  constructor(
    message: string,
    cause?: Error,
    context?: Record<string, unknown>,
  ) {
    super(message, cause, context);
    this.name = 'TransientListEntitledUrlsError';
  }
}
