service: brand-profile-generator

useDotenv: true
configValidationMode: warn

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-step-functions
  - serverless-plugin-datadog
  - serverless-offline

custom:
  esbuild:
    bundle: true
    minify: false
    sourcemap: true
    exclude: ['aws-sdk']
    target: 'node22'
    define: { 'require.resolve': undefined }
    platform: 'node'
    concurrency: 1
    zipConcurrency: 1
    tsconfig: 'tsconfig.json'
    format: 'cjs'
    mainFields: ['module', 'main']
  serverless-offline:
    httpPort: 3003
    lambdaPort: 3004
  datadog:
    site: datadoghq.com
    apiKey: ${env:DD_API_KEY}
    enableDDTracing: true
    enableDDLogs: true
    enableXrayTracing: false
    enableSourceCodeIntegration: true
    captureLambdaPayload: true
    propagateUpstreamTrace: true
    addLayers: true
    enableColdStartTracing: true
    enableDDMonitoring: true
    env: ${self:provider.stage}
    service: brand-profile-generator
    version: ${env:GIT_SHA, 'latest'}
    tags: "team:client-marketing"

package:
  individually: true

provider:
  name: aws
  stage: ${env:ENVIRONMENT, 'development'}
  region: us-east-1
  runtime: nodejs22.x
  versionFunctions: false
  iam:
    role:
      name: ${self:service}-${self:provider.stage}-exec-role-v3
      statements:
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
            - lambda:InvokeFunction
            - states:StartExecution
            - states:DescribeExecution
            - states:StopExecution
            - states:SendTaskSuccess
            - states:SendTaskFailure
            - states:SendTaskHeartbeat
          Resource:
            - '*'
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:PutObjectTagging
            - s3:DeleteObject
            - s3:ListBucket
          Resource:
            - '*'
        - Effect: Allow
          Action:
            - xray:PutTraceSegments
            - xray:PutTelemetryRecords
          Resource: '*'
  environment:
    ENVIRONMENT: ${env:ENVIRONMENT, 'development'}
    SLACK_NOTIFICATIONS_ENABLED: ${env:SLACK_NOTIFICATIONS_ENABLED, 'false'}
    SLACK_WEBHOOK_URL: ${env:SLACK_WEBHOOK_URL, ''}
    API_GATEWAY_SUPER_USER_COMPANY_ID: ${env:API_GATEWAY_SUPER_USER_COMPANY_ID, ''}
    API_GATEWAY_KEY: ${env:API_GATEWAY_KEY, ''}
    LAUNCHDARKLY_KEY: ${env:LAUNCHDARKLY_KEY, ''}
    TENANT_SERVICE_URL: ${env:TENANT_SERVICE_URL, ''}
    CMS_SERVICE_URL: ${env:CMS_SERVICE_URL, ''}
    API_GATEWAY_URL: ${env:API_GATEWAY_URL, ''}
    COSMO_GQL_URL: ${env:COSMO_GQL_URL, ''}
    LANGFUSE_SECRET_KEY: ${env:LANGFUSE_SECRET_KEY, ''}
    LANGFUSE_PUBLIC_KEY: ${env:LANGFUSE_PUBLIC_KEY, ''}
    LANGFUSE_FLUSH_AT: ${env:LANGFUSE_FLUSH_AT, 1}
    M2M_SUPER_API_KEY: ${env:M2M_SUPER_API_KEY, ''}
    OPENAI_API_KEY: ${env:OPENAI_API_KEY, ''}
    BRAND_PROFILES_S3_BUCKET_NAME: brand-profile-generator-${self:provider.stage}
  deploymentBucket: ${file(./serverless/conditionalDeployBucket.js)}
  stackTags:
    env: ${env:ENVIRONMENT, 'development'}
    app: brand-profile-generator
    platform_version: v3
    team: client-marketing
  tags:
    env: ${env:ENVIRONMENT, 'development'}
    app: brand-profile-generator
    platform_version: v3
    team: client-marketing
    Project: seo-automation
    Service: brand-profile-generator
    dd_trace_enabled: true
    dd_service: brand-profile-generator
    dd_env: ${env:ENVIRONMENT, 'development'}
    dd_version: ${env:GIT_SHA, 'latest'}
  vpc: ${file(./serverless/conditionalVPC.js)}

functions:
  processInputs:
    handler: src/lambdas/process-inputs.handler
    timeout: 60
    memorySize: 256
    description: Process and validate input parameters with defaults

  listEntitledUrls:
    handler: src/lambdas/list-entitled-urls.handler
    timeout: 300
    memorySize: 1024
    description: Query entitled URLs or use provided company IDs

  checkExistingBrandProfile:
    handler: src/lambdas/check-existing-brand-profile.handler
    timeout: 60
    memorySize: 512
    description: Check if brand profile already exists for company

  reportResults:
    handler: src/lambdas/report-results.handler
    timeout: 120
    memorySize: 512
    description: Generate comprehensive execution report with statistics and Slack notifications

  brandResearch:
    handler: src/lambdas/brand-research.handler
    timeout: 300
    memorySize: 1024
    description: Initiate brand research via LLM API

  getResearchJobStatus:
    handler: src/lambdas/get-research-job-status.handler
    timeout: 60
    memorySize: 512
    description: Check research job status

  createBrandProfile:
    handler: src/lambdas/create-brand-profile.handler
    timeout: 300
    memorySize: 1024
    description: Create brand profile from research data using OpenAI

  handleBrandProfileOutput:
    handler: src/lambdas/handle-brand-profile-output.handler
    timeout: 300
    memorySize: 1024
    description: Save brand profile to database or S3

  catchFailure:
    handler: src/lambdas/catch-failure.handler
    timeout: 60
    memorySize: 256
    description: Handle and log step function failures

stepFunctions:
  stateMachines:
    brandProfileGenerator:
      name: brand-profile-generator-${self:provider.stage}
      role: !GetAtt StepFunctionsExecutionRole.Arn
      definition:
        Comment: "Brand Profile Generator State Machine"
        StartAt: ProcessInputs
        States:
          ProcessInputs:
            Type: Task
            Resource: !GetAtt processInputs.Arn
            Parameters:
              input.$: $
              executionName.$: $$.Execution.Name
              executionStartTime.$: $$.Execution.StartTime
            ResultPath: $.normalizedInput
            Retry:
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
            Next: ListEntitledUrls
          
          ListEntitledUrls:
            Type: Task
            Resource: !GetAtt listEntitledUrls.Arn
            Parameters:
              companyIds.$: $.normalizedInput.companyIds
              regenerateIfExisting.$: $.normalizedInput.regenerateIfExisting
              dryRun.$: $.normalizedInput.dryRun
              executionName.$: $.normalizedInput.executionName
              executionStartTime.$: $.normalizedInput.executionStartTime
            ResultPath: $.urlData
            Retry:
              - ErrorEquals:
                  - 'TransientListEntitledUrlsError'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 5
                MaxAttempts: 2
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
            Next: ProcessCompanies
          
          ProcessCompanies:
            Type: Map
            ItemReader:
              Resource: arn:aws:states:::s3:getObject
              ReaderConfig:
                InputType: JSON
              Parameters:
                Bucket: brand-profile-generator-${self:provider.stage}
                Key.$: $.urlData.s3Key
            MaxConcurrency: 5
            ItemSelector:
              companyId.$: $$.Map.Item.Value.companyId
              websiteUrl.$: $$.Map.Item.Value.websiteUrl
              urlData.$: $.urlData
            ItemProcessor:
              ProcessorConfig:
                Mode: DISTRIBUTED
                ExecutionType: STANDARD
              StartAt: PrepareCompanyContext
              States:
                PrepareCompanyContext:
                  Type: Pass
                  Parameters:
                    companyId.$: $.companyId
                    websiteUrl.$: $.websiteUrl
                    config:
                      regenerateIfExisting.$: $.urlData.regenerateIfExisting
                      dryRun.$: $.urlData.dryRun
                  ResultPath: $.context
                  Next: CheckExistingBrandProfile
                
                CheckExistingBrandProfile:
                  Type: Task
                  Resource: !GetAtt checkExistingBrandProfile.Arn
                  Parameters:
                    companyId.$: $.context.companyId
                    websiteUrl.$: $.context.websiteUrl
                    regenerateIfExisting.$: $.context.config.regenerateIfExisting
                  ResultPath: $.profileCheck
                  Retry:
                    - ErrorEquals:
                        - 'TransientCheckBrandProfileError'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: ShouldProcessChoice
                
                ShouldProcessChoice:
                  Type: Choice
                  Choices:
                    - Variable: $.profileCheck.shouldProcess
                      BooleanEquals: true
                      Next: BrandResearch
                  Default: SkipProcessing
                
                BrandResearch:
                  Type: Task
                  Resource: !GetAtt brandResearch.Arn
                  Parameters:
                    companyId.$: $.context.companyId
                    websiteUrl.$: $.context.websiteUrl
                    dryRun.$: $.context.config.dryRun
                    executionId.$: $$.Execution.Name
                  ResultPath: $.researchResult
                  Retry:
                    - ErrorEquals:
                        - 'TransientBrandResearchError'
                        - 'BrandResearchError'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: InitializePollingCounter
                
                InitializePollingCounter:
                  Type: Pass
                  Parameters:
                    count: 0
                  ResultPath: $.pollingAttempts
                  Next: WaitForResearch
                
                WaitForResearch:
                  Type: Wait
                  Seconds: 60
                  Next: IncrementPollingCounter
                
                IncrementPollingCounter:
                  Type: Pass
                  Parameters:
                    count.$: States.MathAdd($.pollingAttempts.count, 1)
                  ResultPath: $.pollingAttempts
                  OutputPath: $
                  Next: GetResearchJobStatus
                
                GetResearchJobStatus:
                  Type: Task
                  Resource: !GetAtt getResearchJobStatus.Arn
                  Parameters:
                    companyId.$: $.context.companyId
                    researchRequestId.$: $.researchResult.researchRequestId
                    dryRun.$: $.context.config.dryRun
                    executionId.$: $$.Execution.Name
                  ResultPath: $.jobStatus
                  Retry:
                    - ErrorEquals: ['TransientResearchJobStatusError']
                      IntervalSeconds: 2
                      MaxAttempts: 5
                      BackoffRate: 2
                    - ErrorEquals: ["States.ALL"]
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ["States.ALL"]
                      Next: ResearchFailed
                      ResultPath: $.error
                  Next: CheckJobStatus
                
                CheckJobStatus:
                  Type: Choice
                  Choices:
                    - Variable: $.jobStatus.status
                      StringEquals: completed
                      Next: CreateBrandProfile
                    - Variable: $.jobStatus.status
                      StringEquals: failed
                      Next: ResearchFailed
                    - Variable: $.jobStatus.status
                      StringEquals: cancelled
                      Next: ResearchFailed
                    - Variable: $.jobStatus.status
                      StringEquals: expired
                      Next: ResearchFailed
                    - Variable: $.pollingAttempts.count
                      NumericGreaterThan: 60
                      Next: PollingTimeout
                  Default: WaitForResearch
                
                PollingTimeout:
                  Type: Fail
                  Error: "PollingTimeout"
                  Cause: "Research job polling exceeded maximum attempts (60 attempts * 60 seconds = 60 minutes)"
                
                CreateBrandProfile:
                  Type: Task
                  Resource: !GetAtt createBrandProfile.Arn
                  Parameters:
                    companyId.$: $.context.companyId
                    researchData.$: $.jobStatus.researchData
                    dryRun.$: $.context.config.dryRun
                    executionId.$: $$.Execution.Name
                  ResultPath: $.brandProfileResult
                  Retry:
                    - ErrorEquals:
                        - 'TransientCreateBrandProfileError'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: HandleOutput
                
                HandleOutput:
                  Type: Task
                  Resource: !GetAtt handleBrandProfileOutput.Arn
                  Parameters:
                    companyId.$: $.context.companyId
                    brandProfile.$: $.brandProfileResult.brandProfile
                    dryRun.$: $.context.config.dryRun
                    executionName.$: $$.Execution.Name
                    executionDate.$: States.ArrayGetItem(States.StringSplit($$.Execution.StartTime, 'T'), 0)
                    executionFolderName.$: $.urlData.executionFolderName
                  ResultPath: $.outputResult
                  Retry:
                    - ErrorEquals:
                        - 'TransientHandleBrandProfileOutputError'
                      IntervalSeconds: 2
                      MaxAttempts: 3
                      BackoffRate: 2
                    - ErrorEquals:
                        - 'States.ALL'
                      IntervalSeconds: 5
                      MaxAttempts: 2
                      BackoffRate: 2
                  Catch:
                    - ErrorEquals: ['States.ALL']
                      ResultPath: $.error
                      Next: CompanyProcessingFailed
                  Next: NormalizeSuccessOutput
                
                NormalizeSuccessOutput:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    websiteUrl.$: $.context.websiteUrl
                    hasExistingProfile: false
                    shouldProcess: true
                    decisionReason: "successfully_processed"
                    s3Location.$: $.outputResult.s3Location
                    databaseSaved.$: $.outputResult.databaseSaved
                  End: true
                
                SkipProcessing:
                  Type: Pass
                  Parameters:
                    companyId.$: $.profileCheck.companyId
                    websiteUrl.$: $.profileCheck.websiteUrl
                    hasExistingProfile.$: $.profileCheck.hasExistingProfile
                    shouldProcess.$: $.profileCheck.shouldProcess
                    decisionReason.$: $.profileCheck.decisionReason
                    skippedMessage: "Profile already exists and regenerate not requested"
                  End: true
                
                ResearchFailed:
                  Type: Fail
                  Error: "ResearchFailed"
                  Cause: "Research job failed"
                
                CompanyProcessingFailed:
                  Type: Pass
                  Parameters:
                    companyId.$: $.context.companyId
                    websiteUrl.$: $.context.websiteUrl
                    hasExistingProfile: false
                    shouldProcess: false
                    decisionReason: "processing_failed"
                    errorMessage: "Company processing failed - error logged"
                    error.$: $.error
                  End: true
            ResultWriter:
              Resource: arn:aws:states:::s3:putObject
              Parameters:
                Bucket: brand-profile-generator-${self:provider.stage}
                Prefix.$: States.Format('brand-profile-jobs/{}/{}',
                  States.ArrayGetItem(States.StringSplit($.urlData.executionStartTime, 'T'), 0),
                  $.urlData.executionFolderName)
            ResultPath: $.mapResults
            Next: ReportResults
          
          ReportResults:
            Type: Task
            Resource: !GetAtt reportResults.Arn
            Parameters:
              executionName.$: $.normalizedInput.executionName
              executionStartTime.$: $.normalizedInput.executionStartTime
              executionArn.$: $$.Execution.Id
              dryRun.$: $.normalizedInput.dryRun
              regenerateIfExisting.$: $.normalizedInput.regenerateIfExisting
              results.$: $.mapResults
            ResultPath: $.executionReport
            Retry:
              - ErrorEquals:
                  - 'TransientReportResultsError'
                IntervalSeconds: 2
                MaxAttempts: 3
                BackoffRate: 2
              - ErrorEquals:
                  - 'States.ALL'
                IntervalSeconds: 5
                MaxAttempts: 2
                BackoffRate: 2
            Catch:
              - ErrorEquals: ['States.ALL']
                ResultPath: $.error
                Next: CatchFailure
            End: true
          
          CatchFailure:
            Type: Task
            Resource: !GetAtt catchFailure.Arn
            Parameters:
              error.$: $.error
              executionName.$: $.normalizedInput.executionName
              companyIds.$: $.normalizedInput.companyIds
              context:
                companyId.$: $.urlData.companyWebsites[0].companyId
                websiteUrl.$: $.urlData.companyWebsites[0].websiteUrl
            End: true

resources:
  - ${file(./serverless/conditionalBucket.js)}
  - Resources:
      StepFunctionsExecutionRole:
        Type: AWS::IAM::Role
        Properties:
          AssumeRolePolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Principal:
                  Service: states.amazonaws.com
                Action: sts:AssumeRole
          Policies:
            - PolicyName: StepFunctionsExecutionPolicy
              PolicyDocument:
                Version: '2012-10-17'
                Statement:
                  - Effect: Allow
                    Action:
                      - lambda:InvokeFunction
                    Resource:
                      - !Sub arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:${self:service}-${self:provider.stage}-*
                  - Effect: Allow
                    Action:
                      - s3:GetObject
                      - s3:PutObject
                      - s3:PutObjectTagging
                      - s3:DeleteObject
                      - s3:ListBucket
                    Resource:
                      - !Sub 'arn:aws:s3:::brand-profile-generator-${self:provider.stage}'
                      - !Sub 'arn:aws:s3:::brand-profile-generator-${self:provider.stage}/*'
                  - Effect: Allow
                    Action:
                      - states:StartExecution
                      - states:DescribeExecution
                      - states:StopExecution
                    Resource:
                      - !Sub arn:aws:states:${AWS::Region}:${AWS::AccountId}:stateMachine:*
                      - !Sub arn:aws:states:${AWS::Region}:${AWS::AccountId}:execution:*:*
                  - Effect: Allow
                    Action:
                      - xray:PutTraceSegments
                      - xray:PutTelemetryRecords
                    Resource: '*'
