# brand-profile-generator

## 1.24.0

### Minor Changes

- [#895](https://github.com/luxurypresence/seo-automation/pull/895) [`996b6cf`](https://github.com/luxurypresence/seo-automation/commit/996b6cffdf0488d04509070d7b866bb9da6923c4) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add environment separation for LangfuseSpanProcessor to enable trace filtering by deployment environment

## 1.23.4

### Patch Changes

- [#775](https://github.com/luxurypresence/seo-automation/pull/775) [`a09582f`](https://github.com/luxurypresence/seo-automation/commit/a09582fbe52ba09723b4b34d6fd0a0ebb6838e54) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix schema setting

## 1.23.3

### Patch Changes

- [#774](https://github.com/luxurypresence/seo-automation/pull/774) [`5fd7ac3`](https://github.com/luxurypresence/seo-automation/commit/5fd7ac34a8cb7c67772b483fb7b8b7be165796c4) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix create-brand-profile request and params

## 1.23.2

### Patch Changes

- [#765](https://github.com/luxurypresence/seo-automation/pull/765) [`40209b4`](https://github.com/luxurypresence/seo-automation/commit/40209b4c319cf1961d5e56361d13b38b8340834b) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix reponse format settings

## 1.23.1

### Patch Changes

- [#764](https://github.com/luxurypresence/seo-automation/pull/764) [`b09e643`](https://github.com/luxurypresence/seo-automation/commit/b09e64381a2774c613a284457bf4c4d61ce3349e) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix reponse format settings

## 1.23.0

### Minor Changes

- [#763](https://github.com/luxurypresence/seo-automation/pull/763) [`c40970b`](https://github.com/luxurypresence/seo-automation/commit/c40970bb3f8ff156f175a9903bbdd713e28328a7) Thanks [@fbionsc](https://github.com/fbionsc)! - Update create_brand_profile prompt settings

## 1.22.0

### Minor Changes

- [#761](https://github.com/luxurypresence/seo-automation/pull/761) [`21c8243`](https://github.com/luxurypresence/seo-automation/commit/21c82436a4178b19361b4cb27f4b3b7d05d4100b) Thanks [@fbionsc](https://github.com/fbionsc)! - Replace brand research model

## 1.21.3

### Patch Changes

- [#749](https://github.com/luxurypresence/seo-automation/pull/749) [`08460e3`](https://github.com/luxurypresence/seo-automation/commit/08460e3c7179d021745c54f5698bd14bbb4769d8) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix temperature param

## 1.21.2

### Patch Changes

- [#748](https://github.com/luxurypresence/seo-automation/pull/748) [`fa12570`](https://github.com/luxurypresence/seo-automation/commit/fa1257005662c8f1de600fe0bd0d4e05c33930b5) Thanks [@fbionsc](https://github.com/fbionsc)! - Replace max_tokens parameter

## 1.21.1

### Patch Changes

- [#747](https://github.com/luxurypresence/seo-automation/pull/747) [`74fcb37`](https://github.com/luxurypresence/seo-automation/commit/74fcb3755ba6d34b7b905d2b7c80c5b6cb6826fe) Thanks [@fbionsc](https://github.com/fbionsc)! - Fix changesets

## 1.21.0

### Minor Changes

- [#741](https://github.com/luxurypresence/seo-automation/pull/741) [`72cfe37`](https://github.com/luxurypresence/seo-automation/commit/72cfe37271668b82e7eaa5bcc67ac85ae4eab706) Thanks [@fbionsc](https://github.com/fbionsc)! - - Add user id to traces
  - Add prompt to traces
  - Move flushAt setting to enviroment variable
  - Remove label when fetching prompts

## 1.20.1

### Patch Changes

- [#738](https://github.com/luxurypresence/seo-automation/pull/738) [`3b57bde`](https://github.com/luxurypresence/seo-automation/commit/3b57bdecb30552b15ac621e8618f249dca76c1ff) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix incorrect model name from gpt-o3 to o3 in OpenAI API call

## 1.20.0

### Minor Changes

- [#736](https://github.com/luxurypresence/seo-automation/pull/736) [`83c2237`](https://github.com/luxurypresence/seo-automation/commit/83c2237f3fd6cfb44df3026656105601890b0817) Thanks [@fbionsc](https://github.com/fbionsc)! - Add tag to polling traces

## 1.19.1

### Patch Changes

- [#733](https://github.com/luxurypresence/seo-automation/pull/733) [`568c737`](https://github.com/luxurypresence/seo-automation/commit/568c7370222d6aa13e0e3db1bfa5664d91fd178f) Thanks [@DanielFrank](https://github.com/DanielFrank)! - Modify Cosmo calls to split m2mSuperApiKey

## 1.19.0

### Minor Changes

- [#729](https://github.com/luxurypresence/seo-automation/pull/729) [`6b8e9f5`](https://github.com/luxurypresence/seo-automation/commit/6b8e9f5842e2ba09e0d8575bf9b4f8afb523d803) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Update brand profile generator to use new Langfuse prompt structure - change prompt name, use single research parameter, and update to gpt-o3 model

## 1.18.0

### Minor Changes

- [#728](https://github.com/luxurypresence/seo-automation/pull/728) [`b9d2f05`](https://github.com/luxurypresence/seo-automation/commit/b9d2f0568a375fb487c16e25ca0c3474bb788060) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add sessionId parameter to Langfuse observeOpenAI calls for trace correlation

## 1.17.5

### Patch Changes

- [#725](https://github.com/luxurypresence/seo-automation/pull/725) [`d826f85`](https://github.com/luxurypresence/seo-automation/commit/d826f85a83218f9db0666971ab21a1b88c6dcf6f) Thanks [@jaycholland](https://github.com/jaycholland)! - esbuild concurrency 1

## 1.17.4

### Patch Changes

- [#724](https://github.com/luxurypresence/seo-automation/pull/724) [`fea69be`](https://github.com/luxurypresence/seo-automation/commit/fea69beec8d5de1c27aac751c59fb4b23d957925) Thanks [@jaycholland](https://github.com/jaycholland)! - iam role v3, zipConcurrency 1

## 1.17.3

### Patch Changes

- [#723](https://github.com/luxurypresence/seo-automation/pull/723) [`15d92ba`](https://github.com/luxurypresence/seo-automation/commit/15d92ba0f3a7e2f2b2a9e559de68fa60e7711659) Thanks [@jaycholland](https://github.com/jaycholland)! - change to initiate deploy

## 1.17.2

### Patch Changes

- [#722](https://github.com/luxurypresence/seo-automation/pull/722) [`cf4225f`](https://github.com/luxurypresence/seo-automation/commit/cf4225f6b72b0d3d0eb8352849d58a84c5a0f898) Thanks [@jaycholland](https://github.com/jaycholland)! - inconsequential edit to initiate another deploy

## 1.17.1

### Patch Changes

- [#721](https://github.com/luxurypresence/seo-automation/pull/721) [`856fdd6`](https://github.com/luxurypresence/seo-automation/commit/856fdd6a5d3405c7f538c0b94ecd4ca1bfca9351) Thanks [@jaycholland](https://github.com/jaycholland)! - conditional bucket fix 2

## 1.17.0

### Minor Changes

- [#720](https://github.com/luxurypresence/seo-automation/pull/720) [`9f1a258`](https://github.com/luxurypresence/seo-automation/commit/9f1a2588b56391d1adb098404b5f86757eb3fbb9) Thanks [@jaycholland](https://github.com/jaycholland)! - conditional bucket creation on deploy

## 1.16.5

### Patch Changes

- [#719](https://github.com/luxurypresence/seo-automation/pull/719) [`134fd06`](https://github.com/luxurypresence/seo-automation/commit/134fd0696417f0cf20d05748206e794153956c02) Thanks [@jaycholland](https://github.com/jaycholland)! - added a zipConcurrency limit of 3 to avoid OOMKilled in Argo deploy

## 1.16.4

### Patch Changes

- [#718](https://github.com/luxurypresence/seo-automation/pull/718) [`93094bc`](https://github.com/luxurypresence/seo-automation/commit/93094bc568d036fdb421fd5d8f89cec9aa472aca) Thanks [@jaycholland](https://github.com/jaycholland)! - Use latest version of Langfuse prompts

## 1.16.3

### Patch Changes

- [#717](https://github.com/luxurypresence/seo-automation/pull/717) [`42e7edb`](https://github.com/luxurypresence/seo-automation/commit/42e7edb4d2817befd3da87579bc8aabacc62e693) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed brand_research response output structure

## 1.16.2

### Patch Changes

- [#716](https://github.com/luxurypresence/seo-automation/pull/716) [`d683f21`](https://github.com/luxurypresence/seo-automation/commit/d683f21ab4e4dd630d8c48c25e6681bf90a39578) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed polling counter, traceName = promptName

## 1.16.1

### Patch Changes

- [#715](https://github.com/luxurypresence/seo-automation/pull/715) [`452a4f1`](https://github.com/luxurypresence/seo-automation/commit/452a4f11329fcaa7efaa85e24e5af8a2741e7f46) Thanks [@jaycholland](https://github.com/jaycholland)! - reduced serverless build concurrency

## 1.16.0

### Minor Changes

- [#714](https://github.com/luxurypresence/seo-automation/pull/714) [`9b0d47e`](https://github.com/luxurypresence/seo-automation/commit/9b0d47e6a4ef26e560603ff53bb2b2062c9c5418) Thanks [@jaycholland](https://github.com/jaycholland)! - implemented create brand profile from research

## 1.15.0

### Minor Changes

- [#713](https://github.com/luxurypresence/seo-automation/pull/713) [`b9c0683`](https://github.com/luxurypresence/seo-automation/commit/b9c0683c3a296ea4b3b1de46606e90fa2bc5e653) Thanks [@jaycholland](https://github.com/jaycholland)! - implemented get research job status

## 1.14.1

### Patch Changes

- [#712](https://github.com/luxurypresence/seo-automation/pull/712) [`84571d3`](https://github.com/luxurypresence/seo-automation/commit/84571d3086bb74fc2296dfea94960db4077ff48e) Thanks [@jaycholland](https://github.com/jaycholland)! - removed success rate from report

## 1.14.0

### Minor Changes

- [#711](https://github.com/luxurypresence/seo-automation/pull/711) [`8b78ee8`](https://github.com/luxurypresence/seo-automation/commit/8b78ee86622c3e57abc6d2dccac4015df0c737d5) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Implement hybrid website fetching approach combining tenant-service and API Gateway data sources

## 1.13.1

### Patch Changes

- [#710](https://github.com/luxurypresence/seo-automation/pull/710) [`380bc7f`](https://github.com/luxurypresence/seo-automation/commit/380bc7f91bad40173e9457cf20d468c14a6aee39) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - @devin-ai-integration - fix GraphQL endpoint URL construction to resolve 404 error

## 1.13.0

### Minor Changes

- [#709](https://github.com/luxurypresence/seo-automation/pull/709) [`721609b`](https://github.com/luxurypresence/seo-automation/commit/721609b47747f0d0b09807626f5bfec42f707061) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Migrate to API Gateway for website data fetching and fix JWT authentication

## 1.12.1

### Patch Changes

- [#708](https://github.com/luxurypresence/seo-automation/pull/708) [`85bcc36`](https://github.com/luxurypresence/seo-automation/commit/85bcc36c845b8479fd55e58d5d87b4493bba3acf) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Refactor brand-profile-generator to consolidate URL normalization and remove ApiGatewayClient dependencies

- [#708](https://github.com/luxurypresence/seo-automation/pull/708) [`85bcc36`](https://github.com/luxurypresence/seo-automation/commit/85bcc36c845b8479fd55e58d5d87b4493bba3acf) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Fix TypeError: Invalid URL by adding URL normalization to companyIds execution path

## 1.12.0

### Minor Changes

- [#707](https://github.com/luxurypresence/seo-automation/pull/707) [`b0d0021`](https://github.com/luxurypresence/seo-automation/commit/b0d0021130085225f5b5f0a6dd4c4721a5c3cc11) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Add hybrid approach for company website fetching when companyIds provided - uses tenant-service as primary source with API Gateway fallback

- [#707](https://github.com/luxurypresence/seo-automation/pull/707) [`b0d0021`](https://github.com/luxurypresence/seo-automation/commit/b0d0021130085225f5b5f0a6dd4c4721a5c3cc11) Thanks [@devin-ai-integration](https://github.com/apps/devin-ai-integration)! - Simplify companyIds path to use only tenant-service as source of truth

## 1.11.4

### Patch Changes

- [#705](https://github.com/luxurypresence/seo-automation/pull/705) [`42f10de`](https://github.com/luxurypresence/seo-automation/commit/42f10de87acaf3bd3c7203c8ff318f95a1dba88e) Thanks [@jaycholland](https://github.com/jaycholland)! - api gateway token fix

## 1.11.3

### Patch Changes

- [#703](https://github.com/luxurypresence/seo-automation/pull/703) [`67474ae`](https://github.com/luxurypresence/seo-automation/commit/67474aeaa5a54032cb14e59de49dc140e718a382) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed reading correct keys from resultwriter

## 1.11.2

### Patch Changes

- [#702](https://github.com/luxurypresence/seo-automation/pull/702) [`a3b2677`](https://github.com/luxurypresence/seo-automation/commit/a3b2677651c88f6602780d4ca0549f705c6b3dbd) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed report results reference to prefix vs actual file

## 1.11.1

### Patch Changes

- [#700](https://github.com/luxurypresence/seo-automation/pull/700) [`93b32a5`](https://github.com/luxurypresence/seo-automation/commit/93b32a577b0a48e963c3b794af04eebde8cf0713) Thanks [@jaycholland](https://github.com/jaycholland)! - Added logging and retries for debugging results writer

## 1.11.0

### Minor Changes

- [#697](https://github.com/luxurypresence/seo-automation/pull/697) [`1655e7e`](https://github.com/luxurypresence/seo-automation/commit/1655e7e764af80d375e9864d723d0de684b56e00) Thanks [@fbionsc](https://github.com/fbionsc)! - Remove output limit

## 1.10.0

### Minor Changes

- [#695](https://github.com/luxurypresence/seo-automation/pull/695) [`47ae2db`](https://github.com/luxurypresence/seo-automation/commit/47ae2db0fb984c36a374c087d33b91d914e55086) Thanks [@fbionsc](https://github.com/fbionsc)! - Add brand research lambda

## 1.9.0

### Minor Changes

- [#693](https://github.com/luxurypresence/seo-automation/pull/693) [`7fb54aa`](https://github.com/luxurypresence/seo-automation/commit/7fb54aabfeb2db8c9268708f9df47fd8f2077e25) Thanks [@jaycholland](https://github.com/jaycholland)! - Updated s3 implementation, tests, and readme doc

## 1.8.0

### Minor Changes

- [#690](https://github.com/luxurypresence/seo-automation/pull/690) [`4859097`](https://github.com/luxurypresence/seo-automation/commit/4859097ee127911a52091eb8c77a071d2a371a23) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed reading actual manifest format for results reporting

## 1.7.0

### Minor Changes

- [#687](https://github.com/luxurypresence/seo-automation/pull/687) [`742c944`](https://github.com/luxurypresence/seo-automation/commit/742c944ffdd9e034bd63f587af83890539c0f98e) Thanks [@jaycholland](https://github.com/jaycholland)! - Implemented report results

## 1.6.0

### Minor Changes

- [#686](https://github.com/luxurypresence/seo-automation/pull/686) [`2ef92e4`](https://github.com/luxurypresence/seo-automation/commit/2ef92e443379c67c0e21709cbd033f432bcf154a) Thanks [@jaycholland](https://github.com/jaycholland)! - Fixed skipped companies output to include more data

## 1.5.1

### Patch Changes

- [#685](https://github.com/luxurypresence/seo-automation/pull/685) [`51672b7`](https://github.com/luxurypresence/seo-automation/commit/51672b70fb7038b9b73968a9ae5756ff05e26ddf) Thanks [@jaycholland](https://github.com/jaycholland)! - Fixed api endpoint for graphql query and mutation

## 1.5.0

### Minor Changes

- [#684](https://github.com/luxurypresence/seo-automation/pull/684) [`de639ad`](https://github.com/luxurypresence/seo-automation/commit/de639ad5e0017aa8f4e946f36333c323cd726148) Thanks [@jaycholland](https://github.com/jaycholland)! - implemented saving brand profile via mutation

## 1.4.3

### Patch Changes

- [#683](https://github.com/luxurypresence/seo-automation/pull/683) [`3d1e12c`](https://github.com/luxurypresence/seo-automation/commit/3d1e12c66cbb1ca26f5b3e566ba74232bb6cf62e) Thanks [@jaycholland](https://github.com/jaycholland)! - companyId reference in tenant response

## 1.4.2

### Patch Changes

- [#678](https://github.com/luxurypresence/seo-automation/pull/678) [`ba0424b`](https://github.com/luxurypresence/seo-automation/commit/ba0424b5362f0f802c8d866b19b101162e53c8b8) Thanks [@jaycholland](https://github.com/jaycholland)! - updated s3 folder structure to include dry-run on the execution name for clarity

## 1.4.1

### Patch Changes

- [#677](https://github.com/luxurypresence/seo-automation/pull/677) [`4395fd1`](https://github.com/luxurypresence/seo-automation/commit/4395fd14904548e805ad138a4792cb06bfc728b0) Thanks [@jaycholland](https://github.com/jaycholland)! - adding logging for debugging

## 1.4.0

### Minor Changes

- [#675](https://github.com/luxurypresence/seo-automation/pull/675) [`060097c`](https://github.com/luxurypresence/seo-automation/commit/060097cb4d31668b22e1ff4e1cf9a3dc9cc9a0df) Thanks [@jaycholland](https://github.com/jaycholland)! - implement check existing brand profile

## 1.3.0

### Minor Changes

- [#674](https://github.com/luxurypresence/seo-automation/pull/674) [`354f0dd`](https://github.com/luxurypresence/seo-automation/commit/354f0ddbb5a4aea84fb9b97c27ae383ef2708854) Thanks [@jaycholland](https://github.com/jaycholland)! - implemented list-entitled-urls lambda

## 1.2.1

### Patch Changes

- [#672](https://github.com/luxurypresence/seo-automation/pull/672) [`7bcda4f`](https://github.com/luxurypresence/seo-automation/commit/7bcda4fbbaa8eb3a42f38f3c12e549ce2964c85a) Thanks [@jaycholland](https://github.com/jaycholland)! - iam role serverless config change to recreate

## 1.2.0

### Minor Changes

- [#671](https://github.com/luxurypresence/seo-automation/pull/671) [`09d66b0`](https://github.com/luxurypresence/seo-automation/commit/09d66b0c336a74e1b45ee1162ad7e6911538d13f) Thanks [@jaycholland](https://github.com/jaycholland)! - added process inputs lambda step

## 1.1.0

### Minor Changes

- [#670](https://github.com/luxurypresence/seo-automation/pull/670) [`e351981`](https://github.com/luxurypresence/seo-automation/commit/e3519817235c7f0076e37be30202fbe24a1c8c90) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed results writer to nest within parent execution id

## 1.0.0

### Major Changes

- [#668](https://github.com/luxurypresence/seo-automation/pull/668) [`23399f2`](https://github.com/luxurypresence/seo-automation/commit/23399f2f921ecd0a424bdbce8fb8b55dfc86dca3) Thanks [@jaycholland](https://github.com/jaycholland)! - First major release. Added catch failure with error types per lambda.

## 0.4.0

### Minor Changes

- [#667](https://github.com/luxurypresence/seo-automation/pull/667) [`8df7437`](https://github.com/luxurypresence/seo-automation/commit/8df7437d1e57342d85366c078d6b58c731537d4b) Thanks [@jaycholland](https://github.com/jaycholland)! - normalize inputs optional and fallback to defaults. fixed map iterable nodes issues.

## 0.3.0

### Minor Changes

- [#666](https://github.com/luxurypresence/seo-automation/pull/666) [`308f442`](https://github.com/luxurypresence/seo-automation/commit/308f442f0dd5453c59b4e089704b2ea229fe8715) Thanks [@jaycholland](https://github.com/jaycholland)! - Update state machine config to accept optional inputs, set defaults. List entitled urls now writes direct array to s3 for iterable map.

## 0.2.1

### Patch Changes

- [#665](https://github.com/luxurypresence/seo-automation/pull/665) [`27b4cfa`](https://github.com/luxurypresence/seo-automation/commit/27b4cfabd31f4b17e4ea66708b3f9903c8db0a91) Thanks [@jaycholland](https://github.com/jaycholland)! - added s3 tag permission

## 0.2.0

### Minor Changes

- [#664](https://github.com/luxurypresence/seo-automation/pull/664) [`f2c1efe`](https://github.com/luxurypresence/seo-automation/commit/f2c1efe1eff97cd90615b156a5310682e5ce0eaa) Thanks [@jaycholland](https://github.com/jaycholland)! - create brand profile lambda rename for name length limit

## 0.1.8

### Patch Changes

- [#663](https://github.com/luxurypresence/seo-automation/pull/663) [`12e4e13`](https://github.com/luxurypresence/seo-automation/commit/12e4e139df0ea25183eb4310395158a7e40d1b98) Thanks [@jaycholland](https://github.com/jaycholland)! - added s3 bucket config env

## 0.1.7

### Patch Changes

- [#662](https://github.com/luxurypresence/seo-automation/pull/662) [`8693513`](https://github.com/luxurypresence/seo-automation/commit/8693513972cdf9f6b78d4029a9368774afb6ecaa) Thanks [@jaycholland](https://github.com/jaycholland)! - dry run default true in all lambdas

## 0.1.6

### Patch Changes

- [#660](https://github.com/luxurypresence/seo-automation/pull/660) [`58c258e`](https://github.com/luxurypresence/seo-automation/commit/58c258ee1b160be1b26cec0fd082b63e0821cba2) Thanks [@jaycholland](https://github.com/jaycholland)! - iam role statements syntax version and conditional vpc

## 0.1.5

### Patch Changes

- [#659](https://github.com/luxurypresence/seo-automation/pull/659) [`82321a0`](https://github.com/luxurypresence/seo-automation/commit/82321a0b0021e47d2fe7d44650e59efe7a36ba97) Thanks [@jaycholland](https://github.com/jaycholland)! - fixed resultswriter prefix parameter

## 0.1.4

### Patch Changes

- [#658](https://github.com/luxurypresence/seo-automation/pull/658) [`1bc1054`](https://github.com/luxurypresence/seo-automation/commit/1bc10543120a6d260966bface603e6b0d472d546) Thanks [@jaycholland](https://github.com/jaycholland)! - serverless schema

## 0.1.3

### Patch Changes

- [#657](https://github.com/luxurypresence/seo-automation/pull/657) [`30f0ecb`](https://github.com/luxurypresence/seo-automation/commit/30f0ecb4add770e6766643e0cd733c852f751c5d) Thanks [@jaycholland](https://github.com/jaycholland)! - fix for deploy

## 0.1.2

### Patch Changes

- [#656](https://github.com/luxurypresence/seo-automation/pull/656) [`e32f58a`](https://github.com/luxurypresence/seo-automation/commit/e32f58a56950cd4d4fb782fbd360b15e6c07fe92) Thanks [@jaycholland](https://github.com/jaycholland)! - state machine inputs and handling to spec

## 0.1.1

### Patch Changes

- [#638](https://github.com/luxurypresence/seo-automation/pull/638) [`d4e76bb`](https://github.com/luxurypresence/seo-automation/commit/d4e76bbf98a0d08df655a419e574701c693a4a49) Thanks [@jaycholland](https://github.com/jaycholland)! - initial deployable version of brand-profile-generator with placeholder lambdas

## 0.1.0

### Initial Release

- Initial package setup for Brand Profile Generator service
