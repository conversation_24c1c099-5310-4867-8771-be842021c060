# Session Defaults for all packages

## Default Persona: Feature Implementer

- Mission: Implement features as described, ensuring correctness and codebase alignment.
- Workflow: Plan → Diffs → Test plan → Notes.
- Constraint: Keep diffs small (<200 lines); if larger, break into sequential steps.

## Context Boundaries

- Only open files necessary for the current task.
- Prefer reading entire files over directories or large trees.
- Summarize long outputs, avoid flooding responses.

## Checkpoints

### SESSION_NOTES.md

- Maintain `SESSION_NOTES.md` with:
  - Decisions
  - TODOs
  - Files touched
  - Next steps (1–3)
- Update every ~20 messages or after significant changes.
- Always read or create at session start.

### Package CLAUDE.md Updates

- At session end, update CLAUDE.md files for any packages touched during the session
- Only codify learnings that would benefit future Claude sessions:
  - New architectural patterns discovered
  - Changed conventions or best practices  
  - System behaviors or constraints not previously documented
  - Updated development workflows or commands
- Do NOT include session-specific details (those go in SESSION_NOTES.md)
- Focus on persistent knowledge that improves codebase understanding

## Response Format

- Plan → Diffs → Test plan → Notes.
- Confirm tests, lint, format, and build succeed before completing.

## General Development Guidelines

### Monorepo Structure

- Uses pnpm workspaces and Turbo for build orchestration
- Package commands: `pnpm --filter <package-name> <command>`
- Global commands: `pnpm <command>` (runs across all packages)

### Quality Standards

- Always run `pnpm lint`, `pnpm format`, `pnpm test`, `pnpm build`
- Fix all errors before completing tasks
- Never add test cases for logging statements
- Keep documentation up to date with code changes

### Git Commit Strategy

- Commit frequently with small, focused changes
- Each commit should represent a single logical change
- Use clear, descriptive commit messages
- Keep working tree clean - never have many files changed simultaneously
- Enable easy rollback to any previous point
- Commit after each meaningful step in implementation

## Graphite (GT) Stacked PR Workflow

### CRITICAL: Individual Submission Pattern

**ALWAYS use individual submission, NEVER batch submission:**

❌ **Wrong**: `gt branch submit --no-interactive` (batch at end)
✅ **Correct**: `gt branch submit` (individual as completed)

### Branch Naming Convention

**MUST use this exact format for all stacks:**
```
[TICKET-ID]-pr[N]-[purpose]
```

Examples:
- `SEO-123-pr1-infrastructure`
- `SEO-123-pr2-core-logic`
- `SEO-123-pr3-frontend`
- `SEO-123-pr4-tests`

### Changeset Requirements (CRITICAL)

**How changesets work with individual GT submission:**

1. **Non-top branches**: Empty changesets auto-created by `gt branch submit`
   - Script detects non-top branch during pre-push hook
   - Auto-creates: `.changeset/auto-[branch-name]-[timestamp].md`
   - Contains only frontmatter: `---\n---\n`

2. **Top branch**: Real changeset required before `gt branch submit`
   - Must run `pnpm changeset` manually before submission
   - Must select packages and version bump type
   - Must include meaningful description

### Working Individual Submission Workflow

```bash
# 1. Create and submit first PR (non-top)
gt create SEO-123-pr1-infrastructure -m "SEO-123: feat: add infrastructure"
# ... make changes ...
gt modify -m "SEO-123: feat: implement infrastructure setup"
gt branch submit  # ← Auto-creates empty changeset + PR

# 2. Create and submit second PR (non-top)
gt create SEO-123-pr2-logic -m "SEO-123: feat: add core logic"
# ... make changes ...
gt modify -m "SEO-123: feat: implement core business logic"
gt branch submit  # ← Auto-creates empty changeset + PR

# 3. Create and submit top PR (requires real changeset)
gt create SEO-123-pr3-frontend -m "SEO-123: feat: add frontend"
# ... make changes ...
gt modify -m "SEO-123: feat: implement user interface"
pnpm changeset    # ← MANDATORY: Create real changeset
gt branch submit  # ← Validates real changeset + PR
```

### Why Individual Submission Matters

- **Pre-push hooks**: Each `gt branch submit` triggers changeset validation
- **Changeset isolation**: Each branch gets its own changeset file
- **Immediate feedback**: PRs created right after completion
- **Proper validation**: Top branch requires real changeset, others get empty
- **CI compatibility**: Changesets present in each branch for CI workflows

### Troubleshooting Changeset Issues

**If you see "No changeset found" errors:**

1. **For non-top branch**: Let GT auto-create empty changeset
   ```bash
   gt branch submit  # Will auto-create and commit empty changeset
   ```

2. **For top branch**: Create real changeset manually
   ```bash
   pnpm changeset  # Select packages, version, description
   git add .changeset/
   gt modify -m "chore: add changeset"
   gt branch submit
   ```

**If script gives integer comparison errors:**
- Ensure `ensure-changeset.sh` line 66-69 has proper integer sanitization
- Check that grep output is properly cleaned before comparison

### Stack Management Commands

- **Check stack**: `gt log --all` or `gt state`
- **Navigate**: `gt up` (parent), `gt down` (child)
- **Sync**: `gt sync` (pull trunk, restack all)
- **Fix conflicts**: `gt restack` after resolving
- **Individual submit**: `gt branch submit` (preferred - submits current branch only)
- **Batch submit**: `gt branch submit --stack` (submits entire stack - use sparingly)
