# seo-automation

## Scrape + Generate SEO Recommendations

### Step 1: Scrape pages for SEO recommendations in the browser package

1. Set environment variable `ENVIRONMENT` to either `qa` or `staging` to use qa / staging data, otherwise beta clients will be used
2. `cd` into the `packages/browser` directory
3. Run `npx tsx src/generateDomainsToScrape.ts`
   - This reads the `clientData.ts` file which has the beta clients list with their keyword template variables and base domains. This is a hardcoded list for now.
   - Injects the variables into the keyword template
   - Generates a list of clients with
     - The URL to scrape for every page type based on the pageConfig
     - The keyword interpolated from the template + variables to use for the page
4. Run `node src/scrape.js`

   - This reads the `src/domainsToScrape/domains-to-scrape.json` file
   - Scrapes the URL for each client in batches
   - Extracts the meta title, description, and H1
   - Generates the JSON representation of the scraped page
   - Writes a JSON files to `scrapedJsons` directory which contains
     - JSON representation of the scraped page
     - Metadata (This will be the input for the SEO recommendations)
       - target_url
       - primary_keyword
       - scrapedAt
       - meta_title
       - meta_description
       - main_heading
       - location

### Step 2: Generate recommendations for SEO in the AI SEO Automation package

#### Generate recommendations for a batch of clients

1. Set environment variable `ENVIRONMENT` to either `qa` or `staging` to use qa / staging data
2. `cd` into the `packages/ai-seo-automation` directory
3. Run `pnpm cli process-directory`

   - Optionally pass in `--csv` to write the results to a CSV file as a dataset
   - This reads the `scrapedJsons` directory in the browser package generated in Step 1
   - Converts each JSON scraped page to markdown via LLM
   - Generates the SEO recommendations for each client via LLM
   - Optionally writes a CSV data set with all the inputs to the recommendations engine in `src/data-sets`

#### Generate recommendations for a single client

4. `cd` into the `packages/ai-seo-automation` directory
5. Run `pnpm cli process-file <json/file/path>` (no `--csv` option for single client)
   - This reads the json file from the `scrapedJsons` directory
   - Converts the JSON scraped page to markdown via LLM
   - Generates the SEO recommendations for the client via LLM

### Step 3: Get rankings for a batch of clients

1. Use this query in metabase to export a JSON list of all domains, paths and primary keywords

```sql
SELECT DISTINCT
    sp.domain,
    sp.path,
    g.metadata->>'keyword' as primary_keyword
FROM
    crew_ai_seo_automation."group" as g
    JOIN crew_ai_seo_automation.recommendation as r
        ON g.id = r.group_id
    JOIN crew_ai_seo_automation.seo_page as sp
        ON sp.id = r.seo_page_id
ORDER BY
    sp.domain,
    sp.path;
```

2. Paste the JSON results in `packages/ai-seo-automation/src/betaClientData/keywordsForRankingsdata.ts`
3. `cd` into the `packages/ai-seo-automation` directory
4. Run `npx tsx scripts/DataForSEOGetRankings.ts`
   - This reads the `keywordsForRankingsdata.ts` file
   - Makes API calls to DataForSEO to get rankings for each client
   - Writes the results to `randomData/DataForSEORankingResults.ts` (might write JSON, just convert it to ts)
5. Export all groups from metabase and paste in `packages/ai-seo-automation/randomData/allGroups.ts`
6. Run `getGroupIdsForRankings` (method in `scripts/updateGroupWithRankings.ts`) to match each ranking with its corresponding group
7. Run `updateGroupDescriptionWithRankings` (method in `scripts/updateGroupWithRankings.ts`) to update the group descriptions with their rankings
8. Run `addRankingsToGroupMetadata` (method in `scripts/updateGroupWithRankings.ts`) to add the rankings to the group metadata

## Scripts

### CLI Tool

The `ai-seo-automation` package includes a CLI tool for processing scraped web pages and generating SEO recommendations.

#### Commands:

1. **Process Single File**

   ```bash
   pnpm cli process-file <file> [options]
   ```

   Example:

   ```bash
   pnpm cli process-file ../browser/src/scrapedJsons/client1.json
   ```

   - Processes a single scraped JSON file
   - Converts JSON to markdown and analyzes content
   - Generates SEO recommendations
   - Options:
     - `-o, --output <directory>`: Custom output directory for markdown files (defaults to `../browser/src/scrapedMarkdown`)

2. **Process Directory**

   ```bash
   pnpm cli process-directory [directory] [options]
   ```

   Example:

   ```bash
   pnpm cli process-directory ../browser/src/scrapedJsons --csv
   ```

   - Batch processes all JSON files in a directory
   - Default directory: `../browser/src/scrapedJsons`
   - Options:
     - `-o, --output <directory>`: Custom output directory for markdown files (defaults to `../browser/src/scrapedMarkdown`)
     - `--csv`: Write results to a CSV file

### Analysis Tools

1. **View average evaluation scores for a batch run**

   ```bash
   pnpm average-scores <runId>
   ```

   - `cd` into the `packages/ai-seo-automation` directory
   - Run `pnpm average-scores <runId>` where `runId` is the batch run identifier
   - This will fetch all traces for the specified run
   - Calculate and display average scores for various metrics (quality, similarity, etc.)

## Environment Variables

### Browser Package

Place these in `packages/browser/.env`:

- `ENVIRONMENT`: Set to 'qa' or 'staging' to use qa / staging data, otherwise uses beta clients
- `SEO_API_GATEWAY_URL`: API Gateway URL (default: 'http://localhost:3000')
  - This is where the `seo-page` will be saved

### AI SEO Automation Package

Place these in `packages/ai-seo-automation/.env`:

- `SEO_API_GATEWAY_URL`: API Gateway URL (default: 'http://localhost:3000')
  - This is where the `group` and `recommendation` will be saved
- `LANGFUSE_SECRET_KEY`: Langfuse secret key for API access
- `LANGFUSE_PUBLIC_KEY`: Langfuse public key for API access
- `LANGFUSE_HOST`: Langfuse host URL (default: 'https://us.cloud.langfuse.com')
- `LANGFUSE_ENVIRONMENT`: Langfuse environment (default: 'default')

### Client Marketing Service

Place these in `packages/client-marketing-service/.env`:

- `PORT`: Server port (default: 3000)
- `WEBSITE_SERVICE_URL`: Website service URL (default: 'http://localhost:3001')
  - This is where the website update will be sent on recommendation status update

**Database Configuration:**

- `PG_CONNECTION_STRING`: PostgreSQL connection string (optional)
- `DATABASE_HOST`: Database host (default: 'localhost')
- `DATABASE_PORT`: Database port (default: '5432')
- `DATABASE_USER`: Database user (default: 'postgres')
- `DATABASE_PASSWORD`: Database password (default: '123123')
- `DATABASE_NAME`: Database name (default: 'lp_local')
- `DATABASE_SCHEMA`: Database schema (default: 'crew_ai_seo_automation')

## Random Notes
### Data sources for fields

- `company.id`: `identity.company.display_id`
- `company_seo_data.name`: `identity.company.name`
- `company_seo_data.business_name`: `core.brand.legal_business_name`
- `company_seo_data.location`: `property.neighbordhood.name` where `is_primary` is true
- `company_neighborhood.name`: `property.neighbordhood.name`
- `company_neighborhood.slug`: `property.neighbordhood.slug`
- `company_agent.name`: `property.agent.first_name` + `' '` + `property.agent.last_name`
- `company_agent.slug`: `property.agent.slug`
