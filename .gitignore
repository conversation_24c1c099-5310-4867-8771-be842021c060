.DS_Store
.DS_Store?
._*
logs
*.log
npm-debug.log*
pids
*.pid
*.seed
*.pid.lock
lib-cov
coverage
.nyc_output
.grunt
.lock-wscript
node_modules
jspm_packages
.npm
.npmrc
.eslintcache
.node_repl_history
*.tgz
.pnpm-debug.log*
.turbo
out
.idea


[._]*.s[a-w][a-z]
[._]s[a-w][a-z]
Session.vim
.netrwhist
*~
tags
package-lock.json

# Build outputs
.build/
build/
dist/
.serverless/
.esbuild/

local.json
local.json.*
flyway

.pyc
scripts/migrate.prod.sh
scripts/migrate.staging.sh

# mkcert
local.luxurycoders.com-key.pem
local.luxurycoders.com.pem

# sonarqube
.scannerwork
reports
test-report.xml
# reporters
junit.xml

# migration reports
report.html
report.json


test-results/
tests-examples/
playwright-report/
playwright/.cache/
blob-report/
.env
.history
.venv
scrapedJsons
stagingScrapedJsons
testStagingScrapedJsons
qaScrapedJsons
blogScrapedJsons
newScrapedJsons
newBlogScrapedJsons
oneMoreScrapedJsons
scrapedMarkdown
qaScrapedMarkdown
blogScrapedMarkdown
newScrapedMarkdown
newBlogScrapedMarkdown
oneMoreScrapedMarkdown

# for devin
.envrc
.envrc.*

.vscode
.env
.env.development
.env.production
.env.staging

SESSION_NOTES.md

# Claude Code: Keep project config (.claude/*.md), ignore personal data
.claude/tdd-guard/
.claude/projects/
.claude/transcripts/
.claude/**/data/
.claude/**/*.jsonl
