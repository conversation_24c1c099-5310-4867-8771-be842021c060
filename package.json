{"name": "seo-automation", "version": "0.0.1", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@9.11.0", "engines": {"node": ">=18"}, "repository": "**************:luxurypresence/seo-automation.git", "scripts": {"start": "turbo run start", "start:local": "turbo run start:local", "build": "turbo run build", "lint": "turbo run lint", "format": "turbo run format", "test": "turbo run test", "prepare": "husky || true", "preinstall": "npx only-allow pnpm", "publish-packages": "changeset version && changeset publish", "changeset:ensure": "./scripts/ensure-changeset.sh", "changeset:empty": "changeset add --empty"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.27.10", "@swc/core": "^1.9.3", "@swc/jest": "^0.2.37", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "buffer": "^6.0.3", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-promise": "^7.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "jest-junit": "^16.0.0", "prettier": "^3.4.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "turbo": "2.3.3", "typescript": "^5.7.2"}}