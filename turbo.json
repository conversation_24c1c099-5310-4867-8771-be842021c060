{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["env", "USERNAME", "NODE_ENV"], "tasks": {"start": {"dependsOn": ["^build"]}, "start:local": {"dependsOn": ["^build"]}, "build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**"]}, "clean": {"cache": false}, "lint": {"dependsOn": ["build"]}, "format": {}, "test": {"dependsOn": ["build"]}}}